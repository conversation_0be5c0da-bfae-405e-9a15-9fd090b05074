# VesselVoyage

[![codecov](https://codecov.io/bb/teqplay/vesselvoyage-backend/graph/badge.svg?token=IIYD2XYQFM)](https://codecov.io/bb/teqplay/vesselvoyage-backend)

VesselVoyage provides information on the level of a ship's trips and port visits. It is an abstraction level on top of AIS data points and events.

-   Production:

    https://backendvesselvoyage.teqplay.nl

-   Development:

    https://backendvesselvoyagedev.teqplay.nl

The Application runs on JDK 17.


## API

Documentation of the REST API is available via Swagger:

https://backendvesselvoyagedev.teqplay.nl/swagger-ui/index.html


## Getting started

When using IntelliJ for local development, it is advised to use the JDK of Corretto when running the app locally, which are also used by the Beanstalk applications on AWS. IntelliJ allows for an easy set-up:

Using an IntelliJ Run/Debug configuration:

### Kotlin linter

VesselVoyage makes use of `ktlint` a Kotlin linter. The `ktlintCheck` Gradle task is executed in the build step of our CI. Keep in mind when working on this project to always run this Gradle task yourself to manually fix the issues or execute the `ktlintFormat` Gradle step to fix any problems that arise automatically.

## Deploy

1. If changes made to api package, do a release from master and upgrade revents (in ais-engine)
   1. Prepare ais-engine live release
   2. Deploy revents-components
2. Deploy vesselvoyage processing and api back-ends
3. Optionally, deploy front-end if needed

Make sure the logging directory environment `APPLICATION_LOGGING_DIRECTORY` is set when deploying on a server!

Otherwise, the application logs are not saved to a file and not streamed to CloudWatch!


### Automated deployment via CircleCI

The application is normally tested, built, and deployed via CircleCI

Go to "Workflows", select the "vesselvoyage-backend" project, select the right and branch (`develop` or `master`), click on the green button of the latest run, and to deploy, click the "Hold" button.


### Manual deployment

Though not preferable, it is possible to deploy directly from your computer.

To deploy the application to the dev environment:

```
./gradlew deployStaging
```

To deploy to the production environment:

```
./gradlew deployProduction
```

## Configuration

All available configuration options can be found in the file `application.properties`.

To set the log level to debug and see fine-grained logging, specify the following VM option:

```
-Dlogging.level.nl.teqplay.vesselvoyage=DEBUG
```


## Authentication and Authorization

The application uses Auth0 to authenticate users, and uses Spring Aspect security annotations to do authorization.

The authentication flow is:

- the user clicks on Login in the frontend application.
- that will redirect the user to Auth0.
- the user logs in with an Auth0 account.
- the user is redirected to the frontend application with an access token.
- The frontend calls the `v1/auth/login/auth0` endpoint on the vesselvoyage backend server.
- The backend checks the token against Auth0 (I think?), and checks whether the user exists in the `users` collection
  In MongoDB. If valid, it sends a JWT token and a refresh token to the frontend as response.
- The frontend sends this token in an `Authentication` header with every request.


## Create a user

To create a new user:

-   A user account must be created in Auth0: https://manage.auth0.com/dashboard/eu/teqplay/users.
    The new user must be added to the Connection  `vesselvoyage` (production) or  `vesselvoyage-dev` (development).
-   In the MongoDB of VesselVoyage, the user must be added to the collection `users` in the database `vesselvoyage`.
    The user must have a role. Example:

    ```
    {
        "username" : "<EMAIL>",
        "roles" : [ 
            "ROLE_USER",
            "ROLE_DEVELOPER"
        ]
    }
    ```

    Available roles:`"ROLE_USER"` and `"ROLE_DEVELOPER"`.


## Create a new machine-2-machine account

There are two variants to do machine-2-machine communication, Auth0 and KeyCloak. It is recommended to use KeyCloak as Auth0 will soon be deprecated.

### KeyCloak

Step 1 can be skipped if you already have a KeyCloak client set up for your application.

1. Create a client on the KeyCloak management panel. [Production KeyCloak](https://keycloak.teqplay.nl/auth/) or [Develop KeyCloak](https://keycloakdev.teqplay.nl/auth/)
    1. Create a new client in the `prod` or `dev` realm.
    2. Set `Access Type` to `confidential`
    3. Switch `Authorization Enabled` to `ON`
    4. `Advanced Settings` -> `Access Token Lifespan` set to 1 day
2. Add a new Audience mapper on your client. Open the `Mappers` tab on the KeyCloak management panel and create the following entry:
    1. Set `Mapper Type` to `Audience`
    2. At `Included Client Audience` select `VesselVoyage`
    3. Name the mapper entry `Audience VesselVoyage`
3. In the `keycloak_s2s_connections` add the following entry:
   ```json
   {
     "clientId" : "your client id",
     "username" : "your client id",
     "roles" : ["ROLE_USER"]
   }
   ```

### Auth0

- Go to the auth0 management website: https://manage.auth0.com/dashboard/eu/teqplay/
- Create a new machine-2-machine application under "Applications".
- In the MongoDB collection `s2s_connections`, add a new entry like:

```json
{
  "clientId" : "client_id_from_auth0_application",
  "username" : "username",
  "roles" : [ 
    "ROLE_USER"
  ]
}
```

More detailed documentation is available in the README of the `skeleton-plugins` project, module `nl.teqplay.skeleton:auth-credentials-auth-zero-s2s`.

### M2M Usage

#### KeyCloak

Retrieving your token:

```
POST https://keycloakdev.teqplay.nl/auth/realms/dev/protocol/openid-connect/token
Content-Type: application/x-www-form-urlencoded
```

Input:

```
grant_type=client_credentials
client_id=YOUR_CLIENT_NAME
client_secret=YOUR_SECRET
```

Output example:

```json
{
  "access_token": "some_bearer_token",
  "expires_in": 21600,
  "refresh_expires_in": 0,
  "token_type": "Bearer",
  "not-before-policy": 0,
  "scope": "ignored"
}
```

The `access_token` needs to be sent along with every request as a header:

```
Authorization: Bearer <<<access token here>>>
```

#### Auth0

The m2m connection can be used as follows. Do a POST request with the body

```
POST https://teqplay.eu.auth0.com/oauth/token

{
  "client_id": "client_id_from_auth0_application"
  "client_secret": "client_secret_from_auth0_application"
  "audience": "yourproject.teqplay.nl"
  "grant_type": "client_credentials"
}
```

This will return a response like:

```json
{
    "access_token": "token to be used in further requests",
    "token_type": "token type"
}
```

The `access_token` needs to be sent along with every request as a header:

```
Authorization: Bearer <<<access token here>>>
```

## RecalculationService

The RecalculationService can recalculate the full history of a ship. This can be triggered by hand for a single ship via the `/ship/{imo}/recalculate` endpoint, or via a scheduler which can schedule recalculation of all ships that need an update.

The RecalculationService can automatically determine if there are ships that need recalculation. It takes all ships, and will schedule recalculation for all ships that do not have an entry in the `recalculations` collection. In order to schedule recalculation of a set of ships, you can remove those ships from the `recalculations` data source, and then trigger the backend to reschedule all ships via the `/ship/resetRecalculationService` endpoint, or you can just wait until the next day when the  backend reschedules all ships. For example a typical use case to recalculate all ships is when the VesselVoyage data models are extended with new information, and you want to populate this for all historic data. 

By default, traces are only recalculated when the corresponding entry (visit or voyage) is changed or the trace is just missing. In order to force recreation of a trace for a single ship, one can use the `forceRegenerateTraces` parameter on the `/ship/{imo}/recalculate?forceRegenerateTraces=true` endpoint, or (a bit ugly) one can delete all the traces of this ship from the `traces` collection and then trigger a recalculation.

To force recreation of *all* traces, one can increment the `TRACE_ALGORITHM_VERSION` number in the code, and deploy the application again. This is only needed when there is a change in the trace simplification logic, or when the historic AIS data changed. Be careful, recalculation of all traces is a huge amount of work. After that, the recalculations data collection has to be cleared and the RecalculationService enabled.

In case there is a ship stuck in recalculation which fails all the time, you can manually insert an entry in the `recalculations` collection in order to make it stop try again every day.


## Dependencies

To determine whether there are dependencies having updates, run:

```
./gradlew dependencyUpdates -Drevision=release
```

## FAQ

- When starting the application in IntelliJ on Windows, I get the error:

    ```
    CreateProcess error=206, The filename or extension is too long
    ```
  The solution explained here on StackOverflow may help: https://stackoverflow.com/a/64685180/1262753

  > Adding this to my .idea/workspace.xml fixed it
  >
  >     <component name="PropertiesComponent">
  >      ...
  >      <property name="dynamic.classpath" value="true"/>
  >      ...
  >     </component>
