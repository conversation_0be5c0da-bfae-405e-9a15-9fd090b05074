# Disable mongock when running tests
spring.mongock.enabled=false

cors.allowed-origins=https://vesselvoyage.teqplay.nl,https://vesselvoyagedev.teqplay.nl,http://localhost:3000

# Enable response compression (gzip)
server.compression.enabled=true
server.compression.mime-types=application/json,application/xml,text/html,text/xml,text/plain

# Fill the error message property in error responses
server.error.include-message=always

app.environment=TEST

mongodb.host=localhost
# Mongo is run on 27017 locally, set to a different port to trigger exceptions if this is every used
mongodb.port=20000
mongodb.authDb=fake-vesselvoyage
mongodb.username=
mongodb.password=
mongodb.db=fake-vesselvoyage

auth-credentials-auth0.domain=teqplay.eu.auth0.com
auth-credentials-auth0.audience=https://vesselmatcherdev.teqplay.nl

auth-credentials-auth0-s2s.domain=teqplay.eu.auth0.com
auth-credentials-auth0-s2s.audience=https://vesselvoyagedev.teqplay.nl

auth-credentials-keycloak-s2s.domain=keycloakdev.teqplay.nl
auth-credentials-keycloak-s2s.realm=vesselvoyage
auth-credentials-keycloak-s2s.audience=https://vesselvoyagedev.teqplay.nl
auth-credentials-keycloak-s2s.salt=test-salt

# token age is 24 hours: 86400 seconds
auth-credentials.token-age=86400

auth-credentials.secret=bla-bla-bla-bla-bla-bla-bla-bla-bla-bla-bla-bla

amqp.outgoing.enabled=false
amqp.outgoing.uri=amqps://localhost:5671/VesselVoyageDev
amqp.outgoing.exchange=

nats.ais-stream.enabled=false
nats.ais-stream.url=nats://localhost:4222
nats.ais-stream.username=
nats.ais-stream.password=

nats.event-stream.enabled=false
nats.event-stream.url=nats://localhost:4222
nats.event-stream.username=
nats.event-stream.password=

nats.change-stream.enabled=false
nats.change-stream.url=nats://localhost:4222
nats.change-stream.username=
nats.change-stream.password=

auth0.url=

# Enable to make use of the KeyCloak instead of Auth0
keycloak.enabled=false

# PoMa m2m config
keycloak.poma.url=https://backendpomadev.teqplay.nl
keycloak.poma.realm=poma
keycloak.poma.domain=keycloakdev.teqplay.nl
keycloak.poma.audience=pomadev
keycloak.poma.client-id=test-id
keycloak.poma.client-secret=test-secret
keycloak.poma.salt=test-salt

poma.domain=teqplay.eu.auth0.com
poma.url=https://poma-fake-for-unit-test.teqplay.nl
poma.client-id=
poma.client-secret=
poma.audience=

# CSI m2m config
keycloak.csi.url=https://csibackenddev.teqplay.nl
keycloak.csi.realm=dev
keycloak.csi.domain=keycloakdev.teqplay.nl
keycloak.csi.audience=csi
keycloak.csi.client-id=test-id
keycloak.csi.client-secret=test-secret
keycloak.csi.salt=test-salt

csi.domain=teqplay.eu.auth0.com
csi.url=https://csi-fake-for-unit-test.teqplay.nl
csi.client-id=
csi.client-secret=
csi.audience=csibackend.teqplay.nl

event-fetching.filter=foo
event-fetching.maxDays=99999
event-fetching.chunkDuration=P1D

ais-fetching.max-days=99999
ais-fetching.chunk-duration=P1D

event-processing.max-speed-mps=1
event-processing.min-duration=PT30M
event-processing.new-stop-detection=true
event-processing.enable-trace-calculations=true
event-processing.enable-slow-moving-periods=true
event-processing.total-threads=5
event-processing.log-results=false

event-processing.activeness-monitoring.enabled=true
event-processing.activeness-monitoring.interval=60000
event-processing.activeness-monitoring.max-idle-time=PT5M

processing.enable-real-time=false
event-processing.enable-new-definition=false
# Turned on by default until everyone uses V2
event-processing.enable-old-definition=true

trace.total-threads=5
trace.enable-new-definition=false
# Turned on by default until everyone uses V2
trace.enable-old-definition=true

# The maximum deviation of traces in rad from the original trace
trace.tolerance=0.0001

# the maximum length in days when requesting AIS information for traces
trace.max-request-length-days=90

# Ongoing traces will be deleted when older than the configured maxAge
trace.ongoing.max-age=P30D

# Ongoing traces will be simplified when the size exceeds the maxNonSimplifiedSize
trace.ongoing.max-non-simplified-size=50

# Ongoing traces will be kept in cache and appended to until the following
# max is reached after that a new ongoing trace will be started.
# This max is the maximum number of locations in a single ongoing trace.
trace.ongoing.max-size=100

# persist all ongoing traces once in a while,
# to prevent losing much ongoing trace AIS data in case of a crash
trace.ongoing.persist-interval=900000

# If true, an historic trace will be generated as soon as a visit/voyage is
# finished. When false, historic traces will be generated lazily: only when
# requested and missing.
trace.historic.generate-when-finished=true

# When false, the ongoing trace will be used to generate historic traces
# when a visit/voyage is finished. When true, a full AIS trace will be
# fetched from platform, which is a slower and demanding operation.
trace.historic.finished.create-from-ais=false

# When false, the ongoing trace will be used to generate historic traces
# when requested for a non-finished visit/voyage. When true, a full AIS
# trace will be fetched from platform, which is a slower and more demanding
# operation.
trace.historic.unfinished.createFromAis=false

trace.activeness-monitoring.enabled=true
trace.activeness-monitoring.interval=60000
trace.activeness-monitoring.max-idle-time=PT5M

# fetch all ports and anchorages once every day at 08:00:00 UTC
infra.refresh.cron=0 0 8 * * *

# fetch all ships once every day at 09:00:00 UTC
ship-info.refresh.cron=0 0 9 * * *

# if true, the full story of all ships will be recalculated once automatically
# recalculation will be scheduled
recalculation.enabled=false

# refresh the list with ships that need to be recalculated once every day at 10:00:00 UTC
recalculation.refresh-recalculable-ships-cron=0 0 10 * * *

# recalculate a ship every interval
# note that if recalculation of a single ships takes 20 seconds, and you set
# the interval to say 10 seconds, effectively two ships will be recalculated
# simultaneously.
recalculation.recalculate-ship-interval=PT1M

# if true, recalculate every ship when the snapshot created is older than the configured snapshot age,
# creating a new snapshot when recalculated
recalculation.snapshot-enabled=false

# The amount of time between each batch of snapshots that is being recalculated
recalculation.snapshot-interval=PT1M

# The minimum age a snapshot should be before it is considered for recalculation
recalculation.snapshot-age=PT24h

# the maximum number of ships that can be recalculated in parallel
recalculation.thread-pool-size=1
recalculation.max-mmsi-count=10

recalculation.revents-retry-enabled=false

health.slack.webhook=
health.teqplay-events-queue.max-time-between-events=PT5M
health.ais-streaming-queue.max-time-between-events=PT5M

# the following is to allow mocking PomaClient
spring.main.allow-bean-definition-overriding=true

cache.ttl.poma-berths=PT24H

diskcache.enabled=true
diskcache.ports-file=vesselVoyagePomaPortsTest.json
diskcache.anchorages-file=vesselVoyagePomaAnchoragesTest.json
diskcache.berths-file=vesselVoyagePomaBerthsTest.json
diskcache.csi-ships-file=vesselVoyageCSIShipsTest.json
diskcache.csi-shipRegistry-file=vesselVoyageCSIShipsRegistryTest.json

logging.event-stats-interval=PT1M

# limit the number of entries (visits, voyages) and traces in the controllers
# to prevent the backend from running out of memory due to a large request
controller.limit.max-entries=10000
controller.limit.max-traces=1000

automatic-recalculation.enabled=false
automatic-recalculation.event-history-pre-recalculation-enabled=false
automatic-recalculation.batch-size=1000
automatic-recalculation.cron=0 0 13 * * *
automatic-recalculation.ready-offset=PT48H
automatic-recalculation.ship-refresh-interval=PT1M

# DriftPredictor config
drift-predictor.url=https://driftpredictordev.teqplay.dev

## Post-processing config
post-processing.enabled=false
post-processing.total-threads=5
post-processing.interval=5000
post-processing.only-visits=false

event-publishing.enabled=false
event-publishing.nats.enabled=true
event-publishing.nats.url=test
event-publishing.nats.username=test
event-publishing.nats.password=test
event-publishing.rabbit-mq.enabled=true
event-publishing.rabbit-mq.uri=test
event-publishing.rabbit-mq.exchange=test
event-publishing.rabbit-mq.v1-exchange=test
event-publishing.enabled-publishers=entries,statement_of_facts
