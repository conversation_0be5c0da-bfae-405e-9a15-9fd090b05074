package nl.teqplay.vesselvoyage.logic.stop

import nl.teqplay.aisengine.event.interfaces.UniqueBerthEvent
import nl.teqplay.platform.util.LocationUtils.createBoundingBox
import nl.teqplay.vesselvoyage.logic.DEFAULT_ACTUAL_TIME
import nl.teqplay.vesselvoyage.logic.DEFAULT_END_TIME
import nl.teqplay.vesselvoyage.logic.DEFAULT_START_TIME
import nl.teqplay.vesselvoyage.logic.PORT_BEANR
import nl.teqplay.vesselvoyage.logic.PORT_NLRTM
import nl.teqplay.vesselvoyage.logic.createBerth
import nl.teqplay.vesselvoyage.logic.createPortAreaVisit
import nl.teqplay.vesselvoyage.logic.createStop
import nl.teqplay.vesselvoyage.logic.createUniqueBerthEvent
import nl.teqplay.vesselvoyage.logic.createVisit
import nl.teqplay.vesselvoyage.logic.generateUniqueId
import nl.teqplay.vesselvoyage.model.EventStatus
import nl.teqplay.vesselvoyage.model.Location
import nl.teqplay.vesselvoyage.model.Stop
import nl.teqplay.vesselvoyage.model.StopDetectionLocation
import nl.teqplay.vesselvoyage.model.StopDetectionVersion
import nl.teqplay.vesselvoyage.model.StopEndDetectionInfo
import nl.teqplay.vesselvoyage.model.StopStartDetectionInfo
import nl.teqplay.vesselvoyage.model.StopType
import nl.teqplay.vesselvoyage.model.lightweight.poma.Anchorage
import nl.teqplay.vesselvoyage.model.lightweight.poma.Berth
import nl.teqplay.vesselvoyage.util.toVesselVoyageLocation
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.ZoneOffset
import java.time.ZonedDateTime

class MergeStopBerthEventTest {
    companion object {
        private const val BERTH_ID_1 = "Z100/6/1237"
        private const val BERTH_ID_2 = "Z100/6/1238"
        private val VISIT_START_TIME = ZonedDateTime.parse("2023-06-01T00:00:00Z")
        private val START_TIME_1 = ZonedDateTime.parse("2023-07-01T00:00:00Z")
        private val START_TIME_1_ACTUAL = ZonedDateTime.parse("2023-07-01T01:00:00Z")
        private val START_TIME_2 = ZonedDateTime.parse("2023-08-01T00:00:00Z")
        private val START_TIME_2_ACTUAL = ZonedDateTime.parse("2023-08-01T01:00:00Z")
        private val END_TIME_2_ACTUAL = ZonedDateTime.parse("2023-08-02T01:00:00Z")
        private val START_TIME_3 = ZonedDateTime.parse("2023-09-01T00:00:00Z")
        private val TEST_BERTH_1 = createBerth(
            area = createBoundingBox(0.0, 0.0, 2.0, 2.0).toList(),
            authorityId = BERTH_ID_1,
            _id = "TEST_BERTH_ID_1",
            name = "TEST_BERTH_1"
        )
        private val TEST_BERTH_2 = createBerth(
            createBoundingBox(2.0, 2.0, 3.0, 3.0).toList(),
            BERTH_ID_2,
            _id = "TEST_BERTH_ID_2",
            name = "TEST_BERTH_2"
        )

        private val TEST_VISIT = createVisit(portAreas = listOf(createPortAreaVisit(PORT_NLRTM, VISIT_START_TIME)))
    }

    private fun findNoStopTrace(imo: String, stop: Stop): List<StopDetectionLocation> = emptyList()

    private fun findStopTrace(imo: String, stop: Stop): List<StopDetectionLocation> = listOf(
        StopDetectionLocation(1.0, 1.0, ZonedDateTime.parse("2023-08-01T00:00:00Z"), 0),
        StopDetectionLocation(1.0, 1.0, ZonedDateTime.parse("2023-08-01T01:00:00Z"), 0),
        StopDetectionLocation(1.0, 1.0, ZonedDateTime.parse("2023-08-01T02:00:00Z"), 0),
        StopDetectionLocation(1.0, 1.0, ZonedDateTime.parse("2023-08-01T03:00:00Z"), 0),
        StopDetectionLocation(1.0, 1.0, ZonedDateTime.parse("2023-08-01T04:00:00Z"), 0),
        StopDetectionLocation(1.0, 1.0, ZonedDateTime.parse("2023-08-01T05:00:00Z"), 0)
    )

    private fun findNoBerthsByLocation(location: Location): List<Berth> = emptyList()

    private fun findBerthsByLocation(location: Location): List<Berth> = listOf(TEST_BERTH_1)

    private fun findNoAnchoragesByLocation(location: Location): List<Anchorage> = emptyList()

    private fun findNoBerth(id: String): Berth? = null

    private fun findBerth(id: String): Berth? {
        return when (id) {
            BERTH_ID_1 -> TEST_BERTH_1
            BERTH_ID_2 -> TEST_BERTH_2
            else -> null
        }
    }

    @Test
    fun `should create new stop on start UniqueBerthEvent, without poma Berth and no current stops`() {
        val event = createUniqueBerthEvent(EventStatus.START, BERTH_ID_1, "2023-01-01T00:15:00Z")
        val currentStops = emptyList<Stop>()
        val mergedStops = currentStops.tryMergeWithBerthEvent(event, ::findNoBerth, TEST_VISIT, ::findNoStopTrace, ::findNoBerthsByLocation, ::findNoAnchoragesByLocation, BERTH_ID_1)

        val expectedStop = createExpectedStopOnStartEvent(event)
        val expected = listOf(expectedStop)

        assertEquals(expected, mergedStops)
    }

    @Test
    fun `should create new stop on start UniqueBerthEvent, with poma Berth and no current stops`() {
        val event = createUniqueBerthEvent(EventStatus.START, BERTH_ID_1, "2023-01-01T00:15:00Z")
        val currentStops = emptyList<Stop>()
        val mergedStops = currentStops.tryMergeWithBerthEvent(event, ::findBerth, TEST_VISIT, ::findNoStopTrace, ::findNoBerthsByLocation, ::findNoAnchoragesByLocation, BERTH_ID_1)

        val expectedStop = createExpectedStopOnStartEvent(event, TEST_BERTH_1)
        val expected = listOf(expectedStop)

        assertEquals(expected, mergedStops)
    }

    @Test
    fun `should create new stop on start UniqueBerthEvent, without poma Berth, with not matching single finished stop`() {
        val event = createUniqueBerthEvent(EventStatus.START, BERTH_ID_1, "2023-01-01T00:15:00Z")
        val currentStops = listOf(createFinishedStop())
        val mergedStops = currentStops.tryMergeWithBerthEvent(event, ::findNoBerth, TEST_VISIT, ::findNoStopTrace, ::findNoBerthsByLocation, ::findNoAnchoragesByLocation, BERTH_ID_1)

        val expectedStop = createExpectedStopOnStartEvent(event)
        val expected = currentStops + expectedStop

        assertEquals(expected, mergedStops)
    }

    @Test
    fun `should create new stop on start UniqueBerthEvent, with poma Berth, with not matching single finished stop`() {
        val event = createUniqueBerthEvent(EventStatus.START, BERTH_ID_1, "2023-01-01T00:15:00Z")
        val currentStops = listOf(createFinishedStop())
        val mergedStops = currentStops.tryMergeWithBerthEvent(event, ::findBerth, TEST_VISIT, ::findNoStopTrace, ::findNoBerthsByLocation, ::findNoAnchoragesByLocation, BERTH_ID_1)

        val expectedStop = createExpectedStopOnStartEvent(event, TEST_BERTH_1)
        val expected = currentStops + expectedStop

        assertEquals(expected, mergedStops)
    }

    @Test
    fun `should create new stop on start UniqueBerthEvent, finished single stop not matching locations`() {
        val event = createUniqueBerthEvent(EventStatus.START, BERTH_ID_1, "2023-01-01T00:15:00Z", PORT_BEANR)
        val currentStops = listOf(createFinishedStop())
        val mergedStops = currentStops.tryMergeWithBerthEvent(event, ::findNoBerth, TEST_VISIT, ::findNoStopTrace, ::findNoBerthsByLocation, ::findNoAnchoragesByLocation, BERTH_ID_1)

        val expectedStop = createExpectedStopOnStartEvent(event)
        val expected = currentStops + expectedStop

        assertEquals(expected, mergedStops)
    }

    @Test
    fun `should merge on start UniqueBerthEvent, without poma Berth, with matching single finished stop`() {
        val event = createUniqueBerthEvent(EventStatus.START, BERTH_ID_1, "2023-07-01T01:15:00Z")
        val currentStop = createFinishedStop()
        val currentStops = listOf(currentStop)
        val mergedStops = currentStops.tryMergeWithBerthEvent(event, ::findNoBerth, TEST_VISIT, ::findNoStopTrace, ::findNoBerthsByLocation, ::findNoAnchoragesByLocation, BERTH_ID_1)

        val expectedStop = currentStop.copy(
            detectionVersion = StopDetectionVersion.BERTH_EVENT,
            startEventId = event._id,
            startTime = START_TIME_1_ACTUAL,
            startLocation = event.location.toVesselVoyageLocation(),
            berthStart = StopStartDetectionInfo(
                id = event._id,
                time = START_TIME_1_ACTUAL,
                location = event.location.toVesselVoyageLocation(),
            )
        )
        val expected = listOf(expectedStop)

        assertEquals(expected, mergedStops)
    }

    @Test
    fun `should merge on start UniqueBerthEvent, with poma Berth, with matching single finished stop`() {
        val event = createUniqueBerthEvent(EventStatus.START, BERTH_ID_1, "2023-07-01T01:15:00Z")
        val currentStop = createFinishedStop()
        val currentStops = listOf(currentStop)
        val mergedStops = currentStops.tryMergeWithBerthEvent(event, ::findBerth, TEST_VISIT, ::findNoStopTrace, ::findNoBerthsByLocation, ::findNoAnchoragesByLocation, BERTH_ID_1)

        val expectedStop = currentStop.copy(
            name = "TEST_BERTH_1",
            pomaId = "TEST_BERTH_ID_1",
            pomaType = StopType.BERTH,
            detectionVersion = StopDetectionVersion.BERTH_EVENT,
            startEventId = event._id,
            startTime = START_TIME_1_ACTUAL,
            startLocation = event.location.toVesselVoyageLocation(),
            berthStart = StopStartDetectionInfo(
                id = event._id,
                time = START_TIME_1_ACTUAL,
                location = event.location.toVesselVoyageLocation(),
            )
        )
        val expected = listOf(expectedStop)

        assertEquals(expected, mergedStops)
    }

    @Test
    fun `should merge on start UniqueBerthEvent, with matching single ongoing stop`() {
        val event = createUniqueBerthEvent(EventStatus.START, BERTH_ID_1, "2023-07-01T01:15:00Z")
        val currentStop = createStop(type = StopType.BERTH)
        val currentStops = listOf(currentStop)
        val mergedStops = currentStops.tryMergeWithBerthEvent(event, ::findNoBerth, TEST_VISIT, ::findNoStopTrace, ::findNoBerthsByLocation, ::findNoAnchoragesByLocation, BERTH_ID_1)

        val expectedStop = currentStop.copy(
            detectionVersion = StopDetectionVersion.BERTH_EVENT,
            startEventId = event._id,
            startTime = START_TIME_1_ACTUAL,
            startLocation = event.location.toVesselVoyageLocation(),
            berthStart = StopStartDetectionInfo(
                id = event._id,
                time = START_TIME_1_ACTUAL,
                location = event.location.toVesselVoyageLocation(),
            )
        )
        val expected = listOf(expectedStop)

        assertEquals(expected, mergedStops)
    }

    @Test
    fun `should create new stop on start UniqueBerthEvent, with single ongoing stop started after event time`() {
        val event = createUniqueBerthEvent(EventStatus.START, BERTH_ID_2, "2023-06-01T00:15:00Z", PORT_BEANR)
        val currentStop = createStop(type = StopType.BERTH)
        val currentStops = listOf(currentStop)
        val mergedStops = currentStops.tryMergeWithBerthEvent(event, ::findBerth, TEST_VISIT, ::findNoStopTrace, ::findNoBerthsByLocation, ::findNoAnchoragesByLocation, BERTH_ID_2)

        val expectedStop = createExpectedStopOnStartEvent(event, TEST_BERTH_2)
        val expected = currentStops + expectedStop

        assertEquals(expected, mergedStops)
    }

    @Test
    fun `should merge on start UniqueBerthEvent, with multiple stops and latest ongoing stop, matching finished stop`() {
        val event = createUniqueBerthEvent(EventStatus.START, BERTH_ID_1, "2023-07-01T01:15:00Z")
        val finishedStop = createFinishedStop(pomaBerth = TEST_BERTH_1)
        val ongoingStop = createStop(type = StopType.BERTH, startTime = START_TIME_2)
        val currentStops = listOf(finishedStop, ongoingStop)
        val mergedStops = currentStops.tryMergeWithBerthEvent(event, ::findBerth, TEST_VISIT, ::findNoStopTrace, ::findNoBerthsByLocation, ::findNoAnchoragesByLocation, BERTH_ID_1)

        val expectedStop = finishedStop.copy(
            name = "TEST_BERTH_1",
            pomaId = "TEST_BERTH_ID_1",
            pomaType = StopType.BERTH,
            detectionVersion = StopDetectionVersion.BERTH_EVENT,
            startEventId = event._id,
            startTime = START_TIME_1_ACTUAL,
            startLocation = event.location.toVesselVoyageLocation(),
            berthStart = StopStartDetectionInfo(
                id = event._id,
                time = START_TIME_1_ACTUAL,
                location = event.location.toVesselVoyageLocation(),
            )
        )
        val expected = listOf(expectedStop, ongoingStop)

        assertEquals(expected, mergedStops)
    }

    @Test
    fun `should not merge on start UniqueBerthEvent, with multiple stops and latest ongoing stop, matching finished stop but already had berth start event`() {
        val event = createUniqueBerthEvent(EventStatus.START, BERTH_ID_1, "2023-07-01T00:15:00Z")
        val finishedStop = createFinishedStop(
            pomaBerth = TEST_BERTH_1,
            berthStart = StopStartDetectionInfo(BERTH_ID_1, PORT_NLRTM.location, START_TIME_1)
        )
        val ongoingStop = createStop(type = StopType.BERTH, startTime = START_TIME_2)
        val currentStops = listOf(finishedStop, ongoingStop)
        val mergedStops = currentStops.tryMergeWithBerthEvent(event, ::findBerth, TEST_VISIT, ::findNoStopTrace, ::findNoBerthsByLocation, ::findNoAnchoragesByLocation, BERTH_ID_1)

        assertEquals(currentStops, mergedStops)
    }

    @Test
    fun `should merge on start UniqueBerthEvent, with multiple stops and latest ongoing stop, matching ongoing stop`() {
        val event = createUniqueBerthEvent(EventStatus.START, BERTH_ID_1, "2023-08-01T01:15:00Z")
        val finishedStop = createFinishedStop()
        val ongoingStop = createStop(type = StopType.BERTH, startTime = START_TIME_2)
        val currentStops = listOf(finishedStop, ongoingStop)
        val mergedStops = currentStops.tryMergeWithBerthEvent(event, ::findBerth, TEST_VISIT, ::findNoStopTrace, ::findNoBerthsByLocation, ::findNoAnchoragesByLocation, BERTH_ID_1)

        val expectedStop = ongoingStop.copy(
            name = "TEST_BERTH_1",
            pomaId = "TEST_BERTH_ID_1",
            pomaType = StopType.BERTH,
            detectionVersion = StopDetectionVersion.BERTH_EVENT,
            startEventId = event._id,
            startTime = START_TIME_2_ACTUAL,
            startLocation = event.location.toVesselVoyageLocation(),
            berthStart = StopStartDetectionInfo(
                id = event._id,
                time = START_TIME_2_ACTUAL,
                location = event.location.toVesselVoyageLocation(),
            )
        )
        val expected = listOf(finishedStop, expectedStop)

        assertEquals(expected, mergedStops)
    }

    @Test
    fun `should create new stop on start UniqueBerthEvent, with multiple stops and latest ongoing stop, no matching stop`() {
        val event = createUniqueBerthEvent(EventStatus.START, BERTH_ID_2, "2023-08-01T00:15:00Z", port = PORT_BEANR)
        val finishedStop = createFinishedStop()
        val ongoingStop = createStop(
            type = StopType.BERTH,
            startTime = START_TIME_1,
            berthStart = StopStartDetectionInfo(
                id = "SOME_OLDER_EVENT_ID",
                time = START_TIME_1,
                location = PORT_NLRTM.location
            )
        )
        val currentStops = listOf(finishedStop, ongoingStop)
        val mergedStops = currentStops.tryMergeWithBerthEvent(event, ::findBerth, TEST_VISIT, ::findNoStopTrace, ::findNoBerthsByLocation, ::findNoAnchoragesByLocation, BERTH_ID_2)

        val expectedStop = createExpectedStopOnStartEvent(event, TEST_BERTH_2)
        val expected = listOf(finishedStop, ongoingStop, expectedStop)

        assertEquals(expected, mergedStops)
    }

    @Test
    fun `should merge on start UniqueBerthEvent, with multiple stops and latest ongoing stop, matching multiple finished stop, only taking last`() {
        val event = createUniqueBerthEvent(EventStatus.START, BERTH_ID_1, "2023-07-01T00:15:00Z")
        val firstFinishedStop = createFinishedStop(pomaBerth = TEST_BERTH_1)
        val lastFinishedStop = createFinishedStop(pomaBerth = TEST_BERTH_1)
        val ongoingStop = createStop(type = StopType.BERTH, startTime = START_TIME_2)
        val currentStops = listOf(firstFinishedStop, lastFinishedStop, ongoingStop)
        val mergedStops = currentStops.tryMergeWithBerthEvent(event, ::findBerth, TEST_VISIT, ::findNoStopTrace, ::findNoBerthsByLocation, ::findNoAnchoragesByLocation, BERTH_ID_1)

        val expectedStop = lastFinishedStop.copy(
            name = "TEST_BERTH_1",
            pomaId = "TEST_BERTH_ID_1",
            pomaType = StopType.BERTH,
            detectionVersion = StopDetectionVersion.BERTH_EVENT,
            startEventId = event._id,
            startTime = START_TIME_1,
            startLocation = event.location.toVesselVoyageLocation(),
            berthStart = StopStartDetectionInfo(
                id = event._id,
                time = START_TIME_1,
                location = event.location.toVesselVoyageLocation(),
            )
        )
        val expected = listOf(firstFinishedStop, expectedStop, ongoingStop)

        assertEquals(expected, mergedStops)
    }

    @Test
    fun `should create new stop on start UniqueBerthEvent, with multiple stops and latest ongoing stop, matching ongoing inbetween stop`() {
        val event = createUniqueBerthEvent(EventStatus.START, BERTH_ID_1, "2023-08-01T00:15:00Z")
        val finishedStop = createFinishedStop(
            pomaBerth = TEST_BERTH_1,
            berthStart = StopStartDetectionInfo(BERTH_ID_1, PORT_NLRTM.location, START_TIME_1)
        )
        val matchingWithTimeOngoingStop = createStop(type = StopType.BERTH, startTime = START_TIME_2)
        val latestOngoingStop = createStop(type = StopType.BERTH, startTime = START_TIME_3)
        val currentStops = listOf(finishedStop, matchingWithTimeOngoingStop, latestOngoingStop)
        val mergedStops = currentStops.tryMergeWithBerthEvent(event, ::findBerth, TEST_VISIT, ::findNoStopTrace, ::findNoBerthsByLocation, ::findNoAnchoragesByLocation, BERTH_ID_1)

        val expectedStop = createExpectedStopOnStartEvent(event, TEST_BERTH_1)
        val expected = currentStops + expectedStop

        assertEquals(expected, mergedStops)
    }

    @Test
    fun `should merge new stop on start UniqueBerthEvent, with multiple stops and all finished, matching latest finished`() {
        val event = createUniqueBerthEvent(EventStatus.START, BERTH_ID_2, "2023-08-01T01:15:00Z")
        val finishedStop = createFinishedStop(
            pomaBerth = TEST_BERTH_1,
            berthStart = StopStartDetectionInfo(BERTH_ID_1, PORT_NLRTM.location, START_TIME_2)
        )
        val finishedStop2 = createFinishedStop(
            pomaBerth = TEST_BERTH_2,
            startTime = START_TIME_2,
            endTime = START_TIME_3
        )
        val currentStops = listOf(finishedStop, finishedStop2)
        val mergedStops = currentStops.tryMergeWithBerthEvent(event, ::findBerth, TEST_VISIT, ::findNoStopTrace, ::findNoBerthsByLocation, ::findNoAnchoragesByLocation, BERTH_ID_2)

        val expectedStop = finishedStop2.copy(
            name = "TEST_BERTH_2",
            pomaId = "TEST_BERTH_ID_2",
            pomaType = StopType.BERTH,
            detectionVersion = StopDetectionVersion.BERTH_EVENT,
            startEventId = event._id,
            startTime = START_TIME_2_ACTUAL,
            startLocation = event.location.toVesselVoyageLocation(),
            berthStart = StopStartDetectionInfo(
                id = event._id,
                time = START_TIME_2_ACTUAL,
                location = event.location.toVesselVoyageLocation(),
            )
        )
        val expected = listOf(finishedStop, expectedStop)

        assertEquals(expected, mergedStops)
    }

    @Test
    fun `should drop start UniqueBerthEvent when already received berth event`() {
        val event = createUniqueBerthEvent(EventStatus.START, BERTH_ID_2, "2023-08-01T01:15:00Z")
        val finishedStop = createFinishedStop(
            pomaBerth = TEST_BERTH_1,
            berthStart = StopStartDetectionInfo(BERTH_ID_1, PORT_NLRTM.location, START_TIME_1_ACTUAL)
        )
        val finishedStop2 = createFinishedStop(
            pomaBerth = TEST_BERTH_2,
            berthStart = StopStartDetectionInfo(BERTH_ID_2, PORT_NLRTM.location, START_TIME_2_ACTUAL),
            startTime = START_TIME_2,
            endTime = START_TIME_3
        )
        val currentStops = listOf(finishedStop, finishedStop2)
        val mergedStops = currentStops.tryMergeWithBerthEvent(event, ::findBerth, TEST_VISIT, ::findNoStopTrace, ::findNoBerthsByLocation, ::findNoAnchoragesByLocation, BERTH_ID_2)

        assertEquals(currentStops, mergedStops)
    }

    @Test
    fun `should drop start UniqueBerthEvent when entering same berth after leaving`() {
        val ongoingStop = createStop(type = StopType.UNCLASSIFIED, startTime = START_TIME_2)
        val currentStops = listOf(ongoingStop)

        val event1 = createUniqueBerthEvent(EventStatus.START, BERTH_ID_2, "2023-08-01T01:15:00Z")
        val event2 = createUniqueBerthEvent(EventStatus.END, BERTH_ID_2, "2023-08-02T01:15:00Z")
        val event3 = createUniqueBerthEvent(EventStatus.START, BERTH_ID_2, "2023-09-01T01:15:00Z")

        val mergedStops = currentStops.tryMergeWithBerthEvent(event1, ::findBerth, TEST_VISIT, ::findNoStopTrace, ::findNoBerthsByLocation, ::findNoAnchoragesByLocation, BERTH_ID_2)
            .tryMergeWithBerthEvent(event2, ::findBerth, TEST_VISIT, ::findNoStopTrace, ::findNoBerthsByLocation, ::findNoAnchoragesByLocation, BERTH_ID_2)
            .tryMergeWithBerthEvent(event3, ::findBerth, TEST_VISIT, ::findNoStopTrace, ::findNoBerthsByLocation, ::findNoAnchoragesByLocation, BERTH_ID_2)

        val expectedStop = createFinishedStop(
            pomaBerth = TEST_BERTH_2,
            berthStart = StopStartDetectionInfo(event1._id, PORT_NLRTM.location, START_TIME_2_ACTUAL),
            berthEnd = StopEndDetectionInfo(event2._id, PORT_NLRTM.location, END_TIME_2_ACTUAL),
            startTime = START_TIME_2_ACTUAL,
            endTime = END_TIME_2_ACTUAL,
            actualTime = null
        )
        val expected = listOf(expectedStop)

        assertEquals(expected, mergedStops)
    }

    @Test
    fun `should merge new stop on start UniqueBerthEvent, with multiple stops and all finished, none matching`() {
        val event = createUniqueBerthEvent(EventStatus.START, BERTH_ID_2, "2023-09-01T00:15:00Z")
        val finishedStop = createFinishedStop(
            pomaBerth = TEST_BERTH_1,
            berthStart = StopStartDetectionInfo(BERTH_ID_1, PORT_NLRTM.location, START_TIME_1_ACTUAL)
        )
        val finishedStop2 = createFinishedStop(
            pomaBerth = TEST_BERTH_1,
            berthStart = StopStartDetectionInfo(BERTH_ID_1, PORT_NLRTM.location, START_TIME_2_ACTUAL),
            startTime = START_TIME_2,
            endTime = START_TIME_3
        )
        val currentStops = listOf(finishedStop, finishedStop2)
        val mergedStops = currentStops.tryMergeWithBerthEvent(event, ::findBerth, TEST_VISIT, ::findNoStopTrace, ::findNoBerthsByLocation, ::findNoAnchoragesByLocation, BERTH_ID_2)
        val expectedNewStop = createExpectedStopOnStartEvent(event, TEST_BERTH_2)
        val expected = currentStops + expectedNewStop

        assertEquals(expected, mergedStops)
    }

    @Test
    fun `should create new stop on end UniqueBerthEvent, no current stops`() {
        val endEvent = createUniqueBerthEvent(EventStatus.END, BERTH_ID_1, "2023-09-01T00:15:00Z")
        val currentStops = emptyList<Stop>()

        val result = currentStops.tryMergeWithBerthEvent(endEvent, ::findBerth, TEST_VISIT, ::findStopTrace, ::findBerthsByLocation, ::findNoAnchoragesByLocation, BERTH_ID_1)
        val expectedStop = createExpectedStopOnEndEvent(endEvent, endEvent._id, START_TIME_2, endEvent.location.toVesselVoyageLocation(), START_TIME_2, TEST_BERTH_1).copy(
            aisStart = StopStartDetectionInfo(
                id = endEvent._id,
                location = endEvent.location.toVesselVoyageLocation(),
                time = START_TIME_2
            ),
            aisEnd = StopEndDetectionInfo(
                id = endEvent._id,
                location = endEvent.location.toVesselVoyageLocation(),
                time = START_TIME_3
            )
        )
        val expected = listOf(expectedStop)

        assertEquals(expected, result)
    }

    @Test
    fun `should create new stop on end UniqueBerthEvent, no matching stops`() {
        val endEvent = createUniqueBerthEvent(EventStatus.END, BERTH_ID_1, "2023-09-01T00:15:00Z")
        val finishedStop = createFinishedStop(
            startTime = VISIT_START_TIME,
            endTime = VISIT_START_TIME.plusDays(2),
            startLocation = PORT_BEANR.location,
            endLocation = PORT_BEANR.location,
            pomaBerth = TEST_BERTH_2
        )
        val currentStops = listOf(finishedStop)

        val result = currentStops.tryMergeWithBerthEvent(endEvent, ::findBerth, TEST_VISIT, ::findStopTrace, ::findBerthsByLocation, ::findNoAnchoragesByLocation, BERTH_ID_1)
        val expectedStop = createExpectedStopOnEndEvent(endEvent, endEvent._id, START_TIME_2, endEvent.location.toVesselVoyageLocation(), START_TIME_2, TEST_BERTH_1).copy(
            aisStart = StopStartDetectionInfo(
                id = endEvent._id,
                location = endEvent.location.toVesselVoyageLocation(),
                time = START_TIME_2
            ),
            aisEnd = StopEndDetectionInfo(
                id = endEvent._id,
                location = endEvent.location.toVesselVoyageLocation(),
                time = START_TIME_3
            )
        )
        val expected = currentStops + expectedStop

        assertEquals(expected, result)
    }

    @Test
    fun `should merge stop on start UniqueBerthEvent, ongoing stop, berth event actual time before movement event`() {
        val event = createUniqueBerthEvent(
            status = EventStatus.START,
            berthId = BERTH_ID_1,
            time = "2023-09-25T11:43:25.461Z",
            originalTime = "2023-09-25T11:27:33Z",
            location = Location(
                latitude = 51.97220333333333,
                longitude = 4.040003333333334
            ),
            portId = "NLRTM"
        )
        val aisStart = StopStartDetectionInfo(
            id = "999ab8e1-f0bc-4693-8b60-a8b5eb4f8619",
            time = ZonedDateTime.parse("2023-09-25T11:28:21Z"),
            location = Location(
                latitude = 51.972433333333335,
                longitude = 4.0401316666666665
            )
        )
        val berthStart = StopStartDetectionInfo(
            id = event._id,
            time = event.actualTime.atZone(ZoneOffset.UTC),
            location = event.location.toVesselVoyageLocation(),
        )

        val currentStop = createStop(
            type = StopType.UNCLASSIFIED,
            startTime = aisStart.time,
            startEventId = aisStart.id,
            location = aisStart.location,
            detectionVersion = StopDetectionVersion.MOVEMENT_EVENT,
            accuracy = 0f,
            aisStart = aisStart
        )
        val currentStops = listOf(currentStop)
        val mergedStops = currentStops.tryMergeWithBerthEvent(event, ::findBerth, TEST_VISIT, ::findNoStopTrace, ::findNoBerthsByLocation, ::findNoAnchoragesByLocation, BERTH_ID_1)

        val expectedStop = currentStop.copy(
            type = StopType.BERTH,
            pomaType = StopType.BERTH,
            detectionVersion = StopDetectionVersion.BERTH_EVENT,
            pomaId = TEST_BERTH_1._id,
            name = TEST_BERTH_1.name,
            startEventId = berthStart.id,
            startTime = berthStart.time,
            startLocation = berthStart.location,
            berthStart = berthStart
        )
        val expected = listOf(expectedStop)

        assertEquals(expected, mergedStops)
    }

    private fun createExpectedStopOnStartEvent(event: UniqueBerthEvent, pomaBerth: Berth? = null): Stop {
        val pomaType = if (pomaBerth != null) {
            StopType.BERTH
        } else {
            StopType.UNCLASSIFIED
        }

        return Stop(
            type = StopType.BERTH,
            pomaType = pomaType,
            pomaId = pomaBerth?._id,
            name = pomaBerth?.nameLong ?: pomaBerth?.name,
            startEventId = event._id,
            startTime = event.actualTime.atZone(ZoneOffset.UTC),
            startLocation = event.location.toVesselVoyageLocation(),
            endEventId = null,
            endTime = null,
            endLocation = null,
            actualLocation = null,
            actualTime = null,
            detectionVersion = StopDetectionVersion.BERTH_EVENT,
            accuracy = null,
            berthStart = StopStartDetectionInfo(
                id = event._id,
                time = event.actualTime.atZone(ZoneOffset.UTC),
                location = event.location.toVesselVoyageLocation(),
            ),
            berthEnd = null,
            aisStart = null,
            aisEnd = null
        )
    }

    private fun createExpectedStopOnEndEvent(
        event: UniqueBerthEvent,
        startEventId: String,
        startTime: ZonedDateTime,
        startLocation: Location,
        actualTime: ZonedDateTime,
        pomaBerth: Berth? = null,
    ): Stop {
        val pomaType = if (pomaBerth != null) {
            StopType.BERTH
        } else {
            StopType.UNCLASSIFIED
        }

        return Stop(
            type = StopType.BERTH,
            pomaType = pomaType,
            pomaId = pomaBerth?._id,
            name = pomaBerth?.nameLong ?: pomaBerth?.name,
            startEventId = startEventId,
            startTime = startTime,
            startLocation = startLocation,
            endEventId = event._id,
            endTime = event.actualTime.atZone(ZoneOffset.UTC),
            endLocation = event.location.toVesselVoyageLocation(),
            actualLocation = event.location.toVesselVoyageLocation(),
            actualTime = actualTime,
            detectionVersion = StopDetectionVersion.BERTH_EVENT,
            accuracy = null,
            berthStart = null,
            berthEnd = StopEndDetectionInfo(
                id = event._id,
                time = event.actualTime.atZone(ZoneOffset.UTC),
                location = event.location.toVesselVoyageLocation(),
            ),
            aisStart = null,
            aisEnd = null
        )
    }

    private fun createFinishedStop(
        startTime: ZonedDateTime = DEFAULT_START_TIME,
        startLocation: Location = PORT_NLRTM.location,
        endTime: ZonedDateTime = DEFAULT_END_TIME,
        endLocation: Location = PORT_NLRTM.location,
        pomaBerth: Berth? = null,
        berthStart: StopStartDetectionInfo? = null,
        berthEnd: StopEndDetectionInfo? = null,
        actualTime: ZonedDateTime? = DEFAULT_ACTUAL_TIME
    ): Stop {
        val pomaType = if (pomaBerth != null) {
            StopType.BERTH
        } else {
            StopType.UNCLASSIFIED
        }

        return createStop(
            type = StopType.BERTH,
            pomaType = pomaType,
            pomaId = pomaBerth?._id,
            name = pomaBerth?.nameLong ?: pomaBerth?.name,
            startEventId = berthStart?.id ?: generateUniqueId(),
            startTime = startTime,
            location = startLocation,
            endEventId = berthEnd?.id ?: generateUniqueId(),
            endTime = endTime,
            endLocation = endLocation,
            berthStart = berthStart,
            berthEnd = berthEnd,
            actualTime = actualTime,
            detectionVersion = if (pomaBerth != null) StopDetectionVersion.BERTH_EVENT else StopDetectionVersion.MOVEMENT_EVENT
        )
    }
}
