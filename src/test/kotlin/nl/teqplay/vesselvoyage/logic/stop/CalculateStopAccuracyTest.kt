package nl.teqplay.vesselvoyage.logic.stop

import nl.teqplay.vesselvoyage.logic.createStop
import nl.teqplay.vesselvoyage.model.Stop
import nl.teqplay.vesselvoyage.model.StopDetectionVersion
import nl.teqplay.vesselvoyage.model.StopType
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CalculateStopAccuracyTest {
    private fun stops(): Stream<Arguments> {
        return Stream.of(
            Arguments.of(createStop(type = StopType.BERTH), 0.0f),
            Arguments.of(createStop(type = StopType.BERTH, pomaType = StopType.BERTH, pomaId = "TEST_ID", name = "SOME_BERTH_NAME"), 0.5f),
            Arguments.of(createStop(type = StopType.BERTH, pomaType = StopType.BERTH, pomaId = "TEST_ID", name = "SOME_BERTH_NAME", detectionVersion = StopDetectionVersion.TRACE_BETWEEN_MOVEMENT_EVENT), 0.6f),
            Arguments.of(createStop(type = StopType.BERTH, detectionVersion = StopDetectionVersion.TRACE_BETWEEN_MOVEMENT_EVENT), 0.1f)
        )
    }

    @ParameterizedTest
    @MethodSource("stops")
    fun `should calculate accuracy of stop correctly`(stop: Stop, expectedAccuracy: Float) {
        val accuracy = stop.calculateAccuracy()

        assertEquals(expectedAccuracy, accuracy)
    }
}
