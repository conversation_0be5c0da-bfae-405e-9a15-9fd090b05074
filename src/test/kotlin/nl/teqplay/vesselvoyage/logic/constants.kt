package nl.teqplay.vesselvoyage.logic

import nl.teqplay.vesselvoyage.model.Location
import nl.teqplay.vesselvoyage.util.toSkeletonLocation

const val MMSI_1 = *********
const val MMSI_2 = *********
const val MMSI_3 = *********
const val IMO_1 = 1111111
const val IMO_2 = 2222222
const val IMO_3 = 3333333
const val BEANR = "BEANR"
const val NLRTM = "NLRTM"
const val NLDOR = "NLDOR"
const val NLVLA = "NLVLA"
const val NLVLI = "NLVLI"
const val NLTNZ = "NLTNZ"
const val BEGNE = "BEGNE"
const val DEHAM = "DEHAM"
const val ANCHOR_AREA_4EAST = "4EAST" // NLRTM
const val ANCHOR_AREA_SCHOUWENBANK = "ANCHOR SCHOUWENBANK (BEANR)" // BEANR, NLRTM, BEGNE, NLVLI, NLTNZ

val PORT_BEANR = TestPort(
    portId = BEANR,
    location = Location(0.0, 0.0)
)

val PORT_NLRTM = TestPort(
    portId = NLRTM,
    location = Location(1.0, 1.0)
)

val PORT_DEHAM = TestPort(
    portId = DEHAM,
    location = Location(2.0, 2.0)
)

val PORT_NLVLA = TestPort(
    portId = NLVLA,
    location = Location(3.0, 3.0)
)

val PORT_NLDOR = TestPort(
    portId = NLDOR,
    location = Location(4.0, 4.0)
)

val PORT_NLTNZ = TestPort(
    portId = NLTNZ,
    location = Location(5.0, 5.0)
)

val PORT_BEGNE = TestPort(
    portId = BEGNE,
    location = Location(6.0, 6.0)
)

val PORT_NOT_EXISTING = TestPort(
    portId = "Not Existing Port",
    location = Location(9.0, 9.0)
)

val ANCHOR_AREA_1 = TestAnchorArea(
    anchorAreaId = ANCHOR_AREA_4EAST,
    location = PORT_NLRTM.location.toSkeletonLocation(),
    destinations = setOf(PORT_NLRTM.portId)
)

val ANCHOR_AREA_2 = TestAnchorArea(
    anchorAreaId = ANCHOR_AREA_SCHOUWENBANK,
    location = PORT_BEANR.location.toSkeletonLocation(),
    destinations = setOf(PORT_BEANR.portId)
)
