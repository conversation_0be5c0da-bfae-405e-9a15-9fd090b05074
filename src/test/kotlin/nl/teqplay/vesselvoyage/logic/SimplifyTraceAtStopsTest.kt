package nl.teqplay.vesselvoyage.logic

import nl.teqplay.vesselvoyage.model.LocationTime
import nl.teqplay.vesselvoyage.model.SIMPLIFY_TRACE_LOCATIONS_HALFWAY_COUNT
import nl.teqplay.vesselvoyage.model.SIMPLIFY_TRACE_STOP_TIME_MARGIN
import nl.teqplay.vesselvoyage.model.Stop
import nl.teqplay.vesselvoyage.model.StopType
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.ZonedDateTime

class SimplifyTraceAtStopsTest {
    @Test
    fun `should calculate a stop location using actual location and time`() {
        val stop = createStop(
            type = StopType.BERTH,
            startTime = time,
            endTime = time.plusHours(24),
            location = PORT_NLRTM.location,
            actualLocation = PORT_NLRTM.location,
            actualTime = time
        )
        val locations = listOf(
            LocationTime(52.0, 40.0, time.plusHours(4)),
            LocationTime(52.0, 41.0, time.plusHours(9)),
            LocationTime(58.0, 48.0, time.plusHours(22)),
        )

        val result = calculateStopLocation(stop, locations)
        val expected = LocationTime(
            latitude = PORT_NLRTM.location.latitude,
            longitude = PORT_NLRTM.location.longitude,
            time = time
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should calculate a stop location by calculating averages`() {
        val stop = createStop(StopType.BERTH, time, time.plusHours(24))
        val locations = listOf(
            LocationTime(52.0, 40.0, time.plusHours(4)),
            LocationTime(52.0, 41.0, time.plusHours(9)),
            LocationTime(58.0, 48.0, time.plusHours(22)),
        )

        val stopLocation = calculateStopLocation(stop, locations)
        assertEquals(LocationTime(54.0, 43.0, time.plusHours(12)), stopLocation)
    }

    @Test
    fun `should only use the locations halfway to calculate a stop location`() {
        val stop = createStop(StopType.BERTH, time, time.plusHours(24))
        val locations = List(SIMPLIFY_TRACE_LOCATIONS_HALFWAY_COUNT) {
            LocationTime(52.0, 40.0, time.plusHours(12))
        } + listOf(
            // the following location time should not be used in the average
            LocationTime(0.0, 0.0, time.plusHours(1))
        )

        val stopLocation = calculateStopLocation(stop, locations)
        assertEquals(LocationTime(52.0, 40.0, time.plusHours(12)), stopLocation)
    }

    @Test
    fun `should calculate a stop location for an ongoing stop`() {
        val stop = createStop(StopType.BERTH, time, null)
        val locations = listOf(
            LocationTime(52.0, 40.0, time.plusHours(4)),
            LocationTime(52.0, 41.0, time.plusHours(9)),
            LocationTime(58.0, 48.0, time.plusHours(22)),
        )

        val stopLocation = calculateStopLocation(stop, locations)
        assertEquals(LocationTime(58.0, 48.0, time.plusHours(22)), stopLocation)
    }

    @Test
    fun `should return null when there are no locations`() {
        val stop = createStop(StopType.BERTH, time, time.plusHours(24))
        val locations = emptyList<LocationTime>()

        val stopLocation = calculateStopLocation(stop, locations)
        assertEquals(null, stopLocation)
    }

    @Test
    fun `simplifyTraceAtStops - one stop halfway`() {
        val locations = listOf(
            List(1) { locationBeforeStop1 },
            List(10) { locationDuringStop1 },
            List(1) { locationBeforeStop2 }
        ).flatten()

        val stops = listOf(stop1)

        val actual = simplifyTraceAtStops(locations, stops, time, null)
        assertEquals(listOf(locationBeforeStop1, locationDuringStop1, locationBeforeStop2), actual)
    }

    @Test
    fun `simplifyTraceAtStops - two stops halfway`() {
        val locations = listOf(
            List(1) { locationBeforeStop1 },
            List(10) { locationDuringStop1 },
            List(1) { locationBeforeStop2 },
            List(10) { locationDuringStop2 },
            List(1) { locationAfterStop2 }
        ).flatten()

        val stops = listOf(stop1, stop2)

        val actual = simplifyTraceAtStops(locations, stops, time, null)
        assertEquals(listOf(locationBeforeStop1, locationDuringStop1, locationBeforeStop2, locationDuringStop2, locationAfterStop2), actual)
    }

    @Test
    fun `simplifyTraceAtStops - stop at the start`() {
        val locations = listOf(
            List(10) { locationDuringStop1 },
            List(1) { locationBeforeStop2 },
        ).flatten()

        val stops = listOf(stop1)

        val actual = simplifyTraceAtStops(locations, stops, time, null)
        assertEquals(listOf(locationDuringStop1, locationBeforeStop2), actual)
    }

    @Test
    fun `simplifyTraceAtStops - stop at the end`() {
        val locations = listOf(
            List(1) { locationBeforeStop1 },
            List(10) { locationDuringStop1 },
        ).flatten()
        val stops = listOf(stop1)

        val actual = simplifyTraceAtStops(locations, stops, time, null)
        assertEquals(listOf(locationBeforeStop1, locationDuringStop1), actual)
    }

    @Test
    fun `simplifyTraceAtStops - locations only during stop`() {
        val locations = listOf(
            List(10) { locationDuringStop1 },
        ).flatten()
        val stops = listOf(stop1)

        val actual = simplifyTraceAtStops(locations, stops, time, null)
        assertEquals(listOf(locationDuringStop1), actual)
    }

    @Test
    fun `simplifyTraceAtStops - should apply a margin for locations close to the begin and end of a berth stop`() {
        val locations = listOf(
            List(1) { locationAtBeginOfStop1 },
            List(10) { locationDuringStop1 },
            List(1) { locationAtEndOfStop1 },
        ).flatten()

        // should apply time margins for berth stops
        assertEquals(
            listOf(locationAtBeginOfStop1, locationDuringStop1, locationAtEndOfStop1),
            simplifyTraceAtStops(locations, listOf(stop1), time, null)
        )

        // should NOT apply time margins for non-berth stops
        assertEquals(
            listOf(locationDuringStop1),
            simplifyTraceAtStops(locations, listOf(stop1.copy(type = StopType.ANCHOR_AREA)), time, null)
        )
        assertEquals(
            listOf(locationDuringStop1),
            simplifyTraceAtStops(locations, listOf(stop1.copy(type = StopType.UNCLASSIFIED)), time, null)
        )
    }

    @Test
    fun `simplifyTraceAtStops - no locations during stop`() {
        val locations = listOf(
            locationBeforeStop1,
            locationBeforeStop2
        )
        val stops = listOf(stop1)

        val actual = simplifyTraceAtStops(locations, stops, time, null)
        assertEquals(listOf(locationBeforeStop1, locationBeforeStop2), actual)
    }

    @Test
    fun `simplifyTraceAtStops - no locations at all`() {
        val locations = emptyList<LocationTime>()
        val stops = listOf(stop1)

        val actual = simplifyTraceAtStops(locations, stops, time, null)
        assertEquals(emptyList<LocationTime>(), actual)
    }

    @Test
    fun `simplifyTraceAtStops - ongoing stop`() {
        val locations = listOf(
            listOf(locationBeforeStop1),
            List(10) { locationDuringStop1 },
            listOf(locationBeforeStop2)
        ).flatten()
        val stops = listOf(stop1Ongoing)

        val actual = simplifyTraceAtStops(locations, stops, time, null)
        assertEquals(listOf(locationBeforeStop1, locationBeforeStop2), actual)
    }

    @Test
    fun `simplifyTraceAtStops - no stops at all`() {
        val locations = listOf(
            locationBeforeStop1,
            locationDuringStop1,
            locationBeforeStop2,
            locationDuringStop2,
            locationAfterStop2
        )
        val stops = emptyList<Stop>()

        val actual = simplifyTraceAtStops(locations, stops, time, null)
        assertEquals(locations, actual)
    }
}

private val time = ZonedDateTime.parse("2022-01-27T00:00Z")
private val stop1 = createStop(StopType.BERTH, time, time.plusHours(24))
private val stop1Ongoing = createStop(StopType.BERTH, time, null)
private val stop2 = createStop(StopType.BERTH, time.plusHours(48), time.plusHours(72))
private val locationBeforeStop1 = LocationTime(52.0, 40.0, time.minusHours(4))
private val locationAtBeginOfStop1 = LocationTime(
    53.0, 41.0,
    time.plus(SIMPLIFY_TRACE_STOP_TIME_MARGIN.dividedBy(2))
)
private val locationDuringStop1 = LocationTime(53.0, 41.0, time.plusHours(12))
private val locationAtEndOfStop1 = LocationTime(
    53.0, 41.0,
    time.plusHours(24).minus(SIMPLIFY_TRACE_STOP_TIME_MARGIN.dividedBy(2))
)
private val locationBeforeStop2 = LocationTime(54.0, 42.0, time.plusHours(25))
private val locationDuringStop2 = LocationTime(55.0, 43.0, time.plusHours(60))
private val locationAfterStop2 = LocationTime(56.0, 44.0, time.plusHours(74))
