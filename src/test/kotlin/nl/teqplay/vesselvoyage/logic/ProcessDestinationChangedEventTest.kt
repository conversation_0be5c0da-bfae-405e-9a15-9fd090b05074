package nl.teqplay.vesselvoyage.logic

import com.nhaarman.mockitokotlin2.mock
import nl.teqplay.aisengine.event.interfaces.AreaBasedEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.Change
import nl.teqplay.vesselvoyage.model.Destination
import nl.teqplay.vesselvoyage.model.InitialShipStatus
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.service.processing.destination.DestinationChangedProcessor
import nl.teqplay.vesselvoyage.util.toEta
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.ZoneOffset
import java.time.ZonedDateTime

class ProcessDestinationChangedEventTest {
    private val destinationChangedProcessor = DestinationChangedProcessor(mock())

    @Test
    fun `should update voyage with a destination changed event`() {
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")

        val voyage1start = createVoyage(
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC),
        )

        val destinationChangedEvent = createDestinationChangedEvent(
            time = "2021-03-09T05:00:00Z",
            newTrueDestination = "DEHAM",
            oldDestination = null,
            newDestination = "DEHAM"
        )

        val voyage1updated = voyage1start.copy(
            dest = Destination(
                updatedAt = ZonedDateTime.parse("2021-03-09T05:00:00Z"),
                aisDestination = "DEHAM",
                trueDestination = "DEHAM"
            )
        )

        val (status, changes) = destinationChangedProcessor.processEvent(VoyageShipStatus(voyage1start, null, null), destinationChangedEvent)

        assertEquals(VoyageShipStatus(voyage1updated, null, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, voyage1updated)
            ),
            changes
        )
    }

    @Test
    fun `should not update voyage with a destination changed event when there is no actual change`() {
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")

        val voyage1 = createVoyage(
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC),
            dest = Destination(
                updatedAt = event1end.actualTime.atZone(ZoneOffset.UTC),
                aisDestination = DEHAM,
                trueDestination = DEHAM
            )
        )

        val destinationChangedEvent = createDestinationChangedEvent(
            time = "2021-03-09T05:00:00Z",
            newTrueDestination = "DEHAM",
            oldDestination = null,
            newDestination = null
        )

        val statusBefore = VoyageShipStatus(voyage1, null, null)
        val (status, changes) = destinationChangedProcessor.processEvent(statusBefore, destinationChangedEvent)

        assertEquals(statusBefore, status)
        assertEquals(emptyList<Change>(), changes)
    }

    @Test
    fun `should clear eta when new destination of voyage differs from the portId of the eta`() {
        val event2end = createPortEventEnd(PORT_BEANR, "2021-03-09T04:00:00Z")

        val etaEvent = createEtaEvent(
            port = PORT_NLRTM,
            time = "2021-05-28T13:00:00Z",
            predictedTime = "2021-05-29T08:00:00Z"
        )

        val voyage2 = createVoyage(
            startPortIds = listOf(event2end.area.unlocode ?: ""),
            startTime = event2end.actualTime.atZone(ZoneOffset.UTC),
            dest = Destination(
                updatedAt = event2end.actualTime.atZone(ZoneOffset.UTC),
                aisDestination = etaEvent.area.unlocode ?: "",
                trueDestination = etaEvent.area.unlocode ?: ""
            ),
            eta = etaEvent.toEta(),
        )

        val destinationChangedEvent = createDestinationChangedEvent(
            time = "2021-03-09T05:00:00Z",
            newTrueDestination = "DEHAM",
            oldDestination = null,
            newDestination = "DEHAM"
        )

        val voyage2updated = voyage2.copy(
            dest = Destination(
                updatedAt = ZonedDateTime.parse("2021-03-09T05:00:00Z"),
                aisDestination = "DEHAM",
                trueDestination = "DEHAM"
            ),
            eta = null
        )

        val (status, changes) = destinationChangedProcessor.processEvent(VoyageShipStatus(voyage2, null, null), destinationChangedEvent)

        assertEquals(VoyageShipStatus(voyage2updated, null, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, voyage2updated)
            ),
            changes
        )
    }

    @Test
    fun `should update visit with a destination changed event`() {
        val event1start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")

        val visit1start = createVisit(
            portAreas = listOf(createPortAreaVisit(event1start))
        )

        val destinationChangedEvent = createDestinationChangedEvent(
            time = "2021-03-09T05:00:00Z",
            newTrueDestination = "DEHAM",
            oldDestination = null,
            newDestination = "DEHAM"
        )

        val visit1updated = visit1start.copy(
            dest = Destination(
                updatedAt = ZonedDateTime.parse("2021-03-09T05:00:00Z"),
                aisDestination = "DEHAM",
                trueDestination = "DEHAM"
            )
        )

        val statusBefore = VisitShipStatus(visit1start, null, null)
        val (status, changes) = destinationChangedProcessor.processEvent(statusBefore, destinationChangedEvent)

        assertEquals(VisitShipStatus(visit1updated, null, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, visit1updated)
            ),
            changes
        )
    }

    @Test
    fun `should not update visit with a destination changed event when there is no actual change`() {
        val event1start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")

        val visit1 = createVisit(
            _id = event1start._id,
            portAreas = listOf(createPortAreaVisit(event1start)),
            dest = Destination(
                updatedAt = event1start.actualTime.atZone(ZoneOffset.UTC),
                aisDestination = "DEHAM",
                trueDestination = "DEHAM"
            )
        )

        val destinationChangedEvent = createDestinationChangedEvent(
            time = "2021-03-09T05:00:00Z",
            newTrueDestination = "DEHAM",
            oldDestination = null,
            newDestination = null
        )

        val statusBefore = VisitShipStatus(visit1, null, null)
        val (status, changes) = destinationChangedProcessor.processEvent(statusBefore, destinationChangedEvent)

        assertEquals(statusBefore, status)
        assertEquals(emptyList<Change>(), changes)
    }

    @Test
    fun `should clear eta when new destination of visit differs from the portId of the eta`() {
        val event2start = createPortEventStart(PORT_BEANR, "2021-03-08T00:00:00Z")

        val etaEvent = createEtaEvent(
            port = PORT_NLRTM,
            time = "2021-05-28T13:00:00Z",
            predictedTime = "2021-05-29T08:00:00Z"
        )

        val visit2start = createVisit(
            _id = event2start._id,
            portAreas = listOf(createPortAreaVisit(event2start)),
            dest = Destination(
                updatedAt = event2start.actualTime.atZone(ZoneOffset.UTC),
                aisDestination = (etaEvent as AreaBasedEvent).area.unlocode ?: "",
                trueDestination = (etaEvent as AreaBasedEvent).area.unlocode ?: ""
            ),
            eta = etaEvent.toEta()
        )

        val destinationChangedEvent = createDestinationChangedEvent(
            time = "2021-03-09T05:00:00Z",
            newTrueDestination = "DEHAM",
            oldDestination = null,
            newDestination = "DEHAM"
        )

        val visit2updated = visit2start.copy(
            dest = Destination(
                updatedAt = ZonedDateTime.parse("2021-03-09T05:00:00Z"),
                aisDestination = "DEHAM",
                trueDestination = "DEHAM"
            ),
            eta = null
        )

        val statusBefore = VisitShipStatus(visit2start, null, null)
        val (status, changes) = destinationChangedProcessor.processEvent(statusBefore, destinationChangedEvent)

        assertEquals(VisitShipStatus(visit2updated, null, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, visit2updated)
            ),
            changes
        )
    }

    @Test
    fun `should update initial state with a destination changed event`() {
        val destinationChangedEvent = createDestinationChangedEvent(
            time = "2021-03-09T05:00:00Z",
            newTrueDestination = "DEHAM",
            oldDestination = null,
            newDestination = null
        )

        val (status, changes) = destinationChangedProcessor.processEvent(InitialShipStatus, destinationChangedEvent)

        assertEquals(InitialShipStatus, status)
        assertEquals(emptyList<List<Change>>(), changes)
    }

    @Test
    fun `should ignore destination changed when event time is outdated`() {
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")

        val voyage1start = createVoyage(
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC),
        )

        val destinationChangedEvent = createDestinationChangedEvent(
            time = "2021-02-09T05:00:00Z",
            newTrueDestination = "DEHAM",
            oldDestination = null,
            newDestination = null
        )

        val (status, changes) = destinationChangedProcessor.processEvent(VoyageShipStatus(voyage1start, null, null), destinationChangedEvent)

        assertEquals(VoyageShipStatus(voyage1start, null, null), status)
        assertEquals(emptyList<List<Change>>(), changes)
    }

    @Test
    fun `should ignore destination changed when event IMO doesn't match`() {
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")

        val voyage1start = createVoyage(
            _id = event1end._id,
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC),
        )

        val destinationChangedEvent = createDestinationChangedEvent(
            time = "2021-03-09T05:00:00Z",
            newTrueDestination = "DEHAM",
            oldDestination = null,
            newDestination = null
        ).copy(
            ship = AisShipIdentifier(0)
        )

        val (status, changes) = destinationChangedProcessor.processEvent(VoyageShipStatus(voyage1start, null, null), destinationChangedEvent)

        assertEquals(VoyageShipStatus(voyage1start, null, null), status)
        assertEquals(emptyList<List<Change>>(), changes)
    }

    @Test
    fun `should update visit with dest object`() {
        val event1start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")

        val visit1start = createVisit(
            portAreas = listOf(createPortAreaVisit(event1start))
        )

        val destinationChangedEvent = createDestinationChangedEvent(
            time = "2021-03-09T05:00:00Z",
            newTrueDestination = "DEHAM",
            oldDestination = "ROTTERDAM",
            newDestination = "HAMBURG",
        )

        val visit1updated = visit1start.copy(
            dest = Destination(
                updatedAt = ZonedDateTime.parse("2021-03-09T05:00:00Z"),
                aisDestination = "HAMBURG",
                trueDestination = "DEHAM"
            )
        )

        val statusBefore = VisitShipStatus(visit1start, null, null)
        val (status, changes) = destinationChangedProcessor.processEvent(statusBefore, destinationChangedEvent)
        assertEquals(VisitShipStatus(visit1updated, null, null), status)
    }

    @Test
    fun `should update voyage with dest object`() {
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")

        val voyage1start = createVoyage(
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC),
        )

        val destinationChangedEvent = createDestinationChangedEvent(
            time = "2021-03-09T05:00:00Z",
            newTrueDestination = "DEHAM",
            oldDestination = "ROTTERDAM",
            newDestination = "HAMBURG"
        )

        val voyage1updated = voyage1start.copy(
            dest = Destination(
                updatedAt = ZonedDateTime.parse("2021-03-09T05:00:00Z"),
                aisDestination = "HAMBURG",
                trueDestination = "DEHAM"
            )
        )

        val (status, changes) = destinationChangedProcessor.processEvent(VoyageShipStatus(voyage1start, null, null), destinationChangedEvent)

        assertEquals(VoyageShipStatus(voyage1updated, null, null), status)
    }

    @Test
    fun `shouldn't update dest object when trueDestination is null`() {
        val event1start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")

        val visit1 = createVisit(portAreas = listOf(createPortAreaVisit(event1start)))
        val voyage1 = createVoyage(
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC)
        )

        val destinationChangedEvent1 = createDestinationChangedEvent(
            time = "2021-03-09T05:00:00Z",
            newTrueDestination = null,
            oldDestination = null,
            newDestination = null,
        )

        val destinationChangedEvent2 = createDestinationChangedEvent(
            time = "2021-03-09T05:00:00Z",
            newTrueDestination = null,
            oldDestination = null,
            newDestination = "NLRTM",
        )

        val statusBefore = VisitShipStatus(visit1, null, null)
        val statusBefore2 = VoyageShipStatus(voyage1, null, null)

        val (status1, _) = destinationChangedProcessor.processEvent(
            VisitShipStatus(visit1, null, null),
            destinationChangedEvent1
        )
        val (status2, _) = destinationChangedProcessor.processEvent(
            VisitShipStatus(visit1, null, null),
            destinationChangedEvent2
        )
        val (status3, _) = destinationChangedProcessor.processEvent(
            VoyageShipStatus(voyage1, null, null),
            destinationChangedEvent1
        )
        val (status4, _) = destinationChangedProcessor.processEvent(
            VoyageShipStatus(voyage1, null, null),
            destinationChangedEvent2
        )

        assertEquals(statusBefore, status1)
        assertEquals(statusBefore, status2)
        assertEquals(statusBefore2, status3)
        assertEquals(statusBefore2, status4)
    }
}
