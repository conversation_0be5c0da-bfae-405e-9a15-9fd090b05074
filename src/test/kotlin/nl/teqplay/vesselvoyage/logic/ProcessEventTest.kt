package nl.teqplay.vesselvoyage.logic

import com.fasterxml.jackson.module.kotlin.readValue
import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.whenever
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.skeleton.model.Location
import nl.teqplay.vesselvoyage.datasource.ProcessorLogDatasource
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.AnchorAreaVisit
import nl.teqplay.vesselvoyage.model.Change
import nl.teqplay.vesselvoyage.model.Destination
import nl.teqplay.vesselvoyage.model.ESof
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.EventStatus
import nl.teqplay.vesselvoyage.model.InitialShipStatus
import nl.teqplay.vesselvoyage.model.MovementStatus
import nl.teqplay.vesselvoyage.model.StopType
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.model.createVisitId
import nl.teqplay.vesselvoyage.model.createVoyageId
import nl.teqplay.vesselvoyage.model.lightweight.poma.Anchorage
import nl.teqplay.vesselvoyage.model.lightweight.poma.Port
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.AisFetchingService
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.StaticShipInfoService
import nl.teqplay.vesselvoyage.service.processing.EventProcessingService
import nl.teqplay.vesselvoyage.service.processing.anchor.AnchorProcessor
import nl.teqplay.vesselvoyage.service.processing.anchorarea.AnchorAreaProcessor
import nl.teqplay.vesselvoyage.service.processing.approach.ApproachAreaProcessor
import nl.teqplay.vesselvoyage.service.processing.berth.UniqueBerthProcessor
import nl.teqplay.vesselvoyage.service.processing.destination.DestinationChangedProcessor
import nl.teqplay.vesselvoyage.service.processing.encounter.EncounterProcessor
import nl.teqplay.vesselvoyage.service.processing.eosp.EndOfSeaPassageProcessor
import nl.teqplay.vesselvoyage.service.processing.eta.EtaProcessor
import nl.teqplay.vesselvoyage.service.processing.lock.LockAreaProcessor
import nl.teqplay.vesselvoyage.service.processing.movement.MovementProcessor
import nl.teqplay.vesselvoyage.service.processing.pilot.PilotAreaProcessor
import nl.teqplay.vesselvoyage.service.processing.port.PortProcessor
import nl.teqplay.vesselvoyage.service.processing.shiptoship.ShipToShipTransferProcessor
import nl.teqplay.vesselvoyage.service.processing.status.StatusChangedProcessor
import nl.teqplay.vesselvoyage.service.processing.stop.StopProcessor
import nl.teqplay.vesselvoyage.service.processing.terminalmooring.TerminalMooringAreaProcessor
import nl.teqplay.vesselvoyage.util.getCurrentEntry
import nl.teqplay.vesselvoyage.util.globalObjectMapper
import nl.teqplay.vesselvoyage.util.loadResource
import nl.teqplay.vesselvoyage.util.toEta
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import java.time.Duration
import java.time.ZoneOffset
import java.time.ZonedDateTime

class ProcessEventTest {
    private val config = EventProcessingProperties(
        maxSpeedMps = 1.0,
        minDuration = Duration.ofMinutes(30),
        newStopDetection = true,
        enableTraceCalculations = true,
        enableSlowMovingPeriods = true,
        enableNewDefinition = false,
        enableOldDefinition = true,
        totalThreads = 5,
        logResults = false,
        activenessMonitoring = EventProcessingProperties.ActivenessMonitoring(
            enabled = false,
            interval = 1000,
            maxIdleTime = Duration.ofMinutes(5)
        )
    )

    private val infraService = mock<InfraService> {
        whenever(it.getPortByUnlocode(any())).thenAnswer { func ->
            val unlocode = func.arguments.firstOrNull()
            testPorts[unlocode]
        }
        whenever(it.getAnchorage(any(), any()))
            .thenReturn(
                Anchorage(
                    name = "TEST_ANCHORAGE",
                    ports = listOf("BEANR"),
                    location = Location(0.0, 0.0),
                    _id = ""
                )
            )
    }
    private val aisFetchingService = mock<AisFetchingService>()
    private val staticShipInfoService = mock<StaticShipInfoService>()
    private val processorLogDatasource = mock<ProcessorLogDatasource>()

    private val eventProcessingService = EventProcessingService(
        AnchorProcessor(config, infraService),
        DestinationChangedProcessor(config),
        EncounterProcessor(config, SimpleMeterRegistry()),
        ShipToShipTransferProcessor(config, SimpleMeterRegistry()),
        EtaProcessor(config),
        MovementProcessor(config, infraService, aisFetchingService),
        PortProcessor(config, infraService, aisFetchingService, staticShipInfoService),
        StatusChangedProcessor(config),
        UniqueBerthProcessor(config, infraService, aisFetchingService),
        EndOfSeaPassageProcessor(config, infraService),
        StopProcessor(config, SimpleMeterRegistry(), infraService, emptyList()),
        PilotAreaProcessor(config),
        AnchorAreaProcessor(config),
        TerminalMooringAreaProcessor(config),
        LockAreaProcessor(config),
        ApproachAreaProcessor(config),
        processorLogDatasource,
        mock()
    )

    private val portResources = listOf(
        "ports/nlrtm.json",
        "ports/nlvla.json",
        "ports/nldor.json",
        "ports/beanr.json",
        "ports/deham.json"
    )
    private val testPorts: Map<String, Port> = portResources
        .map<String, Port> { globalObjectMapper.readValue(loadResource(it)) }
        .associateBy { it.unlocode!! }

    @Test
    fun `should process a PortEvent`() {
        val event1start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")

        val visit1start = createVisit(
            _id = createVisitId(event1start._id),
            portAreas = listOf(createPortAreaVisit(event1start)),
        )

        val result = eventProcessingService.onEvent(InitialShipStatus, event1start)

        assertEquals(VisitShipStatus(visit1start, null, null), result.status)
        assertEquals(
            listOf(
                Change(Action.CREATE, visit1start)
            ),
            result.changes
        )
        assertEquals(emptyList<EventProcessingIssue>(), result.issues)
    }

    @Test
    fun `should process a DestinationChangedEvent`() {
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")

        val voyage1start = createVoyage(
            _id = createVoyageId(event1end._id),
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC),
        )

        val destinationChangedEvent = createDestinationChangedEvent(
            time = "2021-03-09T05:00:00Z",
            newTrueDestination = BEANR,
            oldDestination = null,
            newDestination = BEANR
        )

        val voyage1startWithDestination = voyage1start.copy(
            dest = Destination(
                updatedAt = ZonedDateTime.parse("2021-03-09T05:00:00Z"),
                trueDestination = "BEANR",
                aisDestination = "BEANR",
            )
        )

        val statusBefore = VoyageShipStatus(voyage1start, null, null)

        val result = eventProcessingService.onEvent(statusBefore, destinationChangedEvent)

        assertEquals(VoyageShipStatus(voyage1startWithDestination, null, null), result.status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, voyage1startWithDestination)
            ),
            result.changes
        )
        assertEquals(emptyList<EventProcessingIssue>(), result.issues)
    }

    @Test
    fun `should process an AnchorEvent`() {
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")
        val destination = Destination(
            updatedAt = event1end.actualTime.atZone(ZoneOffset.UTC),
            aisDestination = BEANR,
            trueDestination = BEANR
        )

        val voyage1startWithDestination = createVoyage(
            _id = createVoyageId(event1end._id),
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC),
            dest = destination
        )

        val anchorEvent = createAnchorEventStart(ANCHOR_AREA_1, "2021-03-09T08:00:00Z")

        val voyage1end = createVoyage(
            _id = createVoyageId(event1end._id),
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC),
            dest = Destination(
                updatedAt = event1end.actualTime.atZone(ZoneOffset.UTC),
                aisDestination = BEANR,
                trueDestination = BEANR
            ),
            endPortIds = null,
            endTime = anchorEvent.actualTime.atZone(ZoneOffset.UTC),
            finished = true,
            nextEntryId = createVisitId(anchorEvent._id)
        )

        val visit2start = createVisit(
            _id = createVisitId(anchorEvent._id),
            anchorAreas = listOf(
                AnchorAreaVisit(
                    anchorAreaId = ANCHOR_AREA_1.anchorAreaId,
                    startEventId = anchorEvent._id,
                    startTime = anchorEvent.actualTime.atZone(ZoneOffset.UTC),
                    destinations = setOf(BEANR),
                    endEventId = null,
                    endTime = null
                )
            ),
            dest = destination,
            eta = null,
            previousEntryId = createVoyageId(event1end._id),
        )

        val statusBefore = VoyageShipStatus(voyage1startWithDestination, null, null)

        val result = eventProcessingService.onEvent(statusBefore, anchorEvent)
        val expectedVisitStatus = VisitShipStatus(visit2start, voyage1end, null)

        assertEquals(expectedVisitStatus, result.status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, voyage1end),
                Change(Action.CREATE, visit2start)
            ),
            result.changes
        )
        assertEquals(emptyList<EventProcessingIssue>(), result.issues)
    }

    @Test
    fun `should process an EtaEvent`() {
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")

        val voyage1startWithDestination = createVoyage(
            _id = createVoyageId(event1end._id),
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC),
            dest = Destination(
                updatedAt = event1end.actualTime.atZone(ZoneOffset.UTC),
                aisDestination = BEANR,
                trueDestination = BEANR
            )
        )

        val etaEvent = createEtaEvent(
            port = PORT_BEANR,
            time = "2021-05-28T13:00:00Z",
            predictedTime = "2021-05-29T08:00:00Z"
        )

        val voyage1startWithEta = voyage1startWithDestination.copy(
            eta = etaEvent.toEta()
        )

        val statusBefore = VoyageShipStatus(voyage1startWithDestination, null, null)

        val result = eventProcessingService.onEvent(statusBefore, etaEvent)

        assertEquals(VoyageShipStatus(voyage1startWithEta, null, null), result.status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, voyage1startWithEta)
            ),
            result.changes
        )
        assertEquals(emptyList<EventProcessingIssue>(), result.issues)
    }

    @Test
    fun `should process an EncounterEvent`() {
        val portEvent = createPortEventStart(PORT_NLRTM, "2021-11-21T00:00:00Z")

        val timeStart = "2021-11-22T00:00:00Z"
        val visit = createVisit(
            portAreas = listOf(createPortAreaVisit(portEvent))
        )
        val encounterEventStart = createEncounterEvent(EncounterEvent.EncounterType.TUG, EventStatus.START, MMSI_2.toString(), IMO_2.toString(), timeStart)

        val statusBefore = VisitShipStatus(visit, null, null)
        val (status, changes) = eventProcessingService.onEvent(statusBefore, encounterEventStart)

        assertNotEquals(statusBefore, status)
        assertNotNull(status.getCurrentEntry()?.esof)
        assertEquals(1, changes.size)
    }

    @Test
    fun `should process a MovementEvent`() {
        val portEvent = createPortEventStart(PORT_NLRTM, "2021-11-21T00:00:00Z")

        val timeStop = "2021-11-22T00:00:00Z"
        val movementEvent = createMovementEvent(MovementStatus.STOP, timeStop)
        val visit = createVisit(
            portAreas = listOf(createPortAreaVisit(portEvent))
        )

        val statusBefore = VisitShipStatus(visit, null, null)
        val result = eventProcessingService.onEvent(statusBefore, movementEvent)

        assertNotEquals(statusBefore, result.status)
        assertNotNull(result.status.getCurrentEntry()?.esof)
        assertEquals(1, result.changes.size)
        assertEquals(emptyList<EventProcessingIssue>(), result.issues)
    }

    @Test
    fun `should process a StatusChangedEvent`() {
        val time = "2021-11-22T00:00:00Z"

        val portEvent = createPortEventStart(PORT_NLRTM, "2021-11-21T00:00:00Z")

        val visit = createVisit(
            portAreas = listOf(createPortAreaVisit(portEvent)),
            esof = ESof(
                encounters = emptyList(),
                stops = listOf(
                    createStop(
                        type = StopType.UNCLASSIFIED,
                        startTime = time,
                        endTime = null
                    )
                ),
                slowMovingPeriods = null
            )
        )

        val statusChangedEvent = createStatusChangedEvent(AisMessage.ShipStatus.AT_ANCHOR, time)

        val statusBefore = VisitShipStatus(visit, null, null)
        val result = eventProcessingService.onEvent(statusBefore, statusChangedEvent)

        assertNotEquals(statusBefore, result.status)
        assertNotNull(result.status.getCurrentEntry()?.esof)
        assertEquals(1, result.changes.size)
        assertEquals(emptyList<EventProcessingIssue>(), result.issues)
    }
}
