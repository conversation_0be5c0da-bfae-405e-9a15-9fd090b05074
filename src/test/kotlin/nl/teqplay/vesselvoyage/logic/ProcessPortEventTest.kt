package nl.teqplay.vesselvoyage.logic

import com.fasterxml.jackson.module.kotlin.readValue
import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.whenever
import nl.teqplay.aisengine.event.model.AreaEndEvent
import nl.teqplay.aisengine.event.model.AreaStartEvent
import nl.teqplay.aisengine.event.model.EtaEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.poma.api.v1.Port
import nl.teqplay.vesselvoyage.ApplicationTestConfig
import nl.teqplay.vesselvoyage.mapper.PomaMapper
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.AnchorAreaVisit
import nl.teqplay.vesselvoyage.model.Change
import nl.teqplay.vesselvoyage.model.ESof
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.InitialShipStatus
import nl.teqplay.vesselvoyage.model.Location
import nl.teqplay.vesselvoyage.model.MAX_PORT_AREAS
import nl.teqplay.vesselvoyage.model.StopType
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.model.createVisitId
import nl.teqplay.vesselvoyage.model.createVoyageId
import nl.teqplay.vesselvoyage.model.lightweight.poma.Anchorage
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.AisFetchingService
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.StaticShipInfoService
import nl.teqplay.vesselvoyage.service.processing.port.PortProcessor
import nl.teqplay.vesselvoyage.util.eventIsPartOfVisit
import nl.teqplay.vesselvoyage.util.getCurrentEntry
import nl.teqplay.vesselvoyage.util.globalObjectMapper
import nl.teqplay.vesselvoyage.util.loadResource
import nl.teqplay.vesselvoyage.util.toEta
import nl.teqplay.vesselvoyage.util.toSkeletonLocation
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNotEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.TestConstructor
import java.time.Duration
import java.time.ZoneOffset
import java.time.ZonedDateTime
import nl.teqplay.vesselvoyage.model.lightweight.poma.Port as PomaPort

@Import(ApplicationTestConfig::class)
@SpringBootTest
@ActiveProfiles("api")
@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
@ContextConfiguration
class ProcessPortEventTest(
    private val pomaMapper: PomaMapper
) {
    private val config = EventProcessingProperties(
        maxSpeedMps = 1.0,
        minDuration = Duration.ofMinutes(30),
        newStopDetection = true,
        enableTraceCalculations = true,
        enableSlowMovingPeriods = true,
        enableNewDefinition = false,
        enableOldDefinition = true,
        totalThreads = 5,
        logResults = false,
        activenessMonitoring = EventProcessingProperties.ActivenessMonitoring(
            enabled = false,
            interval = 1000,
            maxIdleTime = Duration.ofMinutes(5)
        )
    )

    private val portResources = listOf(
        "ports/nlrtm.json",
        "ports/nlvla.json",
        "ports/nldor.json",
        "ports/beanr.json",
        "ports/deham.json"
    )

    private val testPorts: Map<String, PomaPort> = portResources
        .map<String, Port> { globalObjectMapper.readValue(loadResource(it)) }
        .map { pomaMapper.toLightweightPort(it) }
        .associateBy { it.unlocode!! }

    private fun findPortById(portId: String): PomaPort? {
        return testPorts[portId]
    }

    private fun findAnchorage(name: String, location: Location): Anchorage? {
        return ALL_ANCHOR_AREAS[name]
    }

    private val infraService = mock<InfraService> { service ->
        whenever(service.getPortByUnlocode(any())).thenAnswer { findPortById(it.getArgument(0)) }
        whenever(service.getBerthsByLocation(any())).thenReturn(emptyList())
        whenever(service.getAnchorage(any(), any())).thenAnswer { findAnchorage(it.getArgument(0), it.getArgument(1)) }
    }

    private val aisFetchingService = mock<AisFetchingService> {
        whenever(it.getTraceForStopDetection(any(), any())).thenReturn(emptyList())
    }

    private val staticShipInfoService = mock<StaticShipInfoService> {
        whenever(it.getShipRegisterInfoCacheByIMO(any())).thenReturn(null)
    }

    private val portProcessor = PortProcessor(config, infraService, aisFetchingService, staticShipInfoService)

    @Test
    fun `initialize ship status with start event`() {
        val eventStart = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")

        val visitStart = createVisit(
            _id = createVisitId(eventStart._id),
            portAreas = listOf(createPortAreaVisit(eventStart))
        )

        val (status, changes) = portProcessor.processEvent(InitialShipStatus, eventStart)

        assertEquals(VisitShipStatus(visitStart, null, null), status)
        assertEquals(
            listOf(
                Change(Action.CREATE, visitStart)
            ),
            changes
        )
    }

    @Test
    fun `update visit with a second start event`() {
        val eventStart1 = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")
        val eventStart2 = createPortEventStart(PORT_NLVLA, "2021-03-09T00:00:00Z")
        val dest = createDestination(BEGNE, eventStart1.actualTime.atZone(ZoneOffset.UTC))

        val visitStart = createVisit(
            _id = createVisitId(eventStart1._id),
            portAreas = listOf(createPortAreaVisit(eventStart1)),
            dest = dest,
            eta = ETA_BEGNE
        )

        val visitTwoStartEvents = createVisit(
            _id = createVisitId(eventStart1._id),
            portAreas = listOf(
                createPortAreaVisit(eventStart1),
                createPortAreaVisit(eventStart2),
            ),
            dest = dest,
            eta = ETA_BEGNE
        )

        val (status, changes) = portProcessor.processEvent(
            VisitShipStatus(visitStart, null, null), eventStart2
        )

        assertEquals(VisitShipStatus(visitTwoStartEvents, null, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, visitTwoStartEvents)
            ),
            changes
        )
    }

    @Test
    fun `update visit having two start events with one end event`() {
        val event1start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")
        val event2start = createPortEventStart(PORT_NLVLA, "2021-03-09T00:00:00Z")
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")
        val dest = createDestination(BEGNE, event1start.actualTime.atZone(ZoneOffset.UTC))

        val visitTwoStartEvents = createVisit(
            _id = createVisitId(event1start._id),
            portAreas = listOf(
                createPortAreaVisit(event1start),
                createPortAreaVisit(event2start),
            ),
            dest = dest,
            eta = ETA_BEGNE
        )

        val visitTwoStartEventsOneEnd = createVisit(
            _id = createVisitId(event1start._id),
            portAreas = listOf(
                createPortAreaVisit(event1start, event1end),
                createPortAreaVisit(event2start),
            ),
            dest = dest,
            eta = ETA_BEGNE
        )

        val (status, changes) = portProcessor.processEvent(
            VisitShipStatus(visitTwoStartEvents, null, null),
            event1end
        )

        assertEquals(VisitShipStatus(visitTwoStartEventsOneEnd, null, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, visitTwoStartEventsOneEnd)
            ),
            changes
        )
    }

    @Test
    fun `update visit having two start events with one pass-through end event on the second port area`() {
        val event1start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")
        val event2start = createPortEventStart(PORT_NLVLA, "2021-03-09T00:00:00Z")
        val event2endPassThrough = createPortEventEnd(PORT_NLVLA, "2021-03-09T00:15:00Z") // 15 minutes after the start
        val dest = createDestination(BEGNE, event1start.actualTime.atZone(ZoneOffset.UTC))

        val visitTwoStartEvents = createVisit(
            _id = createVisitId(event1start._id),
            portAreas = listOf(
                createPortAreaVisit(event1start),
                createPortAreaVisit(event2start),
            ),
            dest = dest,
            eta = ETA_BEGNE
        )

        val visitOneStartEvent = createVisit(
            _id = createVisitId(event1start._id),
            portAreas = listOf(createPortAreaVisit(event1start)),
            passThroughAreas = listOf(createPortAreaVisit(event2start, event2endPassThrough)),
            dest = dest,
            eta = ETA_BEGNE
        )

        val (status, changes) = portProcessor.processEvent(
            VisitShipStatus(visitTwoStartEvents, null, null),
            event2endPassThrough
        )

        assertEquals(VisitShipStatus(visitOneStartEvent, null, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, visitOneStartEvent)
            ),
            changes
        )
    }

    @Test
    fun `update visit having two start events with one pass-through end event on the first port area`() {
        val event0end = createPortEventEnd(PORT_BEANR, "2021-03-05T00:00:00Z")
        val event1start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")
        val event2start = createPortEventStart(PORT_NLVLA, "2021-03-09T00:00:00Z")
        val dest = createDestination(BEGNE, event1start.actualTime.atZone(ZoneOffset.UTC))

        // 15 minutes is seen as a pass through, and will be filtered
        val eventEnd1PassThrough = createPortEventEnd(PORT_NLRTM, event1start.actualTime.atZone(ZoneOffset.UTC).plusMinutes(15))

        val portAreaVisit1 = createPortAreaVisit(event1start)
        val portAreaVisit1Finished = createPortAreaVisit(event1start, eventEnd1PassThrough)
        val portAreaVisit2 = createPortAreaVisit(event2start)

        val visitTwoStartEvents = createVisit(
            _id = createVisitId(event1start._id),
            portAreas = listOf(portAreaVisit1, portAreaVisit2,),
            dest = dest,
            eta = ETA_BEGNE
        )

        val voyageWithOneEndPort = createVoyage(
            _id = createVoyageId(event1start._id),
            startPortIds = listOf(event0end.area.unlocode ?: ""),
            startTime = event0end.actualTime.atZone(ZoneOffset.UTC),
            dest = dest,
            eta = ETA_BEGNE,
            endPortIds = listOf(event1start.area.unlocode ?: ""),
            endTime = ZonedDateTime.parse("2021-03-07T11:00:00Z"),
            finished = true
        )

        val visitSecondAreaOnly = createVisit(
            createVisitId(event1start._id),
            portAreas = listOf(portAreaVisit2),
            passThroughAreas = listOf(portAreaVisit1Finished),
            dest = dest,
            eta = ETA_BEGNE
        )

        val voyageWithTwoEndPorts = voyageWithOneEndPort.copy(
            endPortIds = listOf(event2start.area.unlocode ?: ""),
            endTime = event2start.actualTime.atZone(ZoneOffset.UTC)
        )

        val (status, changes) = portProcessor.processEvent(
            VisitShipStatus(visitTwoStartEvents, voyageWithOneEndPort, null),
            eventEnd1PassThrough
        )

        assertEquals(VisitShipStatus(visitSecondAreaOnly, voyageWithTwoEndPorts, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, voyageWithTwoEndPorts),
                Change(Action.UPDATE, visitSecondAreaOnly)
            ),
            changes
        )
    }

    @Test
    fun `adding a second port area to a visit should update previous voyage with two port ids`() {
        val event0end = createPortEventEnd(PORT_BEANR, "2021-03-05T00:00:00Z")
        val event1start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")
        val anchorEvent4start = createAnchorEventStart(ANCHOR_AREA_1, "2021-03-07T11:00:00Z")
        val event2start = createPortEventStart(PORT_NLVLA, "2021-03-09T00:00:00Z")
        val dest = createDestination(BEGNE, event1start.actualTime.atZone(ZoneOffset.UTC))

        val voyage0 = createVoyage(
            _id = createVoyageId(event1start._id),
            startPortIds = listOf(event0end.area.unlocode ?: ""),
            startTime = event0end.actualTime.atZone(ZoneOffset.UTC),
            dest = dest,
            eta = ETA_BEGNE,
            endPortIds = listOf(event1start.area.unlocode ?: ""),
            endTime = anchorEvent4start.actualTime.atZone(ZoneOffset.UTC),
            finished = true
        )

        val visit1 = createVisit(
            _id = createVisitId(event1start._id),
            portAreas = listOf(createPortAreaVisit(event1start)),
            dest = dest,
            eta = ETA_BEGNE
        )

        val visit1TwoStartEvents = createVisit(
            _id = createVisitId(event1start._id),
            portAreas = listOf(
                createPortAreaVisit(event1start),
                createPortAreaVisit(event2start),
            ),
            dest = dest,
            eta = ETA_BEGNE
        )

        val voyageUpdatedWithTwoEndPorts = voyage0.copy(
            endPortIds = listOf(event1start.area.unlocode ?: "", event2start.area.unlocode ?: ""),
            endTime = event1start.actualTime.atZone(ZoneOffset.UTC)
        )

        val (status, changes) = portProcessor.processEvent(
            VisitShipStatus(visit1, voyage0, null),
            event2start
        )

        assertEquals(VisitShipStatus(visit1TwoStartEvents, voyageUpdatedWithTwoEndPorts, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, voyageUpdatedWithTwoEndPorts),
                Change(Action.UPDATE, visit1TwoStartEvents)
            ),
            changes
        )
    }

    // TODO: add a unit test to validate that the previousVoyage is updated when a visit with two port areas is
    //  finished in such a way that the second area is removed (pass-through)

    @Test
    fun `should create new voyage with two port ids when visit has two port areas`() {
        val event1start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")
        val event3start = createPortEventStart(PORT_NLVLA, "2021-03-09T00:00:00Z")
        val event3end = createPortEventEnd(PORT_NLVLA, "2021-03-11T00:00:00Z")
        val dest = createDestination(BEGNE, event1start.actualTime.atZone(ZoneOffset.UTC))

        val visitTwoStartAndOneEnd = createVisit(
            _id = createVisitId(event1start._id),
            portAreas = listOf(
                createPortAreaVisit(event1start, event1end),
                createPortAreaVisit(event3start),
            ),
            dest = dest,
            eta = ETA_BEGNE
        )

        val voyageStart = createVoyage(
            _id = createVoyageId(event3end._id),
            startPortIds = listOf(event1start.area.unlocode ?: "", event3start.area.unlocode ?: ""),
            startTime = event3end.actualTime.atZone(ZoneOffset.UTC),
            dest = dest,
            eta = ETA_BEGNE,
            previousEntryId = createVisitId(event1start._id)
        )

        val visitTwoStartAndTwoEnd = createVisit(
            createVisitId(event1start._id),
            portAreas = listOf(
                createPortAreaVisit(event1start, event1end),
                createPortAreaVisit(event3start, event3end)
            ),
            dest = dest,
            eta = ETA_BEGNE,
            finished = true,
            nextEntryId = createVoyageId(event3end._id)
        )

        val (status, changes) = portProcessor.processEvent(
            VisitShipStatus(visitTwoStartAndOneEnd, null, null),
            event3end
        )

        assertEquals(VoyageShipStatus(voyageStart, visitTwoStartAndTwoEnd, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, visitTwoStartAndTwoEnd),
                Change(Action.CREATE, voyageStart)
            ),
            changes
        )
    }

    @Test
    fun `should update previous voyage when finishing visit removes a port area`() {
        val event0end = createPortEventEnd(PORT_BEANR, "2021-03-05T00:00:00Z")
        val event1start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")
        val event3start = createPortEventStart(PORT_NLVLA, "2021-03-09T00:00:00Z")
        val event3endPassThrough = createPortEventEnd(PORT_NLVLA, "2021-03-09T00:15:00Z") // 15 minutes after the start
        val dest1 = createDestination(BEGNE, event1start.actualTime.atZone(ZoneOffset.UTC))
        val dest0 = createDestination(BEGNE, event0end.actualTime.atZone(ZoneOffset.UTC))

        val portAreaVisit1 = createPortAreaVisit(event1start, event1end)
        val portAreaVisit3 = createPortAreaVisit(event3start)
        val portAreaVisit3Finished = createPortAreaVisit(event3start, event3endPassThrough)

        val visit1TwoStartAndOneEnd = createVisit(
            _id = createVisitId(event1start._id),
            portAreas = listOf(
                portAreaVisit1,
                portAreaVisit3,
            ),
            dest = dest1,
            eta = ETA_BEGNE
        )

        val visit1End = createVisit(
            _id = createVisitId(event1start._id),
            portAreas = listOf(createPortAreaVisit(event1start, event1end)),
            dest = dest1,
            eta = ETA_BEGNE,
            passThroughAreas = listOf(portAreaVisit3Finished),
            finished = true,
            previousEntryId = createVoyageId(event1start._id),
            nextEntryId = createVoyageId(event1end._id)
        )

        val voyage0WithTwoEndTwoPorts = createVoyage(
            _id = createVoyageId(event1start._id),
            startPortIds = listOf(event0end.area.unlocode ?: ""),
            startTime = event0end.actualTime.atZone(ZoneOffset.UTC),
            dest = dest0,
            eta = ETA_BEGNE,
            passThroughAreas = null,
            endPortIds = listOf(event1start.area.unlocode ?: "", event3start.area.unlocode ?: ""),
            endTime = event1start.actualTime.atZone(ZoneOffset.UTC),
            finished = true,
            nextEntryId = visit1TwoStartAndOneEnd._id
        )

        val voyage0WithOneEndPort = voyage0WithTwoEndTwoPorts.copy(
            endPortIds = listOf(event1start.area.unlocode ?: ""),
            nextEntryId = visit1End._id
        )

        val voyage1start = createVoyage(
            _id = createVoyageId(event1end._id),
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC),
            dest = dest1,
            eta = ETA_BEGNE,
            previousEntryId = createVisitId(event1start._id)
        )

        val (status, changes) = portProcessor.processEvent(
            VisitShipStatus(
                visit = visit1TwoStartAndOneEnd.copy(previousEntryId = voyage0WithTwoEndTwoPorts._id), // TODO: .copy() here is ugly
                previousVoyage = voyage0WithTwoEndTwoPorts,
                previousVisit = null
            ),
            event3endPassThrough
        )

        assertEquals(VoyageShipStatus(voyage1start, visit1End, voyage0WithOneEndPort), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, voyage0WithOneEndPort),
                Change(Action.UPDATE, visit1End),
                Change(Action.CREATE, voyage1start)
            ),
            changes
        )
    }

    @Test
    fun `update visit having just an anchor start area`() {
        val event1start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")
        val event4anchorStart = createAnchorEventStart(ANCHOR_AREA_1, "2021-03-07T11:00:00Z")

        val visit4anchorStart = createVisit(
            _id = createVisitId(event1start._id),
            anchorAreas = listOf(createAnchorAreaVisit(ANCHOR_AREA_1, event4anchorStart.actualTime.atZone(ZoneOffset.UTC))),
            dest = createDestination(BEGNE, event1start.actualTime.atZone(ZoneOffset.UTC)),
            eta = ETA_BEGNE
        )

        val visit4update = visit4anchorStart.copy(
            portAreas = listOf(createPortAreaVisit(event1start))
        )

        val (status, changes) = portProcessor.processEvent(
            VisitShipStatus(visit4anchorStart, null, null),
            event1start
        )

        assertEquals(VisitShipStatus(visit4update, null, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, visit4update)
            ),
            changes
        )
    }

    @Test
    fun `update visit having an anchor start and end`() {
        val event1start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")
        val event4startAnchor = createAnchorEventStart(ANCHOR_AREA_1, "2021-03-07T11:00:00Z")
        val event4endAnchor = createAnchorEventEnd(ANCHOR_AREA_1, "2021-03-07T16:00:00Z")

        val visit4withAnchorage = createVisit(
            _id = createVisitId(event1start._id),
            anchorAreas = listOf(
                createAnchorAreaVisit(
                    ANCHOR_AREA_1,
                    event4startAnchor.actualTime.atZone(ZoneOffset.UTC),
                    event4endAnchor.actualTime.atZone(ZoneOffset.UTC)
                )
            ),
            dest = createDestination(BEGNE, event1start.actualTime.atZone(ZoneOffset.UTC)),
            eta = ETA_BEGNE
        )

        val visit4update = visit4withAnchorage.copy(
            portAreas = listOf(createPortAreaVisit(event1start))
        )

        val (status, changes) = portProcessor.processEvent(
            VisitShipStatus(visit4withAnchorage, null, null),
            event1start
        )

        assertEquals(VisitShipStatus(visit4update, null, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, visit4update)
            ),
            changes
        )
    }

    @Test
    fun `update visit and previous voyage having an anchor start and end`() {
        val event0end = createPortEventEnd(PORT_BEANR, "2021-03-05T00:00:00Z")
        val event1start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")
        val anchorEvent4Start = createAnchorEventStart(ANCHOR_AREA_1, "2021-03-07T11:00:00Z")
        val anchorEvent4End = createAnchorEventStart(ANCHOR_AREA_1, "2021-03-07T16:00:00Z")

        val visitWithAnchorage = createVisit(
            _id = createVisitId(event1start._id),
            anchorAreas = listOf(
                createAnchorAreaVisit(ANCHOR_AREA_1, anchorEvent4Start.actualTime.atZone(ZoneOffset.UTC), anchorEvent4End.actualTime.atZone(ZoneOffset.UTC))
            ),
            dest = createDestination(BEGNE, event1start.actualTime.atZone(ZoneOffset.UTC)),
            eta = ETA_BEGNE
        )

        val voyageWithoutEndPort = createVoyage(
            _id = createVoyageId(event1start._id),
            startPortIds = listOf(event0end.area.unlocode ?: ""),
            startTime = event0end.actualTime.atZone(ZoneOffset.UTC),
            dest = createDestination(BEGNE, event0end.actualTime.atZone(ZoneOffset.UTC)),
            eta = ETA_BEGNE,
            endPortIds = null,
            endTime = anchorEvent4Start.actualTime.atZone(ZoneOffset.UTC),
            finished = true
        )

        val visitUpdated = visitWithAnchorage.copy(
            portAreas = listOf(
                createPortAreaVisit(event1start)
            )
        )

        val voyageWithEndPort = voyageWithoutEndPort.copy(
            endPortIds = listOf(event1start.area.unlocode ?: "")
        )

        val (status, changes) = portProcessor.processEvent(
            VisitShipStatus(visitWithAnchorage, voyageWithoutEndPort, null),
            event1start
        )

        assertEquals(VisitShipStatus(visitUpdated, voyageWithEndPort, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, voyageWithEndPort),
                Change(Action.UPDATE, visitUpdated)
            ),
            changes
        )
    }

    @Test
    fun `move non-matching anchor areas to the previous voyage when finishing a visit`() {
        // TODO: make this unit test less verbose by creating/using util functions
        val event7start = createPortEventEnd(PORT_BEANR, TIME_5)
        val event7end = createPortEventEnd(PORT_BEANR, TIME_6)

        val anchorAreaVisit1 = AnchorAreaVisit(
            anchorAreaId = ANCHOR_AREA_4EAST,
            startEventId = "ANCHOR_1_START",
            startTime = TIME_1,
            destinations = setOf(),
            endEventId = "ANCHOR_1_END",
            endTime = TIME_2
        )

        val anchorAreaVisit2 = AnchorAreaVisit(
            anchorAreaId = ANCHOR_AREA_SCHOUWENBANK,
            startEventId = "ANCHOR_2_START",
            startTime = TIME_3,
            destinations = setOf(),
            endEventId = "ANCHOR_2_END",
            endTime = TIME_4
        )

        val visit7 = createVisit(
            anchorAreas = listOf(
                anchorAreaVisit1,
                anchorAreaVisit2
            ),
            portAreas = listOf(
                createPortAreaVisit(event7start)
            ),
        )

        val voyage6 = createVoyage(
            _id = createVoyageId("EVENT_8_END"),
            startPortIds = listOf(NLDOR),
            startTime = TIME_1,
            endPortIds = listOf(BEANR),
            endTime = TIME_4,
            finished = true,
            previousEntryId = "foo"
        )

        val voyage6updated = voyage6.copy(
            endTime = TIME_3,
            nonMatchingAnchorAreas = listOf(
                anchorAreaVisit1
            )
        )

        val visit7finished = createVisit(
            _id = visit7._id,
            anchorAreas = listOf(
                anchorAreaVisit2
            ),
            portAreas = listOf(
                createPortAreaVisit(event7start, event7end)
            ),
            finished = true,
            nextEntryId = createVoyageId(event7end._id)
        )

        val voyage7start = createVoyage(
            _id = createVoyageId(event7end._id),
            startPortIds = listOf(BEANR),
            startTime = TIME_6,
            previousEntryId = visit7finished._id
        )

        val (status, changes) = portProcessor.processEvent(
            VisitShipStatus(visit7, voyage6, null),
            event7end
        )

        assertEquals(VoyageShipStatus(voyage7start, visit7finished, voyage6updated), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, voyage6updated),
                Change(Action.UPDATE, visit7finished),
                Change(Action.CREATE, voyage7start)
            ),
            changes
        )
    }

    @Test
    fun `update visit one start events a pass-through end event, without previous voyage`() {
        val eventStart = createPortEventStart(PORT_NLVLA, "2021-03-09T00:00:00Z")
        val eventEndPassThrough = createPortEventEnd(PORT_NLVLA, "2021-03-09T00:15:00Z") // 15 minutes after the start

        val visitStart = createVisit(
            _id = createVisitId(eventStart._id),
            portAreas = listOf(createPortAreaVisit(eventStart)),
            dest = createDestination(BEGNE, eventStart.actualTime.atZone(ZoneOffset.UTC)),
            eta = ETA_BEGNE
        )

        val (status, changes) = portProcessor.processEvent(
            VisitShipStatus(visitStart, null, null),
            eventEndPassThrough
        )

        assertEquals(InitialShipStatus, status)
        assertEquals(
            listOf(
                Change(Action.DELETE, visitStart)
            ),
            changes
        )
    }

    @Test
    fun `update visit one start events a pass-through end event, with previous voyage`() {
        val event1start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")
        val event2start = createPortEventStart(PORT_DEHAM, "2021-03-09T00:00:00Z")
        val event2endPassThrough = createPortEventEnd(PORT_DEHAM, "2021-03-09T00:15:00Z") // 15 minutes after the start
        val dest1 = createDestination(BEGNE, event1end.actualTime.atZone(ZoneOffset.UTC))
        val dest2 = createDestination(BEGNE, event2start.actualTime.atZone(ZoneOffset.UTC))

        val portAreaVisit2 = createPortAreaVisit(event2start)
        val portAreaVisit2Finished = createPortAreaVisit(event2start, event2endPassThrough)

        val voyage = createVoyage(
            _id = createVoyageId(event1end._id),
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC),
            dest = dest1,
            eta = ETA_BEGNE,
            endPortIds = listOf(event2start.area.unlocode ?: ""),
            endTime = event2start.actualTime.atZone(ZoneOffset.UTC),
            finished = true,
            previousEntryId = createVisitId(event1start._id),
            nextEntryId = createVisitId(event2start._id)
        )

        val voyageUpdated = createVoyage(
            _id = createVoyageId(event1end._id),
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC),
            dest = dest2,
            eta = ETA_BEGNE,
            passThroughAreas = listOf(portAreaVisit2Finished),
            previousEntryId = createVisitId(event1start._id)
        )

        val visit = createVisit(
            _id = createVisitId(event2start._id),
            portAreas = listOf(portAreaVisit2),
            dest = dest2,
            eta = ETA_BEGNE
        )

        val visitDeleted = visit.copy(
            portAreas = listOf(portAreaVisit2Finished),
        )

        val (status, changes) = portProcessor.processEvent(
            VisitShipStatus(visit, voyage, null),
            event2endPassThrough
        )

        assertEquals(VoyageShipStatus(voyageUpdated, null, null), status)
        assertEquals(
            listOf(
                Change(Action.DELETE, visitDeleted),
                Change(Action.UPDATE, voyageUpdated)
            ),
            changes
        )
    }

    @Test
    fun `move anchor areas to previous voyage when deleting a pass-through visit`() {
        val event1start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")
        val event2start = createPortEventStart(PORT_DEHAM, "2021-03-12T00:00:00Z")
        val event2endPassThrough = createPortEventEnd(PORT_DEHAM, "2021-03-12T00:15:00Z") // 15 minutes after the start
        val dest1 = createDestination(BEGNE, event1end.actualTime.atZone(ZoneOffset.UTC))
        val dest2 = createDestination(BEGNE, event2start.actualTime.atZone(ZoneOffset.UTC))

        val voyage = createVoyage(
            _id = createVoyageId(event1end._id),
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC),
            dest = dest1,
            eta = ETA_BEGNE,
            endPortIds = listOf(event2start.area.unlocode ?: ""),
            endTime = event2start.actualTime.atZone(ZoneOffset.UTC),
            finished = true,
            previousEntryId = createVisitId(event1start._id),
            nextEntryId = createVisitId(event2start._id)
        )

        val anchorAreaVisit = createAnchorAreaVisit(
            anchorArea = ANCHOR_AREA_1,
            startTime = ZonedDateTime.parse("2021-03-11T00:00:00Z"),
            endTime = ZonedDateTime.parse("2021-03-11T08:00:00Z")
        )

        val portAreaVisit = createPortAreaVisit(event2start)
        val portAreaVisitFinished = createPortAreaVisit(event2start, event2endPassThrough)

        val visit = createVisit(
            _id = createVisitId(event2start._id),
            anchorAreas = listOf(anchorAreaVisit),
            portAreas = listOf(portAreaVisit),
            dest = dest2,
            eta = ETA_BEGNE
        )

        val visitDeleted = visit.copy(
            portAreas = listOf(portAreaVisitFinished),
        )

        val voyageUpdated = createVoyage(
            _id = createVoyageId(event1end._id),
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC),
            dest = dest2,
            eta = ETA_BEGNE,
            nonMatchingAnchorAreas = listOf(anchorAreaVisit),
            passThroughAreas = listOf(portAreaVisitFinished),
            previousEntryId = createVisitId(event1start._id)
        )

        val (status, changes) = portProcessor.processEvent(
            VisitShipStatus(visit, voyage, null),
            event2endPassThrough
        )

        assertEquals(VoyageShipStatus(voyageUpdated, null, null), status)
        assertEquals(
            listOf(
                Change(Action.DELETE, visitDeleted),
                Change(Action.UPDATE, voyageUpdated)
            ),
            changes
        )
    }

    @Test
    fun `update visit with end event`() {
        val eventStart = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")
        val eventEnd = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")
        val dest = createDestination(BEGNE, eventStart.actualTime.atZone(ZoneOffset.UTC))

        val visitStart = createVisit(
            _id = createVisitId(eventStart._id),
            portAreas = listOf(createPortAreaVisit(eventStart)),
            dest = dest,
            eta = ETA_BEGNE
        )

        val voyageStart = createVoyage(
            _id = createVoyageId(eventEnd._id),
            startPortIds = listOf(eventEnd.area.unlocode ?: ""),
            startTime = eventEnd.actualTime.atZone(ZoneOffset.UTC),
            dest = dest,
            eta = ETA_BEGNE,
            previousEntryId = createVisitId(eventStart._id)
        )

        val visitEnd = createVisit(
            createVisitId(eventStart._id),
            portAreas = listOf(createPortAreaVisit(eventStart, eventEnd)),
            dest = dest,
            eta = ETA_BEGNE,
            finished = true,
            nextEntryId = createVoyageId(eventEnd._id)
        )

        val (status, changes) = portProcessor.processEvent(
            VisitShipStatus(visitStart, null, null),
            eventEnd
        )

        assertEquals(VoyageShipStatus(voyageStart, visitEnd, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, visitEnd),
                Change(Action.CREATE, voyageStart)
            ),
            changes
        )
    }

    @Test
    fun `update visit with end event - should copy destination and NOT copy outdated eta`() {
        val eventStart = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")
        val eventEnd = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")

        val beforeVisitStart = eventStart.actualTime.atZone(ZoneOffset.UTC).minusDays(1)
        val outdatedDest = createDestination(BEGNE, beforeVisitStart)
        val etaOutdated = ETA_BEGNE?.copy(
            predictedAt = beforeVisitStart,
        )

        val visitStart = createVisit(
            _id = createVisitId(eventStart._id),
            portAreas = listOf(createPortAreaVisit(eventStart)),
            dest = outdatedDest,
            eta = etaOutdated
        )

        val voyageStart = createVoyage(
            _id = createVoyageId(eventEnd._id),
            startPortIds = listOf(eventEnd.area.unlocode ?: ""),
            startTime = eventEnd.actualTime.atZone(ZoneOffset.UTC),
            dest = outdatedDest,
            eta = null,
            previousEntryId = createVisitId(eventStart._id)
        )

        val visitEnd = visitStart.copy(
            portAreas = listOf(createPortAreaVisit(eventStart, eventEnd)),
            finished = true,
            nextEntryId = createVoyageId(eventEnd._id)
        )

        val (status, changes) = portProcessor.processEvent(
            VisitShipStatus(visitStart, null, null),
            eventEnd
        )

        assertEquals(VoyageShipStatus(voyageStart, visitEnd, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, visitEnd),
                Change(Action.CREATE, voyageStart)
            ),
            changes
        )
    }

    @Test
    fun `update visit with end event - should take the correct endTime`() {
        val event1start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")
        val dest = createDestination(BEGNE, event1start.actualTime.atZone(ZoneOffset.UTC))

        val anchorAreaVisit = createAnchorAreaVisit(
            ANCHOR_AREA_1,
            event1start.actualTime.atZone(ZoneOffset.UTC).minusDays(1),
            event1end.actualTime.atZone(ZoneOffset.UTC).plusDays(1)
        )

        // *after* the end time of the port end event (testing this edge case)
        val visit1start = createVisit(
            createVisitId(event1start._id),
            anchorAreas = listOf(anchorAreaVisit),
            portAreas = listOf(createPortAreaVisit(event1start)),
            dest = dest,
            eta = ETA_BEGNE
        )

        val voyage1start = createVoyage(
            _id = createVoyageId(anchorAreaVisit.endEventId!!),
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = anchorAreaVisit.endTime!!,
            dest = dest,
            eta = ETA_BEGNE,
            previousEntryId = createVisitId(event1start._id)
        )

        val visit1end = visit1start.copy(
            portAreas = listOf(createPortAreaVisit(event1start, event1end)),
            finished = true,
            nextEntryId = createVoyageId(anchorAreaVisit.endEventId!!),
        )

        val (status, changes) = portProcessor.processEvent(
            VisitShipStatus(visit1start, null, null),
            event1end
        )

        assertEquals(VoyageShipStatus(voyage1start, visit1end, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, visit1end),
                Change(Action.CREATE, voyage1start)
            ),
            changes
        )
    }

    @Test
    fun `update visit with start event, missing previous end event`() {
        val event1start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")
        val event2start = createPortEventStart(PORT_DEHAM, "2021-04-02T00:00:00Z")

        val visit1start = createVisit(
            _id = createVisitId(event1start._id),
            portAreas = listOf(createPortAreaVisit(event1start)),
            dest = null,
            eta = ETA_BEGNE
        )

        val visit1missingEnd = createVisit(
            _id = createVisitId(event1start._id),
            portAreas = listOf(createPortAreaVisit(event1start)),
            dest = null,
            eta = ETA_BEGNE,
            finished = true,
            nextEntryId = createVoyageId(event1start._id)
        )

        val voyage1missingEnd = createVoyage(
            _id = createVoyageId(event1start._id),
            startPortIds = listOf(event1start.area.unlocode ?: ""),
            startTime = event1start.actualTime.atZone(ZoneOffset.UTC),
            dest = null,
            eta = ETA_BEGNE,
            endPortIds = listOf(event2start.area.unlocode ?: ""),
            endTime = event2start.actualTime.atZone(ZoneOffset.UTC),
            finished = true,
            previousEntryId = createVisitId(event1start._id),
            nextEntryId = createVisitId(event2start._id)
        )

        val visit2start = createVisit(
            _id = createVisitId(event2start._id),
            portAreas = listOf(createPortAreaVisit(event2start)),
            previousEntryId = voyage1missingEnd._id,
            eta = ETA_BEGNE,
        )

        val (status, changes) = portProcessor.processEvent(
            VisitShipStatus(visit1start, null, null),
            event2start
        )

        assertEquals(VisitShipStatus(visit2start, voyage1missingEnd, visit1missingEnd), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, visit1missingEnd),
                Change(Action.CREATE, voyage1missingEnd),
                Change(Action.CREATE, visit2start)
            ),
            changes
        )

        // both visit 1 and voyage 1 are based on the same event, but they must not get the same identifier
        val visit1id = changes[0].entry._id
        val voyage1id = changes[1].entry._id
        assertNotEquals(visit1id, voyage1id)
    }

    @Test
    fun `update visit with invalid end event, should ignore event`() {
        val event1start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")
        val event2end = createPortEventEnd(PORT_NLVLA, "2021-03-11T00:00:00Z")

        val visit = createVisit(
            _id = createVisitId(event1start._id),
            portAreas = listOf(createPortAreaVisit(event1start)),
            dest = createDestination(BEGNE, event1start.actualTime.atZone(ZoneOffset.UTC)),
            eta = ETA_BEGNE
        )

        val statusBefore = VisitShipStatus(visit, null, null)
        val (statusAfter, changes) = portProcessor.processEvent(statusBefore, event2end)

        assertEquals(statusBefore, statusAfter)
        assertEquals(listOf<Change>(), changes)
    }

    @Test
    fun `update voyage with new start event`() {
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")
        val event2start = createPortEventStart(PORT_DEHAM, "2021-04-02T00:00:00Z")
        val dest = createDestination(BEGNE, event1end.actualTime.atZone(ZoneOffset.UTC))

        val voyage1start = createVoyage(
            _id = createVoyageId(event1end._id),
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC),
            dest = dest,
            eta = ETA_BEGNE,
            previousEntryId = "foo"
        )

        val visit2start = createVisit(
            _id = createVisitId(event2start._id),
            portAreas = listOf(createPortAreaVisit(event2start)),
            previousEntryId = createVoyageId(event1end._id),
            dest = dest,
            eta = ETA_BEGNE,
        )

        val voyage1end = voyage1start.copy(
            endPortIds = listOf(event2start.area.unlocode ?: ""),
            endTime = event2start.actualTime.atZone(ZoneOffset.UTC),
            finished = true,
            nextEntryId = createVisitId(event2start._id)
        )

        val (status, changes) = portProcessor.processEvent(
            VoyageShipStatus(voyage1start, null, null),
            event2start
        )

        assertEquals(VisitShipStatus(visit2start, voyage1end, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, voyage1end),
                Change(Action.CREATE, visit2start)
            ),
            changes
        )
    }

    @Test
    fun `update voyage with new start event - should copy destination and NOT copy outdated eta`() {
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")
        val event2start = createPortEventStart(PORT_DEHAM, "2021-04-02T00:00:00Z")

        val beforeVisitStart = event1end.actualTime.atZone(ZoneOffset.UTC).minusDays(1)
        val outdatedDest = createDestination(BEGNE, beforeVisitStart)
        val etaOutdated = ETA_BEGNE?.copy(
            predictedAt = beforeVisitStart,
        )

        val voyage1start = createVoyage(
            _id = createVoyageId(event1end._id),
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC),
            dest = outdatedDest,
            eta = etaOutdated,
            previousEntryId = "foo"
        )

        val visit2start = createVisit(
            _id = createVisitId(event2start._id),
            portAreas = listOf(createPortAreaVisit(event2start)),
            previousEntryId = createVoyageId(event1end._id),
            dest = outdatedDest,
            eta = null,
        )

        val voyage1end = voyage1start.copy(
            endPortIds = listOf(event2start.area.unlocode ?: ""),
            endTime = event2start.actualTime.atZone(ZoneOffset.UTC),
            finished = true,
            nextEntryId = createVisitId(event2start._id)
        )

        val (status, changes) = portProcessor.processEvent(
            VoyageShipStatus(voyage1start, null, null),
            event2start
        )

        assertEquals(VisitShipStatus(visit2start, voyage1end, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, voyage1end),
                Change(Action.CREATE, visit2start)
            ),
            changes
        )
    }

    @Test
    fun `initialize voyage with an end event`() {
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")

        val voyage1start = createVoyage(
            _id = createVoyageId(event1end._id),
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC)
        )

        val (status, changes) = portProcessor.processEvent(InitialShipStatus, event1end)

        assertEquals(VoyageShipStatus(voyage1start, null, null), status)
        assertEquals(
            listOf(
                Change(Action.CREATE, voyage1start)
            ),
            changes
        )
    }

    @Test
    fun `Start a voyage with the right start port and time`() {
        val event7start = createPortEventStart(PORT_NLRTM, TIME_2)
        val event8start = createPortEventStart(PORT_NLDOR, TIME_3)
        val event8end = createPortEventStart(PORT_NLDOR, TIME_4)

        // Make sure the port area event is being marked as pass-through
        val event7end = createPortEventEnd(PORT_NLRTM, TIME_2.plusMinutes(15))

        val visit7 = createVisit(
            portAreas = listOf(
                createPortAreaVisit(event7start),
                createPortAreaVisit(event8start, event8end)
            )
        )

        val voyage6start = createVoyage(
            _id = createVoyageId(event8end._id),
            startPortIds = listOf(NLDOR),
            startTime = TIME_4, // <- time of NLDOR port area entry, NLRTM area is removed
            previousEntryId = visit7._id
        )

        val visit6finished = visit7.copy(
            portAreas = listOf(visit7.portAreas[1]),
            passThroughAreas = listOf(createPortAreaVisit(event7start, event7end)),
            finished = true,
            nextEntryId = createVoyageId(event8end._id)
        )

        val (status, changes) = portProcessor.processEvent(
            VisitShipStatus(visit7, null, null), event7end
        )

        assertEquals(VoyageShipStatus(voyage6start, visit6finished, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, visit6finished),
                Change(Action.CREATE, voyage6start)
            ),
            changes
        )
    }

    @Test
    fun `update voyage with a start event`() {
        val event1start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")
        val event2start = createPortEventStart(PORT_DEHAM, "2021-04-02T00:00:00Z")

        val voyage1start = createVoyage(
            _id = createVoyageId(event1end._id),
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC),
            dest = null,
            eta = ETA_BEGNE,
            previousEntryId = createVisitId(event1start._id)
        )

        val visit2start = createVisit(
            _id = createVisitId(event2start._id),
            portAreas = listOf(createPortAreaVisit(event2start)),
            previousEntryId = createVoyageId(event1end._id),
            eta = ETA_BEGNE,
        )

        val voyage1end = voyage1start.copy(
            endPortIds = listOf(event2start.area.unlocode ?: ""),
            endTime = event2start.actualTime.atZone(ZoneOffset.UTC),
            finished = true,
            nextEntryId = createVisitId(event2start._id)
        )

        val (status, changes) = portProcessor.processEvent(
            VoyageShipStatus(voyage1start, null, null), event2start
        )

        assertEquals(VisitShipStatus(visit2start, voyage1end, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, voyage1end),
                Change(Action.CREATE, visit2start)
            ),
            changes
        )
    }

    @Test
    fun `update voyage with an end event (should ignore event)`() {
        val event1start = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")
        val event3end = createPortEventEnd(PORT_NLVLA, "2021-03-11T00:00:00Z")

        val voyage1start = createVoyage(
            _id = createVoyageId(event1start._id),
            startPortIds = listOf(event1start.area.unlocode ?: ""),
            startTime = event1start.actualTime.atZone(ZoneOffset.UTC)
        )

        val statusBefore = VoyageShipStatus(voyage1start, null, null)
        val (statusAfter, changes) = portProcessor.processEvent(statusBefore, event3end)

        assertEquals(statusBefore, statusAfter)
        assertEquals(listOf<Change>(), changes)
    }

    @Test
    fun `should ignore events before the current latest event`() {
        val event1start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")
        val event2start = createPortEventStart(PORT_DEHAM, "2021-04-02T00:00:00Z")

        val visit2start = createVisit(
            _id = createVisitId(event2start._id),
            portAreas = listOf(createPortAreaVisit(event2start)),
            previousEntryId = createVoyageId(event1end._id),
        )

        val (status, changes) = portProcessor.processEvent(
            VisitShipStatus(visit2start, null, null), event1start
        )

        assertEquals(VisitShipStatus(visit2start, null, null), status)
        assertEquals(listOf<Change>(), changes)
    }

    @Test
    fun `should allow events with equal start and end time`() {
        val event1start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")

        val visit1start = createVisit(
            _id = createVisitId(event1start._id),
            portAreas = listOf(createPortAreaVisit(event1start)),
            dest = createDestination(BEGNE, event1start.actualTime.atZone(ZoneOffset.UTC)),
            eta = ETA_BEGNE
        )

        val endEventSameTime = (event1end as AreaEndEvent).copy(
            actualTime = visit1start.startTime.toInstant()
        )

        val (status, changes) = portProcessor.processEvent(
            VisitShipStatus(visit1start, null, null), endEventSameTime
        )

        // since start and end are equal, the visit is too short and should be filtered away
        assertEquals(InitialShipStatus, status)
        assertEquals(listOf(Change(Action.DELETE, visit1start)), changes)
    }

    @Test
    fun `should ignore a second start event for the same port`() {
        val event1start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")

        val event1secondStart = (event1start as AreaStartEvent).copy(
            _id = generateUniqueId(),
            actualTime = event1start.actualTime
        )

        val visit1start = createVisit(
            _id = createVisitId(event1start._id),
            portAreas = listOf(createPortAreaVisit(event1start)),
            dest = createDestination(BEGNE, event1start.actualTime.atZone(ZoneOffset.UTC)),
            eta = ETA_BEGNE
        )

        val (status, changes) = portProcessor.processEvent(
            VisitShipStatus(visit1start, null, null),
            event1secondStart
        )

        // since start and end are equal, the visit is too short and should be filtered away
        assertEquals(VisitShipStatus(visit1start, null, null), status)
        assertEquals(listOf<Change>(), changes)
    }

    @Test
    fun `should ignore event when the IMO does not match`() {
        val event1start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")

        val visit1start = createVisit(
            _id = createVisitId(event1start._id),
            portAreas = listOf(createPortAreaVisit(event1start)),
            dest = createDestination(BEGNE, event1start.actualTime.atZone(ZoneOffset.UTC)),
            eta = ETA_BEGNE
        )

        val eventWithDifferingImo = (event1start as AreaStartEvent).copy(ship = event1start.ship.copy(imo = 222))

        val (status, changes) = portProcessor.processEvent(
            VisitShipStatus(visit1start, null, null),
            eventWithDifferingImo
        )

        assertEquals(VisitShipStatus(visit1start, null, null), status)
        assertEquals(listOf<Change>(), changes)
    }

    @Test
    fun `should determine whether an event can be part of a visit`() {
        fun createPortEventStart(port: TestPort) = createPortEventStart(port, "2021-03-08T00:00:00Z")

        fun findPortByIdWithoutOuterArea(portId: String): nl.teqplay.vesselvoyage.model.lightweight.poma.Port? {
            return findPortById(portId)
                ?.copy(outerArea = emptyList())
        }

        val visit = createVisit(
            portAreas = listOf(
                createPortAreaVisit(createPortEventStart(PORT_NLRTM))
            ),
        )

        // with outer area
        assertTrue(eventIsPartOfVisit(visit, ::findPortById, PORT_NLRTM.portId))
        assertTrue(eventIsPartOfVisit(visit, ::findPortById, PORT_NLVLA.portId))
        assertFalse(eventIsPartOfVisit(visit, ::findPortById, PORT_BEANR.portId))
        assertFalse(eventIsPartOfVisit(visit, ::findPortById, PORT_DEHAM.portId))

        // without outer area
        // Antwerp is now within the maximum distance of 100 km
        assertTrue(eventIsPartOfVisit(visit, ::findPortByIdWithoutOuterArea, PORT_NLRTM.portId))
        assertTrue(eventIsPartOfVisit(visit, ::findPortByIdWithoutOuterArea, PORT_NLVLA.portId))
        assertTrue(eventIsPartOfVisit(visit, ::findPortByIdWithoutOuterArea, PORT_BEANR.portId))
        assertFalse(eventIsPartOfVisit(visit, ::findPortByIdWithoutOuterArea, PORT_DEHAM.portId))
    }

    @Test
    fun `should protect against storing too many portAreas in a single visit`() {
        val eventStart1 = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")
        val eventStart2 = createPortEventStart(PORT_NLVLA, "2021-03-09T00:00:00Z")
        val dest = createDestination(BEGNE, eventStart1.actualTime.atZone(ZoneOffset.UTC))

        val visitStart = createVisit(
            _id = createVisitId(eventStart1._id),
            portAreas = List(MAX_PORT_AREAS) { createPortAreaVisit(eventStart1) },
            dest = dest,
            eta = ETA_BEGNE
        )

        val (status, changes, issues) = portProcessor.processEvent(
            VisitShipStatus(visitStart, null, null), eventStart2
        )

        assertEquals(VisitShipStatus(visitStart, null, null), status)
        assertEquals(listOf<Change>(), changes)
        assertEquals(
            listOf(
                EventProcessingIssue(
                    eventId = eventStart2._id,
                    description = "Cannot process PortEvent: too many port areas in current visit. " +
                        "Will ignore the event (imo: 1111111, portAreas: 100)"
                )
            ),
            issues
        )
    }

    @Test
    fun `should copy relevant esof items from voyage to visit`() {
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")
        val event2start = createPortEventStart(PORT_DEHAM, "2021-04-02T00:00:00Z")

        // not relevant: too old
        val encounter1 = createEncounter("2021-03-01T00:00:00Z", null, location = PORT_DEHAM.location)

        // not relevant: too far away
        val encounter2 = createEncounter("2021-04-01T12:00:00Z", null, location = PORT_NLRTM.location)

        // still relevant
        val encounter3 = createEncounter("2021-04-01T12:00:00Z", null, location = PORT_DEHAM.location)

        val voyage1start = createVoyage(
            _id = createVoyageId(event1end._id),
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC),
            esof = ESof(
                encounters = listOf(encounter1, encounter2, encounter3),
                stops = listOf(),
                slowMovingPeriods = null
            )
        )

        val visitESof = ESof(
            encounters = listOf(encounter3),
            stops = listOf(),
            slowMovingPeriods = null
        )

        val visit2start = createVisit(
            _id = createVisitId(event2start._id),
            portAreas = listOf(createPortAreaVisit(event2start)),
            previousEntryId = createVoyageId(event1end._id),
            esof = visitESof
        )

        val voyage1end = voyage1start.copy(
            endPortIds = listOf(event2start.area.unlocode ?: ""),
            endTime = event2start.actualTime.atZone(ZoneOffset.UTC),
            finished = true,
            esof = ESof(
                encounters = listOf(encounter1, encounter2),
                stops = listOf(),
                slowMovingPeriods = listOf()
            ),
            nextEntryId = createVisitId(event2start._id)
        )

        val (status, changes) = portProcessor.processEvent(
            VoyageShipStatus(voyage1start, null, null),
            event2start
        )

        assertEquals(VisitShipStatus(visit2start, voyage1end, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, voyage1end),
                Change(Action.CREATE, visit2start)
            ),
            changes
        )
    }

    @Test
    fun `should copy relevant esof items from visit to voyage`() {
        val eventStart = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")
        val eventEnd = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")

        // we just two simple encounters: one relevant and one not, to check whether the filtering function is triggered
        val encounter1 = createEncounter("2021-03-01T00:00:00Z", null) // belongs to the visit
        val encounter2 = createEncounter("2021-03-12T12:00:00Z", null) // not during or near the visit -> move to voyage

        val visitStart = createVisit(
            _id = createVisitId(eventStart._id),
            portAreas = listOf(createPortAreaVisit(eventStart)),
            esof = ESof(
                encounters = listOf(encounter1, encounter2),
                stops = listOf(),
                slowMovingPeriods = null
            )
        )

        val voyageStart = createVoyage(
            _id = createVoyageId(eventEnd._id),
            startPortIds = listOf(eventEnd.area.unlocode ?: ""),
            startTime = eventEnd.actualTime.atZone(ZoneOffset.UTC),
            previousEntryId = createVisitId(eventStart._id),
            esof = ESof(
                encounters = listOf(encounter2),
                stops = listOf(),
                slowMovingPeriods = null
            )
        )

        val visitEnd = createVisit(
            createVisitId(eventStart._id),
            portAreas = listOf(createPortAreaVisit(eventStart, eventEnd)),
            finished = true,
            nextEntryId = createVoyageId(eventEnd._id),
            esof = ESof(
                encounters = listOf(encounter1),
                stops = listOf(),
                slowMovingPeriods = null
            )
        )

        val (status, changes) = portProcessor.processEvent(
            VisitShipStatus(visitStart, null, null),
            eventEnd
        )

        assertEquals(VoyageShipStatus(voyageStart, visitEnd, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, visitEnd),
                Change(Action.CREATE, voyageStart)
            ),
            changes
        )
    }

    @Test
    fun `should merge relevant esof encounters when un-finishing a voyage after a pass-through visit`() {
        val event1start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")
        val event2start = createPortEventStart(PORT_DEHAM, "2021-03-11T00:00:00Z")
        val event2endPassThrough = createPortEventEnd(PORT_DEHAM, "2021-03-11T00:05:00Z") // 5 minutes after the start

        val (status1) = portProcessor.processEvent(
            InitialShipStatus,
            event1start
        )
        val (status2) = portProcessor.processEvent(
            status1,
            event1end
        )
        val (status3) = portProcessor.processEvent(
            status2,
            event2start
        )

        assert(status1 is VisitShipStatus)
        assert(status2 is VoyageShipStatus)
        assert(status3 is VisitShipStatus)

        val encounter1 = createEncounter("2021-03-01T00:00:00Z", null)
        val encounter2 = createEncounter("2021-03-10T00:00:00Z", null)
        val encounter2updated = encounter2.copy(
            endTime = ZonedDateTime.parse("2021-03-11T00:02:00Z"),
            endLocation = Location(0.0, 0.0),
            endEventId = "1"
        )
        val encounter3 = createEncounter("2021-03-11T00:04:00Z", null)

        val esofVoyage = ESof(
            encounters = listOf(encounter1, encounter2),
            stops = listOf(),
            slowMovingPeriods = null
        )

        val esofVisit = ESof(
            encounters = listOf(encounter2updated, encounter3),
            stops = listOf(),
            slowMovingPeriods = null
        )

        val esofMerged = ESof(
            encounters = listOf(encounter1, encounter2updated, encounter3),
            stops = listOf(),
            slowMovingPeriods = null
        )

        val statusBefore = VisitShipStatus(
            visit = (status3 as VisitShipStatus).visit.copy(esof = esofVisit),
            previousVoyage = status3.previousVoyage?.copy(esof = esofVoyage),
            previousVisit = null
        )

        val (status) = portProcessor.processEvent(
            statusBefore,
            event2endPassThrough
        )

        assertEquals(esofMerged, status.getCurrentEntry()?.esof)
    }

    @Test
    fun `should merge relevant esof stops when un-finishing a voyage after a pass-through visit`() {
        val event1start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")
        val event2start = createPortEventStart(PORT_DEHAM, "2021-03-11T00:00:00Z")
        val event2endPassThrough = createPortEventEnd(PORT_DEHAM, "2021-03-11T00:05:00Z") // 5 minutes after the start

        val (status1) = portProcessor.processEvent(
            InitialShipStatus,
            event1start
        )
        val (status2) = portProcessor.processEvent(
            status1,
            event1end
        )
        val (status3) = portProcessor.processEvent(
            status2,
            event2start
        )

        assert(status1 is VisitShipStatus)
        assert(status2 is VoyageShipStatus)
        assert(status3 is VisitShipStatus)

        val stop1 = createStop(StopType.ANCHOR_AREA, "2021-03-01T00:00:00Z", null)
        val stop2 = createStop(StopType.BERTH, "2021-03-10T00:00:00Z", null)

        // we use UNCLASSIFIED type here, else we can never get this visit marked as pass through
        val stop3 = createStop(StopType.UNCLASSIFIED, "2021-03-11T00:04:00Z", null)

        val esofVoyage = ESof(
            encounters = listOf(),
            stops = listOf(stop1, stop2),
            slowMovingPeriods = null
        )

        val esofVisit = ESof(
            encounters = listOf(),
            stops = listOf(stop3),
            slowMovingPeriods = null
        )

        val esofMerged = ESof(
            encounters = listOf(),
            stops = listOf(stop1, stop2, stop3),
            slowMovingPeriods = null
        )

        val statusBefore = VisitShipStatus(
            visit = (status3 as VisitShipStatus).visit.copy(esof = esofVisit),
            previousVoyage = status3.previousVoyage?.copy(esof = esofVoyage),
            previousVisit = null
        )

        val (status) = portProcessor.processEvent(
            statusBefore,
            event2endPassThrough
        )

        assertEquals(esofMerged, status.getCurrentEntry()?.esof)
    }

    @Test
    fun `should keep track on the previous voyage and visit when going from visit to voyage and vice versa`() {
        // start a visit
        val (status1) = portProcessor.processEvent(InitialShipStatus, createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z"))
        assertEquals(null, (status1 as VisitShipStatus).previousVoyage)
        assertEquals(null, status1.previousVisit)

        // start a voyage
        val (status2) = portProcessor.processEvent(status1, createPortEventEnd(PORT_NLRTM, "2021-03-09T00:00:00Z"))
        assertTrue(status2 is VoyageShipStatus)
        assertEquals(status1.visit._id, (status2 as VoyageShipStatus).previousVisit?._id)

        // start a visit
        val (status3) = portProcessor.processEvent(status2, createPortEventStart(PORT_BEANR, "2021-03-10T00:00:00Z"))
        assertTrue(status3 is VisitShipStatus)
        assertEquals(status2.voyage._id, (status3 as VisitShipStatus).previousVoyage?._id)
        assertEquals(status1.visit._id, status3.previousVisit?._id)

        // start a voyage
        val (status4) = portProcessor.processEvent(status3, createPortEventEnd(PORT_BEANR, "2021-03-11T00:00:00Z"))
        assertTrue(status4 is VoyageShipStatus)
        assertEquals(status3.visit._id, (status4 as VoyageShipStatus).previousVisit?._id)

        // start a pass-through visit
        val (status5) = portProcessor.processEvent(status4, createPortEventStart(PORT_DEHAM, "2021-03-12T00:00:00Z"))
        assertTrue(status5 is VisitShipStatus)
        assertEquals(status4.voyage._id, (status5 as VisitShipStatus).previousVoyage?._id)
        assertEquals(status3.visit._id, status5.previousVisit?._id)

        // end a pass-through visit (duration of only 1 minute)
        val (status6) = portProcessor.processEvent(status5, createPortEventEnd(PORT_DEHAM, "2021-03-12T00:01:00Z"))
        assertTrue(status6 is VoyageShipStatus)
        assertEquals(status3.visit._id, (status6 as VoyageShipStatus).previousVisit?._id)
    }

    @Test
    fun `should ignore events event with an unknown port`() {
        val eventStart = createPortEventStart(PORT_NOT_EXISTING, "2021-03-08T00:00:00Z")

        val (status, changes, issues) = portProcessor.processEvent(
            InitialShipStatus, eventStart
        )

        assertEquals(InitialShipStatus, status)
        assertEquals(listOf<Change>(), changes)
        assertEquals(
            listOf(
                EventProcessingIssue(
                    eventId = eventStart._id,
                    description = "Unknown port id \"Not Existing Port\" in PortEvent. " +
                        "Will ignore the event (imo: 1111111)"
                )
            ),
            issues
        )
    }
}

private val EVENT_ETA_BEGNE = EtaEvent(
    _id = "EVENT_ETA_BEGNE",
    ship = AisShipIdentifier(MMSI_1, IMO_1),
    area = AreaIdentifier(id = "BEGNE123", type = AreaIdentifier.AreaType.BERTH, unlocode = "BEGNE"),
    predictedTime = ZonedDateTime.parse("2021-05-29T08:00:00Z").toInstant(),
    vesselAgent = null,
    portcallId = "PC12"
)

private val ETA_BEGNE = EVENT_ETA_BEGNE.toEta()

val TIME_1: ZonedDateTime = ZonedDateTime.parse("2021-05-14T00:00:00Z")
val TIME_2: ZonedDateTime = TIME_1.plusHours(24)
val TIME_3: ZonedDateTime = TIME_2.plusHours(4)
val TIME_4: ZonedDateTime = TIME_3.plusHours(8)
val TIME_5: ZonedDateTime = TIME_3.plusHours(12)
val TIME_6: ZonedDateTime = TIME_3.plusHours(48)

private val ANCHOR_AREA_4EAST_DATA = Anchorage(
    _id = ANCHOR_AREA_4EAST,
    name = ANCHOR_AREA_4EAST,
    ports = listOf(NLRTM),
    location = PORT_NLRTM.location.toSkeletonLocation(),
    area = emptyList()
)

private val ANCHOR_AREA_SCHOUWENBANK_DATA = Anchorage(
    _id = ANCHOR_AREA_SCHOUWENBANK,
    name = ANCHOR_AREA_SCHOUWENBANK,
    ports = listOf(BEANR, NLRTM, BEGNE, NLVLI, NLTNZ),
    location = PORT_BEANR.location.toSkeletonLocation(),
    area = emptyList()
)

private val ALL_ANCHOR_AREAS = listOf(
    ANCHOR_AREA_4EAST_DATA,
    ANCHOR_AREA_SCHOUWENBANK_DATA
).associateBy { it.name }
