package nl.teqplay.vesselvoyage.logic

import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.whenever
import nl.teqplay.aisengine.event.model.AnchoredEndEvent
import nl.teqplay.aisengine.event.model.AnchoredStartEvent
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.AnchorAreaVisit
import nl.teqplay.vesselvoyage.model.Change
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.InitialShipStatus
import nl.teqplay.vesselvoyage.model.MAX_ANCHOR_AREAS
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.model.createVisitId
import nl.teqplay.vesselvoyage.model.createVoyageId
import nl.teqplay.vesselvoyage.model.lightweight.poma.Anchorage
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.processing.anchor.AnchorProcessor
import nl.teqplay.vesselvoyage.util.toEta
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.ZoneOffset
import java.time.ZonedDateTime

class ProcessAnchorEventTest {
    private val infraService = mock<InfraService> { service ->
        whenever(service.getAnchorage(any(), any()))
            .thenAnswer { mock -> findAnchorage(mock.getArgument(0)) }
    }
    private val anchorProcessor = AnchorProcessor(mock(), infraService)

    private fun findAnchorage(name: String): Anchorage? {
        if (name == ANCHOR_AREA_1.anchorAreaId) {
            return Anchorage(
                _id = ANCHOR_AREA_1.anchorAreaId,
                name = ANCHOR_AREA_1.anchorAreaId,
                ports = listOf(NLRTM, BEANR),
                location = ANCHOR_AREA_1.location,
                area = listOf()
            )
        }

        if (name == ANCHOR_AREA_2.anchorAreaId) {
            return Anchorage(
                _id = ANCHOR_AREA_2.anchorAreaId,
                name = ANCHOR_AREA_2.anchorAreaId,
                ports = listOf(DEHAM),
                location = ANCHOR_AREA_2.location,
                area = listOf()
            )
        }

        return null
    }

    @Test
    fun `should process a start AnchorEvent with eta and destination`() {
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")
        val anchorEvent1start = createAnchorEventStart(ANCHOR_AREA_1, "2021-03-09T08:00:00Z")
        val dest = createDestination(BEANR, event1end.createdTime.atZone(ZoneOffset.UTC))

        val voyage1start = createVoyage(
            _id = createVoyageId(event1end._id),
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.createdTime.atZone(ZoneOffset.UTC),
        )

        val voyage1startWithDestination = voyage1start.copy(
            dest = dest,
            eta = ETA
        )

        val voyage1end = voyage1start.copy(
            dest = dest,
            eta = ETA,
            endPortIds = null,
            endTime = anchorEvent1start.actualTime.atZone(ZoneOffset.UTC),
            finished = true,
            nextEntryId = createVisitId(anchorEvent1start._id)
        )

        val visit2start = createVisit(
            _id = createVisitId(anchorEvent1start._id),
            anchorAreas = listOf(
                AnchorAreaVisit(
                    anchorAreaId = ANCHOR_AREA_1.anchorAreaId,
                    startEventId = anchorEvent1start._id,
                    startTime = anchorEvent1start.actualTime.atZone(ZoneOffset.UTC),
                    destinations = setOf(BEANR),
                    endEventId = null,
                    endTime = null
                )
            ),
            dest = dest,
            eta = ETA,
            previousEntryId = createVoyageId(event1end._id)
        )

        val statusBefore = VoyageShipStatus(voyage1startWithDestination, null, null)

        val (status, changes, issues) = anchorProcessor.processEvent(statusBefore, anchorEvent1start)

        assertEquals(VisitShipStatus(visit2start, voyage1end, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, voyage1end),
                Change(Action.CREATE, visit2start)
            ),
            changes
        )
        assertEquals(listOf<EventProcessingIssue>(), issues)
    }

    @Test
    fun `should process a start AnchorEvent - do NOT copy outdated eta but keep destination`() {
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")
        val anchorEvent1start = createAnchorEventStart(ANCHOR_AREA_1, "2021-03-09T08:00:00Z")
        val dest = createDestination(BEANR, event1end.actualTime.atZone(ZoneOffset.UTC).minusDays(1))
        val outdatedEta = ETA?.copy(
            predictedAt = event1end.actualTime.atZone(ZoneOffset.UTC).minusDays(1)
        )

        val voyage1start = createVoyage(
            _id = createVoyageId(event1end._id),
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC),
        )

        val voyage1startWithDestination = voyage1start.copy(
            dest = dest,
            eta = outdatedEta
        )

        val voyage1end = voyage1start.copy(
            dest = dest,
            eta = outdatedEta,
            endPortIds = null,
            endTime = anchorEvent1start.actualTime.atZone(ZoneOffset.UTC),
            finished = true,
            nextEntryId = createVisitId(anchorEvent1start._id)
        )

        val visit2start = createVisit(
            _id = createVisitId(anchorEvent1start._id),
            anchorAreas = listOf(
                AnchorAreaVisit(
                    anchorAreaId = ANCHOR_AREA_1.anchorAreaId,
                    startEventId = anchorEvent1start._id,
                    startTime = anchorEvent1start.actualTime.atZone(ZoneOffset.UTC),
                    destinations = setOf(BEANR),
                    endEventId = null,
                    endTime = null
                )
            ),
            dest = dest,
            eta = null,
            previousEntryId = createVoyageId(event1end._id)
        )

        val statusBefore = VoyageShipStatus(voyage1startWithDestination, null, null)

        val (status, changes, issues) = anchorProcessor.processEvent(statusBefore, anchorEvent1start)

        assertEquals(VisitShipStatus(visit2start, voyage1end, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, voyage1end),
                Change(Action.CREATE, visit2start)
            ),
            changes
        )
        assertEquals(listOf<EventProcessingIssue>(), issues)
    }

    @Test
    fun `should add a second start AnchorEvent`() {
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")
        val anchorEvent1start = createAnchorEventStart(ANCHOR_AREA_1, "2021-03-09T08:00:00Z", ANCHOR_AREA_1.anchorAreaId)
        val anchorEvent2start = createAnchorEventStart(ANCHOR_AREA_2, "2021-03-09T11:00:00Z", ANCHOR_AREA_2.anchorAreaId)

        val voyage1start = createVoyage(
            _id = createVoyageId(event1end._id),
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC),
        )

        val voyage1end = voyage1start.copy(
            eta = ETA,
            endPortIds = null,
            endTime = anchorEvent1start.actualTime.atZone(ZoneOffset.UTC),
            finished = true,
            nextEntryId = createVisitId(anchorEvent1start._id)
        )

        val visit2start = createVisit(
            _id = createVisitId(anchorEvent1start._id),
            anchorAreas = listOf(
                AnchorAreaVisit(
                    anchorAreaId = ANCHOR_AREA_1.anchorAreaId,
                    startEventId = anchorEvent1start._id,
                    startTime = anchorEvent1start.actualTime.atZone(ZoneOffset.UTC),
                    destinations = setOf(BEANR),
                    endEventId = null,
                    endTime = null
                )
            ),
            dest = createDestination(BEANR, anchorEvent1start.actualTime.atZone(ZoneOffset.UTC)),
            eta = ETA,
            previousEntryId = createVoyageId(event1end._id)
        )

        val visit2updatedSecondAnchorage = visit2start.copy(
            anchorAreas = visit2start.anchorAreas + AnchorAreaVisit(
                anchorAreaId = ANCHOR_AREA_2.anchorAreaId,
                startEventId = anchorEvent2start._id,
                startTime = anchorEvent2start.actualTime.atZone(ZoneOffset.UTC),
                destinations = setOf(DEHAM),
                endEventId = null,
                endTime = null
            )
        )

        val statusBefore = VisitShipStatus(visit2start, voyage1end, null)

        val (status, changes, issues) = anchorProcessor.processEvent(statusBefore, anchorEvent2start)

        assertEquals(VisitShipStatus(visit2updatedSecondAnchorage, voyage1end, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, visit2updatedSecondAnchorage)
            ),
            changes
        )
        assertEquals(listOf<EventProcessingIssue>(), issues)
    }

    @Test
    fun `should process a start AnchorEvent from initial status`() {
        val anchorEvent1start = createAnchorEventStart(ANCHOR_AREA_1, "2021-03-09T08:00:00Z")

        val visit2start = Visit(
            _id = createVisitId(anchorEvent1start._id),
            mmsi = MMSI_1.toString(),
            imo = IMO_1.toString(),
            anchorAreas = listOf(
                AnchorAreaVisit(
                    anchorAreaId = ANCHOR_AREA_1.anchorAreaId,
                    startEventId = anchorEvent1start._id,
                    startTime = anchorEvent1start.actualTime.atZone(ZoneOffset.UTC),
                    destinations = setOf(NLRTM, BEANR),
                    endEventId = null,
                    endTime = null
                )
            ),
            portAreas = listOf(),
            berthAreas = listOf(),
            dest = null,
            eta = null,
            esof = null,
            passThroughAreas = null,
            finished = false,
            previousEntryId = null,
            nextEntryId = null
        )

        val (status, changes, issues) = anchorProcessor.processEvent(InitialShipStatus, anchorEvent1start)

        assertEquals(VisitShipStatus(visit2start, null, null), status)
        assertEquals(
            listOf(
                Change(Action.CREATE, visit2start)
            ),
            changes
        )
        assertEquals(listOf<EventProcessingIssue>(), issues)
    }

    @Test
    fun `should process a start AnchorEvent using anchorage ports`() {
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")
        val anchorEvent1start = createAnchorEventStart(ANCHOR_AREA_1, "2021-03-09T08:00:00Z")

        val voyage1start = createVoyage(
            _id = createVoyageId(event1end._id),
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC),
        )

        val voyage1endNoDestination = voyage1start.copy(
            endPortIds = null,
            endTime = anchorEvent1start.actualTime.atZone(ZoneOffset.UTC),
            finished = true,
            eta = null,
            nextEntryId = createVisitId(anchorEvent1start._id)
        )

        val visit2start = Visit(
            _id = createVisitId(anchorEvent1start._id),
            mmsi = MMSI_1.toString(),
            imo = IMO_1.toString(),
            anchorAreas = listOf(
                AnchorAreaVisit(
                    anchorAreaId = ANCHOR_AREA_1.anchorAreaId,
                    startEventId = anchorEvent1start._id,
                    startTime = anchorEvent1start.actualTime.atZone(ZoneOffset.UTC),
                    destinations = setOf(NLRTM, BEANR),
                    endEventId = null,
                    endTime = null
                )
            ),
            portAreas = listOf(),
            berthAreas = listOf(),
            dest = null,
            eta = null,
            esof = null,
            passThroughAreas = null,
            finished = false,
            previousEntryId = createVoyageId(event1end._id),
            nextEntryId = null
        )

        val statusBefore = VoyageShipStatus(voyage1start, null, null)

        val (status, changes, issues) = anchorProcessor.processEvent(statusBefore, anchorEvent1start)

        assertEquals(VisitShipStatus(visit2start, voyage1endNoDestination, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, voyage1endNoDestination),
                Change(Action.CREATE, visit2start)
            ),
            changes
        )
        assertEquals(listOf<EventProcessingIssue>(), issues)
    }

    @Test
    fun `should process an end AnchorEvent - do NOT copy outdated destination`() {
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")
        val anchorEvent1start = createAnchorEventStart(ANCHOR_AREA_1, "2021-03-09T08:00:00Z")
        val anchorEvent1end = createAnchorEventEnd(ANCHOR_AREA_1, "2021-03-11T02:00:00Z")
        val dest = createDestination(BEANR, anchorEvent1start.actualTime.atZone(ZoneOffset.UTC))

        val visit2start = createVisit(
            _id = createVisitId(anchorEvent1start._id),
            anchorAreas = listOf(
                AnchorAreaVisit(
                    anchorAreaId = ANCHOR_AREA_1.anchorAreaId,
                    startEventId = anchorEvent1start._id,
                    startTime = anchorEvent1start.actualTime.atZone(ZoneOffset.UTC),
                    destinations = setOf(BEANR),
                    endEventId = null,
                    endTime = null
                )
            ),
            dest = dest,
            eta = ETA,
            previousEntryId = createVoyageId(event1end._id)
        )

        val visit2updated = createVisit(
            _id = createVisitId(anchorEvent1start._id),
            anchorAreas = listOf(
                AnchorAreaVisit(
                    anchorAreaId = ANCHOR_AREA_1.anchorAreaId,
                    startEventId = anchorEvent1start._id,
                    startTime = anchorEvent1start.actualTime.atZone(ZoneOffset.UTC),
                    destinations = setOf(BEANR),
                    endEventId = anchorEvent1end._id,
                    endTime = anchorEvent1end.actualTime.atZone(ZoneOffset.UTC)
                )
            ),
            dest = null,
            eta = ETA,
            previousEntryId = createVoyageId(event1end._id),
        )

        val statusBefore = VisitShipStatus(visit2start, null, null)

        val (status, changes, issues) = anchorProcessor.processEvent(statusBefore, anchorEvent1end)

        assertEquals(VisitShipStatus(visit2updated, null, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, visit2updated)
            ),
            changes
        )
        assertEquals(listOf<EventProcessingIssue>(), issues)
    }

    @Test
    fun `should ignore a mismatching end AnchorEvent`() {
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")
        val anchorEvent1start = createAnchorEventStart(ANCHOR_AREA_1, "2021-03-09T08:00:00Z")
        val anchorEvent1end = createAnchorEventEnd(ANCHOR_AREA_1, "2021-03-11T02:00:00Z")

        val visit2start = createVisit(
            _id = createVisitId(anchorEvent1start._id),
            anchorAreas = listOf(
                AnchorAreaVisit(
                    anchorAreaId = ANCHOR_AREA_1.anchorAreaId,
                    startEventId = anchorEvent1start._id,
                    startTime = anchorEvent1start.actualTime.atZone(ZoneOffset.UTC),
                    destinations = setOf(BEANR),
                    endEventId = null,
                    endTime = null
                )
            ),
            dest = createDestination(BEANR, anchorEvent1start.actualTime.atZone(ZoneOffset.UTC)),
            eta = ETA,
            previousEntryId = createVoyageId(event1end._id)
        )

        val statusBefore = VisitShipStatus(visit2start, null, null)
        val anchorEventWrongArea = (anchorEvent1end as AnchoredEndEvent).copy(
            area = anchorEvent1end.area.copy(name = ANCHOR_AREA_2.anchorAreaId)
        )

        val (status, changes, issues) = anchorProcessor.processEvent(statusBefore, anchorEventWrongArea)

        assertEquals(statusBefore, status)
        assertEquals(emptyList<Change>(), changes)
        assertEquals(
            listOf(
                EventProcessingIssue(
                    eventId = anchorEvent1end._id,
                    description = "Anchor end event does not match an anchor area of the current visit. " +
                        "Ignoring event (imo: 1111111, open anchor areas: 4EAST, event area: ANCHOR SCHOUWENBANK (BEANR))"
                )
            ),
            issues
        )
    }

    @Test
    fun `should ignore end event when start is missing (from voyage)`() {
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")
        val anchorEvent1end = createAnchorEventEnd(ANCHOR_AREA_1, "2021-03-11T02:00:00Z")

        val voyage1start = createVoyage(
            _id = createVoyageId(event1end._id),
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC),
        )

        val voyage1startWithDestination = voyage1start.copy(
            dest = createDestination(BEANR, event1end.actualTime.atZone(ZoneOffset.UTC)),
            eta = ETA
        )

        val statusBefore = VoyageShipStatus(voyage1startWithDestination, null, null)

        val (status, changes, issues) = anchorProcessor.processEvent(statusBefore, anchorEvent1end)

        assertEquals(statusBefore, status)
        assertEquals(listOf<Change>(), changes)
        assertEquals(
            listOf(
                EventProcessingIssue(
                    eventId = anchorEvent1end._id,
                    description = "Unexpected anchor end event: no corresponding anchor start event, " +
                        "and there is still an ongoing voyage. " +
                        "Will ignore this anchor event (imo: 1111111, event area: 4EAST)"
                )
            ),
            issues
        )
    }

    @Test
    fun `should ignore end event when start is missing (initial status)`() {
        val anchorEvent1end = createAnchorEventEnd(ANCHOR_AREA_1, "2021-03-11T02:00:00Z")

        val (status, changes, issues) = anchorProcessor.processEvent(InitialShipStatus, anchorEvent1end)

        assertEquals(InitialShipStatus, status)
        assertEquals(listOf<Change>(), changes)
        assertEquals(
            listOf(
                EventProcessingIssue(
                    eventId = anchorEvent1end._id,
                    description = "Unexpected anchor end event: no corresponding anchor start event, ship status is empty. " +
                        "Will ignore this anchor event (imo: 1111111, event area: 4EAST)"
                )
            ),
            issues
        )
    }

    @Test
    fun `should ignore event when the IMO does not match`() {
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")
        val anchorEvent1start = createAnchorEventStart(ANCHOR_AREA_1, "2021-03-09T08:00:00Z")

        val voyage1start = createVoyage(
            _id = createVoyageId(event1end._id),
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC),
        )

        val voyage1startWithDestination = voyage1start.copy(
            dest = createDestination(BEANR, event1end.actualTime.atZone(ZoneOffset.UTC)),
            eta = ETA
        )

        val statusBefore = VoyageShipStatus(voyage1startWithDestination, null, null)
        val eventWithDifferingImo = (anchorEvent1start as AnchoredStartEvent).copy(ship = anchorEvent1start.ship.copy(imo = IMO_2))

        val (status, changes, issues) = anchorProcessor.processEvent(statusBefore, eventWithDifferingImo)

        assertEquals(statusBefore, status)
        assertEquals(listOf<Change>(), changes)
        assertEquals(
            listOf(
                EventProcessingIssue(
                    eventId = anchorEvent1start._id,
                    description = "Event has mismatching IMO: ship status has IMO 1111111, event has IMO 2222222. " +
                        "New event will be ignored (eventId: ${anchorEvent1start._id})"
                )
            ),
            issues
        )
    }

    @Test
    fun `should ignore event when the event time is outdated`() {
        val event1end = createPortEventEnd(PORT_NLRTM, "2021-03-09T04:00:00Z")
        val anchorEvent1start = createAnchorEventStart(ANCHOR_AREA_1, "2021-03-09T08:00:00Z")

        val voyage1start = createVoyage(
            _id = createVoyageId(event1end._id),
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC),
        )

        val voyage1startWithDestination = voyage1start.copy(
            dest = createDestination(BEANR, event1end.actualTime.atZone(ZoneOffset.UTC)),
            eta = ETA
        )

        val statusBefore = VoyageShipStatus(voyage1startWithDestination, null, null)
        val eventWithOldEventTime = (anchorEvent1start as AnchoredStartEvent).copy(actualTime = statusBefore.voyage.startTime.minusDays(2).toInstant())

        val (status, changes, issues) = anchorProcessor.processEvent(statusBefore, eventWithOldEventTime)

        assertEquals(statusBefore, status)
        assertEquals(listOf<Change>(), changes)
        assertEquals(
            listOf(
                EventProcessingIssue(
                    eventId = anchorEvent1start._id,
                    description = "Event time is older than the latest event. " +
                        "New event will be ignored (eventId: ${anchorEvent1start._id})"
                )
            ),
            issues
        )
    }

    @Test
    fun `should protect against storing too many anchorAreas in a single visit`() {
        val anchorEvent1start = createAnchorEventStart(ANCHOR_AREA_1, "2021-03-09T08:00:00Z", ANCHOR_AREA_4EAST)
        val anchorEvent2start = createAnchorEventStart(ANCHOR_AREA_2, "2021-03-09T11:00:00Z", ANCHOR_AREA_SCHOUWENBANK)

        val visit2start = createVisit(
            _id = createVisitId(anchorEvent1start._id),
            anchorAreas = List(MAX_ANCHOR_AREAS) {
                AnchorAreaVisit(
                    anchorAreaId = ANCHOR_AREA_1.anchorAreaId,
                    startEventId = anchorEvent1start._id,
                    startTime = anchorEvent1start.actualTime.atZone(ZoneOffset.UTC),
                    destinations = setOf(BEANR),
                    endEventId = null,
                    endTime = null
                )
            }
        )

        val statusBefore = VisitShipStatus(visit2start, null, null)

        val (status, changes, issues) = anchorProcessor.processEvent(statusBefore, anchorEvent2start)

        assertEquals(statusBefore, status)
        assertEquals(listOf<Change>(), changes)
        assertEquals(
            listOf(
                EventProcessingIssue(
                    eventId = anchorEvent2start._id,
                    description = "Cannot process AnchorEvent: too many anchor areas in current visit. " +
                        "Will ignore the event (imo: 1111111, anchorAreas: 100)"
                )
            ),
            issues
        )
    }

    @Test
    fun `should keep track on the previous voyage and visit when going from a voyage to a visit with anchorage`() {
        val anchorEvent1start = createAnchorEventStart(ANCHOR_AREA_1, "2021-03-11T00:00:00Z")

        // note that these previousVisit and voyage are not setup 100% correct, but enough for this test
        val previousVisit = createVisit(
            portAreas = listOf(
                createPortAreaVisit(
                    createPortEventStart(PORT_NLRTM, "2021-03-07T00:00:00Z"),
                    createPortEventEnd(PORT_NLRTM, "2021-03-09T00:00:00Z"),
                )
            ),
            finished = true
        )
        val voyage = createVoyage(
            startPortIds = listOf(NLRTM),
            startTime = ZonedDateTime.parse("2021-03-09T00:00:00Z"),
            endPortIds = listOf(BEANR),
            endTime = ZonedDateTime.parse("2021-03-10T00:00:00Z"),
            finished = true,
        )

        val statusBefore = VoyageShipStatus(voyage, previousVisit, null)

        val (status) = anchorProcessor.processEvent(statusBefore, anchorEvent1start)
        assertEquals(voyage._id, (status as VisitShipStatus).previousVoyage?._id)
        assertEquals(previousVisit._id, status.previousVisit?._id)
    }
}

private val ETA_EVENT = createEtaEvent(
    port = PORT_NLRTM,
    time = "2021-05-28T13:00:00Z",
    predictedTime = "2021-05-29T08:00:00Z"
)

private val ETA = ETA_EVENT.toEta()
