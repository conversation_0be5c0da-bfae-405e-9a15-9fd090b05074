package nl.teqplay.vesselvoyage.logic

import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.whenever
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.Change
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.EventProcessingResult
import nl.teqplay.vesselvoyage.model.EventStatus
import nl.teqplay.vesselvoyage.model.InitialShipStatus
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.AisFetchingService
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.processing.berth.UniqueBerthProcessor
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.Duration
import java.time.ZonedDateTime

class ProcessUniqueBerthEventTest {
    private val config = EventProcessingProperties(
        maxSpeedMps = 1.0,
        minDuration = Duration.ofMinutes(30),
        newStopDetection = true,
        enableTraceCalculations = true,
        enableSlowMovingPeriods = true,
        enableNewDefinition = false,
        enableOldDefinition = true,
        totalThreads = 5,
        logResults = false,
        activenessMonitoring = EventProcessingProperties.ActivenessMonitoring(
            enabled = false,
            interval = 1000,
            maxIdleTime = Duration.ofMinutes(5)
        )
    )

    private val infraService = mock<InfraService> { service ->
        whenever(service.getBerth(any())).thenReturn(null)
        whenever(service.getBerthsByLocation(any())).thenReturn(emptyList())
        whenever(service.getAnchoragesByLocation(any())).thenReturn(emptyList())
    }

    private val aisFetchingService = mock<AisFetchingService> {
        whenever(it.getTraceForStopDetection(any(), any())).thenReturn(emptyList())
    }

    private val uniqueBerthProcessor = UniqueBerthProcessor(config, infraService, aisFetchingService)

    @Test
    fun `should add a start UniqueBerthEvent to a visit`() {
        val portEvent = createPortEventStart(PORT_NLRTM, "2022-01-08T19:00:00Z")
        val berthEventStart = createUniqueBerthEvent(EventStatus.START, BERTH_ID_1, "2022-01-08T22:00:00Z")

        val visit = createVisit(
            portAreas = listOf(
                createPortAreaVisit(portEvent)
            )
        )
        val statusBefore = VisitShipStatus(visit, null, null)

        val updatedVisit = visit.copy(
            berthAreas = listOf(
                createBerthAreaVisit(berthEventStart)
            )
        )

        val (status, changes, issues) = uniqueBerthProcessor.processEvent(
            statusBefore,
            berthEventStart
        )

        assertEquals(VisitShipStatus(updatedVisit, null, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, updatedVisit)
            ),
            changes
        )
        assertEquals(listOf<EventProcessingIssue>(), issues)
    }

    @Test
    fun `should ignore a start UniqueBerthEvent when there is already an ongoing berth visit`() {
        val portEvent = createPortEventStart(PORT_NLRTM, "2022-01-08T19:00:00Z")
        val berthEventStart1 = createUniqueBerthEvent(EventStatus.START, BERTH_ID_1, "2022-01-08T22:00:00Z")
        val berthEventStart2 = createUniqueBerthEvent(EventStatus.START, BERTH_ID_1, "2022-01-09T04:00:00Z")

        val visit = createVisit(
            portAreas = listOf(
                createPortAreaVisit(portEvent)
            ),
            berthAreas = listOf(
                createBerthAreaVisit(berthEventStart1)
            )
        )
        val statusBefore = VisitShipStatus(visit, null, null)

        val (status, changes, issues) = uniqueBerthProcessor.processEvent(
            statusBefore,
            berthEventStart2
        )

        assertEquals(statusBefore, status)
        assertEquals(listOf<Change>(), changes)
        assertEquals(
            listOf(
                EventProcessingIssue(
                    eventId = berthEventStart2._id,
                    description = "Cannot process UniqueBerthEvent start: ship is currently already in this berth. " +
                        "Will ignore the event (imo: 1111111, berthId: Z100/11/430)"
                )
            ),
            issues
        )
    }

    @Test
    fun `should add an end UniqueBerthEvent to a visit`() {
        val portEvent = createPortEventStart(PORT_NLRTM, "2022-01-08T19:00:00Z")
        val berthEventStart = createUniqueBerthEvent(EventStatus.START, BERTH_ID_1, "2022-01-08T22:00:00Z")
        val berthEventEnd = createUniqueBerthEvent(EventStatus.END, BERTH_ID_1, "2022-01-09T04:00:00Z")

        val visit = createVisit(
            portAreas = listOf(
                createPortAreaVisit(portEvent)
            ),
            berthAreas = listOf(
                createBerthAreaVisit(berthEventStart)
            )
        )
        val statusBefore = VisitShipStatus(visit, null, null)

        val updatedVisit = visit.copy(
            berthAreas = listOf(
                createBerthAreaVisit(berthEventStart, berthEventEnd)
            ),
        )

        val (status, changes, issues) = uniqueBerthProcessor.processEvent(
            statusBefore,
            berthEventEnd
        )

        assertEquals(VisitShipStatus(updatedVisit, null, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, updatedVisit)
            ),
            changes
        )
        assertEquals(listOf<EventProcessingIssue>(), issues)
    }

    @Test
    fun `should add an end UniqueBerthEvent to the matching berthArea`() {
        val portEvent = createPortEventStart(PORT_NLRTM, "2022-01-08T19:00:00Z")
        val berthEventStart1 = createUniqueBerthEvent(EventStatus.START, BERTH_ID_1, "2022-01-08T22:00:00Z")
        val berthEventStart2 = createUniqueBerthEvent(EventStatus.START, BERTH_ID_2, "2022-01-08T22:00:00Z")
        val berthEventEnd1 = createUniqueBerthEvent(EventStatus.END, BERTH_ID_1, "2022-01-09T04:00:00Z")

        val visit = createVisit(
            portAreas = listOf(
                createPortAreaVisit(portEvent)
            ),
            berthAreas = listOf(
                createBerthAreaVisit(berthEventStart1),
                createBerthAreaVisit(berthEventStart2)
            )
        )
        val statusBefore = VisitShipStatus(visit, null, null)

        val updatedVisit = visit.copy(
            berthAreas = listOf(
                createBerthAreaVisit(berthEventStart1, berthEventEnd1),
                createBerthAreaVisit(berthEventStart2)
            ),
        )

        val (status, changes, issues) = uniqueBerthProcessor.processEvent(
            statusBefore,
            berthEventEnd1
        )

        assertEquals(VisitShipStatus(updatedVisit, null, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, updatedVisit)
            ),
            changes
        )
        assertEquals(listOf<EventProcessingIssue>(), issues)
    }

    @Test
    fun `should ignore an end UniqueBerthEvent when there is no matching start event`() {
        val portEvent = createPortEventStart(PORT_NLRTM, "2022-01-08T19:00:00Z")
        val berthEventStart = createUniqueBerthEvent(EventStatus.START, BERTH_ID_1, "2022-01-08T22:00:00Z")
        val berthEventEnd = createUniqueBerthEvent(EventStatus.END, BERTH_ID_2, "2022-01-09T04:00:00Z")

        val visit = createVisit(
            portAreas = listOf(
                createPortAreaVisit(portEvent)
            ),
            berthAreas = listOf(
                createBerthAreaVisit(berthEventStart)
            )
        )
        val statusBefore = VisitShipStatus(visit, null, null)

        val (status, changes, issues) = uniqueBerthProcessor.processEvent(
            statusBefore,
            berthEventEnd
        )

        assertEquals(VisitShipStatus(visit, null, null), status)
        assertEquals(listOf<Change>(), changes)
        assertEquals(
            listOf(
                EventProcessingIssue(
                    eventId = berthEventEnd._id,
                    description = "Unique berth end event does not match any berth area visit of the current visit. " +
                        "Ignoring event (imo: 1111111, open berth areas: Z100/11/430, event berthId: Berth 2)"
                )
            ),
            issues
        )
    }

    @Test
    fun `should ignore a start UniqueBerthEvent when there is a no ship status`() {
        val berthEventStart = createUniqueBerthEvent(EventStatus.START, BERTH_ID_1, "2022-01-08T22:00:00Z")

        assertEquals(
            EventProcessingResult(
                status = InitialShipStatus,
                changes = emptyList(),
                issues = listOf(
                    EventProcessingIssue(
                        eventId = berthEventStart._id,
                        description = "Cannot process UniqueBerthEvent: no visit active " +
                            "(imo: 1111111, portId: NLRTM, berthId: Z100/11/430)"
                    )
                )
            ),
            uniqueBerthProcessor.processEvent(
                InitialShipStatus,
                berthEventStart
            )
        )
    }

    @Test
    fun `should ignore a start UniqueBerthEvent when there is a voyage going on`() {
        val berthEventStart = createUniqueBerthEvent(EventStatus.START, BERTH_ID_1, "2022-01-08T22:00:00Z")

        val voyageShipStatus = VoyageShipStatus(
            voyage = createVoyage(
                _id = "voyage1",
                startPortIds = listOf(NLRTM),
                startTime = ZonedDateTime.parse("2022-01-08T21:00:00Z")
            ),
            previousVisit = null,
            previousVoyage = null
        )

        assertEquals(
            EventProcessingResult(
                status = voyageShipStatus,
                changes = emptyList(),
                issues = listOf(
                    EventProcessingIssue(
                        eventId = berthEventStart._id,
                        description = "Cannot process UniqueBerthEvent: no visit active " +
                            "(imo: 1111111, portId: NLRTM, berthId: Z100/11/430)"
                    )
                )
            ),
            uniqueBerthProcessor.processEvent(
                voyageShipStatus,
                berthEventStart
            )
        )
    }

    @Test
    fun `should ignore an end UniqueBerthEvent when there is a no ship status`() {
        val berthEventStart = createUniqueBerthEvent(EventStatus.START, BERTH_ID_1, "2022-01-08T22:00:00Z")

        assertEquals(
            EventProcessingResult(
                status = InitialShipStatus,
                changes = emptyList(),
                issues = listOf(
                    EventProcessingIssue(
                        eventId = berthEventStart._id,
                        description = "Cannot process UniqueBerthEvent: no visit active " +
                            "(imo: 1111111, portId: NLRTM, berthId: Z100/11/430)"
                    )
                )
            ),
            uniqueBerthProcessor.processEvent(
                InitialShipStatus,
                berthEventStart
            )
        )
    }

    @Test
    fun `should ignore an end UniqueBerthEvent when there is a voyage going on`() {
        val berthEventStart = createUniqueBerthEvent(EventStatus.START, BERTH_ID_1, "2022-01-09T16:00:00Z")

        val voyageShipStatus = VoyageShipStatus(
            voyage = createVoyage(
                _id = "voyage1",
                startPortIds = listOf(NLRTM),
                startTime = ZonedDateTime.parse("2022-01-08T21:00:00Z")
            ),
            previousVisit = null,
            previousVoyage = null
        )

        assertEquals(
            EventProcessingResult(
                status = voyageShipStatus,
                changes = emptyList(),
                issues = listOf(
                    EventProcessingIssue(
                        eventId = berthEventStart._id,
                        description = "Cannot process UniqueBerthEvent: no visit active " +
                            "(imo: 1111111, portId: NLRTM, berthId: Z100/11/430)"
                    )
                )
            ),
            uniqueBerthProcessor.processEvent(
                voyageShipStatus,
                berthEventStart
            )
        )
    }
}

private const val BERTH_ID_1 = "Z100/11/430"
private const val BERTH_ID_2 = "Berth 2"
