package nl.teqplay.vesselvoyage.logic

import com.nhaarman.mockitokotlin2.mock
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.Change
import nl.teqplay.vesselvoyage.model.Destination
import nl.teqplay.vesselvoyage.model.InitialShipStatus
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.service.processing.eta.EtaProcessor
import nl.teqplay.vesselvoyage.util.toEta
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.ZoneOffset

class ProcessEtaEventTest {
    private val etaProcessor = EtaProcessor(mock())

    @Test
    fun `should process an ETA event in a voyage`() {
        val event1end = createPortEventEnd(PORT_BEANR, "2021-03-09T04:00:00Z")

        val etaEvent = createEtaEvent(
            port = PORT_NLRTM,
            time = "2021-05-27T00:00:00Z",
            predictedTime = "2021-05-28T13:00:00Z"
        )

        val voyage1start = createVoyage(
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC),
            dest = Destination(
                updatedAt = event1end.actualTime.atZone(ZoneOffset.UTC),
                aisDestination = PORT_NLRTM.portId,
                trueDestination = PORT_NLRTM.portId
            )
        )

        val voyage1updated = voyage1start.copy(
            eta = etaEvent.toEta()
        )

        val statusBefore = VoyageShipStatus(voyage1start, null, null)

        val (status, changes) = etaProcessor.processEvent(statusBefore, etaEvent)

        assertEquals(VoyageShipStatus(voyage1updated, null, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, voyage1updated)
            ),
            changes
        )
    }

    @Test
    fun `should ignore an ETA event the ETA doesn't change in a voyage`() {
        val event1end = createPortEventEnd(PORT_BEANR, "2021-03-09T04:00:00Z")

        val etaEvent = createEtaEvent(
            port = PORT_NLRTM,
            time = "2021-05-27T00:00:00Z",
            predictedTime = "2021-05-28T13:00:00Z"
        )

        val voyage1start = createVoyage(
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC),
            dest = Destination(
                updatedAt = event1end.actualTime.atZone(ZoneOffset.UTC),
                aisDestination = PORT_NLRTM.portId,
                trueDestination = PORT_NLRTM.portId
            ),
            eta = etaEvent.toEta()
        )

        val statusBefore = VoyageShipStatus(voyage1start, null, null)

        val (status, changes) = etaProcessor.processEvent(statusBefore, etaEvent)

        assertEquals(VoyageShipStatus(voyage1start, null, null), status)
        assertEquals(emptyList<Change>(), changes)
    }

    @Test
    fun `should ignore an ETA event in a voyage when no destination is set`() {
        val event1end = createPortEventEnd(PORT_BEANR, "2021-03-09T04:00:00Z")

        val voyage = createVoyage(
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC),
            dest = null
        )

        val etaEvent = createEtaEvent(
            port = PORT_NLRTM,
            time = "2021-05-27T00:00:00Z",
            predictedTime = "2021-05-28T13:00:00Z"
        )

        val statusBefore = VoyageShipStatus(voyage, null, null)

        val (status, changes) = etaProcessor.processEvent(statusBefore, etaEvent)

        assertEquals(statusBefore, status)
        assertEquals(emptyList<Change>(), changes)
    }

    @Test
    fun `should ignore an ETA event in a voyage when the destination doesn't match`() {
        val event1end = createPortEventEnd(PORT_BEANR, "2021-03-09T04:00:00Z")

        val voyage = createVoyage(
            startPortIds = listOf(event1end.area.unlocode ?: ""),
            startTime = event1end.actualTime.atZone(ZoneOffset.UTC),
            dest = Destination(
                updatedAt = event1end.actualTime.atZone(ZoneOffset.UTC),
                aisDestination = "NON_MATCHING_DESTINATION",
                trueDestination = "NON_MATCHING_DESTINATION"
            )
        )

        val statusBefore = VoyageShipStatus(voyage, null, null)

        val etaEvent = createEtaEvent(
            port = PORT_NLRTM,
            time = "2021-05-27T00:00:00Z",
            predictedTime = "2021-05-28T13:00:00Z"
        )

        val (status, changes) = etaProcessor.processEvent(statusBefore, etaEvent)

        assertEquals(statusBefore, status)
        assertEquals(emptyList<Change>(), changes)
    }

    @Test
    fun `should process an ETA event in a visit`() {
        val event2start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")

        val etaEvent = createEtaEvent(
            port = PORT_NLRTM,
            time = "2021-05-27T00:00:00Z",
            predictedTime = "2021-05-28T13:00:00Z"
        )

        val visit2start = createVisit(
            portAreas = listOf(createPortAreaVisit(event2start)),
            dest = Destination(
                updatedAt = event2start.actualTime.atZone(ZoneOffset.UTC),
                aisDestination = etaEvent.area.unlocode ?: "",
                trueDestination = etaEvent.area.unlocode ?: ""
            )
        )

        val visit2updated = visit2start.copy(
            eta = etaEvent.toEta()
        )

        val statusBefore = VisitShipStatus(visit2start, null, null)

        val (status, changes) = etaProcessor.processEvent(statusBefore, etaEvent)

        assertEquals(VisitShipStatus(visit2updated, null, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, visit2updated)
            ),
            changes
        )
    }

    @Test
    fun `should ignore an ETA event in a visit when no destination is set`() {
        val event2start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")

        val etaEvent = createEtaEvent(
            port = PORT_NLRTM,
            time = "2021-05-27T00:00:00Z",
            predictedTime = "2021-05-28T13:00:00Z"
        )

        val visit2start = createVisit(
            portAreas = listOf(createPortAreaVisit(event2start)),
            dest = Destination(
                updatedAt = event2start.actualTime.atZone(ZoneOffset.UTC),
                aisDestination = etaEvent.area.unlocode ?: "",
                trueDestination = etaEvent.area.unlocode ?: ""
            )
        )

        val statusBefore = VisitShipStatus(visit2start.copy(dest = null), null, null)

        val (status, changes) = etaProcessor.processEvent(statusBefore, etaEvent)

        assertEquals(statusBefore, status)
        assertEquals(emptyList<Change>(), changes)
    }

    @Test
    fun `should ignore an ETA event in a visit when the destination doesn't match`() {
        val event2start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")

        val etaEvent = createEtaEvent(
            port = PORT_NLRTM,
            time = "2021-05-27T00:00:00Z",
            predictedTime = "2021-05-28T13:00:00Z"
        )

        val visit2start = createVisit(
            portAreas = listOf(createPortAreaVisit(event2start)),
            dest = Destination(
                updatedAt = event2start.actualTime.atZone(ZoneOffset.UTC),
                aisDestination = etaEvent.area.unlocode ?: "",
                trueDestination = etaEvent.area.unlocode ?: ""
            )
        )

        val statusBefore = VisitShipStatus(
            visit2start.copy(
                dest = Destination(
                    updatedAt = event2start.actualTime.atZone(ZoneOffset.UTC),
                    aisDestination = "NON_MATCHING_DESTINATION",
                    trueDestination = "NON_MATCHING_DESTINATION"
                )
            ),
            null, null
        )

        val (status, changes) = etaProcessor.processEvent(statusBefore, etaEvent)

        assertEquals(statusBefore, status)
        assertEquals(emptyList<Change>(), changes)
    }

    @Test
    fun `should ignore an ETA event the ETA doesn't change in a visit`() {
        val event2start = createPortEventStart(PORT_NLRTM, "2021-03-08T00:00:00Z")

        val etaEvent = createEtaEvent(
            port = PORT_NLRTM,
            time = "2021-05-27T00:00:00Z",
            predictedTime = "2021-05-28T13:00:00Z"
        )
        val visit2startWithEta = createVisit(
            portAreas = listOf(createPortAreaVisit(event2start)),
            dest = Destination(
                updatedAt = event2start.actualTime.atZone(ZoneOffset.UTC),
                aisDestination = etaEvent.area.unlocode ?: "",
                trueDestination = etaEvent.area.unlocode ?: ""
            ),
            eta = etaEvent.toEta()
        )

        val statusBefore = VisitShipStatus(visit2startWithEta, null, null)

        val (status, changes) = etaProcessor.processEvent(statusBefore, etaEvent)

        assertEquals(VisitShipStatus(visit2startWithEta, null, null), status)
        assertEquals(emptyList<Change>(), changes)
    }

    @Test
    fun `should ignore an ETA event when there is no voyage active (initial status)`() {
        val etaEvent = createEtaEvent(
            port = PORT_NLRTM,
            time = "2021-05-27T00:00:00Z",
            predictedTime = "2021-05-28T13:00:00Z"
        )

        val (status, changes) = etaProcessor.processEvent(InitialShipStatus, etaEvent)

        assertEquals(InitialShipStatus, status)
        assertEquals(emptyList<Change>(), changes)
    }
}
