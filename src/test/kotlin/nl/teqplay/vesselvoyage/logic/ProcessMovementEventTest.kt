package nl.teqplay.vesselvoyage.logic

import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.whenever
import nl.teqplay.platform.util.LocationUtils.createBoundingBox
import nl.teqplay.vesselvoyage.logic.CalculateStops.Companion.fillPomaStop
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.Change
import nl.teqplay.vesselvoyage.model.ESof
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.Location
import nl.teqplay.vesselvoyage.model.MAX_STOPS
import nl.teqplay.vesselvoyage.model.MovementStatus
import nl.teqplay.vesselvoyage.model.StopDetectionLocation
import nl.teqplay.vesselvoyage.model.StopEndDetectionInfo
import nl.teqplay.vesselvoyage.model.StopStartDetectionInfo
import nl.teqplay.vesselvoyage.model.StopType
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.AisFetchingService
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.processing.movement.MovementProcessor
import nl.teqplay.vesselvoyage.util.toVesselVoyageLocation
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.Duration
import java.time.Instant
import java.time.ZoneOffset
import java.time.ZonedDateTime

class ProcessMovementEventTest {
    private val config = EventProcessingProperties(
        maxSpeedMps = 1.0,
        minDuration = Duration.ofMinutes(30),
        newStopDetection = true,
        enableTraceCalculations = true,
        enableSlowMovingPeriods = true,
        enableNewDefinition = false,
        enableOldDefinition = true,
        totalThreads = 5,
        logResults = false,
        activenessMonitoring = EventProcessingProperties.ActivenessMonitoring(
            enabled = false,
            interval = 1000,
            maxIdleTime = Duration.ofMinutes(5)
        )
    )

    private val infraService = mock<InfraService> {
        whenever(it.getBerthsByLocation(any())).thenReturn(emptyList())
        whenever(it.getAnchoragesByLocation(any())).thenReturn(emptyList())
    }

    private val aisFetchingService = mock<AisFetchingService> {
        whenever(it.getTraceForStopDetection(any(), any())).thenReturn(emptyList())
    }

    private val movementProcessor = MovementProcessor(config, infraService, aisFetchingService)

    @Test
    fun `should create a Stop after processing a MovementEvent with status STOP`() {
        val portEvent = createPortEventStart(PORT_NLRTM, "2021-11-21T00:00:00Z")

        val timeStop = "2021-11-22T00:00:00Z"
        val movementEvent = createMovementEvent(MovementStatus.STOP, timeStop)
        val visit = createVisit(
            portAreas = listOf(createPortAreaVisit(portEvent))
        )
        val visitUpdated = visit.copy(
            esof = ESof(
                encounters = emptyList(),
                stops = listOf(
                    createStop(
                        type = StopType.UNCLASSIFIED,
                        startEventId = movementEvent._id,
                        startTime = timeStop,
                        endTime = null,
                        accuracy = 0.0f
                    ).copy(
                        aisStart = StopStartDetectionInfo(
                            id = movementEvent._id,
                            location = PORT_NLRTM.location,
                            time = ZonedDateTime.parse(timeStop)
                        )
                    )
                ),
                slowMovingPeriods = null
            )
        )

        val statusBefore = VisitShipStatus(visit, null, null)
        val (status, changes) = movementProcessor.processEvent(statusBefore, movementEvent)

        assertEquals(VisitShipStatus(visitUpdated, null, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, visitUpdated)
            ),
            changes
        )
    }

    @Test
    fun `should update a Stop after processing a MovementEvent with status START`() {
        val portEvent = createPortEventStart(PORT_NLRTM, "2021-11-21T00:00:00Z")

        val timeStop = "2021-11-22T00:00:00Z"
        val timeStart = "2021-11-23T00:00:00Z"
        val movementEvent = createMovementEvent(MovementStatus.START, timeStart)

        val ongoingStop = createStop(StopType.UNCLASSIFIED, timeStop, null)

        val visit = createVisit(
            portAreas = listOf(createPortAreaVisit(portEvent)),
            esof = ESof(
                encounters = emptyList(),
                stops = listOf(ongoingStop),
                slowMovingPeriods = null
            )
        )

        val finishedStop = ongoingStop.copy(
            endEventId = movementEvent._id,
            endTime = movementEvent.actualTime.atZone(ZoneOffset.UTC),
            endLocation = movementEvent.location.toVesselVoyageLocation(),
            aisEnd = StopEndDetectionInfo(
                id = movementEvent._id,
                location = movementEvent.location.toVesselVoyageLocation(),
                time = movementEvent.actualTime.atZone(ZoneOffset.UTC)
            )
        )
        val visitUpdated = visit.copy(
            esof = visit.esof?.copy(
                stops = listOf(finishedStop)
            )
        )

        val statusBefore = VisitShipStatus(visit, null, null)
        val (status, changes) = movementProcessor.processEvent(statusBefore, movementEvent)

        assertEquals(VisitShipStatus(visitUpdated, null, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, visitUpdated)
            ),
            changes
        )
    }

    @Test
    fun `should ignore a MovementEvent with status STOP when status is already stopped`() {
        val portEvent = createPortEventStart(PORT_NLRTM, "2021-11-21T00:00:00Z")

        val timeStop1 = "2021-11-22T00:00:00Z"
        val timeStop2 = "2021-11-23T00:00:00Z"
        val movementEvent2 = createMovementEvent(MovementStatus.STOP, timeStop2)

        val visit = createVisit(
            portAreas = listOf(createPortAreaVisit(portEvent)),
            esof = ESof(
                encounters = emptyList(),
                stops = listOf(
                    createStop(StopType.UNCLASSIFIED, timeStop1, null)
                ),
                slowMovingPeriods = null
            )
        )

        val statusBefore = VisitShipStatus(visit, null, null)
        val (status, changes, issues) = movementProcessor.processEvent(statusBefore, movementEvent2)

        assertEquals(statusBefore, status)
        assertEquals(listOf<Change>(), changes)
        assertEquals(
            listOf(
                EventProcessingIssue(
                    eventId = movementEvent2._id,
                    description = "Unexpected movement stop event: ship is currently already stopped. " +
                        "Event will be ignored (imo: 1111111)"
                )
            ),
            issues
        )
    }

    @Test
    fun `should ignore a MovementEvent with status START when status is already moving`() {
        val portEvent = createPortEventStart(PORT_NLRTM, "2021-11-21T00:00:00Z")

        val timeStart = "2021-11-23T00:00:00Z"
        val movementEvent = createMovementEvent(MovementStatus.START, timeStart)

        val visit = createVisit(
            portAreas = listOf(createPortAreaVisit(portEvent))
        )

        val statusBefore = VisitShipStatus(visit, null, null)
        val (status, changes, issues) = movementProcessor.processEvent(statusBefore, movementEvent)

        assertEquals(statusBefore, status)
        assertEquals(listOf<Change>(), changes)
        assertEquals(
            listOf(
                EventProcessingIssue(
                    eventId = movementEvent._id,
                    description = "Unexpected movement start event: ship does not have a stopped status right now. " +
                        "Event will be ignored (imo: 1111111)"
                )
            ),
            issues
        )
    }

    @Test
    fun `should ignore a MovementEvent with status STOP when current visit has too many Stops already`() {
        val portEvent = createPortEventStart(PORT_NLRTM, "2021-11-21T00:00:00Z")

        val time = "2021-11-23T00:00:00Z"
        val movementEvent = createMovementEvent(MovementStatus.STOP, time)

        val visit = createVisit(
            portAreas = listOf(createPortAreaVisit(portEvent)),
            esof = createESofWithManyStops()
        )
        val statusBefore = VisitShipStatus(visit, null, null)
        val (status, changes, issues) = movementProcessor.processEvent(statusBefore, movementEvent)

        assertEquals(statusBefore, status)
        assertEquals(listOf<Change>(), changes)
        assertEquals(
            listOf(
                EventProcessingIssue(
                    eventId = movementEvent._id,
                    description = "Cannot process MovementEvent: too many stops in current esof. " +
                        "Will ignore the event (imo: 1111111, stops: 100)"
                )
            ),
            issues
        )
    }

    @Test
    fun `should append event to esof of previous visit`() {
        val portEventStart = createPortEventStart(PORT_BEANR, "2021-11-21T00:00:00Z")
        val portEventEnd = createPortEventEnd(PORT_BEANR, "2021-11-23T00:00:00Z")

        val stop = createStop(StopType.UNCLASSIFIED, "2021-11-22T00:00:00Z", null)

        val previousVisit = createVisit(
            portAreas = listOf(createPortAreaVisit(portEventStart, portEventEnd)),
            esof = ESof(
                encounters = listOf(),
                stops = listOf(stop),
                slowMovingPeriods = null
            )
        )

        val voyage = createVoyage(
            startTime = portEventEnd.actualTime.atZone(ZoneOffset.UTC),
            startPortIds = listOf(BEANR)
        )

        val stopEventEnd = createMovementEvent(
            status = MovementStatus.START,
            time = "2021-11-24T00:00:00Z"
        )

        val statusBefore = VoyageShipStatus(voyage, previousVisit, null)

        val (status) = movementProcessor.processEvent(statusBefore, stopEventEnd)

        val statusAfter = VoyageShipStatus(
            voyage,
            previousVisit.copy(
                esof = ESof(
                    encounters = listOf(),
                    stops = listOf(
                        stop.copy(
                            endTime = stopEventEnd.actualTime.atZone(ZoneOffset.UTC),
                            endEventId = stopEventEnd._id,
                            endLocation = stopEventEnd.location.toVesselVoyageLocation(),
                            aisEnd = StopEndDetectionInfo(
                                id = stopEventEnd._id,
                                location = stopEventEnd.location.toVesselVoyageLocation(),
                                time = stopEventEnd.actualTime.atZone(ZoneOffset.UTC)
                            )
                        )
                    ),
                    slowMovingPeriods = null
                )
            ),
            null
        )

        assertEquals(statusAfter, status)
    }

    @Test
    fun `should ignore a MovementEvent with status STOP when current voyage has too many Stops already`() {
        val time = "2021-11-23T00:00:00Z"
        val movementEvent = createMovementEvent(MovementStatus.STOP, time)

        val voyage = createVoyage(
            startPortIds = listOf(NLRTM),
            startTime = movementEvent.actualTime.atZone(ZoneOffset.UTC),
            esof = createESofWithManyStops()
        )
        val statusBefore = VoyageShipStatus(voyage, null, null)
        val (status, changes, issues) = movementProcessor.processEvent(statusBefore, movementEvent)

        assertEquals(statusBefore, status)
        assertEquals(listOf<Change>(), changes)
        assertEquals(
            listOf(
                EventProcessingIssue(
                    eventId = movementEvent._id,
                    description = "Cannot process MovementEvent: too many stops in current esof. " +
                        "Will ignore the event (imo: 1111111, stops: 100)"
                )
            ),
            issues
        )
    }

    private fun createESofWithManyStops(): ESof {
        val timeStop = "2021-11-23T00:00:00Z"
        val timeStart = "2021-11-24T00:00:00Z"
        val movementEvent = createMovementEvent(MovementStatus.STOP, timeStop)

        return ESof(
            encounters = listOf(),
            stops = List(MAX_STOPS) {
                createStop(
                    type = StopType.UNCLASSIFIED,
                    startEventId = movementEvent._id,
                    startTime = timeStop,
                    endTime = timeStart
                )
            },
            slowMovingPeriods = null
        )
    }

    @Test
    fun `should set actual stop location when processing MovementEvent with status START`() {
        val portEvent = createPortEventStart(PORT_NLRTM, "2021-11-21T00:00:00Z")

        val timeStop = "2021-11-22T00:00:00Z"
        val timeStart = "2021-11-23T00:00:00Z"
        val movementEvent = createMovementEvent(MovementStatus.START, timeStart)

        val ongoingStop = createStop(StopType.UNCLASSIFIED, timeStop, null)

        val visit = createVisit(
            portAreas = listOf(createPortAreaVisit(portEvent)),
            esof = ESof(
                encounters = emptyList(),
                stops = listOf(ongoingStop),
                slowMovingPeriods = null
            )
        )

        val traceTime = Instant.parse(timeStop).atZone(ZoneOffset.UTC)
        val actualLocation = Location(2.0, 1.0)
        val traces = listOf(
            StopDetectionLocation(1.0, 1.0, traceTime, 0),
            StopDetectionLocation(2.0, 1.0, traceTime.plusHours(12), 0),
            StopDetectionLocation(3.0, 1.0, traceTime.plusHours(24), 0)
        )
        whenever(aisFetchingService.getTraceForStopDetection(any(), any())).thenReturn(traces)

        val finishedStop = ongoingStop.copy(
            endEventId = movementEvent._id,
            endTime = movementEvent.actualTime.atZone(ZoneOffset.UTC),
            endLocation = movementEvent.location.toVesselVoyageLocation(),
            actualLocation = actualLocation,
            actualTime = ZonedDateTime.parse("2021-11-22T12:00Z"),
            accuracy = 0.0f,
            aisEnd = StopEndDetectionInfo(
                id = movementEvent._id,
                location = movementEvent.location.toVesselVoyageLocation(),
                time = movementEvent.actualTime.atZone(ZoneOffset.UTC)
            )
        )
        val visitUpdated = visit.copy(
            esof = visit.esof?.copy(
                stops = listOf(finishedStop)
            )
        )

        val statusBefore = VisitShipStatus(visit, null, null)
        val (status, _) = movementProcessor.processEvent(statusBefore, movementEvent)

        assertEquals(VisitShipStatus(visitUpdated, null, null), status)
    }

    @Test
    fun `should set poma type to berth on stop when inside berth`() {
        val timeStopStart = "2021-11-22T00:00:00Z"
        val timeStopEnd = "2021-11-23T00:00:00Z"

        val stops = listOf(
            createStop(
                type = StopType.UNCLASSIFIED,
                startTime = timeStopStart,
                endTime = timeStopEnd,
                name = "TEST_BERTH"
            ),
        )
        val boundingBox = createBoundingBox(0.0, 0.0, 2.0, 2.0)
        val testBerth = createBerth(boundingBox.toList())
            .copy(nameLong = "")
        val berths = listOf(testBerth)

        val result = stops.fillPomaStop({ berths }, infraService::getAnchoragesByLocation)
        val expected = stops.map {
            it.copy(type = StopType.BERTH, pomaType = StopType.BERTH, name = "TEST_BERTH", pomaId = "TEST_BERTH_ID")
        }

        assertEquals(expected, result)
    }

    @Test
    fun `should not set type when outside berth`() {
        val timeStopStart = "2021-11-22T00:00:00Z"
        val timeStopEnd = "2021-11-23T00:00:00Z"

        val stops = listOf(createStop(StopType.ANCHOR_AREA, timeStopStart, timeStopEnd))
        val boundingBox = createBoundingBox(2.0, 2.0, 2.5, 2.5)
        val berths = listOf(createBerth(boundingBox.toList()))

        val result = stops.fillPomaStop({ berths }, infraService::getAnchoragesByLocation)

        assertEquals(stops, result)
    }

    @Test
    fun `should set type when on edge of berth`() {
        val timeStopStart = "2021-11-22T00:00:00Z"
        val timeStopEnd = "2021-11-23T00:00:00Z"

        val stops = listOf(
            createStop(
                type = StopType.ANCHOR_AREA,
                startTime = timeStopStart,
                endTime = timeStopEnd,
                name = "TEST_BERTH",
            ),
        )
        val boundingBox = createBoundingBox(1.0, 1.0, 2.5, 2.5)
        val berths = listOf(createBerth(boundingBox.toList()))

        val result = stops.fillPomaStop({ berths }, infraService::getAnchoragesByLocation)
        val expected = stops.map {
            it.copy(type = StopType.BERTH, pomaType = StopType.BERTH, name = "TEST_BERTH", pomaId = "TEST_BERTH_ID")
        }

        assertEquals(expected, result)
    }

    @Test
    fun `should set poma type on accurate location`() {
        val timeStopStart = "2021-11-22T00:00:00Z"
        val timeStopEnd = "2021-11-23T00:00:00Z"

        val stop = createStop(
            type = StopType.ANCHOR_AREA,
            startTime = timeStopStart,
            endTime = timeStopEnd,
            location = Location(1.1112, 1.1112),
            name = "TEST_BERTH"
        )
        val stops = listOf(stop)
        val boundingBox = createBoundingBox(1.111111, 1.111111, 2.0, 2.0)
        val berths = listOf(createBerth(boundingBox.toList()))

        val result = stops.fillPomaStop({ berths }, infraService::getAnchoragesByLocation)
        val expected = stops.map {
            it.copy(type = StopType.BERTH, pomaType = StopType.BERTH, name = "TEST_BERTH", pomaId = "TEST_BERTH_ID")
        }

        assertEquals(expected, result)
    }
}
