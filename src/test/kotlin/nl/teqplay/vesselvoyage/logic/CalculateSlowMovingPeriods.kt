package nl.teqplay.vesselvoyage.logic

import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.vesselvoyage.model.MINIMUM_SLOW_MOVEMENT_TIME
import nl.teqplay.vesselvoyage.util.createAisHistoricMessage
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.Instant
import java.time.ZoneOffset

class CalculateSlowMovingPeriods {

    @Test
    fun `detect slow moving periods`() {
        val start = Instant.now().toEpochMilli()
        val minute = MINIMUM_SLOW_MOVEMENT_TIME.toMillis() / 3
        val ais = createAisHistoricMessageTraces(
            start,
            minute,
            createAisHistoricMessage(speedOverGround = 10f),
            createAisHistoricMessage(speedOverGround = 10f),
            createAisHistoricMessage(speedOverGround = 10f),
            createAisHistoricMessage(speedOverGround = 4f),
            createAisHistoricMessage(speedOverGround = 4f),
            createAisHistoricMessage(speedOverGround = 4f),
            createAisHistoricMessage(speedOverGround = 4f),
            createAisHistoricMessage(speedOverGround = 4f),
            createAisHistoricMessage(speedOverGround = 4f),
            createAisHistoricMessage(speedOverGround = 7f),
            createAisHistoricMessage(speedOverGround = 7f),
            createAisHistoricMessage(speedOverGround = 7f),
            createAisHistoricMessage(speedOverGround = 7f),
            createAisHistoricMessage(speedOverGround = 7f),
            createAisHistoricMessage(speedOverGround = 4f),
            createAisHistoricMessage(speedOverGround = 4f),
            createAisHistoricMessage(speedOverGround = 4f),
            createAisHistoricMessage(speedOverGround = 4f),
            createAisHistoricMessage(speedOverGround = 4f),
            createAisHistoricMessage(speedOverGround = 4f)
        )
        val slowMovingPeriods = detectSlowMovingPeriods(ais)
        assertEquals(2, slowMovingPeriods.size)
        assertEquals(
            Instant.ofEpochMilli(start + 3 * minute).atZone(ZoneOffset.UTC),
            slowMovingPeriods[0].startTime
        )
        assertEquals(
            Instant.ofEpochMilli(start + 9 * minute).atZone(ZoneOffset.UTC),
            slowMovingPeriods[0].endTime
        )
        assertEquals(
            Instant.ofEpochMilli(start + 14 * minute).atZone(ZoneOffset.UTC),
            slowMovingPeriods[1].startTime
        )
        assertEquals(
            Instant.ofEpochMilli(start + 19 * minute).atZone(ZoneOffset.UTC),
            slowMovingPeriods[1].endTime
        )
    }

    @Test
    fun `detect slow moving periods ignore jitter`() {
        val start = Instant.now().toEpochMilli()
        val minute = MINIMUM_SLOW_MOVEMENT_TIME.toMillis() / 3
        val ais = createAisHistoricMessageTraces(
            start,
            minute,
            createAisHistoricMessage(speedOverGround = 10f),
            createAisHistoricMessage(speedOverGround = 4f),
            createAisHistoricMessage(speedOverGround = 10f),
            createAisHistoricMessage(speedOverGround = 10f),
            createAisHistoricMessage(speedOverGround = 10f),
            createAisHistoricMessage(speedOverGround = 4f),
            createAisHistoricMessage(speedOverGround = 4f),
            createAisHistoricMessage(speedOverGround = 10f),
            createAisHistoricMessage(speedOverGround = 4f),
            createAisHistoricMessage(speedOverGround = 4f),
        )
        val slowMovingPeriods = detectSlowMovingPeriods(ais)
        assertEquals(1, slowMovingPeriods.size)
        assertEquals(
            Instant.ofEpochMilli(start + 5 * minute).atZone(ZoneOffset.UTC),
            slowMovingPeriods[0].startTime
        )
        assertEquals(
            Instant.ofEpochMilli(start + 9 * minute).atZone(ZoneOffset.UTC),
            slowMovingPeriods[0].endTime
        )
    }

    private fun createAisHistoricMessageTraces(start: Long, diff: Long, vararg history: AisHistoricMessage) =
        history.mapIndexed { index, aisHistoricMessage ->
            aisHistoricMessage.copy(messageTime = Instant.ofEpochMilli(start + diff * index))
        }
}
