package nl.teqplay.vesselvoyage.logic

import com.nhaarman.mockitokotlin2.mock
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.Change
import nl.teqplay.vesselvoyage.model.ESof
import nl.teqplay.vesselvoyage.model.Encounter
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.EventStatus
import nl.teqplay.vesselvoyage.model.MAX_ENCOUNTERS
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.service.processing.encounter.EncounterProcessor
import nl.teqplay.vesselvoyage.util.toVesselVoyageLocation
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.ZoneOffset

class ProcessEncounterEventTest {
    private val encounterProcessor = EncounterProcessor(mock(), SimpleMeterRegistry())

    @Test
    fun `should create an e-sof entry when processing an encounter event start`() {
        val portEvent = createPortEventStart(PORT_NLRTM, "2021-11-21T00:00:00Z")

        val timeStart = "2021-11-22T00:00:00Z"
        val visit = createVisit(
            portAreas = listOf(createPortAreaVisit(portEvent))
        )
        val encounterEventStart = createEncounterEvent(EncounterType.TUG, EventStatus.START, MMSI_2.toString(), IMO_2.toString(), timeStart)

        val updatedVisit = visit.copy(
            esof = ESof(
                encounters = listOf(
                    Encounter(
                        type = encounterEventStart.encounterType,
                        otherImo = encounterEventStart.otherShip.imo.toString(),
                        otherMmsi = encounterEventStart.otherShip.mmsi.toString(),
                        startEventId = encounterEventStart._id,
                        startTime = encounterEventStart.actualTime.atZone(ZoneOffset.UTC),
                        startLocation = encounterEventStart.location.toVesselVoyageLocation(),
                        endEventId = null,
                        endTime = null,
                        endLocation = null,
                    )
                ),
                stops = emptyList(),
                slowMovingPeriods = null
            )
        )

        val statusBefore = VisitShipStatus(visit, null, null)
        val (status, changes) = encounterProcessor.processEvent(statusBefore, encounterEventStart)

        assertEquals(VisitShipStatus(updatedVisit, null, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, updatedVisit)
            ),
            changes
        )
    }

    @Test
    fun `should create an e-sof entry when processing an encounter event end`() {
        val portEvent = createPortEventStart(PORT_NLRTM, "2021-11-21T00:00:00Z")
        val encounterEventStart = createEncounterEvent(EncounterType.TUG, EventStatus.START, MMSI_2.toString(), IMO_2.toString(), "2021-11-22T00:00:00Z")
        val encounterEventEnd = createEncounterEvent(EncounterType.TUG, EventStatus.END, MMSI_2.toString(), IMO_2.toString(), "2021-11-23T00:00:00Z")

        val visit = createVisit(
            portAreas = listOf(createPortAreaVisit(portEvent)),
            esof = ESof(
                encounters = listOf(
                    Encounter(
                        type = encounterEventStart.encounterType,
                        otherImo = encounterEventStart.otherShip.imo?.toString(),
                        otherMmsi = encounterEventStart.otherShip.mmsi.toString(),
                        startEventId = encounterEventStart._id,
                        startTime = encounterEventStart.actualTime.atZone(ZoneOffset.UTC),
                        startLocation = encounterEventStart.location.toVesselVoyageLocation(),
                        endEventId = null,
                        endTime = null,
                        endLocation = null,
                    )
                ),
                stops = emptyList(),
                slowMovingPeriods = null
            )
        )

        val updatedVisit = visit.copy(
            esof = ESof(
                encounters = listOfNotNull(
                    visit.esof?.encounters?.first()?.copy(
                        endEventId = encounterEventEnd._id,
                        endTime = encounterEventEnd.actualTime.atZone(ZoneOffset.UTC),
                        endLocation = encounterEventEnd.location.toVesselVoyageLocation(),
                    )
                ),
                stops = emptyList(),
                slowMovingPeriods = null
            )
        )

        val statusBefore = VisitShipStatus(visit, null, null)
        val (status, changes) = encounterProcessor.processEvent(statusBefore, encounterEventEnd)

        assertEquals(VisitShipStatus(updatedVisit, null, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, updatedVisit)
            ),
            changes
        )
    }

    @Test
    fun `should ignore an encounter event end when there is no matching encounter start (different ship)`() {
        val portEvent = createPortEventStart(PORT_NLRTM, "2021-11-21T00:00:00Z")
        val encounterEventStart = createEncounterEvent(EncounterType.TUG, EventStatus.START, MMSI_2.toString(), IMO_2.toString(), "2021-11-22T00:00:00Z")
        val encounterEventEnd = createEncounterEvent(EncounterType.PILOT, EventStatus.END, MMSI_3.toString(), IMO_3.toString(), "2021-11-23T00:00:00Z")

        val visit = createVisit(
            portAreas = listOf(createPortAreaVisit(portEvent)),
            esof = ESof(
                encounters = listOf(
                    Encounter(
                        type = encounterEventStart.encounterType,
                        otherImo = encounterEventStart.otherShip.imo?.toString(),
                        otherMmsi = encounterEventStart.otherShip.mmsi.toString(),
                        startEventId = encounterEventStart._id,
                        startTime = encounterEventStart.actualTime.atZone(ZoneOffset.UTC),
                        startLocation = encounterEventStart.location.toVesselVoyageLocation(),
                        endEventId = null,
                        endTime = null,
                        endLocation = null,
                    )
                ),
                stops = emptyList(),
                slowMovingPeriods = null
            )
        )

        val statusBefore = VisitShipStatus(visit, null, null)
        val (status, changes, issues) = encounterProcessor.processEvent(statusBefore, encounterEventEnd)

        assertEquals(statusBefore, status)
        assertEquals(listOf<Change>(), changes)
        assertEquals(
            listOf(
                EventProcessingIssue(
                    eventId = encounterEventEnd._id,
                    description = "Unexpected encounter end event: no matching start event found " +
                        "(imo: 1111111, otherImo: 3333333, type: PILOT)"
                )
            ),
            issues
        )
    }

    @Test
    fun `should ignore an encounter event end when there is no matching encounter start (no encounter)`() {
        val portEvent = createPortEventStart(PORT_NLRTM, "2021-11-21T00:00:00Z")
        val encounterEventEnd = createEncounterEvent(EncounterType.PILOT, EventStatus.END, MMSI_3.toString(), IMO_3.toString(), "2021-11-23T00:00:00Z")

        val visit = createVisit(
            portAreas = listOf(createPortAreaVisit(portEvent)),
        )

        val statusBefore = VisitShipStatus(visit, null, null)
        val (status, changes, issues) = encounterProcessor.processEvent(statusBefore, encounterEventEnd)

        assertEquals(statusBefore, status)
        assertEquals(listOf<Change>(), changes)
        assertEquals(
            listOf(
                EventProcessingIssue(
                    eventId = encounterEventEnd._id,
                    description = "Unexpected encounter end event: no matching start event found " +
                        "(imo: 1111111, otherImo: 3333333, type: PILOT)"
                )
            ),
            issues
        )
    }

    @Test
    fun `should ignore an encounter event start when an encounter for the same otherShip is already ongoing`() {
        val portEvent = createPortEventStart(PORT_NLRTM, "2021-11-21T00:00:00Z")

        val encounterEventStart1 = createEncounterEvent(EncounterType.TUG, EventStatus.START, MMSI_2.toString(), IMO_2.toString(), "2021-11-22T00:00:00Z")
        val encounterEventStart2 = createEncounterEvent(EncounterType.TUG, EventStatus.START, MMSI_2.toString(), IMO_2.toString(), "2021-11-22T00:00:01Z")

        val visit = createVisit(
            portAreas = listOf(createPortAreaVisit(portEvent)),
            esof = ESof(
                encounters = listOf(
                    Encounter(
                        type = encounterEventStart1.encounterType,
                        otherImo = encounterEventStart1.otherShip.imo?.toString(),
                        otherMmsi = encounterEventStart1.otherShip.mmsi.toString(),
                        startEventId = encounterEventStart1._id,
                        startTime = encounterEventStart1.actualTime.atZone(ZoneOffset.UTC),
                        startLocation = encounterEventStart1.location.toVesselVoyageLocation(),
                        endEventId = null,
                        endTime = null,
                        endLocation = null,
                    )
                ),
                stops = emptyList(),
                slowMovingPeriods = null
            )
        )

        val statusBefore = VisitShipStatus(visit, null, null)
        val (status, changes, issues) = encounterProcessor.processEvent(statusBefore, encounterEventStart2)

        assertEquals(statusBefore, status)
        assertEquals(listOf<Change>(), changes)
        assertEquals(
            listOf(
                EventProcessingIssue(
                    eventId = encounterEventStart2._id,
                    description = "Cannot process EncounterEvent: an encounter with this ship is already ongoing. " +
                        "Will ignore the new event (imo: 1111111, otherImo: 2222222)"
                )
            ),
            issues
        )
    }

    @Test
    fun `should append event to esof of previous visit`() {
        val portEventStart = createPortEventStart(PORT_BEANR, "2021-11-21T00:00:00Z")
        val portEventEnd = createPortEventEnd(PORT_BEANR, "2021-11-23T00:00:00Z")

        val encounterStarted = createEncounter("2021-11-22T00:00:00Z", null)

        val previousVisit = createVisit(
            portAreas = listOf(createPortAreaVisit(portEventStart, portEventEnd)),
            esof = ESof(
                encounters = listOf(encounterStarted),
                stops = listOf(),
                slowMovingPeriods = null
            )
        )

        val voyage = createVoyage(
            startTime = portEventEnd.actualTime.atZone(ZoneOffset.UTC),
            startPortIds = listOf(BEANR)
        )

        val encounterEventEnd = createEncounterEvent(
            type = encounterStarted.type,
            status = EventStatus.END,
            otherMmsi = encounterStarted.otherMmsi,
            otherImo = encounterStarted.otherImo,
            time = "2021-11-24T00:00:00Z"
        )

        val statusBefore = VoyageShipStatus(voyage, previousVisit, null)

        val (status) = encounterProcessor.processEvent(statusBefore, encounterEventEnd)

        val statusAfter = VoyageShipStatus(
            voyage,
            previousVisit.copy(
                esof = ESof(
                    encounters = listOf(
                        encounterStarted.copy(
                            endTime = encounterEventEnd.actualTime.atZone(ZoneOffset.UTC),
                            endEventId = encounterEventEnd._id,
                            endLocation = encounterEventEnd.location.toVesselVoyageLocation()
                        )
                    ),
                    stops = listOf(),
                    slowMovingPeriods = null
                )
            ),
            null
        )

        assertEquals(statusAfter, status)
    }

    @Test
    fun `should protect against storing too many encounters in a single visit`() {
        val portEvent = createPortEventStart(PORT_NLRTM, "2021-11-21T00:00:00Z")

        val timeStart = "2021-11-22T00:00:00Z"
        val visit = createVisit(
            portAreas = listOf(createPortAreaVisit(portEvent)),
            esof = ESof(
                encounters = List(MAX_ENCOUNTERS) { createEncounter("2021-11-22T00:00:00Z", null) },
                stops = listOf(),
                slowMovingPeriods = null
            )
        )
        val encounterEventStart = createEncounterEvent(EncounterType.TUG, EventStatus.START, MMSI_2.toString(), IMO_2.toString(), timeStart)

        val statusBefore = VisitShipStatus(visit, null, null)
        val (status, changes, issues) = encounterProcessor.processEvent(statusBefore, encounterEventStart)

        assertEquals(statusBefore, status)
        assertEquals(listOf<Change>(), changes)
        assertEquals(
            listOf(
                EventProcessingIssue(
                    eventId = encounterEventStart._id,
                    description = "Cannot process EncounterEvent: too many encounters in current esof. " +
                        "Will ignore the event (imo: 1111111, encounters: 100)"
                )
            ),
            issues
        )
    }
}
