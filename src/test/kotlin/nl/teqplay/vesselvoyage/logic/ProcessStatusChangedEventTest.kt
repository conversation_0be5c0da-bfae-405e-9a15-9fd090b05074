package nl.teqplay.vesselvoyage.logic

import com.nhaarman.mockitokotlin2.mock
import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.Change
import nl.teqplay.vesselvoyage.model.ESof
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.InitialShipStatus
import nl.teqplay.vesselvoyage.model.StopType
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.service.processing.status.StatusChangedProcessor
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.ZoneOffset

class ProcessStatusChangedEventTest {
    private val statusChangedProcessor = StatusChangedProcessor(mock())

    @Test
    fun `should update a Stop to ANCHOR_AREA based on the status of the ship`() {
        val time = "2021-11-22T00:00:00Z"
        val visit = createVisitWithStop()
        val statusChangedEvent = createStatusChangedEvent(AisMessage.ShipStatus.AT_ANCHOR, time)

        val updatedStop = visit.esof?.stops?.first()?.copy(
            type = StopType.ANCHOR_AREA,
            aisType = StopType.ANCHOR_AREA
        )
        val updatedVisit = visit.copy(
            esof = visit.esof?.copy(stops = listOfNotNull(updatedStop))
        )

        val statusBefore = VisitShipStatus(visit, null, null)
        val (status, changes) = statusChangedProcessor.processEvent(statusBefore, statusChangedEvent)

        assertEquals(VisitShipStatus(updatedVisit, null, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, updatedVisit)
            ),
            changes
        )
    }

    @Test
    fun `should update a Stop to BERTH based on the status of the ship`() {
        val time = "2021-11-22T00:00:00Z"
        val visit = createVisitWithStop()
        val statusChangedEvent = createStatusChangedEvent(AisMessage.ShipStatus.MOORED, time)

        val updatedStop = visit.esof?.stops?.first()?.copy(
            type = StopType.BERTH,
            aisType = StopType.BERTH
        )
        val updatedVisit = visit.copy(
            esof = visit.esof?.copy(stops = listOfNotNull(updatedStop))
        )

        val statusBefore = VisitShipStatus(visit, null, null)
        val (status, changes) = statusChangedProcessor.processEvent(statusBefore, statusChangedEvent)

        assertEquals(VisitShipStatus(updatedVisit, null, null), status)
        assertEquals(
            listOf(
                Change(Action.UPDATE, updatedVisit)
            ),
            changes
        )
    }

    @Test
    fun `should ignore a StatusChangedEvent when there is no active visit or voyage`() {
        val time = "2021-11-22T00:00:00Z"
        val statusChangedEvent = createStatusChangedEvent(AisMessage.ShipStatus.MOORED, time)

        val (status, changes, issues) = statusChangedProcessor.processEvent(InitialShipStatus, statusChangedEvent)

        assertEquals(InitialShipStatus, status)
        assertEquals(listOf<Change>(), changes)
        assertEquals(listOf<EventProcessingIssue>(), issues)
    }

    @Test
    fun `should ignore a StatusChangedEvent having an other status`() {
        val time = "2021-11-22T00:00:00Z"
        val statusChangedEvent = createStatusChangedEvent(AisMessage.ShipStatus.ENGAGED_IN_FISHING, time)

        val (status, changes, issues) = statusChangedProcessor.processEvent(InitialShipStatus, statusChangedEvent)

        assertEquals(InitialShipStatus, status)
        assertEquals(listOf<Change>(), changes)
        assertEquals(listOf<EventProcessingIssue>(), issues)
    }

    @Test
    fun `should append event to esof of previous visit`() {
        val portEventStart = createPortEventStart(PORT_BEANR, "2021-11-21T00:00:00Z")
        val portEventEnd = createPortEventEnd(PORT_BEANR, "2021-11-23T00:00:00Z")

        val stop = createStop(StopType.UNCLASSIFIED, "2021-11-22T00:00:00Z", null, accuracy = 0.0f)

        val previousVisit = createVisit(
            portAreas = listOf(createPortAreaVisit(portEventStart, portEventEnd)),
            esof = ESof(
                encounters = listOf(),
                stops = listOf(stop),
                slowMovingPeriods = null
            )
        )

        val voyage = createVoyage(
            startTime = portEventEnd.actualTime.atZone(ZoneOffset.UTC),
            startPortIds = listOf(BEANR)
        )

        val statusChangedEvent = createStatusChangedEvent(AisMessage.ShipStatus.MOORED, "2021-11-24T00:00:00Z")

        val statusBefore = VoyageShipStatus(voyage, previousVisit, null)

        val (status) = statusChangedProcessor.processEvent(statusBefore, statusChangedEvent)

        val statusAfter = VoyageShipStatus(
            voyage,
            previousVisit.copy(
                esof = ESof(
                    encounters = listOf(),
                    stops = listOf(
                        stop.copy(
                            type = StopType.BERTH,
                            aisType = StopType.BERTH
                        )
                    ),
                    slowMovingPeriods = null
                )
            ),
            null
        )

        assertEquals(statusAfter, status)
    }

    private fun createVisitWithStop(): Visit {
        val portEvent = createPortEventStart(PORT_NLRTM, "2021-11-21T00:00:00Z")
        val time = "2021-11-22T00:00:00Z"
        val stop = createStop(
            type = StopType.UNCLASSIFIED,
            startTime = time,
            endTime = null,
            accuracy = 0.0f
        )

        return createVisit(
            portAreas = listOf(createPortAreaVisit(portEvent)),
            esof = ESof(
                encounters = emptyList(),
                stops = listOf(stop),
                slowMovingPeriods = null
            )
        )
    }
}
