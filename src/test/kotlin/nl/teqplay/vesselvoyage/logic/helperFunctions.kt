package nl.teqplay.vesselvoyage.logic

import com.mongodb.kotlin.client.FindIterable
import com.nhaarman.mockitokotlin2.mock
import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.event.interfaces.AnchoredEvent
import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType
import nl.teqplay.aisengine.event.interfaces.PredictedEvent
import nl.teqplay.aisengine.event.interfaces.UniqueBerthEvent
import nl.teqplay.aisengine.event.model.AisStatusChangedEvent
import nl.teqplay.aisengine.event.model.AnchoredEndEvent
import nl.teqplay.aisengine.event.model.AnchoredStartEvent
import nl.teqplay.aisengine.event.model.AreaEndEvent
import nl.teqplay.aisengine.event.model.AreaStartEvent
import nl.teqplay.aisengine.event.model.EncounterEndEvent
import nl.teqplay.aisengine.event.model.EncounterStartEvent
import nl.teqplay.aisengine.event.model.PortcallPilotBoardingEtaEvent
import nl.teqplay.aisengine.event.model.ShipMovingEndEvent
import nl.teqplay.aisengine.event.model.ShipMovingStartEvent
import nl.teqplay.aisengine.event.model.StopEndEvent
import nl.teqplay.aisengine.event.model.StopStartEvent
import nl.teqplay.aisengine.event.model.TrueDestinationChangedEvent
import nl.teqplay.aisengine.event.model.UniqueBerthEndEvent
import nl.teqplay.aisengine.event.model.UniqueBerthStartEvent
import nl.teqplay.aisengine.event.model.encountermetadata.STSEncounterMetadata
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.model.identifier.BerthIdentifier
import nl.teqplay.poma.api.v1.CargoCategoryType
import nl.teqplay.vesselvoyage.model.AnchorAreaVisit
import nl.teqplay.vesselvoyage.model.BerthAreaVisit
import nl.teqplay.vesselvoyage.model.Destination
import nl.teqplay.vesselvoyage.model.ESof
import nl.teqplay.vesselvoyage.model.Encounter
import nl.teqplay.vesselvoyage.model.Eta
import nl.teqplay.vesselvoyage.model.EventStatus
import nl.teqplay.vesselvoyage.model.Location
import nl.teqplay.vesselvoyage.model.MovementStatus
import nl.teqplay.vesselvoyage.model.PortAreaVisit
import nl.teqplay.vesselvoyage.model.Stop
import nl.teqplay.vesselvoyage.model.StopDetectionVersion
import nl.teqplay.vesselvoyage.model.StopEndDetectionInfo
import nl.teqplay.vesselvoyage.model.StopStartDetectionInfo
import nl.teqplay.vesselvoyage.model.StopType
import nl.teqplay.vesselvoyage.model.TimeWindow
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.model.Voyage
import nl.teqplay.vesselvoyage.model.createVisitId
import nl.teqplay.vesselvoyage.model.lightweight.poma.Anchorage
import nl.teqplay.vesselvoyage.model.lightweight.poma.ApproachArea
import nl.teqplay.vesselvoyage.model.lightweight.poma.Berth
import nl.teqplay.vesselvoyage.model.lightweight.poma.Lock
import nl.teqplay.vesselvoyage.model.lightweight.poma.PilotBoardingPlace
import nl.teqplay.vesselvoyage.model.lightweight.poma.Port
import nl.teqplay.vesselvoyage.model.lightweight.poma.ShipToShipArea
import nl.teqplay.vesselvoyage.model.lightweight.poma.Terminal
import nl.teqplay.vesselvoyage.util.toSkeletonLocation
import nl.teqplay.vesselvoyage.util.toVesselVoyageLocation
import java.time.Instant
import java.time.ZoneOffset
import java.time.ZonedDateTime
import nl.teqplay.platform.model.Location as PlatformLocation
import nl.teqplay.skeleton.model.Location as SkeletonLocation

// **** IMPORTANT: THESE HELPER FUNCTIONS ONLY HELP TO FILL IN DEFAULT VALUES  ****
// ****            THAT ARE NOT RELEVANT FOR A TEST, SO THE TEST IS MORE       ****
// ****            CONCISE TO WRITE. THESE HELPER FUNCTIONS MUST NOT CONTAIN   ****
// ****            LOGIC, ELSE WE CAN GET BUGS AND COMPLEXITIES HERE TOO       ****

val DEFAULT_START_TIME = Instant.ofEpochMilli(1688169600000).atZone(ZoneOffset.UTC)
val DEFAULT_ACTUAL_TIME = DEFAULT_START_TIME.plusDays(3)
val DEFAULT_END_TIME = DEFAULT_START_TIME.plusDays(7)

fun createPortEventStart(port: TestPort, time: String) =
    createAreaPortEvent(port, time = ZonedDateTime.parse(time), EventStatus.START)

fun createPortEventEnd(port: TestPort, time: String) =
    createAreaPortEvent(port, time = ZonedDateTime.parse(time), EventStatus.END)

fun createPortEventStart(port: TestPort, time: ZonedDateTime) =
    createAreaPortEvent(port, time, EventStatus.START)

fun createPortEventEnd(port: TestPort, time: ZonedDateTime) =
    createAreaPortEvent(port, time, EventStatus.END)

fun createAreaPortEvent(
    port: TestPort,
    time: ZonedDateTime,
    status: EventStatus
) = when (status) {
    EventStatus.START -> AreaStartEvent(
        _id = generateUniqueId(),
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        createdTime = time.toInstant(),
        actualTime = time.toInstant(),
        area = AreaIdentifier(port.portId, type = AreaIdentifier.AreaType.PORT, unlocode = port.portId),
        location = port.location.toSkeletonLocation(),
        draught = 1.0F,
        berth = null,
        heading = null,
        speedOverGround = null,
    )
    EventStatus.END -> AreaEndEvent(
        _id = generateUniqueId(),
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        createdTime = time.toInstant(),
        actualTime = time.toInstant(),
        area = AreaIdentifier(port.portId, type = AreaIdentifier.AreaType.PORT, unlocode = port.portId),
        location = port.location.toSkeletonLocation(),
        draught = 1.0F,
        berth = null,
        heading = null,
        startEventId = null,
        speedOverGround = null,
    )
}

fun createAreaPortEvent(
    eventId: String,
    status: EventStatus,
    time: ZonedDateTime,
    port: TestPort = PORT_NLRTM
) = when (status) {
    EventStatus.START -> AreaStartEvent(
        _id = eventId,
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        createdTime = time.toInstant(),
        actualTime = time.toInstant(),
        area = AreaIdentifier(port.portId, type = AreaIdentifier.AreaType.PORT, unlocode = port.portId),
        location = port.location.toSkeletonLocation(),
        draught = 1.0F,
        berth = null,
        heading = null,
        speedOverGround = null,
    )
    EventStatus.END -> AreaEndEvent(
        _id = eventId,
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        createdTime = time.toInstant(),
        actualTime = time.toInstant(),
        area = AreaIdentifier(port.portId, type = AreaIdentifier.AreaType.PORT, unlocode = port.portId),
        location = port.location.toSkeletonLocation(),
        draught = 1.0F,
        berth = null,
        heading = null,
        startEventId = null,
        speedOverGround = null,
    )
}

fun createEndOfSeaPassageEvent(
    _id: String,
    status: EventStatus,
    time: ZonedDateTime,
    port: TestPort = PORT_NLRTM,
    areaId: String = "EOSP_TEST_AREA_ID"
) = when (status) {
    EventStatus.START -> AreaStartEvent(
        _id = _id,
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        createdTime = time.toInstant(),
        actualTime = time.toInstant(),
        area = AreaIdentifier(areaId, type = AreaIdentifier.AreaType.END_OF_SEA_PASSAGE, unlocode = port.portId),
        location = port.location.toSkeletonLocation(),
        draught = 1.0F,
        berth = null,
        heading = null,
        speedOverGround = null,
    )
    EventStatus.END -> AreaEndEvent(
        _id = _id,
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        createdTime = time.toInstant(),
        actualTime = time.toInstant(),
        area = AreaIdentifier(areaId, type = AreaIdentifier.AreaType.END_OF_SEA_PASSAGE, unlocode = port.portId),
        location = port.location.toSkeletonLocation(),
        draught = 1.0F,
        berth = null,
        heading = null,
        startEventId = null,
        speedOverGround = null,
    )
}

fun createStopEvent(
    _id: String,
    status: EventStatus,
    time: ZonedDateTime,
    location: SkeletonLocation,
    startEventId: String? = null,
    actualLocation: SkeletonLocation = SkeletonLocation(0.0, 0.0)
) = when (status) {
    EventStatus.START -> StopStartEvent(
        _id = _id,
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        location = location,
        actualTime = time.toInstant(),
        createdTime = time.toInstant(),
    )

    EventStatus.END -> StopEndEvent(
        _id = _id,
        startEventId = startEventId,
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        location = location,
        actualTime = time.toInstant(),
        createdTime = time.toInstant(),
        stopLocation = actualLocation
    )
}

fun createVisit(
    _id: String? = null,
    anchorAreas: List<AnchorAreaVisit> = emptyList(),
    portAreas: List<PortAreaVisit> = emptyList(),
    berthAreas: List<BerthAreaVisit> = emptyList(),
    dest: Destination? = null,
    eta: Eta? = null,
    esof: ESof? = null,
    passThroughAreas: List<PortAreaVisit>? = null,
    finished: Boolean? = null,
    previousEntryId: String? = null,
    nextEntryId: String? = null,
    imo: String = IMO_1.toString()
) = Visit(
    _id = _id ?: createVisitId(generateUniqueId()),
    mmsi = MMSI_1.toString(),
    imo = imo,
    anchorAreas = anchorAreas,
    portAreas = portAreas,
    berthAreas = berthAreas,
    dest = dest,
    eta = eta,
    esof = esof,
    passThroughAreas = passThroughAreas,
    finished = finished ?: false,
    previousEntryId = previousEntryId,
    nextEntryId = nextEntryId
)

fun createPortAreaVisit(
    startEvent: AreaEvent,
    endEvent: AreaEvent? = null
) = PortAreaVisit(
    startEventId = startEvent._id,
    portId = startEvent.area.id ?: "Unkown",
    startTime = startEvent.actualTime.atZone(ZoneOffset.UTC),
    startLocation = startEvent.location.toVesselVoyageLocation(),
    startDraught = startEvent.draught?.toDouble(),
    endEventId = endEvent?._id,
    endTime = endEvent?.actualTime?.atZone(ZoneOffset.UTC),
    endLocation = endEvent?.location?.toVesselVoyageLocation(),
    endDraught = endEvent?.draught?.toDouble()
)

fun createPortAreaVisit(
    port: TestPort,
    startTime: String,
    endTime: String? = null
) = createPortAreaVisit(port, ZonedDateTime.parse(startTime), endTime?.let { ZonedDateTime.parse(it) })

fun createPortAreaVisit(
    port: TestPort,
    startTime: ZonedDateTime,
    endTime: ZonedDateTime? = null
): PortAreaVisit {
    val startEvent = createPortEventStart(port, startTime)
    val endEvent = endTime?.let { createPortEventStart(port, endTime) }

    return PortAreaVisit(
        startEventId = startEvent._id,
        portId = startEvent.area.id ?: "Unkown",
        startTime = startEvent.actualTime.atZone(ZoneOffset.UTC),
        startLocation = startEvent.location.toVesselVoyageLocation(),
        startDraught = startEvent.draught?.toDouble(),
        endEventId = endEvent?._id,
        endTime = endEvent?.actualTime?.atZone(ZoneOffset.UTC),
        endLocation = endEvent?.location?.toVesselVoyageLocation(),
        endDraught = endEvent?.draught?.toDouble()
    )
}

fun createAnchorAreaVisit(
    anchorArea: TestAnchorArea,
    startTime: ZonedDateTime,
    endTime: ZonedDateTime? = null
) = AnchorAreaVisit(
    anchorAreaId = anchorArea.anchorAreaId,
    destinations = anchorArea.destinations,
    startEventId = generateUniqueId(),
    startTime = startTime,
    endEventId = if (endTime != null) generateUniqueId() else null,
    endTime = endTime
)

fun createAnchorAreaVisit(
    startEvent: AnchoredEvent,
    endEvent: AnchoredEvent? = null,
    destinations: Set<String>? = null
) = AnchorAreaVisit(
    anchorAreaId = startEvent.area.id ?: throw Exception("No anchor area id"),
    destinations = destinations ?: emptySet(),
    startEventId = startEvent._id,
    startTime = startEvent.actualTime.atZone(ZoneOffset.UTC),
    endEventId = endEvent?._id,
    endTime = endEvent?.actualTime?.atZone(ZoneOffset.UTC),
)

fun createBerthAreaVisit(
    startEvent: UniqueBerthEvent,
    endEvent: UniqueBerthEvent? = null
) = BerthAreaVisit(
    startEventId = startEvent._id,
    portId = startEvent.area.unlocode ?: "Unknown",
    berthId = startEvent.area.id ?: "Unknown",
    startTime = startEvent.actualTime.atZone(ZoneOffset.UTC),
    startLocation = startEvent.location.toVesselVoyageLocation(),
    startDraught = startEvent.draught?.toDouble(),
    endEventId = endEvent?._id,
    endTime = endEvent?.actualTime?.atZone(ZoneOffset.UTC),
    endLocation = endEvent?.location?.toVesselVoyageLocation(),
    endDraught = endEvent?.draught?.toDouble()
)

fun createVoyage(
    _id: String? = null,

    startPortIds: List<String>,
    startTime: ZonedDateTime,

    dest: Destination? = null,
    eta: Eta? = null,
    esof: ESof? = null,
    nonMatchingAnchorAreas: List<AnchorAreaVisit>? = null,
    passThroughAreas: List<PortAreaVisit>? = null,

    endPortIds: List<String>? = null,
    endTime: ZonedDateTime? = null,

    finished: Boolean? = null,
    previousEntryId: String? = null,
    nextEntryId: String? = null
) = Voyage(
    _id = _id ?: generateUniqueId(),
    mmsi = MMSI_1.toString(),
    imo = IMO_1.toString(),

    startPortIds = startPortIds,
    startTime = startTime,

    dest = dest,
    eta = eta,
    esof = esof,
    nonMatchingAnchorAreas = nonMatchingAnchorAreas,
    passThroughAreas = passThroughAreas,
    finished = finished ?: false,

    endPortIds = endPortIds,
    endTime = endTime,

    previousEntryId = previousEntryId,
    nextEntryId = nextEntryId
)

fun createVoyageFromVisitToVisit(origin: Visit, destination: Visit): Voyage {
    return createVoyage(
        _id = destination.previousEntryId,
        startPortIds = origin.portAreas.map { it.portId },
        startTime = origin.endTime!!,
        endPortIds = destination.portAreas.map { it.portId },
        endTime = destination.startTime
    )
}

fun createAnchorEventStart(anchorArea: TestAnchorArea, time: String, areaId: String? = null) =
    createAnchorEvent(anchorArea, time, EventStatus.START, areaId)

fun createAnchorEventEnd(anchorArea: TestAnchorArea, time: String, areaId: String? = null) =
    createAnchorEvent(anchorArea, time, EventStatus.END, areaId)

fun createAnchorEvent(
    anchorArea: TestAnchorArea,
    time: String,
    status: EventStatus,
    areaId: String? = null
) = createAnchorEvent(anchorArea, ZonedDateTime.parse(time), status, areaId)

fun createAnchorEvent(
    anchorArea: TestAnchorArea,
    time: ZonedDateTime,
    status: EventStatus,
    areaId: String? = null
) =
    when (status) {
        EventStatus.START -> AnchoredStartEvent(
            _id = generateUniqueId(),
            ship = AisShipIdentifier(MMSI_1, IMO_1),
            actualTime = time.toInstant(),
            createdTime = time.toInstant(),
            area = AreaIdentifier(areaId, type = AreaIdentifier.AreaType.ANCHOR, anchorArea.anchorAreaId),
            location = anchorArea.location
        )
        EventStatus.END -> AnchoredEndEvent(
            _id = generateUniqueId(),
            ship = AisShipIdentifier(MMSI_1, IMO_1),
            actualTime = time.toInstant(),
            createdTime = time.toInstant(),
            area = AreaIdentifier(areaId, type = AreaIdentifier.AreaType.ANCHOR, anchorArea.anchorAreaId),
            location = anchorArea.location,
            startEventId = null,
        )
    }

fun createAnchorEvent(
    eventId: String,
    status: EventStatus,
    time: ZonedDateTime,
    areaId: String = ANCHOR_AREA_4EAST,
    areaLocation: Location = PORT_NLRTM.location
) =
    when (status) {
        EventStatus.START -> AnchoredStartEvent(
            _id = eventId,
            ship = AisShipIdentifier(MMSI_1, IMO_1),
            actualTime = time.toInstant(),
            createdTime = time.toInstant(),
            area = AreaIdentifier(areaId, type = AreaIdentifier.AreaType.ANCHOR, areaId),
            location = areaLocation.toSkeletonLocation()
        )
        EventStatus.END -> AnchoredEndEvent(
            _id = eventId,
            ship = AisShipIdentifier(MMSI_1, IMO_1),
            actualTime = time.toInstant(),
            createdTime = time.toInstant(),
            area = AreaIdentifier(areaId, type = AreaIdentifier.AreaType.ANCHOR, areaId),
            location = areaLocation.toSkeletonLocation(),
            startEventId = null,
        )
    }

fun createPilotAreaEvent(
    eventId: String,
    status: EventStatus,
    time: ZonedDateTime,
    areaId: String = "TEST_AREA_ID",
    areaLocation: Location = PORT_NLRTM.location
) = when (status) {
    EventStatus.START -> AreaStartEvent(
        _id = eventId,
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        createdTime = time.toInstant(),
        actualTime = time.toInstant(),
        area = AreaIdentifier(areaId, type = AreaIdentifier.AreaType.PILOT_BOARDING_PLACE, areaId),
        location = areaLocation.toSkeletonLocation(),
        draught = 1.0F,
        berth = null,
        heading = null,
        speedOverGround = null,
    )
    EventStatus.END -> AreaEndEvent(
        _id = eventId,
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        createdTime = time.toInstant(),
        actualTime = time.toInstant(),
        area = AreaIdentifier(areaId, type = AreaIdentifier.AreaType.PILOT_BOARDING_PLACE, areaId),
        location = areaLocation.toSkeletonLocation(),
        draught = 1.0F,
        berth = null,
        heading = null,
        startEventId = null,
        speedOverGround = null,
    )
}

fun createAnchorAreaEvent(
    eventId: String,
    status: EventStatus,
    time: ZonedDateTime,
    areaId: String = "TEST_AREA_ID",
    areaLocation: Location = PORT_NLRTM.location
) = when (status) {
    EventStatus.START -> AreaStartEvent(
        _id = eventId,
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        createdTime = time.toInstant(),
        actualTime = time.toInstant(),
        area = AreaIdentifier(areaId, type = AreaIdentifier.AreaType.ANCHOR, areaId),
        location = areaLocation.toSkeletonLocation(),
        draught = 1.0F,
        berth = null,
        heading = null,
        speedOverGround = null,
    )

    EventStatus.END -> AreaEndEvent(
        _id = eventId,
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        createdTime = time.toInstant(),
        actualTime = time.toInstant(),
        area = AreaIdentifier(areaId, type = AreaIdentifier.AreaType.ANCHOR, areaId),
        location = areaLocation.toSkeletonLocation(),
        draught = 1.0F,
        berth = null,
        heading = null,
        startEventId = null,
        speedOverGround = null,
    )
}

fun createTerminalMooringAreaEvent(
    eventId: String,
    status: EventStatus,
    time: ZonedDateTime,
    areaId: String = "TEST_AREA_ID",
    areaLocation: Location = PORT_NLRTM.location
) = when (status) {
    EventStatus.START -> AreaStartEvent(
        _id = eventId,
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        createdTime = time.toInstant(),
        actualTime = time.toInstant(),
        area = AreaIdentifier(areaId, type = AreaIdentifier.AreaType.TERMINAL_MOORING_AREA, areaId),
        location = areaLocation.toSkeletonLocation(),
        draught = 1.0F,
        berth = null,
        heading = null,
        speedOverGround = null,
    )

    EventStatus.END -> AreaEndEvent(
        _id = eventId,
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        createdTime = time.toInstant(),
        actualTime = time.toInstant(),
        area = AreaIdentifier(areaId, type = AreaIdentifier.AreaType.TERMINAL_MOORING_AREA, areaId),
        location = areaLocation.toSkeletonLocation(),
        draught = 1.0F,
        berth = null,
        heading = null,
        startEventId = null,
        speedOverGround = null,
    )
}

fun createLockAreaEvent(
    eventId: String,
    status: EventStatus,
    time: ZonedDateTime,
    areaId: String = "TEST_AREA_ID",
    areaLocation: Location = PORT_NLRTM.location
) = when (status) {
    EventStatus.START -> AreaStartEvent(
        _id = eventId,
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        createdTime = time.toInstant(),
        actualTime = time.toInstant(),
        area = AreaIdentifier(areaId, type = AreaIdentifier.AreaType.LOCK, areaId),
        location = areaLocation.toSkeletonLocation(),
        draught = 1.0F,
        berth = null,
        heading = null,
        speedOverGround = null,
    )

    EventStatus.END -> AreaEndEvent(
        _id = eventId,
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        createdTime = time.toInstant(),
        actualTime = time.toInstant(),
        area = AreaIdentifier(areaId, type = AreaIdentifier.AreaType.LOCK, areaId),
        location = areaLocation.toSkeletonLocation(),
        draught = 1.0F,
        berth = null,
        heading = null,
        startEventId = null,
        speedOverGround = null,
    )
}

fun createApproachAreaEvent(
    eventId: String,
    status: EventStatus,
    time: ZonedDateTime,
    areaId: String = "TEST_AREA_ID",
    areaLocation: Location = PORT_NLRTM.location
) = when (status) {
    EventStatus.START -> AreaStartEvent(
        _id = eventId,
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        createdTime = time.toInstant(),
        actualTime = time.toInstant(),
        area = AreaIdentifier(areaId, type = AreaIdentifier.AreaType.APPROACH_POINT, areaId),
        location = areaLocation.toSkeletonLocation(),
        draught = 1.0F,
        berth = null,
        heading = null,
        speedOverGround = null,
    )

    EventStatus.END -> AreaEndEvent(
        _id = eventId,
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        createdTime = time.toInstant(),
        actualTime = time.toInstant(),
        area = AreaIdentifier(areaId, type = AreaIdentifier.AreaType.APPROACH_POINT, areaId),
        location = areaLocation.toSkeletonLocation(),
        draught = 1.0F,
        berth = null,
        heading = null,
        startEventId = null,
        speedOverGround = null,
    )
}

fun createDestinationChangedEvent(
    time: String,
    newTrueDestination: String?,
    oldDestination: String? = null,
    newDestination: String? = null
) = TrueDestinationChangedEvent(
    _id = generateUniqueId(),
    ship = AisShipIdentifier(MMSI_1, IMO_1),
    createdTime = ZonedDateTime.parse(time).toInstant(),
    actualTime = ZonedDateTime.parse(time).toInstant(),
    newValue = newDestination,
    oldValue = oldDestination,
    trueDestination = newTrueDestination,
    location = nl.teqplay.skeleton.model.Location(0.0, 0.0),
)

fun createDestinationChangedEvent(
    time: ZonedDateTime,
    newTrueDestination: String?,
    oldDestination: String? = null,
    newDestination: String? = null
) = TrueDestinationChangedEvent(
    _id = generateUniqueId(),
    ship = AisShipIdentifier(MMSI_1, IMO_1),
    createdTime = time.toInstant(),
    actualTime = time.toInstant(),
    newValue = newDestination,
    oldValue = oldDestination,
    trueDestination = newTrueDestination,
    location = nl.teqplay.skeleton.model.Location(0.0, 0.0),
)

fun createEtaEvent(
    port: TestPort,
    time: String,
    predictedTime: String
) = PortcallPilotBoardingEtaEvent(
    _id = generateUniqueId(),
    ship = AisShipIdentifier(MMSI_1, IMO_1),
    createdTime = ZonedDateTime.parse(time).toInstant(),
    predictedTime = ZonedDateTime.parse(predictedTime).toInstant(),
    area = AreaIdentifier(port.portId, type = AreaIdentifier.AreaType.PILOT_BOARDING_PLACE, unlocode = port.portId),
    source = "UNIT_TEST",
    portcallId = "TEST_PORTCALL_ID"
)

fun createEtaEvent(
    port: TestPort,
    time: ZonedDateTime,
    predictedTime: ZonedDateTime
) = PortcallPilotBoardingEtaEvent(
    _id = generateUniqueId(),
    ship = AisShipIdentifier(MMSI_1, IMO_1),
    createdTime = time.toInstant(),
    predictedTime = predictedTime.toInstant(),
    area = AreaIdentifier(port.portId, AreaIdentifier.AreaType.PILOT_BOARDING_PLACE, unlocode = port.portId),
    source = "UNIT_TEST",
    portcallId = "TEST_PORTCALL_ID"
) as PredictedEvent

fun createUniqueBerthEvent(
    status: EventStatus,
    berthId: String,
    time: String,
    port: TestPort = PORT_NLRTM
) = when (status) {
    EventStatus.START -> UniqueBerthStartEvent(
        _id = generateUniqueId(),
        berthEventId = generateUniqueId(),
        berthConfirmedEventId = generateUniqueId(),
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        area = AreaIdentifier(id = berthId, type = AreaIdentifier.AreaType.BERTH, unlocode = port.portId),
        createdTime = ZonedDateTime.parse(time).toInstant(),
        actualTime = ZonedDateTime.parse(time).minusMinutes(15).toInstant(),
        location = port.location.toSkeletonLocation(),
        berth = BerthIdentifier(null, null),
        draught = 4.5F,
        heading = null
    )
    EventStatus.END -> UniqueBerthEndEvent(
        _id = generateUniqueId(),
        berthEventId = generateUniqueId(),
        berthConfirmedEventId = generateUniqueId(),
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        area = AreaIdentifier(id = berthId, type = AreaIdentifier.AreaType.BERTH, unlocode = port.portId),
        createdTime = ZonedDateTime.parse(time).toInstant(),
        actualTime = ZonedDateTime.parse(time).minusMinutes(15).toInstant(),
        location = port.location.toSkeletonLocation(),
        berth = BerthIdentifier(null, null),
        draught = 4.5F,
        heading = null,
        startEventId = null,
    )
}

fun createUniqueBerthEvent(
    eventId: String,
    status: EventStatus,
    time: ZonedDateTime,
    areaId: String = "TEST_BERTH",
    port: TestPort = PORT_NLRTM
) = when (status) {
    EventStatus.START -> UniqueBerthStartEvent(
        _id = eventId,
        berthEventId = generateUniqueId(),
        berthConfirmedEventId = generateUniqueId(),
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        area = AreaIdentifier(areaId, AreaIdentifier.AreaType.BERTH, unlocode = port.portId),
        createdTime = time.toInstant(),
        actualTime = time.toInstant(),
        location = port.location.toSkeletonLocation(),
        berth = BerthIdentifier(null, null),
        draught = 4.5F,
        heading = null
    )
    EventStatus.END -> UniqueBerthEndEvent(
        _id = eventId,
        berthEventId = generateUniqueId(),
        berthConfirmedEventId = generateUniqueId(),
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        area = AreaIdentifier(areaId, AreaIdentifier.AreaType.BERTH, unlocode = port.portId),
        createdTime = time.toInstant(),
        actualTime = time.toInstant(),
        location = port.location.toSkeletonLocation(),
        berth = BerthIdentifier(null, null),
        draught = 4.5F,
        heading = null,
        startEventId = null,
    )
}

fun createUniqueBerthEvent(
    status: EventStatus,
    berthId: String,
    time: String,
    originalTime: String,
    location: Location,
    portId: String
) = when (status) {
    EventStatus.START -> UniqueBerthStartEvent(
        _id = generateUniqueId(),
        berthEventId = generateUniqueId(),
        berthConfirmedEventId = generateUniqueId(),
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        area = AreaIdentifier(berthId, AreaIdentifier.AreaType.BERTH, unlocode = portId),
        createdTime = ZonedDateTime.parse(time).toInstant(),
        actualTime = ZonedDateTime.parse(originalTime).toInstant(),
        location = location.toSkeletonLocation(),
        berth = BerthIdentifier(null, null),
        draught = 4.5F,
        heading = null
    )

    EventStatus.END -> UniqueBerthEndEvent(
        _id = generateUniqueId(),
        berthEventId = generateUniqueId(),
        berthConfirmedEventId = generateUniqueId(),
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        area = AreaIdentifier(berthId, AreaIdentifier.AreaType.BERTH, unlocode = portId),
        createdTime = ZonedDateTime.parse(time).toInstant(),
        actualTime = ZonedDateTime.parse(originalTime).toInstant(),
        location = location.toSkeletonLocation(),
        berth = BerthIdentifier(null, null),
        draught = 4.5F,
        heading = null,
        startEventId = null,
    )
}

fun createDestination(destination: String, updatedAt: ZonedDateTime) = Destination(
    updatedAt = updatedAt,
    aisDestination = destination,
    trueDestination = destination
)

fun createMovementEvent(
    status: MovementStatus,
    time: String,
    location: Location = PORT_NLRTM.location,
    id: String? = null
) = when (status) {
    MovementStatus.START -> ShipMovingStartEvent(
        _id = id ?: generateUniqueId(),
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        location = location.toSkeletonLocation(),
        createdTime = ZonedDateTime.parse(time).toInstant(),
        actualTime = ZonedDateTime.parse(time).toInstant(),
    )
    MovementStatus.STOP -> ShipMovingEndEvent(
        _id = id ?: generateUniqueId(),
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        location = location.toSkeletonLocation(),
        createdTime = ZonedDateTime.parse(time).toInstant(),
        actualTime = ZonedDateTime.parse(time).toInstant(),
        startEventId = null,
    )
}

fun createMovementEvent(
    status: MovementStatus,
    time: ZonedDateTime,
    location: Location = PORT_NLRTM.location,
    id: String? = null
) = when (status) {
    MovementStatus.START -> ShipMovingStartEvent(
        _id = id ?: generateUniqueId(),
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        location = location.toSkeletonLocation(),
        createdTime = time.toInstant(),
        actualTime = time.toInstant(),
    )
    MovementStatus.STOP -> ShipMovingEndEvent(
        _id = id ?: generateUniqueId(),
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        location = location.toSkeletonLocation(),
        createdTime = time.toInstant(),
        actualTime = time.toInstant(),
        startEventId = null,
    )
}

fun createStatusChangedEvent(
    newStatus: AisMessage.ShipStatus,
    time: String
) = AisStatusChangedEvent(
    _id = generateUniqueId(),
    ship = AisShipIdentifier(MMSI_1, IMO_1),
    location = nl.teqplay.skeleton.model.Location(0.0, 0.0),
    oldValue = AisMessage.ShipStatus.UNDEFINED,
    newValue = newStatus,
    actualTime = ZonedDateTime.parse(time).toInstant(),
    createdTime = ZonedDateTime.parse(time).toInstant()
)

fun createStatusChangedEvent(
    newStatus: AisMessage.ShipStatus,
    time: ZonedDateTime
) = AisStatusChangedEvent(
    _id = generateUniqueId(),
    ship = AisShipIdentifier(MMSI_1, IMO_1),
    location = nl.teqplay.skeleton.model.Location(0.0, 0.0),
    oldValue = AisMessage.ShipStatus.UNDEFINED,
    newValue = newStatus,
    actualTime = time.toInstant(),
    createdTime = time.toInstant()
)

fun createEncounterEvent(
    type: EncounterType,
    status: EventStatus,
    otherMmsi: String,
    otherImo: String?,
    time: String,
    relatedEvent: String? = null
) = when (status) {
    EventStatus.START -> EncounterStartEvent(
        _id = generateUniqueId(),
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        otherShip = AisShipIdentifier(otherMmsi.toInt(), otherImo?.toInt()),
        encounterType = type,
        location = SkeletonLocation(0.0, 0.0),
        createdTime = ZonedDateTime.parse(time).toInstant(),
        actualTime = ZonedDateTime.parse(time).toInstant(),
        probability = 0.0
    )
    EventStatus.END -> EncounterEndEvent(
        _id = generateUniqueId(),
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        otherShip = AisShipIdentifier(otherMmsi.toInt(), otherImo?.toInt()),
        encounterType = type,
        location = SkeletonLocation(0.0, 0.0),
        createdTime = ZonedDateTime.parse(time).toInstant(),
        actualTime = ZonedDateTime.parse(time).toInstant(),
        probability = 0.0,
        startEventId = relatedEvent,
    )
}

fun createEncounterEvent(
    eventId: String,
    status: EventStatus,
    time: ZonedDateTime,
    type: EncounterType,
    otherMmsi: String,
    otherImo: String?,
    relatedEvent: String? = null,
    location: Location = PORT_NLRTM.location,
    mmsi: Int = MMSI_1,
    imo: Int = IMO_1
) = when (status) {
    EventStatus.START -> EncounterStartEvent(
        _id = eventId,
        ship = AisShipIdentifier(mmsi, imo),
        otherShip = AisShipIdentifier(otherMmsi.toInt(), otherImo?.toInt()),
        encounterType = type,
        location = location.toSkeletonLocation(),
        createdTime = time.toInstant(),
        actualTime = time.toInstant(),
        probability = 0.0
    )
    EventStatus.END -> EncounterEndEvent(
        _id = eventId,
        ship = AisShipIdentifier(mmsi, imo),
        otherShip = AisShipIdentifier(otherMmsi.toInt(), otherImo?.toInt()),
        encounterType = type,
        location = location.toSkeletonLocation(),
        createdTime = time.toInstant(),
        actualTime = time.toInstant(),
        probability = 0.0,
        startEventId = relatedEvent,
    )
}

fun createEncounter(
    startTime: String,
    endTime: String?,
    type: EncounterType = EncounterType.TUG,
    location: Location = PORT_NLRTM.location
) = Encounter(
    type = type,
    otherMmsi = MMSI_2.toString(),
    otherImo = IMO_2.toString(),

    startEventId = generateUniqueId(),
    startTime = ZonedDateTime.parse(startTime),
    startLocation = location,

    endEventId = endTime?.let { generateUniqueId() },
    endTime = endTime?.let { ZonedDateTime.parse(it) },
    endLocation = endTime?.let { location }
)

fun createStop(
    type: StopType,
    startTime: String,
    endTime: String? = null,
    startEventId: String = generateUniqueId(),
    endEventId: String? = endTime?.let { generateUniqueId() },
    location: Location = PORT_NLRTM.location,
    endLocation: Location? = null,
    actualLocation: Location? = null,
    actualTime: String? = null,
    detectionVersion: StopDetectionVersion = StopDetectionVersion.MOVEMENT_EVENT,
    name: String? = null,
    accuracy: Float? = null
) = createStop(
    type = type,
    startEventId = startEventId,
    startTime = ZonedDateTime.parse(startTime),
    endEventId = endEventId,
    endTime = endTime?.let { ZonedDateTime.parse(it) },
    location = location,
    endLocation = endLocation,
    actualLocation = actualLocation,
    actualTime = actualTime?.let { ZonedDateTime.parse(actualTime) },
    detectionVersion = detectionVersion,
    name = name,
    accuracy = accuracy
)

fun createStop(
    type: StopType,
    startTime: ZonedDateTime,
    endTime: ZonedDateTime? = null,
    startEventId: String = generateUniqueId(),
    endEventId: String? = endTime?.let { generateUniqueId() },
    location: Location = PORT_NLRTM.location,
    endLocation: Location? = null,
    actualLocation: Location? = null,
    actualTime: ZonedDateTime? = null,
    detectionVersion: StopDetectionVersion = StopDetectionVersion.MOVEMENT_EVENT,
    name: String? = null,
    accuracy: Float? = null,
    berthStart: StopStartDetectionInfo? = null,
    berthEnd: StopEndDetectionInfo? = null,
    aisStart: StopStartDetectionInfo? = null,
    aisEnd: StopEndDetectionInfo? = null
) = Stop(
    type = type,
    aisType = type,
    name = name,
    startEventId = startEventId,
    startTime = startTime,
    startLocation = location,
    endEventId = endEventId,
    endTime = endTime.takeIf { endEventId != null },
    endLocation = (endLocation ?: location).takeIf { endEventId != null },
    actualLocation = actualLocation,
    actualTime = actualTime,
    detectionVersion = detectionVersion,
    accuracy = accuracy,
    berthStart = berthStart,
    berthEnd = berthEnd,
    aisStart = aisStart,
    aisEnd = aisEnd
)

fun createStop(
    type: StopType,
    aisType: StopType = StopType.UNCLASSIFIED,
    pomaType: StopType = StopType.UNCLASSIFIED,
    pomaId: String? = null,
    startTime: ZonedDateTime = DEFAULT_START_TIME,
    endTime: ZonedDateTime? = null,
    startEventId: String = generateUniqueId(),
    endEventId: String? = endTime?.let { generateUniqueId() },
    location: Location = PORT_NLRTM.location,
    endLocation: Location? = null,
    actualLocation: Location? = null,
    actualTime: ZonedDateTime? = null,
    detectionVersion: StopDetectionVersion = StopDetectionVersion.MOVEMENT_EVENT,
    name: String? = null,
    accuracy: Float? = null,
    berthStart: StopStartDetectionInfo? = null,
    berthEnd: StopEndDetectionInfo? = null
) = Stop(
    type = type,
    aisType = aisType,
    pomaType = pomaType,
    pomaId = pomaId,
    name = name,
    startEventId = startEventId,
    startTime = startTime,
    startLocation = location,
    endEventId = endEventId,
    endTime = endTime.takeIf { endEventId != null },
    endLocation = (endLocation ?: location).takeIf { endEventId != null },
    actualLocation = actualLocation,
    actualTime = actualTime,
    detectionVersion = detectionVersion,
    accuracy = accuracy,
    berthStart = berthStart,
    berthEnd = berthEnd,
    aisStart = null,
    aisEnd = null
)

fun createTimeWindow(
    startTime: String,
    endTime: String
) = TimeWindow(
    start = ZonedDateTime.parse(startTime),
    end = ZonedDateTime.parse(endTime)
)

fun createBerth(
    area: List<PlatformLocation>,
    authorityId: String? = null,
    _id: String = "TEST_BERTH_ID",
    name: String = "TEST_BERTH",
): Berth {
    return Berth(
        authorityId = authorityId,
        name = name,
        nameLong = null,
        terminalId = null,
        ports = listOf(PORT_NLRTM.portId),
        length = 20.0,
        location = PORT_NLRTM.location.toSkeletonLocation(),
        area = area.map { it.toSkeletonLocation() },
        _id = _id,
        cargoCategoryType = null,
        mooringType = null,
        mainPort = null
    )
}

fun createBerth(
    _id: String = "TEST_BERTH_ID",
    name: String = "TEST_BERTH",
    ports: List<String> = listOf(PORT_NLRTM.portId),
    mainPort: String? = null,
    cargoCategoryType: Set<CargoCategoryType>? = null,
    terminalId: String? = null,
): Berth {
    return Berth(
        authorityId = null,
        name = name,
        nameLong = null,
        terminalId = terminalId,
        ports = ports,
        length = 20.0,
        location = PORT_NLRTM.location.toSkeletonLocation(),
        area = emptyList(),
        _id = _id,
        cargoCategoryType = cargoCategoryType,
        mooringType = null,
        mainPort = mainPort
    )
}

fun createPort(
    _id: String = "TEST_PORT_ID",
    name: String = "PORT OR ROTTERDAM",
    unlocode: String = "NLRTM",
    mainPortId: String? = null,
    area: List<SkeletonLocation> = emptyList(),
    eosArea: List<SkeletonLocation>? = null
): Port {
    return Port(
        _id = _id,
        name = name,
        unlocode = unlocode,
        location = PORT_NLRTM.location.toSkeletonLocation(),
        area = area,
        outerArea = emptyList(),
        eosArea = eosArea,
        mainPort = mainPortId
    )
}

fun createTerminal(
    _id: String = "TEST_TERMINAL_ID",
    name: String = "Test Terminal",
    ports: List<String> = listOf(PORT_NLRTM.portId),
) = Terminal(
    name = name,
    ports = ports,
    location = PORT_NLRTM.location.toSkeletonLocation(),
    area = emptyList(),
    _id = _id
)

fun createAnchorage(
    name: String = ANCHOR_AREA_4EAST,
    ports: List<String> = listOf(PORT_NLRTM.portId),
    location: SkeletonLocation = ANCHOR_AREA_1.location,
    area: List<SkeletonLocation> = emptyList(),
    _id: String? = ANCHOR_AREA_1.anchorAreaId,
) = Anchorage(
    name = name,
    ports = ports,
    location = location,
    area = area,
    _id = _id,
)

fun createLock(
    name: String = "TEST_LOCK",
    ports: List<String> = listOf(PORT_NLRTM.portId),
    location: SkeletonLocation = SkeletonLocation(0.5, 0.5),
    area: List<SkeletonLocation> = emptyList(),
    _id: String? = "TEST_LOCK_ID",
) = Lock(
    name = name,
    ports = ports,
    location = location,
    area = area,
    _id = _id,
)

fun createApproachArea(
    name: String = "TEST_APPROACH_AREA",
    ports: List<String> = listOf(PORT_NLRTM.portId),
    location: SkeletonLocation = SkeletonLocation(0.5, 0.5),
    area: List<SkeletonLocation> = emptyList(),
    _id: String? = "TEST_APPROACH_AREA_ID",
) = ApproachArea(
    name = name,
    ports = ports,
    location = location,
    area = area,
    _id = _id,
)

fun createPilotBoardingPlace(
    name: String = "TEST_PILOT_BOARDING_PLACE",
    ports: List<String> = listOf(PORT_NLRTM.portId),
    location: SkeletonLocation = SkeletonLocation(0.5, 0.5),
    area: List<SkeletonLocation> = emptyList(),
    _id: String? = "TEST_PILOT_BOARDING_PLACE_ID",
) = PilotBoardingPlace(
    name = name,
    ports = ports,
    location = location,
    area = area,
    _id = _id,
)

fun createShipToShipArea(
    name: String = "TEST_SHIPTOSHIP_AREA",
    ports: List<String> = listOf(PORT_NLRTM.portId),
    location: SkeletonLocation = SkeletonLocation(0.5, 0.5),
    area: List<SkeletonLocation> = emptyList(),
    _id: String? = "TEST_SHIPTOSHIP_AREA_ID",
) = ShipToShipArea(
    name = name,
    ports = ports,
    location = location,
    area = area,
    _id = _id
)

fun createShipToShipTransferEvent(
    eventId: String,
    status: EventStatus,
    time: ZonedDateTime,
    otherMmsi: String,
    otherImo: String?,
    areaId: String? = null,
    relatedEvent: String? = null,
    location: Location = PORT_NLRTM.location
) = when (status) {
    EventStatus.START -> EncounterStartEvent(
        _id = eventId,
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        otherShip = AisShipIdentifier(otherMmsi.toInt(), otherImo?.toInt()),
        encounterType = EncounterType.SHIP_TO_SHIP,
        location = location.toSkeletonLocation(),
        createdTime = time.toInstant(),
        actualTime = time.toInstant(),
        metadata = STSEncounterMetadata(
            areaId = areaId
        ),
        probability = 0.0
    )
    EventStatus.END -> EncounterEndEvent(
        _id = eventId,
        ship = AisShipIdentifier(MMSI_1, IMO_1),
        otherShip = AisShipIdentifier(otherMmsi.toInt(), otherImo?.toInt()),
        encounterType = EncounterType.SHIP_TO_SHIP,
        location = location.toSkeletonLocation(),
        createdTime = time.toInstant(),
        actualTime = time.toInstant(),
        metadata = STSEncounterMetadata(
            areaId = areaId
        ),
        probability = 0.0,
        startEventId = relatedEvent,
    )
}

fun <T : Any> getMockIterable() = mock<FindIterable<out T>>()

data class TestPort(
    val portId: String,
    val location: Location
)

data class TestAnchorArea(
    val anchorAreaId: String,
    val location: SkeletonLocation,
    val destinations: Set<String>
)

private var uniqueIdCounter: Long = 0
fun generateUniqueId(): String {
    uniqueIdCounter++
    return "ID_$uniqueIdCounter"
}
