package nl.teqplay.vesselvoyage.logic

import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.vesselvoyage.model.LocationTime
import nl.teqplay.vesselvoyage.util.globalObjectMapper
import nl.teqplay.vesselvoyage.util.loadResource
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class SimplifyTraceTest {
    private val originalLocations: List<LocationTime> = globalObjectMapper.readValue(
        loadResource("traces/356712000_maasvlakte2_trace_original.json")
    )

    private val simplifiedLocations0001: List<LocationTime> = globalObjectMapper.readValue(
        loadResource("traces/356712000_maasvlakte2_trace_simplified_0001.json")
    )

    private val simplifiedLocations001: List<LocationTime> = globalObjectMapper.readValue(
        loadResource("traces/356712000_maasvlakte2_trace_simplified_001.json")
    )

    @Test
    fun `should simplify traces with tolerance 0_0001 (about 10m)`() {
        assertEquals(333, originalLocations.size)

        val tolerance = 0.0001
        val simplifiedLocations = originalLocations.simplifyTrace(
            tolerance = tolerance
        )

        assertEquals(45, simplifiedLocations.size)
        assertEquals(simplifiedLocations0001, simplifiedLocations)
    }

    @Test
    fun `should simplify traces with tolerance 0_001 (about 100m)`() {
        assertEquals(333, originalLocations.size)

        val tolerance = 0.001
        val simplifiedLocations = originalLocations.simplifyTrace(
            tolerance = tolerance
        )

        assertEquals(13, simplifiedLocations.size)
        assertEquals(simplifiedLocations001, simplifiedLocations)
    }

    // @Test
    fun `test visually in geojson_io`() {
        val geojson = """
        {
            "type": "FeatureCollection",
            "features": [
                {
                    "type": "Feature",
                    "properties": {
                        "stroke": "yellow",
                        "stroke-width": 4,
                        "stroke-opacity": 1,
                        "name": "original"
                    },
                    "geometry": {
                        "type": "LineString",
                        "coordinates": ${originalLocations.map { listOf(it.longitude, it.latitude) }}
                    }
                },
                {
                    "type": "Feature",
                    "properties": {
                        "stroke": "red",
                        "stroke-width": 2,
                        "stroke-opacity": 1,
                        "name": "simplified0001",
                        "tolerance": "0.0001 rad (~10 m)"
                    },
                    "geometry": {
                        "type": "LineString",
                        "coordinates": ${simplifiedLocations0001.map { listOf(it.longitude, it.latitude) }}
                    }
                },
                {
                    "type": "Feature",
                    "properties": {
                        "stroke": "blue",
                        "stroke-width": 2,
                        "stroke-opacity": 1,
                        "name": "simplified001",
                        "tolerance": "0.001 rad (~100 m)"
                    },
                    "geometry": {
                        "type": "LineString",
                        "coordinates": ${simplifiedLocations001.map { listOf(it.longitude, it.latitude) }}
                    }
                }                
            ]
        }
        """.trimIndent()

        println("Load the following geojson in http:geojson.io to see how it looks:")
        println()
        println(geojson)
    }
}
