package nl.teqplay.vesselvoyage.logic

import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.aisengine.event.model.ShipMovingStartEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.csi.model.ship.info.component.ShipDimensions
import nl.teqplay.vesselvoyage.logic.CalculateStops.Companion.findActualStops
import nl.teqplay.vesselvoyage.logic.CalculateStops.Companion.prepareDetectedStops
import nl.teqplay.vesselvoyage.logic.CalculateStops.Companion.tryToCombineStopsOnVisitComplete
import nl.teqplay.vesselvoyage.model.Location
import nl.teqplay.vesselvoyage.model.MovementStatus
import nl.teqplay.vesselvoyage.model.Stop
import nl.teqplay.vesselvoyage.model.StopDetectionLocation
import nl.teqplay.vesselvoyage.model.StopDetectionVersion
import nl.teqplay.vesselvoyage.model.StopEndDetectionInfo
import nl.teqplay.vesselvoyage.model.StopStartDetectionInfo
import nl.teqplay.vesselvoyage.model.StopType
import nl.teqplay.vesselvoyage.model.internal.DetectedStop
import nl.teqplay.vesselvoyage.util.globalObjectMapper
import nl.teqplay.vesselvoyage.util.loadTextResource
import nl.teqplay.vesselvoyage.util.toVesselVoyageLocation
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.util.stream.Stream
import nl.teqplay.skeleton.model.Location as SkeletonLocation

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CalculateStopsTest {
    companion object {
        const val ID_1 = "ID_1"
        const val ID_2 = "ID_2"
        const val ID_3 = "ID_3"
        const val TIME_1 = "2022-09-01T00:00:00Z"
        const val TIME_2 = "2022-09-08T00:00:00Z"
        const val TIME_3 = "2022-09-15T00:00:00Z"
        const val TIME_4 = "2022-09-15T00:01:00Z"
        const val TIME_5 = "2022-09-15T00:30:00Z"

        val LOCATION_1 = Location(1.0, 1.0)
        val LOCATION_2 = Location(1.0000005, 1.0)
        val LOCATION_3 = Location(1.1, 1.0)
        val LOCATION_4 = Location(1.5, 1.0)

        val START_MOVEMENT_EVENT = createMovementEvent(MovementStatus.START, TIME_2, LOCATION_4, ID_2)
        val START_MOVEMENT_EVENT_STOP = createStop(StopType.BERTH, TIME_1, TIME_2, ID_1, ID_2)
    }

    @Test
    fun `should prepare single stop correctly when no end was detected`() {
        val stop = createStop(
            type = StopType.BERTH,
            startTime = TIME_1,
            location = LOCATION_1
        )
        val stops = createListOfDetectedStops(stop)

        val result = prepareDetectedStops(
            stops,
            START_MOVEMENT_EVENT._id,
            START_MOVEMENT_EVENT.actualTime.atZone(ZoneOffset.UTC),
            START_MOVEMENT_EVENT.location.toVesselVoyageLocation(),
            { emptyList() },
            { emptyList() }
        )
        val expected = listOf(
            createStop(
                type = StopType.BERTH,
                startTime = TIME_1,
                endTime = TIME_2,
                startEventId = stop.startEventId,
                endEventId = START_MOVEMENT_EVENT._id,
                location = LOCATION_1,
                endLocation = LOCATION_4,
                actualLocation = LOCATION_1,
                actualTime = TIME_1
            ).copy(
                aisEnd = StopEndDetectionInfo(
                    id = START_MOVEMENT_EVENT._id,
                    location = LOCATION_4,
                    time = ZonedDateTime.parse(TIME_2)
                )
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should combine stops when preparing`() {
        val stop1 = createStop(
            type = StopType.BERTH,
            startTime = TIME_1,
            endTime = TIME_2,
            startEventId = ID_1,
            endEventId = ID_2
        )
        val stop2 = createStop(
            type = StopType.BERTH,
            startTime = TIME_3,
            startEventId = ID_1
        )
        val stops = createListOfDetectedStops(stop1, stop2)

        val result = prepareDetectedStops(
            stops,
            START_MOVEMENT_EVENT._id,
            START_MOVEMENT_EVENT.actualTime.atZone(ZoneOffset.UTC),
            START_MOVEMENT_EVENT.location.toVesselVoyageLocation(),
            { emptyList() },
            { emptyList() }
        )
        val combinedStop = createStop(
            type = StopType.BERTH,
            startTime = stop1.startTime,
            endTime = stop2.startTime,
            startEventId = ID_1,
            endEventId = ID_2
        )
        val expected = listOf(
            combinedStop.copy(
                actualLocation = combinedStop.startLocation,
                actualTime = ZonedDateTime.parse(TIME_2),
                aisEnd = StopEndDetectionInfo(
                    id = combinedStop.endEventId,
                    location = combinedStop.endLocation,
                    time = combinedStop.endTime
                )
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should not combine the first stop but combine stop two and three`() {
        val stop1 = createStop(
            type = StopType.BERTH,
            startTime = TIME_1,
            endTime = TIME_2,
            startEventId = ID_1,
            endEventId = ID_2,
            location = PORT_BEANR.location
        )
        val stop2 = createStop(
            type = StopType.BERTH,
            startTime = TIME_3,
            endTime = TIME_4,
            startEventId = ID_1,
            endEventId = ID_2
        )
        val stop3 = createStop(
            type = StopType.BERTH,
            startTime = TIME_5,
            startEventId = ID_1,
            endEventId = ID_2
        )

        val stops = createListOfDetectedStops(stop1, stop2, stop3)

        val result = prepareDetectedStops(
            stops,
            START_MOVEMENT_EVENT._id,
            START_MOVEMENT_EVENT.actualTime.atZone(ZoneOffset.UTC),
            START_MOVEMENT_EVENT.location.toVesselVoyageLocation(),
            { emptyList() },
            { emptyList() }
        )
        val combinedStop = createStop(
            type = StopType.BERTH,
            startTime = stop2.startTime,
            endTime = stop3.startTime,
            startEventId = ID_1,
            endEventId = ID_2
        )

        val expectedStop1 = stop1.copy(
            actualLocation = stop1.startLocation,
            actualTime = ZonedDateTime.parse("2022-09-04T12:00:00Z")
        )
        val expectedStop2 = combinedStop.copy(
            actualLocation = combinedStop.startLocation,
            actualTime = ZonedDateTime.parse("2022-09-15T00:15:00Z"),
            aisEnd = StopEndDetectionInfo(
                id = combinedStop.endEventId,
                location = combinedStop.endLocation,
                time = combinedStop.endTime
            )
        )
        val expected = listOf(expectedStop1, expectedStop2)

        assertEquals(expected, result)
    }

    @Test
    fun `should filter out unfinished stops when preparing`() {
        val finishedStop = createStop(
            type = StopType.BERTH,
            startTime = TIME_1,
            endTime = TIME_2,
            startEventId = ID_1,
            endEventId = ID_2,
            location = LOCATION_4
        )
        val unfinishedStop = createStop(
            type = StopType.BERTH,
            startTime = TIME_3,
            startEventId = ID_3
        )
        val stops = createListOfDetectedStops(finishedStop, unfinishedStop)

        val result = prepareDetectedStops(
            stops,
            START_MOVEMENT_EVENT._id,
            START_MOVEMENT_EVENT.actualTime.atZone(ZoneOffset.UTC),
            START_MOVEMENT_EVENT.location.toVesselVoyageLocation(),
            { emptyList() },
            { emptyList() }
        )
        val expected = listOf(
            createStop(
                type = StopType.BERTH,
                startTime = TIME_1,
                endTime = TIME_2,
                startEventId = ID_1,
                endEventId = ID_2,
                location = LOCATION_4,
                actualLocation = LOCATION_4,
                actualTime = "2022-09-04T12:00Z"
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should filter out unrelated stops when preparing`() {
        val shortStop = createStop(type = StopType.BERTH, startTime = TIME_1, endTime = TIME_2, location = PORT_NLRTM.location)
        val shortStop2 = createStop(type = StopType.BERTH, startTime = TIME_2, endTime = TIME_3, location = PORT_BEANR.location)

        val stops = createListOfDetectedStops(shortStop, shortStop2)
        val result = prepareDetectedStops(
            stops,
            START_MOVEMENT_EVENT._id,
            START_MOVEMENT_EVENT.actualTime.atZone(ZoneOffset.UTC),
            START_MOVEMENT_EVENT.location.toVesselVoyageLocation(),
            { emptyList() },
            { emptyList() }
        )

        val expectedStop = shortStop.copy(actualLocation = PORT_NLRTM.location, actualTime = ZonedDateTime.parse("2022-09-04T12:00Z"))
        val expectedStop2 = shortStop2.copy(actualLocation = PORT_BEANR.location, actualTime = ZonedDateTime.parse("2022-09-11T12:00Z"))
        val expected = listOf(expectedStop, expectedStop2)

        assertEquals(expected, result)
    }

    @Test
    fun `should filter out single stop when too short`() {
        val shortStop = createStop(type = StopType.BERTH, startTime = TIME_3, endTime = TIME_4)

        val stops = createListOfDetectedStops(shortStop)
        val result = prepareDetectedStops(
            stops,
            START_MOVEMENT_EVENT._id,
            START_MOVEMENT_EVENT.actualTime.atZone(ZoneOffset.UTC),
            START_MOVEMENT_EVENT.location.toVesselVoyageLocation(),
            { emptyList() },
            { emptyList() }
        )
        val expected = emptyList<Stop>()

        assertEquals(expected, result)
    }

    @Test
    fun `should find stop in provided trace`() {
        val shipTrace = listOf(
            createStopDetectionLocation(LOCATION_3, "2022-09-01T00:00:00Z"),
            createStopDetectionLocation(LOCATION_1, "2022-09-01T00:01:00Z"),
            createStopDetectionLocation(LOCATION_1, "2022-09-01T00:02:00Z"),
            createStopDetectionLocation(LOCATION_1, "2022-09-01T00:03:00Z"),
            createStopDetectionLocation(LOCATION_1, "2022-09-01T00:04:00Z"),
            createStopDetectionLocation(LOCATION_2, "2022-09-01T00:05:00Z"),
            createStopDetectionLocation(LOCATION_1, "2022-09-01T00:06:00Z"),
            createStopDetectionLocation(LOCATION_2, "2022-09-01T00:07:00Z"),
            createStopDetectionLocation(LOCATION_1, "2022-09-07T23:10:00Z"),
            createStopDetectionLocation(LOCATION_4, "2022-09-07T23:20:00Z")
        )

        val result = findActualStops(START_MOVEMENT_EVENT_STOP, shipTrace, START_MOVEMENT_EVENT._id).map { it.stop }

        val expectedStop = createStop(
            type = StopType.BERTH,
            startTime = "2022-09-01T00:01:00Z",
            endTime = "2022-09-07T23:10:00Z",
            startEventId = START_MOVEMENT_EVENT_STOP.startEventId,
            endEventId = START_MOVEMENT_EVENT_STOP.endEventId,
            location = LOCATION_1,
            endLocation = LOCATION_1,
            detectionVersion = StopDetectionVersion.TRACE_BETWEEN_MOVEMENT_EVENT,
        )

        val expected = listOf(
            expectedStop.copy(
                aisStart = StopStartDetectionInfo(
                    id = expectedStop.startEventId,
                    location = expectedStop.startLocation,
                    time = expectedStop.startTime
                ),
                aisEnd = StopEndDetectionInfo(
                    id = expectedStop.endEventId,
                    location = expectedStop.endLocation,
                    time = expectedStop.endTime
                )
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should detect stop in trace`() {
        val shipTraceText = loadTextResource("./traces/9350460_stopdetection_trace.json")
        val shipTrace: List<StopDetectionLocation> = globalObjectMapper.readValue(shipTraceText)

        val stop = Stop(
            type = StopType.BERTH,
            aisType = StopType.BERTH,
            pomaType = StopType.UNCLASSIFIED,
            startEventId = "5b7b28a9-15ce-48a2-9a14-2ea66c08eb38",
            startTime = ZonedDateTime.parse("2022-09-15T22:11:36.262Z"),
            startLocation = Location(latitude = 54.618093333333334, longitude = -1.1581566666666667),
            endEventId = "7cfae2cc-62b0-477b-b195-9a629e029103",
            endTime = ZonedDateTime.parse("2022-09-16T07:12:25Z"),
            endLocation = Location(latitude = 54.6265, longitude = -1.1596416666666667),
            actualLocation = null,
            actualTime = null,
            detectionVersion = StopDetectionVersion.MOVEMENT_EVENT,
            accuracy = null,
            berthStart = null,
            berthEnd = null,
            aisStart = StopStartDetectionInfo(
                id = "5b7b28a9-15ce-48a2-9a14-2ea66c08eb38",
                location = Location(latitude = 54.618093333333334, longitude = -1.1581566666666667),
                time = ZonedDateTime.parse("2022-09-15T22:11:36.262Z")
            ),
            aisEnd = null
        )

        val movementEvent = ShipMovingStartEvent(
            _id = "7cfae2cc-62b0-477b-b195-9a629e029103",
            ship = AisShipIdentifier(MMSI_1, IMO_1),
            actualTime = ZonedDateTime.parse("2022-09-16T07:12:25Z").toInstant(),
            createdTime = ZonedDateTime.parse("2022-09-16T07:12:25Z").toInstant(),
            location = SkeletonLocation(lat = 54.6265, lon = -1.1596416666666667)
        )

        val result = findActualStops(stop, shipTrace, movementEvent._id).map { it.stop }
        val expected = listOf(
            Stop(
                type = StopType.BERTH,
                aisType = StopType.BERTH,
                pomaType = StopType.UNCLASSIFIED,
                startEventId = "5b7b28a9-15ce-48a2-9a14-2ea66c08eb38",
                startTime = ZonedDateTime.parse("2022-09-15T22:14:46Z"),
                startLocation = Location(latitude = 54.617488333333334, longitude = -1.15822),
                endEventId = "7cfae2cc-62b0-477b-b195-9a629e029103",
                endTime = ZonedDateTime.parse("2022-09-16T07:10:36Z"),
                endLocation = Location(latitude = 54.61871333333333, longitude = -1.1560183333333331),
                actualLocation = null,
                actualTime = null,
                detectionVersion = StopDetectionVersion.TRACE_BETWEEN_MOVEMENT_EVENT,
                accuracy = null,
                berthStart = null,
                berthEnd = null,
                aisStart = StopStartDetectionInfo(
                    id = "5b7b28a9-15ce-48a2-9a14-2ea66c08eb38",
                    location = Location(latitude = 54.617488333333334, longitude = -1.15822),
                    time = ZonedDateTime.parse("2022-09-15T22:14:46Z")
                ),
                aisEnd = StopEndDetectionInfo(
                    id = "7cfae2cc-62b0-477b-b195-9a629e029103",
                    location = Location(latitude = 54.61871333333333, longitude = -1.1560183333333331),
                    time = ZonedDateTime.parse("2022-09-16T07:10:36Z")
                )
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should detect stop in trace and combine correctly`() {
        val shipTraceText = loadTextResource("./traces/9391971_stopdetection_trace.json")
        val shipTrace: List<StopDetectionLocation> = globalObjectMapper.readValue(shipTraceText)

        val stop = Stop(
            type = StopType.BERTH,
            aisType = StopType.BERTH,
            pomaType = StopType.UNCLASSIFIED,
            startEventId = "02750291-16f4-4915-95af-16a3f4692fb5",
            startTime = ZonedDateTime.parse("2022-10-23T12:54:39.261Z"),
            startLocation = Location(latitude = 51.86277333333333, longitude = 3.4207533333333333),
            endEventId = "44cc4717-5df0-4eac-87c4-ac98f2d082d3",
            endTime = ZonedDateTime.parse("2022-10-25T13:09:53Z"),
            endLocation = Location(latitude = 51.89781666666667, longitude = 4.290535),
            actualLocation = null,
            actualTime = null,
            detectionVersion = StopDetectionVersion.MOVEMENT_EVENT,
            accuracy = null,
            berthStart = null,
            berthEnd = null,
            aisStart = StopStartDetectionInfo(
                id = "02750291-16f4-4915-95af-16a3f4692fb5",
                location = Location(latitude = 51.86277333333333, longitude = 3.4207533333333333),
                time = ZonedDateTime.parse("2022-10-23T12:54:39.261Z")
            ),
            aisEnd = StopEndDetectionInfo(
                id = "44cc4717-5df0-4eac-87c4-ac98f2d082d3",
                location = Location(latitude = 51.89781666666667, longitude = 4.290535),
                time = ZonedDateTime.parse("2022-10-25T13:09:53Z")
            )
        )

        val movementEvent = ShipMovingStartEvent(
            _id = "44cc4717-5df0-4eac-87c4-ac98f2d082d3",
            ship = AisShipIdentifier(MMSI_1, IMO_1),
            createdTime = ZonedDateTime.parse("2022-10-25T13:09:53Z").toInstant(),
            actualTime = ZonedDateTime.parse("2022-10-25T13:09:53Z").toInstant(),
            location = SkeletonLocation(lat = 51.89781666666667, lon = 4.290535),
        )

        val actualStops = findActualStops(stop, shipTrace, movementEvent._id)
        val result = prepareDetectedStops(
            actualStops,
            movementEvent._id,
            movementEvent.actualTime.atZone(ZoneOffset.UTC),
            movementEvent.location.toVesselVoyageLocation(),
            { emptyList() },
            { emptyList() }
        )
        val expected = listOf(
            Stop(
                type = StopType.BERTH,
                aisType = StopType.BERTH,
                pomaType = StopType.UNCLASSIFIED,
                startEventId = "02750291-16f4-4915-95af-16a3f4692fb5",
                startTime = ZonedDateTime.parse("2022-10-23T13:03:52Z"),
                startLocation = Location(latitude = 51.885196666666666, longitude = 4.275526666666667),
                endEventId = "44cc4717-5df0-4eac-87c4-ac98f2d082d3",
                endTime = ZonedDateTime.parse("2022-10-25T12:24:31.432Z"),
                endLocation = Location(latitude = 51.885215, longitude = 4.275641666666667),
                actualLocation = Location(latitude = 51.88644291640951, longitude = 4.274092352275036),
                actualTime = ZonedDateTime.parse("2022-10-24T12:44:11.716Z"),
                detectionVersion = StopDetectionVersion.TRACE_BETWEEN_MOVEMENT_EVENT,
                accuracy = null,
                berthStart = null,
                berthEnd = null,
                aisStart = StopStartDetectionInfo(
                    id = "02750291-16f4-4915-95af-16a3f4692fb5",
                    location = Location(latitude = 51.885196666666666, longitude = 4.275526666666667),
                    time = ZonedDateTime.parse("2022-10-23T13:03:52Z")
                ),
                aisEnd = StopEndDetectionInfo(
                    id = "44cc4717-5df0-4eac-87c4-ac98f2d082d3",
                    location = Location(latitude = 51.885215, longitude = 4.275641666666667),
                    time = ZonedDateTime.parse("2022-10-25T12:24:31.432Z")
                )
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should combine overlapping stops from different movement events - short time between`() {
        val stop1 = createStop(
            type = StopType.BERTH,
            startTime = "2022-10-23T13:00:00Z",
            endTime = "2022-10-23T14:00:00Z",
            actualLocation = PORT_NLRTM.location
        )

        val stop2 = createStop(
            type = StopType.BERTH,
            startTime = "2022-10-23T14:01:00Z",
            endTime = "2022-10-23T15:00:00Z",
            actualLocation = PORT_NLRTM.location
        )

        val expectedStop = stop1.copy(
            aisStart = StopStartDetectionInfo(stop1.startEventId, stop1.startLocation, stop1.startTime),
            aisEnd = StopEndDetectionInfo(stop2.endEventId, stop2.endLocation, stop2.endTime),
            endEventId = stop2.endEventId,
            endTime = stop2.endTime,
            actualTime = ZonedDateTime.parse("2022-10-23T14:00:00Z"),
            detectionVersion = StopDetectionVersion.COMBINED_STOPS_VISIT_FINISHED
        )

        val stops = listOf(stop1, stop2)
        val result = tryToCombineStopsOnVisitComplete(stops, null)
        val expected = listOf(expectedStop)

        assertEquals(expected, result)
    }

    private fun testStopCombiningWithShipDimensions(): Stream<Arguments> {
        return Stream.of(
            Arguments.of("2022-10-23T14:00:00Z", "2022-10-23T14:10:00Z", ShipDimensions(length = 400.0)),
            Arguments.of("2022-10-23T14:00:00Z", "2022-10-23T14:10:00Z", ShipDimensions(length = 150.0)),
            Arguments.of("2022-10-23T14:00:00Z", "2022-10-23T14:10:00Z", ShipDimensions(length = 75.0)),
            Arguments.of("2022-10-23T14:00:00Z", "2022-10-23T14:10:00Z", ShipDimensions(length = 50.0))
        )
    }

    @ParameterizedTest
    @MethodSource("testStopCombiningWithShipDimensions")
    fun `should combine overlapping stops from different movement events - with ship dimension`(
        stopOneEndTime: String,
        stopTwoStartTime: String,
        shipDimensions: ShipDimensions
    ) {
        val stop1 = createStop(
            type = StopType.BERTH,
            startTime = "2022-10-23T13:00:00Z",
            endTime = stopOneEndTime,
            actualLocation = PORT_NLRTM.location
        )

        val stop2 = createStop(
            type = StopType.BERTH,
            startTime = stopTwoStartTime,
            endTime = "2022-10-23T17:00:00Z",
            actualLocation = PORT_NLRTM.location
        )

        val expectedStop = stop1.copy(
            aisStart = StopStartDetectionInfo(stop1.startEventId, stop1.startLocation, stop1.startTime),
            aisEnd = StopEndDetectionInfo(stop2.endEventId, stop2.endLocation, stop2.endTime),
            endEventId = stop2.endEventId,
            endTime = stop2.endTime,
            actualTime = ZonedDateTime.parse("2022-10-23T15:00:00Z"),
            detectionVersion = StopDetectionVersion.COMBINED_STOPS_VISIT_FINISHED
        )

        val stops = listOf(stop1, stop2)
        val result = tryToCombineStopsOnVisitComplete(stops, shipDimensions)
        val expected = listOf(expectedStop)

        assertEquals(expected, result)
    }

    private fun testStopSmallVessels(): Stream<Arguments> {
        return Stream.of(
            Arguments.of("2022-10-23T12:00:00Z", "2022-10-23T14:00:00Z", "2022-10-23T19:00:00Z", ShipDimensions(length = 75.0)),
            Arguments.of("2022-10-23T12:00:00Z", "2022-10-23T13:00:00Z", "2022-10-23T18:30:00Z", ShipDimensions(length = 50.0))
        )
    }

    @ParameterizedTest
    @MethodSource("testStopSmallVessels")
    fun `should not combine overlapping stops of small vessels`(
        stopOneEndTime: String,
        stopTwoStartTime: String,
        stopTwoActualTime: String,
        shipDimensions: ShipDimensions
    ) {
        val stop1 = createStop(
            type = StopType.BERTH,
            startTime = "2022-10-23T00:00:00Z",
            actualTime = "2022-10-23T06:00:00Z",
            endTime = stopOneEndTime,
            actualLocation = PORT_NLRTM.location
        )

        val stop2 = createStop(
            type = StopType.BERTH,
            startTime = stopTwoStartTime,
            actualTime = stopTwoActualTime,
            endTime = "2022-10-24T00:00:00Z",
            actualLocation = PORT_NLRTM.location
        )

        val stops = listOf(stop1, stop2)
        val result = tryToCombineStopsOnVisitComplete(stops, shipDimensions)

        assertEquals(stops, result)
    }

    private fun testStopCombiningBigVessels(): Stream<Arguments> {
        return Stream.of(
            Arguments.of("2022-10-23T12:00:00Z", "2022-10-23T15:00:00Z", "2022-10-23T19:30:00Z", ShipDimensions(length = 400.0)),
            Arguments.of("2022-10-23T12:00:00Z", "2022-10-23T15:00:00Z", "2022-10-23T19:30:00Z", ShipDimensions(length = 150.0))
        )
    }

    @ParameterizedTest
    @MethodSource("testStopCombiningBigVessels")
    fun `should combine overlapping stops of big vessels`(
        stopOneEndTime: String,
        stopTwoStartTime: String,
        stopTwoActualTime: String,
        shipDimensions: ShipDimensions
    ) {
        val stop1 = createStop(
            type = StopType.BERTH,
            startTime = "2022-10-23T00:00:00Z",
            actualTime = "2022-10-23T06:00:00Z",
            endTime = stopOneEndTime,
            actualLocation = PORT_NLRTM.location
        )

        val stop2 = createStop(
            type = StopType.BERTH,
            startTime = stopTwoStartTime,
            actualTime = stopTwoActualTime,
            endTime = "2022-10-24T00:00:00Z",
            actualLocation = PORT_NLRTM.location
        )

        val expectedStop = stop1.copy(
            aisStart = StopStartDetectionInfo(stop1.startEventId, stop1.startLocation, stop1.startTime),
            aisEnd = StopEndDetectionInfo(stop2.endEventId, stop2.endLocation, stop2.endTime),
            endEventId = stop2.endEventId,
            endTime = stop2.endTime,
            actualTime = ZonedDateTime.parse("2022-10-23T12:00Z"),
            detectionVersion = StopDetectionVersion.COMBINED_STOPS_VISIT_FINISHED
        )

        val stops = listOf(stop1, stop2)
        val result = tryToCombineStopsOnVisitComplete(stops, shipDimensions)
        val expected = listOf(expectedStop)

        assertEquals(expected, result)
    }

    private fun createListOfDetectedStops(vararg stops: Stop): List<DetectedStop> {
        return stops.map { DetectedStop(it, listOf(createStopDetectionLocation(it.startLocation, ZonedDateTime.now()))) }
    }

    private fun createStopDetectionLocation(location: Location, time: String): StopDetectionLocation {
        return createStopDetectionLocation(location, ZonedDateTime.parse(time))
    }

    private fun createStopDetectionLocation(location: Location, time: ZonedDateTime): StopDetectionLocation {
        return StopDetectionLocation(
            latitude = location.latitude,
            longitude = location.longitude,
            time = time,
            heading = 0
        )
    }
}
