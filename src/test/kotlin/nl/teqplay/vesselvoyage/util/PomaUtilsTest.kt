package nl.teqplay.vesselvoyage.util

import com.nhaarman.mockitokotlin2.mock
import nl.teqplay.poma.api.v1.Anchorage
import nl.teqplay.poma.api.v1.ApproachArea
import nl.teqplay.poma.api.v1.ApproachRoute
import nl.teqplay.poma.api.v1.Basin
import nl.teqplay.poma.api.v1.BreakWaterArea
import nl.teqplay.poma.api.v1.CustomArea
import nl.teqplay.poma.api.v1.Lock
import nl.teqplay.poma.api.v1.PilotBoardingPlace
import nl.teqplay.vesselvoyage.logic.NLRTM
import nl.teqplay.vesselvoyage.logic.createBerth
import nl.teqplay.vesselvoyage.logic.createPort
import nl.teqplay.vesselvoyage.logic.createTerminal
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class PomaUtilsTest {

    private val portList = listOf(NLRTM)

    @Test
    fun ports() {
        val empty = emptyList<String>()
        assertEquals(portList, anchorage.ports)
        assertEquals(portList, approachArea.ports)
        assertEquals(portList, approachRoute.ports)
        assertEquals(portList, basin.ports)
        assertEquals(portList, berth.ports)
        assertEquals(portList, breakWaterArea.ports)
        assertEquals(portList, customArea.copy(ports = portList).ports)

        assertEquals(portList, lock.ports)
        assertEquals(portList, pilotBoardingPlace.ports)
        assertEquals(empty, port.ports())
        assertEquals(portList, terminal.ports())
    }

    private val anchorage = Anchorage(
        ports = portList,
        name = "", location = mock(), areaSizeInM2 = null, manualOverriddenArea = false, uniqueId = null,
        modelType = "", source = null, sourceType = null, _id = null
    )

    private val approachArea = ApproachArea(
        ports = portList,
        name = "", location = mock(), areaSizeInM2 = null, manualOverriddenArea = false, uniqueId = null,
        modelType = "", source = null, sourceType = null, _id = null
    )

    private val approachRoute = ApproachRoute(
        ports = portList,
        name = "", location = mock(), uniqueId = null, modelType = "", source = null, sourceType = null, _id = null
    )

    private val basin = Basin(
        ports = portList,
        name = "", location = mock(), areaSizeInM2 = null, manualOverriddenArea = false, uniqueId = null,
        modelType = "", source = null, sourceType = null, _id = null
    )

    private val berth = createBerth(ports = portList)

    private val breakWaterArea = BreakWaterArea(
        ports = portList,
        name = "", location = mock(), areaSizeInM2 = null, manualOverriddenArea = false, uniqueId = null,
        modelType = "", source = null, sourceType = null, _id = null
    )

    private val customArea = CustomArea(
        ports = emptyList(),
        name = "", location = mock(), areaSizeInM2 = null, manualOverriddenArea = false, uniqueId = null,
        modelType = "", source = null, sourceType = null, _id = null, countries = emptySet(), validatedByUser = false
    )

    private val lock = Lock(
        ports = portList,
        name = "", location = mock(), areaSizeInM2 = null, manualOverriddenArea = false, uniqueId = null,
        modelType = "", source = null, sourceType = null, _id = null, isrsCode = null
    )

    private val pilotBoardingPlace = PilotBoardingPlace(
        ports = portList,
        name = "", location = mock(), areaSizeInM2 = null, manualOverriddenArea = false, uniqueId = null,
        modelType = "", source = null, sourceType = null, _id = null
    )

    private val port = createPort()

    private val terminal = createTerminal(ports = portList)
}
