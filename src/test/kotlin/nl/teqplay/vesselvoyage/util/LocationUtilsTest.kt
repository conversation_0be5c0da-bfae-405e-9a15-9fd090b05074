package nl.teqplay.vesselvoyage.util

import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.poma.api.v1.Port
import nl.teqplay.vesselvoyage.model.Location
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream
import nl.teqplay.poma.api.v1.Location as PomaLocation
import nl.teqplay.skeleton.model.Location as SkeletonLocation

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class LocationUtilsTest {
    @Test
    fun testHaversineDistance() {
        val location1 = Location(1.0, 2.0)
        val location2 = Location(3.0, 4.0)

        assertEquals(314755.15536540095, haversineDistance(location1, location2))
    }

    @Test
    fun testOverlapping() {
        val area1 = listOf(
            SkeletonLocation(0.0, 0.0),
            SkeletonLocation(4.0, 0.0),
            SkeletonLocation(4.0, 4.0),
            SkeletonLocation(0.0, 4.0),
            SkeletonLocation(0.0, 0.0),
        )

        // fully overlapping with area1
        val area2 = listOf(
            SkeletonLocation(1.0, 1.0),
            SkeletonLocation(2.0, 1.0),
            SkeletonLocation(2.0, 2.0),
            SkeletonLocation(1.0, 2.0),
            SkeletonLocation(1.0, 1.0),
        )
        assertTrue(overlapping(area1, area2))
        assertTrue(overlapping(area2, area1))

        // partly overlapping with area1
        val area3 = listOf(
            SkeletonLocation(3.0, 3.0),
            SkeletonLocation(5.0, 3.0),
            SkeletonLocation(5.0, 5.0),
            SkeletonLocation(3.0, 5.0),
            SkeletonLocation(3.0, 3.0),
        )
        assertTrue(overlapping(area1, area3))
        assertTrue(overlapping(area3, area1))

        // not overlapping with area1
        val area4 = listOf(
            SkeletonLocation(5.0, 5.0),
            SkeletonLocation(6.0, 5.0),
            SkeletonLocation(6.0, 6.0),
            SkeletonLocation(5.0, 6.0),
            SkeletonLocation(5.0, 5.0),
        )
        assertFalse(overlapping(area1, area4))
        assertFalse(overlapping(area4, area1))

        // touching but not overlapping area1
        val area5 = listOf(
            SkeletonLocation(4.0, 0.0),
            SkeletonLocation(5.0, 0.0),
            SkeletonLocation(5.0, 1.0),
            SkeletonLocation(4.0, 1.0),
            SkeletonLocation(4.0, 0.0),
        )
        assertFalse(overlapping(area1, area5))
        assertFalse(overlapping(area5, area1))

        // touching and *slightly* overlapping area1
        val area6 = listOf(
            SkeletonLocation(4.0 - 0.00001, 0.0),
            SkeletonLocation(5.0, 0.0),
            SkeletonLocation(5.0, 1.0),
            SkeletonLocation(4.0 - 0.00001, 1.0),
            SkeletonLocation(4.0 - 0.00001, 0.0),
        )
        assertTrue(overlapping(area1, area6))
        assertTrue(overlapping(area5, area6))
    }

    @Test
    fun `test overlapping with real ports`() {
        val rotterdam: Port = globalObjectMapper.readValue(loadResource("ports/nlrtm.json"))
        val vlaardingen: Port = globalObjectMapper.readValue(loadResource("ports/nlvla.json"))
        val dordtrecht: Port = globalObjectMapper.readValue(loadResource("ports/nldor.json"))
        val antwerp: Port = globalObjectMapper.readValue(loadResource("ports/beanr.json"))

        assertTrue(overlapping(rotterdam.area.map { it.toSkeletonLocation() }, vlaardingen.area.map { it.toSkeletonLocation() }))
        assertTrue(overlapping(rotterdam.area.map { it.toSkeletonLocation() }, dordtrecht.area.map { it.toSkeletonLocation() }))
        assertFalse(overlapping(rotterdam.area.map { it.toSkeletonLocation() }, antwerp.area.map { it.toSkeletonLocation() }))
    }

    private fun PomaLocation.toSkeletonLocation(): SkeletonLocation {
        return SkeletonLocation(this.latitude, this.longitude)
    }

    private fun testScaleLocation(): Stream<Arguments> {
        return Stream.of(
            Arguments.of(1.11, 1.11, 1.1, 1.1, 1),
            Arguments.of(-1.11, -1.11, -1.2, -1.2, 1),
            Arguments.of(1.19, 1.19, 1.1, 1.1, 1),
            Arguments.of(1.11, 1.11, 1.0, 1.0, 0),
            Arguments.of(1.111111, 1.111111, 1.111, 1.111, 3),
        )
    }

    @ParameterizedTest
    @MethodSource("testScaleLocation")
    fun `test scale location to correct amount of digits`(
        inputLat: Double,
        inputLon: Double,
        expectedLat: Double,
        expectedLon: Double,
        scale: Int
    ) {
        val testLocation = Location(latitude = inputLat, longitude = inputLon)
        val result = testLocation.toScaled(scale)
        val expected = Location(latitude = expectedLat, longitude = expectedLon)
        assertEquals(expected, result)
    }
}
