package nl.teqplay.vesselvoyage.util

import nl.teqplay.vesselvoyage.logic.PORT_NLRTM
import nl.teqplay.vesselvoyage.logic.createEncounter
import nl.teqplay.vesselvoyage.logic.createPortAreaVisit
import nl.teqplay.vesselvoyage.logic.createPortEventEnd
import nl.teqplay.vesselvoyage.logic.createPortEventStart
import nl.teqplay.vesselvoyage.logic.createStop
import nl.teqplay.vesselvoyage.logic.createVisit
import nl.teqplay.vesselvoyage.logic.createVoyage
import nl.teqplay.vesselvoyage.model.ESof
import nl.teqplay.vesselvoyage.model.Encounter
import nl.teqplay.vesselvoyage.model.SlowMovingPeriod
import nl.teqplay.vesselvoyage.model.Stop
import nl.teqplay.vesselvoyage.model.StopType
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.Voyage
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.Duration
import java.time.ZonedDateTime

class ESofUtilsTest {
    private val voyage1 = createVoyage("2021-11-18T00:00:00Z", "2021-11-21T00:00:00Z")
    private val visit2 = createVisit("2021-11-21T00:00:00Z", "2021-11-23T00:00:00Z")
    private val voyage3 = createVoyage("2021-11-23T00:00:00Z", "2021-11-28T00:00:00Z")
    private val visit4 = createVisit("2021-11-28T00:00:00Z", "2021-11-30T00:00:00Z")

    private val encounter1 = createEncounter("2021-11-22T00:00:00Z", "2021-11-23T00:00:00Z") // during visit2
    private val encounter2 = createEncounter("2021-11-23T02:00:00Z", "2021-11-23T08:00:00Z") // during start margin
    private val encounter3 = createEncounter("2021-11-25T00:00:00Z", "2021-11-26T00:00:00Z") // during voyage3
    private val encounter4 = createEncounter("2021-11-26T12:00:00Z", "2021-11-27T12:00:00Z") // during end margin
    private val encounter5 = createEncounter("2021-11-29T10:00:00Z", "2021-11-30T10:00:00Z") // during visit4

    private val stop1 = createStop(StopType.BERTH, "2021-11-19T02:00:00Z", "2021-11-19T08:00:00Z") // during voyage1
    private val stop2 = createStop(StopType.BERTH, "2021-11-19T00:00:00Z", "2021-11-20T23:00:00Z") // during end margin
    private val stop3 = createStop(StopType.BERTH, "2021-11-22T00:00:00Z", "2021-11-23T00:00:00Z") // during visit2
    private val stop4 = createStop(StopType.BERTH, "2021-11-23T02:00:00Z", "2021-11-23T08:00:00Z") // during start margin
    private val stop5 = createStop(StopType.BERTH, "2021-11-25T00:00:00Z", "2021-11-26T00:00:00Z") // during voyage3
    private val stop6 = createStop(StopType.BERTH, "2021-11-26T12:00:00Z", "2021-11-27T12:00:00Z") // during end margin
    private val stop7 = createStop(StopType.BERTH, "2021-11-29T10:00:00Z", "2021-11-30T10:00:00Z") // during visit4

    @Test
    fun `should regroup esof encounter events on a VisitShipStatus`() {
        val esof = ESof(
            encounters = listOf(encounter1, encounter2, encounter3, encounter4, encounter5),
            stops = emptyList(),
            slowMovingPeriods = emptyList()
        )

        val initialStatus = VisitShipStatus(
            previousVisit = visit2,
            previousVoyage = voyage3.copy(esof = esof),
            visit = visit4
        )

        val actualStatus = initialStatus.regroupESof()

        assertEquals(createESof(encounters = listOf(encounter1, encounter2), slowMovingPeriods = null), actualStatus.previousVisit?.esof)
        assertEquals(createESof(encounters = listOf(encounter3), slowMovingPeriods = emptyList()), actualStatus.previousVoyage?.esof)
        assertEquals(createESof(encounters = listOf(encounter4, encounter5), slowMovingPeriods = null), actualStatus.visit.esof)
    }

    @Test
    fun `should regroup esof stop events on a VisitShipStatus`() {
        val esof = ESof(
            encounters = emptyList(),
            stops = listOf(stop3, stop4, stop5, stop6, stop7),
            slowMovingPeriods = null
        )

        val initialStatus = VisitShipStatus(
            previousVisit = visit2,
            previousVoyage = voyage3.copy(esof = esof),
            visit = visit4
        )

        val actualStatus = initialStatus.regroupESof()

        assertEquals(createESof(stops = listOf(stop3, stop4), slowMovingPeriods = null), actualStatus.previousVisit?.esof)
        assertEquals(createESof(stops = listOf(stop5), slowMovingPeriods = emptyList()), actualStatus.previousVoyage?.esof)
        assertEquals(createESof(stops = listOf(stop6, stop7)), actualStatus.visit.esof)
    }

    @Test
    fun `should regroup esof stop events on a VisitShipStatus without previousVisit`() {
        val esof = ESof(
            encounters = emptyList(),
            stops = listOf(stop3, stop4, stop5, stop6, stop7),
            slowMovingPeriods = null
        )

        val initialStatus = VisitShipStatus(
            previousVisit = null,
            previousVoyage = voyage3.copy(esof = esof),
            visit = visit4
        )

        val actualStatus = initialStatus.regroupESof()

        assertEquals(null, actualStatus.previousVisit?.esof)
        assertEquals(createESof(stops = listOf(stop3, stop4, stop5), slowMovingPeriods = emptyList()), actualStatus.previousVoyage?.esof)
        assertEquals(createESof(stops = listOf(stop6, stop7)), actualStatus.visit.esof)
    }

    @Test
    fun `should regroup esof stop events on a VoyageShipStatus without previousVoyage`() {
        val esof = ESof(
            encounters = emptyList(),
            stops = listOf(stop3, stop4, stop5, stop6, stop7),
            slowMovingPeriods = null
        )

        val status = VoyageShipStatus(
            previousVoyage = null,
            previousVisit = visit2,
            voyage = voyage3.copy(esof = esof)
        )

        val actualStatus = status.regroupESof()

        assertEquals(null, actualStatus.previousVoyage)
        assertEquals(createESof(stops = listOf(stop3, stop4)), actualStatus.previousVisit?.esof)
        assertEquals(createESof(stops = listOf(stop5, stop6, stop7)), actualStatus.voyage.esof)
    }

    @Test
    fun `should regroup esof stop events on a VoyageShipStatus`() {
        val esof = ESof(
            encounters = emptyList(),
            stops = listOf(stop1, stop2, stop3, stop4, stop5, stop6, stop7),
            slowMovingPeriods = null
        )

        val status = VoyageShipStatus(
            previousVoyage = voyage1,
            previousVisit = visit2,
            voyage = voyage3.copy(esof = esof)
        )

        val actualStatus = status.regroupESof()

        assertEquals(createESof(stops = listOf(stop1)), actualStatus.previousVoyage?.esof)
        assertEquals(createESof(stops = listOf(stop2, stop3, stop4)), actualStatus.previousVisit?.esof)
        assertEquals(createESof(stops = listOf(stop5, stop6, stop7)), actualStatus.voyage.esof)
    }

    @Test
    fun `should determine the correct esof period using margins`() {
        fun parse(date: String) = ZonedDateTime.parse(date)

        // no overlapping margins
        assertEquals(
            ESofVoyageMargins(Duration.ofHours(4), Duration.ofHours(24)),
            calculateEsofVoyageMargins(parse("2021-12-01T00:00:00Z"), parse("2021-12-07T00:00:00Z"))
        )

        // partly overlapping margins
        assertEquals(
            ESofVoyageMargins(Duration.ofHours(4), Duration.ofHours(20)),
            calculateEsofVoyageMargins(parse("2021-12-01T00:00:00Z"), parse("2021-12-02T00:00:00Z"))
        )

        // fully overlapping margins
        assertEquals(
            ESofVoyageMargins(Duration.ofHours(1), Duration.ofHours(1)),
            calculateEsofVoyageMargins(parse("2021-12-01T00:00:00Z"), parse("2021-12-01T02:00:00Z"))
        )

        val expectedDefaultMargins = ESofVoyageMargins(
            Duration.ofHours(4),
            Duration.ofHours(24)
        )

        // no start or end time
        assertEquals(expectedDefaultMargins, calculateEsofVoyageMargins(null, parse("2021-12-07T00:00:00Z")))
        assertEquals(expectedDefaultMargins, calculateEsofVoyageMargins(parse("2021-12-01T00:00:00Z"), null))
        assertEquals(expectedDefaultMargins, calculateEsofVoyageMargins(null, null))
    }
}

private fun createVisit(startTime: String, endTime: String?, esof: ESof? = null): Visit {
    val portEventStart = createPortEventStart(PORT_NLRTM, startTime)
    val portEventEnd = endTime?.let { createPortEventEnd(PORT_NLRTM, endTime) }

    return createVisit(
        portAreas = listOf(
            createPortAreaVisit(portEventStart, portEventEnd)
        ),
        finished = portEventEnd != null,
        esof = esof
    )
}

private fun createVoyage(startTime: String, endTime: String?, esof: ESof? = null): Voyage {
    return createVoyage(
        startPortIds = listOf(
            PORT_NLRTM.portId
        ),
        startTime = ZonedDateTime.parse(startTime),
        endTime = endTime?.let { ZonedDateTime.parse(endTime) },
        finished = endTime != null,
        esof = esof
    )
}

private fun createESof(
    encounters: List<Encounter>? = null,
    stops: List<Stop>? = null,
    slowMovingPeriods: List<SlowMovingPeriod>? = null
) = ESof(
    encounters = encounters ?: emptyList(),
    stops = stops ?: emptyList(),
    slowMovingPeriods = slowMovingPeriods
)
