package nl.teqplay.vesselvoyage.util

import nl.teqplay.vesselvoyage.logic.createTimeWindow
import nl.teqplay.vesselvoyage.model.TimeWindow
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.time.Duration
import java.time.ZonedDateTime

class DateTimeUtilsTest {
    @Test
    fun `calculate sum of a list with durations`() {
        assertEquals(
            Duration.ofHours(12),
            listOf(
                Duration.ofHours(2),
                Duration.ofHours(3),
                Duration.ofHours(7),
            ).sum()
        )

        assertEquals(Duration.ZERO, emptyList<Duration>().sum())
    }

    @Test
    fun `calculate average of a list with durations`() {
        assertEquals(
            Duration.ofHours(4),
            listOf(
                Duration.ofHours(2),
                Duration.ofHours(3),
                Duration.ofHours(7),
            ).average()
        )

        assertEquals(Duration.ZERO, emptyList<Duration>().average())
    }

    @Test
    fun `generateTimeWindows should generate a list with time windows`() {
        val startTime = ZonedDateTime.parse("2022-01-26T10:00:00Z")
        val endTime = ZonedDateTime.parse("2022-03-14T04:00:00Z")

        val timeWindows = generateTimeWindows(startTime, endTime, Duration.ofDays(10))
        assertEquals(
            listOf(
                createTimeWindow("2022-01-26T10:00:00Z", "2022-02-05T10:00:00Z"),
                createTimeWindow("2022-02-05T10:00:00Z", "2022-02-15T10:00:00Z"),
                createTimeWindow("2022-02-15T10:00:00Z", "2022-02-25T10:00:00Z"),
                createTimeWindow("2022-02-25T10:00:00Z", "2022-03-07T10:00:00Z"),
                createTimeWindow("2022-03-07T10:00:00Z", "2022-03-14T04:00:00Z"),
            ),
            timeWindows
        )
    }

    @Test
    fun `generateTimeWindows should work correctly with timezones`() {
        val startTime = ZonedDateTime.parse("2022-01-12T21:00:00+01:00[Europe/Berlin]")
        val endTime = ZonedDateTime.parse("2022-02-11T21:00:00+01:00[Europe/Berlin]")

        val timeWindows = generateTimeWindows(startTime, endTime, Duration.ofDays(30))
        assertEquals(
            listOf(
                createTimeWindow("2022-01-12T20:00:00Z", "2022-02-11T20:00:00Z"),
            ),
            timeWindows
        )
    }

    @Test
    fun `generateTimeWindows should prevent against an infinite loop`() {
        val startTime = ZonedDateTime.parse("2022-01-01T00:00:00Z")
        val endTime = ZonedDateTime.parse("5032-01-01T00:00:00Z")

        assertThrows<TimeWindowException> {
            generateTimeWindows(startTime, endTime, Duration.ofDays(1))
        }
    }

    @Test
    fun `generateTimeWindowsInMonths should generate a list with time windows`() {
        val startTime = ZonedDateTime.parse("2022-01-25T00:00:00Z")
        val endTime = ZonedDateTime.parse("2022-05-14T00:00:00Z")

        val timeWindows = generateTimeWindowsInMonths(startTime, endTime)
        assertEquals(
            listOf(
                createTimeWindow("2022-01-01T00:00:00Z", "2022-02-01T00:00:00Z"),
                createTimeWindow("2022-02-01T00:00:00Z", "2022-03-01T00:00:00Z"),
                createTimeWindow("2022-03-01T00:00:00Z", "2022-04-01T00:00:00Z"),
                createTimeWindow("2022-04-01T00:00:00Z", "2022-05-01T00:00:00Z"),
                createTimeWindow("2022-05-01T00:00:00Z", "2022-06-01T00:00:00Z"),
            ),
            timeWindows
        )
    }

    @Test
    fun `generateTimeWindowsInMonths should prevent against an infinite loop`() {
        val startTime = ZonedDateTime.parse("2022-01-01T00:00:00Z")
        val endTime = ZonedDateTime.parse("5032-01-01T00:00:00Z")

        assertThrows<TimeWindowException> {
            generateTimeWindowsInMonths(startTime, endTime)
        }
    }

    @Test
    fun `generateTimeWindows should give an empty list when end time is before start time`() {
        val startTime = ZonedDateTime.parse("2022-01-02T00:00:00Z")
        val endTime = ZonedDateTime.parse("2022-01-01T00:00:00Z")
        val timeWindows = generateTimeWindows(startTime, endTime, Duration.ofHours(1))

        assertEquals(emptyList<TimeWindow>(), timeWindows)
    }

    @Test
    fun `test toUTC`() {
        val startTime = ZonedDateTime.parse("2022-01-25T01:00+01:00")

        assertEquals("2022-01-25T01:00+01:00", startTime.toString())
        assertEquals("2022-01-25T00:00Z", startTime.toUTC().toString())
    }
}
