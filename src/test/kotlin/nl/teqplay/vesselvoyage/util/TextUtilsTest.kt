package nl.teqplay.vesselvoyage.util

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class TextUtilsTest {
    @Test
    fun `should truncate a string`() {
        val text = "Hello World"

        assertEquals("Hello W...", text.truncate(7))
        assertEquals("Hello World", text.truncate(100))
        assertEquals("Hello W [and more]", text.truncate(7, " [and more]"))
    }
}
