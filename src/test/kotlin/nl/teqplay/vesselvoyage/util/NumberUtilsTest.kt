package nl.teqplay.vesselvoyage.util

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class NumberUtilsTest {
    private fun floatTestInput(): Stream<Arguments> {
        return Stream.of(
            Arguments.of(0.1f, 0.1),
            Arguments.of(1.01f, 1.01),
            Arguments.of(4.51f, 4.51),
            Arguments.of(0.739454f, 0.739454),
            Arguments.of(-4.51f, -4.51),
        )
    }

    @ParameterizedTest
    @MethodSource("floatTestInput")
    fun testFloatToExactDouble(input: Float, expected: Double) {
        val actual = input.toExactDouble()
        assertEquals(expected, actual)
    }

    @Test
    fun testWeightedAverage() {

        /*
        // Code to generate all options with below values and weights, not going to code that by hand ;)
        // Aim is to test with negative, zero and positive values, with whole and decimal numbers. And with weights
        // of zero, one and more than 1. That should cover all cases possible.
        val values = listOf(-1.0F, -0.5F, 0.0F, 0.5F, 1.0F)
        val weights = listOf(0L, 1L, 2L)

        values.forEach { valueA ->
            values.forEach { valueB ->
                weights.forEach { weightA ->
                    weights.forEach { weightB ->
                        val outcome = kotlin.runCatching { weightedAverage(valueA, weightA, valueB, weightB) }
                        print("assertEquals(${outcome.getOrNull()}F, ")
                        print("weightedAverage(${valueA}F, $weightA, ${valueB}F, $weightB)")
                        println(", 0.001F)")
                    }
                }
            }
        }
         */

        assertEquals(-1.0F, weightedAverage(-1.0F, 0, -1.0F, 0), 0.001F)
        assertEquals(-1.0F, weightedAverage(-1.0F, 0, -1.0F, 1), 0.001F)
        assertEquals(-1.0F, weightedAverage(-1.0F, 0, -1.0F, 2), 0.001F)
        assertEquals(-1.0F, weightedAverage(-1.0F, 1, -1.0F, 0), 0.001F)
        assertEquals(-1.0F, weightedAverage(-1.0F, 1, -1.0F, 1), 0.001F)
        assertEquals(-1.0F, weightedAverage(-1.0F, 1, -1.0F, 2), 0.001F)
        assertEquals(-1.0F, weightedAverage(-1.0F, 2, -1.0F, 0), 0.001F)
        assertEquals(-1.0F, weightedAverage(-1.0F, 2, -1.0F, 1), 0.001F)
        assertEquals(-1.0F, weightedAverage(-1.0F, 2, -1.0F, 2), 0.001F)
        assertEquals(-0.75F, weightedAverage(-1.0F, 0, -0.5F, 0), 0.001F)
        assertEquals(-0.5F, weightedAverage(-1.0F, 0, -0.5F, 1), 0.001F)
        assertEquals(-0.5F, weightedAverage(-1.0F, 0, -0.5F, 2), 0.001F)
        assertEquals(-1.0F, weightedAverage(-1.0F, 1, -0.5F, 0), 0.001F)
        assertEquals(-0.75F, weightedAverage(-1.0F, 1, -0.5F, 1), 0.001F)
        assertEquals(-0.6666667F, weightedAverage(-1.0F, 1, -0.5F, 2), 0.001F)
        assertEquals(-1.0F, weightedAverage(-1.0F, 2, -0.5F, 0), 0.001F)
        assertEquals(-0.8333333F, weightedAverage(-1.0F, 2, -0.5F, 1), 0.001F)
        assertEquals(-0.75F, weightedAverage(-1.0F, 2, -0.5F, 2), 0.001F)
        assertEquals(-0.5F, weightedAverage(-1.0F, 0, 0.0F, 0), 0.001F)
        assertEquals(0.0F, weightedAverage(-1.0F, 0, 0.0F, 1), 0.001F)
        assertEquals(0.0F, weightedAverage(-1.0F, 0, 0.0F, 2), 0.001F)
        assertEquals(-1.0F, weightedAverage(-1.0F, 1, 0.0F, 0), 0.001F)
        assertEquals(-0.5F, weightedAverage(-1.0F, 1, 0.0F, 1), 0.001F)
        assertEquals(-0.33333334F, weightedAverage(-1.0F, 1, 0.0F, 2), 0.001F)
        assertEquals(-1.0F, weightedAverage(-1.0F, 2, 0.0F, 0), 0.001F)
        assertEquals(-0.6666667F, weightedAverage(-1.0F, 2, 0.0F, 1), 0.001F)
        assertEquals(-0.5F, weightedAverage(-1.0F, 2, 0.0F, 2), 0.001F)
        assertEquals(-0.25F, weightedAverage(-1.0F, 0, 0.5F, 0), 0.001F)
        assertEquals(0.5F, weightedAverage(-1.0F, 0, 0.5F, 1), 0.001F)
        assertEquals(0.5F, weightedAverage(-1.0F, 0, 0.5F, 2), 0.001F)
        assertEquals(-1.0F, weightedAverage(-1.0F, 1, 0.5F, 0), 0.001F)
        assertEquals(-0.25F, weightedAverage(-1.0F, 1, 0.5F, 1), 0.001F)
        assertEquals(0.0F, weightedAverage(-1.0F, 1, 0.5F, 2), 0.001F)
        assertEquals(-1.0F, weightedAverage(-1.0F, 2, 0.5F, 0), 0.001F)
        assertEquals(-0.5F, weightedAverage(-1.0F, 2, 0.5F, 1), 0.001F)
        assertEquals(-0.25F, weightedAverage(-1.0F, 2, 0.5F, 2), 0.001F)
        assertEquals(0.0F, weightedAverage(-1.0F, 0, 1.0F, 0), 0.001F)
        assertEquals(1.0F, weightedAverage(-1.0F, 0, 1.0F, 1), 0.001F)
        assertEquals(1.0F, weightedAverage(-1.0F, 0, 1.0F, 2), 0.001F)
        assertEquals(-1.0F, weightedAverage(-1.0F, 1, 1.0F, 0), 0.001F)
        assertEquals(0.0F, weightedAverage(-1.0F, 1, 1.0F, 1), 0.001F)
        assertEquals(0.33333334F, weightedAverage(-1.0F, 1, 1.0F, 2), 0.001F)
        assertEquals(-1.0F, weightedAverage(-1.0F, 2, 1.0F, 0), 0.001F)
        assertEquals(-0.33333334F, weightedAverage(-1.0F, 2, 1.0F, 1), 0.001F)
        assertEquals(0.0F, weightedAverage(-1.0F, 2, 1.0F, 2), 0.001F)
        assertEquals(-0.75F, weightedAverage(-0.5F, 0, -1.0F, 0), 0.001F)
        assertEquals(-1.0F, weightedAverage(-0.5F, 0, -1.0F, 1), 0.001F)
        assertEquals(-1.0F, weightedAverage(-0.5F, 0, -1.0F, 2), 0.001F)
        assertEquals(-0.5F, weightedAverage(-0.5F, 1, -1.0F, 0), 0.001F)
        assertEquals(-0.75F, weightedAverage(-0.5F, 1, -1.0F, 1), 0.001F)
        assertEquals(-0.8333333F, weightedAverage(-0.5F, 1, -1.0F, 2), 0.001F)
        assertEquals(-0.5F, weightedAverage(-0.5F, 2, -1.0F, 0), 0.001F)
        assertEquals(-0.6666667F, weightedAverage(-0.5F, 2, -1.0F, 1), 0.001F)
        assertEquals(-0.75F, weightedAverage(-0.5F, 2, -1.0F, 2), 0.001F)
        assertEquals(-0.5F, weightedAverage(-0.5F, 0, -0.5F, 0), 0.001F)
        assertEquals(-0.5F, weightedAverage(-0.5F, 0, -0.5F, 1), 0.001F)
        assertEquals(-0.5F, weightedAverage(-0.5F, 0, -0.5F, 2), 0.001F)
        assertEquals(-0.5F, weightedAverage(-0.5F, 1, -0.5F, 0), 0.001F)
        assertEquals(-0.5F, weightedAverage(-0.5F, 1, -0.5F, 1), 0.001F)
        assertEquals(-0.5F, weightedAverage(-0.5F, 1, -0.5F, 2), 0.001F)
        assertEquals(-0.5F, weightedAverage(-0.5F, 2, -0.5F, 0), 0.001F)
        assertEquals(-0.5F, weightedAverage(-0.5F, 2, -0.5F, 1), 0.001F)
        assertEquals(-0.5F, weightedAverage(-0.5F, 2, -0.5F, 2), 0.001F)
        assertEquals(-0.25F, weightedAverage(-0.5F, 0, 0.0F, 0), 0.001F)
        assertEquals(0.0F, weightedAverage(-0.5F, 0, 0.0F, 1), 0.001F)
        assertEquals(0.0F, weightedAverage(-0.5F, 0, 0.0F, 2), 0.001F)
        assertEquals(-0.5F, weightedAverage(-0.5F, 1, 0.0F, 0), 0.001F)
        assertEquals(-0.25F, weightedAverage(-0.5F, 1, 0.0F, 1), 0.001F)
        assertEquals(-0.16666667F, weightedAverage(-0.5F, 1, 0.0F, 2), 0.001F)
        assertEquals(-0.5F, weightedAverage(-0.5F, 2, 0.0F, 0), 0.001F)
        assertEquals(-0.33333334F, weightedAverage(-0.5F, 2, 0.0F, 1), 0.001F)
        assertEquals(-0.25F, weightedAverage(-0.5F, 2, 0.0F, 2), 0.001F)
        assertEquals(0.0F, weightedAverage(-0.5F, 0, 0.5F, 0), 0.001F)
        assertEquals(0.5F, weightedAverage(-0.5F, 0, 0.5F, 1), 0.001F)
        assertEquals(0.5F, weightedAverage(-0.5F, 0, 0.5F, 2), 0.001F)
        assertEquals(-0.5F, weightedAverage(-0.5F, 1, 0.5F, 0), 0.001F)
        assertEquals(0.0F, weightedAverage(-0.5F, 1, 0.5F, 1), 0.001F)
        assertEquals(0.16666667F, weightedAverage(-0.5F, 1, 0.5F, 2), 0.001F)
        assertEquals(-0.5F, weightedAverage(-0.5F, 2, 0.5F, 0), 0.001F)
        assertEquals(-0.16666667F, weightedAverage(-0.5F, 2, 0.5F, 1), 0.001F)
        assertEquals(0.0F, weightedAverage(-0.5F, 2, 0.5F, 2), 0.001F)
        assertEquals(0.25F, weightedAverage(-0.5F, 0, 1.0F, 0), 0.001F)
        assertEquals(1.0F, weightedAverage(-0.5F, 0, 1.0F, 1), 0.001F)
        assertEquals(1.0F, weightedAverage(-0.5F, 0, 1.0F, 2), 0.001F)
        assertEquals(-0.5F, weightedAverage(-0.5F, 1, 1.0F, 0), 0.001F)
        assertEquals(0.25F, weightedAverage(-0.5F, 1, 1.0F, 1), 0.001F)
        assertEquals(0.5F, weightedAverage(-0.5F, 1, 1.0F, 2), 0.001F)
        assertEquals(-0.5F, weightedAverage(-0.5F, 2, 1.0F, 0), 0.001F)
        assertEquals(0.0F, weightedAverage(-0.5F, 2, 1.0F, 1), 0.001F)
        assertEquals(0.25F, weightedAverage(-0.5F, 2, 1.0F, 2), 0.001F)
        assertEquals(-0.5F, weightedAverage(0.0F, 0, -1.0F, 0), 0.001F)
        assertEquals(-1.0F, weightedAverage(0.0F, 0, -1.0F, 1), 0.001F)
        assertEquals(-1.0F, weightedAverage(0.0F, 0, -1.0F, 2), 0.001F)
        assertEquals(0.0F, weightedAverage(0.0F, 1, -1.0F, 0), 0.001F)
        assertEquals(-0.5F, weightedAverage(0.0F, 1, -1.0F, 1), 0.001F)
        assertEquals(-0.6666667F, weightedAverage(0.0F, 1, -1.0F, 2), 0.001F)
        assertEquals(0.0F, weightedAverage(0.0F, 2, -1.0F, 0), 0.001F)
        assertEquals(-0.33333334F, weightedAverage(0.0F, 2, -1.0F, 1), 0.001F)
        assertEquals(-0.5F, weightedAverage(0.0F, 2, -1.0F, 2), 0.001F)
        assertEquals(-0.25F, weightedAverage(0.0F, 0, -0.5F, 0), 0.001F)
        assertEquals(-0.5F, weightedAverage(0.0F, 0, -0.5F, 1), 0.001F)
        assertEquals(-0.5F, weightedAverage(0.0F, 0, -0.5F, 2), 0.001F)
        assertEquals(0.0F, weightedAverage(0.0F, 1, -0.5F, 0), 0.001F)
        assertEquals(-0.25F, weightedAverage(0.0F, 1, -0.5F, 1), 0.001F)
        assertEquals(-0.33333334F, weightedAverage(0.0F, 1, -0.5F, 2), 0.001F)
        assertEquals(0.0F, weightedAverage(0.0F, 2, -0.5F, 0), 0.001F)
        assertEquals(-0.16666667F, weightedAverage(0.0F, 2, -0.5F, 1), 0.001F)
        assertEquals(-0.25F, weightedAverage(0.0F, 2, -0.5F, 2), 0.001F)
        assertEquals(0.0F, weightedAverage(0.0F, 0, 0.0F, 0), 0.001F)
        assertEquals(0.0F, weightedAverage(0.0F, 0, 0.0F, 1), 0.001F)
        assertEquals(0.0F, weightedAverage(0.0F, 0, 0.0F, 2), 0.001F)
        assertEquals(0.0F, weightedAverage(0.0F, 1, 0.0F, 0), 0.001F)
        assertEquals(0.0F, weightedAverage(0.0F, 1, 0.0F, 1), 0.001F)
        assertEquals(0.0F, weightedAverage(0.0F, 1, 0.0F, 2), 0.001F)
        assertEquals(0.0F, weightedAverage(0.0F, 2, 0.0F, 0), 0.001F)
        assertEquals(0.0F, weightedAverage(0.0F, 2, 0.0F, 1), 0.001F)
        assertEquals(0.0F, weightedAverage(0.0F, 2, 0.0F, 2), 0.001F)
        assertEquals(0.25F, weightedAverage(0.0F, 0, 0.5F, 0), 0.001F)
        assertEquals(0.5F, weightedAverage(0.0F, 0, 0.5F, 1), 0.001F)
        assertEquals(0.5F, weightedAverage(0.0F, 0, 0.5F, 2), 0.001F)
        assertEquals(0.0F, weightedAverage(0.0F, 1, 0.5F, 0), 0.001F)
        assertEquals(0.25F, weightedAverage(0.0F, 1, 0.5F, 1), 0.001F)
        assertEquals(0.33333334F, weightedAverage(0.0F, 1, 0.5F, 2), 0.001F)
        assertEquals(0.0F, weightedAverage(0.0F, 2, 0.5F, 0), 0.001F)
        assertEquals(0.16666667F, weightedAverage(0.0F, 2, 0.5F, 1), 0.001F)
        assertEquals(0.25F, weightedAverage(0.0F, 2, 0.5F, 2), 0.001F)
        assertEquals(0.5F, weightedAverage(0.0F, 0, 1.0F, 0), 0.001F)
        assertEquals(1.0F, weightedAverage(0.0F, 0, 1.0F, 1), 0.001F)
        assertEquals(1.0F, weightedAverage(0.0F, 0, 1.0F, 2), 0.001F)
        assertEquals(0.0F, weightedAverage(0.0F, 1, 1.0F, 0), 0.001F)
        assertEquals(0.5F, weightedAverage(0.0F, 1, 1.0F, 1), 0.001F)
        assertEquals(0.6666667F, weightedAverage(0.0F, 1, 1.0F, 2), 0.001F)
        assertEquals(0.0F, weightedAverage(0.0F, 2, 1.0F, 0), 0.001F)
        assertEquals(0.33333334F, weightedAverage(0.0F, 2, 1.0F, 1), 0.001F)
        assertEquals(0.5F, weightedAverage(0.0F, 2, 1.0F, 2), 0.001F)
        assertEquals(-0.25F, weightedAverage(0.5F, 0, -1.0F, 0), 0.001F)
        assertEquals(-1.0F, weightedAverage(0.5F, 0, -1.0F, 1), 0.001F)
        assertEquals(-1.0F, weightedAverage(0.5F, 0, -1.0F, 2), 0.001F)
        assertEquals(0.5F, weightedAverage(0.5F, 1, -1.0F, 0), 0.001F)
        assertEquals(-0.25F, weightedAverage(0.5F, 1, -1.0F, 1), 0.001F)
        assertEquals(-0.5F, weightedAverage(0.5F, 1, -1.0F, 2), 0.001F)
        assertEquals(0.5F, weightedAverage(0.5F, 2, -1.0F, 0), 0.001F)
        assertEquals(0.0F, weightedAverage(0.5F, 2, -1.0F, 1), 0.001F)
        assertEquals(-0.25F, weightedAverage(0.5F, 2, -1.0F, 2), 0.001F)
        assertEquals(0.0F, weightedAverage(0.5F, 0, -0.5F, 0), 0.001F)
        assertEquals(-0.5F, weightedAverage(0.5F, 0, -0.5F, 1), 0.001F)
        assertEquals(-0.5F, weightedAverage(0.5F, 0, -0.5F, 2), 0.001F)
        assertEquals(0.5F, weightedAverage(0.5F, 1, -0.5F, 0), 0.001F)
        assertEquals(0.0F, weightedAverage(0.5F, 1, -0.5F, 1), 0.001F)
        assertEquals(-0.16666667F, weightedAverage(0.5F, 1, -0.5F, 2), 0.001F)
        assertEquals(0.5F, weightedAverage(0.5F, 2, -0.5F, 0), 0.001F)
        assertEquals(0.16666667F, weightedAverage(0.5F, 2, -0.5F, 1), 0.001F)
        assertEquals(0.0F, weightedAverage(0.5F, 2, -0.5F, 2), 0.001F)
        assertEquals(0.25F, weightedAverage(0.5F, 0, 0.0F, 0), 0.001F)
        assertEquals(0.0F, weightedAverage(0.5F, 0, 0.0F, 1), 0.001F)
        assertEquals(0.0F, weightedAverage(0.5F, 0, 0.0F, 2), 0.001F)
        assertEquals(0.5F, weightedAverage(0.5F, 1, 0.0F, 0), 0.001F)
        assertEquals(0.25F, weightedAverage(0.5F, 1, 0.0F, 1), 0.001F)
        assertEquals(0.16666667F, weightedAverage(0.5F, 1, 0.0F, 2), 0.001F)
        assertEquals(0.5F, weightedAverage(0.5F, 2, 0.0F, 0), 0.001F)
        assertEquals(0.33333334F, weightedAverage(0.5F, 2, 0.0F, 1), 0.001F)
        assertEquals(0.25F, weightedAverage(0.5F, 2, 0.0F, 2), 0.001F)
        assertEquals(0.5F, weightedAverage(0.5F, 0, 0.5F, 0), 0.001F)
        assertEquals(0.5F, weightedAverage(0.5F, 0, 0.5F, 1), 0.001F)
        assertEquals(0.5F, weightedAverage(0.5F, 0, 0.5F, 2), 0.001F)
        assertEquals(0.5F, weightedAverage(0.5F, 1, 0.5F, 0), 0.001F)
        assertEquals(0.5F, weightedAverage(0.5F, 1, 0.5F, 1), 0.001F)
        assertEquals(0.5F, weightedAverage(0.5F, 1, 0.5F, 2), 0.001F)
        assertEquals(0.5F, weightedAverage(0.5F, 2, 0.5F, 0), 0.001F)
        assertEquals(0.5F, weightedAverage(0.5F, 2, 0.5F, 1), 0.001F)
        assertEquals(0.5F, weightedAverage(0.5F, 2, 0.5F, 2), 0.001F)
        assertEquals(0.75F, weightedAverage(0.5F, 0, 1.0F, 0), 0.001F)
        assertEquals(1.0F, weightedAverage(0.5F, 0, 1.0F, 1), 0.001F)
        assertEquals(1.0F, weightedAverage(0.5F, 0, 1.0F, 2), 0.001F)
        assertEquals(0.5F, weightedAverage(0.5F, 1, 1.0F, 0), 0.001F)
        assertEquals(0.75F, weightedAverage(0.5F, 1, 1.0F, 1), 0.001F)
        assertEquals(0.8333333F, weightedAverage(0.5F, 1, 1.0F, 2), 0.001F)
        assertEquals(0.5F, weightedAverage(0.5F, 2, 1.0F, 0), 0.001F)
        assertEquals(0.6666667F, weightedAverage(0.5F, 2, 1.0F, 1), 0.001F)
        assertEquals(0.75F, weightedAverage(0.5F, 2, 1.0F, 2), 0.001F)
        assertEquals(0.0F, weightedAverage(1.0F, 0, -1.0F, 0), 0.001F)
        assertEquals(-1.0F, weightedAverage(1.0F, 0, -1.0F, 1), 0.001F)
        assertEquals(-1.0F, weightedAverage(1.0F, 0, -1.0F, 2), 0.001F)
        assertEquals(1.0F, weightedAverage(1.0F, 1, -1.0F, 0), 0.001F)
        assertEquals(0.0F, weightedAverage(1.0F, 1, -1.0F, 1), 0.001F)
        assertEquals(-0.33333334F, weightedAverage(1.0F, 1, -1.0F, 2), 0.001F)
        assertEquals(1.0F, weightedAverage(1.0F, 2, -1.0F, 0), 0.001F)
        assertEquals(0.33333334F, weightedAverage(1.0F, 2, -1.0F, 1), 0.001F)
        assertEquals(0.0F, weightedAverage(1.0F, 2, -1.0F, 2), 0.001F)
        assertEquals(0.25F, weightedAverage(1.0F, 0, -0.5F, 0), 0.001F)
        assertEquals(-0.5F, weightedAverage(1.0F, 0, -0.5F, 1), 0.001F)
        assertEquals(-0.5F, weightedAverage(1.0F, 0, -0.5F, 2), 0.001F)
        assertEquals(1.0F, weightedAverage(1.0F, 1, -0.5F, 0), 0.001F)
        assertEquals(0.25F, weightedAverage(1.0F, 1, -0.5F, 1), 0.001F)
        assertEquals(0.0F, weightedAverage(1.0F, 1, -0.5F, 2), 0.001F)
        assertEquals(1.0F, weightedAverage(1.0F, 2, -0.5F, 0), 0.001F)
        assertEquals(0.5F, weightedAverage(1.0F, 2, -0.5F, 1), 0.001F)
        assertEquals(0.25F, weightedAverage(1.0F, 2, -0.5F, 2), 0.001F)
        assertEquals(0.5F, weightedAverage(1.0F, 0, 0.0F, 0), 0.001F)
        assertEquals(0.0F, weightedAverage(1.0F, 0, 0.0F, 1), 0.001F)
        assertEquals(0.0F, weightedAverage(1.0F, 0, 0.0F, 2), 0.001F)
        assertEquals(1.0F, weightedAverage(1.0F, 1, 0.0F, 0), 0.001F)
        assertEquals(0.5F, weightedAverage(1.0F, 1, 0.0F, 1), 0.001F)
        assertEquals(0.33333334F, weightedAverage(1.0F, 1, 0.0F, 2), 0.001F)
        assertEquals(1.0F, weightedAverage(1.0F, 2, 0.0F, 0), 0.001F)
        assertEquals(0.6666667F, weightedAverage(1.0F, 2, 0.0F, 1), 0.001F)
        assertEquals(0.5F, weightedAverage(1.0F, 2, 0.0F, 2), 0.001F)
        assertEquals(0.75F, weightedAverage(1.0F, 0, 0.5F, 0), 0.001F)
        assertEquals(0.5F, weightedAverage(1.0F, 0, 0.5F, 1), 0.001F)
        assertEquals(0.5F, weightedAverage(1.0F, 0, 0.5F, 2), 0.001F)
        assertEquals(1.0F, weightedAverage(1.0F, 1, 0.5F, 0), 0.001F)
        assertEquals(0.75F, weightedAverage(1.0F, 1, 0.5F, 1), 0.001F)
        assertEquals(0.6666667F, weightedAverage(1.0F, 1, 0.5F, 2), 0.001F)
        assertEquals(1.0F, weightedAverage(1.0F, 2, 0.5F, 0), 0.001F)
        assertEquals(0.8333333F, weightedAverage(1.0F, 2, 0.5F, 1), 0.001F)
        assertEquals(0.75F, weightedAverage(1.0F, 2, 0.5F, 2), 0.001F)
        assertEquals(1.0F, weightedAverage(1.0F, 0, 1.0F, 0), 0.001F)
        assertEquals(1.0F, weightedAverage(1.0F, 0, 1.0F, 1), 0.001F)
        assertEquals(1.0F, weightedAverage(1.0F, 0, 1.0F, 2), 0.001F)
        assertEquals(1.0F, weightedAverage(1.0F, 1, 1.0F, 0), 0.001F)
        assertEquals(1.0F, weightedAverage(1.0F, 1, 1.0F, 1), 0.001F)
        assertEquals(1.0F, weightedAverage(1.0F, 1, 1.0F, 2), 0.001F)
        assertEquals(1.0F, weightedAverage(1.0F, 2, 1.0F, 0), 0.001F)
        assertEquals(1.0F, weightedAverage(1.0F, 2, 1.0F, 1), 0.001F)
        assertEquals(1.0F, weightedAverage(1.0F, 2, 1.0F, 2), 0.001F)
    }
}
