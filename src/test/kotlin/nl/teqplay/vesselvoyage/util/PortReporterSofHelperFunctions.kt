package nl.teqplay.vesselvoyage.util

import nl.teqplay.vesselvoyage.model.esof.portreporterview.BerthVisitInfo
import nl.teqplay.vesselvoyage.model.esof.portreporterview.PortAreaInfo
import nl.teqplay.vesselvoyage.model.esof.portreporterview.TerminalVisitInfo
import nl.teqplay.vesselvoyage.model.esof.portreporterview.TugInfo
import nl.teqplay.vesselvoyage.model.lightweight.poma.Berth
import nl.teqplay.vesselvoyage.model.lightweight.poma.Terminal
import nl.teqplay.vesselvoyage.model.v2.AreaActivity
import nl.teqplay.vesselvoyage.model.v2.LocationTime

object PortReporterSofHelperFunctions {

    fun createBerthVisitInfo(
        ref: Int,
        activity: AreaActivity,
        area: Berth? = null,
        portArea: PortAreaInfo? = null,
        arrivalTugs: List<TugInfo> = emptyList(),
        departureTugs: List<TugInfo> = emptyList(),
        firstLineSecured: LocationTime? = null,
        allFast: LocationTime? = null,
        lastLineReleased: LocationTime? = null,
        terminalVisit: TerminalVisitInfo? = null
    ) = BerthVisitInfo(
        ref,
        activity,
        area,
        portArea,
        arrivalTugs,
        departureTugs,
        firstLineSecured,
        allFast,
        lastLineReleased,
        terminalVisit
    )

    fun createTerminalVisitInfo(
        ref: Int,
        activity: AreaActivity,
        mooringActivity: AreaActivity? = null,
        area: Terminal? = null,
        portArea: PortAreaInfo? = null,
        berthVisits: List<BerthVisitInfo> = emptyList()
    ) = TerminalVisitInfo(ref, activity, mooringActivity, area, portArea, berthVisits)
}
