package nl.teqplay.vesselvoyage.util

import nl.teqplay.vesselvoyage.model.internal.TraceItem
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test

class SpeedUtilsTest {

    private val timeline = Timeline()

    @Test
    fun `create from trace`() {
        val trace = listOf(
            traceItem(timeline, speedOverGround = 1.0F),
            traceItem(timeline, speedOverGround = 2.0F),
            traceItem(timeline, speedOverGround = 3.0F),
            traceItem(timeline, speedOverGround = 4.0F),
        )
        val actual = createSpeedOfTraceItemsOrNull(trace, speedOverGroundWhenMissingFirst = null)
        assertNotNull(actual)
        assertEquals(1.0F, actual!!.min)
        assertEquals(4.0F, actual.max)
        assertEquals(2F, actual.avg)
    }

    @Test
    fun `create from trace 2`() {
        val trace = listOf(
            traceItem(timeline, speedOverGround = 1.0F),
            traceItem(timeline, speedOverGround = 2.0F),
            traceItem(timeline, speedOverGround = 3.0F),
            traceItem(timeline, speedOverGround = 4.0F),
            traceItemNoSpeed(timeline)
        )
        val actual = createSpeedOfTraceItemsOrNull(trace, speedOverGroundWhenMissingFirst = null)
        assertNotNull(actual)
        assertEquals(1.0F, actual!!.min)
        assertEquals(4.0F, actual.max)
        assertEquals(2.5F, actual.avg)
    }

    private fun traceItem(timeline: Timeline, speedOverGround: Float? = null, draught: Float? = null): TraceItem {
        val locationTime = timeline.generate()
        return TraceItem(
            location = locationTime.location,
            timestamp = locationTime.time,
            speedOverGround = speedOverGround,
            draught = draught
        )
    }

    private fun traceItemNoSpeed(timeline: Timeline): TraceItem {
        val locationTime = timeline.generate()
        return TraceItem(
            location = locationTime.location,
            timestamp = locationTime.time,
            speedOverGround = null,
            draught = null
        )
    }
}
