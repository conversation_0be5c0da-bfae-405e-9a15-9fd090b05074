package nl.teqplay.vesselvoyage.util

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class ListUtilsTest {
    @Test
    fun `should join a list to a string and truncate it`() {
        val list = listOf(1, 2, 3, 4, 5, 6, 7, 8, 9)

        assertEquals("1, 2, 3, ... and 6 more", list.joinToStringTruncated(3))
    }

    @Test
    fun `should join a list to a string and transform and truncate it`() {
        val list = listOf(Pair("A", 1), Pair("B", 2), Pair("C", 3), Pair("D", 4))

        assertEquals("A, B, C, ... and 1 more", list.joinToStringTruncated(3) { it.first })
    }
}
