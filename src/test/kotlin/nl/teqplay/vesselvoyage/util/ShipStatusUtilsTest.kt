package nl.teqplay.vesselvoyage.util

import nl.teqplay.vesselvoyage.logic.BEANR
import nl.teqplay.vesselvoyage.logic.NLRTM
import nl.teqplay.vesselvoyage.logic.PORT_NLRTM
import nl.teqplay.vesselvoyage.logic.createDestination
import nl.teqplay.vesselvoyage.logic.createPortAreaVisit
import nl.teqplay.vesselvoyage.logic.createPortEventEnd
import nl.teqplay.vesselvoyage.logic.createPortEventStart
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.Change
import nl.teqplay.vesselvoyage.model.Entry
import nl.teqplay.vesselvoyage.model.InitialShipStatus
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.Voyage
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.ZonedDateTime

class ShipStatusUtilsTest {
    @Test
    fun `should return the current entry from a ShipStatus`() {
        val visit1 = createVisit("2021-12-01T00:00:00Z", "2021-12-03T00:00:00Z")
        val voyage = createVoyage("2021-12-03T00:00:00Z", "2021-12-09T00:00:00Z")
        val visit2 = createVisit("2021-12-09T00:00:00Z", "2021-12-10T00:00:00Z")

        assertEquals(
            visit2,
            VisitShipStatus(
                previousVisit = visit1,
                previousVoyage = voyage,
                visit = visit2
            ).getCurrentEntry()
        )

        assertEquals(
            voyage,
            VoyageShipStatus(
                previousVoyage = null,
                previousVisit = visit1,
                voyage = voyage
            ).getCurrentEntry()
        )

        assertEquals(null, InitialShipStatus.getCurrentEntry())
    }
    @Test
    fun `should extract all entries from a ShipStatus`() {
        val voyage1 = createVoyage("2021-11-16T00:00:00Z", "2021-12-01T00:00:00Z")
        val visit1 = createVisit("2021-12-01T00:00:00Z", "2021-12-03T00:00:00Z")
        val voyage2 = createVoyage("2021-12-03T00:00:00Z", "2021-12-09T00:00:00Z")
        val visit2 = createVisit("2021-12-09T00:00:00Z", "2021-12-10T00:00:00Z")

        assertEquals(
            listOf(visit2, voyage2, visit1),
            VisitShipStatus(
                previousVisit = visit1,
                previousVoyage = voyage2,
                visit = visit2
            ).getAllEntries()
        )

        assertEquals(
            listOf(voyage2, visit1, voyage1),
            VoyageShipStatus(
                previousVoyage = voyage1,
                previousVisit = visit1,
                voyage = voyage2
            ).getAllEntries()
        )

        assertEquals(emptyList<Entry>(), InitialShipStatus.getAllEntries())
    }

    private val visit1 = createVisit("2021-12-01T00:00:00Z", "2021-12-03T00:00:00Z")
    private val visit1updated = visit1.copy(
        dest = createDestination(NLRTM, ZonedDateTime.parse("2021-12-03T00:00:00Z"))
    )

    private val voyage = createVoyage("2021-12-03T00:00:00Z", "2021-12-09T00:00:00Z")
    private val voyageUpdated = voyage.copy(
        dest = createDestination(NLRTM, ZonedDateTime.parse("2021-12-03T00:00:00Z"))
    )
    private val voyageUpdatedTwice = voyage.copy(
        dest = createDestination(BEANR, ZonedDateTime.parse("2021-12-03T00:00:01Z"))
    )
    private val visit2 = createVisit("2021-12-09T00:00:00Z", "2021-12-10T00:00:00Z")
    private val visit2updated = visit2.copy(
        dest = createDestination(NLRTM, ZonedDateTime.parse("2021-12-03T00:00:00Z"))
    )

    @Test
    fun `changelist should generate a an empty changelist when nothing changed`() {
        val previousShipStatus = VoyageShipStatus(voyage, visit1, null)
        val nextShipStatus = VoyageShipStatus(voyage, visit1, null)

        assertEquals(listOf<Change>(), createChangeList(previousShipStatus, nextShipStatus))
    }

    @Test
    fun `changelist should generate a change on every changed entry of a VoyageShipStatus`() {
        val previousShipStatus = VoyageShipStatus(voyage, visit1, null)
        val nextShipStatus = VoyageShipStatus(voyageUpdated, visit1updated, null)

        assertEquals(
            listOf(
                Change(Action.UPDATE, visit1updated),
                Change(Action.UPDATE, voyageUpdated)
            ),
            createChangeList(previousShipStatus, nextShipStatus)
        )
    }

    @Test
    fun `changelist should generate a change on every changed entry of a VisitShipStatus`() {
        val previousShipStatus = VisitShipStatus(visit2, voyage, visit1)
        val nextShipStatus = VisitShipStatus(visit2updated, voyageUpdated, visit1updated)

        assertEquals(
            listOf(
                Change(Action.UPDATE, visit1updated),
                Change(Action.UPDATE, voyageUpdated),
                Change(Action.UPDATE, visit2updated)
            ),
            createChangeList(previousShipStatus, nextShipStatus)
        )
    }

    @Test
    fun `changelist should generate a create`() {
        val previousShipStatus = VoyageShipStatus(voyage, visit1, null)
        val nextShipStatus = VisitShipStatus(visit2, voyage, visit1)

        assertEquals(listOf(Change(Action.CREATE, visit2)), createChangeList(previousShipStatus, nextShipStatus))
    }

    @Test
    fun `changelist should only update once to the latest version`() {
        val previousShipStatus = VoyageShipStatus(voyage, visit1, null)
        val nextShipStatus = VoyageShipStatus(voyageUpdated, visit1, null)
        val nextShipStatus2 = VoyageShipStatus(voyageUpdatedTwice, visit1, null)

        assertEquals(
            listOf(Change(Action.UPDATE, voyageUpdatedTwice)),
            createChangeList(previousShipStatus, listOf(nextShipStatus, nextShipStatus2))
        )
    }
}

private fun createVisit(startTime: String, endTime: String?): Visit {
    val portEventStart = createPortEventStart(PORT_NLRTM, startTime)
    val portEventEnd = endTime?.let { createPortEventEnd(PORT_NLRTM, endTime) }

    return nl.teqplay.vesselvoyage.logic.createVisit(
        portAreas = listOf(
            createPortAreaVisit(portEventStart, portEventEnd)
        )
    )
}

private fun createVoyage(startTime: String, endTime: String?): Voyage {
    return nl.teqplay.vesselvoyage.logic.createVoyage(
        startPortIds = listOf(NLRTM),
        startTime = ZonedDateTime.parse(startTime),
        endTime = endTime?.let { ZonedDateTime.parse(endTime) }
    )
}
