package nl.teqplay.vesselvoyage.util

import nl.teqplay.skeleton.model.Location
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import java.time.Duration
import java.time.Instant

/**
 * Helper class to generate [LocationTime] objects, where the time advances with each generated item. A generated
 * item is a copy of [default] with the advanced time set, but can also be overridden when calling the generate
 * functions.
 *
 * @param start Timeline starts with this time
 * @param default The base LocationTime when generating an item, with the time set to [start] and location to 0.0,0.0.
 * @param timeBetweenEvents The amount of time to advance before the new LocationTime is constructed
 */
class Timeline(
    private val start: Instant = Instant.EPOCH,
    private val default: LocationTime = LocationTime(time = start, location = Location(0.0, 0.0), fallback = null),
    private val timeBetweenEvents: Duration = Duration.ofSeconds(60L)
) {
    private var current = start

    private fun advanceAndGet(): Instant {
        current += timeBetweenEvents
        return current
    }

    /** Advances the timeline with [timeBetweenEvents] and generates the [LocationTime] */
    fun generate(item: LocationTime = default) = item.copy(time = advanceAndGet())

    /** [generate]s two items, often used as start + end location time */
    fun generatePair(start: LocationTime = default, end: LocationTime = default): Pair<LocationTime, LocationTime> {
        return generate(start) to generate(end)
    }

    /**
     * Generate an [amount] of location time objects.
     * When using destructuring, note that additional componentN operator functions have been added, up to
     * [component16].
     */
    fun generateN(amount: Int, item: LocationTime = default) = 0.until(amount).map { generate(item) }
}

val Pair<LocationTime, LocationTime>.start: LocationTime
    get() = first

val Pair<LocationTime, LocationTime>.end: LocationTime
    get() = second

operator fun Instant.minus(seconds: Long) = minusSeconds(seconds)
operator fun Instant.plus(seconds: Long) = plusSeconds(seconds)

operator fun LocationTime.minus(seconds: Long) = copy(time = time - seconds)
operator fun LocationTime.plus(seconds: Long) = copy(time = time + seconds)

fun LocationTime.shiftSeconds(amount: Long) = copy(time = time.plusSeconds(amount))

/*
 * Go further than x.component5(), allowing destructuring with more than 5 items:
 * val (time1, time2, time3, time4, time5, time6, ...) = timeline.generateN(..)
 */
operator fun List<LocationTime>.component6() = get(5)
operator fun List<LocationTime>.component7() = get(6)
operator fun List<LocationTime>.component8() = get(7)
operator fun List<LocationTime>.component9() = get(8)
operator fun List<LocationTime>.component10() = get(9)
operator fun List<LocationTime>.component11() = get(10)
operator fun List<LocationTime>.component12() = get(11)
operator fun List<LocationTime>.component13() = get(12)
operator fun List<LocationTime>.component14() = get(13)
operator fun List<LocationTime>.component15() = get(14)
operator fun List<LocationTime>.component16() = get(15)
