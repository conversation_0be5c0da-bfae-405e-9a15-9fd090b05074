package nl.teqplay.vesselvoyage.util

import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.csi.model.ship.info.ShipMetadata
import nl.teqplay.csi.model.ship.info.ShipRegisterInfoCache
import nl.teqplay.csi.model.ship.info.component.ShipCalculated
import nl.teqplay.csi.model.ship.info.component.ShipCategories
import nl.teqplay.csi.model.ship.info.component.ShipIdentifiers
import nl.teqplay.csi.model.ship.info.component.ShipSpecification
import nl.teqplay.csi.model.ship.info.component.ShipTypes
import nl.teqplay.csi.model.ship.mapping.ImoMmsiMapping
import nl.teqplay.csi.model.ship.mapping.ShipRegisterMapping
import nl.teqplay.skeleton.model.Location
import nl.teqplay.vesselvoyage.logic.DEFAULT_START_TIME
import nl.teqplay.vesselvoyage.logic.IMO_1
import nl.teqplay.vesselvoyage.logic.MMSI_1
import nl.teqplay.vesselvoyage.logic.generateUniqueId
import nl.teqplay.vesselvoyage.model.v2.AreaActivity
import nl.teqplay.vesselvoyage.model.v2.Destination
import nl.teqplay.vesselvoyage.model.v2.EntryESoFWrapper
import nl.teqplay.vesselvoyage.model.v2.EntryId
import nl.teqplay.vesselvoyage.model.v2.FallbackType
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewESoF
import nl.teqplay.vesselvoyage.model.v2.NewEncounter
import nl.teqplay.vesselvoyage.model.v2.NewSlowMovingPeriod
import nl.teqplay.vesselvoyage.model.v2.NewStop
import nl.teqplay.vesselvoyage.model.v2.NewStopType
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.ShipToShipTransfer
import java.time.Instant
import java.time.YearMonth
import java.time.ZoneOffset
import java.time.ZonedDateTime

const val DEFAULT_TEST_PORT_ID = "aaaaa-aaaaa-aaaaa"
const val DEFAULT_TEST_EOSP_ID = "$DEFAULT_TEST_PORT_ID.eosp"

const val DEFAULT_TEST_VISIT_ID = "TEST_VISIT_ID"
const val DEFAULT_TEST_VOYAGE_ID = "TEST_VOYAGE_ID"
private val DEFAULT_TEST_PROCESSING_TIME = YearMonth.of(2025, 1)
    .atDay(1)
    .atStartOfDay()
    .atZone(ZoneOffset.UTC)
    .toInstant()

fun createMmsiMapping(from: ZonedDateTime?, to: ZonedDateTime?, mmsi: String): ImoMmsiMapping {
    return ImoMmsiMapping(
        mmsi,
        from?.toEpochMillisecond(),
        null,
        to?.toEpochMillisecond(),
        from?.toEpochMillisecond() ?: to?.toEpochMillisecond() ?: ZonedDateTime.now().toEpochMillisecond(),
        null
    )
}

fun createShipMmsiMapping(imo: String, from: ZonedDateTime, to: ZonedDateTime?, vararg mmsis: String): ShipRegisterMapping {
    return ShipRegisterMapping(
        imo,
        mmsis.map { mmsi -> createMmsiMapping(from, to, mmsi) },
        emptySet(),
        ""
    )
}

fun createShipRegisterInfoCache(
    imo: String?,
    mmsi: String,
    categories: ShipCategories,
    specification: ShipSpecification,
    calculated: ShipCalculated? = null,
    name: String? = null
): ShipRegisterInfoCache {
    return ShipRegisterInfoCache(
        _id = generateUniqueId(),
        identifiers = ShipIdentifiers(
            imo = imo,
            mmsi = mmsi,
            eni = null,
            callSign = null,
            name = name
        ),
        categories = categories,
        specification = specification,
        metadata = ShipMetadata(),
        updatedTime = null,
        calculated = calculated ?: ShipCalculated(),
        types = ShipTypes()
    )
}

fun createAisHistoricMessage(
    mmsi: Int = 0,
    messageTime: Instant = Instant.EPOCH,
    historic: Boolean = false,
    source: String = "SPIRE",
    location: Location = Location(0.0, 0.0),
    heading: Int = 0,
    speedOverGround: Float = 15f,
    courseOverGround: Float = 0f,
    status: AisMessage.ShipStatus = AisMessage.ShipStatus.UNDER_WAY_SAILING,
    draught: Float = 0f,
    eta: Instant = Instant.EPOCH,
    destination: String = "NLRTM",
) = AisHistoricMessage(
    mmsi = mmsi,
    messageTime = messageTime,
    receptionTime = messageTime,
    historic = historic,
    source = source,
    subSource = null,
    messageType = null,
    location = location,
    heading = heading,
    speedOverGround = speedOverGround,
    courseOverGround = courseOverGround,
    status = status,
    imo = null,
    shipType = null,
    draught = draught,
    eta = eta,
    destination = destination,
    transponderPosition = null
)

fun createLocationTimeApi(
    location: Location = Location(0.0, 0.0),
    time: Instant = DEFAULT_START_TIME.toInstant(),
    fallback: FallbackType? = null
): nl.teqplay.vesselvoyage.apiv2.model.LocationTime {
    return nl.teqplay.vesselvoyage.apiv2.model.LocationTime(
        location = location,
        time = time,
        fallback = fallback?.name
    )
}

fun createLocationTime(
    location: Location = Location(0.0, 0.0),
    time: Instant = DEFAULT_START_TIME.toInstant(),
    fallback: FallbackType? = null
): LocationTime {
    return LocationTime(
        location = location,
        time = time,
        fallback = fallback
    )
}

fun createAreaActivity(
    id: String = "TEST_EVENT_ID",
    start: LocationTime = createLocationTime(),
    end: LocationTime? = null,
    areaId: String
): AreaActivity {
    return AreaActivity(
        id = id,
        start = start,
        end = end,
        areaId = areaId
    )
}

fun createAreaActivity(
    startEnd: Pair<LocationTime, LocationTime?>,
    id: String = "TEST_EVENT_ID",
    areaId: String = "TEST_AREA_ID"
): AreaActivity {
    return AreaActivity(
        id = id,
        start = startEnd.first,
        end = startEnd.second,
        areaId = areaId
    )
}

fun createNewVisitShipStatus(
    visit: NewVisit = createNewVisit(),
    visitEsof: NewESoF? = null,
    previousVoyage: NewVoyage? = null,
    previousVoyageEsof: NewESoF? = null,
    previousVisit: NewVisit? = null,
    previousVisitEsof: NewESoF? = null
): NewVisitShipStatus {
    return NewVisitShipStatus(
        visit = EntryESoFWrapper(visit, visitEsof),
        previousVoyage = previousVoyage?.let { EntryESoFWrapper(previousVoyage, previousVoyageEsof) },
        previousVisit = previousVisit?.let { EntryESoFWrapper(previousVisit, previousVisitEsof) }
    )
}

fun createNewVoyageShipStatus(
    voyage: NewVoyage = createNewVoyage(),
    voyageEsof: NewESoF? = null,
    previousVisit: NewVisit? = null,
    previousVisitEsof: NewESoF? = null,
    previousVoyage: NewVoyage? = null,
    previousVoyageEsof: NewESoF? = null
): NewVoyageShipStatus {
    return NewVoyageShipStatus(
        voyage = EntryESoFWrapper(voyage, voyageEsof),
        previousVisit = previousVisit?.let { EntryESoFWrapper(previousVisit, previousVisitEsof) },
        previousVoyage = previousVoyage?.let { EntryESoFWrapper(previousVoyage, previousVoyageEsof) }
    )
}

fun createNewVisit(
    _id: String = DEFAULT_TEST_VISIT_ID,
    imo: Int = IMO_1.toInt(),
    start: LocationTime = createLocationTime(),
    end: LocationTime? = null,
    stops: List<NewStop> = emptyList(),
    passThroughEosp: List<AreaActivity> = emptyList(),
    passThroughPort: List<AreaActivity> = emptyList(),
    destination: Destination? = null,
    eospAreaActivity: AreaActivity = createAreaActivity(areaId = DEFAULT_TEST_EOSP_ID),
    otherOngoingEospAreaActivities: List<AreaActivity> = emptyList(),
    portAreaActivities: List<AreaActivity> = emptyList(),
    anchorAreaActivities: List<AreaActivity> = emptyList(),
    berthAreaActivities: List<AreaActivity> = emptyList(),
    lockAreaActivities: List<AreaActivity> = emptyList(),
    previous: EntryId? = null,
    next: EntryId? = null,
    confirmed: Boolean = false,
    limited: Boolean = false,
    updatedAt: Instant = DEFAULT_TEST_PROCESSING_TIME,
    regenerated: Boolean = false,
): NewVisit {
    return NewVisit(
        _id = _id,
        imo = imo,
        start = start,
        end = end,
        stops = stops,
        passThroughEosp = passThroughEosp,
        passThroughPort = passThroughPort,
        destination = destination,
        previous = previous,
        next = next,
        eospAreaActivity = eospAreaActivity,
        otherOngoingEospAreaActivities = otherOngoingEospAreaActivities,
        portAreaActivities = portAreaActivities.toMutableList(),
        anchorAreaActivities = anchorAreaActivities.toMutableList(),
        berthAreaActivities = berthAreaActivities.toMutableList(),
        lockAreaActivities = lockAreaActivities.toMutableList(),
        confirmed = confirmed,
        limited = limited,
        updatedAt = updatedAt,
        regenerated = regenerated,
    )
}

fun createNewVoyage(
    _id: String = DEFAULT_TEST_VOYAGE_ID,
    imo: Int = IMO_1.toInt(),
    start: LocationTime = createLocationTime(),
    actualStart: LocationTime? = null,
    end: LocationTime? = null,
    stops: List<NewStop> = emptyList(),
    passThroughEosp: List<AreaActivity> = emptyList(),
    destination: Destination? = null,
    previous: EntryId? = null,
    next: EntryId? = null,
    originPort: String? = null,
    destinationPort: String? = null,
    limited: Boolean = false,
    updatedAt: Instant = DEFAULT_TEST_PROCESSING_TIME
): NewVoyage {
    return NewVoyage(
        _id = _id,
        imo = imo,
        start = start,
        actualStart = actualStart,
        end = end,
        stops = stops,
        passThroughEosp = passThroughEosp,
        destination = destination,
        previous = previous,
        next = next,
        originPort = originPort,
        destinationPort = destinationPort,
        limited = limited,
        updatedAt = updatedAt
    )
}

fun createNewStop(
    type: NewStopType,
    startEventId: String = "TEST_STOP_START_EVENT_ID",
    endEventId: String? = null,
    location: Location? = null,
    start: LocationTime = createLocationTime(),
    end: LocationTime? = null,
    areaId: String? = null,
    accuracy: Float? = null
): NewStop {
    return NewStop(
        startEventId = startEventId,
        endEventId = endEventId,
        location = location,
        start = start,
        end = end,
        type = type,
        areaId = areaId,
        accuracy = accuracy
    )
}

fun createNewESoF(
    _id: String,
    encounters: List<NewEncounter> = emptyList(),
    slowMovingPeriods: List<NewSlowMovingPeriod>? = null,
    shipToShipTransfers: List<ShipToShipTransfer> = emptyList(),
    updatedAt: Instant = DEFAULT_TEST_PROCESSING_TIME
): NewESoF {
    return NewESoF(
        _id = _id,
        encounters = encounters,
        slowMovingPeriods = slowMovingPeriods,
        shipToShipTransfers = shipToShipTransfers,
        updatedAt = updatedAt
    )
}

fun createNewEncounter(
    type: EncounterType = EncounterType.UNCLASSIFIED,
    otherMmsi: Int = IMO_1.toInt(),
    otherImo: Int? = MMSI_1.toInt(),
    startEventId: String = "TEST_EVENT_ID",
    start: LocationTime = createLocationTime(),
    end: LocationTime? = null,
): NewEncounter {
    return NewEncounter(
        type = type,
        otherMmsi = otherMmsi,
        otherImo = otherImo,
        startEventId = startEventId,
        start = start,
        end = end
    )
}

fun createNewEncounter(
    type: EncounterType = EncounterType.UNCLASSIFIED,
    startEnd: Pair<LocationTime, LocationTime?>,
    otherMmsi: Int = IMO_1.toInt(),
    otherImo: Int? = MMSI_1.toInt(),
    startEventId: String = "TEST_EVENT_ID"
) = createNewEncounter(
    type = type,
    otherMmsi = otherMmsi,
    otherImo = otherImo,
    startEventId = startEventId,
    start = startEnd.first,
    end = startEnd.second
)

fun createShipToShipTransfer(
    startEnd: Pair<LocationTime, LocationTime?>,
    otherMmsi: Int = MMSI_1.toInt(),
    otherImo: Int? = IMO_1.toInt(),
    areaId: String? = null
) = ShipToShipTransfer(
    otherMmsi = otherMmsi,
    otherImo = otherImo,
    start = startEnd.first,
    end = startEnd.second,
    areaId = areaId,
    startEventId = "TEST_EVENT_ID"
)
