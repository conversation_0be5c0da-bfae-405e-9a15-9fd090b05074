package nl.teqplay.vesselvoyage.util

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class CollectionExtensionsTest {
    @Test
    fun mapWithSurrounding() {
        val base = listOf(1, 2, 3)
        val actual = base.mapWithSurrounding { previous, current, next ->
            current + 1
        }
        val expected = listOf(2, 3, 4)
        assertEquals(expected, actual)
    }
}
