package nl.teqplay.vesselvoyage.util

import nl.teqplay.csi.model.ship.mapping.ImoMmsiMapping
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.ZoneOffset
import java.time.ZonedDateTime

class ImoMmsiMappingUtilsTest {
    private val MMSI_1 = "MMSI_1"
    private val MMSI_2 = "MMSI_2"
    private val MMSI_3 = "MMSI_3"
    private val TIMESTAMP_2019 = LocalDate.ofYearDay(2019, 1).atStartOfDay(ZoneOffset.UTC)
    private val TIMESTAMP_2020 = LocalDate.ofYearDay(2020, 1).atStartOfDay(ZoneOffset.UTC)
    private val TIMESTAMP_2021 = LocalDate.ofYearDay(2021, 1).atStartOfDay(ZoneOffset.UTC)
    private val TIMESTAMP_2022 = LocalDate.ofYearDay(2022, 1).atStartOfDay(ZoneOffset.UTC)

    @Test
    fun `should group mmsi mappings correctly when calling groupOverlapping`() {
        val mapping = listOf(
            createMmsiMapping(MMSI_1, TIMESTAMP_2019),
            createMmsiMapping(MMSI_2, TIMESTAMP_2020, TIMESTAMP_2021),
            createMmsiMapping(MMSI_3, TIMESTAMP_2021)
        )

        val expected = listOf(
            GroupedMapping(setOf(MMSI_1), TIMESTAMP_2019, TIMESTAMP_2020),
            GroupedMapping(setOf(MMSI_1, MMSI_2), TIMESTAMP_2020, TIMESTAMP_2021),
            GroupedMapping(setOf(MMSI_1, MMSI_3), TIMESTAMP_2021, TIMESTAMP_2022)
        )

        assertEquals(expected, mapping.groupOverlapping(TIMESTAMP_2019, TIMESTAMP_2022))
    }

    @Test
    fun `should group mmsi mappings correctly when calling groupOverlapping, only 1 mapping`() {
        val mapping = listOf(
            createMmsiMapping(MMSI_1, TIMESTAMP_2019)
        )

        val expected = listOf(
            GroupedMapping(setOf(MMSI_1), TIMESTAMP_2019, TIMESTAMP_2022)
        )

        assertEquals(expected, mapping.groupOverlapping(TIMESTAMP_2019, TIMESTAMP_2022))
    }

    @Test
    fun `should group mmsi mappings correctly when calling groupOverlapping, group range outside found mappings`() {
        val mapping = listOf(
            createMmsiMapping(MMSI_1, TIMESTAMP_2019)
        )

        val expected = listOf(
            GroupedMapping(setOf(MMSI_1), TIMESTAMP_2021, TIMESTAMP_2022)
        )

        assertEquals(expected, mapping.groupOverlapping(TIMESTAMP_2021, TIMESTAMP_2022))
    }

    @Test
    fun `should group mmsi mappings correctly when calling groupOverlapping, group range only from provided`() {
        val mapping = listOf(
            createMmsiMapping(MMSI_1, TIMESTAMP_2019)
        )

        val expected = GroupedMapping(setOf(MMSI_1), TIMESTAMP_2021, null)

        val groupedMapping = mapping.groupOverlapping(TIMESTAMP_2021).first()

        assertEquals(expected.mmsis, groupedMapping.mmsis)
        assertEquals(expected.from, groupedMapping.from)
    }

    @Test
    fun `should return an empty list when from date is bigger then to date when calling groupOverlapping`() {
        val mapping = listOf(
            createMmsiMapping(MMSI_1, TIMESTAMP_2019)
        )

        val expected = emptyList<GroupedMapping>()

        val groupedMapping = mapping.groupOverlapping(TIMESTAMP_2021, TIMESTAMP_2020)

        assertEquals(expected, groupedMapping)
    }

    @Test
    fun `should ignore mappings when mapping dates are invalid when calling groupOverlapping`() {
        val mapping = listOf(
            createMmsiMapping(MMSI_1, TIMESTAMP_2019),
            createMmsiMapping(MMSI_2, TIMESTAMP_2020, TIMESTAMP_2019)
        )

        val expected = GroupedMapping(setOf(MMSI_1), TIMESTAMP_2021, null)

        val groupedMapping = mapping.groupOverlapping(TIMESTAMP_2021, null).first()

        assertEquals(expected.mmsis, groupedMapping.mmsis)
        assertEquals(expected.from, groupedMapping.from)
    }

    @Test
    fun `should group mmsi mapping correctly when mapping doesn't contain a from and to when calling groupOverlapping`() {
        val mapping = listOf(
            createMmsiMapping(MMSI_1)
        )

        val expected = listOf(
            GroupedMapping(setOf(MMSI_1), TIMESTAMP_2019, TIMESTAMP_2022)
        )

        assertEquals(expected, mapping.groupOverlapping(TIMESTAMP_2019, TIMESTAMP_2022))
    }

    @Test
    fun `should group mmsi mapping correctly when multiple mappings don't contain a from and to when calling groupOverlapping`() {
        val mapping = listOf(
            createMmsiMapping(MMSI_1),
            createMmsiMapping(MMSI_2)
        )

        val expected = listOf(
            GroupedMapping(setOf(MMSI_2, MMSI_1), TIMESTAMP_2019, TIMESTAMP_2022)
        )

        assertEquals(expected, mapping.groupOverlapping(TIMESTAMP_2019, TIMESTAMP_2022))
    }

    private fun createMmsiMapping(mmsi: String, from: ZonedDateTime? = null, to: ZonedDateTime? = null): ImoMmsiMapping {
        return ImoMmsiMapping(
            mmsi,
            from?.toEpochMillisecond(),
            null,
            to?.toEpochMillisecond(),
            from?.toEpochMillisecond() ?: ZonedDateTime.now().toEpochMillisecond(),
            null
        )
    }
}
