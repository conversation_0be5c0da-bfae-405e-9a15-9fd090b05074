package nl.teqplay.vesselvoyage.util

import nl.teqplay.aisengine.event.model.AreaStartEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.Change
import nl.teqplay.vesselvoyage.model.ChangeMetadata
import nl.teqplay.vesselvoyage.model.Location
import nl.teqplay.vesselvoyage.model.OutgoingChange
import nl.teqplay.vesselvoyage.model.PortAreaVisit
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.model.Voyage
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.ZoneOffset
import java.time.ZonedDateTime

class ChangeUtilsTest {
    @Test
    fun `should create a routing key for a change`() {
        val metadata = ChangeMetadata(null, null, null, null)

        assertEquals("VISIT.111111.CREATE", OutgoingChange(Change(Action.CREATE, VISIT_1_START), metadata).getRoutingKey())
        assertEquals("VISIT.111111.UPDATE", OutgoingChange(Change(Action.UPDATE, VISIT_1_START), metadata).getRoutingKey())
        assertEquals("VISIT.111111.DELETE", OutgoingChange(Change(Action.DELETE, VISIT_1_START), metadata).getRoutingKey())

        assertEquals("VOYAGE.111111.CREATE", OutgoingChange(Change(Action.CREATE, VOYAGE_1_START), metadata).getRoutingKey())
        assertEquals("VOYAGE.111111.UPDATE", OutgoingChange(Change(Action.UPDATE, VOYAGE_1_START), metadata).getRoutingKey())
        assertEquals("VOYAGE.111111.DELETE", OutgoingChange(Change(Action.DELETE, VOYAGE_1_START), metadata).getRoutingKey())
    }
}

private const val MMSI_1 = "111111"
private const val IMO_1 = "111111"
private val LOCATION_1 = Location(1.0, 1.0)
private const val PORT_NLRTM = "NLRTM"

private val EVENT_1_START = AreaStartEvent(
    _id = "EVENT_1_START",
    ship = AisShipIdentifier(
        imo = IMO_1.toInt(),
        mmsi = MMSI_1.toInt(),
    ),
    area = AreaIdentifier(
        id = PORT_NLRTM,
        unlocode = PORT_NLRTM,
        type = AreaIdentifier.AreaType.PORT,
    ),
    createdTime = ZonedDateTime.parse("2021-03-08T00:00:00Z").toInstant(),
    actualTime = ZonedDateTime.parse("2021-03-08T00:00:00Z").toInstant(),
    location = nl.teqplay.skeleton.model.Location(1.0, 1.0),
    draught = 1.0F,
    berth = null,
    heading = null,
    speedOverGround = null
)

private val EVENT_1_END = nl.teqplay.aisengine.event.model.AreaEndEvent(
    _id = "EVENT_1_END",
    ship = AisShipIdentifier(
        imo = IMO_1.toInt(),
        mmsi = MMSI_1.toInt(),
    ),
    area = AreaIdentifier(
        id = PORT_NLRTM,
        unlocode = PORT_NLRTM,
        type = AreaIdentifier.AreaType.PORT,
    ),
    createdTime = ZonedDateTime.parse("2021-03-09T04:00:00Z").toInstant(),
    actualTime = ZonedDateTime.parse("2021-03-09T04:00:00Z").toInstant(),
    location = nl.teqplay.skeleton.model.Location(1.0, 1.0),
    draught = 1.0F,
    berth = null,
    heading = null,
    speedOverGround = null,
    startEventId = null
)

private val VISIT_1_START = Visit(
    _id = EVENT_1_START._id,
    mmsi = MMSI_1,
    imo = IMO_1,
    anchorAreas = listOf(),
    portAreas = listOf(
        PortAreaVisit(
            startEventId = EVENT_1_START._id,
            portId = EVENT_1_START.area.id ?: "",
            startTime = EVENT_1_START.createdTime.atZone(ZoneOffset.UTC),
            startLocation = LOCATION_1,
            startDraught = 1.0,
            endEventId = null,
            endTime = null,
            endLocation = null,
            endDraught = null
        )
    ),
    berthAreas = listOf(),
    dest = null,
    eta = null,
    esof = null,
    passThroughAreas = null,
    finished = false,
    previousEntryId = null,
    nextEntryId = null
)

private val VOYAGE_1_START = Voyage(
    _id = EVENT_1_END._id,
    mmsi = MMSI_1,
    imo = IMO_1,

    startPortIds = listOf(EVENT_1_END.area.unlocode ?: ""),
    startTime = EVENT_1_END.createdTime.atZone(ZoneOffset.UTC),

    dest = null,
    eta = null,
    esof = null,
    nonMatchingAnchorAreas = null,
    passThroughAreas = null,

    endPortIds = null,
    endTime = null,

    finished = false,
    previousEntryId = null,
    nextEntryId = null
)
