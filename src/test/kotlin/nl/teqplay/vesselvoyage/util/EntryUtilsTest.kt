package nl.teqplay.vesselvoyage.util

import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.whenever
import nl.teqplay.csi.model.ship.info.ShipRegisterInfoCache
import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2
import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2.BULK_CARRIER
import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2.CONTAINER
import nl.teqplay.skeleton.common.exception.BadRequestException
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.ShipPropertyFilterRequest
import nl.teqplay.vesselvoyage.logic.ANCHOR_AREA_1
import nl.teqplay.vesselvoyage.logic.ANCHOR_AREA_2
import nl.teqplay.vesselvoyage.logic.NLRTM
import nl.teqplay.vesselvoyage.logic.createAnchorAreaVisit
import nl.teqplay.vesselvoyage.logic.createAnchorEventEnd
import nl.teqplay.vesselvoyage.logic.createAnchorEventStart
import nl.teqplay.vesselvoyage.logic.createBerthAreaVisit
import nl.teqplay.vesselvoyage.logic.createStop
import nl.teqplay.vesselvoyage.logic.createUniqueBerthEvent
import nl.teqplay.vesselvoyage.logic.createVoyage
import nl.teqplay.vesselvoyage.model.EventStatus
import nl.teqplay.vesselvoyage.model.Location
import nl.teqplay.vesselvoyage.model.MAX_NON_MATCHING_ANCHOR_AREAS
import nl.teqplay.vesselvoyage.model.MAX_PASS_THROUGH_AREAS
import nl.teqplay.vesselvoyage.model.PortAreaVisit
import nl.teqplay.vesselvoyage.model.StopType
import nl.teqplay.vesselvoyage.model.v2.NewStopType
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.processing.dsl.atLocation
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.Answers
import java.time.Duration
import java.time.YearMonth
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class EntryUtilsTest {
    private val config = EventProcessingProperties(
        maxSpeedMps = 1.0,
        minDuration = Duration.ofMinutes(30),
        newStopDetection = true,
        enableTraceCalculations = true,
        enableSlowMovingPeriods = true,
        enableNewDefinition = false,
        enableOldDefinition = true,
        totalThreads = 5,
        logResults = false,
        activenessMonitoring = EventProcessingProperties.ActivenessMonitoring(
            enabled = false,
            interval = 1000,
            maxIdleTime = Duration.ofMinutes(5)
        )
    )

    @Test
    fun `test updateMatchingPortAreaVisit, match the right portId (1)`() {
        val area1 = createVisitArea("NLRTM", "event1", "2021-03-08T13:00:00Z")
        val area2 = createVisitArea("NLVLA", "event2", "2021-03-08T14:00:00Z")
        val endEvent = createEndEvent("NLVLA", "event3", "2021-03-08T15:00:00Z")
        val portAreas = listOf(area1, area2)

        val expected = listOf(
            area1,
            area2.copy(
                endLocation = endEvent.location.toVesselVoyageLocation(),
                endEventId = endEvent._id,
                endTime = endEvent.createdTime.atZone(ZoneOffset.UTC),
                endDraught = endEvent.draught?.toDouble()
            )
        )

        assertEquals(expected, updateMatchingPortAreaVisit(portAreas, endEvent, "NLVLA"))
    }

    @Test
    fun `test updateMatchingPortAreaVisit, match the right portId (2)`() {
        val area1 = createVisitArea("NLRTM", "event1", "2021-03-08T13:00:00Z")
        val area2 = createVisitArea("NLVLA", "event2", "2021-03-08T14:00:00Z")
        val endEvent = createEndEvent("NLRTM", "event3", "2021-03-08T15:00:00Z")
        val portAreas = listOf(area1, area2)

        val expected = listOf(
            area1.copy(
                endLocation = endEvent.location.toVesselVoyageLocation(),
                endEventId = endEvent._id,
                endTime = endEvent.createdTime.atZone(ZoneOffset.UTC),
                endDraught = endEvent.draught?.toDouble()
            ),
            area2
        )

        assertEquals(expected, updateMatchingPortAreaVisit(portAreas, endEvent, "NLRTM"))
    }

    @Test
    fun `test updateMatchingPortAreaVisit, match the last of multiple portIds`() {
        val area1 = createVisitArea("NLRTM", "event1", "2021-03-08T13:00:00Z")
        val area2 = createVisitArea("NLRTM", "event2", "2021-03-08T14:00:00Z")
        val endEvent = createEndEvent("NLRTM", "event3", "2021-03-08T15:00:00Z")
        val portAreas = listOf(area1, area2)

        val expected = listOf(
            area1,
            area2.copy(
                endLocation = endEvent.location.toVesselVoyageLocation(),
                endEventId = endEvent._id,
                endTime = endEvent.createdTime.atZone(ZoneOffset.UTC),
                endDraught = endEvent.draught?.toDouble()
            )
        )

        assertEquals(expected, updateMatchingPortAreaVisit(portAreas, endEvent, "NLRTM"))
    }

    @Test
    fun `test updateMatchingPortAreaVisit, should not overwrite when already finished`() {
        val area1 = createVisitArea("NLRTM", "event1", "2021-03-08T13:00:00Z")
        val area2Finished = createVisitArea("NLRTM", "event2", "2021-03-08T14:00:00Z").copy(
            endLocation = Location(0.0, 0.0),
            endEventId = "event3",
            endTime = ZonedDateTime.parse("2021-03-08T15:00:00Z"),
            endDraught = null
        )
        val endEvent = createEndEvent("NLRTM", "event5", "2021-03-08T17:00:00Z")
        val portAreas = listOf(area1, area2Finished)

        val expected = listOf(
            area1,
            area2Finished
        )

        assertEquals(expected, updateMatchingPortAreaVisit(listOf(area1, area2Finished), endEvent, "NLRTM"))
    }

    @Test
    fun `test updateMatchingPortAreaVisit, match relatedEvent`() {
        val area1 = createVisitArea("NLRTM", "event1", "2021-03-08T13:00:00Z")
        val area2 = createVisitArea("NLRTM", "event2", "2021-03-08T14:00:00Z")
        val endEvent = createEndEvent("NLRTM", "event3", "2021-03-08T15:00:00Z").copy(
            startEventId = area1.startEventId
        )
        val portAreas = listOf(area1, area2)

        val expected = listOf(
            area1.copy(
                endLocation = endEvent.location.toVesselVoyageLocation(),
                endEventId = endEvent._id,
                endTime = endEvent.createdTime.atZone(ZoneOffset.UTC),
                endDraught = endEvent.draught?.toDouble()
            ),
            area2
        )

        assertEquals(expected, updateMatchingPortAreaVisit(portAreas, endEvent, "NLRTM"))
    }

    @Test
    fun `test updateMatchingAnchorAreaVisit, match the right anchor visit`() {
        val anchorEvent1start = createAnchorEventStart(ANCHOR_AREA_1, "2021-03-09T16:00:00Z", ANCHOR_AREA_1.anchorAreaId)
        val anchorEvent2start = createAnchorEventStart(ANCHOR_AREA_2, "2021-03-09T11:00:00Z", ANCHOR_AREA_2.anchorAreaId)
        val anchorEvent1end = createAnchorEventEnd(ANCHOR_AREA_1, "2021-03-09T16:00:00Z", ANCHOR_AREA_1.anchorAreaId)

        val anchorAreas = listOf(
            createAnchorAreaVisit(anchorEvent1start),
            createAnchorAreaVisit(anchorEvent2start),
        )

        val anchorAreasUpdated = listOf(
            createAnchorAreaVisit(anchorEvent1start, anchorEvent1end),
            createAnchorAreaVisit(anchorEvent2start),
        )

        assertEquals(anchorAreasUpdated, updateMatchingAnchorAreaVisit(anchorAreas, anchorEvent1end))
    }

    @Test
    fun `test updateMatchingBerthAreaVisit, match the right berth visit`() {
        val berthEvent1start = createUniqueBerthEvent(EventStatus.START, "BERTH_1", "2021-03-09T16:00:00Z")
        val berthEvent2start = createUniqueBerthEvent(EventStatus.START, "BERTH_2", "2021-03-09T11:00:00Z")
        val berthEvent1end = createUniqueBerthEvent(EventStatus.END, "BERTH_1", "2021-03-09T16:00:00Z")

        val berthAreas = listOf(
            createBerthAreaVisit(berthEvent1start),
            createBerthAreaVisit(berthEvent2start),
        )

        val berthAreasUpdated = listOf(
            createBerthAreaVisit(berthEvent1start, berthEvent1end),
            createBerthAreaVisit(berthEvent2start),
        )

        assertEquals(berthAreasUpdated, updateMatchingBerthAreaVisit(berthAreas, berthEvent1end, nl.teqplay.vesselvoyage.logic.PORT_NLRTM.portId, "BERTH_1"))
    }

    @Test
    fun `should determine whether a visit area is a pass-through or not`() {
        val location1 = Location(52.00136957796644, 3.997650146484375) // sea entrance 1
        val location2 = Location(52.057979327779506, 3.887786865234375) // sea entrance 2
        val location3 = Location(51.82432022466468, 4.9294281005859375) // inland

        // no end
        assertFalse(
            isPassThrough(
                PortAreaVisit(
                    portId = NLRTM,
                    startEventId = "1",
                    startTime = ZonedDateTime.parse("2021-03-08T00:00:00Z"),
                    startLocation = location1,
                    startDraught = 1.0,
                    endEventId = null,
                    endTime = null,
                    endLocation = null,
                    endDraught = null
                ),
                null, config
            )
        )

        // no pass-through (long duration)
        assertFalse(
            isPassThrough(
                PortAreaVisit(
                    portId = NLRTM,
                    startEventId = "1",
                    startTime = ZonedDateTime.parse("2021-03-08T00:00:00Z"),
                    startLocation = location1,
                    startDraught = 1.0,
                    endEventId = "2",
                    endTime = ZonedDateTime.parse("2021-03-12T00:00:00Z"),
                    endLocation = location2,
                    endDraught = null
                ),
                null, config
            )
        )

        // pass-through due to too short duration (not realistic this speed ;) )
        assertTrue(
            isPassThrough(
                PortAreaVisit(
                    portId = NLRTM,
                    startEventId = "1",
                    startTime = ZonedDateTime.parse("2021-03-08T00:00:00Z"),
                    startLocation = location1,
                    startDraught = 1.0,
                    endEventId = "2",
                    endTime = ZonedDateTime.parse("2021-03-08T00:15:00Z"),
                    endLocation = location3,
                    endDraught = 1.0
                ),
                null, config
            )
        )

        // pass-through due to too high speed
        assertTrue(
            isPassThrough(
                PortAreaVisit(
                    portId = NLRTM,
                    startEventId = "1",
                    startTime = ZonedDateTime.parse("2021-03-08T14:00:00Z"),
                    startLocation = location1,
                    startDraught = 1.0,
                    endEventId = "2",
                    endTime = ZonedDateTime.parse("2021-03-08T16:00:00Z"),
                    endLocation = location3,
                    endDraught = 1.0
                ),
                null, config
            )
        )

        // no pass-through (too short, but not marked as pass-though because of the matching stop)
        val matchingStopFinished = createStop(StopType.BERTH, "2021-03-08T00:01:00Z", "2021-03-08T00:02:00Z")
        assertFalse(
            isPassThrough(
                PortAreaVisit(
                    portId = NLRTM,
                    startEventId = "1",
                    startTime = ZonedDateTime.parse("2021-03-08T00:00:00Z"),
                    startLocation = location1,
                    startDraught = 1.0,
                    endEventId = "2",
                    endTime = ZonedDateTime.parse("2021-03-08T00:15:00Z"),
                    endLocation = location3,
                    endDraught = 1.0
                ),
                listOf(matchingStopFinished), config
            )
        )

        // no pass-through (too short, but not marked as pass-though because of the matching stop)
        val matchingStopUnfinished = createStop(StopType.BERTH, "2021-03-08T00:01:00Z", null)
        assertFalse(
            isPassThrough(
                PortAreaVisit(
                    portId = NLRTM,
                    startEventId = "1",
                    startTime = ZonedDateTime.parse("2021-03-08T00:00:00Z"),
                    startLocation = location1,
                    startDraught = 1.0,
                    endEventId = "2",
                    endTime = ZonedDateTime.parse("2021-03-08T00:15:00Z"),
                    endLocation = location3,
                    endDraught = 1.0
                ),
                listOf(matchingStopUnfinished), config
            )
        )

        // pass-through (too short -> and despite the stop should still be marked as pass through because the stop doesn't overlap)
        val nonMatchingStop = createStop(StopType.BERTH, "2021-03-01T00:00:00Z", "2021-03-02T00:00:00Z")
        assertTrue(
            isPassThrough(
                PortAreaVisit(
                    portId = NLRTM,
                    startEventId = "1",
                    startTime = ZonedDateTime.parse("2021-03-08T00:00:00Z"),
                    startLocation = location1,
                    startDraught = 1.0,
                    endEventId = "2",
                    endTime = ZonedDateTime.parse("2021-03-08T00:15:00Z"),
                    endLocation = location3,
                    endDraught = 1.0
                ),
                listOf(nonMatchingStop), config
            )
        )

        // pass-through (too short -> and despite the stop should still be marked as pass through because the stop is UNCLASSIFIED)
        val unclassifiedMatchingStop = createStop(StopType.UNCLASSIFIED, "2021-03-08T00:01:00Z", "2021-03-08T00:02:00Z")
        assertTrue(
            isPassThrough(
                PortAreaVisit(
                    portId = NLRTM,
                    startEventId = "1",
                    startTime = ZonedDateTime.parse("2021-03-08T00:00:00Z"),
                    startLocation = location1,
                    startDraught = 1.0,
                    endEventId = "2",
                    endTime = ZonedDateTime.parse("2021-03-08T00:15:00Z"),
                    endLocation = location3,
                    endDraught = 1.0
                ),
                listOf(unclassifiedMatchingStop), config
            )
        )
    }

    @Test
    fun `should limit the number of non-matching anchor areas that can be stored in a voyage`() {
        val voyage = createVoyage(
            startPortIds = listOf(NLRTM),
            startTime = ZonedDateTime.parse("2021-12-30T10:00:00Z"),
            endTime = ZonedDateTime.parse("2021-12-31T16:00:00Z"),
            nonMatchingAnchorAreas = List(MAX_NON_MATCHING_ANCHOR_AREAS) {
                createAnchorAreaVisit(
                    anchorArea = ANCHOR_AREA_1,
                    startTime = ZonedDateTime.parse("2021-12-30T00:00:00Z"),
                    endTime = ZonedDateTime.parse("2021-12-30T04:00:00Z")
                )
            }
        )

        val newAnchorAreaVisit = createAnchorAreaVisit(
            anchorArea = ANCHOR_AREA_2,
            startTime = ZonedDateTime.parse("2021-12-30T05:00:00Z"),
            endTime = ZonedDateTime.parse("2021-12-30T07:00:00Z")
        )

        // should not add another area: limit is reached
        val updatedVoyage = voyage.appendAnchorAreas(listOf(newAnchorAreaVisit))

        assertEquals(MAX_NON_MATCHING_ANCHOR_AREAS, updatedVoyage.nonMatchingAnchorAreas?.size)
    }

    @Test
    fun `should limit the number of pass-through areas that can be stored in a voyage`() {
        val voyage = createVoyage(
            startPortIds = listOf(NLRTM),
            startTime = ZonedDateTime.parse("2021-12-30T10:00:00Z"),
            endTime = ZonedDateTime.parse("2021-12-31T16:00:00Z"),
            passThroughAreas = List(MAX_PASS_THROUGH_AREAS) {
                createVisitArea(
                    portId = NLRTM,
                    startEventId = "1",
                    startTime = "2021-12-30T00:00:00Z",
                    endTime = "2021-12-30T04:00:00Z"
                )
            }
        )

        val newPassThroughArea = createVisitArea(
            portId = NLRTM,
            startEventId = "2",
            startTime = "2021-12-30T05:00:00Z",
            endTime = "2021-12-30T07:00:00Z"
        )

        // should not add another area: limit is reached
        val updatedVoyage = voyage.appendPassThroughAreas(listOf(newPassThroughArea))

        assertEquals(MAX_PASS_THROUGH_AREAS, updatedVoyage.passThroughAreas?.size)
    }

    @Test
    fun `should sort merged pass-throughs chronologically`() {
        val passThroughArea1 = createVisitArea(
            portId = NLRTM,
            startEventId = "1",
            startTime = "2021-12-30T00:00:00Z",
            endTime = "2021-12-30T04:00:00Z"
        )
        val passThroughArea2 = createVisitArea(
            portId = NLRTM,
            startEventId = "2",
            startTime = "2021-12-30T05:00:00Z",
            endTime = "2021-12-30T07:00:00Z"
        )

        val voyage = createVoyage(
            startPortIds = listOf(NLRTM),
            startTime = ZonedDateTime.parse("2021-12-30T10:00:00Z"),
            endTime = ZonedDateTime.parse("2021-12-31T16:00:00Z"),
            passThroughAreas = listOf(passThroughArea2)
        )

        val updatedVoyage = voyage.appendPassThroughAreas(listOf(passThroughArea1))

        assertEquals(listOf(passThroughArea1, passThroughArea2), updatedVoyage.passThroughAreas)
    }

    // Simple implementation for testing
    private data class TestShipPropertyFilterRequest(
        override val categories: Set<ShipCategoryV2>?,
        override val minDwt: Int? = null,
        override val maxDwt: Int? = null,
        override val minTeu: Int? = null,
        override val maxTeu: Int? = null
    ) : ShipPropertyFilterRequest

    private fun testShipRegisterInfoCache(
        category: ShipCategoryV2?,
        dwt: Int?,
        teu: Int?
    ): ShipRegisterInfoCache {
        val cache = mock<ShipRegisterInfoCache>(defaultAnswer = Answers.RETURNS_DEEP_STUBS)
        whenever(cache.categories.v2).thenReturn(category)
        whenever(cache.specification.deadWeightTonnage).thenReturn(dwt?.toDouble())
        whenever(cache.specification.twentyFootEquivalentUnit).thenReturn(teu?.toDouble())
        return cache
    }

    private data class ShipCategoryAndRangeCase(
        val requestedCategories: Set<ShipCategoryV2>,
        val shipCategory: ShipCategoryV2?,
        val dwt: Int?,
        val minDwt: Int?,
        val maxDwt: Int?,
        val teu: Int?,
        val minTeu: Int?,
        val maxTeu: Int?,
        val expectedMatch: Boolean
    )

    private fun provideShipCategoryAndRangeCases(): Stream<Arguments> {
        val cases = listOf(
            // Only category filter, should match
            ShipCategoryAndRangeCase(
                requestedCategories = setOf(CONTAINER),
                shipCategory = CONTAINER,
                dwt = 1000, minDwt = null, maxDwt = null,
                teu = 200, minTeu = null, maxTeu = null, expectedMatch = true
            ),
            // Category filter, ship category does not match, should not match
            ShipCategoryAndRangeCase(
                requestedCategories = setOf(BULK_CARRIER),
                shipCategory = CONTAINER,
                dwt = 1000, minDwt = null, maxDwt = null,
                teu = 200, minTeu = null, maxTeu = null, expectedMatch = false
            ),
            // Category + DWT filter, DWT in range, should match (non-container)
            ShipCategoryAndRangeCase(
                requestedCategories = setOf(BULK_CARRIER),
                shipCategory = BULK_CARRIER,
                dwt = 1500, minDwt = 1000, maxDwt = 2000,
                teu = null, minTeu = null, maxTeu = null, expectedMatch = true
            ),
            // Category + DWT filter, DWT below min, should not match (non-container)
            ShipCategoryAndRangeCase(
                requestedCategories = setOf(BULK_CARRIER),
                shipCategory = BULK_CARRIER,
                dwt = 900, minDwt = 1000, maxDwt = 2000,
                teu = null, minTeu = null, maxTeu = null, expectedMatch = false
            ),
            // Category + DWT filter, DWT above max, should not match (non-container)
            ShipCategoryAndRangeCase(
                requestedCategories = setOf(BULK_CARRIER),
                shipCategory = BULK_CARRIER,
                dwt = 2500, minDwt = 1000, maxDwt = 2000,
                teu = null, minTeu = null, maxTeu = null, expectedMatch = false
            ),
            // Category + TEU filter, TEU in range, should match (container)
            ShipCategoryAndRangeCase(
                requestedCategories = setOf(CONTAINER),
                shipCategory = CONTAINER,
                dwt = 1000, minDwt = null, maxDwt = null,
                teu = 300, minTeu = 200, maxTeu = 400, expectedMatch = true
            ),
            // Category + TEU filter, TEU below min, should not match (container)
            ShipCategoryAndRangeCase(
                requestedCategories = setOf(CONTAINER),
                shipCategory = CONTAINER,
                dwt = 1000, minDwt = null, maxDwt = null,
                teu = 100, minTeu = 200, maxTeu = 400, expectedMatch = false
            ),
            // Category + TEU filter, TEU above max, should not match (container)
            ShipCategoryAndRangeCase(
                requestedCategories = setOf(CONTAINER),
                shipCategory = CONTAINER,
                dwt = 1000, minDwt = null, maxDwt = null,
                teu = 500, minTeu = 200, maxTeu = 400, expectedMatch = false
            ),
            // Category + TEU filter, only minTeu set, TEU above min, should match (container)
            ShipCategoryAndRangeCase(
                requestedCategories = setOf(CONTAINER),
                shipCategory = CONTAINER,
                dwt = 1000, minDwt = null, maxDwt = null,
                teu = 300, minTeu = 200, maxTeu = null, expectedMatch = true
            ),
            // Category + TEU filter, only minTeu set, TEU below min, should not match (container)
            ShipCategoryAndRangeCase(
                requestedCategories = setOf(CONTAINER),
                shipCategory = CONTAINER,
                dwt = 1000, minDwt = null, maxDwt = null,
                teu = 100, minTeu = 200, maxTeu = null, expectedMatch = false
            ),
            // Category + TEU filter, only maxTeu set, TEU below max, should match (container)
            ShipCategoryAndRangeCase(
                requestedCategories = setOf(CONTAINER),
                shipCategory = CONTAINER,
                dwt = 1000, minDwt = null, maxDwt = null,
                teu = 100, minTeu = null, maxTeu = 200, expectedMatch = true
            ),
            // Category + TEU filter, only maxTeu set, TEU above max, should not match (container)
            ShipCategoryAndRangeCase(
                requestedCategories = setOf(CONTAINER),
                shipCategory = CONTAINER,
                dwt = 1000, minDwt = null, maxDwt = null,
                teu = 300, minTeu = null, maxTeu = 200, expectedMatch = false
            ),
            // Category + DWT filter, only minDwt set, DWT above min, should match (non-container)
            ShipCategoryAndRangeCase(
                requestedCategories = setOf(BULK_CARRIER),
                shipCategory = BULK_CARRIER,
                dwt = 1500, minDwt = 1000, maxDwt = null,
                teu = null, minTeu = null, maxTeu = null, expectedMatch = true
            ),
            // Category + DWT filter, only minDwt set, DWT below min, should not match (non-container)
            ShipCategoryAndRangeCase(
                requestedCategories = setOf(BULK_CARRIER),
                shipCategory = BULK_CARRIER,
                dwt = 900, minDwt = 1000, maxDwt = null,
                teu = null, minTeu = null, maxTeu = null, expectedMatch = false
            ),
            // Category + DWT filter, only maxDwt set, DWT below max, should match (non-container)
            ShipCategoryAndRangeCase(
                requestedCategories = setOf(BULK_CARRIER),
                shipCategory = BULK_CARRIER,
                dwt = 900, minDwt = null, maxDwt = 1000,
                teu = null, minTeu = null, maxTeu = null, expectedMatch = true
            ),
            // Category + DWT filter, only maxDwt set, DWT above max, should not match (non-container)
            ShipCategoryAndRangeCase(
                requestedCategories = setOf(BULK_CARRIER),
                shipCategory = BULK_CARRIER,
                dwt = 1500, minDwt = null, maxDwt = 1000,
                teu = null, minTeu = null, maxTeu = null, expectedMatch = false
            ),
            // Multiple categories, ship matches one, should match
            ShipCategoryAndRangeCase(
                requestedCategories = setOf(CONTAINER, BULK_CARRIER),
                shipCategory = BULK_CARRIER,
                dwt = 1000, minDwt = null, maxDwt = null,
                teu = 200, minTeu = null, maxTeu = null, expectedMatch = true
            ),
            // Multiple categories, ship matches none, should not match
            ShipCategoryAndRangeCase(
                requestedCategories = setOf(ShipCategoryV2.TANKER, ShipCategoryV2.GENERAL_CARGO),
                shipCategory = CONTAINER,
                dwt = 1000, minDwt = null, maxDwt = null,
                teu = 200, minTeu = null, maxTeu = null, expectedMatch = false
            )
        )
        return cases.stream().map { case ->
            Arguments.of(
                case.requestedCategories,
                case.shipCategory,
                case.dwt, case.minDwt, case.maxDwt,
                case.teu, case.minTeu, case.maxTeu,
                case.expectedMatch
            )
        }
    }

    @ParameterizedTest
    @MethodSource("provideShipCategoryAndRangeCases")
    fun `test shipMatchesCategoryAndOptionalRange permutations`(
        requestedCategories: Set<ShipCategoryV2>,
        shipCategory: ShipCategoryV2?,
        dwt: Int?,
        minDwt: Int?,
        maxDwt: Int?,
        teu: Int?,
        minTeu: Int?,
        maxTeu: Int?,
        expectedMatch: Boolean
    ) {
        val ship = testShipRegisterInfoCache(
            shipCategory,
            dwt,
            teu
        )
        val request = TestShipPropertyFilterRequest(
            categories = requestedCategories,
            minDwt = minDwt,
            maxDwt = maxDwt,
            minTeu = minTeu,
            maxTeu = maxTeu
        )
        val matches = shipMatchesCategoryAndOptionalRange(ship, request)
        assertEquals(expectedMatch, matches)
    }

    @Test
    fun `test shipMatchesCategoryAndOptionalRange when ship cannot be resolved`() {
        val ship: ShipRegisterInfoCache? = null
        val request = TestShipPropertyFilterRequest(categories = setOf(CONTAINER))
        val matches = ship?.let { shipMatchesCategoryAndOptionalRange(it, request) } ?: false
        assertEquals(false, matches)
    }

    @Test
    fun `validateDwtTeu throws when both dwt and teu filters are provided`() {
        val request = TestShipPropertyFilterRequest(
            categories = emptySet(),
            minDwt = 1000,
            maxDwt = 2000,
            minTeu = 100,
            maxTeu = 200
        )
        val exception = assertThrows<BadRequestException> {
            validateDwtTeu(request)
        }
        assertEquals("DWT and TEU filter cannot be applied simultaneously", exception.message)
    }

    @Test
    fun `validateDwtTeu throws when maxDwt is less than minDwt`() {
        val request = TestShipPropertyFilterRequest(
            categories = emptySet(),
            minDwt = 2000,
            maxDwt = 1000
        )
        val exception = assertThrows<BadRequestException> {
            validateDwtTeu(request)
        }
        assertEquals("maxDwt must be bigger than minDwt", exception.message)
    }

    @Test
    fun `validateDwtTeu throws when maxTeu is less than minTeu`() {
        val request = TestShipPropertyFilterRequest(
            categories = emptySet(),
            minTeu = 200,
            maxTeu = 100
        )
        val exception = assertThrows<BadRequestException> {
            validateDwtTeu(request)
        }
        assertEquals("maxTeu must be bigger than minTeu", exception.message)
    }

    @Test
    fun `validateDwtTeu throws when TEU filter is combined with CONTAINER and another category`() {
        val request = TestShipPropertyFilterRequest(
            categories = setOf(CONTAINER, BULK_CARRIER),
            minTeu = 100
        )
        val exception = assertThrows<BadRequestException> {
            validateDwtTeu(request)
        }
        assertEquals("TEU filter can only be combined ship category CONTAINER", exception.message)
    }

    @Test
    fun `validateDwtTeu throws when DWT filter is combined with CONTAINER`() {
        val request = TestShipPropertyFilterRequest(
            categories = setOf(CONTAINER),
            minDwt = 1000
        )
        val exception = assertThrows<BadRequestException> {
            validateDwtTeu(request)
        }
        assertEquals("Cannot combine DWT filter with ship category CONTAINER. Use TEU filter.", exception.message)
    }

    @Test
    fun `validateDwtTeu throws when TEU filter is used without CONTAINER category`() {
        val request = TestShipPropertyFilterRequest(
            categories = setOf(BULK_CARRIER),
            minTeu = 100
        )
        val exception = assertThrows<BadRequestException> {
            validateDwtTeu(request)
        }
        assertEquals("TEU filter can only be combined ship category CONTAINER", exception.message)
    }

    @Test
    fun `should resume visit`() {
        val updateTime = YearMonth.of(2025, 1)
            .atDay(1)
            .atStartOfDay()
            .toInstant(ZoneOffset.UTC)

        val stop1Start = atLocation defaultWithTime "2024-01-01T00:00:00Z"
        val stop1End = atLocation defaultWithTime "2024-01-02T00:00:00Z"
        val stop2Start = atLocation defaultWithTime "2024-01-03T00:00:00Z"
        val stop2Split = atLocation defaultWithTime "2024-01-04T00:00:00Z"
        val stop2End = atLocation defaultWithTime "2024-01-05T00:00:00Z"

        val stop1 = createNewStop(
            type = NewStopType.BERTH,
            startEventId = "STOP_1",
            endEventId = "STOP_1_END",
            start = stop1Start,
            end = stop1End
        )
        val stop2Visit = createNewStop(
            type = NewStopType.BERTH,
            startEventId = "STOP_2",
            endEventId = "STOP_2_END",
            start = stop2Start,
            end = stop2Split
        )
        val stop2Voyage = createNewStop(
            type = NewStopType.BERTH,
            startEventId = "STOP_2",
            endEventId = "STOP_2_END",
            start = stop2Split,
            end = stop2End
        )
        val previousVisit = createNewVisit(
            _id = "VISIT_1",
            stops = listOf(stop1, stop2Visit),
            start = stop1Start,
            end = stop2Split,
            next = "VOYAGE_2"
        )
        val canceledVoyage = createNewVoyage(
            _id = "VOYAGE_2",
            stops = listOf(stop2Voyage),
            start = stop2Split,
            previous = "VISIT_1"
        )

        val result = resumeVisit(
            currentVoyage = canceledVoyage,
            previousVisit = previousVisit,
            updateTime = updateTime
        )

        val expectedStop2 = createNewStop(
            type = NewStopType.BERTH,
            startEventId = "STOP_2",
            endEventId = "STOP_2_END",
            start = stop2Start,
            end = stop2End
        )
        val expected = createNewVisit(
            _id = "VISIT_1",
            stops = listOf(stop1, expectedStop2),
            start = stop1Start,
            end = null,
            next = null
        )
        assertEquals(expected, result)
    }

    @Test
    fun `should resume voyage`() {
        val updateTime = YearMonth.of(2025, 1)
            .atDay(1)
            .atStartOfDay()
            .toInstant(ZoneOffset.UTC)

        val stop1Start = atLocation defaultWithTime "2024-01-01T00:00:00Z"
        val stop1End = atLocation defaultWithTime "2024-01-02T00:00:00Z"
        val stop2Start = atLocation defaultWithTime "2024-01-03T00:00:00Z"
        val stop2Split = atLocation defaultWithTime "2024-01-04T00:00:00Z"
        val stop2End = atLocation defaultWithTime "2024-01-05T00:00:00Z"
        val visitEnd = atLocation defaultWithTime "2024-01-06T00:00:00Z"

        val stop1 = createNewStop(
            type = NewStopType.BERTH,
            startEventId = "STOP_1",
            endEventId = "STOP_1_END",
            start = stop1Start,
            end = stop1End
        )
        val stop2Voyage = createNewStop(
            type = NewStopType.BERTH,
            startEventId = "STOP_2",
            endEventId = "STOP_2_END",
            start = stop2Start,
            end = stop2Split
        )
        val stop2Visit = createNewStop(
            type = NewStopType.BERTH,
            startEventId = "STOP_2",
            endEventId = "STOP_2_END",
            start = stop2Split,
            end = stop2End
        )
        val previousVoyage = createNewVoyage(
            _id = "VOYAGE_1",
            stops = listOf(stop1, stop2Visit),
            start = stop1Start,
            end = stop2Split,
            next = "VISIT_2"
        )
        val canceledVisit = createNewVisit(
            _id = "VISIT_2",
            stops = listOf(stop2Voyage),
            start = stop2Split,
            eospAreaActivity = createAreaActivity(
                start = stop2Split,
                areaId = DEFAULT_TEST_EOSP_ID
            ),
            previous = "VOYAGE_1"
        )

        val result = resumeVoyage(
            currentVisit = canceledVisit,
            previousVoyage = previousVoyage,
            passThroughEndTime = visitEnd,
            updateTime = updateTime
        )

        val expectedStop2 = createNewStop(
            type = NewStopType.BERTH,
            startEventId = "STOP_2",
            endEventId = "STOP_2_END",
            start = stop2Start,
            end = stop2End
        )
        val expectedPassThrough = createAreaActivity(
            start = stop2Split,
            end = visitEnd,
            areaId = DEFAULT_TEST_EOSP_ID
        )
        val expected = createNewVoyage(
            _id = "VOYAGE_1",
            stops = listOf(stop1, expectedStop2),
            passThroughEosp = listOf(expectedPassThrough),
            start = stop1Start,
            end = null,
            next = null
        )
        assertEquals(expected, result)
    }
}

private fun createVisitArea(portId: String, startEventId: String, startTime: String, endTime: String? = null) = PortAreaVisit(
    portId = portId,
    startEventId = startEventId,
    startTime = ZonedDateTime.parse(startTime),
    startLocation = Location(0.0, 0.0),
    startDraught = 1.0,
    endEventId = null,
    endTime = endTime?.let { ZonedDateTime.parse(it) },
    endLocation = null,
    endDraught = null
)

private fun createEndEvent(portId: String, eventId: String, eventTime: String) = nl.teqplay.aisengine.event.model.AreaEndEvent(
    _id = eventId,
    ship = nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier(
        imo = 1,
        mmsi = 1,
    ),
    area = nl.teqplay.aisengine.event.model.identifier.AreaIdentifier(
        id = portId,
        unlocode = portId,
        type = nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType.PORT,
    ),
    createdTime = ZonedDateTime.parse(eventTime).toInstant(),
    actualTime = ZonedDateTime.parse(eventTime).toInstant(),
    location = nl.teqplay.skeleton.model.Location(0.0, 0.0),
    draught = 1.0F,
    berth = null,
    heading = null,
    speedOverGround = null,
    startEventId = null
)
