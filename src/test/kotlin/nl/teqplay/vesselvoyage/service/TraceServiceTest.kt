package nl.teqplay.vesselvoyage.service

import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.whenever
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.csi.model.ship.mapping.ImoMmsiMapping
import nl.teqplay.vesselvoyage.client.AisHistoryClient
import nl.teqplay.vesselvoyage.datasource.HistoricTraceDataSource
import nl.teqplay.vesselvoyage.datasource.OngoingTraceDataSource
import nl.teqplay.vesselvoyage.datasource.VisitDataSource
import nl.teqplay.vesselvoyage.datasource.VoyageDataSource
import nl.teqplay.vesselvoyage.logic.MMSI_1
import nl.teqplay.vesselvoyage.logic.MMSI_2
import nl.teqplay.vesselvoyage.logic.PORT_NLRTM
import nl.teqplay.vesselvoyage.logic.createPortAreaVisit
import nl.teqplay.vesselvoyage.logic.createPortEventEnd
import nl.teqplay.vesselvoyage.logic.createPortEventStart
import nl.teqplay.vesselvoyage.logic.createVisit
import nl.teqplay.vesselvoyage.model.LocationTime
import nl.teqplay.vesselvoyage.properties.AisFetchingProperties
import nl.teqplay.vesselvoyage.properties.TraceProperties
import nl.teqplay.vesselvoyage.util.toEpochMillisecond
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import java.time.Duration
import java.time.ZonedDateTime

class TraceServiceTest {

    @Test
    fun `test historic trace retrieving of finished visit should be null when not found`() {
        val (traceService, _, _, _, _, _) = createMockTraceService()

        val now = ZonedDateTime.now()
        val yesterday = now.minusDays(1)

        val finishedVisit = createVisit(
            portAreas = listOf(
                createPortAreaVisit(PORT_NLRTM, yesterday, now)
            ),
            finished = true
        )

        val result = traceService.getByEntry(finishedVisit)

        assertNull(result)
    }

    @Test
    fun `test historic trace retrieving of unfinished old visit should be null when not found`() {
        val (traceService, _, _, _, _, _) = createMockTraceService()

        val now = ZonedDateTime.now()
            .minusDays(60)

        val finishedVisit = createVisit(
            portAreas = listOf(
                createPortAreaVisit(PORT_NLRTM, now, null)
            ),
            finished = false
        )

        val result = traceService.getByEntry(finishedVisit)

        assertNull(result)
    }

    @Test
    fun `test getHistoricTraceFromAIS - should not crash on no imo mmsi mapping`() {
        val (traceService, _, _, aisHistoryClient, _, staticShipInfoService) = createMockTraceService()

        whenever(staticShipInfoService.getMmsiMappingFromImoAndDateRange(any(), any(), any()))
            .thenAnswer { emptyList<ImoMmsiMapping>() }
        whenever(aisHistoryClient.queryAisHistory(any(), any()))
            .thenAnswer { emptyList<AisHistoricMessage>() }

        val visit = createVisit(
            portAreas = listOf(
                createPortAreaVisit(
                    createPortEventStart(PORT_NLRTM, "2022-02-11T00:00:00.000Z"),
                    createPortEventEnd(PORT_NLRTM, "2022-02-14T00:00:00Z")
                )
            ),
            finished = true
        )

        val trace = traceService.createHistoricTraceFromAIS(visit)

        assertEquals(emptyList<LocationTime>(), trace?.locations)
    }

    @Test
    fun `test getHistoricTraceFromAIS - should not crash when imo mmsi mapping is partically incorrect`() {
        val (traceService, _, _, aisHistoryClient, _, staticShipInfoService) = createMockTraceService()

        whenever(staticShipInfoService.getMmsiMappingFromImoAndDateRange(any(), any(), any()))
            .thenAnswer {
                listOf(
                    createMmsiMapping(MMSI_1.toString(), ZonedDateTime.parse("2022-01-01T00:00:00Z"), ZonedDateTime.parse("2022-01-02T00:00:00Z")),
                    createMmsiMapping(MMSI_2.toString(), ZonedDateTime.parse("2022-03-01T00:00:00Z"))
                )
            }
        whenever(aisHistoryClient.queryAisHistory(any(), any()))
            .thenAnswer { emptyList<AisHistoricMessage>() }

        val visit = createVisit(
            portAreas = listOf(
                createPortAreaVisit(
                    createPortEventStart(PORT_NLRTM, "2022-02-11T00:00:00.000Z"),
                    createPortEventEnd(PORT_NLRTM, "2022-02-14T00:00:00Z")
                )
            ),
            finished = true
        )

        val trace = traceService.createHistoricTraceFromAIS(visit)

        assertEquals(emptyList<LocationTime>(), trace?.locations)
    }
}

private fun createMockTraceService(): MockTraceServiceWrapper {
    val traceProperties = TraceProperties(
        tolerance = 0.0001,
        ongoing = TraceProperties.Ongoing(
            maxAge = Duration.ofDays(30),
            maxNonSimplifiedSize = 50,
            maxSize = 100,
            persistInterval = "900000"
        ),
        historic = TraceProperties.Historic(
            generateWhenFinished = true,
            finished = TraceProperties.Historic.Finished(
                createFromAis = false
            ),
            unfinished = TraceProperties.Historic.Unfinished(
                createFromAis = false
            )
        ),
        enableNewDefinition = false,
        enableOldDefinition = true,
        totalThreads = 5,
        maxRequestLengthDays = 90,
        activenessMonitoring = TraceProperties.ActivenessMonitoring(
            enabled = false,
            interval = 1000,
            maxIdleTime = Duration.ofMinutes(5)
        )
    )
    val aisFetchingProperties = AisFetchingProperties(
        maxDays = 99999,
        chunkDuration = Duration.ofDays(1)
    )
    val historicTraceDataSource: HistoricTraceDataSource = mock()
    val ongoingTraceDataSource: OngoingTraceDataSource = mock()
    val visitDataSource: VisitDataSource = mock()
    val voyageDataSource: VoyageDataSource = mock()
    val aisHistoryClient: AisHistoryClient = mock()
    val shipHistoryService = ShipHistoryService(aisFetchingProperties, aisHistoryClient)
    val staticShipInfoService: StaticShipInfoService = mock()
    val applicationScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    val aisFetchingService = mock<AisFetchingService>()

    val v1TraceService = V1TraceService(
        traceProperties = traceProperties,
        aisFetchingProperties = aisFetchingProperties,
        historicTraceDataSource = historicTraceDataSource,
        ongoingTraceDataSource = ongoingTraceDataSource,
        shipHistoryService = shipHistoryService,
        staticShipInfoService = staticShipInfoService,
        visitDataSource = visitDataSource,
        voyageDataSource = voyageDataSource,
        applicationScope = applicationScope,
        aisFetchingService = aisFetchingService,
        slackMessageService = null,
    )

    return MockTraceServiceWrapper(
        v1TraceService,
        historicTraceDataSource,
        ongoingTraceDataSource,
        aisHistoryClient,
        shipHistoryService,
        staticShipInfoService
    )
}

private data class MockTraceServiceWrapper(
    val v1TraceService: V1TraceService,
    val historicTraceDataSource: HistoricTraceDataSource,
    val ongoingTraceDataSource: OngoingTraceDataSource,
    val aisHistoryClient: AisHistoryClient,
    val shipHistoryService: ShipHistoryService,
    val staticShipInfoService: StaticShipInfoService
)

private fun createMmsiMapping(mmsi: String, from: ZonedDateTime, to: ZonedDateTime? = null): ImoMmsiMapping {
    return ImoMmsiMapping(
        mmsi,
        from.toEpochMillisecond(),
        null,
        to?.toEpochMillisecond(),
        from.toEpochMillisecond(),
        null
    )
}
