package nl.teqplay.vesselvoyage.service

import com.github.seratch.jslack.Slack
import com.github.seratch.jslack.api.model.Attachment
import com.github.seratch.jslack.api.webhook.Payload
import com.github.seratch.jslack.api.webhook.WebhookResponse
import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.eq
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.never
import com.nhaarman.mockitokotlin2.verify
import com.nhaarman.mockitokotlin2.whenever
import nl.teqplay.vesselvoyage.properties.HealthProperties
import org.junit.jupiter.api.Test
import java.io.IOException

class SlackMessageServiceTest {
    private val testWebhook = "TEST_WEBHOOK"
    private val testTitle = "TEST_TITLE"
    private val testText = "TEST_TEXT"
    private val properties = mock<HealthProperties>().also {
        whenever(it.slack).thenReturn(HealthProperties.Slack(testWebhook))
    }
    private val slackClient = mock<Slack>()
    private val service = SlackMessageService(
        properties = properties,
        slackClient = slackClient
    )

    @Test
    fun `should create payload as intended`() {
        val webhookResponse = WebhookResponse.builder()
            .message("testing message")
            .body("testing body")
            .code(200)
            .build()

        whenever(slackClient.send(eq(testWebhook), any<Payload>()))
            .thenReturn(webhookResponse)

        service.sendMessage(
            title = testTitle,
            text = testText,
            barColor = SLACK_COLOR_GREEN
        )

        val expectedPayload = with(Payload.builder()) {
            val testAttachment = with(Attachment.builder()) {
                title(testTitle)
                fallback(testTitle)
                text(testText)
                color(SLACK_COLOR_GREEN)
            }.build()

            attachments(listOf(testAttachment))
        }.build()

        verify(slackClient).send(testWebhook, expectedPayload)
    }

    @Test
    fun `should not crash when not getting 200 status`() {
        val webhookResponse = WebhookResponse.builder()
            .message("error message")
            .body("error body")
            .code(503)
            .build()
        whenever(slackClient.send(eq(testWebhook), any<Payload>())).thenReturn(webhookResponse)
        service.sendMessage(
            title = testTitle,
            text = testText,
            barColor = SLACK_COLOR_GREEN
        )

        val expectedPayload = with(Payload.builder()) {
            val testAttachment = with(Attachment.builder()) {
                title(testTitle)
                fallback(testTitle)
                text(testText)
                color(SLACK_COLOR_GREEN)
            }.build()

            attachments(listOf(testAttachment))
        }.build()

        verify(slackClient).send(testWebhook, expectedPayload)
    }

    @Test
    fun `should not crash when throwing exception`() {
        whenever(slackClient.send(eq(testWebhook), any<Payload>())).thenThrow(IOException::class.java)
        service.sendMessage(
            title = testTitle,
            text = testText,
            barColor = SLACK_COLOR_GREEN
        )

        val expectedPayload = with(Payload.builder()) {
            val testAttachment = with(Attachment.builder()) {
                title(testTitle)
                fallback(testTitle)
                text(testText)
                color(SLACK_COLOR_GREEN)
            }.build()

            attachments(listOf(testAttachment))
        }.build()

        verify(slackClient).send(testWebhook, expectedPayload)
    }

    @Test
    fun `should not send message when having no slack client`() {
        val service = SlackMessageService(
            properties = properties,
            slackClient = null
        )

        service.sendMessage(testTitle, testText, SLACK_COLOR_GREEN)
        verify(slackClient, never()).send(any(), any<Payload>())
    }
}
