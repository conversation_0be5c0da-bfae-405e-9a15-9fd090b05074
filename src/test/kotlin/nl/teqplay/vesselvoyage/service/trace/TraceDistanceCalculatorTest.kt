package nl.teqplay.vesselvoyage.service.trace

import nl.teqplay.skeleton.model.Location
import nl.teqplay.vesselvoyage.model.v2.TraceDistance
import nl.teqplay.vesselvoyage.util.appendToTraceDistance
import nl.teqplay.vesselvoyage.util.createTraceDistanceBetween
import nl.teqplay.vesselvoyage.util.createTraceDistanceFrom
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test

class TraceDistanceCalculatorTest {

    private val location1 = Location(0.0, 1.0)
    private val location2 = Location(0.0, 2.0)
    private val location3 = Location(0.0, 3.0)
    private val location4 = Location(0.0, 4.0)
    private val location5 = Location(0.0, 5.0)
    private val allLocations = listOf(location1, location2, location3, location4, location5)

    @Test
    fun between() {
        val result = createTraceDistanceBetween(location1, location2)
        assertEquals(111319, result.distanceMeters)
        assertEquals(location2, result.lastLocation)
    }

    @Test
    fun createFromLocation() {
        val result = createTraceDistanceFrom(location1)
        assertEquals(0, result.distanceMeters)
        assertEquals(location1, result.lastLocation)
    }

    @Test
    fun createFromLocationList() {
        // empty list
        assertNull(createTraceDistanceFrom(emptyList()))

        // single item
        with(createTraceDistanceFrom(listOf(location1))) {
            assertNotNull(this)
            this!!
            assertEquals(0, distanceMeters)
            assertEquals(location1, lastLocation)
        }

        // multiple items
        with(createTraceDistanceFrom(allLocations)) {
            assertNotNull(this)
            this!!
            assertEquals(445276, distanceMeters)
            assertEquals(location5, lastLocation)
        }
    }

    @Test
    fun appendLocation() {
        // location1 + location2
        val base = TraceDistance(distanceMeters = 111319, lastLocation = location2)

        // appending empty list should result in no change
        assertEquals(base, appendToTraceDistance(base, emptyList()))

        // appending 1 item
        with(appendToTraceDistance(base, listOf(location3))) {
            assertEquals(222638, distanceMeters)
            assertEquals(location3, lastLocation, "last appended location is not lastLocation")
        }

        // multiple items
        with(appendToTraceDistance(base, listOf(location3, location4, location5))) {
            assertEquals(445276, distanceMeters)
            assertEquals(location5, lastLocation, "last appended location is not lastLocation")
        }
    }
}
