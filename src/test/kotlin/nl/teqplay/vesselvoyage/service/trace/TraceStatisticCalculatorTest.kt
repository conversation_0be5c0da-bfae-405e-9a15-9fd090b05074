package nl.teqplay.vesselvoyage.service.trace

import nl.teqplay.vesselvoyage.model.internal.TraceItem
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.TraceStatistic
import nl.teqplay.vesselvoyage.util.Timeline
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.fail
import org.junit.jupiter.api.Test
import java.time.Duration

class TraceStatisticCalculatorTest {

    private val timeline = Timeline()
    private val traceItem1 = traceItem(timeline.generate(), draught = 1.0f)
    private val traceItem2 = traceItem(timeline.generate(), draught = 2.0f)
    private val traceItem3 = traceItem(timeline.generate(), draught = 3.0f)
    private val traceItem4 = traceItem(timeline.generate(), draught = 4.0f)
    private val traceItem5 = traceItem(timeline.generate(), draught = 5.0f)
    private val traceItem6 = traceItem(timeline.generate(), draught = 6.0f)

    /** Trace item 1-3 */
    private val traceStatistic123 = TraceStatistic(
        min = traceItem1.draught!!,
        max = traceItem3.draught!!,
        avg = 1.5f, // weighted average: ((1*60)+(2*60)+(3*0)) = 60+120+0 = 180 / 120 = 1.5
        last = traceItem3.draught!!,
        duration = Duration.between(traceItem1.timestamp, traceItem3.timestamp),
        lastTimestamp = traceItem3.timestamp
    )

    /** Trace item 1-4 */
    private val traceStatistic1234 = TraceStatistic(
        min = traceItem1.draught!!,
        max = traceItem4.draught!!,
        avg = 2.0f, // weighted average: ((1*60)+(2*60)+(3*60)+(4*0)) = 60+120+180+0 = 360 / 180 = 2.0
        last = traceItem4.draught!!,
        duration = Duration.between(traceItem1.timestamp, traceItem4.timestamp),
        lastTimestamp = traceItem4.timestamp
    )

    /** Trace item 1-5 */
    private val traceStatistic12345 = TraceStatistic(
        min = traceItem1.draught!!,
        max = traceItem5.draught!!,
        avg = 2.5f, // weighted average: ((1*60)+(2*60)+(3*60)+(4*60)+(5*0)) = 60+120+180+240+0 = 600 / 240 = 2.0
        last = traceItem5.draught!!,
        duration = Duration.between(traceItem1.timestamp, traceItem5.timestamp),
        lastTimestamp = traceItem5.timestamp
    )

    /** Trace item 4-6 */
    private val traceStatistic456 = TraceStatistic(
        min = traceItem4.draught!!,
        max = traceItem6.draught!!,
        avg = 4.5f, // weighted average: ((4*60)+(5*60)+(6*0)) = 240+300+0 = 540 / 120 = 4.5
        last = traceItem6.draught!!,
        duration = Duration.between(traceItem4.timestamp, traceItem6.timestamp),
        lastTimestamp = traceItem6.timestamp
    )

    @Test
    fun createFromValueAndTimestamp() {
        val draught = 2.0f
        val timestamp = timeline.generate()
        val result = createTraceStatistic(draught, timestamp.time)
        assertEquals(draught, result.min, 0.1f, "expected 'min' to be given value")
        assertEquals(draught, result.max, 0.1f, "expected 'max' to be given value")
        assertEquals(draught, result.avg, 0.1f, "expected 'avg' to be given value")
        assertEquals(draught, result.last, 0.1f, "expected 'last' to be given value")
        assertEquals(Duration.ZERO, result.duration, "expected 0 duration, as based on one data point")
        assertEquals(timestamp.time, result.lastTimestamp)
    }

    @Test
    fun `createFromTraceItems - null when no draught information in list`() {
        assertNull(createTraceStatisticFromTraceItemsOrNull(emptyList(), TraceItem::draught))
        assertNull(
            createTraceStatisticFromTraceItemsOrNull(
                items = listOf(traceItem(timeline.generate())),
                valueProperty = TraceItem::draught
            )
        )
        assertNull(
            createTraceStatisticFromTraceItemsOrNull(
                items = listOf(traceItem(timeline.generate()), traceItem(timeline.generate())),
                valueProperty = TraceItem::draught
            )
        )
    }

    @Test
    fun `createFromTraceItems - single item with draught, no list remainder`() {
        // consider this list, using draught as the example value:
        // 1 - draught: null
        // 2 - draught: 1.0
        // Item 1 is discarded because the system starts calculating with the first item having a draught value.
        val (time1, time2) = timeline.generateN(2)
        val list = listOf(
            traceItem(time1, draught = null),
            traceItem(time2, draught = 1.0f),
        )
        val actual = createTraceStatisticFromTraceItemsOrNull(list, TraceItem::draught)!!
        val expected = TraceStatistic(
            min = 1.0f,
            max = 1.0f,
            avg = 1.0f,
            duration = Duration.ZERO,
            last = 1.0f,
            lastTimestamp = time2.time
        )
        assertEqualsTraceStatistic(expected, actual)
    }

    @Test
    fun `createFromTraceItems - single item with draught, with draught null as list remainder`() {
        // consider this list, using draught as the example value (same as previous function but more in list tail)
        // 1 - draught: null
        // 2 - draught: 1.0
        // 3 - draught: null
        // 4 - draught: null
        // the trace statistic is calculated using item 2
        // Item 1 is discarded because the system starts calculating with the first item having a draught value.
        val (time1, time2, time3) = timeline.generateN(3)
        val list = listOf(
            traceItem(time1, draught = null),
            traceItem(time2, draught = 1.0f),
            traceItem(time3, draught = null),
        )
        val actual = createTraceStatisticFromTraceItemsOrNull(list, TraceItem::draught)!!
        val expected = TraceStatistic(
            min = 1.0f,
            max = 1.0f,
            avg = 1.0f,
            duration = Duration.between(time2.time, time3.time),
            last = 1.0f,
            lastTimestamp = time3.time
        )
        assertEqualsTraceStatistic(expected, actual)
    }

    @Test
    fun `createFromTraceItems - with draught as list remainder`() {
        // consider this list, using draught as the example value (same as previous function but more in list tail)
        // 1 - draught: null
        // 2 - draught: 1.0
        // 3 - draught: null
        // 4 - draught: 2.0
        // Item 1 is discarded because the system starts calculating with the first item having a draught value.
        val (time1, time2, time3, time4) = timeline.generateN(4)
        val list = listOf(
            traceItem(time1, draught = null),
            traceItem(time2, draught = 1.0f),
            traceItem(time3, draught = null),
            traceItem(time4, draught = 2.0f),
        )
        val actual = createTraceStatisticFromTraceItemsOrNull(list, TraceItem::draught)
        val expected = TraceStatistic(
            min = 1.0f,
            max = 2.0f,
            avg = 1.0f, // 2.0f has no duration, therefore no weight in the average
            duration = Duration.between(time2.time, time4.time),
            last = 2.0f,
            lastTimestamp = time4.time
        )
        assertEqualsTraceStatistic(expected, actual)
    }

    @Test
    fun `appendFromTraceItems - new items empty list returns existing unchanged`() {
        assertEqualsTraceStatistic(
            expected = traceStatistic123,
            actual = appendTraceStatisticFromTraceItems(
                existing = traceStatistic123,
                newItems = emptyList(),
                valueProperty = TraceItem::draught
            )
        )
    }

    @Test
    fun `appendFromTraceItems - new items without draught add duration to last draught`() {

        // take trace items 1+2+3 and append 4+5 without draught
        val existing = traceStatistic123
        val newItems = listOf(
            traceItem4.copy(draught = null),
            traceItem5.copy(draught = null)
        )
        val expected = TraceStatistic(
            min = existing.min, // no change, 4 and 5 have no draught set
            max = existing.max, // no change, 4 and 5 have no draught set
            avg = 2.25f, // this changes 1.5 -> 2.25 because the weight (duration) of 'last' changes with appending 4+5
            last = existing.last, // no change, 4 and 5 have no draught set
            lastTimestamp = traceItem5.timestamp,
            duration = Duration.between(traceItem1.timestamp, traceItem5.timestamp)
        )
        assertEqualsTraceStatistic(expected, appendTraceStatisticFromTraceItems(existing, newItems, TraceItem::draught))
    }

    @Test
    fun `appendFromTraceItems - new items with and without draught`() {

        // take trace items 1+2+3 and append 4+5 without draught
        val existing = traceStatistic123
        val newItems = listOf(
            traceItem4,
            traceItem5.copy(draught = null)
        )
        // the average changes of traceItem5 is set to null, but the duration is appended
        // meaning the duration between trace items 4 and 5 is taken into account in the weighted average
        val expected = TraceStatistic(
            min = traceItem1.draught!!,
            max = traceItem4.draught!!,
            avg = 2.5f, // weighted average: ((1*60)+(2*60)+(3*60)+(4*60)+(null*0)) = 60+120+180+240 = 600 / 240 = 2.5
            last = traceItem4.draught!!,
            duration = Duration.between(traceItem1.timestamp, traceItem5.timestamp),
            lastTimestamp = traceItem5.timestamp
        )
        assertEqualsTraceStatistic(expected, appendTraceStatisticFromTraceItems(existing, newItems, TraceItem::draught))
    }

    @Test
    fun `appendFromTraceItems - new items without and with draught`() {
        // this tests the reverse (newItems draught=null) of the function above

        // take trace items 1+2+3 and append 4+5 without draught
        val existing = traceStatistic123
        val newItems = listOf(
            traceItem4.copy(draught = null),
            traceItem5
        )
        // the average changes of traceItem5 is set to null, but the duration is appended
        // meaning the duration between trace items 4 and 5 is taken into account in the weighted average
        val expected = TraceStatistic(
            min = traceItem1.draught!!,
            max = traceItem5.draught!!,
            avg = 2.25f, // weighted average: ((1*60)+(2*60)+(3*120)+(5*0)) = 60+120+360+0 / 240 = 2.25
            last = traceItem5.draught!!,
            duration = Duration.between(traceItem1.timestamp, traceItem5.timestamp),
            lastTimestamp = traceItem5.timestamp
        )
        assertEqualsTraceStatistic(expected, appendTraceStatisticFromTraceItems(existing, newItems, TraceItem::draught))
    }

    @Test
    fun mergeAdjacentTraceStatistics() {
        val left = traceStatistic123
        val right = traceStatistic456

        assertNull(mergeAdjacentTraceStatistics(null, null))
        assertEqualsTraceStatistic(left, mergeAdjacentTraceStatistics(left, null))
        assertEqualsTraceStatistic(right, mergeAdjacentTraceStatistics(null, right))

        // argument order (left-right, right-left) doesn't matter, the function determines it based on lastTimestamp
        // which one is left and right
        val expectedMerge = TraceStatistic(
            min = left.min,
            max = right.max,
            avg = 3.0f, // 1 + 2 + 3 + 4 + 5 = 15 / 5 items with same weight (60 sec) = 3
            last = right.last,
            duration = left.duration + right.duration,
            lastTimestamp = right.lastTimestamp
        )
        assertEqualsTraceStatistic(expectedMerge, mergeAdjacentTraceStatistics(left, right))
        assertEqualsTraceStatistic(expectedMerge, mergeAdjacentTraceStatistics(right, left))
    }

    private fun traceItem(
        locationTime: LocationTime,
        speedOverGround: Float? = null,
        draught: Float? = null
    ) = TraceItem(
        location = locationTime.location,
        speedOverGround = speedOverGround,
        timestamp = locationTime.time,
        draught = draught
    )
}

fun assertEqualsTraceStatistic(expected: TraceStatistic, actual: TraceStatistic?, avgDelta: Float = 0.01f) {
    actual ?: fail("Actual trace statistic is null")
    actual!!
    assertEquals(expected.min, actual.min, "min")
    assertEquals(expected.max, actual.max, "max")
    assertEquals(expected.avg, actual.avg, avgDelta, "avg")
    assertEquals(expected.last, actual.last, "last")
    assertEquals(expected.duration, actual.duration, "duration")
    assertEquals(expected.lastTimestamp, actual.lastTimestamp, "lastTimestamp")
}
