package nl.teqplay.vesselvoyage.service

import com.fasterxml.jackson.module.kotlin.readValue
import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.anyOrNull
import com.nhaarman.mockitokotlin2.argumentCaptor
import com.nhaarman.mockitokotlin2.doReturn
import com.nhaarman.mockitokotlin2.eq
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.reset
import com.nhaarman.mockitokotlin2.times
import com.nhaarman.mockitokotlin2.verify
import com.nhaarman.mockitokotlin2.whenever
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.model.AreaEndEvent
import nl.teqplay.aisengine.event.model.AreaStartEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.poma.api.v1.Port
import nl.teqplay.skeleton.common.exception.NotFoundException
import nl.teqplay.skeleton.model.Location
import nl.teqplay.vesselvoyage.ApplicationTestConfig
import nl.teqplay.vesselvoyage.datasource.NewESoFDataSource
import nl.teqplay.vesselvoyage.datasource.NewShipStatusStateDataSource
import nl.teqplay.vesselvoyage.datasource.NewVisitDataSource
import nl.teqplay.vesselvoyage.datasource.NewVoyageDataSource
import nl.teqplay.vesselvoyage.datasource.VisitDataSource
import nl.teqplay.vesselvoyage.datasource.VoyageDataSource
import nl.teqplay.vesselvoyage.logic.PORT_BEANR
import nl.teqplay.vesselvoyage.logic.PORT_BEGNE
import nl.teqplay.vesselvoyage.logic.PORT_DEHAM
import nl.teqplay.vesselvoyage.logic.PORT_NLDOR
import nl.teqplay.vesselvoyage.logic.PORT_NLRTM
import nl.teqplay.vesselvoyage.logic.PORT_NLTNZ
import nl.teqplay.vesselvoyage.logic.PORT_NLVLA
import nl.teqplay.vesselvoyage.logic.createPortAreaVisit
import nl.teqplay.vesselvoyage.logic.createPortEventEnd
import nl.teqplay.vesselvoyage.logic.createPortEventStart
import nl.teqplay.vesselvoyage.logic.createVisit
import nl.teqplay.vesselvoyage.logic.createVoyageFromVisitToVisit
import nl.teqplay.vesselvoyage.logic.getMockIterable
import nl.teqplay.vesselvoyage.mapper.PomaMapper
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.HistoricTrace
import nl.teqplay.vesselvoyage.model.OutgoingChange
import nl.teqplay.vesselvoyage.model.TRACE_ALGORITHM_VERSION
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.Voyage
import nl.teqplay.vesselvoyage.model.createVisitId
import nl.teqplay.vesselvoyage.model.createVoyageId
import nl.teqplay.vesselvoyage.model.internal.SnapshotShipStatus
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.properties.TraceProperties
import nl.teqplay.vesselvoyage.service.processing.EventProcessingService
import nl.teqplay.vesselvoyage.service.processing.anchor.AnchorProcessor
import nl.teqplay.vesselvoyage.service.processing.anchorarea.AnchorAreaProcessor
import nl.teqplay.vesselvoyage.service.processing.approach.ApproachAreaProcessor
import nl.teqplay.vesselvoyage.service.processing.berth.UniqueBerthProcessor
import nl.teqplay.vesselvoyage.service.processing.destination.DestinationChangedProcessor
import nl.teqplay.vesselvoyage.service.processing.encounter.EncounterProcessor
import nl.teqplay.vesselvoyage.service.processing.eosp.EndOfSeaPassageProcessor
import nl.teqplay.vesselvoyage.service.processing.eta.EtaProcessor
import nl.teqplay.vesselvoyage.service.processing.lock.LockAreaProcessor
import nl.teqplay.vesselvoyage.service.processing.movement.MovementProcessor
import nl.teqplay.vesselvoyage.service.processing.pilot.PilotAreaProcessor
import nl.teqplay.vesselvoyage.service.processing.port.PortProcessor
import nl.teqplay.vesselvoyage.service.processing.shiptoship.ShipToShipTransferProcessor
import nl.teqplay.vesselvoyage.service.processing.status.StatusChangedProcessor
import nl.teqplay.vesselvoyage.service.processing.stop.StopProcessor
import nl.teqplay.vesselvoyage.service.processing.terminalmooring.TerminalMooringAreaProcessor
import nl.teqplay.vesselvoyage.service.publisher.OutgoingEventsSender
import nl.teqplay.vesselvoyage.service.trace.ProcessingTraceService
import nl.teqplay.vesselvoyage.util.globalObjectMapper
import nl.teqplay.vesselvoyage.util.loadResource
import nl.teqplay.vesselvoyage.util.startInitialVisit
import nl.teqplay.vesselvoyage.util.startInitialVoyage
import nl.teqplay.vesselvoyage.util.toVesselVoyageLocation
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.TestConstructor
import java.time.Duration
import java.time.ZoneOffset
import java.time.ZonedDateTime

@Import(ApplicationTestConfig::class)
@SpringBootTest
@ActiveProfiles("api")
@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
@ContextConfiguration
class EntryServiceTest(
    private val pomaMapper: PomaMapper
) {
    @Test
    fun `should process and inserted a start event`() {
        val (_, entryProcessingService, visitDataSource, voyageDataSource) = createMockEntryService()

        doReturn(VisitDataSource.LastTwoVisits(null, null)).whenever(visitDataSource).findLastTwoByIMO(IMO_1)
        doReturn(VoyageDataSource.LastTwoVoyages(null, null)).whenever(voyageDataSource).findLastTwoByIMO(IMO_1)

        entryProcessingService.insert(EVENT_1_START, imo = IMO_1)

        // should load initial status
        verify(visitDataSource).findLastTwoByIMO(IMO_1)
        verify(voyageDataSource).findLastTwoByIMO(IMO_1)

        // should persist the event and a visit in the database
        verify(visitDataSource, times(1)).createOrReplace(startInitialVisit(EVENT_1_START, imo = IMO_1.toInt(), unlocode = PORT_1).visit)
    }

    @Test
    fun `should process and insert an end event`() {
        val (_, entryProcessingService, visitDataSource, voyageDataSource) = createMockEntryService()

        doReturn(VisitDataSource.LastTwoVisits(null, null)).whenever(visitDataSource).findLastTwoByIMO(IMO_1)
        doReturn(VoyageDataSource.LastTwoVoyages(null, null)).whenever(voyageDataSource).findLastTwoByIMO(IMO_1)

        entryProcessingService.insert(EVENT_1_END, imo = IMO_1)

        // should load initial status
        verify(visitDataSource).findLastTwoByIMO(IMO_1)
        verify(voyageDataSource).findLastTwoByIMO(IMO_1)

        // should persist the event and a voyage in the database
        verify(voyageDataSource, times(1)).createOrReplace(startInitialVoyage(EVENT_1_END, imo = IMO_1.toInt(), unlocode = PORT_1).voyage)
    }

    @Test
    fun `should load and use initial status of a ship`() {
        val (_, entryProcessingService, visitDataSource, voyageDataSource) = createMockEntryService()

        doReturn(
            VisitDataSource.LastTwoVisits(
                lastVisit = startInitialVisit(EVENT_1_START, imo = IMO_1.toInt(), unlocode = PORT_1).visit,
                secondLastVisit = null
            )
        ).whenever(visitDataSource).findLastTwoByIMO(IMO_1)

        doReturn(
            VoyageDataSource.LastTwoVoyages(
                lastVoyage = null,
                secondLastVoyage = null
            )
        ).whenever(voyageDataSource).findLastTwoByIMO(IMO_1)

        entryProcessingService.insert(EVENT_1_END, imo = IMO_1)

        // should load initial status
        verify(visitDataSource).findLastTwoByIMO(IMO_1)
        verify(voyageDataSource).findLastTwoByIMO(IMO_1)

        // should persist the event and a voyage in the database
        verify(visitDataSource, times(1)).createOrReplace(VISIT_1_END)
        verify(voyageDataSource, times(1)).createOrReplace(VOYAGE_1_START)
    }

    @Test
    fun `should fetch and process all events for a single ship`() {
        val (
            _,
            entryProcessingService,
            visitDataSource,
            voyageDataSource,
            eventFetchingService,
            outgoingEventsSender
        ) = createMockEntryService()

        val events = listOf(EVENT_1_START, EVENT_1_END, EVENT_2_START)

        whenever(visitDataSource.findLastTwoByIMO(IMO_1))
            .doReturn(VisitDataSource.LastTwoVisits(null, null))
        whenever(voyageDataSource.findLastTwoByIMO(IMO_1))
            .doReturn(VoyageDataSource.LastTwoVoyages(null, null))
        whenever(eventFetchingService.fetchEventsByIMO(eq(IMO_1), any(), any())).thenAnswer { events.asSequence() }
        whenever(visitDataSource.findByIds(any())).doReturn(emptyList<Visit>())
        whenever(voyageDataSource.findByIds(any())).doReturn(emptyList<Voyage>())

        entryProcessingService.regenerateAllEventsAndTracesByIMO(IMO_1, forceRegenerateTraces = false)

        verify(visitDataSource).deleteAllByIMO(IMO_1)
        verify(voyageDataSource).deleteAllByIMO(IMO_1)
        verify(eventFetchingService).fetchEventsByIMO(eq(IMO_1), any(), any())

        // should:
        // - create visit 1
        // - update visit 1
        // - create visit 2 (also updates visit1 due to some missing optimization/deduplication)
        val visitCaptor = argumentCaptor<Visit>()
        val idVisit1 = createVisitId(EVENT_1_START._id)
        val idVisit2 = createVisitId(EVENT_2_START._id)
        verify(visitDataSource, times(3)).createOrReplace(visitCaptor.capture())
        assertEquals(listOf(idVisit1, idVisit1, idVisit2), visitCaptor.allValues.map { it._id })
        assertEquals(listOf(false, true, false), visitCaptor.allValues.map { it.finished })

        // should:
        // - create voyage between visit 1 and 2
        // - update voyage between visit 1 and 2
        val voyageCaptor = argumentCaptor<Voyage>()
        verify(voyageDataSource, times(2)).createOrReplace(voyageCaptor.capture())
        assertEquals(listOf(listOf(PORT_1), listOf(PORT_1)), voyageCaptor.allValues.map { it.startPortIds })
        assertEquals(listOf(null, listOf(PORT_2)), voyageCaptor.allValues.map { it.endPortIds })
        assertEquals(listOf(false, true), voyageCaptor.allValues.map { it.finished })

        // should:
        // - only send the final state via the outgoing events sender
        val changeCaptor = argumentCaptor<OutgoingChange>()
        verify(outgoingEventsSender, times(1)).send(changeCaptor.capture())
        assertEquals(Action.REVISE, changeCaptor.allValues.first().action)
        assertEquals(idVisit2, changeCaptor.allValues.first().entry._id)
    }

    @Test
    fun `should discontinue a ship`() {
        val (
            _,
            entryProcessingService,
            visitDataSource,
            voyageDataSource,
            eventFetchingService,
            outgoingEventsSender
        ) = createMockEntryService()

        doReturn(VisitDataSource.LastTwoVisits(null, null)).whenever(visitDataSource).findLastTwoByIMO(IMO_1)
        doReturn(VoyageDataSource.LastTwoVoyages(null, null)).whenever(voyageDataSource).findLastTwoByIMO(IMO_1)

        // make sure the ship has a status
        entryProcessingService.insert(EVENT_1_START, imo = IMO_1)
        reset(outgoingEventsSender)

        // double-check that the ship status is there
        val oldEntry = entryProcessingService.getCurrentStatus(IMO_1)
        assertEquals(EVENT_1_START.actualTime.atZone(ZoneOffset.UTC), oldEntry?.startTime)

        // recalculate with an empty list with events -> will result in no status
        doReturn(emptySequence<Event>()).whenever(eventFetchingService).fetchEventsByIMO(eq(IMO_1), any(), any())
        doReturn(emptyList<Visit>()).whenever(visitDataSource).findByIds(any())
        doReturn(emptyList<Voyage>()).whenever(voyageDataSource).findByIds(any())
        entryProcessingService.regenerateAllEventsAndTracesByIMO(IMO_1, forceRegenerateTraces = false)

        // make sure the ship has NO status anymore
        val newEntry = entryProcessingService.getCurrentStatus(IMO_1)
        assertNull(newEntry)

        // should send the final state DISCONTINUE via the outgoing events sender
        val changeCaptor = argumentCaptor<OutgoingChange>()
        verify(outgoingEventsSender, times(1)).send(changeCaptor.capture())
        assertEquals(Action.DISCONTINUE, changeCaptor.allValues.first().action)
        assertEquals(oldEntry, changeCaptor.allValues.first().entry)
    }

    @Test
    fun `should recalculate a ship - forceRegenerateTraces=true`() {
        val setup = setupRecalculatingShipMocks(TRACE_ALGORITHM_VERSION)

        val eventsSize = setup.entryProcessingService.regenerateAllEventsAndTracesByIMO(IMO_1, forceRegenerateTraces = true)

        verify(setup.visitDataSource, times(2)).createOrReplace(any())
        verify(setup.voyageDataSource).createOrReplace(any())
        verify(setup.outgoingEventsSender).send(any())
        verify(setup.v1TraceService).deleteHistoricTraces(listOf(createVisitId(EVENT_1_START._id)), IMO_1)
        verify(setup.v1TraceService).createHistoricTraceFromAIS(any(), anyOrNull())

        assertEquals(2, eventsSize)
    }

    @Test
    fun `should recalculate a ship - forceRegenerateTraces=false`() {
        val setup = setupRecalculatingShipMocks(TRACE_ALGORITHM_VERSION)

        val eventsSize = setup.entryProcessingService.regenerateAllEventsAndTracesByIMO(IMO_1, forceRegenerateTraces = false)

        verify(setup.visitDataSource, times(2)).createOrReplace(any())
        verify(setup.voyageDataSource).createOrReplace(any())
        verify(setup.outgoingEventsSender).send(any())
        verify(setup.v1TraceService).deleteHistoricTraces(emptyList(), IMO_1)
        verify(setup.v1TraceService, times(0)).createHistoricTraceFromAIS(any(), anyOrNull())

        assertEquals(2, eventsSize)
    }

    @Test
    fun `should recalculate a ship - forceRegenerateTraces=false, old version`() {
        val oldTraceVersion = 0 // must not equal TRACE_ALGORITHM_VERSION
        assertNotEquals(oldTraceVersion, TRACE_ALGORITHM_VERSION)
        val setup = setupRecalculatingShipMocks(oldTraceVersion)

        val eventsSize = setup.entryProcessingService.regenerateAllEventsAndTracesByIMO(IMO_1, forceRegenerateTraces = false)

        verify(setup.visitDataSource, times(2)).createOrReplace(any())
        verify(setup.voyageDataSource).createOrReplace(any())
        verify(setup.outgoingEventsSender).send(any())
        verify(setup.v1TraceService).deleteHistoricTraces(listOf(createVisitId(EVENT_1_START._id)), IMO_1)
        verify(setup.v1TraceService, times(1)).createHistoricTraceFromAIS(any(), anyOrNull())

        assertEquals(2, eventsSize)
    }

    @Test
    fun `should recalculate by snapshot`() {
        val setup = setupRecalculatingShipMocks(TRACE_ALGORITHM_VERSION)
        val oldStatus = setup.entryService.getCurrentShipStatus(IMO_1)
        val snapshot = SnapshotShipStatus(IMO_1, oldStatus, ZonedDateTime.now())

        val events = listOf(EVENT_1_START, EVENT_1_END, EVENT_2_START)
        whenever(setup.eventFetchingService.fetchEventsByIMO(eq(IMO_1), any(), any())).thenAnswer { events.asSequence() }

        val eventsCount = setup.entryProcessingService.regenerateBySnapshot(snapshot)

        verify(setup.visitDataSource).delete(eq(IMO_1), eq(snapshot.createdAt), any())
        verify(setup.voyageDataSource).delete(eq(IMO_1), eq(snapshot.createdAt), any())
        verify(setup.eventFetchingService).fetchEventsByIMO(eq(IMO_1), eq(snapshot.createdAt), any())
        assertEquals(events.size, eventsCount)

        val newStatus = setup.entryService.getCurrentShipStatus(IMO_1)
        val expectedVisit = startInitialVisit(EVENT_2_START, IMO_1.toInt(), PORT_2).visit.copy(
            previousEntryId = createVoyageId(EVENT_1_END._id)
        )
        val expectedNewStatus = VisitShipStatus(
            visit = expectedVisit,
            previousVoyage = VOYAGE_1_END,
            previousVisit = VISIT_1_END
        )

        assertEquals(expectedNewStatus, newStatus)
    }

    @Test
    fun `getVisitsAroundVisit fails by visit not found by visitId`() {
        val requestedLimit = 1u
        val (
            entryService,
            _,
            visitDataSource
        ) = createMockEntryService()
        val visitId = "visitIdNotFound"
        val imo = "myImo"
        whenever(visitDataSource.findByIdAndImo(any(), any()))
            .thenAnswer { null }
        assertThrows<NotFoundException> {
            entryService.getVisitsAroundVisit(imo, visitId, requestedLimit)
        }
    }

    @Test
    fun `getVisitsAroundVisit fails by visit not found by imo's recent visits`() {
        val requestedLimit = 1u
        val (
            entryService,
            _,
            visitDataSource
        ) = createMockEntryService()
        val imo = "myImo"
        whenever(visitDataSource.findRecentByIMO(any(), any()))
            .thenAnswer { getMockIterable<List<Visit>>() }
        assertThrows<NotFoundException> {
            entryService.getVisitsAroundVisit(imo, null, requestedLimit)
        }
    }

    @Test
    fun `getVisitsAroundVisit returns correctly`() {
        val requestedLimit = 3u
        val (
            entryService,
            _,
            visitDataSource,
            voyageDataSource
        ) = createMockEntryService()
        val imo = "myImo"
        val visit_23 = createVisit(
            _id = "Visit_23", previousEntryId = "Voyage_22_23", imo = imo, finished = false,
            portAreas = listOf(
                createPortAreaVisit(
                    createPortEventStart(PORT_BEGNE, "2021-11-23T01:00:00Z"),
                )
            )
        )
        val visit_22 = createVisit(
            _id = "Visit_22", previousEntryId = "Voyage_21_22", imo = imo, finished = true,
            portAreas = listOf(
                createPortAreaVisit(
                    createPortEventStart(PORT_NLTNZ, "2021-11-22T01:00:00Z"),
                    createPortEventEnd(PORT_NLTNZ, "2021-11-22T12:00:00Z")
                )
            )
        )
        val visit_21 = createVisit(
            _id = "Visit_21", previousEntryId = "Voyage_20_21", imo = imo, finished = true,
            portAreas = listOf(
                createPortAreaVisit(
                    createPortEventStart(PORT_NLDOR, "2021-11-21T01:00:00Z"),
                    createPortEventEnd(PORT_NLDOR, "2021-11-21T12:00:00Z")
                )
            )
        )
        val requestedVisit_20 = createVisit(
            _id = "Visit_20", previousEntryId = "Voyage_19_20", imo = imo, finished = true,
            portAreas = listOf(
                createPortAreaVisit(
                    createPortEventStart(PORT_NLVLA, "2021-11-20T01:00:00Z"),
                    createPortEventEnd(PORT_NLVLA, "2021-11-20T12:00:00Z")
                )
            )
        )
        val visit_19 = createVisit(
            _id = "Visit_19", previousEntryId = "Voyage_18_19", imo = imo, finished = true,
            portAreas = listOf(
                createPortAreaVisit(
                    createPortEventStart(PORT_DEHAM, "2021-11-19T01:00:00Z"),
                    createPortEventEnd(PORT_DEHAM, "2021-11-19T12:00:00Z")
                )
            )
        )
        val visit_18 = createVisit(
            _id = "Visit_18", previousEntryId = "Voyage_17_18", imo = imo, finished = true,
            portAreas = listOf(
                createPortAreaVisit(
                    createPortEventStart(PORT_NLRTM, "2021-11-18T00:00:00Z"),
                    createPortEventEnd(PORT_NLRTM, "2021-11-18T12:00:00Z")
                )
            )
        )
        val visit_17 = createVisit(
            _id = "Visit_17", previousEntryId = null, imo = imo, finished = true,
            portAreas = listOf(
                createPortAreaVisit(
                    createPortEventStart(PORT_BEANR, "2021-11-17T00:00:00Z"),
                    createPortEventEnd(PORT_BEANR, "2021-11-17T12:00:00Z")
                )
            )
        )
        val mockedPastVisits = listOf(visit_19, visit_18, visit_17)
        val mockedFutureVisits = listOf(visit_23, visit_22, visit_21)
        val mockedAllVisits = mockedFutureVisits + requestedVisit_20 + mockedPastVisits

        val voyage_22_23 = createVoyageFromVisitToVisit(visit_22, visit_23)
        val voyage_21_22 = createVoyageFromVisitToVisit(visit_21, visit_22)
        val voyage_20_21 = createVoyageFromVisitToVisit(requestedVisit_20, visit_21)
        val voyage_19_20 = createVoyageFromVisitToVisit(visit_19, requestedVisit_20)
        val voyage_20_19 = createVoyageFromVisitToVisit(visit_18, visit_19)
        val voyage_19_18 = createVoyageFromVisitToVisit(visit_17, visit_18)

        val mockedAllVoyages = listOf(
            voyage_22_23, voyage_21_22, voyage_20_21,
            voyage_19_20, voyage_20_19, voyage_19_18
        )

        // it should be mocked:
        whenever(visitDataSource.findByIdAndImo(requestedVisit_20._id, imo))
            .thenAnswer { requestedVisit_20 }
        whenever(visitDataSource.findPastVisits(requestedVisit_20, requestedLimit))
            .thenAnswer { mockedPastVisits }
        whenever(visitDataSource.findFutureVisits(requestedVisit_20, requestedLimit))
            .thenAnswer { mockedFutureVisits }
        whenever(voyageDataSource.findByIds(mockedAllVisits.mapNotNull { it.previousEntryId }))
            .thenAnswer { mockedAllVoyages }

        val combinedVisitVoyagePairs = entryService.getVisitsAroundVisit(imo, requestedVisit_20._id, requestedLimit)
        // assert the coherence of the returned pairs
        // future 3
        assertEquals("Visit_23", combinedVisitVoyagePairs[0].leadingVisit._id)
        assertEquals("Voyage_22_23", combinedVisitVoyagePairs[0].leadingVisit.previousEntryId)
        assertEquals(combinedVisitVoyagePairs[0].leadingVisit.previousEntryId, combinedVisitVoyagePairs[0].leadingVisit.previousEntryId)

        assertEquals("Visit_22", combinedVisitVoyagePairs[1].leadingVisit._id)
        assertEquals("Voyage_21_22", combinedVisitVoyagePairs[1].leadingVisit.previousEntryId)
        assertEquals(combinedVisitVoyagePairs[1].leadingVisit.previousEntryId, combinedVisitVoyagePairs[1].voyage?._id)

        assertEquals("Visit_21", combinedVisitVoyagePairs[2].leadingVisit._id)
        assertEquals("Voyage_20_21", combinedVisitVoyagePairs[2].leadingVisit.previousEntryId)
        assertEquals(combinedVisitVoyagePairs[2].leadingVisit.previousEntryId, combinedVisitVoyagePairs[2].voyage?._id)

        // pivot visit!
        assertEquals("Visit_20", combinedVisitVoyagePairs[3].leadingVisit._id)
        assertEquals("Voyage_19_20", combinedVisitVoyagePairs[3].leadingVisit.previousEntryId)
        assertEquals(combinedVisitVoyagePairs[3].leadingVisit.previousEntryId, combinedVisitVoyagePairs[3].voyage?._id)
        // past 3
        assertEquals("Visit_19", combinedVisitVoyagePairs[4].leadingVisit._id)
        assertEquals("Voyage_18_19", combinedVisitVoyagePairs[4].leadingVisit.previousEntryId)
        assertEquals(combinedVisitVoyagePairs[4].leadingVisit.previousEntryId, combinedVisitVoyagePairs[4].voyage?._id)

        assertEquals("Visit_18", combinedVisitVoyagePairs[5].leadingVisit._id)
        assertEquals("Voyage_17_18", combinedVisitVoyagePairs[5].leadingVisit.previousEntryId)
        assertEquals(combinedVisitVoyagePairs[5].leadingVisit.previousEntryId, combinedVisitVoyagePairs[5].voyage?._id)

        assertEquals("Visit_17", combinedVisitVoyagePairs[6].leadingVisit._id)
        assertEquals(null, combinedVisitVoyagePairs[6].leadingVisit.previousEntryId)
        assertEquals(null, combinedVisitVoyagePairs[6].voyage?._id)
    }

    private fun createMockEntryService(): MockEntryServiceWrapper {
        val traceProperties = TraceProperties(
            tolerance = 0.0001,
            ongoing = TraceProperties.Ongoing(
                maxAge = Duration.ofDays(30),
                maxNonSimplifiedSize = 50,
                maxSize = 100,
                persistInterval = "900000"
            ),
            historic = TraceProperties.Historic(
                generateWhenFinished = true,
                finished = TraceProperties.Historic.Finished(
                    createFromAis = false
                ),
                unfinished = TraceProperties.Historic.Unfinished(
                    createFromAis = false
                )
            ),
            enableNewDefinition = false,
            enableOldDefinition = true,
            totalThreads = 5,
            maxRequestLengthDays = 90,
            activenessMonitoring = TraceProperties.ActivenessMonitoring(
                enabled = false,
                interval = 1000,
                maxIdleTime = Duration.ofMinutes(5)
            )
        )
        val config = EventProcessingProperties(
            maxSpeedMps = 1.0,
            minDuration = Duration.ofMinutes(30),
            newStopDetection = true,
            enableTraceCalculations = true,
            enableSlowMovingPeriods = true,
            enableNewDefinition = false,
            enableOldDefinition = true,
            totalThreads = 5,
            logResults = false,
            activenessMonitoring = EventProcessingProperties.ActivenessMonitoring(
                enabled = false,
                interval = 1000,
                maxIdleTime = Duration.ofMinutes(5)
            )
        )

        val visitDataSource: VisitDataSource = mock()
        val voyageDataSource: VoyageDataSource = mock()
        val newVisitDataSource: NewVisitDataSource = mock()
        val newVoyageDataSource: NewVoyageDataSource = mock()
        val newESoFDataSource: NewESoFDataSource = mock()
        val newShipStatusStateDataSource: NewShipStatusStateDataSource = mock()
        val eventFetchingService: EventFetchingService = mock()
        val v1TraceService: V1TraceService = mock()
        val aisFetchingService: AisFetchingService = mock()
        val processingTraceService: ProcessingTraceService = mock()
        val infraService: InfraService = mock()
        val outgoingEventsSender: OutgoingEventsSender = mock()
        val staticShipInfoService: StaticShipInfoService = mock()
        val slowMovingService: SlowMovingService = mock()
        val postProcessingService: PostProcessingService = mock()
        val persistChangesService: PersistChangesService = mock()
        val meterRegistry = SimpleMeterRegistry()
        val shipStatusService = ProcessingShipStatusService(
            visitDataSource,
            voyageDataSource,
            newVisitDataSource,
            newVoyageDataSource,
            newESoFDataSource,
            newShipStatusStateDataSource,
            meterRegistry
        )

        val portResources = listOf(
            "ports/nlrtm.json",
            "ports/nlvla.json",
            "ports/nldor.json",
            "ports/beanr.json",
            "ports/deham.json"
        )
        portResources.forEach {
            val port: Port = globalObjectMapper.readValue(loadResource(it))
            val unlocode = port.unlocode
            if (unlocode != null) {
                doReturn(pomaMapper.toLightweightPort(port)).whenever(infraService).getPortByUnlocode(unlocode)
            }
        }

        val entryService = EntryService(
            visitDataSource = visitDataSource,
            voyageDataSource = voyageDataSource,
            staticShipInfoService = staticShipInfoService,
            shipStatuses = shipStatusService
        )

        val eventProcessingService = EventProcessingService(
            AnchorProcessor(config, infraService),
            DestinationChangedProcessor(config),
            EncounterProcessor(config, SimpleMeterRegistry()),
            ShipToShipTransferProcessor(config, SimpleMeterRegistry()),
            EtaProcessor(config),
            MovementProcessor(config, infraService, aisFetchingService),
            PortProcessor(config, infraService, aisFetchingService, staticShipInfoService),
            StatusChangedProcessor(config),
            UniqueBerthProcessor(config, infraService, aisFetchingService),
            EndOfSeaPassageProcessor(config, infraService),
            StopProcessor(config, SimpleMeterRegistry(), infraService, emptyList()),
            PilotAreaProcessor(config),
            AnchorAreaProcessor(config),
            TerminalMooringAreaProcessor(config),
            LockAreaProcessor(config),
            ApproachAreaProcessor(config),
            processorLogDatasource = mock(),
            eventProcessingProperties = mock()
        )

        val entryProcessingService = EntryProcessingService(
            entryService = entryService,
            traceProperties = traceProperties,
            eventProcessingProperties = config,
            visitDataSource = visitDataSource,
            voyageDataSource = voyageDataSource,
            v1TraceService = v1TraceService,
            processingTraceService = processingTraceService,
            eventFetchingService = eventFetchingService,
            outgoingEventsSender = outgoingEventsSender,
            shipStatuses = shipStatusService,
            eventProcessingService = eventProcessingService,
            slowMovingService = slowMovingService,
            postProcessingService = postProcessingService,
            persistChangesService = persistChangesService,
            changesPublisherService = mock()
        )

        return MockEntryServiceWrapper(
            entryService,
            entryProcessingService,
            visitDataSource,
            voyageDataSource,
            eventFetchingService,
            outgoingEventsSender,
            v1TraceService,
            infraService
        )
    }

    private fun setupRecalculatingShipMocks(traceVersion: Int): MockEntryServiceWrapper {
        val mockEntryService = createMockEntryService()

        val events = listOf(EVENT_1_START, EVENT_1_END)
        val visit = createVisit(
            _id = createVisitId(EVENT_1_START._id),
            portAreas = listOf(
                createPortAreaVisit(EVENT_1_START),
                createPortAreaVisit(EVENT_1_END),
            ),
            finished = true
        )

        whenever(mockEntryService.eventFetchingService.fetchEventsByIMO(eq(IMO_1), any(), any()))
            .thenAnswer { events.asSequence() }

        whenever(mockEntryService.visitDataSource.findLastTwoByIMO(IMO_1))
            .thenReturn(VisitDataSource.LastTwoVisits(null, null))

        whenever(mockEntryService.visitDataSource.findById(visit._id))
            .thenReturn(visit)

        whenever(mockEntryService.voyageDataSource.findLastTwoByIMO(IMO_1))
            .thenReturn(VoyageDataSource.LastTwoVoyages(null, null))

        val trace = HistoricTrace(
            _id = "1",
            mmsi = Companion.MMSI_1,
            imo = IMO_1,
            entryId = visit._id,
            startTime = visit.startTime,
            endTime = visit.endTime,
            finished = true,
            locations = emptyList(),
            tolerance = 0.0001,
            version = traceVersion
        )

        doReturn(listOf(trace))
            .whenever(mockEntryService.v1TraceService).getByImo(IMO_1)

        return mockEntryService
    }

    private data class MockEntryServiceWrapper(
        val entryService: EntryService,
        val entryProcessingService: EntryProcessingService,
        val visitDataSource: VisitDataSource,
        val voyageDataSource: VoyageDataSource,
        val eventFetchingService: EventFetchingService,
        val outgoingEventsSender: OutgoingEventsSender,
        val v1TraceService: V1TraceService,
        val infraService: InfraService
    )

    private val IMO_1 = "1"
    private val PORT_1 = "NLRTM"
    private val PORT_2 = "BEANR"

    private val EVENT_1_START = AreaStartEvent(
        _id = "EVENT_1_START",
        ship = AisShipIdentifier(
            imo = IMO_1.toInt(),
            mmsi = MMSI_1.toInt(),
        ),
        area = AreaIdentifier(
            id = PORT_1,
            unlocode = PORT_1,
            type = AreaIdentifier.AreaType.PORT,
        ),
        createdTime = ZonedDateTime.parse("2021-03-08T15:00:00Z").toInstant(),
        actualTime = ZonedDateTime.parse("2021-03-08T15:00:00Z").toInstant(),
        location = Location(0.0, 0.0),
        draught = 1.0F,
        berth = null,
        heading = null,
        speedOverGround = null
    )

    private val EVENT_1_END = AreaEndEvent(
        _id = "EVENT_1_END",
        ship = AisShipIdentifier(
            imo = IMO_1.toInt(),
            mmsi = MMSI_1.toInt(),
        ),
        area = AreaIdentifier(
            id = PORT_1,
            unlocode = PORT_1,
            type = AreaIdentifier.AreaType.PORT,
        ),
        createdTime = ZonedDateTime.parse("2021-03-08T22:00:00Z").toInstant(),
        actualTime = ZonedDateTime.parse("2021-03-08T22:00:00Z").toInstant(),
        location = Location(0.0, 0.0),
        draught = 1.0F,
        berth = null,
        heading = null,
        speedOverGround = null,
        startEventId = null,
    )

    private val EVENT_2_START = AreaStartEvent(
        _id = "EVENT_2_START",
        ship = AisShipIdentifier(
            imo = IMO_1.toInt(),
            mmsi = MMSI_1.toInt(),
        ),
        area = AreaIdentifier(
            id = PORT_2,
            unlocode = PORT_2,
            type = AreaIdentifier.AreaType.PORT,
        ),
        createdTime = ZonedDateTime.parse("2021-03-11T16:00:00Z").toInstant(),
        actualTime = ZonedDateTime.parse("2021-03-11T16:00:00Z").toInstant(),
        location = Location(0.0, 0.0),
        draught = 1.0F,
        berth = null,
        heading = null,
        speedOverGround = null
    )

    private val VISIT_1_START = startInitialVisit(EVENT_1_START, IMO_1.toInt(), PORT_1).visit
    private val VISIT_1_END = VISIT_1_START.copy(
        portAreas = VISIT_1_START.portAreas.map {
            it.copy(
                endEventId = EVENT_1_END._id,
                endLocation = EVENT_1_END.location.toVesselVoyageLocation(),
                endTime = EVENT_1_END.actualTime.atZone(ZoneOffset.UTC),
                endDraught = EVENT_1_END.draught?.toDouble()
            )
        },
        finished = true,
        nextEntryId = createVoyageId(EVENT_1_END._id)
    )

    private val VOYAGE_1_START = Voyage(
        _id = createVoyageId(EVENT_1_END._id),
        mmsi = EVENT_1_END.ship.mmsi.toString(),
        imo = EVENT_1_END.ship.imo.toString(),
        startPortIds = listOf(PORT_1),
        startTime = EVENT_1_END.actualTime.atZone(ZoneOffset.UTC),
        dest = null,
        eta = null,
        esof = null,
        nonMatchingAnchorAreas = null,
        passThroughAreas = null,
        endPortIds = null,
        endTime = null,
        finished = false,
        previousEntryId = createVisitId(EVENT_1_START._id),
        nextEntryId = null
    )

    private val VOYAGE_1_END = VOYAGE_1_START.copy(
        nextEntryId = createVisitId(EVENT_2_START._id),
        endPortIds = listOf(PORT_2),
        endTime = EVENT_2_START.actualTime.atZone(ZoneOffset.UTC),
        finished = true
    )

    companion object {
        private const val MMSI_1 = "1"
    }
}
