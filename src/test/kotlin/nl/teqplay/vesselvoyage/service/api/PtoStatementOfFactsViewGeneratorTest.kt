package nl.teqplay.vesselvoyage.service.api

import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.eq
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.whenever
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType.PILOT
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType.TUG
import nl.teqplay.csi.model.ship.info.component.ShipCategories
import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2
import nl.teqplay.csi.model.ship.info.component.ShipSpecification
import nl.teqplay.skeleton.model.Location
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.AreaMeta
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.BerthVisit
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.CategorizedPeriods
import nl.teqplay.vesselvoyage.logic.ANCHOR_AREA_4EAST
import nl.teqplay.vesselvoyage.logic.BEANR
import nl.teqplay.vesselvoyage.logic.IMO_1
import nl.teqplay.vesselvoyage.logic.MMSI_1
import nl.teqplay.vesselvoyage.logic.NLRTM
import nl.teqplay.vesselvoyage.logic.createAnchorage
import nl.teqplay.vesselvoyage.logic.createBerth
import nl.teqplay.vesselvoyage.logic.createLock
import nl.teqplay.vesselvoyage.logic.createPilotBoardingPlace
import nl.teqplay.vesselvoyage.logic.createPort
import nl.teqplay.vesselvoyage.logic.createShipToShipArea
import nl.teqplay.vesselvoyage.logic.createTerminal
import nl.teqplay.vesselvoyage.mapper.EntryV2Mapper
import nl.teqplay.vesselvoyage.mapper.EntryV2MapperImpl
import nl.teqplay.vesselvoyage.mapper.PtoStatementOfFactsMapper
import nl.teqplay.vesselvoyage.mapper.PtoStatementOfFactsMapperImpl
import nl.teqplay.vesselvoyage.model.ShipDetails
import nl.teqplay.vesselvoyage.model.esof.ptoview.AnchorStopInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.BerthVisitInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.EncounterInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.LockStopInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.PilotInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.PortAreaInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.ShipToShipTransferInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.TerminalVisitInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.TugInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.UnclassifiedStopInfo
import nl.teqplay.vesselvoyage.model.internal.InfraAreaType
import nl.teqplay.vesselvoyage.model.internal.ShipCache
import nl.teqplay.vesselvoyage.model.lightweight.poma.Berth
import nl.teqplay.vesselvoyage.model.v2.AreaActivity
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewEncounter
import nl.teqplay.vesselvoyage.model.v2.NewSlowMovingPeriod
import nl.teqplay.vesselvoyage.model.v2.NewStop
import nl.teqplay.vesselvoyage.model.v2.NewStopType.ANCHOR_AREA
import nl.teqplay.vesselvoyage.model.v2.NewStopType.BERTH
import nl.teqplay.vesselvoyage.model.v2.NewStopType.LOCK
import nl.teqplay.vesselvoyage.model.v2.NewStopType.UNCLASSIFIED
import nl.teqplay.vesselvoyage.model.v2.SimpleStartEnd
import nl.teqplay.vesselvoyage.model.v2.Speed
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.ShipCacheService
import nl.teqplay.vesselvoyage.service.api.PtoStatementOfFactsViewGenerator.BerthVisitGroup
import nl.teqplay.vesselvoyage.util.DEFAULT_TEST_PORT_ID
import nl.teqplay.vesselvoyage.util.PtoSofHelperFunctions.createBerthVisitInfo
import nl.teqplay.vesselvoyage.util.PtoSofHelperFunctions.createTerminalVisitInfo
import nl.teqplay.vesselvoyage.util.Timeline
import nl.teqplay.vesselvoyage.util.component10
import nl.teqplay.vesselvoyage.util.component11
import nl.teqplay.vesselvoyage.util.component12
import nl.teqplay.vesselvoyage.util.component6
import nl.teqplay.vesselvoyage.util.component7
import nl.teqplay.vesselvoyage.util.component8
import nl.teqplay.vesselvoyage.util.component9
import nl.teqplay.vesselvoyage.util.createAreaActivity
import nl.teqplay.vesselvoyage.util.createLocationTime
import nl.teqplay.vesselvoyage.util.createLocationTimeApi
import nl.teqplay.vesselvoyage.util.createNewESoF
import nl.teqplay.vesselvoyage.util.createNewEncounter
import nl.teqplay.vesselvoyage.util.createNewStop
import nl.teqplay.vesselvoyage.util.createNewVisit
import nl.teqplay.vesselvoyage.util.createShipRegisterInfoCache
import nl.teqplay.vesselvoyage.util.createShipToShipTransfer
import nl.teqplay.vesselvoyage.util.minus
import nl.teqplay.vesselvoyage.util.minusMinutes
import nl.teqplay.vesselvoyage.util.plus
import nl.teqplay.vesselvoyage.util.plusMinutes
import nl.teqplay.vesselvoyage.util.toShipDetails
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.fail
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource
import java.time.Duration
import java.time.Instant
import java.util.UUID
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class PtoStatementOfFactsViewGeneratorTest {

    private val ptoMapper: PtoStatementOfFactsMapper = PtoStatementOfFactsMapperImpl()
    private val entryV2Mapper: EntryV2Mapper = EntryV2MapperImpl()
    private val timeline = Timeline()
    private val visitId = "TEST_VISIT_ID"

    private lateinit var infraService: InfraService
    private lateinit var shipCacheService: ShipCacheService
    private lateinit var generator: PtoStatementOfFactsViewGenerator

    @BeforeEach
    fun setUp() {
        infraService = mock<InfraService>().apply {
            whenever(this.getById(eq(DEFAULT_TEST_PORT_ID), eq(InfraAreaType.PORT)))
                .thenReturn(createPort())
        }
        shipCacheService = mock()
        generator = PtoStatementOfFactsViewGenerator(infraService, ptoMapper, entryV2Mapper, shipCacheService)
    }

    private fun portAreasIsPassingThroughSource(): Stream<Arguments> {
        val loc = Location(0.0, 0.0)
        val now = Instant.now()

        val areaStart = LocationTime(loc, now)
        val areaEnd = LocationTime(loc, now.plusSeconds(60))
        val areaActivityFinished = AreaActivity(id = "finished", areaStart, areaEnd, areaId = "finished")
        val areaActivityOngoing = AreaActivity(id = "ongoing", areaStart, end = null, areaId = "ongoing")

        // 'inside' here means it is in the time range of start..end
        val stopInsideAreaFinished = createNewStop(
            start = areaStart.copy(time = areaStart.time.plusSeconds(5)),
            end = areaEnd.copy(time = areaEnd.time.minusSeconds(5)),
            type = BERTH // type doesn't really matter, but is not nullable
        )
        val stopInsideAreaOngoing = createNewStop(
            start = areaStart.copy(time = areaStart.time.plusSeconds(5)),
            end = null,
            type = BERTH // type doesn't really matter, but is not nullable
        )
        val outsideOffsetSeconds = 180L // well exceeds area start..end
        val stopOutsideAreaFinished = createNewStop(
            start = areaStart.copy(time = areaStart.time.plusSeconds(outsideOffsetSeconds)),
            end = areaEnd.copy(time = areaEnd.time.plusSeconds(outsideOffsetSeconds)),
            type = BERTH // type doesn't really matter, but is not nullable
        )
        val stopOutsideAreaOngoing = createNewStop(
            start = areaStart.copy(time = areaStart.time.plusSeconds(outsideOffsetSeconds)),
            end = null,
            type = BERTH // type doesn't really matter, but is not nullable
        )

        return listOf(
            PortAreaSetup(areaActivityFinished, emptyList(), isPassThrough = true),
            PortAreaSetup(areaActivityFinished, stopInsideAreaFinished, isPassThrough = false),
            // note that areaActivityFinished + stopInsideAreaOngoing should not possible, therefore not testing it
            PortAreaSetup(areaActivityFinished, stopOutsideAreaFinished, isPassThrough = true),
            PortAreaSetup(areaActivityFinished, stopOutsideAreaOngoing, isPassThrough = true),

            PortAreaSetup(areaActivityOngoing, emptyList(), isPassThrough = true),
            PortAreaSetup(areaActivityOngoing, stopInsideAreaFinished, isPassThrough = false),
            PortAreaSetup(areaActivityOngoing, stopInsideAreaOngoing, isPassThrough = false),
            PortAreaSetup(areaActivityOngoing, stopOutsideAreaFinished, isPassThrough = false),
            PortAreaSetup(areaActivityOngoing, stopOutsideAreaOngoing, isPassThrough = false),
        ).map { Arguments.of(it) }.stream()
    }

    @ParameterizedTest
    @MethodSource("portAreasIsPassingThroughSource")
    fun portAreasIsPassingThrough(setup: PortAreaSetup) {
        val actual = generator.isPassingThroughPortActivity(setup.areaActivity, setup.stops)
        assertTrue(setup.isPassThrough == actual)
    }

    data class PortAreaSetup(
        val areaActivity: AreaActivity,
        val stops: List<NewStop>,
        val isPassThrough: Boolean
    ) {
        constructor(areaActivity: AreaActivity, stop: NewStop, isPassThrough: Boolean) :
            this(areaActivity, listOf(stop), isPassThrough)
    }

    @Test
    fun `match berth visit to port area`() {

        val port = createPort()
        val portId = port._id!!
        val loc = Location(0.0, 0.0)
        val now = Instant.now()
        val portAreaStart = LocationTime(loc, now)
        val berthStart = LocationTime(loc, now.plusSeconds(30))
        val berthEnd = LocationTime(loc, now.plusSeconds(90))
        val portAreaEnd = LocationTime(loc, now.plusSeconds(120))

        val portActivity = AreaActivity(id = "", start = portAreaStart, end = portAreaEnd, areaId = portId)
        val portAreaInfo = PortAreaInfo(ref = 0, isPassThrough = false, portActivity, port)

        // matches on berth.mainPort
        assertEquals(
            portAreaInfo,
            generator.matchBerthVisitToPortArea(
                berthActivity = AreaActivity(id = "", start = berthStart, end = berthEnd, areaId = "bert"),
                listOf(portAreaInfo),
                berth = createBerth(mainPort = portId)
            ),
            "Should match on berth.mainPort -> port._id"
        )

        // matches on port._id in berth.ports
        assertEquals(
            portAreaInfo,
            generator.matchBerthVisitToPortArea(
                berthActivity = AreaActivity(id = "", start = berthStart, end = berthEnd, areaId = "bert"),
                listOf(portAreaInfo),
                berth = createBerth(ports = listOf(portId))
            ),
            "Should match on berth.mainPort -> port._id"
        )
    }

    private fun Instant.plusMinutes(minutes: Long): Instant {
        return this.plus(Duration.ofMinutes(minutes))
    }

    private fun arrivalTugsSource(): Stream<Arguments> {

        fun tug(start: LocationTime, end: LocationTime? = null) = SimpleStartEnd(start, end)

        // timeline
        // berth stops --------[previous]------[current]--------
        //             |---|---|---|---|---|---|---|---|---|---
        // times      now 30  60  90  120 150 180 210 240 270

        val now = Instant.EPOCH
        val beforePreviousStop = createLocationTime(time = now.plusMinutes(30))
        val time1 = createLocationTime(time = now.plusMinutes(60))
        val insidePreviousStop = createLocationTime(time = now.plusMinutes(90))
        val time2 = createLocationTime(time = now.plusMinutes(120))
        val betweenStops = createLocationTime(time = now.plusMinutes(150))
        val time3 = createLocationTime(time = now.plusMinutes(180))
        val inCurrentStop = createLocationTime(time = now.plusMinutes(210))
        val time4 = createLocationTime(time = now.plusMinutes(240))
        val afterCurrentStop = createLocationTime(time = now.plusMinutes(270))

        val previous = SimpleStartEnd(start = time1, end = time2)
        val currentOngoing = SimpleStartEnd(start = time3, end = null)
        val currentFinished = SimpleStartEnd(start = time3, end = time4)

        // 10 Minutes before start, in tolerance window
        val toleranceTimeBefore = currentFinished.start.minusMinutes(10)
        val toleranceTimeAfter = currentFinished.start.plusMinutes(10)

        // variables:
        // - stops
        //   - no previous stop + current stop ongoing
        //   - no previous stop + current stop finished
        //   - previous stop finished + current stop ongoing
        //   - previous stop finished + current stop finished
        // - tug ongoing | finished
        // - tug starts before | in | after previous stop
        // - tug ends before | in | after current stop

        return listOf(
            // 1
            // true, tug and main ship still in arrival process (both ongoing)
            TugSource(null, currentOngoing, tug(betweenStops, null), qualifies = true),
            // false, tug encounter started and stopped before arrival process
            // maybe tug helped tugging main ship into the port, but it did not help at the berth
            TugSource(null, currentOngoing, tug(betweenStops, betweenStops), qualifies = false),
            // true, tug started before and left after main ship berth start
            TugSource(null, currentOngoing, tug(betweenStops, inCurrentStop), qualifies = true),
            // false because tug starts after main ship arrival
            TugSource(null, currentOngoing, tug(inCurrentStop, null), qualifies = false),

            // 5
            // false, tug was not involved in arrival, it started and ended in main ship berth visit
            TugSource(null, currentOngoing, tug(inCurrentStop, inCurrentStop), qualifies = false),
            // false, this is a departure tug
            TugSource(null, currentOngoing, tug(afterCurrentStop, null), qualifies = false),
            // false, although tug started before main ship berth visit, tug is still ongoing,
            TugSource(null, currentFinished, tug(betweenStops, null), qualifies = false),
            // false, tug started and ended before main ship berth visit began
            TugSource(null, currentFinished, tug(betweenStops, betweenStops), qualifies = false),
            // true, started before and ended in main ship berth visit
            TugSource(null, currentFinished, tug(betweenStops, inCurrentStop), qualifies = true),

            // 10
            // false, tug did not end in berth visit
            TugSource(null, currentFinished, tug(betweenStops, afterCurrentStop), qualifies = false),
            // false, this is a departure tug
            TugSource(null, currentFinished, tug(inCurrentStop, null), qualifies = false),
            // false, tug started and ended in main ship berth visit
            TugSource(null, currentFinished, tug(inCurrentStop, inCurrentStop), qualifies = false),
            // false, this is a departure tug
            TugSource(null, currentFinished, tug(inCurrentStop, afterCurrentStop), qualifies = false),
            // false, tug encounter is fully outside main ship berth visit
            TugSource(null, currentFinished, tug(afterCurrentStop, null), qualifies = false),

            // 15
            // false, tug encounter is fully outside main ship berth visit
            TugSource(null, currentFinished, tug(afterCurrentStop, afterCurrentStop), qualifies = false),
            // false, although main ship is still arriving in berth visit, tug was already there before previous start?
            TugSource(previous, currentOngoing, tug(beforePreviousStop, null), qualifies = false),
            // false, tug encounter is outside main ship berth visit
            TugSource(previous, currentOngoing, tug(beforePreviousStop, beforePreviousStop), qualifies = false),
            TugSource(previous, currentOngoing, tug(beforePreviousStop, insidePreviousStop), qualifies = false),
            TugSource(previous, currentOngoing, tug(beforePreviousStop, betweenStops), qualifies = false),

            // 20
            // false, appears valid for arrival, but was already encountering with main ship before previous stop?
            TugSource(previous, currentOngoing, tug(beforePreviousStop, inCurrentStop), qualifies = false),
            // true, tug is tugging main ship from previous berth to this berth, arrival still ongoing
            TugSource(previous, currentOngoing, tug(insidePreviousStop, null), qualifies = true),
            // false, tug encounter was before current berth
            TugSource(previous, currentOngoing, tug(insidePreviousStop, insidePreviousStop), qualifies = false),
            TugSource(previous, currentOngoing, tug(insidePreviousStop, betweenStops), qualifies = false),
            // true, tug tugged main ship from previous berth to this berth and tug left
            TugSource(previous, currentOngoing, tug(insidePreviousStop, inCurrentStop), qualifies = true),

            // 25
            // true, arrival still in progress
            TugSource(previous, currentOngoing, tug(betweenStops, null), qualifies = true),
            // false, tug encounter is only before berth visit
            TugSource(previous, currentOngoing, tug(betweenStops, betweenStops), qualifies = false),
            // true, tug started before and left after main ship berth start
            TugSource(previous, currentOngoing, tug(betweenStops, inCurrentStop), qualifies = true),
            // false, main ship already arrived after tug arrived
            TugSource(previous, currentOngoing, tug(inCurrentStop, null), qualifies = false),
            // false, encounter is only inside berth visit
            TugSource(previous, currentOngoing, tug(inCurrentStop, inCurrentStop), qualifies = false),

            // 30
            // false, tug start was after berth visit
            TugSource(previous, currentOngoing, tug(afterCurrentStop, null), qualifies = false),
            TugSource(previous, currentOngoing, tug(afterCurrentStop, afterCurrentStop), qualifies = false),
            // false, tug is encountering main ship already before previous stop?
            TugSource(previous, currentFinished, tug(beforePreviousStop, null), qualifies = false),
            // false, tug encounter was before current berth
            TugSource(previous, currentFinished, tug(beforePreviousStop, beforePreviousStop), qualifies = false),
            // false, tug is encountering main ship already before previous stop?
            TugSource(previous, currentFinished, tug(beforePreviousStop, insidePreviousStop), qualifies = false),

            // 35
            // false, tug is encountering main ship already before previous stop?
            TugSource(previous, currentFinished, tug(beforePreviousStop, betweenStops), qualifies = false),
            // false, although ending in current stop, tug is already longer with the main ship
            TugSource(previous, currentFinished, tug(beforePreviousStop, inCurrentStop), qualifies = false),
            TugSource(previous, currentFinished, tug(beforePreviousStop, afterCurrentStop), qualifies = false),
            // false, tugging happened from previous berth to current berth, but berth visit is finished and tug not?!
            TugSource(previous, currentFinished, tug(insidePreviousStop, null), qualifies = false),
            // false, encounter not in current berth visit
            TugSource(previous, currentFinished, tug(insidePreviousStop, insidePreviousStop), qualifies = false),

            // 40
            // false, encounter not in current berth visit
            TugSource(previous, currentFinished, tug(insidePreviousStop, betweenStops), qualifies = false),
            // true, tug tugged main ship from previous berth to this berth and tug left
            TugSource(previous, currentFinished, tug(insidePreviousStop, inCurrentStop), qualifies = true),
            // false, tug kept encountering main ship after arrival, that is weird
            TugSource(previous, currentFinished, tug(insidePreviousStop, afterCurrentStop), qualifies = false),
            TugSource(previous, currentFinished, tug(betweenStops, null), qualifies = false),
            // false, encounter not in current visit
            TugSource(previous, currentFinished, tug(betweenStops, betweenStops), qualifies = false),

            // 45
            // true, tug helped with arrival, then left
            TugSource(previous, currentFinished, tug(betweenStops, inCurrentStop), qualifies = true),
            // false, tug kept encountering main ship after arrival, that is weird
            TugSource(previous, currentFinished, tug(betweenStops, afterCurrentStop), qualifies = false),
            // false, tug start should be before berth visit start
            TugSource(previous, currentFinished, tug(inCurrentStop, null), qualifies = false),
            TugSource(previous, currentFinished, tug(inCurrentStop, inCurrentStop), qualifies = false),
            // false, this is a departure tug
            TugSource(previous, currentFinished, tug(inCurrentStop, afterCurrentStop), qualifies = false),

            // 50
            // false, encounter does not overlap current berth visit
            TugSource(previous, currentFinished, tug(afterCurrentStop, null), qualifies = false),
            TugSource(previous, currentFinished, tug(afterCurrentStop, afterCurrentStop), qualifies = false),

            // 10 Minutes before should still qualify because of tolerance, 10 minutes after start should not
            TugSource(null, currentFinished, tug(toleranceTimeBefore, inCurrentStop), qualifies = true),
            TugSource(previous, currentFinished, tug(toleranceTimeBefore, inCurrentStop), qualifies = true),
            TugSource(null, currentFinished, tug(toleranceTimeAfter, toleranceTimeAfter), qualifies = false),
            // 55
            TugSource(previous, currentFinished, tug(toleranceTimeAfter, toleranceTimeAfter), qualifies = false),

        ).map { Arguments.of(it) }.stream()
    }

    @ParameterizedTest
    @MethodSource("arrivalTugsSource")
    fun arrivalTugs(tugSource: TugSource) {
        val (previousStop, currentStop, tug, qualifiesAsArrivalTug) = tugSource
        currentStop ?: fail("definition failure, currentStop must always be set")
        val encounter = NewEncounter(TUG, otherMmsi = 0, otherImo = 0, startEventId = "", tug.start, tug.end)
        val tugInfo = TugInfo(id = "0", ref = 0, encounter)
        val result = generator.findArrivalTugs(previousStop, currentStop, listOf(tugInfo))

        println(tugSource)

        if (qualifiesAsArrivalTug) {
            assertTrue(result.isNotEmpty())
            assertEquals(tugInfo, result.first())
        } else {
            // tug should not be returned, as it is not an arrival tug
            assertTrue(result.isEmpty())
        }
    }

    private fun departureTugsSource(): Stream<Arguments> {
        fun tug(start: LocationTime, end: LocationTime? = null) = SimpleStartEnd(start, end)

        // timeline
        // berth stops ----------[current]-------[ next  ]--------
        //             |----|----|---|---|---|---|---|---|----|---
        // times       now  30  60  90  120 150 180 210 240  270

        // variables:
        // - stops
        //   - current stop ongoing + no next stop
        //   - current stop finished + no next stop
        //   - current stop finished + next stop ongoing
        //   - current stop finished + next stop finished
        // - tug ongoing | finished
        // - tug starts before | in | after current stop
        // - tug ends before | in | after next stop

        val now = Instant.EPOCH
        val beforeCurrentStop = createLocationTime(time = now.plusMinutes(30))
        val time1 = createLocationTime(time = now.plusMinutes(60))
        val inCurrentStop = createLocationTime(time = now.plusMinutes(90))
        val time2 = createLocationTime(time = now.plusMinutes(120))
        val justAfterCurrentStop = createLocationTime(time = now.plusMinutes(130))
        val betweenStops = createLocationTime(time = now.plusMinutes(150))
        val justAfterBetweenStops = createLocationTime(time = now.plusMinutes(160))
        val time3 = createLocationTime(time = now.plusMinutes(180))
        val inNextStop = createLocationTime(time = now.plusMinutes(210))
        val time4 = createLocationTime(time = now.plusMinutes(240))
        val afterNextStop = createLocationTime(time = now.plusMinutes(270))

        val currentOngoing = SimpleStartEnd(start = time1, end = null)
        val currentFinished = SimpleStartEnd(start = time1, end = time2)
        val nextOngoing = SimpleStartEnd(start = time3, end = null)
        val nextFinished = SimpleStartEnd(start = time3, end = time4)

        val toleranceTimeBefore = time2.minusMinutes(10)
        val toleranceTimeAfter = time2.plusMinutes(10)

        return listOf(
            // 1
            // false, tug start expected in berth visit. This is actually an ongoing arrival
            TugSource(currentOngoing, null, tug(beforeCurrentStop, null), qualifies = false),
            // false, tug encounter outside current visit
            TugSource(currentOngoing, null, tug(beforeCurrentStop, beforeCurrentStop), qualifies = false),
            // false, tug start expected in berth visit. This depicts an arrival tug
            TugSource(currentOngoing, null, tug(beforeCurrentStop, inCurrentStop), qualifies = false),
            // false, tug start expected in berth visit
            TugSource(currentOngoing, null, tug(beforeCurrentStop, betweenStops), qualifies = false),

            // 5
            TugSource(currentOngoing, null, tug(beforeCurrentStop, inNextStop), qualifies = false),
            TugSource(currentOngoing, null, tug(beforeCurrentStop, afterNextStop), qualifies = false),
            // true, tug arrived in ongoing berth visit, indicating departure of main ship
            TugSource(currentOngoing, null, tug(inCurrentStop, null), qualifies = true),
            // false, tug started and ended in current berth visit
            TugSource(currentOngoing, null, tug(inCurrentStop, inCurrentStop), qualifies = false),
            // false, tug start expected in berth visit
            TugSource(currentFinished, null, tug(beforeCurrentStop, null), qualifies = false),

            // 10
            TugSource(currentFinished, null, tug(beforeCurrentStop, beforeCurrentStop), qualifies = false),
            // false, tug start expected in berth visit. This is actually an ongoing arrival
            TugSource(currentFinished, null, tug(beforeCurrentStop, inCurrentStop), qualifies = false),
            // true, this is an ongoing departure
            TugSource(currentFinished, null, tug(inCurrentStop, null), qualifies = true),
            // false, tug encounter start and end in current berth
            TugSource(currentFinished, null, tug(inCurrentStop, inCurrentStop), qualifies = false),
            // true, this is a finished departure
            TugSource(currentFinished, null, tug(inCurrentStop, betweenStops), qualifies = true),

            // 15
            // true, tug start after only berth visit
            TugSource(currentFinished, null, tug(betweenStops, null), qualifies = true),
            TugSource(currentFinished, null, tug(justAfterCurrentStop, null), qualifies = true),
            // true, tug start at first-half of in-between time of stops
            TugSource(currentFinished, nextOngoing, tug(justAfterCurrentStop, null), qualifies = true),

            // false, tug start at second-half of in-between time of stops
            TugSource(currentFinished, nextOngoing, tug(justAfterBetweenStops, null), qualifies = false),
            TugSource(currentFinished, nextOngoing, tug(justAfterBetweenStops, justAfterBetweenStops), qualifies = false),
            TugSource(currentFinished, nextOngoing, tug(betweenStops, null), qualifies = false),
            TugSource(currentFinished, nextOngoing, tug(betweenStops, betweenStops), qualifies = false),

            // false, tug start expected in berth visit
            TugSource(currentFinished, nextOngoing, tug(beforeCurrentStop, null), qualifies = false),
            TugSource(currentFinished, nextOngoing, tug(beforeCurrentStop, beforeCurrentStop), qualifies = false),
            TugSource(currentFinished, nextOngoing, tug(beforeCurrentStop, inCurrentStop), qualifies = false),

            // 25
            TugSource(currentFinished, nextOngoing, tug(beforeCurrentStop, betweenStops), qualifies = false),
            TugSource(currentFinished, nextOngoing, tug(beforeCurrentStop, inNextStop), qualifies = false),
            // true, tug did departure and is most likely tugging for arrival at next stop
            TugSource(currentFinished, nextOngoing, tug(inCurrentStop, null), qualifies = true),
            // false, tug encounter start and end in current berth
            TugSource(currentFinished, nextOngoing, tug(inCurrentStop, inCurrentStop), qualifies = false),
            TugSource(currentFinished, nextOngoing, tug(inCurrentStop, betweenStops), qualifies = true),

            // 30
            // true, tug did departure and is most likely tugging for arrival at next stop
            TugSource(currentFinished, nextOngoing, tug(inCurrentStop, inNextStop), qualifies = true),
            // false, tug encounter outside current berth (also for next items)
            TugSource(currentFinished, nextOngoing, tug(betweenStops, null), qualifies = false),
            TugSource(currentFinished, nextOngoing, tug(betweenStops, betweenStops), qualifies = false),
            TugSource(currentFinished, nextOngoing, tug(betweenStops, inNextStop), qualifies = false),
            TugSource(currentFinished, nextOngoing, tug(inNextStop, null), qualifies = false),

            // 35
            // false, not related to current berth
            TugSource(currentFinished, nextOngoing, tug(inNextStop, inNextStop), qualifies = false),
            // false, both current and next berth finished, but tug still ongoing, indicates other activities, not a tug
            TugSource(currentFinished, nextFinished, tug(beforeCurrentStop, null), qualifies = false),

            TugSource(currentFinished, nextFinished, tug(beforeCurrentStop, beforeCurrentStop), qualifies = false),
            // false, this is an arrival tug
            TugSource(currentFinished, nextFinished, tug(beforeCurrentStop, inCurrentStop), qualifies = false),
            // false, tug encounter encompasses current stop, indicating other activities, so not a tug
            TugSource(currentFinished, nextFinished, tug(beforeCurrentStop, betweenStops), qualifies = false),

            // 40
            // false, tug encounter encompasses current stop, indicating other activities, so not a tug
            TugSource(currentFinished, nextFinished, tug(beforeCurrentStop, inNextStop), qualifies = false),
            // false, tug encounter encompasses current and next stop, indicating other activities, so not a tug
            TugSource(currentFinished, nextFinished, tug(beforeCurrentStop, afterNextStop), qualifies = false),
            // false, current and next stops are finished, but tug still tugging, indicates other activities, not a tug
            TugSource(currentFinished, nextFinished, tug(inCurrentStop, null), qualifies = false),
            // false, tug encounter start and end in current berth
            TugSource(currentFinished, nextFinished, tug(inCurrentStop, inCurrentStop), qualifies = false),
            // true, tug tugged main ship from current berth
            TugSource(currentFinished, nextFinished, tug(inCurrentStop, betweenStops), qualifies = true),

            // 45
            // true, tug tugged ship from current berth (departure) to next berth (arrival)
            TugSource(currentFinished, nextFinished, tug(inCurrentStop, inNextStop), qualifies = true),
            // false, tug encounter ended after next stop, indicating other activities, so not a tug
            TugSource(currentFinished, nextFinished, tug(inCurrentStop, afterNextStop), qualifies = false),
            // false, tug encounter start outside current berth (also for next items)
            TugSource(currentFinished, nextFinished, tug(betweenStops, null), qualifies = false),
            TugSource(currentFinished, nextFinished, tug(betweenStops, betweenStops), qualifies = false),
            TugSource(currentFinished, nextFinished, tug(betweenStops, inNextStop), qualifies = false),

            // 50
            // false, tug encounter start outside current berth (also for next items)
            TugSource(currentFinished, nextFinished, tug(betweenStops, afterNextStop), qualifies = false),
            TugSource(currentFinished, nextFinished, tug(inNextStop, null), qualifies = false),
            TugSource(currentFinished, nextFinished, tug(inNextStop, inNextStop), qualifies = false),
            TugSource(currentFinished, nextFinished, tug(inNextStop, afterNextStop), qualifies = false),
            TugSource(currentFinished, nextFinished, tug(afterNextStop, null), qualifies = false),

            // 55
            // false, tug encounter start and end outside current berth
            TugSource(currentFinished, nextFinished, tug(afterNextStop, afterNextStop), qualifies = false),
            // Ending 10 minutes before stop should not result in tug
            TugSource(currentFinished, null, tug(inCurrentStop, toleranceTimeBefore), qualifies = false),
            TugSource(currentFinished, nextFinished, tug(inCurrentStop, toleranceTimeBefore), qualifies = false),
            // Starting 10 minutes after stop should result in qualification due to tolerance
            TugSource(currentFinished, null, tug(toleranceTimeAfter, betweenStops), qualifies = true),
            TugSource(currentFinished, null, tug(toleranceTimeAfter, betweenStops), qualifies = true),
            // 60
        ).map { Arguments.of(it) }.stream()
    }

    @ParameterizedTest
    @MethodSource("departureTugsSource")
    fun departureTugs(tugSource: TugSource) {
        val (currentStop, nextStop, tug, qualifiesAsDepartureTug) = tugSource
        currentStop ?: fail("definition failure, currentStop must always be set")
        val encounter = NewEncounter(TUG, otherMmsi = 0, otherImo = 0, startEventId = "", tug.start, tug.end)
        val tugInfo = TugInfo(id = "0", ref = 0, encounter)
        val result = generator.findDepartureTugs(currentStop, nextStop, listOf(tugInfo))

        println(tugSource)

        if (qualifiesAsDepartureTug) {
            assertTrue(result.isNotEmpty())
            assertEquals(tugInfo, result.first())
        } else {
            // tug should not be returned, as it is not an arrival tug
            assertTrue(result.isEmpty())
        }
    }

    data class TugSource(
        val stop1: SimpleStartEnd?,
        val stop2: SimpleStartEnd?,
        val tug: SimpleStartEnd,
        val qualifies: Boolean
    ) {
        init {
            stop1 ?: stop2 ?: throw Exception("At least one stop needs to be set")
        }
    }

    data class GroupBerthVisitsByTerminalSource(
        val visits: List<BerthVisitInfo>,
        val expected: List<Pair<String, List<Int>>>
    )

    private fun groupBerthVisitsByTerminalSource(): Stream<Arguments> {
        val terminalA = "terminalA"
        val terminalB = "terminalB"
        val timeline = Timeline()
        fun berthVisit(terminalId: String?) = berthVisit(
            id = "BERTH_VISIT-$terminalId",
            terminalId = terminalId,
            startEnd = timeline.generatePair()
        )

        return listOf(

            // 1 - no berth visits
            GroupBerthVisitsByTerminalSource(
                visits = emptyList(),
                expected = emptyList()
            ),

            // 2 - one visit to berth without terminal
            GroupBerthVisitsByTerminalSource(
                visits = listOf(berthVisit(terminalId = null)),
                expected = emptyList()
            ),

            // 3 - multiple visit to berth without terminal
            GroupBerthVisitsByTerminalSource(
                visits = listOf(
                    berthVisit(terminalId = null),
                    berthVisit(terminalId = null)
                ),
                expected = emptyList()
            ),

            // 3 - one visit to berth with terminal A
            GroupBerthVisitsByTerminalSource(
                visits = listOf(berthVisit(terminalA)),
                expected = listOf(terminalA to listOf(0))
            ),

            // 4 - multiple visit to berth with terminal A
            GroupBerthVisitsByTerminalSource(
                visits = listOf(
                    berthVisit(terminalA),
                    berthVisit(terminalA)
                ),
                expected = listOf(terminalA to listOf(0, 1))
            ),

            // 5 - one berth with terminal A, then one with terminal B
            GroupBerthVisitsByTerminalSource(
                visits = listOf(
                    berthVisit(terminalA),
                    berthVisit(terminalB)
                ),
                expected = listOf(
                    terminalA to listOf(0),
                    terminalB to listOf(1),
                )
            ),

            // 6 - two berths adjacent with terminal A, then one with terminal B
            GroupBerthVisitsByTerminalSource(
                visits = listOf(
                    berthVisit(terminalA),
                    berthVisit(terminalA),
                    berthVisit(terminalB),
                ),
                expected = listOf(
                    terminalA to listOf(0, 1),
                    terminalB to listOf(2),
                )
            ),

            // 7 - two berths adjacent with terminal A, then two with terminal B
            GroupBerthVisitsByTerminalSource(
                visits = listOf(
                    berthVisit(terminalA),
                    berthVisit(terminalA),
                    berthVisit(terminalB),
                    berthVisit(terminalB),
                ),
                expected = listOf(
                    terminalA to listOf(0, 1),
                    terminalB to listOf(2, 3),
                )
            ),

            // 8 - two berths adjacent with terminal A, then one without terminal, then one with terminal A
            GroupBerthVisitsByTerminalSource(
                visits = listOf(
                    berthVisit(terminalA),
                    berthVisit(terminalA),
                    berthVisit(terminalId = null),
                    berthVisit(terminalB),
                    berthVisit(terminalB),
                ),
                expected = listOf(
                    terminalA to listOf(0, 1),
                    terminalB to listOf(3, 4),
                )
            ),

            // 9 - two berths adjacent with terminal A, then one without terminal, then one with terminal B
            GroupBerthVisitsByTerminalSource(
                visits = listOf(
                    berthVisit(terminalA),
                    berthVisit(terminalA),
                    berthVisit(terminalId = null),
                    berthVisit(terminalId = null),
                    berthVisit(terminalB),
                    berthVisit(terminalB),
                ),
                expected = listOf(
                    terminalA to listOf(0, 1),
                    terminalB to listOf(4, 5),
                )
            ),
        ).map { Arguments.of(it) }.stream()
    }

    @ParameterizedTest
    @MethodSource("groupBerthVisitsByTerminalSource")
    fun groupBerthVisitsByTerminal(source: GroupBerthVisitsByTerminalSource) {
        val result = generator.groupBerthVisitsByTerminal(source.visits)
        val expected = source.expected.map { (terminalId, expectedVisitIndices) ->
            BerthVisitGroup(terminalId, expectedVisitIndices.map { index -> source.visits[index] }.toMutableList())
        }
        assertEquals(expected, result)
    }

    private fun berthVisit(
        id: String,
        ref: Int = 0,
        terminalId: String? = null,
        startEnd: Pair<LocationTime, LocationTime>,
        berth: Berth = createBerth(_id = "berth-$ref", name = "berth-$ref", terminalId = terminalId),
        portAreaInfo: PortAreaInfo? = null,
        terminalAreaVisit: TerminalVisitInfo? = null,
        arrivalTugs: List<TugInfo> = emptyList(),
        departureTugs: List<TugInfo> = emptyList(),
    ) = BerthVisitInfo(
        id = id,
        ref = ref,
        activity = createAreaActivity(
            id = UUID.randomUUID().toString(),
            start = startEnd.first,
            end = startEnd.second,
            areaId = berth._id ?: throw Exception("Need a berth id")
        ),
        area = berth,
        portArea = portAreaInfo,
        arrivalTugs = arrivalTugs,
        departureTugs = departureTugs,
        firstLineSecured = null,
        allFast = null,
        lastLineReleased = null,
        terminalVisit = terminalAreaVisit
    )

    @Test
    fun `generate terminal visits - no berth visits`() {
        assertEquals(
            emptyList<TerminalVisitInfo>(),
            generator.generateTerminalVisits(berthVisitGroups = emptyList(), terminalMoorings = emptyList())
        )
    }

    @Test
    fun `generate terminal visits - one berth visit`() {
        val terminalId = "terminalA"
        val terminal = createTerminal(_id = terminalId)
        val timeline = Timeline()
        val berthVisit = berthVisit(
            id = "TEST_BERTH_VISIT_ID",
            ref = 1,
            terminalId = "terminalA",
            startEnd = timeline.generatePair()
        )
        val visitGroup = BerthVisitGroup(
            terminalId = "terminalA",
            visits = mutableListOf(berthVisit)
        )
        whenever(infraService.getById(eq(terminalId), eq(InfraAreaType.TERMINAL))).thenReturn(terminal)

        val expected = TerminalVisitInfo(
            ref = 0,
            activity = AreaActivity(
                id = "TEST_BERTH_VISIT_ID",
                start = berthVisit.start,
                end = berthVisit.end,
                areaId = terminalId
            ),
            mooringActivity = null,
            area = terminal,
            portArea = berthVisit.portArea,
            berthVisits = listOf(berthVisit)
        )

        assertEquals(
            listOf(expected),
            generator.generateTerminalVisits(listOf(visitGroup), terminalMoorings = emptyList())
        )
    }

    @Test
    fun `generate terminal visits - two berth visits`() {
        val timeline = Timeline()
        val terminalId = "terminalA"
        val terminal = createTerminal(_id = terminalId)
        val port = createPort()
        val portArea = PortAreaInfo(
            ref = 0,
            isPassThrough = false,
            createAreaActivity(start = timeline.generate(), end = timeline.generate(), areaId = port._id!!),
            area = port
        )
        val berthVisit1 = berthVisit(
            id = "BERTH_VISIT_START_1",
            ref = 1,
            terminalId = "terminalA",
            startEnd = timeline.generatePair(),
            portAreaInfo = portArea
        )
        val berthVisit2 = berthVisit(
            id = "BERTH_VISIT_START_2",
            ref = 2,
            terminalId = "terminalA",
            startEnd = timeline.generatePair(),
            portAreaInfo = portArea
        )
        val visitGroup = BerthVisitGroup(
            terminalId = "terminalA",
            visits = mutableListOf(berthVisit1, berthVisit2)
        )
        whenever(infraService.getById(eq(terminalId), eq(InfraAreaType.TERMINAL))).thenReturn(terminal)

        val expected = TerminalVisitInfo(
            ref = 0,
            activity = AreaActivity(
                id = "BERTH_VISIT_START_1",
                start = berthVisit1.start,
                end = berthVisit2.end,
                areaId = terminalId
            ),
            mooringActivity = null,
            area = terminal,
            portArea = portArea,
            berthVisits = listOf(berthVisit1, berthVisit2)
        )

        assertEquals(
            listOf(expected),
            generator.generateTerminalVisits(listOf(visitGroup), terminalMoorings = emptyList())
        )
    }

    @Test
    fun `generateAnchorStops - other types give an empty list`() {
        assertEquals(
            emptyList<AnchorStopInfo>(),
            generator.generateAnchorStops(visitId, listOf(createNewStop(BERTH)), emptyList())
        )
        assertEquals(
            emptyList<AnchorStopInfo>(),
            generator.generateAnchorStops(visitId, listOf(createNewStop(UNCLASSIFIED)), emptyList())
        )
    }

    @Test
    fun `generateAnchorStops - port area not found still results in an anchor stop`() {
        val anchorage = createAnchorage(_id = ANCHOR_AREA_4EAST, ports = emptyList())
        val stop = createNewStop(type = ANCHOR_AREA, areaId = ANCHOR_AREA_4EAST)
        val portAreas = emptyList<PortAreaInfo>()
        whenever(infraService.getById(eq(ANCHOR_AREA_4EAST), eq(InfraAreaType.ANCHOR))).thenReturn(anchorage)
        val result = generator.generateAnchorStops(visitId, listOf(stop), portAreas)

        val expected = AnchorStopInfo("$visitId.${stop.startEventId}", stop, anchorage, portArea = null)
        assertTrue(result.size == 1)
        assertEquals(expected, result.first())
    }

    @Test
    fun generateAnchorStops() {
        val anchorage = createAnchorage(_id = ANCHOR_AREA_4EAST, ports = listOf(NLRTM))
        val stop = createNewStop(type = ANCHOR_AREA, areaId = ANCHOR_AREA_4EAST)
        val port = createPort(_id = NLRTM)
        val portArea = PortAreaInfo(
            ref = 0,
            isPassThrough = false,
            activity = createAreaActivity(areaId = NLRTM),
            area = port
        )
        whenever(infraService.getById(eq(ANCHOR_AREA_4EAST), eq(InfraAreaType.ANCHOR))).thenReturn(anchorage)
        val result = generator.generateAnchorStops(visitId, listOf(stop), listOf(portArea))

        val expected = AnchorStopInfo("$visitId.${stop.startEventId}", stop, anchorage, portArea)
        assertTrue(result.size == 1)
        assertEquals(expected, result.first())
    }

    @Test
    fun `generateUnclassifiedStops - other stop types are ignored`() {
        val stops = listOf(
            createNewStop(type = BERTH),
            createNewStop(type = ANCHOR_AREA),
        )
        assertEquals(
            emptyList<UnclassifiedStopInfo>(),
            generator.generateUnclassifiedStops(visitId, stops, portAreas = emptyList(), eospAreaId = "")
        )
    }

    @Test
    fun `generateUnclassifiedStops - time outside main port inner area`() {
        val timeline = Timeline()
        val outsidePort = timeline.generate()
        val (portStart, portEnd) = timeline.generatePair()
        val nlrtmNoPolygon = PortAreaInfo(
            ref = 1,
            isPassThrough = false,
            activity = createAreaActivity(areaId = NLRTM, start = portStart, end = portEnd),
            area = createPort(_id = NLRTM)
        )
        val eospAreaId = nlrtmNoPolygon.area._id!!

        val stop1 = createNewStop(type = UNCLASSIFIED, start = outsidePort)
        assertEquals(
            listOf(UnclassifiedStopInfo("$visitId.${stop1.startEventId}", stop1, portArea = null)),
            generator.generateUnclassifiedStops(visitId, listOf(stop1), portAreas = listOf(nlrtmNoPolygon), eospAreaId)
        )
        assertEquals(
            listOf(UnclassifiedStopInfo("$visitId.${stop1.startEventId}", stop1, portArea = null)),
            generator.generateUnclassifiedStops(visitId, listOf(stop1), portAreas = listOf(nlrtmNoPolygon), eospAreaId)
        )

        val stop2 = createNewStop(type = UNCLASSIFIED, start = outsidePort)
        assertEquals(
            listOf(UnclassifiedStopInfo("$visitId.${stop2.startEventId}", stop2, portArea = null)),
            generator.generateUnclassifiedStops(visitId, listOf(stop2), portAreas = emptyList(), eospAreaId)
        )
        assertEquals(
            listOf(UnclassifiedStopInfo("$visitId.${stop2.startEventId}", stop2, portArea = null)),
            generator.generateUnclassifiedStops(visitId, listOf(stop2), portAreas = listOf(nlrtmNoPolygon), eospAreaId)
        )
    }

    @Test
    fun `generateUnclassifiedStops - matches port area polygon when in port`() {
        val timeline = Timeline()
        val portStart = timeline.generate()
        val insidePort = timeline.generate()
        val portEnd = timeline.generate()

        val nlrtm = PortAreaInfo(
            ref = 1,
            isPassThrough = false,
            activity = createAreaActivity(areaId = NLRTM, start = portStart, end = portEnd),
            area = createPort(_id = NLRTM)
        )
        val eospAreaId = nlrtm.area._id!!

        val stop1 = createNewStop(type = UNCLASSIFIED, start = insidePort)
        assertEquals(
            listOf(UnclassifiedStopInfo("$visitId.${stop1.startEventId}", stop1, nlrtm)),
            generator.generateUnclassifiedStops(visitId, listOf(stop1), listOf(nlrtm), eospAreaId)
        )

        val stop2 = createNewStop(type = UNCLASSIFIED, start = insidePort)
        assertEquals(
            listOf(UnclassifiedStopInfo("$visitId.${stop2.startEventId}", stop2, nlrtm)),
            generator.generateUnclassifiedStops(visitId, listOf(stop2), listOf(nlrtm), eospAreaId)
        )
    }

    @Test
    fun `generateEncounters - no matching references`() {
        val encounter = NewEncounter(
            type = EncounterType.UNCLASSIFIED,
            otherImo = 1,
            otherMmsi = 2,
            startEventId = "",
            start = createLocationTime(),
            end = createLocationTime()
        )
        val expected = EncounterInfo(
            id = "$visitId.${encounter.startEventId}",
            encounter = encounter,
            portAreaInfo = null,
            berthVisitInfo = null,
            terminalVisitInfo = null
        )
        assertEquals(
            listOf(expected),
            generator.generateEncounters(
                visitId = visitId,
                encounters = listOf(encounter),
                portAreas = emptyList(),
                berthVisits = emptyList(),
                terminalVisits = emptyList()
            )
        )
    }

    @Test
    fun `generateEncounters - matches berth visit`() {
        val timeline = Timeline()
        val (startPort, startBerth, startEncounter) = timeline.generateN(3)
        val (endEncounter, endBerth, endPort) = timeline.generateN(3)
        val berthActivity = createAreaActivity(start = startBerth, end = endBerth, areaId = "")
        val portActivity = createAreaActivity(start = startPort, end = endPort, areaId = "")
        val portAreaInfo = PortAreaInfo(ref = 1, isPassThrough = false, activity = portActivity, area = mock())

        // first check has no terminal visit; terminal visit will be attached before the 2nd check!
        val berthVisit = createBerthVisitInfo(id = "BERTH_START_ID_1", ref = 2, activity = berthActivity)
        // only for 2nd check!
        val terminalVisit = createTerminalVisitInfo(
            ref = 3,
            activity = berthActivity,
            berthVisits = listOf(berthVisit)
        )

        val encounter = NewEncounter(
            type = EncounterType.UNCLASSIFIED,
            otherImo = 1,
            otherMmsi = 2,
            startEventId = "",
            start = startEncounter,
            end = endEncounter
        )

        // berth visit with no terminal visit
        assertEquals(
            listOf(
                EncounterInfo(
                    id = "$visitId.${encounter.startEventId}",
                    encounter = encounter,
                    portAreaInfo = portAreaInfo,
                    berthVisitInfo = berthVisit,
                    terminalVisitInfo = null
                )
            ),
            generator.generateEncounters(
                visitId = visitId,
                encounters = listOf(encounter),
                portAreas = listOf(portAreaInfo),
                berthVisits = listOf(berthVisit),
                terminalVisits = emptyList()
            )
        )

        // berth visit with terminal visit
        val berthVisitWithTerminalVisit = berthVisit.copy(terminalVisit = terminalVisit)
        assertEquals(
            listOf(
                EncounterInfo(
                    id = "$visitId.${encounter.startEventId}",
                    encounter = encounter,
                    portAreaInfo = portAreaInfo,
                    berthVisitInfo = berthVisitWithTerminalVisit,
                    terminalVisitInfo = terminalVisit
                )
            ),
            generator.generateEncounters(
                visitId = visitId,
                encounters = listOf(encounter),
                portAreas = listOf(portAreaInfo),
                berthVisits = listOf(berthVisitWithTerminalVisit),
                terminalVisits = listOf(terminalVisit)
            )
        )
    }

    data class PilotTest(
        val encounters: List<NewEncounter>,
        val anchorVisit: List<AnchorStopInfo>,
        val berthVisits: List<BerthVisitInfo>,
        val pilotAreaActivities: List<AreaActivity>,
        val expectedResult: PilotInfo?
    )

    private fun findInboundPilotSource(): Stream<Arguments> {
        val timeline = Timeline()
        val beforeAnchor = timeline.generatePair()
        val (anchorStart, anchorEnd) = timeline.generateN(2)
        val beforeBerth = timeline.generatePair()
        val (berthStart, pilotDuringBerthStart, pilotDuringBerthEnd, berthEnd) = timeline.generateN(4)
        val duringBerth = Pair(pilotDuringBerthStart, pilotDuringBerthEnd)
        val afterBerth = timeline.generatePair()

        val pilotBeforeAnchor = createNewEncounter(PILOT, beforeAnchor)
        val pilotBeforeBerth = createNewEncounter(PILOT, beforeBerth)
        val pilotDuringBerth = createNewEncounter(PILOT, duringBerth)
        val pilotAfterBerth = createNewEncounter(PILOT, afterBerth)
        val berthVisit = createBerthVisitInfo(
            id = "BERTH_START_ID_1",
            ref = 0,
            activity = createAreaActivity(id = "BERTH_START_ID_1", start = berthStart, end = berthEnd, areaId = "")
        )
        val anchorVisit = AnchorStopInfo(
            id = "$visitId.TEST_ANCHOR_STOP",
            stop = createNewStop(
                startEventId = "TEST_ANCHOR_STOP",
                type = ANCHOR_AREA,
                start = anchorStart,
                end = anchorEnd
            ),
            area = null,
            portArea = null
        )

        val earlierPilot = pilotBeforeBerth.copy(
            start = with(pilotBeforeBerth.start) { copy(time = time.minusSeconds(10L)) },
            end = with(pilotBeforeBerth.end) { this?.copy(time = time.minusSeconds(10L)) }
        )

        return Stream.of(

            // (1) no pilot encounters, no berth visits
            PilotTest(
                encounters = emptyList(),
                berthVisits = emptyList(),
                anchorVisit = emptyList(),
                pilotAreaActivities = emptyList(),
                expectedResult = null
            ),

            // (2) no inbound pilot, needs a berth visit to classify a pilot as inbound or outbound
            PilotTest(
                encounters = listOf(pilotBeforeBerth, pilotDuringBerth, pilotAfterBerth),
                berthVisits = emptyList(),
                anchorVisit = emptyList(),
                pilotAreaActivities = emptyList(),
                expectedResult = null
            ),

            // (3) no inbound pilot, pilot starts during first berth, but needs to start before first berth
            PilotTest(
                encounters = listOf(pilotDuringBerth),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = emptyList(),
                expectedResult = null
            ),

            // (4) no inbound pilot, pilot starts after first berth, but needs to start before first berth
            PilotTest(
                encounters = listOf(pilotAfterBerth),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = emptyList(),
                expectedResult = null
            ),

            // (5) inbound pilot found, starts before first berth
            PilotTest(
                encounters = listOf(pilotBeforeBerth),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = emptyList(),
                expectedResult = PilotInfo.fromPilotShipEncounter(visitId, encounter = pilotBeforeBerth, pilotArea = null)
            ),

            // (6) inbound pilot is the closest pilot before the berth visit, even if there is an earlier pilot
            PilotTest(
                encounters = listOf(earlierPilot, pilotBeforeBerth),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = emptyList(),
                expectedResult = PilotInfo.fromPilotShipEncounter(visitId, encounter = pilotBeforeBerth, pilotArea = null)
            ),

            // (7) Inbound pilot should be the pilot before anchoring when no berth visit is detected
            PilotTest(
                encounters = listOf(pilotBeforeAnchor, pilotBeforeBerth),
                berthVisits = emptyList(),
                anchorVisit = listOf(anchorVisit),
                pilotAreaActivities = emptyList(),
                expectedResult = PilotInfo.fromPilotShipEncounter(visitId, encounter = pilotBeforeAnchor, pilotArea = null)
            ),

            // (8) Inbound pilot should be the pilot after the anchor visit, but before the berth visit
            PilotTest(
                encounters = listOf(pilotBeforeAnchor, pilotBeforeBerth),
                berthVisits = listOf(berthVisit),
                anchorVisit = listOf(anchorVisit),
                pilotAreaActivities = emptyList(),
                expectedResult = PilotInfo.fromPilotShipEncounter(visitId, encounter = pilotBeforeBerth, pilotArea = null)
            ),

            // (9) Inbound pilot should NOT be set when the pilot is after the anchor visit
            PilotTest(
                encounters = listOf(pilotBeforeBerth),
                berthVisits = emptyList(),
                anchorVisit = listOf(anchorVisit),
                pilotAreaActivities = emptyList(),
                expectedResult = null
            )
        ).map { Arguments.of(it) }
    }

    @ParameterizedTest
    @MethodSource("findInboundPilotSource")
    fun findInboundPilot(test: PilotTest) {
        assertEquals(
            test.expectedResult,
            generator.findInboundPilot(
                visitId = visitId,
                encounters = test.encounters,
                berthVisits = test.berthVisits,
                anchorVisits = test.anchorVisit,
                pilotAreaActivities = test.pilotAreaActivities
            )
        )
    }

    private fun findOutboundPilotSource(): Stream<Arguments> {

        val timeline = Timeline()
        val beforeAnchor = timeline.generatePair()
        val (anchorStart, anchorEnd) = timeline.generateN(2)
        val afterAnchor = timeline.generatePair()
        val (anchorStart2, anchorEnd2) = timeline.generateN(2)
        val afterAnchor2 = timeline.generatePair()
        val beforeBerth = timeline.generatePair()
        val (berthStart, pilotDuringBerthStart, pilotDuringBerthEnd, berthEnd) = timeline.generateN(4)
        val duringBerth = Pair(pilotDuringBerthStart, pilotDuringBerthEnd)
        val afterBerth = timeline.generatePair()

        val pilotBeforeAnchor = createNewEncounter(PILOT, beforeAnchor)
        val pilotBeforeBerth = createNewEncounter(PILOT, beforeBerth)
        val pilotAfterAnchor1 = createNewEncounter(PILOT, afterAnchor)
        val pilotAfterAnchor2 = createNewEncounter(PILOT, afterAnchor2)
        val pilotDuringBerth = createNewEncounter(PILOT, duringBerth)
        val pilotAfterBerth = createNewEncounter(PILOT, afterBerth)
        val berthVisit = createBerthVisitInfo(
            id = "BERTH_START_ID_1",
            ref = 0,
            activity = createAreaActivity(id = "BERTH_START_ID_1", start = berthStart, end = berthEnd, areaId = "")
        )
        val anchorVisit = AnchorStopInfo(
            id = "$visitId.ANCHOR_STOP_ID}",
            stop = createNewStop(
                startEventId = "ANCHOR_STOP_ID",
                type = ANCHOR_AREA,
                start = anchorStart,
                end = anchorEnd
            ),
            area = null,
            portArea = null
        )
        val anchorVisit2 = AnchorStopInfo(
            id = "$visitId.ANCHOR_STOP_ID_2}",
            stop = createNewStop(
                startEventId = "ANCHOR_STOP_ID_2",
                type = ANCHOR_AREA,
                start = anchorStart2,
                end = anchorEnd2
            ),
            area = null,
            portArea = null
        )

        // for case 6
        val laterPilot = pilotAfterBerth.copy(
            start = with(pilotAfterBerth.start) { copy(time = time.plusSeconds(10L)) },
            end = with(pilotAfterBerth.end) { this?.copy(time = time.plusSeconds(10L)) }
        )

        return Stream.of(
            // (1) no pilot encounters, no berth visits
            PilotTest(
                encounters = emptyList(),
                berthVisits = emptyList(),
                anchorVisit = emptyList(),
                pilotAreaActivities = emptyList(),
                expectedResult = null
            ),

            // (2) no outbound pilot, needs a berth visit to classify a pilot as outbound or outbound
            PilotTest(
                encounters = listOf(pilotBeforeBerth, pilotDuringBerth, pilotAfterBerth),
                berthVisits = emptyList(),
                anchorVisit = emptyList(),
                pilotAreaActivities = emptyList(),
                expectedResult = null
            ),

            // (3) no outbound pilot, pilot starts before last berth, but needs to start after last berth
            PilotTest(
                encounters = listOf(pilotBeforeBerth),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = emptyList(),
                expectedResult = null
            ),

            // (4) no outbound pilot found, pilot starts during last berth
            PilotTest(
                encounters = listOf(pilotDuringBerth),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = emptyList(),
                expectedResult = null
            ),

            // (5) outbound pilot found, starts after last berth
            PilotTest(
                encounters = listOf(pilotAfterBerth),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = emptyList(),
                expectedResult = PilotInfo.fromPilotShipEncounter(visitId, encounter = pilotAfterBerth, pilotArea = null)
            ),

            // (6) outbound pilot is first pilot just after the berth
            PilotTest(
                encounters = listOf(pilotAfterBerth, laterPilot),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = emptyList(),
                expectedResult = PilotInfo.fromPilotShipEncounter(visitId, encounter = pilotAfterBerth, pilotArea = null)
            ),

            // (7) Outbound pilot should be the pilot after leaving the last anchorage when we don't go to a berth
            PilotTest(
                encounters = listOf(pilotBeforeAnchor, pilotAfterAnchor1, pilotAfterAnchor2),
                berthVisits = emptyList(),
                anchorVisit = listOf(anchorVisit, anchorVisit2),
                pilotAreaActivities = emptyList(),
                expectedResult = PilotInfo.fromPilotShipEncounter(visitId, encounter = pilotAfterAnchor2, pilotArea = null)
            ),

            // (8) Outbound pilot should NOT be set as we went to a berth after the last pilot
            PilotTest(
                encounters = listOf(pilotBeforeAnchor, pilotAfterAnchor1, pilotAfterAnchor2),
                berthVisits = listOf(berthVisit),
                anchorVisit = listOf(anchorVisit),
                pilotAreaActivities = emptyList(),
                expectedResult = null
            )
        ).map { Arguments.of(it) }
    }

    @ParameterizedTest
    @MethodSource("findOutboundPilotSource")
    fun findOutboundPilot(test: PilotTest) {
        assertEquals(
            test.expectedResult,
            generator.findOutboundPilot(
                visitId = visitId,
                encounters = test.encounters,
                berthVisits = test.berthVisits,
                anchorVisits = test.anchorVisit,
                pilotAreaActivities = test.pilotAreaActivities
            )
        )
    }

    private fun findInboundPilotByAreaFallbackSource(): Stream<Arguments> {

        val timeline = Timeline()
        val beforeBerth = timeline.generatePair()
        val (berthStart, pilotDuringBerthStart, pilotDuringBerthEnd, berthEnd) = timeline.generateN(4)
        val duringBerth = Pair(pilotDuringBerthStart, pilotDuringBerthEnd)
        val afterBerth = timeline.generatePair()

        val pilotBeforeBerth = createAreaActivity(beforeBerth)
        val pilotDuringBerth = createAreaActivity(duringBerth)
        val pilotAfterBerth = createAreaActivity(afterBerth)
        val berthVisit = createBerthVisitInfo(
            id = "BERTH_START_ID_1",
            ref = 0,
            activity = createAreaActivity(id = "BERTH_START_ID_1", start = berthStart, end = berthEnd, areaId = "")
        )

        // for case 6
        val earlierPilot = pilotBeforeBerth.copy(
            start = with(pilotBeforeBerth.start) { copy(time = time.minusSeconds(10L)) },
            end = with(pilotBeforeBerth.end) { this?.copy(time = time.minusSeconds(10L)) }
        )

        return Stream.of(

            // (1) no pilot area activity, no berth visits
            PilotTest(
                encounters = emptyList(),
                berthVisits = emptyList(),
                anchorVisit = emptyList(),
                pilotAreaActivities = emptyList(),
                expectedResult = null
            ),

            // (2) no inbound pilot, needs a berth visit to classify a pilot as inbound or outbound
            PilotTest(
                encounters = emptyList(),
                berthVisits = emptyList(),
                anchorVisit = emptyList(),
                pilotAreaActivities = listOf(pilotBeforeBerth, pilotDuringBerth, pilotAfterBerth),
                expectedResult = null
            ),

            // (3) no inbound pilot, pilot starts during first berth, but needs to start before first berth
            PilotTest(
                encounters = emptyList(),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = listOf(pilotDuringBerth),
                expectedResult = null
            ),

            // (4) no inbound pilot, pilot starts after first berth, but needs to start before first berth
            PilotTest(
                encounters = emptyList(),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = listOf(pilotAfterBerth),
                expectedResult = null
            ),

            // (5) inbound pilot found, starts before first berth
            PilotTest(
                encounters = emptyList(),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = listOf(pilotBeforeBerth),
                expectedResult = PilotInfo.fromPilotAreaActivity(visitId, pilotAreaActivity = pilotBeforeBerth, pilotArea = null)
            ),

            // (6) inbound pilot is second pilot, that is the last one before the first berth
            // create an earlier pilot, does not matter if those overlap, as long as the encounter.start is earlier
            PilotTest(
                encounters = emptyList(),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = listOf(earlierPilot, pilotBeforeBerth),
                expectedResult = PilotInfo.fromPilotAreaActivity(visitId, pilotAreaActivity = pilotBeforeBerth, pilotArea = null)
            )
        ).map { Arguments.of(it) }
    }

    @ParameterizedTest
    @MethodSource("findInboundPilotByAreaFallbackSource")
    fun findInboundPilotByAreaFallback(test: PilotTest) {
        assertEquals(
            test.expectedResult,
            generator.findInboundPilotByAreaFallback(visitId, test.pilotAreaActivities, test.berthVisits.firstOrNull())
        )
    }

    private fun findOutboundPilotByAreaFallbackSource(): Stream<Arguments> {

        val timeline = Timeline()
        val beforeBerth = timeline.generatePair()
        val (berthStart, pilotDuringBerthStart, pilotDuringBerthEnd, berthEnd) = timeline.generateN(4)
        val duringBerth = Pair(pilotDuringBerthStart, pilotDuringBerthEnd)
        val afterBerth = timeline.generatePair()

        val pilotBeforeBerth = createAreaActivity(beforeBerth)
        val pilotDuringBerth = createAreaActivity(duringBerth)
        val pilotAfterBerth = createAreaActivity(afterBerth)
        val berthVisit = createBerthVisitInfo(
            id = "BERTH_START_ID_1",
            ref = 0,
            activity = createAreaActivity(id = "BERTH_START_ID_1", start = berthStart, end = berthEnd, areaId = "")
        )

        // for case 6
        val laterPilot = pilotAfterBerth.copy(
            start = with(pilotAfterBerth.start) { copy(time = time.plusSeconds(10L)) },
            end = with(pilotAfterBerth.end) { this?.copy(time = time.plusSeconds(10L)) }
        )

        return Stream.of(
            // (1) no pilot encounters, no berth visits
            PilotTest(
                encounters = emptyList(),
                berthVisits = emptyList(),
                anchorVisit = emptyList(),
                pilotAreaActivities = emptyList(),
                expectedResult = null
            ),

            // (2) no outbound pilot, needs a berth visit to classify a pilot as outbound or outbound
            PilotTest(
                encounters = emptyList(),
                berthVisits = emptyList(),
                anchorVisit = emptyList(),
                pilotAreaActivities = listOf(pilotBeforeBerth, pilotDuringBerth, pilotAfterBerth),
                expectedResult = null
            ),

            // (3) no outbound pilot, pilot starts before last berth, but needs to start after last berth
            PilotTest(
                encounters = emptyList(),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = listOf(pilotBeforeBerth),
                expectedResult = null
            ),

            // (4) no outbound pilot found, pilot starts during last berth
            PilotTest(
                encounters = emptyList(),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = listOf(pilotDuringBerth),
                expectedResult = null
            ),

            // (5) outbound pilot found, starts after last berth
            PilotTest(
                encounters = emptyList(),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = listOf(pilotAfterBerth),
                expectedResult = PilotInfo.fromPilotAreaActivity(visitId, pilotAreaActivity = pilotAfterBerth, pilotArea = null)
            ),

            // (6) outbound pilot is last pilot and not the pilot just after the berth
            PilotTest(
                encounters = emptyList(),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = listOf(pilotAfterBerth, laterPilot),
                expectedResult = PilotInfo.fromPilotAreaActivity(visitId, pilotAreaActivity = laterPilot, pilotArea = null)
            )
        ).map { Arguments.of(it) }
    }

    @ParameterizedTest
    @MethodSource("findOutboundPilotByAreaFallbackSource")
    fun findOutboundPilotByAreaFallback(test: PilotTest) {
        assertEquals(
            test.expectedResult,
            generator.findOutboundPilotByAreaFallback(visitId, test.pilotAreaActivities, test.berthVisits.lastOrNull())
        )
    }

    private fun pilotInboundOutboundAreaMatchingSource(): Stream<Arguments> {

        // note that the berth visit is created in the test method, so this source can be re-used for inbound and
        // outbound

        // The encounter (e.g. pilot-1) must overlap time range of a pilot area activity (e.g. pilot area A) to get
        // pbp1 as part of the expected result.

        // Imagine a ship sailed through these pilot areas:
        //     ----[  pilot area A ]-----[ pilot area B ]----------------------------
        // With these pilot encounters as test cases:
        // (1) --[ pilot-1 ]-------:-----:--------------:----------------------------
        // (2) ----:--[ pilot-2 ]--:-----:--------------:----------------------------
        // (3) ----:-------[ pilot-3 ]---:--------------:----------------------------
        // (4) ----:----------[ both-overlapping ]------:----------------------------
        // (5) ----:---------------:-----:--------------:-----[ non-overlapping ]----

        val duration = Duration.ofSeconds(60)
        val timeline = Timeline(timeBetweenEvents = duration)

        val pilotAreaA = createAreaActivity(timeline.generatePair(), areaId = "pbp-A")
        val pilotAreaB = createAreaActivity(timeline.generatePair(), areaId = "pbp-B")
        val pilotAreaActivities = listOf(pilotAreaA, pilotAreaB)

        val pbp1 = createPilotBoardingPlace(_id = "pbp-A")

        val overlap1 = createNewEncounter(PILOT, start = pilotAreaA.start - 15L, end = pilotAreaA.end!! - 15L)
        val overlap2 = createNewEncounter(PILOT, start = pilotAreaA.start + 15L, end = pilotAreaA.end!! - 15L)
        val overlap3 = createNewEncounter(PILOT, start = pilotAreaA.start + 15L, end = pilotAreaA.end!! + 15L)
        val bothOverlap = createNewEncounter(PILOT, start = pilotAreaA.end!! - 15L, end = pilotAreaB.start + 15L)
        val nonOverlapping = createNewEncounter(PILOT, timeline.generatePair())

        return Stream.of(
            // (1) pbp1 match: overlaps on left side of pilot area A
            PilotTest(
                encounters = listOf(overlap1),
                berthVisits = emptyList(),
                anchorVisit = emptyList(),
                pilotAreaActivities = pilotAreaActivities,
                expectedResult = PilotInfo.fromPilotShipEncounter(visitId, encounter = overlap1, pilotArea = pbp1)
            ),

            // (2) pbp1 match: overlaps on inside pilot area A
            PilotTest(
                encounters = listOf(overlap2),
                berthVisits = emptyList(),
                anchorVisit = emptyList(),
                pilotAreaActivities = pilotAreaActivities,
                expectedResult = PilotInfo.fromPilotShipEncounter(visitId, encounter = overlap2, pilotArea = pbp1)
            ),

            // (3) pbp1 match: overlaps on left side of pilot area A
            PilotTest(
                encounters = listOf(overlap3),
                berthVisits = emptyList(),
                anchorVisit = emptyList(),
                pilotAreaActivities = pilotAreaActivities,
                expectedResult = PilotInfo.fromPilotShipEncounter(visitId, encounter = overlap3, pilotArea = pbp1)
            ),

            // (4) no pbp match: overlaps both pilot area activities
            PilotTest(
                encounters = listOf(bothOverlap),
                berthVisits = emptyList(),
                anchorVisit = emptyList(),
                pilotAreaActivities = pilotAreaActivities,
                expectedResult = PilotInfo.fromPilotShipEncounter(visitId, encounter = bothOverlap, pilotArea = null)
            ),

            // (5) no pbp match: overlaps no pilot area activity
            PilotTest(
                encounters = listOf(nonOverlapping),
                berthVisits = emptyList(),
                anchorVisit = emptyList(),
                pilotAreaActivities = pilotAreaActivities,
                expectedResult = PilotInfo.fromPilotShipEncounter(visitId, encounter = nonOverlapping, pilotArea = null)
            )
        ).map { Arguments.of(it) }
    }

    @ParameterizedTest
    @MethodSource("pilotInboundOutboundAreaMatchingSource")
    fun pilotInboundAreaMatching(test: PilotTest) {
        whenever(infraService.getById(eq("pbp-A"), any())).thenReturn(createPilotBoardingPlace(_id = "pbp-A"))

        // Create a berth visit far right of the pilot activities, so the pilot encounters are classified as inbound
        val startTime = test.encounters.maxOf { it.end?.time ?: it.start.time }
        val berthVisit = createBerthVisitInfo(
            id = "BERTH_START_ID_1",
            ref = 0,
            activity = createAreaActivity(
                id = "BERTH_START_ID_1",
                start = createLocationTime(time = startTime + 60L),
                end = createLocationTime(time = startTime + 120L),
                areaId = ""
            )
        )
        assertEquals(
            test.expectedResult,
            generator.findInboundPilot(
                visitId = visitId,
                encounters = test.encounters,
                berthVisits = listOf(berthVisit),
                anchorVisits = test.anchorVisit,
                pilotAreaActivities = test.pilotAreaActivities
            )
        )
    }

    @ParameterizedTest
    @MethodSource("pilotInboundOutboundAreaMatchingSource")
    fun pilotOutboundAreaMatching(test: PilotTest) {
        whenever(infraService.getById(eq("pbp-A"), any())).thenReturn(createPilotBoardingPlace(_id = "pbp-A"))
        // Create a berth visit far left of the pilot activities, so the pilot encounters are classified as outbound
        val startTime = test.encounters.minOf { it.start.time }
        val berthVisit = createBerthVisitInfo(
            id = "BERTH_START_ID_1",
            ref = 0,
            activity = createAreaActivity(
                id = "BERTH_START_ID_1",
                start = createLocationTime(time = startTime - 120L),
                end = createLocationTime(time = startTime - 60L),
                areaId = ""
            )
        )
        assertEquals(
            test.expectedResult,
            generator.findOutboundPilot(
                visitId = visitId,
                encounters = test.encounters,
                berthVisits = listOf(berthVisit),
                anchorVisits = test.anchorVisit,
                pilotAreaActivities = test.pilotAreaActivities
            )
        )
    }

    @ParameterizedTest
    @EnumSource(ShipCategoryV2::class)
    fun `generate - ship - get name, imo and ship cat v2`(categoryV2: ShipCategoryV2) {
        val imoInt = 1234567
        val imoStr = imoInt.toString()
        val name = "Boat"

        val shipCache = ShipCache(
            identifier = imoStr,
            platform = null,
            csi = ShipCache.CsiShipCache(
                createShipRegisterInfoCache(
                    imo = imoStr,
                    mmsi = "",
                    name = name,
                    categories = ShipCategories(v1 = null, v2 = categoryV2),
                    specification = ShipSpecification()
                )
            )
        )
        whenever(shipCacheService.getCacheByImo(eq(imoStr))).thenReturn(shipCache)
        val actual = generator.generate(visit = createNewVisit(imo = imoInt), esof = null, previousPortAreaId = null)

        assertEquals(imoStr, actual.ship.imo)
        assertEquals(name, actual.ship.name)
        assertEquals(categoryV2, actual.ship.categories?.v2)
    }

    @Test
    fun `generate - when ship is not in cache only serve imo`() {
        val imoInt = 1234567
        val imoStr = imoInt.toString()
        whenever(shipCacheService.getCacheByImo(eq(imoStr))).thenReturn(null)
        val actual = generator.generate(visit = createNewVisit(imo = imoInt), esof = null, previousPortAreaId = null)

        assertEquals(imoStr, actual.ship.imo)
        assertNull(actual.ship.name)
        assertNull(actual.ship.categories)
    }

    @Test
    fun `should generate SOF with correct area info`() {
        val input = createNewVisit()

        val result = generator.generate(visit = input, esof = null, previousPortAreaId = null)
        val expected = AreaMeta(
            areaId = "TEST_PORT_ID",
            unlocode = "NLRTM",
            name = "PORT OR ROTTERDAM",
            type = "eosp"
        )

        assertEquals(expected, result.area)
    }

    @Test
    fun `generate - populates the previous port area when the area id is given`() {
        val area = createPort()
        val areaId = area._id!!
        val visit = createNewVisit()
        val expectedAreaMeta = AreaMeta(areaId = area._id!!, unlocode = area.unlocode, name = area.name, type = "port")
        whenever(infraService.getById(eq(areaId), eq(InfraAreaType.PORT))).thenReturn(area)

        // no area id gives no previousPort
        assertNull(generator.generate(visit, esof = null, previousPortAreaId = null).previousPort)
        // a non-existing area gives no previousPort
        assertNull(generator.generate(visit, esof = null, previousPortAreaId = "non-existing-area").previousPort)

        // area resolves
        assertEquals(
            expectedAreaMeta,
            generator.generate(visit, esof = null, previousPortAreaId = areaId).previousPort
        )
    }

    @Test
    fun generateLockStops() {
        val areaId = "LOCK_1"
        val lock = createLock(_id = areaId, ports = listOf(BEANR))
        val stop = createNewStop(type = LOCK, areaId = areaId)
        val port = createPort(_id = BEANR)
        val portArea = PortAreaInfo(
            ref = 0,
            isPassThrough = false,
            activity = createAreaActivity(areaId = BEANR),
            area = port
        )
        whenever(infraService.getById(eq(areaId), eq(InfraAreaType.LOCK))).thenReturn(lock)
        val result = generator.generateLockStops(visitId, listOf(stop), listOf(portArea))

        val expected = LockStopInfo(
            id = "$visitId.${stop.startEventId}",
            stop = stop,
            area = lock,
            portArea = portArea
        )
        assertTrue(result.size == 1)
        assertEquals(expected, result.first())
    }

    @Test
    fun `generateLockStops - other types give an empty list`() {
        assertEquals(
            emptyList<LockStopInfo>(),
            generator.generateLockStops(visitId, listOf(createNewStop(BERTH)), emptyList())
        )
        assertEquals(
            emptyList<AnchorStopInfo>(),
            generator.generateLockStops(visitId, listOf(createNewStop(UNCLASSIFIED)), emptyList())
        )
    }

    @Test
    fun `generateLockStops - port area not found still results in an anchor stop`() {
        val areaId = "LOCK_1"
        val lock = createLock(_id = areaId, ports = emptyList())
        val stop = createNewStop(type = LOCK, areaId = areaId)
        val portAreas = emptyList<PortAreaInfo>()
        whenever(infraService.getById(eq(areaId), eq(InfraAreaType.LOCK))).thenReturn(lock)
        val result = generator.generateLockStops(visitId, listOf(stop), portAreas)

        val expected = LockStopInfo(
            id = "$visitId.${stop.startEventId}",
            stop = stop,
            area = lock,
            portArea = null
        )
        assertTrue(result.size == 1)
        assertEquals(expected, result.first())
    }

    @Test
    fun `generateShipToShipTransfers - resolve other ship by mmsi`() {
        val imo = "1234567"
        val mmsi = "123456789"
        val ship = createShipRegisterInfoCache(imo, mmsi, ShipCategories(), ShipSpecification())
        val shipDetails = ship.toShipDetails()
        val shipCache = ShipCache(identifier = imo, csi = ShipCache.CsiShipCache(ship))
        whenever(shipCacheService.getCachyByMmsi(eq(mmsi))).thenReturn(shipCache)
        val transfer = createShipToShipTransfer(
            startEnd = timeline.generatePair(),
            otherMmsi = mmsi.toInt(),
            otherImo = imo.toInt()
        )

        val actual = generator.generateShipToShipTransfers(visitId, listOf(transfer))
        val actualTransfer = actual.firstOrNull() ?: fail("Expected 1 ShipToShip transfer")

        assertEquals(actualTransfer.otherShip, shipDetails)
    }

    @Test
    fun `generateShipToShipTransfers - resolve other ship by imo`() {
        val imo = "1234567"
        val mmsi = "123456789"
        val ship = createShipRegisterInfoCache(imo, mmsi, ShipCategories(), ShipSpecification())
        val shipDetails = ship.toShipDetails()
        val shipCache = ShipCache(identifier = imo, csi = ShipCache.CsiShipCache(ship))
        whenever(shipCacheService.getCacheByImo(eq(imo))).thenReturn(shipCache)
        val transfer = createShipToShipTransfer(
            startEnd = timeline.generatePair(),
            otherMmsi = mmsi.toInt(),
            otherImo = imo.toInt()
        )

        val actual = generator.generateShipToShipTransfers(visitId, listOf(transfer))
        val actualTransfer = actual.firstOrNull() ?: fail("Expected 1 ShipToShip transfer")

        assertEquals(actualTransfer.otherShip, shipDetails)
    }

    @Test
    fun `generateShipToShipTransfers - other ship not found, only serve ship identifiers`() {
        val imo = "1234567"
        val mmsi = "123456789"
        val shipDetails = ShipDetails(
            mmsi = mmsi,
            imo = imo,
            name = null,
            type = null,
            categories = null,
            length = null,
            beam = null,
            maxDraught = null,
            dwt = null
        )
        val transfer = createShipToShipTransfer(
            startEnd = timeline.generatePair(),
            otherMmsi = mmsi.toInt(),
            otherImo = imo.toInt()
        )

        val actual = generator.generateShipToShipTransfers(visitId, listOf(transfer))
        val actualTransfer = actual.firstOrNull() ?: fail("Expected 1 ShipToShip transfer")

        assertEquals(shipDetails, actualTransfer.otherShip)
    }

    @Test
    fun `generateShipToShipTransfers - shipToShip area resolves`() {
        val areaId = "area51"
        val area = createShipToShipArea()
        whenever(infraService.getById(eq(areaId), eq(InfraAreaType.SHIP_TO_SHIP))).thenReturn(area)
        val transfer = createShipToShipTransfer(
            startEnd = timeline.generatePair(),
            areaId = areaId
        )

        val actual = generator.generateShipToShipTransfers(visitId, listOf(transfer))
        val actualTransfer = actual.firstOrNull() ?: fail("Expected 1 ShipToShip transfer")

        assertEquals(actualTransfer.area, area)
    }

    @Test
    fun `generateShipToShipTransfers - shipToShip area not found still results in data`() {
        val areaId = "area51"
        val shipDetails = ShipDetails(
            mmsi = MMSI_1.toString(),
            imo = IMO_1.toString(),
            name = null,
            type = null,
            categories = null,
            length = null,
            beam = null,
            maxDraught = null,
            dwt = null
        )
        whenever(infraService.getById(eq(areaId), eq(InfraAreaType.SHIP_TO_SHIP))).thenReturn(null)
        val transfer = createShipToShipTransfer(
            startEnd = timeline.generatePair(),
            areaId = areaId
        )
        val expected = ShipToShipTransferInfo(
            id = "$visitId.${transfer.startEventId}",
            transfer = transfer,
            area = null,
            otherShip = shipDetails
        )

        val actual = generator.generateShipToShipTransfers(visitId, listOf(transfer))
        val actualTransfer = actual.firstOrNull() ?: fail("Expected 1 ShipToShip transfer")

        assertEquals(expected, actualTransfer)
    }

    @Test
    fun `should not generate slow moving period when not determined yet`() {
        val result = generator.determineSlowMovingPeriods(
            initialSlowMovingPeriods = null,
            berthVisits = emptyList(),
            anchorStops = emptyList(),
            lockStops = emptyList(),
            pilotInbound = null,
            pilotOutbound = null,
            portAreaActivities = emptyList()
        )

        assertEquals(null, result)
    }

    @Test
    fun `should generate empty slow moving period when none are found`() {
        val result = generator.determineSlowMovingPeriods(
            initialSlowMovingPeriods = emptyList(),
            berthVisits = emptyList(),
            anchorStops = emptyList(),
            lockStops = emptyList(),
            pilotInbound = null,
            pilotOutbound = null,
            portAreaActivities = emptyList()
        )

        val expected = CategorizedPeriods<NewSlowMovingPeriod>()
        assertEquals(expected, result)
    }

    private fun slowMovingSource(): Stream<Arguments> {

        // [                            visit                           ]
        // [  arrival        |         inPort         |       departure ]
        // [  t1, t2     boundLeft    t3, t4     boundRight    t5, t6   ]
        //
        // boundLeft is pilotInbound (or fallback: first inner port start)
        // boundRight is pilotOutbound (or fallback: last inner port end)
        val timeline = Timeline()
        val (time1, time2) = timeline.generatePair()
        val timeBoundaryLeft = timeline.generate().time
        val (time3, time4) = timeline.generatePair()
        val timeBoundaryRight = timeline.generate().time
        val (time5, time6) = timeline.generatePair()

        val inArrival = createSlowMovingPeriod("in-arrival", time1, time2)
        val inPort = createSlowMovingPeriod("in-port", time3, time4)
        val inDeparture = createSlowMovingPeriod("in-port", time5, time6)

        return listOf(
            // 1: arrival, in-port and departure are in the correct bucket
            SlowMovingPeriodTest(
                smps = listOf(inArrival, inPort, inDeparture),
                boundaries = timeBoundaryLeft to timeBoundaryRight,
                arrival = listOf(inArrival),
                inPort = listOf(inPort),
                departure = listOf(inDeparture),
            ),
            // 2: in-port and departure periods are in the in-port bucket, as the right boundary is missing
            SlowMovingPeriodTest(
                smps = listOf(inArrival, inPort, inDeparture),
                boundaries = timeBoundaryLeft to null,
                arrival = listOf(inArrival),
                inPort = listOf(inPort, inDeparture),
                departure = emptyList(),
            ),
            // 3: all periods are in the arrival bucket, as both boundaries are missing
            SlowMovingPeriodTest(
                smps = listOf(inArrival, inPort, inDeparture),
                boundaries = null to null,
                arrival = listOf(inArrival, inPort, inDeparture),
                inPort = emptyList(),
                departure = emptyList(),
            ),
            // 4: arrival is empty as there is no slow moving period
            SlowMovingPeriodTest(
                smps = listOf(inPort, inDeparture),
                boundaries = timeBoundaryLeft to timeBoundaryRight,
                arrival = emptyList(),
                inPort = listOf(inPort),
                departure = listOf(inDeparture),
            ),
            // 5: in-port is empty as there is no slow moving period
            SlowMovingPeriodTest(
                smps = listOf(inArrival, inDeparture),
                boundaries = timeBoundaryLeft to timeBoundaryRight,
                arrival = listOf(inArrival),
                inPort = emptyList(),
                departure = listOf(inDeparture),
            ),
            // 6: departure is empty as there is no slow moving period
            SlowMovingPeriodTest(
                smps = listOf(inArrival, inPort),
                boundaries = timeBoundaryLeft to timeBoundaryRight,
                arrival = listOf(inArrival),
                inPort = listOf(inPort),
                departure = emptyList(),
            ),

            // 7: empty lists when there's no slow moving periods
            SlowMovingPeriodTest(
                smps = emptyList(),
                boundaries = null to null,
                arrival = emptyList(),
                inPort = emptyList(),
                departure = emptyList(),
            ),
            // 8: empty lists when there's no slow moving periods
            SlowMovingPeriodTest(
                smps = emptyList(),
                boundaries = timeBoundaryLeft to null,
                arrival = emptyList(),
                inPort = emptyList(),
                departure = emptyList(),
            ),
            // 9: empty lists when there's no slow moving periods
            SlowMovingPeriodTest(
                smps = emptyList(),
                boundaries = timeBoundaryLeft to timeBoundaryRight,
                arrival = emptyList(),
                inPort = emptyList(),
                departure = emptyList(),
            ),
        ).map { Arguments.of(it) }.stream()
    }

    @ParameterizedTest
    @MethodSource("slowMovingSource")
    fun slowMoving(case: SlowMovingPeriodTest) {
        val (inPortStart, inPortEnd) = case.boundaries
        val result = generator.categorizePeriods(case.smps, inPortStart, inPortEnd)
        assertEquals(case.result, result)
    }

    data class SlowMovingPeriodTest(
        val smps: List<NewSlowMovingPeriod>,
        val boundaries: Pair<Instant?, Instant?>,
        val result: CategorizedPeriods<NewSlowMovingPeriod>
    ) {
        constructor(
            smps: List<NewSlowMovingPeriod> = emptyList(),
            boundaries: Pair<Instant?, Instant?>,
            arrival: List<NewSlowMovingPeriod> = emptyList(),
            inPort: List<NewSlowMovingPeriod> = emptyList(),
            departure: List<NewSlowMovingPeriod> = emptyList(),
        ) : this(smps, boundaries, CategorizedPeriods(arrival, inPort, departure))
    }

    data class OverlappingSlowMovingPeriodCase(
        val period: NewSlowMovingPeriod,
        val activities: List<Pair<LocationTime, LocationTime>>,
        val result: List<NewSlowMovingPeriod>
    ) {
        constructor(
            period: NewSlowMovingPeriod,
            activities: List<Pair<LocationTime, LocationTime>>,
            isReturned: Boolean
        ) : this(
            period,
            activities,
            result = if (isReturned) listOf(period) else emptyList()
        )
    }

    private fun `slow moving period overlap source`(): Stream<Arguments> {
        // t1  t2  t3  t4  t5  t6  t7  t8  t9  t10 t11 t12
        // :   :   [   a1  ]   :   :   [   a2  ]   :   :     activities (e.g. berth visit or anchor stop)
        // [ 1 ]   :   :   :   :   :   :   :   :   :   :     case 1: not overlapping, left of all activities
        // :   [   2   ]   :   :   :   :   :   :   :   :     case 2: overlapping partially on the left
        // :   :   [   3   ]   :   :   :   :   :   :   :     case 3: equals activity
        // :   :   :   [ 4 ]   :   :   :   :   :   :   :     case 4: enclosed by activity
        // :   :   :   [   5   ]   :   :   :   :   :   :     case 5: overlapping partially on the right
        // :   :   :   :   :   [ 6 ]   :   :   :   :   :     case 6: not overlapping, between activities
        // :   :   :   [   :   : 7 :   :   ]   :   :   :     case 7: overlaps multiple activities partially
        // :   :   [   :   :   : 8 :   :   :   ]   :   :     case 8: overlaps multiple activities entirely
        // :   [   :   :   :   : 9 :   :   :   :   ]   :     case 9: encloses multiple activities
        // :   [   :   10  :   ]   :   :   :   :   :   :     case 10: encloses one activity
        // :   :   :   :   :   :   :   :   :   :   [ 11]     case 11: not overlapping, right of all activities
        // ----
        // :   :   :   :   :   :   :   :   :   :   [ 11]     case 12: no activities
        val (time1, time2, time3, time4, time5, time6, time7, time8, time9, time10, time11, time12) =
            Timeline().generateN(12)

        val activity1 = Pair(time3, time5)
        val activity2 = Pair(time8, time10)
        val activities = listOf(activity1, activity2)

        return listOf(
            // case 1: not overlapping, left of all activities
            OverlappingSlowMovingPeriodCase(createSlowMovingPeriod(1, time1, time2), activities, isReturned = true),

            // case 2: overlapping partially on the left
            OverlappingSlowMovingPeriodCase(createSlowMovingPeriod(2, time2, time4), activities, isReturned = false),

            // case 3: equals activity
            OverlappingSlowMovingPeriodCase(createSlowMovingPeriod(3, time3, time5), activities, isReturned = false),

            // case 4: enclosed by activity
            OverlappingSlowMovingPeriodCase(createSlowMovingPeriod(4, time3, time4), activities, isReturned = false),

            // case 5: overlapping partially on the right
            OverlappingSlowMovingPeriodCase(createSlowMovingPeriod(5, time4, time6), activities, isReturned = false),

            // case 6: not overlapping, between activities
            OverlappingSlowMovingPeriodCase(createSlowMovingPeriod(6, time6, time7), activities, isReturned = true),

            // case 7: overlaps multiple activities partially
            OverlappingSlowMovingPeriodCase(createSlowMovingPeriod(7, time4, time9), activities, isReturned = false),

            // case 8: overlaps multiple activities entirely
            OverlappingSlowMovingPeriodCase(createSlowMovingPeriod(8, time3, time10), activities, isReturned = false),

            // case 9: encloses multiple activities
            OverlappingSlowMovingPeriodCase(createSlowMovingPeriod(9, time2, time11), activities, isReturned = false),

            // case 10: encloses one activity
            OverlappingSlowMovingPeriodCase(createSlowMovingPeriod(10, time2, time6), activities, isReturned = false),

            // case 11: not overlapping, right of all activities
            OverlappingSlowMovingPeriodCase(createSlowMovingPeriod(11, time11, time12), activities, isReturned = true),

            // case 12: no activities
            OverlappingSlowMovingPeriodCase(createSlowMovingPeriod(12, time1, time2), emptyList(), isReturned = true),
        ).map { Arguments.of(it) }.stream()
    }

    @ParameterizedTest
    @MethodSource("slow moving period overlap source")
    fun `slow moving period is not returned when it overlaps a berth visit`(case: OverlappingSlowMovingPeriodCase) {
        val berthVisits = case.activities.mapIndexed { index, startEnd ->
            createBerthVisitInfo(
                id = "BERTH_START_ID_1",
                ref = index,
                activity = createAreaActivity(startEnd)
            )
        }

        assertEquals(
            case.result,
            generator.removeOverlappingActivities(
                smp = listOf(case.period),
                berthVisits = berthVisits,
                anchorStops = emptyList(),
                lockStops = emptyList()
            )
        )
    }

    private fun `slow moving period overlap for anchoring source`(): Stream<Arguments> {
        // t1  t2  t3  t4  t5  t6  t7  t8  t9  t10 t11 t12
        // :   :   [   a1  ]   :   :   [   a2  ]   :   :     activities (e.g. berth visit or anchor stop)
        // [ 1 ]   :   :   :   :   :   :   :   :   :   :     case 1: not overlapping, left of all activities
        // :   [   2   ]   :   :   :   :   :   :   :   :     case 2: overlapping partially on the left
        // :   :   [   3   ]   :   :   :   :   :   :   :     case 3: equals activity
        // :   :   :   [ 4 ]   :   :   :   :   :   :   :     case 4: enclosed by activity
        // :   :   :   [   5   ]   :   :   :   :   :   :     case 5: overlapping partially on the right
        // :   :   :   :   :   [ 6 ]   :   :   :   :   :     case 6: not overlapping, between activities
        // :   :   :   [   :   : 7 :   :   ]   :   :   :     case 7: overlaps multiple activities partially
        // :   :   [   :   :   : 8 :   :   :   ]   :   :     case 8: overlaps multiple activities entirely
        // :   [   :   :   :   : 9 :   :   :   :   ]   :     case 9: encloses multiple activities
        // :   [   :   10  :   ]   :   :   :   :   :   :     case 10: encloses one activity
        // :   :   :   :   :   :   :   :   :   :   [ 11]     case 11: not overlapping, right of all activities
        // ----
        // :   :   :   :   :   :   :   :   :   :   [ 11]     case 12: no activities
        val (time1, time2, time3, time4, time5, time6, time7, time8, time9, time10, time11, time12) =
            Timeline().generateN(12)

        val activity1 = Pair(time3, time5)
        val activity2 = Pair(time8, time10)
        val activities = listOf(activity1, activity2)

        return listOf(
            // case 1: not overlapping, left of all activities
            OverlappingSlowMovingPeriodCase(createSlowMovingPeriod(1, time1, time2), activities, isReturned = true),

            // case 2: overlapping partially on the left
            OverlappingSlowMovingPeriodCase(
                period = createSlowMovingPeriod(2, time2, time4),
                activities = activities,
                result = listOf(
                    createSlowMovingPeriod("2b", time2, time3) // only return the part left of the other activity
                )
            ),

            // case 3: equals activity
            OverlappingSlowMovingPeriodCase(createSlowMovingPeriod(3, time3, time5), activities, isReturned = false),

            // case 4: enclosed by activity
            OverlappingSlowMovingPeriodCase(createSlowMovingPeriod(4, time3, time4), activities, isReturned = false),

            // case 5: overlapping partially on the right
            OverlappingSlowMovingPeriodCase(
                period = createSlowMovingPeriod(5, time4, time6),
                activities = activities,
                result = listOf(
                    createSlowMovingPeriod("5a", time5, time6) // only return the part right of the other activity
                )
            ),

            // case 6: not overlapping, between activities
            OverlappingSlowMovingPeriodCase(createSlowMovingPeriod(6, time6, time7), activities, isReturned = true),

            // case 7: overlaps multiple activities partially
            OverlappingSlowMovingPeriodCase(
                period = createSlowMovingPeriod(7, time4, time9),
                activities = activities,
                result = listOf(
                    createSlowMovingPeriod("7ab", time5, time8) // only return the part in between the activities
                )
            ),

            // case 8: overlaps multiple activities entirely
            OverlappingSlowMovingPeriodCase(
                period = createSlowMovingPeriod(8, time3, time10),
                activities = activities,
                result = listOf(
                    createSlowMovingPeriod("8ab", time5, time8) // only return the part in between the activities
                )
            ),

            // case 9: encloses multiple activities
            OverlappingSlowMovingPeriodCase(
                period = createSlowMovingPeriod(9, time2, time11),
                activities = activities,
                result = listOf(
                    createSlowMovingPeriod("9b", time2, time3), // left of the activities
                    createSlowMovingPeriod("9ab", time5, time8), // in between activities
                    createSlowMovingPeriod("9aa", time10, time11), // right of the activities
                )
            ),

            // case 10: encloses one activity
            OverlappingSlowMovingPeriodCase(
                createSlowMovingPeriod(10, time2, time6),
                activities = activities,
                result = listOf(
                    createSlowMovingPeriod("10b", time2, time3), // left of the activity
                    createSlowMovingPeriod("10a", time5, time6), // right of the activity
                )
            ),

            // case 11: not overlapping, right of all activities
            OverlappingSlowMovingPeriodCase(createSlowMovingPeriod(11, time11, time12), activities, isReturned = true),

            // case 12: no activities
            OverlappingSlowMovingPeriodCase(createSlowMovingPeriod(12, time1, time2), emptyList(), isReturned = true),
        ).map { Arguments.of(it) }.stream()
    }

    @ParameterizedTest
    @MethodSource("slow moving period overlap for anchoring source")
    fun `slow moving period is not returned when it overlaps an anchor stop`(case: OverlappingSlowMovingPeriodCase) {
        val anchorStops = case.activities.mapIndexed { index, (start, end) ->
            AnchorStopInfo(
                id = "$visitId.$index",
                stop = createNewStop(
                    startEventId = "$index",
                    type = ANCHOR_AREA,
                    start = start,
                    end = end
                ),
                area = null,
                portArea = null
            )
        }

        assertEquals(
            case.result,
            generator.removeOverlappingActivities(
                smp = listOf(case.period),
                berthVisits = emptyList(),
                anchorStops = anchorStops,
                lockStops = emptyList()
            )
        )
    }

    @ParameterizedTest
    @MethodSource("slow moving period overlap source")
    fun `slow moving period is not returned when it overlaps a lock stop`(case: OverlappingSlowMovingPeriodCase) {
        val lockStops = case.activities.mapIndexed { index, (start, end) ->
            LockStopInfo(
                id = "$visitId.$index",
                stop = createNewStop(
                    startEventId = "$index",
                    type = LOCK,
                    start = start,
                    end = end
                ),
                area = null,
                portArea = null
            )
        }

        assertEquals(
            case.result,
            generator.removeOverlappingActivities(
                smp = listOf(case.period),
                berthVisits = emptyList(),
                anchorStops = emptyList(),
                lockStops = lockStops
            )
        )
    }

    @ParameterizedTest
    @MethodSource("berthVisits")
    fun berthVisitActivity(case: BerthVisitSample) {
        whenever(infraService.getBerth(any())).thenAnswer { invocation ->
            val id = invocation.arguments[0] as String
            createBerth(_id = id, name = id)
        }

        val visit = createNewVisit(
            stops = case.stops,
            berthAreaActivities = case.berthActivities.toMutableList()
        )

        val actual = generator.generate(
            visit = visit,
            esof = null,
            previousPortAreaId = null
        )

        assertEquals(case.expected, actual.berthVisits)
    }

    /**
     * Helper class containing test data for the berth visit activity test.
     */
    data class BerthVisitSample(
        val stops: List<NewStop>,
        val berthActivities: List<AreaActivity>,
        val expected: List<BerthVisit>
    )

    private fun berthVisits(): Stream<Arguments> {
        val berth1 = "BERTH1"
        val berth2 = "BERTH2"

        return Stream.of(
            Arguments.of(
                // Stop is bigger than the activity with start and end,
                // stop should be cut short on both sides to match the activity
                BerthVisitSample(
                    stops = listOf(
                        createBerthStop(
                            id = "STOP_START_EVENT_ID_1",
                            areaId = berth1,
                            start = Instant.parse("2023-01-01T09:00:00Z"),
                            end = Instant.parse("2023-01-01T12:00:00Z")
                        )
                    ),
                    berthActivities = listOf(
                        createBerthAreaActivity(
                            id = "BERTH_START_EVENT_ID_1",
                            start = Instant.parse("2023-01-01T09:30:00Z"),
                            end = Instant.parse("2023-01-01T11:30:00Z"),
                            areaId = berth1
                        )
                    ),
                    expected = listOf(
                        createBerthVisit(
                            id = "TEST_VISIT_ID.STOP_START_EVENT_ID_1",
                            start = Instant.parse("2023-01-01T09:30:00Z"),
                            end = Instant.parse("2023-01-01T11:30:00Z"),
                            areaRef = berth1
                        )
                    )
                )
            ),
            Arguments.of(
                // Activity is bigger than the stop with start and end
                // Stop should remain the same length.
                BerthVisitSample(
                    stops = listOf(
                        createBerthStop(
                            id = "STOP_START_EVENT_ID_1",
                            areaId = berth1,
                            start = Instant.parse("2023-01-01T10:00:00Z"),
                            end = Instant.parse("2023-01-01T12:00:00Z")
                        ),
                    ),
                    berthActivities = listOf(
                        createBerthAreaActivity(
                            id = "BERTH_START_EVENT_ID_1",
                            start = Instant.parse("2023-01-01T09:00:00Z"),
                            end = Instant.parse("2023-01-01T13:00:00Z"),
                            areaId = berth1
                        ),
                    ),
                    expected = listOf(
                        createBerthVisit(
                            id = "TEST_VISIT_ID.STOP_START_EVENT_ID_1",
                            start = Instant.parse("2023-01-01T10:00:00Z"),
                            end = Instant.parse("2023-01-01T12:00:00Z"),
                            areaRef = berth1
                        )
                    )
                )
            ),
            // Two activities for one berth stop which are both bigger than the stop
            // Stop should remain the same since activities are bigger
            Arguments.of(
                BerthVisitSample(
                    stops = listOf(
                        createBerthStop(
                            id = "STOP_START_EVENT_ID_1",
                            areaId = berth1,
                            start = Instant.parse("2023-01-01T10:00:00Z"),
                            end = Instant.parse("2023-01-01T12:00:00Z")
                        ),
                    ),
                    berthActivities = listOf(
                        createBerthAreaActivity(
                            id = "BERTH_START_EVENT_ID_1",
                            start = Instant.parse("2023-01-01T09:00:00Z"),
                            end = Instant.parse("2023-01-01T11:00:00Z"),
                            areaId = berth1
                        ),
                        createBerthAreaActivity(
                            id = "BERTH_START_EVENT_ID_2",
                            start = Instant.parse("2023-01-01T11:10:00Z"),
                            end = Instant.parse("2023-01-01T13:00:00Z"),
                            areaId = berth1
                        )
                    ),
                    expected = listOf(
                        createBerthVisit(
                            id = "TEST_VISIT_ID.STOP_START_EVENT_ID_1",
                            start = Instant.parse("2023-01-01T10:00:00Z"),
                            end = Instant.parse("2023-01-01T12:00:00Z"),
                            areaRef = berth1
                        )
                    )
                )
            ),
            // Three activities for one berth stop where the end of the last activity is earlier than the stop end
            // End of the stop should be cut short to match the last activity
            Arguments.of(
                BerthVisitSample(
                    stops = listOf(
                        createBerthStop(
                            id = "STOP_START_EVENT_ID_1",
                            areaId = berth1,
                            start = Instant.parse("2023-01-01T10:00:00Z"),
                            end = Instant.parse("2023-01-01T12:00:00Z")
                        ),
                    ),
                    berthActivities = listOf(
                        createBerthAreaActivity(
                            id = "BERTH_START_EVENT_ID_1",
                            start = Instant.parse("2023-01-01T10:00:00Z"),
                            end = Instant.parse("2023-01-01T11:00:00Z"),
                            areaId = berth1
                        ),
                        createBerthAreaActivity(
                            id = "BERTH_START_EVENT_ID_2",
                            start = Instant.parse("2023-01-01T11:00:00Z"),
                            end = Instant.parse("2023-01-01T11:30:00Z"),
                            areaId = berth1
                        ),
                        createBerthAreaActivity(
                            id = "BERTH_START_EVENT_ID_3",
                            start = Instant.parse("2023-01-01T11:45:00Z"),
                            end = Instant.parse("2023-01-01T11:55:00Z"),
                            areaId = berth1
                        )
                    ),
                    expected = listOf(
                        createBerthVisit(
                            id = "TEST_VISIT_ID.STOP_START_EVENT_ID_1",
                            start = Instant.parse("2023-01-01T10:00:00Z"),
                            end = Instant.parse("2023-01-01T11:55:00Z"),
                            areaRef = berth1
                        )
                    )
                )
            ),
            // Two different stops with two different activities
            // Correct activities should cut the correct stops.
            Arguments.of(
                BerthVisitSample(
                    stops = listOf(
                        createBerthStop(
                            id = "STOP_START_EVENT_ID_1",
                            areaId = berth1,
                            start = Instant.parse("2023-01-01T10:00:00Z"),
                            end = Instant.parse("2023-01-01T12:00:00Z")
                        ),
                        createBerthStop(
                            id = "STOP_START_EVENT_ID_2",
                            areaId = berth1,
                            start = Instant.parse("2023-01-01T17:00:00Z"),
                            end = Instant.parse("2023-01-01T19:00:00Z")
                        )
                    ),
                    berthActivities = listOf(
                        createBerthAreaActivity(
                            id = "BERTH_START_EVENT_ID_1",
                            start = Instant.parse("2023-01-01T10:30:00Z"),
                            end = Instant.parse("2023-01-01T12:00:00Z"),
                            areaId = berth1
                        ),
                        createBerthAreaActivity(
                            id = "BERTH_START_EVENT_ID_2",
                            start = Instant.parse("2023-01-01T17:00:00Z"),
                            end = Instant.parse("2023-01-01T18:30:00Z"),
                            areaId = berth1
                        )
                    ),
                    expected = listOf(
                        createBerthVisit(
                            id = "TEST_VISIT_ID.STOP_START_EVENT_ID_1",
                            start = Instant.parse("2023-01-01T10:30:00Z"),
                            end = Instant.parse("2023-01-01T12:00:00Z"),
                            areaRef = berth1
                        ),
                        createBerthVisit(
                            id = "TEST_VISIT_ID.STOP_START_EVENT_ID_2",
                            ref = 1,
                            start = Instant.parse("2023-01-01T17:00:00Z"),
                            end = Instant.parse("2023-01-01T18:30:00Z"),
                            areaRef = berth1
                        )
                    )
                )
            ),
            // No activities stop should stay the same
            Arguments.of(
                BerthVisitSample(
                    stops = listOf(
                        createBerthStop(
                            id = "STOP_START_EVENT_ID_1",
                            areaId = berth1,
                            start = Instant.parse("2023-01-01T10:00:00Z"),
                            end = Instant.parse("2023-01-01T12:00:00Z")
                        )
                    ),
                    berthActivities = listOf(),
                    expected = listOf(
                        createBerthVisit(
                            id = "TEST_VISIT_ID.STOP_START_EVENT_ID_1",
                            start = Instant.parse("2023-01-01T10:00:00Z"),
                            end = Instant.parse("2023-01-01T12:00:00Z"),
                            areaRef = berth1
                        )
                    )
                )
            ),
            // Two stops at two separate berths, first one with 1 activity and second one with 2 activities
            Arguments.of(
                BerthVisitSample(
                    stops = listOf(
                        createBerthStop(
                            id = "STOP_START_EVENT_ID_1",
                            areaId = berth1,
                            start = Instant.parse("2023-01-01T10:00:00Z"),
                            end = Instant.parse("2023-01-01T12:00:00Z")
                        ),
                        createBerthStop(
                            id = "STOP_START_EVENT_ID_2",
                            areaId = berth2,
                            start = Instant.parse("2023-01-01T18:00:00Z"),
                            end = Instant.parse("2023-01-01T19:00:00Z")
                        )
                    ),
                    berthActivities = listOf(
                        createBerthAreaActivity(
                            id = "BERTH_START_EVENT_ID_1",
                            start = Instant.parse("2023-01-01T09:00:00Z"),
                            end = Instant.parse("2023-01-01T11:00:00Z"),
                            areaId = berth1
                        ),
                        createBerthAreaActivity(
                            id = "BERTH_START_EVENT_ID_2",
                            start = Instant.parse("2023-01-01T18:00:00Z"),
                            end = Instant.parse("2023-01-01T18:30:00Z"),
                            areaId = berth2
                        ),
                        createBerthAreaActivity(
                            id = "BERTH_START_EVENT_ID_3",
                            start = Instant.parse("2023-01-01T18:30:00Z"),
                            end = Instant.parse("2023-01-01T18:45:00Z"),
                            areaId = berth2
                        )
                    ),
                    expected = listOf(
                        createBerthVisit(
                            id = "TEST_VISIT_ID.STOP_START_EVENT_ID_1",
                            start = Instant.parse("2023-01-01T10:00:00Z"),
                            end = Instant.parse("2023-01-01T11:00:00Z"),
                            areaRef = berth1
                        ),
                        createBerthVisit(
                            id = "TEST_VISIT_ID.STOP_START_EVENT_ID_2",
                            ref = 1,
                            start = Instant.parse("2023-01-01T18:00:00Z"),
                            end = Instant.parse("2023-01-01T18:45:00Z"),
                            areaRef = berth2
                        )
                    )
                )
            ),
            // Two stops should correctly get cut by one activity
            Arguments.of(
                BerthVisitSample(
                    stops = listOf(
                        createBerthStop(
                            id = "STOP_START_EVENT_ID_1",
                            areaId = berth1,
                            start = Instant.parse("2023-01-01T10:00:00Z"),
                            end = Instant.parse("2023-01-01T11:00:00Z")
                        ),
                        createBerthStop(
                            id = "STOP_START_EVENT_ID_2",
                            areaId = berth1,
                            start = Instant.parse("2023-01-01T12:00:00Z"),
                            end = Instant.parse("2023-01-01T13:00:00Z")
                        )
                    ),
                    berthActivities = listOf(
                        createBerthAreaActivity(
                            id = "BERTH_START_EVENT_ID_1",
                            start = Instant.parse("2023-01-01T10:30:00Z"),
                            end = Instant.parse("2023-01-01T12:30:00Z"),
                            areaId = berth1
                        )
                    ),
                    expected = listOf(
                        createBerthVisit(
                            id = "TEST_VISIT_ID.STOP_START_EVENT_ID_1",
                            start = Instant.parse("2023-01-01T10:30:00Z"),
                            end = Instant.parse("2023-01-01T11:00:00Z"),
                            areaRef = berth1
                        ),
                        createBerthVisit(
                            id = "TEST_VISIT_ID.STOP_START_EVENT_ID_2",
                            ref = 1,
                            start = Instant.parse("2023-01-01T12:00:00Z"),
                            end = Instant.parse("2023-01-01T12:30:00Z"),
                            areaRef = berth1
                        )
                    )
                )
            ),
        )
    }

    @Test
    fun `departure tug should be populated by waiting for departure encounters`() {
        val berthVisit = createBerthStop(
            id = "STOP_START_EVENT_ID_1",
            start = Instant.parse("2023-01-01T10:00:00Z"),
            end = Instant.parse("2023-01-01T12:00:00Z"),
            areaId = "BERTH1"
        )

        val tugEncounter = createTugEncounters(
            start = Instant.parse("2023-01-01T12:05:00Z"),
            end = Instant.parse("2023-01-01T12:15:00Z")
        )

        val tugWaitingForDepartureEncounter = createTugWaitingDeparture(
            start = Instant.parse("2023-01-01T12:05:00Z"),
            end = Instant.parse("2023-01-01T12:15:00Z")
        )

        val visit = createNewVisit(stops = listOf(berthVisit))
        val esof = createNewESoF(
            _id = "idea",
            encounters = listOf(tugEncounter, tugWaitingForDepartureEncounter)
        )

        val result = generator.generate(visit, esof, null)
        val departureTug = result.berthVisits.first().departureTugs.first()

        assertEquals(departureTug.start.time, tugWaitingForDepartureEncounter.start.time)
    }

    @Test
    fun `departure tug should NOT be populated by waiting for departure encounters when time difference exceeds limit`() {
        val berthVisit = createBerthStop(
            id = "STOP_START_EVENT_ID_1",
            start = Instant.parse("2023-01-01T10:00:00Z"),
            end = Instant.parse("2023-01-01T12:00:00Z"),
            areaId = "BERTH1"
        )

        val tugEncounter = createTugEncounters(
            start = Instant.parse("2023-01-01T13:35:00Z"),
            end = Instant.parse("2023-01-01T13:45:00Z")
        )

        val tugWaitingForDepartureEncounter = createTugWaitingDeparture(
            start = Instant.parse("2023-01-01T12:05:00Z"),
            end = Instant.parse("2023-01-01T12:15:00Z")
        )

        val visit = createNewVisit(stops = listOf(berthVisit))
        val esof = createNewESoF(
            _id = "idea",
            encounters = listOf(tugEncounter, tugWaitingForDepartureEncounter)
        )

        val result = generator.generate(visit, esof, null)
        val departureTug = result.berthVisits.first().departureTugs.first()

        assertEquals(departureTug.start.time, tugEncounter.start.time)
    }

    private fun createBerthAreaActivity(
        id: String,
        start: Instant,
        end: Instant,
        areaId: String
    ) = createAreaActivity(
        id = id,
        start = createLocationTime(time = start),
        end = createLocationTime(time = end),
        areaId = areaId
    )

    private fun createBerthStop(
        id: String,
        start: Instant,
        end: Instant,
        areaId: String
    ) = createNewStop(
        startEventId = id,
        type = BERTH,
        start = createLocationTime(time = start),
        end = createLocationTime(time = end),
        areaId = areaId
    )

    private fun createBerthVisit(id: String, ref: Int = 0, start: Instant, end: Instant, areaRef: String) = BerthVisit(
        id = id,
        terminalVisitId = null,
        portVisitId = null,
        ref = ref,
        terminalVisitRef = null,
        portAreaRef = null,
        start = createLocationTimeApi(time = start),
        end = createLocationTimeApi(time = end),
        firstLineSecured = null,
        allFast = null,
        lastLineReleased = null,
        area = AreaMeta(areaId = areaRef, unlocode = null, name = areaRef, type = "berth"),
        cargoCategoryType = null,
        mooringType = null,
        terminalId = null,
        arrivalTugs = emptyList(),
        departureTugs = emptyList()
    )

    private fun createTugEncounters(start: Instant, end: Instant) =
        createNewEncounter(start, end, TUG)

    private fun createTugWaitingDeparture(start: Instant, end: Instant) =
        createNewEncounter(start, end, EncounterType.TUG_WAITING_DEPARTURE)

    private fun createNewEncounter(start: Instant, end: Instant, type: EncounterType) = NewEncounter(
        type = type,
        otherMmsi = 3,
        otherImo = 4,
        startEventId = "startEventId",
        start = createLocationTime(time = start),
        end = createLocationTime(time = end)
    )

    private val defaultSpeed = Speed(
        min = 0.1F,
        max = 1F,
        avg = 0.5F,
        count = 5,
        lastSpeedOverGround = 0.5F,
        lastSpeedOverGroundTimestamp = Instant.now()
    )
    private fun createSlowMovingPeriod(
        id: Int,
        start: LocationTime,
        end: LocationTime,
        speed: Speed = defaultSpeed
    ) = createSlowMovingPeriod(id.toString(), start, end, speed)
    private fun createSlowMovingPeriod(
        id: String,
        start: LocationTime,
        end: LocationTime,
        speed: Speed = defaultSpeed
    ) = NewSlowMovingPeriod(
        id = id,
        start = start,
        end = end,
        speed = speed
    )

    data class AnchorStopPeriodTest(
        val anchorStops: List<AnchorStopInfo>,
        val boundaries: Pair<Instant?, Instant?>,
        val result: CategorizedPeriods<AnchorStopInfo>
    ) {
        constructor(
            anchorStops: List<AnchorStopInfo> = emptyList(),
            boundaries: Pair<Instant?, Instant?>,
            arrival: List<AnchorStopInfo> = emptyList(),
            inPort: List<AnchorStopInfo> = emptyList(),
            departure: List<AnchorStopInfo> = emptyList(),
        ) : this(anchorStops, boundaries, CategorizedPeriods(arrival, inPort, departure))
    }

    private fun createAnchorStopInfo(
        id: String,
        start: Instant,
        end: Instant? = null,
        areaId: String = "test-anchor-area"
    ): AnchorStopInfo {
        val stop = createNewStop(
            type = ANCHOR_AREA,
            start = createLocationTime(time = start),
            end = end?.let { createLocationTime(time = it) },
            areaId = areaId
        )
        val anchorage = createAnchorage(_id = areaId)
        return AnchorStopInfo(id, stop, anchorage, portArea = null)
    }

    private fun anchorStopCategorizeSource(): Stream<Arguments> {
        // t1  t2  t3  t4  t5  t6  t7  t8  t9  t10 t11 t12
        val timeline = Timeline()
        val (time1, time2, time3, time4, time5, time6, time7, time8, time9, time10, time11, time12) =
            timeline.generateN(12)

        val timeBoundaryLeft = time5.time
        val timeBoundaryRight = time10.time

        val inArrival = createAnchorStopInfo("in-arrival", time2.time, time4.time)
        val inPort = createAnchorStopInfo("in-port", time6.time, time8.time)
        val inDeparture = createAnchorStopInfo("in-departure", time11.time, time12.time)
        val ongoingAnchor = createAnchorStopInfo("ongoing", time6.time)

        return listOf(
            // 1: anchor stops are correctly categorized based on boundaries
            AnchorStopPeriodTest(
                anchorStops = listOf(inArrival, inPort, inDeparture),
                boundaries = timeBoundaryLeft to timeBoundaryRight,
                arrival = listOf(inArrival),
                inPort = listOf(inPort),
                departure = listOf(inDeparture),
            ),

            // 2: when no right boundary, in-port and departure are all in the in-port bucket
            AnchorStopPeriodTest(
                anchorStops = listOf(inArrival, inPort, inDeparture),
                boundaries = timeBoundaryLeft to null,
                arrival = listOf(inArrival),
                inPort = listOf(inPort, inDeparture),
                departure = emptyList(),
            ),

            // 3: when no boundaries at all, everything is in the arrival bucket
            AnchorStopPeriodTest(
                anchorStops = listOf(inArrival, inPort, inDeparture),
                boundaries = null to null,
                arrival = listOf(inArrival, inPort, inDeparture),
                inPort = emptyList(),
                departure = emptyList(),
            ),

            // 4: each category can be empty if there are no anchor stops in that category
            AnchorStopPeriodTest(
                anchorStops = listOf(inArrival, inDeparture),
                boundaries = timeBoundaryLeft to timeBoundaryRight,
                arrival = listOf(inArrival),
                inPort = emptyList(),
                departure = listOf(inDeparture),
            ),

            // 5: ongoing anchor stop is categorized based on its start time
            AnchorStopPeriodTest(
                anchorStops = listOf(inArrival, ongoingAnchor, inDeparture),
                boundaries = timeBoundaryLeft to timeBoundaryRight,
                arrival = listOf(inArrival),
                inPort = listOf(ongoingAnchor),
                departure = listOf(inDeparture),
            ),

            // 6: empty result when there are no anchor stops
            AnchorStopPeriodTest(
                anchorStops = emptyList(),
                boundaries = timeBoundaryLeft to timeBoundaryRight,
                arrival = emptyList(),
                inPort = emptyList(),
                departure = emptyList(),
            ),
        ).map { Arguments.of(it) }.stream()
    }

    @ParameterizedTest
    @MethodSource("anchorStopCategorizeSource")
    fun categorizeAnchorStops(case: AnchorStopPeriodTest) {
        val (inPortStart, inPortEnd) = case.boundaries
        val result = generator.categorizePeriods(case.anchorStops, inPortStart, inPortEnd)
        assertEquals(case.result, result)
    }

    @Test
    fun `should return empty categorized anchor stops when no anchor stops exist`() {
        val result = generator.categorizeAnchorStops(
            anchorStops = emptyList(),
            pilotInbound = null,
            pilotOutbound = null,
            portAreaActivities = emptyList()
        )

        val expected = CategorizedPeriods<AnchorStopInfo>()
        assertEquals(expected, result)
    }

    @Test
    fun `should use pilot timestamps as boundaries when available for anchor stops`() {
        val timeline = Timeline()
        val pilotInboundStart = timeline.generate()
        val pilotOutboundStart = timeline.generate()

        val beforeInbound = createAnchorStopInfo("before", pilotInboundStart.time.minusSeconds(30))
        val betweenPilots = createAnchorStopInfo("between", pilotInboundStart.time.plusSeconds(30))
        val afterOutbound = createAnchorStopInfo("after", pilotOutboundStart.time.plusSeconds(30))

        val pilotInbound = PilotInfo.fromPilotShipEncounter(
            visitId = visitId,
            encounter = createNewEncounter(PILOT, startTime = pilotInboundStart.time),
            pilotArea = null
        )
        val pilotOutbound = PilotInfo.fromPilotShipEncounter(
            visitId = visitId,
            encounter = createNewEncounter(PILOT, startTime = pilotOutboundStart.time),
            pilotArea = null
        )

        val result = generator.categorizeAnchorStops(
            anchorStops = listOf(beforeInbound, betweenPilots, afterOutbound),
            pilotInbound = pilotInbound,
            pilotOutbound = pilotOutbound,
            portAreaActivities = emptyList()
        )

        val expected = CategorizedPeriods<AnchorStopInfo>(
            arrival = listOf(beforeInbound),
            inPort = listOf(betweenPilots),
            departure = listOf(afterOutbound)
        )
        assertEquals(expected, result)
    }

    @Test
    fun `should fallback to port activities when pilots not available for anchor stops`() {
        val timeline = Timeline()
        val (portStart, anchorInPort, portEnd) = timeline.generateN(3)

        val beforePort = createAnchorStopInfo("before", portStart.time.minusSeconds(3600))
        val duringPort = createAnchorStopInfo("during", anchorInPort.time)
        val afterPort = createAnchorStopInfo("after", portEnd.time.plusSeconds(3600))

        val portActivity = createAreaActivity(
            start = portStart,
            end = portEnd,
            areaId = "areaId"
        )

        val result = generator.categorizeAnchorStops(
            anchorStops = listOf(beforePort, duringPort, afterPort),
            pilotInbound = null,
            pilotOutbound = null,
            portAreaActivities = listOf(portActivity)
        )

        val expected = CategorizedPeriods<AnchorStopInfo>(
            arrival = listOf(beforePort),
            inPort = listOf(duringPort),
            departure = listOf(afterPort)
        )
        assertEquals(expected, result)
    }

    private fun createNewEncounter(type: EncounterType, startTime: Instant, endTime: Instant? = null): NewEncounter {
        return NewEncounter(
            type = type,
            otherMmsi = 0,
            otherImo = 0,
            startEventId = "",
            start = createLocationTime(time = startTime),
            end = endTime?.let { createLocationTime(time = it) }
        )
    }

    private fun terminalVisitsSource(): Stream<Arguments> {
        val terminalId1 = "B7D95BC04CCB633DDDDF9C386161D7FD536EEA04"
        val terminalId2 = "0974B3322039FED0818C58B71ED80F253A1ABB49"
        val terminalId3 = "53A6BCB5559C69966D9E849D8C20AE56E6D6260D"

        // Create BerthVisitInfo objects for the test
        val berthVisit1 = createBerthVisitInfo(
            id = "fb3c5b70-8da0-4505-9e7c-5cf908a19d86",
            ref = 0,
            activity = createAreaActivity(
                id = "fb3c5b70-8da0-4505-9e7c-5cf908a19d86",
                start = createLocationTime(
                    time = Instant.parse("2024-03-30T04:21:04Z")
                ),
                end = createLocationTime(
                    time = Instant.parse("2024-04-05T17:23:05Z")
                ),
                areaId = "99817DA8937E784E8BE96F0CA867CEF16E939F14"
            ),
            area = createBerth(
                _id = "99817DA8937E784E8BE96F0CA867CEF16E939F14",
                name = "Berth 1",
                terminalId = terminalId1
            )
        )

        val berthVisit2 = createBerthVisitInfo(
            id = "bd1ea6f5-962f-4112-bd91-32cd57ed84ba",
            ref = 1,
            activity = createAreaActivity(
                id = "bd1ea6f5-962f-4112-bd91-32cd57ed84ba",
                start = createLocationTime(
                    time = Instant.parse("2024-04-05T19:15:44Z")
                ),
                end = createLocationTime(
                    time = Instant.parse("2024-04-05T21:15:25Z")
                ),
                areaId = "848AEA4833EF12874818638293502555889CD6D9"
            ),
            area = createBerth(
                _id = "848AEA4833EF12874818638293502555889CD6D9",
                name = "Berth 2",
                terminalId = terminalId2
            )
        )

        val berthVisit3 = createBerthVisitInfo(
            id = "04aa5de9-616d-4bac-8d69-d71c67f0fb2e",
            ref = 2,
            activity = createAreaActivity(
                id = "04aa5de9-616d-4bac-8d69-d71c67f0fb2e",
                start = createLocationTime(
                    time = Instant.parse("2024-04-10T17:57:48Z")
                ),
                end = createLocationTime(
                    time = Instant.parse("2024-04-11T23:06:21Z")
                ),
                areaId = "E9F5E49774185EC38873C155A1332938EF4BDFF2"
            ),
            area = createBerth(
                _id = "E9F5E49774185EC38873C155A1332938EF4BDFF2",
                name = "Berth 3",
                terminalId = terminalId3
            )
        )

        val berthVisit4 = createBerthVisitInfo(
            id = "0da5a5ab-8c56-4a9e-99ef-e5f589131c1a",
            ref = 3,
            activity = createAreaActivity(
                id = "0da5a5ab-8c56-4a9e-99ef-e5f589131c1a",
                start = createLocationTime(
                    time = Instant.parse("2024-04-12T00:28:54Z")
                ),
                end = createLocationTime(
                    time = Instant.parse("2024-04-13T15:51:48Z")
                ),
                areaId = "848AEA4833EF12874818638293502555889CD6D9"
            ),
            area = createBerth(
                _id = "848AEA4833EF12874818638293502555889CD6D9",
                name = "Berth 2",
                terminalId = terminalId2
            )
        )

        // Create terminal mooring activities
        val terminalMooring1 = createAreaActivity(
            id = "a64a1269-0c5d-423b-988e-77d69138e4d8",
            start = createLocationTime(
                time = Instant.parse("2024-03-30T03:58:51Z")
            ),
            end = createLocationTime(
                time = Instant.parse("2024-04-05T17:27:45Z")
            ),
            areaId = "$terminalId1.mooringarea"
        )

        val terminalMooring2 = createAreaActivity(
            id = "50255cb1-b373-41a4-a6bd-187915ca4e03",
            start = createLocationTime(
                time = Instant.parse("2024-04-05T19:09:12Z")
            ),
            end = createLocationTime(
                time = Instant.parse("2024-04-05T21:16:12Z")
            ),
            areaId = "$terminalId2.mooringarea"
        )

        val terminalMooring3 = createAreaActivity(
            id = "71074362-47b9-4297-82d1-e0318e96bdae",
            start = createLocationTime(
                time = Instant.parse("2024-04-10T17:54:45Z")
            ),
            end = createLocationTime(
                time = Instant.parse("2024-04-11T23:07:01Z")
            ),
            areaId = "$terminalId3.mooringarea"
        )

        val terminalMooring4 = createAreaActivity(
            id = "a2335dc1-053f-4276-93f4-9726a504bdf7",
            start = createLocationTime(
                time = Instant.parse("2024-04-12T00:22:34Z")
            ),
            end = createLocationTime(
                time = Instant.parse("2024-04-13T02:11:32Z")
            ),
            areaId = "$terminalId2.mooringarea"
        )

        // Create expected terminal visits
        val terminal1 = createTerminal(_id = terminalId1)
        val terminal2 = createTerminal(_id = terminalId2)
        val terminal3 = createTerminal(_id = terminalId3)

        val expectedTerminalVisit1 = createTerminalVisitInfo(
            ref = 0,
            activity = createAreaActivity(
                id = "a64a1269-0c5d-423b-988e-77d69138e4d8",
                start = berthVisit1.activity.start,
                end = berthVisit1.activity.end,
                areaId = terminalId1
            ),
            mooringActivity = terminalMooring1,
            area = terminal1,
            berthVisits = listOf(berthVisit1)
        )

        val expectedTerminalVisit2 = createTerminalVisitInfo(
            ref = 1,
            activity = createAreaActivity(
                id = "50255cb1-b373-41a4-a6bd-187915ca4e03",
                start = berthVisit2.activity.start,
                end = berthVisit2.activity.end,
                areaId = terminalId2
            ),
            mooringActivity = terminalMooring2,
            area = terminal2,
            berthVisits = listOf(berthVisit2)
        )

        val expectedTerminalVisit3 = createTerminalVisitInfo(
            ref = 2,
            activity = createAreaActivity(
                id = "71074362-47b9-4297-82d1-e0318e96bdae",
                start = berthVisit3.activity.start,
                end = berthVisit3.activity.end,
                areaId = terminalId3
            ),
            mooringActivity = terminalMooring3,
            area = terminal3,
            berthVisits = listOf(berthVisit3)
        )

        val expectedTerminalVisit4 = createTerminalVisitInfo(
            ref = 3,
            activity = createAreaActivity(
                id = "a2335dc1-053f-4276-93f4-9726a504bdf7",
                start = berthVisit4.activity.start,
                end = berthVisit4.activity.end,
                areaId = terminalId2
            ),
            mooringActivity = terminalMooring4,
            area = terminal2,
            berthVisits = listOf(berthVisit4)
        )

        // Create test case
        val realDataTestCase = TerminalVisitTestCase(
            berthVisitInfo = listOf(berthVisit1, berthVisit2, berthVisit3, berthVisit4),
            terminalMoorings = listOf(terminalMooring1, terminalMooring2, terminalMooring3, terminalMooring4),
            expectedTerminalVisits = listOf(expectedTerminalVisit1, expectedTerminalVisit2, expectedTerminalVisit3, expectedTerminalVisit4)
        )

        return Stream.of(
            Arguments.of(realDataTestCase)
        )
    }

    @ParameterizedTest
    @MethodSource("terminalVisitsSource")
    fun generateTerminalVisits(testCase: TerminalVisitTestCase) {
        // Set up any mocks needed for the terminal
        testCase.berthVisitInfo.forEach { berth ->
            val terminalId = berth.area?.terminalId ?: ""
            val terminal = createTerminal(_id = terminalId)
            whenever(infraService.getById(eq(terminalId), eq(InfraAreaType.TERMINAL))).thenReturn(terminal)
        }

        val result = generator.generateTerminalVisits(
            berthVisitGroups = generator.groupBerthVisitsByTerminal(testCase.berthVisitInfo),
            terminalMoorings = testCase.terminalMoorings
        )

        // Verify results
        assertEquals(testCase.expectedTerminalVisits.size, result.size, "Terminal visits count should match")
        assertEquals(testCase.expectedTerminalVisits, result)
    }

    data class TerminalVisitTestCase(
        val berthVisitInfo: List<BerthVisitInfo>,
        val terminalMoorings: List<AreaActivity>,
        val expectedTerminalVisits: List<TerminalVisitInfo>
    )
}
