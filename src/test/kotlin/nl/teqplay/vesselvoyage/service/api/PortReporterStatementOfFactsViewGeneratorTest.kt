package nl.teqplay.vesselvoyage.service.api

import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.eq
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.whenever
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType
import nl.teqplay.csi.model.ship.info.component.ShipCategories
import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2
import nl.teqplay.csi.model.ship.info.component.ShipSpecification
import nl.teqplay.skeleton.model.Location
import nl.teqplay.vesselvoyage.apiv2.model.sof.portreporter.AreaMeta
import nl.teqplay.vesselvoyage.apiv2.model.sof.portreporter.BerthVisit
import nl.teqplay.vesselvoyage.apiv2.model.sof.portreporter.Ship
import nl.teqplay.vesselvoyage.logic.ANCHOR_AREA_4EAST
import nl.teqplay.vesselvoyage.logic.NLRTM
import nl.teqplay.vesselvoyage.logic.createAnchorage
import nl.teqplay.vesselvoyage.logic.createBerth
import nl.teqplay.vesselvoyage.logic.createPilotBoardingPlace
import nl.teqplay.vesselvoyage.logic.createPort
import nl.teqplay.vesselvoyage.logic.createTerminal
import nl.teqplay.vesselvoyage.mapper.EntryV2MapperImpl
import nl.teqplay.vesselvoyage.mapper.PortReporterStatementOfFactsMapperImpl
import nl.teqplay.vesselvoyage.model.esof.portreporterview.AnchorStopInfo
import nl.teqplay.vesselvoyage.model.esof.portreporterview.BerthVisitInfo
import nl.teqplay.vesselvoyage.model.esof.portreporterview.EncounterInfo
import nl.teqplay.vesselvoyage.model.esof.portreporterview.PilotInfo
import nl.teqplay.vesselvoyage.model.esof.portreporterview.PortAreaInfo
import nl.teqplay.vesselvoyage.model.esof.portreporterview.TerminalVisitInfo
import nl.teqplay.vesselvoyage.model.esof.portreporterview.TugInfo
import nl.teqplay.vesselvoyage.model.esof.portreporterview.UnclassifiedStopInfo
import nl.teqplay.vesselvoyage.model.internal.InfraAreaType
import nl.teqplay.vesselvoyage.model.internal.ShipCache
import nl.teqplay.vesselvoyage.model.lightweight.poma.Berth
import nl.teqplay.vesselvoyage.model.v2.AreaActivity
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewEncounter
import nl.teqplay.vesselvoyage.model.v2.NewStop
import nl.teqplay.vesselvoyage.model.v2.NewStopType.ANCHOR_AREA
import nl.teqplay.vesselvoyage.model.v2.NewStopType.BERTH
import nl.teqplay.vesselvoyage.model.v2.NewStopType.UNCLASSIFIED
import nl.teqplay.vesselvoyage.model.v2.SimpleStartEnd
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.ShipCacheService
import nl.teqplay.vesselvoyage.service.api.PortReporterStatementOfFactsViewGenerator.BerthVisitGroup
import nl.teqplay.vesselvoyage.util.DEFAULT_TEST_PORT_ID
import nl.teqplay.vesselvoyage.util.PortReporterSofHelperFunctions.createBerthVisitInfo
import nl.teqplay.vesselvoyage.util.PortReporterSofHelperFunctions.createTerminalVisitInfo
import nl.teqplay.vesselvoyage.util.Timeline
import nl.teqplay.vesselvoyage.util.createAreaActivity
import nl.teqplay.vesselvoyage.util.createLocationTime
import nl.teqplay.vesselvoyage.util.createLocationTimeApi
import nl.teqplay.vesselvoyage.util.createNewESoF
import nl.teqplay.vesselvoyage.util.createNewStop
import nl.teqplay.vesselvoyage.util.createNewVisit
import nl.teqplay.vesselvoyage.util.createShipRegisterInfoCache
import nl.teqplay.vesselvoyage.util.minus
import nl.teqplay.vesselvoyage.util.minusMinutes
import nl.teqplay.vesselvoyage.util.plus
import nl.teqplay.vesselvoyage.util.plusMinutes
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.fail
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource
import java.time.Duration
import java.time.Instant
import java.util.UUID
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class PortReporterStatementOfFactsViewGeneratorTest {

    private lateinit var infraService: InfraService
    private lateinit var shipCacheService: ShipCacheService
    private lateinit var generator: PortReporterStatementOfFactsViewGenerator

    @BeforeEach
    fun setUp() {
        infraService = mock<InfraService>().apply {
            whenever(this.getById(eq(DEFAULT_TEST_PORT_ID), eq(InfraAreaType.PORT)))
                .thenReturn(createPort())
        }
        shipCacheService = mock()
        generator = PortReporterStatementOfFactsViewGenerator(
            infraService = infraService,
            mapper = PortReporterStatementOfFactsMapperImpl(),
            entryMapper = EntryV2MapperImpl(),
            shipCacheService = shipCacheService
        )
    }

    private fun portAreasIsPassingThroughSource(): Stream<Arguments> {
        val loc = Location(0.0, 0.0)
        val now = Instant.now()

        val areaStart = LocationTime(loc, now)
        val areaEnd = LocationTime(loc, now.plusSeconds(60))
        val areaActivityFinished = AreaActivity(id = "finished", areaStart, areaEnd, areaId = "finished")
        val areaActivityOngoing = AreaActivity(id = "ongoing", areaStart, end = null, areaId = "ongoing")

        // 'inside' here means it is in the time range of start..end
        val stopInsideAreaFinished = createNewStop(
            start = areaStart.copy(time = areaStart.time.plusSeconds(5)),
            end = areaEnd.copy(time = areaEnd.time.minusSeconds(5)),
            type = BERTH // type doesn't really matter, but is not nullable
        )
        val stopInsideAreaOngoing = createNewStop(
            start = areaStart.copy(time = areaStart.time.plusSeconds(5)),
            end = null,
            type = BERTH // type doesn't really matter, but is not nullable
        )
        val outsideOffsetSeconds = 180L // well exceeds area start..end
        val stopOutsideAreaFinished = createNewStop(
            start = areaStart.copy(time = areaStart.time.plusSeconds(outsideOffsetSeconds)),
            end = areaEnd.copy(time = areaEnd.time.plusSeconds(outsideOffsetSeconds)),
            type = BERTH // type doesn't really matter, but is not nullable
        )
        val stopOutsideAreaOngoing = createNewStop(
            start = areaStart.copy(time = areaStart.time.plusSeconds(outsideOffsetSeconds)),
            end = null,
            type = BERTH // type doesn't really matter, but is not nullable
        )

        return listOf(
            PortAreaSetup(areaActivityFinished, emptyList(), isPassThrough = true),
            PortAreaSetup(areaActivityFinished, stopInsideAreaFinished, isPassThrough = false),
            // note that areaActivityFinished + stopInsideAreaOngoing should not possible, therefore not testing it
            PortAreaSetup(areaActivityFinished, stopOutsideAreaFinished, isPassThrough = true),
            PortAreaSetup(areaActivityFinished, stopOutsideAreaOngoing, isPassThrough = true),

            PortAreaSetup(areaActivityOngoing, emptyList(), isPassThrough = true),
            PortAreaSetup(areaActivityOngoing, stopInsideAreaFinished, isPassThrough = false),
            PortAreaSetup(areaActivityOngoing, stopInsideAreaOngoing, isPassThrough = false),
            PortAreaSetup(areaActivityOngoing, stopOutsideAreaFinished, isPassThrough = false),
            PortAreaSetup(areaActivityOngoing, stopOutsideAreaOngoing, isPassThrough = false),
        ).map { Arguments.of(it) }.stream()
    }

    @ParameterizedTest
    @MethodSource("portAreasIsPassingThroughSource")
    fun portAreasIsPassingThrough(setup: PortAreaSetup) {
        val actual = generator.isPassingThroughPortActivity(setup.areaActivity, setup.stops)
        assertTrue(setup.isPassThrough == actual)
    }

    data class PortAreaSetup(
        val areaActivity: AreaActivity,
        val stops: List<NewStop>,
        val isPassThrough: Boolean
    ) {
        constructor(areaActivity: AreaActivity, stop: NewStop, isPassThrough: Boolean) :
            this(areaActivity, listOf(stop), isPassThrough)
    }

    @Test
    fun `match berth visit to port area`() {

        val port = createPort()
        val portId = port._id!!
        val loc = Location(0.0, 0.0)
        val now = Instant.now()
        val portAreaStart = LocationTime(loc, now)
        val berthStart = LocationTime(loc, now.plusSeconds(30))
        val berthEnd = LocationTime(loc, now.plusSeconds(90))
        val portAreaEnd = LocationTime(loc, now.plusSeconds(120))

        val portActivity = AreaActivity(id = "", start = portAreaStart, end = portAreaEnd, areaId = portId)
        val portAreaInfo = PortAreaInfo(ref = 0, isPassThrough = false, portActivity, port)

        // matches on berth.mainPort
        assertEquals(
            portAreaInfo,
            generator.matchBerthVisitToPortArea(
                berthActivity = AreaActivity(id = "", start = berthStart, end = berthEnd, areaId = "bert"),
                listOf(portAreaInfo),
                berth = createBerth(mainPort = portId)
            ),
            "Should match on berth.mainPort -> port._id"
        )

        // matches on port._id in berth.ports
        assertEquals(
            portAreaInfo,
            generator.matchBerthVisitToPortArea(
                berthActivity = AreaActivity(id = "", start = berthStart, end = berthEnd, areaId = "bert"),
                listOf(portAreaInfo),
                berth = createBerth(ports = listOf(portId))
            ),
            "Should match on berth.mainPort -> port._id"
        )
    }

    private fun Instant.plusMinutes(minutes: Long): Instant {
        return this.plus(Duration.ofMinutes(minutes))
    }

    private fun arrivalTugsSource(): Stream<Arguments> {

        fun tug(start: LocationTime, end: LocationTime? = null) = SimpleStartEnd(start, end)

        // timeline
        // berth stops --------[previous]------[current]--------
        //             |---|---|---|---|---|---|---|---|---|---
        // times      now 30  60  90  120 150 180 210 240 270

        val now = Instant.EPOCH
        val beforePreviousStop = createLocationTime(time = now.plusMinutes(30))
        val time1 = createLocationTime(time = now.plusMinutes(60))
        val insidePreviousStop = createLocationTime(time = now.plusMinutes(90))
        val time2 = createLocationTime(time = now.plusMinutes(120))
        val betweenStops = createLocationTime(time = now.plusMinutes(150))
        val time3 = createLocationTime(time = now.plusMinutes(180))
        val inCurrentStop = createLocationTime(time = now.plusMinutes(210))
        val time4 = createLocationTime(time = now.plusMinutes(240))
        val afterCurrentStop = createLocationTime(time = now.plusMinutes(270))

        val previous = SimpleStartEnd(start = time1, end = time2)
        val currentOngoing = SimpleStartEnd(start = time3, end = null)
        val currentFinished = SimpleStartEnd(start = time3, end = time4)

        // 10 Minutes before start, in tolerance window
        val toleranceTimeBefore = currentFinished.start.minusMinutes(10)
        val toleranceTimeAfter = currentFinished.start.plusMinutes(10)

        // variables:
        // - stops
        //   - no previous stop + current stop ongoing
        //   - no previous stop + current stop finished
        //   - previous stop finished + current stop ongoing
        //   - previous stop finished + current stop finished
        // - tug ongoing | finished
        // - tug starts before | in | after previous stop
        // - tug ends before | in | after current stop

        return listOf(
            // 1
            // true, tug and main ship still in arrival process (both ongoing)
            TugSource(null, currentOngoing, tug(betweenStops, null), qualifies = true),
            // false, tug encounter started and stopped before arrival process
            // maybe tug helped tugging main ship into the port, but it did not help at the berth
            TugSource(null, currentOngoing, tug(betweenStops, betweenStops), qualifies = false),
            // true, tug started before and left after main ship berth start
            TugSource(null, currentOngoing, tug(betweenStops, inCurrentStop), qualifies = true),
            // false because tug starts after main ship arrival
            TugSource(null, currentOngoing, tug(inCurrentStop, null), qualifies = false),

            // 5
            // false, tug was not involved in arrival, it started and ended in main ship berth visit
            TugSource(null, currentOngoing, tug(inCurrentStop, inCurrentStop), qualifies = false),
            // false, this is a departure tug
            TugSource(null, currentOngoing, tug(afterCurrentStop, null), qualifies = false),
            // false, although tug started before main ship berth visit, tug is still ongoing,
            TugSource(null, currentFinished, tug(betweenStops, null), qualifies = false),
            // false, tug started and ended before main ship berth visit began
            TugSource(null, currentFinished, tug(betweenStops, betweenStops), qualifies = false),
            // true, started before and ended in main ship berth visit
            TugSource(null, currentFinished, tug(betweenStops, inCurrentStop), qualifies = true),

            // 10
            // false, tug did not end in berth visit
            TugSource(null, currentFinished, tug(betweenStops, afterCurrentStop), qualifies = false),
            // false, this is a departure tug
            TugSource(null, currentFinished, tug(inCurrentStop, null), qualifies = false),
            // false, tug started and ended in main ship berth visit
            TugSource(null, currentFinished, tug(inCurrentStop, inCurrentStop), qualifies = false),
            // false, this is a departure tug
            TugSource(null, currentFinished, tug(inCurrentStop, afterCurrentStop), qualifies = false),
            // false, tug encounter is fully outside main ship berth visit
            TugSource(null, currentFinished, tug(afterCurrentStop, null), qualifies = false),

            // 15
            // false, tug encounter is fully outside main ship berth visit
            TugSource(null, currentFinished, tug(afterCurrentStop, afterCurrentStop), qualifies = false),
            // false, although main ship is still arriving in berth visit, tug was already there before previous start?
            TugSource(previous, currentOngoing, tug(beforePreviousStop, null), qualifies = false),
            // false, tug encounter is outside main ship berth visit
            TugSource(previous, currentOngoing, tug(beforePreviousStop, beforePreviousStop), qualifies = false),
            TugSource(previous, currentOngoing, tug(beforePreviousStop, insidePreviousStop), qualifies = false),
            TugSource(previous, currentOngoing, tug(beforePreviousStop, betweenStops), qualifies = false),

            // 20
            // false, appears valid for arrival, but was already encountering with main ship before previous stop?
            TugSource(previous, currentOngoing, tug(beforePreviousStop, inCurrentStop), qualifies = false),
            // true, tug is tugging main ship from previous berth to this berth, arrival still ongoing
            TugSource(previous, currentOngoing, tug(insidePreviousStop, null), qualifies = true),
            // false, tug encounter was before current berth
            TugSource(previous, currentOngoing, tug(insidePreviousStop, insidePreviousStop), qualifies = false),
            TugSource(previous, currentOngoing, tug(insidePreviousStop, betweenStops), qualifies = false),
            // true, tug tugged main ship from previous berth to this berth and tug left
            TugSource(previous, currentOngoing, tug(insidePreviousStop, inCurrentStop), qualifies = true),

            // 25
            // true, arrival still in progress
            TugSource(previous, currentOngoing, tug(betweenStops, null), qualifies = true),
            // false, tug encounter is only before berth visit
            TugSource(previous, currentOngoing, tug(betweenStops, betweenStops), qualifies = false),
            // true, tug started before and left after main ship berth start
            TugSource(previous, currentOngoing, tug(betweenStops, inCurrentStop), qualifies = true),
            // false, main ship already arrived after tug arrived
            TugSource(previous, currentOngoing, tug(inCurrentStop, null), qualifies = false),
            // false, encounter is only inside berth visit
            TugSource(previous, currentOngoing, tug(inCurrentStop, inCurrentStop), qualifies = false),

            // 30
            // false, tug start was after berth visit
            TugSource(previous, currentOngoing, tug(afterCurrentStop, null), qualifies = false),
            TugSource(previous, currentOngoing, tug(afterCurrentStop, afterCurrentStop), qualifies = false),
            // false, tug is encountering main ship already before previous stop?
            TugSource(previous, currentFinished, tug(beforePreviousStop, null), qualifies = false),
            // false, tug encounter was before current berth
            TugSource(previous, currentFinished, tug(beforePreviousStop, beforePreviousStop), qualifies = false),
            // false, tug is encountering main ship already before previous stop?
            TugSource(previous, currentFinished, tug(beforePreviousStop, insidePreviousStop), qualifies = false),

            // 35
            // false, tug is encountering main ship already before previous stop?
            TugSource(previous, currentFinished, tug(beforePreviousStop, betweenStops), qualifies = false),
            // false, although ending in current stop, tug is already longer with the main ship
            TugSource(previous, currentFinished, tug(beforePreviousStop, inCurrentStop), qualifies = false),
            TugSource(previous, currentFinished, tug(beforePreviousStop, afterCurrentStop), qualifies = false),
            // false, tugging happened from previous berth to current berth, but berth visit is finished and tug not?!
            TugSource(previous, currentFinished, tug(insidePreviousStop, null), qualifies = false),
            // false, encounter not in current berth visit
            TugSource(previous, currentFinished, tug(insidePreviousStop, insidePreviousStop), qualifies = false),

            // 40
            // false, encounter not in current berth visit
            TugSource(previous, currentFinished, tug(insidePreviousStop, betweenStops), qualifies = false),
            // true, tug tugged main ship from previous berth to this berth and tug left
            TugSource(previous, currentFinished, tug(insidePreviousStop, inCurrentStop), qualifies = true),
            // false, tug kept encountering main ship after arrival, that is weird
            TugSource(previous, currentFinished, tug(insidePreviousStop, afterCurrentStop), qualifies = false),
            TugSource(previous, currentFinished, tug(betweenStops, null), qualifies = false),
            // false, encounter not in current visit
            TugSource(previous, currentFinished, tug(betweenStops, betweenStops), qualifies = false),

            // 45
            // true, tug helped with arrival, then left
            TugSource(previous, currentFinished, tug(betweenStops, inCurrentStop), qualifies = true),
            // false, tug kept encountering main ship after arrival, that is weird
            TugSource(previous, currentFinished, tug(betweenStops, afterCurrentStop), qualifies = false),
            // false, tug start should be before berth visit start
            TugSource(previous, currentFinished, tug(inCurrentStop, null), qualifies = false),
            TugSource(previous, currentFinished, tug(inCurrentStop, inCurrentStop), qualifies = false),
            // false, this is a departure tug
            TugSource(previous, currentFinished, tug(inCurrentStop, afterCurrentStop), qualifies = false),

            // 50
            // false, encounter does not overlap current berth visit
            TugSource(previous, currentFinished, tug(afterCurrentStop, null), qualifies = false),
            TugSource(previous, currentFinished, tug(afterCurrentStop, afterCurrentStop), qualifies = false),

            // 10 Minutes before should still qualify because of tolerance, 10 minutes after start should not
            TugSource(null, currentFinished, tug(toleranceTimeBefore, inCurrentStop), qualifies = true),
            TugSource(previous, currentFinished, tug(toleranceTimeBefore, inCurrentStop), qualifies = true),
            TugSource(null, currentFinished, tug(toleranceTimeAfter, toleranceTimeAfter), qualifies = false),
            // 55
            TugSource(previous, currentFinished, tug(toleranceTimeAfter, toleranceTimeAfter), qualifies = false),

        ).map { Arguments.of(it) }.stream()
    }

    @ParameterizedTest
    @MethodSource("arrivalTugsSource")
    fun arrivalTugs(tugSource: TugSource) {
        val (previousStop, currentStop, tug, qualifiesAsArrivalTug) = tugSource
        currentStop ?: fail("definition failure, currentStop must always be set")
        val encounter = NewEncounter(EncounterType.TUG, otherMmsi = 0, otherImo = 0, startEventId = "", tug.start, tug.end)
        val tugInfo = TugInfo(ref = 0, encounter)
        val result = generator.findArrivalTugs(previousStop, currentStop, listOf(tugInfo))

        println(tugSource)

        if (qualifiesAsArrivalTug) {
            assertTrue(result.isNotEmpty())
            assertEquals(tugInfo, result.first())
        } else {
            // tug should not be returned, as it is not an arrival tug
            assertTrue(result.isEmpty())
        }
    }

    private fun departureTugsSource(): Stream<Arguments> {
        fun tug(start: LocationTime, end: LocationTime? = null) = SimpleStartEnd(start, end)

        // timeline
        // berth stops ----------[current]-------[ next  ]--------
        //             |----|----|---|---|---|---|---|---|----|---
        // times       now  30  60  90  120 150 180 210 240  270

        // variables:
        // - stops
        //   - current stop ongoing + no next stop
        //   - current stop finished + no next stop
        //   - current stop finished + next stop ongoing
        //   - current stop finished + next stop finished
        // - tug ongoing | finished
        // - tug starts before | in | after current stop
        // - tug ends before | in | after next stop

        val now = Instant.EPOCH
        val beforeCurrentStop = createLocationTime(time = now.plusMinutes(30))
        val time1 = createLocationTime(time = now.plusMinutes(60))
        val inCurrentStop = createLocationTime(time = now.plusMinutes(90))
        val time2 = createLocationTime(time = now.plusMinutes(120))
        val justAfterCurrentStop = createLocationTime(time = now.plusMinutes(130))
        val betweenStops = createLocationTime(time = now.plusMinutes(150))
        val justAfterBetweenStops = createLocationTime(time = now.plusMinutes(160))
        val time3 = createLocationTime(time = now.plusMinutes(180))
        val inNextStop = createLocationTime(time = now.plusMinutes(210))
        val time4 = createLocationTime(time = now.plusMinutes(240))
        val afterNextStop = createLocationTime(time = now.plusMinutes(270))

        val currentOngoing = SimpleStartEnd(start = time1, end = null)
        val currentFinished = SimpleStartEnd(start = time1, end = time2)
        val nextOngoing = SimpleStartEnd(start = time3, end = null)
        val nextFinished = SimpleStartEnd(start = time3, end = time4)

        val toleranceTimeBefore = time2.minusMinutes(10)
        val toleranceTimeAfter = time2.plusMinutes(10)

        return listOf(
            // 1
            // false, tug start expected in berth visit. This is actually an ongoing arrival
            TugSource(currentOngoing, null, tug(beforeCurrentStop, null), qualifies = false),
            // false, tug encounter outside current visit
            TugSource(currentOngoing, null, tug(beforeCurrentStop, beforeCurrentStop), qualifies = false),
            // false, tug start expected in berth visit. This depicts an arrival tug
            TugSource(currentOngoing, null, tug(beforeCurrentStop, inCurrentStop), qualifies = false),
            // false, tug start expected in berth visit
            TugSource(currentOngoing, null, tug(beforeCurrentStop, betweenStops), qualifies = false),

            // 5
            TugSource(currentOngoing, null, tug(beforeCurrentStop, inNextStop), qualifies = false),
            TugSource(currentOngoing, null, tug(beforeCurrentStop, afterNextStop), qualifies = false),
            // true, tug arrived in ongoing berth visit, indicating departure of main ship
            TugSource(currentOngoing, null, tug(inCurrentStop, null), qualifies = true),
            // false, tug started and ended in current berth visit
            TugSource(currentOngoing, null, tug(inCurrentStop, inCurrentStop), qualifies = false),
            // false, tug start expected in berth visit
            TugSource(currentFinished, null, tug(beforeCurrentStop, null), qualifies = false),

            // 10
            TugSource(currentFinished, null, tug(beforeCurrentStop, beforeCurrentStop), qualifies = false),
            // false, tug start expected in berth visit. This is actually an ongoing arrival
            TugSource(currentFinished, null, tug(beforeCurrentStop, inCurrentStop), qualifies = false),
            // true, this is an ongoing departure
            TugSource(currentFinished, null, tug(inCurrentStop, null), qualifies = true),
            // false, tug encounter start and end in current berth
            TugSource(currentFinished, null, tug(inCurrentStop, inCurrentStop), qualifies = false),
            // true, this is a finished departure
            TugSource(currentFinished, null, tug(inCurrentStop, betweenStops), qualifies = true),

            // 15
            // true, tug start after only berth visit
            TugSource(currentFinished, null, tug(betweenStops, null), qualifies = true),
            TugSource(currentFinished, null, tug(justAfterCurrentStop, null), qualifies = true),
            // true, tug start at first-half of in-between time of stops
            TugSource(currentFinished, nextOngoing, tug(justAfterCurrentStop, null), qualifies = true),

            // false, tug start at second-half of in-between time of stops
            TugSource(currentFinished, nextOngoing, tug(justAfterBetweenStops, null), qualifies = false),
            TugSource(currentFinished, nextOngoing, tug(justAfterBetweenStops, justAfterBetweenStops), qualifies = false),
            TugSource(currentFinished, nextOngoing, tug(betweenStops, null), qualifies = false),
            TugSource(currentFinished, nextOngoing, tug(betweenStops, betweenStops), qualifies = false),

            // false, tug start expected in berth visit
            TugSource(currentFinished, nextOngoing, tug(beforeCurrentStop, null), qualifies = false),
            TugSource(currentFinished, nextOngoing, tug(beforeCurrentStop, beforeCurrentStop), qualifies = false),
            TugSource(currentFinished, nextOngoing, tug(beforeCurrentStop, inCurrentStop), qualifies = false),

            // 25
            TugSource(currentFinished, nextOngoing, tug(beforeCurrentStop, betweenStops), qualifies = false),
            TugSource(currentFinished, nextOngoing, tug(beforeCurrentStop, inNextStop), qualifies = false),
            // true, tug did departure and is most likely tugging for arrival at next stop
            TugSource(currentFinished, nextOngoing, tug(inCurrentStop, null), qualifies = true),
            // false, tug encounter start and end in current berth
            TugSource(currentFinished, nextOngoing, tug(inCurrentStop, inCurrentStop), qualifies = false),
            TugSource(currentFinished, nextOngoing, tug(inCurrentStop, betweenStops), qualifies = true),

            // 30
            // true, tug did departure and is most likely tugging for arrival at next stop
            TugSource(currentFinished, nextOngoing, tug(inCurrentStop, inNextStop), qualifies = true),
            // false, tug encounter outside current berth (also for next items)
            TugSource(currentFinished, nextOngoing, tug(betweenStops, null), qualifies = false),
            TugSource(currentFinished, nextOngoing, tug(betweenStops, betweenStops), qualifies = false),
            TugSource(currentFinished, nextOngoing, tug(betweenStops, inNextStop), qualifies = false),
            TugSource(currentFinished, nextOngoing, tug(inNextStop, null), qualifies = false),

            // 35
            // false, not related to current berth
            TugSource(currentFinished, nextOngoing, tug(inNextStop, inNextStop), qualifies = false),
            // false, both current and next berth finished, but tug still ongoing, indicates other activities, not a tug
            TugSource(currentFinished, nextFinished, tug(beforeCurrentStop, null), qualifies = false),

            TugSource(currentFinished, nextFinished, tug(beforeCurrentStop, beforeCurrentStop), qualifies = false),
            // false, this is an arrival tug
            TugSource(currentFinished, nextFinished, tug(beforeCurrentStop, inCurrentStop), qualifies = false),
            // false, tug encounter encompasses current stop, indicating other activities, so not a tug
            TugSource(currentFinished, nextFinished, tug(beforeCurrentStop, betweenStops), qualifies = false),

            // 40
            // false, tug encounter encompasses current stop, indicating other activities, so not a tug
            TugSource(currentFinished, nextFinished, tug(beforeCurrentStop, inNextStop), qualifies = false),
            // false, tug encounter encompasses current and next stop, indicating other activities, so not a tug
            TugSource(currentFinished, nextFinished, tug(beforeCurrentStop, afterNextStop), qualifies = false),
            // false, current and next stops are finished, but tug still tugging, indicates other activities, not a tug
            TugSource(currentFinished, nextFinished, tug(inCurrentStop, null), qualifies = false),
            // false, tug encounter start and end in current berth
            TugSource(currentFinished, nextFinished, tug(inCurrentStop, inCurrentStop), qualifies = false),
            // true, tug tugged main ship from current berth
            TugSource(currentFinished, nextFinished, tug(inCurrentStop, betweenStops), qualifies = true),

            // 45
            // true, tug tugged ship from current berth (departure) to next berth (arrival)
            TugSource(currentFinished, nextFinished, tug(inCurrentStop, inNextStop), qualifies = true),
            // false, tug encounter ended after next stop, indicating other activities, so not a tug
            TugSource(currentFinished, nextFinished, tug(inCurrentStop, afterNextStop), qualifies = false),
            // false, tug encounter start outside current berth (also for next items)
            TugSource(currentFinished, nextFinished, tug(betweenStops, null), qualifies = false),
            TugSource(currentFinished, nextFinished, tug(betweenStops, betweenStops), qualifies = false),
            TugSource(currentFinished, nextFinished, tug(betweenStops, inNextStop), qualifies = false),

            // 50
            // false, tug encounter start outside current berth (also for next items)
            TugSource(currentFinished, nextFinished, tug(betweenStops, afterNextStop), qualifies = false),
            TugSource(currentFinished, nextFinished, tug(inNextStop, null), qualifies = false),
            TugSource(currentFinished, nextFinished, tug(inNextStop, inNextStop), qualifies = false),
            TugSource(currentFinished, nextFinished, tug(inNextStop, afterNextStop), qualifies = false),
            TugSource(currentFinished, nextFinished, tug(afterNextStop, null), qualifies = false),

            // 55
            // false, tug encounter start and end outside current berth
            TugSource(currentFinished, nextFinished, tug(afterNextStop, afterNextStop), qualifies = false),
            // Ending 10 minutes before stop should not result in tug
            TugSource(currentFinished, null, tug(inCurrentStop, toleranceTimeBefore), qualifies = false),
            TugSource(currentFinished, nextFinished, tug(inCurrentStop, toleranceTimeBefore), qualifies = false),
            // Starting 10 minutes after stop should result in qualification due to tolerance
            TugSource(currentFinished, null, tug(toleranceTimeAfter, betweenStops), qualifies = true),
            TugSource(currentFinished, null, tug(toleranceTimeAfter, betweenStops), qualifies = true),
            // 60
        ).map { Arguments.of(it) }.stream()
    }

    @ParameterizedTest
    @MethodSource("departureTugsSource")
    fun departureTugs(tugSource: TugSource) {
        val (currentStop, nextStop, tug, qualifiesAsDepartureTug) = tugSource
        currentStop ?: fail("definition failure, currentStop must always be set")
        val encounter = NewEncounter(EncounterType.TUG, otherMmsi = 0, otherImo = 0, startEventId = "", tug.start, tug.end)
        val tugInfo = TugInfo(ref = 0, encounter)
        val result = generator.findDepartureTugs(currentStop, nextStop, listOf(tugInfo))

        println(tugSource)

        if (qualifiesAsDepartureTug) {
            assertTrue(result.isNotEmpty())
            assertEquals(tugInfo, result.first())
        } else {
            // tug should not be returned, as it is not an arrival tug
            assertTrue(result.isEmpty())
        }
    }

    data class TugSource(
        val stop1: SimpleStartEnd?,
        val stop2: SimpleStartEnd?,
        val tug: SimpleStartEnd,
        val qualifies: Boolean
    ) {
        init {
            stop1 ?: stop2 ?: throw Exception("At least one stop needs to be set")
        }
    }

    data class GroupBerthVisitsByTerminalSource(
        val visits: List<BerthVisitInfo>,
        val expected: List<Pair<String, List<Int>>>
    )

    private fun groupBerthVisitsByTerminalSource(): Stream<Arguments> {
        val terminalA = "terminalA"
        val terminalB = "terminalB"
        val timeline = Timeline()
        fun berthVisit(terminalId: String?) = berthVisit(
            terminalId = terminalId,
            startEnd = timeline.generatePair()
        )

        return listOf(

            // 1 - no berth visits
            GroupBerthVisitsByTerminalSource(
                visits = emptyList(),
                expected = emptyList()
            ),

            // 2 - one visit to berth without terminal
            GroupBerthVisitsByTerminalSource(
                visits = listOf(berthVisit(terminalId = null)),
                expected = emptyList()
            ),

            // 3 - multiple visit to berth without terminal
            GroupBerthVisitsByTerminalSource(
                visits = listOf(
                    berthVisit(terminalId = null),
                    berthVisit(terminalId = null)
                ),
                expected = emptyList()
            ),

            // 3 - one visit to berth with terminal A
            GroupBerthVisitsByTerminalSource(
                visits = listOf(berthVisit(terminalA)),
                expected = listOf(terminalA to listOf(0))
            ),

            // 4 - multiple visit to berth with terminal A
            GroupBerthVisitsByTerminalSource(
                visits = listOf(
                    berthVisit(terminalA),
                    berthVisit(terminalA)
                ),
                expected = listOf(terminalA to listOf(0, 1))
            ),

            // 5 - one berth with terminal A, then one with terminal B
            GroupBerthVisitsByTerminalSource(
                visits = listOf(
                    berthVisit(terminalA),
                    berthVisit(terminalB)
                ),
                expected = listOf(
                    terminalA to listOf(0),
                    terminalB to listOf(1),
                )
            ),

            // 6 - two berths adjacent with terminal A, then one with terminal B
            GroupBerthVisitsByTerminalSource(
                visits = listOf(
                    berthVisit(terminalA),
                    berthVisit(terminalA),
                    berthVisit(terminalB),
                ),
                expected = listOf(
                    terminalA to listOf(0, 1),
                    terminalB to listOf(2),
                )
            ),

            // 7 - two berths adjacent with terminal A, then two with terminal B
            GroupBerthVisitsByTerminalSource(
                visits = listOf(
                    berthVisit(terminalA),
                    berthVisit(terminalA),
                    berthVisit(terminalB),
                    berthVisit(terminalB),
                ),
                expected = listOf(
                    terminalA to listOf(0, 1),
                    terminalB to listOf(2, 3),
                )
            ),

            // 8 - two berths adjacent with terminal A, then one without terminal, then one with terminal A
            GroupBerthVisitsByTerminalSource(
                visits = listOf(
                    berthVisit(terminalA),
                    berthVisit(terminalA),
                    berthVisit(terminalId = null),
                    berthVisit(terminalB),
                    berthVisit(terminalB),
                ),
                expected = listOf(
                    terminalA to listOf(0, 1),
                    terminalB to listOf(3, 4),
                )
            ),

            // 9 - two berths adjacent with terminal A, then one without terminal, then one with terminal B
            GroupBerthVisitsByTerminalSource(
                visits = listOf(
                    berthVisit(terminalA),
                    berthVisit(terminalA),
                    berthVisit(terminalId = null),
                    berthVisit(terminalId = null),
                    berthVisit(terminalB),
                    berthVisit(terminalB),
                ),
                expected = listOf(
                    terminalA to listOf(0, 1),
                    terminalB to listOf(4, 5),
                )
            ),
        ).map { Arguments.of(it) }.stream()
    }

    @ParameterizedTest
    @MethodSource("groupBerthVisitsByTerminalSource")
    fun groupBerthVisitsByTerminal(source: GroupBerthVisitsByTerminalSource) {
        val result = generator.groupBerthVisitsByTerminal(source.visits)
        val expected = source.expected.map { (terminalId, expectedVisitIndices) ->
            BerthVisitGroup(terminalId, expectedVisitIndices.map { index -> source.visits[index] }.toMutableList())
        }
        assertEquals(expected, result)
    }

    private fun berthVisit(
        ref: Int = 0,
        terminalId: String? = null,
        startEnd: Pair<LocationTime, LocationTime>,
        berth: Berth = createBerth(_id = "berth-$ref", name = "berth-$ref", terminalId = terminalId),
        portAreaInfo: PortAreaInfo? = null,
        arrivalTugs: List<TugInfo> = emptyList(),
        departureTugs: List<TugInfo> = emptyList(),
    ) = BerthVisitInfo(
        ref = ref,
        activity = createAreaActivity(
            id = UUID.randomUUID().toString(),
            start = startEnd.first,
            end = startEnd.second,
            areaId = berth._id ?: throw Exception("Need a berth id")
        ),
        area = berth,
        portArea = portAreaInfo,
        arrivalTugs = arrivalTugs,
        departureTugs = departureTugs,
        firstLineSecured = null,
        allFast = null,
        lastLineReleased = null,
        terminalVisit = null
    )

    @Test
    fun `generate terminal visits - no berth visits`() {
        assertEquals(
            emptyList<TerminalVisitInfo>(),
            generator.generateTerminalVisits(berthVisitGroups = emptyList(), terminalMoorings = emptyList())
        )
    }

    @Test
    fun `generate terminal visits - one berth visit`() {
        val terminalId = "terminalA"
        val terminal = createTerminal(_id = terminalId)
        val timeline = Timeline()
        val berthVisit = berthVisit(ref = 1, terminalId = "terminalA", startEnd = timeline.generatePair())
        val visitGroup = BerthVisitGroup(
            terminalId = "terminalA",
            visits = mutableListOf(berthVisit)
        )
        whenever(infraService.getById(eq(terminalId), eq(InfraAreaType.TERMINAL))).thenReturn(terminal)

        val expected = TerminalVisitInfo(
            ref = 0,
            activity = AreaActivity(
                id = "derived-terminal-visit-ref-0",
                start = berthVisit.start,
                end = berthVisit.end,
                areaId = terminalId
            ),
            mooringActivity = null,
            area = terminal,
            portArea = berthVisit.portArea,
            berthVisits = listOf(berthVisit)
        )

        assertEquals(
            listOf(expected),
            generator.generateTerminalVisits(listOf(visitGroup), terminalMoorings = emptyList())
        )
    }

    @Test
    fun `generate terminal visits - two berth visits`() {
        val timeline = Timeline()
        val terminalId = "terminalA"
        val terminal = createTerminal(_id = terminalId)
        val port = createPort()
        val portArea = PortAreaInfo(
            ref = 0,
            isPassThrough = false,
            createAreaActivity(start = timeline.generate(), end = timeline.generate(), areaId = port._id!!),
            area = port
        )
        val berthVisit1 = berthVisit(ref = 1, "terminalA", startEnd = timeline.generatePair(), portAreaInfo = portArea)
        val berthVisit2 = berthVisit(ref = 2, "terminalA", startEnd = timeline.generatePair(), portAreaInfo = portArea)
        val visitGroup = BerthVisitGroup(
            terminalId = "terminalA",
            visits = mutableListOf(berthVisit1, berthVisit2)
        )
        whenever(infraService.getById(eq(terminalId), eq(InfraAreaType.TERMINAL))).thenReturn(terminal)

        val expected = TerminalVisitInfo(
            ref = 0,
            activity = AreaActivity(
                id = "derived-terminal-visit-ref-0",
                start = berthVisit1.start,
                end = berthVisit2.end,
                areaId = terminalId
            ),
            mooringActivity = null,
            area = terminal,
            portArea = portArea,
            berthVisits = listOf(berthVisit1, berthVisit2)
        )

        assertEquals(
            listOf(expected),
            generator.generateTerminalVisits(listOf(visitGroup), terminalMoorings = emptyList())
        )
    }

    @Test
    fun `generateAnchorStops - other types give an empty list`() {
        assertEquals(
            emptyList<AnchorStopInfo>(),
            generator.generateAnchorStops(listOf(createNewStop(BERTH)), emptyList())
        )
        assertEquals(
            emptyList<AnchorStopInfo>(),
            generator.generateAnchorStops(listOf(createNewStop(UNCLASSIFIED)), emptyList())
        )
    }

    @Test
    fun `generateAnchorStops - port area not found still results in an anchor stop`() {
        val anchorage = createAnchorage(_id = ANCHOR_AREA_4EAST, ports = emptyList())
        val stop = createNewStop(type = ANCHOR_AREA, areaId = ANCHOR_AREA_4EAST)
        val portAreas = emptyList<PortAreaInfo>()
        whenever(infraService.getById(eq(ANCHOR_AREA_4EAST), eq(InfraAreaType.ANCHOR))).thenReturn(anchorage)
        val result = generator.generateAnchorStops(listOf(stop), portAreas)

        val expected = AnchorStopInfo(stop, anchorage, portArea = null)
        assertTrue(result.size == 1)
        assertEquals(expected, result.first())
    }

    @Test
    fun generateAnchorStops() {
        val anchorage = createAnchorage(_id = ANCHOR_AREA_4EAST, ports = listOf(NLRTM))
        val stop = createNewStop(type = ANCHOR_AREA, areaId = ANCHOR_AREA_4EAST)
        val port = createPort(_id = NLRTM)
        val portArea = PortAreaInfo(
            ref = 0,
            isPassThrough = false,
            activity = createAreaActivity(areaId = NLRTM),
            area = port
        )
        whenever(infraService.getById(eq(ANCHOR_AREA_4EAST), eq(InfraAreaType.ANCHOR))).thenReturn(anchorage)
        val result = generator.generateAnchorStops(listOf(stop), listOf(portArea))

        val expected = AnchorStopInfo(stop, anchorage, portArea)
        assertTrue(result.size == 1)
        assertEquals(expected, result.first())
    }

    @Test
    fun `generateUnclassifiedStops - other stop types are ignored`() {
        val stops = listOf(
            createNewStop(type = BERTH),
            createNewStop(type = ANCHOR_AREA),
        )
        assertEquals(
            emptyList<UnclassifiedStopInfo>(),
            generator.generateUnclassifiedStops(stops, portAreas = emptyList(), eospAreaId = "")
        )
    }

    @Test
    fun `generateUnclassifiedStops - time outside main port inner area`() {
        val timeline = Timeline()
        val outsidePort = timeline.generate()
        val (portStart, portEnd) = timeline.generatePair()
        val nlrtmNoPolygon = PortAreaInfo(
            ref = 1,
            isPassThrough = false,
            activity = createAreaActivity(areaId = NLRTM, start = portStart, end = portEnd),
            area = createPort(_id = NLRTM)
        )
        val eospAreaId = nlrtmNoPolygon.area._id!!

        val stop1 = createNewStop(type = UNCLASSIFIED, start = outsidePort)
        assertEquals(
            listOf(UnclassifiedStopInfo(stop1, portArea = null)),
            generator.generateUnclassifiedStops(listOf(stop1), portAreas = listOf(nlrtmNoPolygon), eospAreaId)
        )
        assertEquals(
            listOf(UnclassifiedStopInfo(stop1, portArea = null)),
            generator.generateUnclassifiedStops(listOf(stop1), portAreas = listOf(nlrtmNoPolygon), eospAreaId)
        )

        val stop2 = createNewStop(type = UNCLASSIFIED, start = outsidePort)
        assertEquals(
            listOf(UnclassifiedStopInfo(stop2, portArea = null)),
            generator.generateUnclassifiedStops(listOf(stop2), portAreas = emptyList(), eospAreaId)
        )
        assertEquals(
            listOf(UnclassifiedStopInfo(stop2, portArea = null)),
            generator.generateUnclassifiedStops(listOf(stop2), portAreas = listOf(nlrtmNoPolygon), eospAreaId)
        )
    }

    @Test
    fun `generateUnclassifiedStops - matches port area polygon when in port`() {
        val timeline = Timeline()
        val portStart = timeline.generate()
        val insidePort = timeline.generate()
        val portEnd = timeline.generate()

        val nlrtm = PortAreaInfo(
            ref = 1,
            isPassThrough = false,
            activity = createAreaActivity(areaId = NLRTM, start = portStart, end = portEnd),
            area = createPort(_id = NLRTM)
        )
        val eospAreaId = nlrtm.area._id!!

        val stop1 = createNewStop(type = UNCLASSIFIED, start = insidePort)
        assertEquals(
            listOf(UnclassifiedStopInfo(stop1, nlrtm)),
            generator.generateUnclassifiedStops(listOf(stop1), listOf(nlrtm), eospAreaId)
        )

        val stop2 = createNewStop(type = UNCLASSIFIED, start = insidePort)
        assertEquals(
            listOf(UnclassifiedStopInfo(stop2, nlrtm)),
            generator.generateUnclassifiedStops(listOf(stop2), listOf(nlrtm), eospAreaId)
        )
    }

    @Test
    fun `generateEncounters - no matching references`() {
        val encounter = NewEncounter(
            type = EncounterType.UNCLASSIFIED,
            otherImo = 1,
            otherMmsi = 2,
            startEventId = "",
            start = createLocationTime(),
            end = createLocationTime()
        )
        val expected = EncounterInfo(
            encounter,
            portAreaInfo = null,
            berthVisitInfo = null,
            terminalVisitInfo = null
        )
        assertEquals(
            listOf(expected),
            generator.generateEncounters(
                encounters = listOf(encounter),
                portAreas = emptyList(),
                berthVisits = emptyList(),
            )
        )
    }

    @Test
    fun `generateEncounters - matches berth visit`() {
        val timeline = Timeline()
        val (startPort, startBerth, startEncounter) = timeline.generateN(3)
        val (endEncounter, endBerth, endPort) = timeline.generateN(3)
        val berthActivity = createAreaActivity(start = startBerth, end = endBerth, areaId = "")
        val portActivity = createAreaActivity(start = startPort, end = endPort, areaId = "")
        val portAreaInfo = PortAreaInfo(ref = 1, isPassThrough = false, activity = portActivity, area = mock())

        // first check has no terminal visit; terminal visit will be attached before the 2nd check!
        val berthVisit = createBerthVisitInfo(ref = 2, activity = berthActivity)
        // only for 2nd check!
        val terminalVisit = createTerminalVisitInfo(
            ref = 3,
            activity = berthActivity,
            berthVisits = listOf(berthVisit)
        )

        val encounter = NewEncounter(
            type = EncounterType.UNCLASSIFIED,
            otherImo = 1,
            otherMmsi = 2,
            startEventId = "",
            start = startEncounter,
            end = endEncounter
        )

        // berth visit with no terminal visit
        assertEquals(
            listOf(
                EncounterInfo(
                    encounter,
                    portAreaInfo = portAreaInfo,
                    berthVisitInfo = berthVisit,
                    terminalVisitInfo = null
                )
            ),
            generator.generateEncounters(
                encounters = listOf(encounter),
                portAreas = listOf(portAreaInfo),
                berthVisits = listOf(berthVisit),
            )
        )

        // berth visit with terminal visit
        val berthVisitWithTerminalVisit = berthVisit.copy(terminalVisit = terminalVisit)
        assertEquals(
            listOf(
                EncounterInfo(
                    encounter,
                    portAreaInfo = portAreaInfo,
                    berthVisitInfo = berthVisitWithTerminalVisit,
                    terminalVisitInfo = terminalVisit
                )
            ),
            generator.generateEncounters(
                encounters = listOf(encounter),
                portAreas = listOf(portAreaInfo),
                berthVisits = listOf(berthVisitWithTerminalVisit),
            )
        )
    }

    data class PilotTest(
        val encounters: List<NewEncounter>,
        val anchorVisit: List<AnchorStopInfo>,
        val berthVisits: List<BerthVisitInfo>,
        val pilotAreaActivities: List<AreaActivity>,
        val expectedResult: PilotInfo?
    )

    private fun findInboundPilotSource(): Stream<Arguments> {
        val timeline = Timeline()
        val beforeAnchor = timeline.generatePair()
        val (anchorStart, anchorEnd) = timeline.generateN(2)
        val beforeBerth = timeline.generatePair()
        val (berthStart, pilotDuringBerthStart, pilotDuringBerthEnd, berthEnd) = timeline.generateN(4)
        val duringBerth = Pair(pilotDuringBerthStart, pilotDuringBerthEnd)
        val afterBerth = timeline.generatePair()

        val pilotBeforeAnchor = nl.teqplay.vesselvoyage.util.createNewEncounter(EncounterType.PILOT, beforeAnchor)
        val pilotBeforeBerth = nl.teqplay.vesselvoyage.util.createNewEncounter(EncounterType.PILOT, beforeBerth)
        val pilotDuringBerth = nl.teqplay.vesselvoyage.util.createNewEncounter(EncounterType.PILOT, duringBerth)
        val pilotAfterBerth = nl.teqplay.vesselvoyage.util.createNewEncounter(EncounterType.PILOT, afterBerth)
        val berthVisit = createBerthVisitInfo(
            ref = 0,
            activity = createAreaActivity(start = berthStart, end = berthEnd, areaId = "")
        )
        val anchorVisit = AnchorStopInfo(
            stop = createNewStop(
                type = ANCHOR_AREA,
                start = anchorStart,
                end = anchorEnd
            ),
            area = null,
            portArea = null
        )

        val earlierPilot = pilotBeforeBerth.copy(
            start = with(pilotBeforeBerth.start) { copy(time = time.minusSeconds(10L)) },
            end = with(pilotBeforeBerth.end) { this?.copy(time = time.minusSeconds(10L)) }
        )

        return Stream.of(

            // (1) no pilot encounters, no berth visits
            PilotTest(
                encounters = emptyList(),
                berthVisits = emptyList(),
                anchorVisit = emptyList(),
                pilotAreaActivities = emptyList(),
                expectedResult = null
            ),

            // (2) no inbound pilot, needs a berth visit to classify a pilot as inbound or outbound
            PilotTest(
                encounters = listOf(pilotBeforeBerth, pilotDuringBerth, pilotAfterBerth),
                berthVisits = emptyList(),
                anchorVisit = emptyList(),
                pilotAreaActivities = emptyList(),
                expectedResult = null
            ),

            // (3) no inbound pilot, pilot starts during first berth, but needs to start before first berth
            PilotTest(
                encounters = listOf(pilotDuringBerth),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = emptyList(),
                expectedResult = null
            ),

            // (4) no inbound pilot, pilot starts after first berth, but needs to start before first berth
            PilotTest(
                encounters = listOf(pilotAfterBerth),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = emptyList(),
                expectedResult = null
            ),

            // (5) inbound pilot found, starts before first berth
            PilotTest(
                encounters = listOf(pilotBeforeBerth),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = emptyList(),
                expectedResult = PilotInfo.fromPilotShipEncounter(encounter = pilotBeforeBerth, pilotArea = null)
            ),

            // (6) inbound pilot is first pilot, even when there is one closer to the berth start
            PilotTest(
                encounters = listOf(earlierPilot, pilotBeforeBerth),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = emptyList(),
                expectedResult = PilotInfo.fromPilotShipEncounter(encounter = earlierPilot, pilotArea = null)
            ),

            // (7) Inbound pilot should be the pilot before anchoring when no berth visit is detected
            PilotTest(
                encounters = listOf(pilotBeforeAnchor, pilotBeforeBerth),
                berthVisits = emptyList(),
                anchorVisit = listOf(anchorVisit),
                pilotAreaActivities = emptyList(),
                expectedResult = PilotInfo.fromPilotShipEncounter(encounter = pilotBeforeAnchor, pilotArea = null)
            ),

            // (8) Inbound pilot should be the pilot after the anchor visit when we have a berth visit after
            PilotTest(
                encounters = listOf(pilotBeforeAnchor, pilotBeforeBerth),
                berthVisits = listOf(berthVisit),
                anchorVisit = listOf(anchorVisit),
                pilotAreaActivities = emptyList(),
                expectedResult = PilotInfo.fromPilotShipEncounter(encounter = pilotBeforeAnchor, pilotArea = null)
            ),

            // (9) Inbound pilot should NOT be set when the pilot is after the anchor visit
            PilotTest(
                encounters = listOf(pilotBeforeBerth),
                berthVisits = emptyList(),
                anchorVisit = listOf(anchorVisit),
                pilotAreaActivities = emptyList(),
                expectedResult = null
            )
        ).map { Arguments.of(it) }
    }

    @ParameterizedTest
    @MethodSource("findInboundPilotSource")
    fun findInboundPilot(test: PilotTest) {
        assertEquals(
            test.expectedResult,
            generator.findInboundPilot(
                encounters = test.encounters,
                berthVisits = test.berthVisits,
                anchorVisits = test.anchorVisit,
                pilotAreaActivities = test.pilotAreaActivities
            )
        )
    }

    private fun findOutboundPilotSource(): Stream<Arguments> {

        val timeline = Timeline()
        val beforeAnchor = timeline.generatePair()
        val (anchorStart, anchorEnd) = timeline.generateN(2)
        val afterAnchor = timeline.generatePair()
        val (anchorStart2, anchorEnd2) = timeline.generateN(2)
        val afterAnchor2 = timeline.generatePair()
        val beforeBerth = timeline.generatePair()
        val (berthStart, pilotDuringBerthStart, pilotDuringBerthEnd, berthEnd) = timeline.generateN(4)
        val duringBerth = Pair(pilotDuringBerthStart, pilotDuringBerthEnd)
        val afterBerth = timeline.generatePair()

        val pilotBeforeAnchor = nl.teqplay.vesselvoyage.util.createNewEncounter(EncounterType.PILOT, beforeAnchor)
        val pilotBeforeBerth = nl.teqplay.vesselvoyage.util.createNewEncounter(EncounterType.PILOT, beforeBerth)
        val pilotAfterAnchor1 = nl.teqplay.vesselvoyage.util.createNewEncounter(EncounterType.PILOT, afterAnchor)
        val pilotAfterAnchor2 = nl.teqplay.vesselvoyage.util.createNewEncounter(EncounterType.PILOT, afterAnchor2)
        val pilotDuringBerth = nl.teqplay.vesselvoyage.util.createNewEncounter(EncounterType.PILOT, duringBerth)
        val pilotAfterBerth = nl.teqplay.vesselvoyage.util.createNewEncounter(EncounterType.PILOT, afterBerth)
        val berthVisit = createBerthVisitInfo(
            ref = 0,
            activity = createAreaActivity(start = berthStart, end = berthEnd, areaId = "")
        )
        val anchorVisit = AnchorStopInfo(
            stop = createNewStop(
                type = ANCHOR_AREA,
                start = anchorStart,
                end = anchorEnd
            ),
            area = null,
            portArea = null
        )
        val anchorVisit2 = AnchorStopInfo(
            stop = createNewStop(
                type = ANCHOR_AREA,
                start = anchorStart2,
                end = anchorEnd2
            ),
            area = null,
            portArea = null
        )

        // for case 6
        val laterPilot = pilotAfterBerth.copy(
            start = with(pilotAfterBerth.start) { copy(time = time.plusSeconds(10L)) },
            end = with(pilotAfterBerth.end) { this?.copy(time = time.plusSeconds(10L)) }
        )

        return Stream.of(
            // (1) no pilot encounters, no berth visits
            PilotTest(
                encounters = emptyList(),
                berthVisits = emptyList(),
                anchorVisit = emptyList(),
                pilotAreaActivities = emptyList(),
                expectedResult = null
            ),

            // (2) no outbound pilot, needs a berth visit to classify a pilot as outbound or outbound
            PilotTest(
                encounters = listOf(pilotBeforeBerth, pilotDuringBerth, pilotAfterBerth),
                berthVisits = emptyList(),
                anchorVisit = emptyList(),
                pilotAreaActivities = emptyList(),
                expectedResult = null
            ),

            // (3) no outbound pilot, pilot starts before last berth, but needs to start after last berth
            PilotTest(
                encounters = listOf(pilotBeforeBerth),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = emptyList(),
                expectedResult = null
            ),

            // (4) no outbound pilot found, pilot starts during last berth
            PilotTest(
                encounters = listOf(pilotDuringBerth),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = emptyList(),
                expectedResult = null
            ),

            // (5) outbound pilot found, starts after last berth
            PilotTest(
                encounters = listOf(pilotAfterBerth),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = emptyList(),
                expectedResult = PilotInfo.fromPilotShipEncounter(encounter = pilotAfterBerth, pilotArea = null)
            ),

            // (6) outbound pilot is first pilot just after the berth
            PilotTest(
                encounters = listOf(pilotAfterBerth, laterPilot),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = emptyList(),
                expectedResult = PilotInfo.fromPilotShipEncounter(encounter = pilotAfterBerth, pilotArea = null)
            ),

            // (7) Outbound pilot should be the pilot after leaving the last anchorage when we don't go to a berth
            PilotTest(
                encounters = listOf(pilotBeforeAnchor, pilotAfterAnchor1, pilotAfterAnchor2),
                berthVisits = emptyList(),
                anchorVisit = listOf(anchorVisit, anchorVisit2),
                pilotAreaActivities = emptyList(),
                expectedResult = PilotInfo.fromPilotShipEncounter(encounter = pilotAfterAnchor2, pilotArea = null)
            ),

            // (8) Outbound pilot should NOT be set as we went to a berth after the last pilot
            PilotTest(
                encounters = listOf(pilotBeforeAnchor, pilotAfterAnchor1, pilotAfterAnchor2),
                berthVisits = listOf(berthVisit),
                anchorVisit = listOf(anchorVisit),
                pilotAreaActivities = emptyList(),
                expectedResult = null
            )
        ).map { Arguments.of(it) }
    }

    @ParameterizedTest
    @MethodSource("findOutboundPilotSource")
    fun findOutboundPilot(test: PilotTest) {
        assertEquals(
            test.expectedResult,
            generator.findOutboundPilot(
                encounters = test.encounters,
                berthVisits = test.berthVisits,
                anchorVisits = test.anchorVisit,
                pilotAreaActivities = test.pilotAreaActivities
            )
        )
    }

    private fun findInboundPilotByAreaFallbackSource(): Stream<Arguments> {

        val timeline = Timeline()
        val beforeBerth = timeline.generatePair()
        val (berthStart, pilotDuringBerthStart, pilotDuringBerthEnd, berthEnd) = timeline.generateN(4)
        val duringBerth = Pair(pilotDuringBerthStart, pilotDuringBerthEnd)
        val afterBerth = timeline.generatePair()

        val pilotBeforeBerth = createAreaActivity(beforeBerth)
        val pilotDuringBerth = createAreaActivity(duringBerth)
        val pilotAfterBerth = createAreaActivity(afterBerth)
        val berthVisit = createBerthVisitInfo(
            ref = 0,
            activity = createAreaActivity(start = berthStart, end = berthEnd, areaId = "")
        )

        // for case 6
        val earlierPilot = pilotBeforeBerth.copy(
            start = with(pilotBeforeBerth.start) { copy(time = time.minusSeconds(10L)) },
            end = with(pilotBeforeBerth.end) { this?.copy(time = time.minusSeconds(10L)) }
        )

        return Stream.of(

            // (1) no pilot area activity, no berth visits
            PilotTest(
                encounters = emptyList(),
                berthVisits = emptyList(),
                anchorVisit = emptyList(),
                pilotAreaActivities = emptyList(),
                expectedResult = null
            ),

            // (2) no inbound pilot, needs a berth visit to classify a pilot as inbound or outbound
            PilotTest(
                encounters = emptyList(),
                berthVisits = emptyList(),
                anchorVisit = emptyList(),
                pilotAreaActivities = listOf(pilotBeforeBerth, pilotDuringBerth, pilotAfterBerth),
                expectedResult = null
            ),

            // (3) no inbound pilot, pilot starts during first berth, but needs to start before first berth
            PilotTest(
                encounters = emptyList(),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = listOf(pilotDuringBerth),
                expectedResult = null
            ),

            // (4) no inbound pilot, pilot starts after first berth, but needs to start before first berth
            PilotTest(
                encounters = emptyList(),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = listOf(pilotAfterBerth),
                expectedResult = null
            ),

            // (5) inbound pilot found, starts before first berth
            PilotTest(
                encounters = emptyList(),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = listOf(pilotBeforeBerth),
                expectedResult = PilotInfo.fromPilotAreaActivity(pilotAreaActivity = pilotBeforeBerth, pilotArea = null)
            ),

            // (6) inbound pilot is second pilot, that is the last one before the first berth
            // create an earlier pilot, does not matter if those overlap, as long as the encounter.start is earlier
            PilotTest(
                encounters = emptyList(),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = listOf(earlierPilot, pilotBeforeBerth),
                expectedResult = PilotInfo.fromPilotAreaActivity(pilotAreaActivity = pilotBeforeBerth, pilotArea = null)
            )
        ).map { Arguments.of(it) }
    }

    @ParameterizedTest
    @MethodSource("findInboundPilotByAreaFallbackSource")
    fun findInboundPilotByAreaFallback(test: PilotTest) {
        assertEquals(
            test.expectedResult,
            generator.findInboundPilotByAreaFallback(test.pilotAreaActivities, test.berthVisits.firstOrNull())
        )
    }

    private fun findOutboundPilotByAreaFallbackSource(): Stream<Arguments> {

        val timeline = Timeline()
        val beforeBerth = timeline.generatePair()
        val (berthStart, pilotDuringBerthStart, pilotDuringBerthEnd, berthEnd) = timeline.generateN(4)
        val duringBerth = Pair(pilotDuringBerthStart, pilotDuringBerthEnd)
        val afterBerth = timeline.generatePair()

        val pilotBeforeBerth = createAreaActivity(beforeBerth)
        val pilotDuringBerth = createAreaActivity(duringBerth)
        val pilotAfterBerth = createAreaActivity(afterBerth)
        val berthVisit = createBerthVisitInfo(
            ref = 0,
            activity = createAreaActivity(start = berthStart, end = berthEnd, areaId = "")
        )

        // for case 6
        val laterPilot = pilotAfterBerth.copy(
            start = with(pilotAfterBerth.start) { copy(time = time.plusSeconds(10L)) },
            end = with(pilotAfterBerth.end) { this?.copy(time = time.plusSeconds(10L)) }
        )

        return Stream.of(
            // (1) no pilot encounters, no berth visits
            PilotTest(
                encounters = emptyList(),
                berthVisits = emptyList(),
                anchorVisit = emptyList(),
                pilotAreaActivities = emptyList(),
                expectedResult = null
            ),

            // (2) no outbound pilot, needs a berth visit to classify a pilot as outbound or outbound
            PilotTest(
                encounters = emptyList(),
                berthVisits = emptyList(),
                anchorVisit = emptyList(),
                pilotAreaActivities = listOf(pilotBeforeBerth, pilotDuringBerth, pilotAfterBerth),
                expectedResult = null
            ),

            // (3) no outbound pilot, pilot starts before last berth, but needs to start after last berth
            PilotTest(
                encounters = emptyList(),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = listOf(pilotBeforeBerth),
                expectedResult = null
            ),

            // (4) no outbound pilot found, pilot starts during last berth
            PilotTest(
                encounters = emptyList(),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = listOf(pilotDuringBerth),
                expectedResult = null
            ),

            // (5) outbound pilot found, starts after last berth
            PilotTest(
                encounters = emptyList(),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = listOf(pilotAfterBerth),
                expectedResult = PilotInfo.fromPilotAreaActivity(pilotAreaActivity = pilotAfterBerth, pilotArea = null)
            ),

            // (6) outbound pilot is last pilot and not the pilot just after the berth
            PilotTest(
                encounters = emptyList(),
                berthVisits = listOf(berthVisit),
                anchorVisit = emptyList(),
                pilotAreaActivities = listOf(pilotAfterBerth, laterPilot),
                expectedResult = PilotInfo.fromPilotAreaActivity(pilotAreaActivity = laterPilot, pilotArea = null)
            )
        ).map { Arguments.of(it) }
    }

    @ParameterizedTest
    @MethodSource("findOutboundPilotByAreaFallbackSource")
    fun findOutboundPilotByAreaFallback(test: PilotTest) {
        assertEquals(
            test.expectedResult,
            generator.findOutboundPilotByAreaFallback(test.pilotAreaActivities, test.berthVisits.lastOrNull())
        )
    }

    private fun pilotInboundOutboundAreaMatchingSource(): Stream<Arguments> {

        // note that the berth visit is created in the test method, so this source can be re-used for inbound and
        // outbound

        // The encounter (e.g. pilot-1) must overlap time range of a pilot area activity (e.g. pilot area A) to get
        // pbp1 as part of the expected result.

        // Imagine a ship sailed through these pilot areas:
        //     ----[  pilot area A ]-----[ pilot area B ]----------------------------
        // With these pilot encounters as test cases:
        // (1) --[ pilot-1 ]-------:-----:--------------:----------------------------
        // (2) ----:--[ pilot-2 ]--:-----:--------------:----------------------------
        // (3) ----:-------[ pilot-3 ]---:--------------:----------------------------
        // (4) ----:----------[ both-overlapping ]------:----------------------------
        // (5) ----:---------------:-----:--------------:-----[ non-overlapping ]----

        val duration = Duration.ofSeconds(60)
        val timeline = Timeline(timeBetweenEvents = duration)

        val pilotAreaA = createAreaActivity(timeline.generatePair(), areaId = "pbp-A")
        val pilotAreaB = createAreaActivity(timeline.generatePair(), areaId = "pbp-B")
        val pilotAreaActivities = listOf(pilotAreaA, pilotAreaB)

        val pbp1 = createPilotBoardingPlace(_id = "pbp-A")

        val overlap1 = nl.teqplay.vesselvoyage.util.createNewEncounter(
            EncounterType.PILOT,
            start = pilotAreaA.start - 15L,
            end = pilotAreaA.end!! - 15L
        )
        val overlap2 = nl.teqplay.vesselvoyage.util.createNewEncounter(
            EncounterType.PILOT,
            start = pilotAreaA.start + 15L,
            end = pilotAreaA.end!! - 15L
        )
        val overlap3 = nl.teqplay.vesselvoyage.util.createNewEncounter(
            EncounterType.PILOT,
            start = pilotAreaA.start + 15L,
            end = pilotAreaA.end!! + 15L
        )
        val bothOverlap = nl.teqplay.vesselvoyage.util.createNewEncounter(
            EncounterType.PILOT,
            start = pilotAreaA.end!! - 15L,
            end = pilotAreaB.start + 15L
        )
        val nonOverlapping = nl.teqplay.vesselvoyage.util.createNewEncounter(EncounterType.PILOT, timeline.generatePair())

        return Stream.of(
            // (1) pbp1 match: overlaps on left side of pilot area A
            PilotTest(
                encounters = listOf(overlap1),
                berthVisits = emptyList(),
                anchorVisit = emptyList(),
                pilotAreaActivities = pilotAreaActivities,
                expectedResult = PilotInfo.fromPilotShipEncounter(encounter = overlap1, pilotArea = pbp1)
            ),

            // (2) pbp1 match: overlaps on inside pilot area A
            PilotTest(
                encounters = listOf(overlap2),
                berthVisits = emptyList(),
                anchorVisit = emptyList(),
                pilotAreaActivities = pilotAreaActivities,
                expectedResult = PilotInfo.fromPilotShipEncounter(encounter = overlap2, pilotArea = pbp1)
            ),

            // (3) pbp1 match: overlaps on left side of pilot area A
            PilotTest(
                encounters = listOf(overlap3),
                berthVisits = emptyList(),
                anchorVisit = emptyList(),
                pilotAreaActivities = pilotAreaActivities,
                expectedResult = PilotInfo.fromPilotShipEncounter(encounter = overlap3, pilotArea = pbp1)
            ),

            // (4) no pbp match: overlaps both pilot area activities
            PilotTest(
                encounters = listOf(bothOverlap),
                berthVisits = emptyList(),
                anchorVisit = emptyList(),
                pilotAreaActivities = pilotAreaActivities,
                expectedResult = PilotInfo.fromPilotShipEncounter(encounter = bothOverlap, pilotArea = null)
            ),

            // (5) no pbp match: overlaps no pilot area activity
            PilotTest(
                encounters = listOf(nonOverlapping),
                berthVisits = emptyList(),
                anchorVisit = emptyList(),
                pilotAreaActivities = pilotAreaActivities,
                expectedResult = PilotInfo.fromPilotShipEncounter(encounter = nonOverlapping, pilotArea = null)
            )
        ).map { Arguments.of(it) }
    }

    @ParameterizedTest
    @MethodSource("pilotInboundOutboundAreaMatchingSource")
    fun pilotInboundAreaMatching(test: PilotTest) {
        whenever(infraService.getById(eq("pbp-A"), any())).thenReturn(createPilotBoardingPlace(_id = "pbp-A"))

        // Create a berth visit far right of the pilot activities, so the pilot encounters are classified as inbound
        val startTime = test.encounters.maxOf { it.end?.time ?: it.start.time }
        val berthVisit = createBerthVisitInfo(
            ref = 0,
            activity = createAreaActivity(
                start = createLocationTime(time = startTime + 60L),
                end = createLocationTime(time = startTime + 120L),
                areaId = ""
            )
        )
        assertEquals(
            test.expectedResult,
            generator.findInboundPilot(
                encounters = test.encounters,
                berthVisits = listOf(berthVisit),
                anchorVisits = test.anchorVisit,
                pilotAreaActivities = test.pilotAreaActivities
            )
        )
    }

    @ParameterizedTest
    @MethodSource("pilotInboundOutboundAreaMatchingSource")
    fun pilotOutboundAreaMatching(test: PilotTest) {
        whenever(infraService.getById(eq("pbp-A"), any())).thenReturn(createPilotBoardingPlace(_id = "pbp-A"))
        // Create a berth visit far left of the pilot activities, so the pilot encounters are classified as outbound
        val startTime = test.encounters.minOf { it.start.time }
        val berthVisit = createBerthVisitInfo(
            ref = 0,
            activity = createAreaActivity(
                start = createLocationTime(time = startTime - 120L),
                end = createLocationTime(time = startTime - 60L),
                areaId = ""
            )
        )
        assertEquals(
            test.expectedResult,
            generator.findOutboundPilot(
                encounters = test.encounters,
                berthVisits = listOf(berthVisit),
                anchorVisits = test.anchorVisit,
                pilotAreaActivities = test.pilotAreaActivities
            )
        )
    }

    @ParameterizedTest
    @EnumSource(ShipCategoryV2::class)
    fun `generate - ship - get name, imo and ship cat v2`(categoryV2: ShipCategoryV2) {
        val imoInt = 1234567
        val imoStr = imoInt.toString()
        val name = "Boat"

        val shipCache = ShipCache(
            identifier = imoStr,
            platform = null,
            csi = ShipCache.CsiShipCache(
                createShipRegisterInfoCache(
                    imo = imoStr,
                    mmsi = "",
                    name = name,
                    categories = ShipCategories(v1 = null, v2 = categoryV2),
                    specification = ShipSpecification()
                )
            )
        )
        whenever(shipCacheService.getCacheByImo(eq(imoStr))).thenReturn(shipCache)
        val actual = generator.generate(visit = createNewVisit(imo = imoInt), esof = null)
        val expected = Ship(imo = imoInt, name = name, type = categoryV2)

        assertEquals(expected, actual.ship)
    }

    @Test
    fun `generate - when ship is not in cache only serve imo`() {
        val imoInt = 1234567
        val imoStr = imoInt.toString()
        whenever(shipCacheService.getCacheByImo(eq(imoStr))).thenReturn(null)
        val actual = generator.generate(visit = createNewVisit(imo = imoInt), esof = null)
        val expected = Ship(imo = imoInt, name = null, type = null)

        assertEquals(expected, actual.ship)
    }

    @Test
    fun `should generate SOF with correct area info`() {
        val input = createNewVisit()

        val result = generator.generate(visit = input, esof = null)
        val expected = AreaMeta(
            areaId = "TEST_PORT_ID",
            unlocode = "NLRTM",
            name = "PORT OR ROTTERDAM",
            type = "eosp"
        )

        assertEquals(expected, result.area)
    }

    @ParameterizedTest
    @MethodSource("berthVisits")
    fun berthVisitActivity(case: BerthVisitSample) {
        whenever(infraService.getBerth(any())).thenAnswer { invocation ->
            val id = invocation.arguments[0] as String
            createBerth(_id = id, name = id)
        }

        val visit = createNewVisit(
            stops = case.stops,
            berthAreaActivities = case.berthActivities.toMutableList()
        )

        val actual = generator.generate(
            visit = visit,
            esof = null,
        )

        assertEquals(case.expected, actual.berthVisits)
    }

    /**
     * Helper class containing test data for the berth visit activity test.
     */
    data class BerthVisitSample(
        val stops: List<NewStop>,
        val berthActivities: List<AreaActivity>,
        val expected: List<BerthVisit>
    )

    private fun berthVisits(): Stream<Arguments> {
        val berth1 = "BERTH1"
        val berth2 = "BERTH2"

        return Stream.of(
            Arguments.of(
                // Stop is bigger than the activity with start and end,
                // stop should be cut short on both sides to match the activity
                BerthVisitSample(
                    stops = listOf(
                        createBerthStop(
                            areaId = berth1,
                            start = Instant.parse("2023-01-01T09:00:00Z"),
                            end = Instant.parse("2023-01-01T12:00:00Z")
                        )
                    ),
                    berthActivities = listOf(
                        createBerthAreaActivity(
                            start = Instant.parse("2023-01-01T09:30:00Z"),
                            end = Instant.parse("2023-01-01T11:30:00Z"),
                            areaId = berth1
                        )
                    ),
                    expected = listOf(
                        createBerthVisit(
                            start = Instant.parse("2023-01-01T09:30:00Z"),
                            end = Instant.parse("2023-01-01T11:30:00Z"),
                            areaRef = berth1
                        )
                    )
                )
            ),
            Arguments.of(
                // Activity is bigger than the stop with start and end
                // Stop should remain the same length.
                BerthVisitSample(
                    stops = listOf(
                        createBerthStop(
                            areaId = berth1,
                            start = Instant.parse("2023-01-01T10:00:00Z"),
                            end = Instant.parse("2023-01-01T12:00:00Z")
                        ),
                    ),
                    berthActivities = listOf(
                        createBerthAreaActivity(
                            start = Instant.parse("2023-01-01T09:00:00Z"),
                            end = Instant.parse("2023-01-01T13:00:00Z"),
                            areaId = berth1
                        ),
                    ),
                    expected = listOf(
                        createBerthVisit(
                            start = Instant.parse("2023-01-01T10:00:00Z"),
                            end = Instant.parse("2023-01-01T12:00:00Z"),
                            areaRef = berth1
                        )
                    )
                )
            ),
            // Two activities for one berth stop which are both bigger than the stop
            // Stop should remain the same since activities are bigger
            Arguments.of(
                BerthVisitSample(
                    stops = listOf(
                        createBerthStop(
                            areaId = berth1,
                            start = Instant.parse("2023-01-01T10:00:00Z"),
                            end = Instant.parse("2023-01-01T12:00:00Z")
                        ),
                    ),
                    berthActivities = listOf(
                        createBerthAreaActivity(
                            start = Instant.parse("2023-01-01T09:00:00Z"),
                            end = Instant.parse("2023-01-01T11:00:00Z"),
                            areaId = berth1
                        ),
                        createBerthAreaActivity(
                            start = Instant.parse("2023-01-01T11:10:00Z"),
                            end = Instant.parse("2023-01-01T13:00:00Z"),
                            areaId = berth1
                        )
                    ),
                    expected = listOf(
                        createBerthVisit(
                            start = Instant.parse("2023-01-01T10:00:00Z"),
                            end = Instant.parse("2023-01-01T12:00:00Z"),
                            areaRef = berth1
                        )
                    )
                )
            ),
            // Three activities for one berth stop where the end of the last activity is earlier than the stop end
            // End of the stop should be cut short to match the last activity
            Arguments.of(
                BerthVisitSample(
                    stops = listOf(
                        createBerthStop(
                            areaId = berth1,
                            start = Instant.parse("2023-01-01T10:00:00Z"),
                            end = Instant.parse("2023-01-01T12:00:00Z")
                        ),
                    ),
                    berthActivities = listOf(
                        createBerthAreaActivity(
                            start = Instant.parse("2023-01-01T10:00:00Z"),
                            end = Instant.parse("2023-01-01T11:00:00Z"),
                            areaId = berth1
                        ),
                        createBerthAreaActivity(
                            start = Instant.parse("2023-01-01T11:00:00Z"),
                            end = Instant.parse("2023-01-01T11:30:00Z"),
                            areaId = berth1
                        ),
                        createBerthAreaActivity(
                            start = Instant.parse("2023-01-01T11:45:00Z"),
                            end = Instant.parse("2023-01-01T11:55:00Z"),
                            areaId = berth1
                        )
                    ),
                    expected = listOf(
                        createBerthVisit(
                            start = Instant.parse("2023-01-01T10:00:00Z"),
                            end = Instant.parse("2023-01-01T11:55:00Z"),
                            areaRef = berth1
                        )
                    )
                )
            ),
            // Two different stops with two different activities
            // Correct activities should cut the correct stops.
            Arguments.of(
                BerthVisitSample(
                    stops = listOf(
                        createBerthStop(
                            areaId = berth1,
                            start = Instant.parse("2023-01-01T10:00:00Z"),
                            end = Instant.parse("2023-01-01T12:00:00Z")
                        ),
                        createBerthStop(
                            areaId = berth1,
                            start = Instant.parse("2023-01-01T17:00:00Z"),
                            end = Instant.parse("2023-01-01T19:00:00Z")
                        )
                    ),
                    berthActivities = listOf(
                        createBerthAreaActivity(
                            start = Instant.parse("2023-01-01T10:30:00Z"),
                            end = Instant.parse("2023-01-01T12:00:00Z"),
                            areaId = berth1
                        ),
                        createBerthAreaActivity(
                            start = Instant.parse("2023-01-01T17:00:00Z"),
                            end = Instant.parse("2023-01-01T18:30:00Z"),
                            areaId = berth1
                        )
                    ),
                    expected = listOf(
                        createBerthVisit(
                            start = Instant.parse("2023-01-01T10:30:00Z"),
                            end = Instant.parse("2023-01-01T12:00:00Z"),
                            areaRef = berth1
                        ),
                        createBerthVisit(
                            start = Instant.parse("2023-01-01T17:00:00Z"),
                            end = Instant.parse("2023-01-01T18:30:00Z"),
                            areaRef = berth1,
                            ref = 1
                        )
                    )
                )
            ),
            // No activities stop should stay the same
            Arguments.of(
                BerthVisitSample(
                    stops = listOf(
                        createBerthStop(
                            areaId = berth1,
                            start = Instant.parse("2023-01-01T10:00:00Z"),
                            end = Instant.parse("2023-01-01T12:00:00Z")
                        )
                    ),
                    berthActivities = listOf(),
                    expected = listOf(
                        createBerthVisit(
                            start = Instant.parse("2023-01-01T10:00:00Z"),
                            end = Instant.parse("2023-01-01T12:00:00Z"),
                            areaRef = berth1
                        )
                    )
                )
            ),
            // Two stops at two separate berths, first one with 1 activity and second one with 2 activities
            Arguments.of(
                BerthVisitSample(
                    stops = listOf(
                        createBerthStop(
                            areaId = berth1,
                            start = Instant.parse("2023-01-01T10:00:00Z"),
                            end = Instant.parse("2023-01-01T12:00:00Z")
                        ),
                        createBerthStop(
                            areaId = berth2,
                            start = Instant.parse("2023-01-01T18:00:00Z"),
                            end = Instant.parse("2023-01-01T19:00:00Z")
                        )
                    ),
                    berthActivities = listOf(
                        createBerthAreaActivity(
                            start = Instant.parse("2023-01-01T09:00:00Z"),
                            end = Instant.parse("2023-01-01T11:00:00Z"),
                            areaId = berth1
                        ),
                        createBerthAreaActivity(
                            start = Instant.parse("2023-01-01T18:00:00Z"),
                            end = Instant.parse("2023-01-01T18:30:00Z"),
                            areaId = berth2
                        ),
                        createBerthAreaActivity(
                            start = Instant.parse("2023-01-01T18:30:00Z"),
                            end = Instant.parse("2023-01-01T18:45:00Z"),
                            areaId = berth2
                        )
                    ),
                    expected = listOf(
                        createBerthVisit(
                            start = Instant.parse("2023-01-01T10:00:00Z"),
                            end = Instant.parse("2023-01-01T11:00:00Z"),
                            areaRef = berth1
                        ),
                        createBerthVisit(
                            ref = 1,
                            start = Instant.parse("2023-01-01T18:00:00Z"),
                            end = Instant.parse("2023-01-01T18:45:00Z"),
                            areaRef = berth2
                        )
                    )
                )
            ),
            // Two stops should correctly get cut by one activity
            Arguments.of(
                BerthVisitSample(
                    stops = listOf(
                        createBerthStop(
                            areaId = berth1,
                            start = Instant.parse("2023-01-01T10:00:00Z"),
                            end = Instant.parse("2023-01-01T11:00:00Z")
                        ),
                        createBerthStop(
                            areaId = berth1,
                            start = Instant.parse("2023-01-01T12:00:00Z"),
                            end = Instant.parse("2023-01-01T13:00:00Z")
                        )
                    ),
                    berthActivities = listOf(
                        createBerthAreaActivity(
                            start = Instant.parse("2023-01-01T10:30:00Z"),
                            end = Instant.parse("2023-01-01T12:30:00Z"),
                            areaId = berth1
                        )
                    ),
                    expected = listOf(
                        createBerthVisit(
                            start = Instant.parse("2023-01-01T10:30:00Z"),
                            end = Instant.parse("2023-01-01T11:00:00Z"),
                            areaRef = berth1
                        ),
                        createBerthVisit(
                            ref = 1,
                            start = Instant.parse("2023-01-01T12:00:00Z"),
                            end = Instant.parse("2023-01-01T12:30:00Z"),
                            areaRef = berth1
                        )
                    )
                )
            ),
        )
    }

    @Test
    fun `departure tug should be populated by waiting for departure encounters`() {
        val berthVisit = createBerthStop(
            start = Instant.parse("2023-01-01T10:00:00Z"),
            end = Instant.parse("2023-01-01T12:00:00Z"),
            areaId = "BERTH1"
        )

        val tugEncounter = createTugEncounters(
            start = Instant.parse("2023-01-01T12:05:00Z"),
            end = Instant.parse("2023-01-01T12:15:00Z")
        )

        val tugWaitingForDepartureEncounter = createTugWaitingDeparture(
            start = Instant.parse("2023-01-01T12:05:00Z"),
            end = Instant.parse("2023-01-01T12:15:00Z")
        )

        val visit = createNewVisit(stops = listOf(berthVisit))
        val esof = createNewESoF(
            _id = "idea",
            encounters = listOf(tugEncounter, tugWaitingForDepartureEncounter)
        )

        val result = generator.generate(visit, esof)
        val departureTug = result.berthVisits.first().departureTugs.first()

        assertEquals(departureTug.start.time, tugWaitingForDepartureEncounter.start.time)
    }

    @Test
    fun `departure tug should NOT be populated by waiting for departure encounters when time difference exceeds limit`() {
        val berthVisit = createBerthStop(
            start = Instant.parse("2023-01-01T10:00:00Z"),
            end = Instant.parse("2023-01-01T12:00:00Z"),
            areaId = "BERTH1"
        )

        val tugEncounter = createTugEncounters(
            start = Instant.parse("2023-01-01T13:35:00Z"),
            end = Instant.parse("2023-01-01T13:45:00Z")
        )

        val tugWaitingForDepartureEncounter = createTugWaitingDeparture(
            start = Instant.parse("2023-01-01T12:05:00Z"),
            end = Instant.parse("2023-01-01T12:15:00Z")
        )

        val visit = createNewVisit(stops = listOf(berthVisit))
        val esof = createNewESoF(
            _id = "idea",
            encounters = listOf(tugEncounter, tugWaitingForDepartureEncounter)
        )

        val result = generator.generate(visit, esof)
        val departureTug = result.berthVisits.first().departureTugs.first()

        assertEquals(departureTug.start.time, tugEncounter.start.time)
    }

    private fun createBerthAreaActivity(start: Instant, end: Instant, areaId: String) = createAreaActivity(
        start = createLocationTime(time = start),
        end = createLocationTime(time = end),
        areaId = areaId
    )

    private fun createBerthStop(start: Instant, end: Instant, areaId: String) = createNewStop(
        type = BERTH,
        start = createLocationTime(time = start),
        end = createLocationTime(time = end),
        areaId = areaId
    )

    private fun createBerthVisit(ref: Int = 0, start: Instant, end: Instant, areaRef: String) = BerthVisit(
        ref = ref,
        terminalVisitRef = null,
        portAreaRef = null,
        start = createLocationTimeApi(time = start),
        end = createLocationTimeApi(time = end),
        firstLineSecured = null,
        allFast = null,
        lastLineReleased = null,
        area = AreaMeta(areaId = areaRef, unlocode = null, name = areaRef, type = "berth"),
        cargoCategoryType = null,
        mooringType = null,
        terminalId = null,
        arrivalTugs = emptyList(),
        departureTugs = emptyList()
    )

    private fun createTugEncounters(start: Instant, end: Instant) =
        createNewEncounter(start, end, EncounterType.TUG)

    private fun createTugWaitingDeparture(start: Instant, end: Instant) =
        createNewEncounter(start, end, EncounterType.TUG_WAITING_DEPARTURE)

    private fun createNewEncounter(start: Instant, end: Instant, type: EncounterType) = NewEncounter(
        type = type,
        otherMmsi = 3,
        otherImo = 4,
        startEventId = "startEventId",
        start = createLocationTime(time = start),
        end = createLocationTime(time = end)
    )
}
