package nl.teqplay.vesselvoyage.service

import com.nhaarman.mockitokotlin2.doReturn
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.reset
import com.nhaarman.mockitokotlin2.whenever
import kotlinx.coroutines.runBlocking
import nl.teqplay.platform.util.LocationUtils.createBoundingBox
import nl.teqplay.skeleton.model.Location
import nl.teqplay.vesselvoyage.client.poma.PomaClient
import nl.teqplay.vesselvoyage.datasource.TempFileDataSource
import nl.teqplay.vesselvoyage.logic.createAnchorage
import nl.teqplay.vesselvoyage.logic.createBerth
import nl.teqplay.vesselvoyage.logic.createLock
import nl.teqplay.vesselvoyage.logic.createPort
import nl.teqplay.vesselvoyage.model.internal.ClassifiedStop
import nl.teqplay.vesselvoyage.model.lightweight.poma.Anchorage
import nl.teqplay.vesselvoyage.model.lightweight.poma.Berth
import nl.teqplay.vesselvoyage.model.lightweight.poma.Port
import nl.teqplay.vesselvoyage.model.v2.NewStopType
import nl.teqplay.vesselvoyage.properties.DiskCacheProperties
import nl.teqplay.vesselvoyage.util.toSkeletonLocation
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream
import nl.teqplay.vesselvoyage.model.Location as OldLocation

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class InfraServiceTest {
    private lateinit var infraService: InfraService
    private val pomaClient: PomaClient = mock()
    private val slackMessageService: SlackMessageService = mock()
    private val tempFileDataSource: TempFileDataSource = mock()
    private val diskCacheProperties = DiskCacheProperties(
        enabled = true,
        csiShipsFile = "vesselVoyageCSIShipsTest.json",
        csiShipRegistryFile = "vesselVoyageCSIShipsRegistryTest.json"
    )
    private val infraCacheService: InfraCacheService = InfraCacheService(
        diskCacheProperties = diskCacheProperties,
        tempFileDataSource = tempFileDataSource,
        pomaClient = pomaClient,
        slackMessageService = null,
    )

    private val testMainPortId = "TEST_MAIN_PORT_ID"
    private val testMainPortUnlocode = "AABBB"
    private val testSubPortId1 = "TEST_SUB_PORT_ID_1"
    private val testSubPortUnlocode1 = "CCDDD"
    private val testSubPortId2 = "TEST_SUB_PORT_ID_2"
    private val testSubPortUnlocode2 = "EEFFF"
    private val testMainPort = createPort(
        _id = testMainPortId,
        unlocode = testMainPortUnlocode
    )
    private val testSubPortWithMainId = createPort(
        _id = testSubPortId1,
        unlocode = testSubPortUnlocode1,
        mainPortId = testMainPortId
    )
    private val testSubPortWithMainUnlocode = createPort(
        _id = testSubPortId2,
        unlocode = testSubPortUnlocode2,
        mainPortId = testMainPortUnlocode
    )

    private val testBerth = createBerth(area = createBoundingBox(0.0, 0.0, 1.0, 1.0).toList())
    private val testAnchorage = createAnchorage(area = createBoundingBox(1.0, 1.0, 2.0, 2.0).map { it.toSkeletonLocation() })
    private val testLock = createLock(area = createBoundingBox(2.0, 2.0, 3.0, 3.0).map { it.toSkeletonLocation() })

    @BeforeEach
    internal fun setUp() {
        reset(pomaClient, slackMessageService, tempFileDataSource)
    }

    @Test
    fun `should find the right anchorage in case of ambiguous names`() {
        val anchorages = listOf(ANCHORAGE_5_A, ANCHORAGE_5_B)
        doReturn(listOf<Port>()).whenever(pomaClient).getAllPorts()
        doReturn(anchorages).whenever(pomaClient).getAllAnchorages()

        infraService = InfraService(infraCacheService).also {
            runBlocking { it.onStartup() }
        }

        val locationNear5a = OldLocation(6.0, 6.0)
        assertEquals(ANCHORAGE_5_A, infraService.getAnchorage("5", locationNear5a))

        val locationNear5b = OldLocation(50.0, 52.0)
        assertEquals(ANCHORAGE_5_B, infraService.getAnchorage("5", locationNear5b))
    }

    @Test
    fun `add all subports of the mainports`() {
        doReturn(listOf(NLRTM, NLVLA, BEANR, USBAY, USNYC)).whenever(pomaClient).getAllPorts()
        doReturn(listOf<Anchorage>()).whenever(pomaClient).getAllAnchorages()

        infraService = InfraService(infraCacheService).also {
            runBlocking { it.onStartup() }
        }
        val result = infraService.addAllSubPorts(setOf(NLRTM.unlocode!!, BEANR.unlocode!!, USBAY.unlocode!!))
        assertEquals(4, result.size)
        assertTrue(NLVLA.unlocode!! in result)
        assertTrue(USBAY.unlocode!! in result)
        assertTrue(NLRTM.unlocode!! in result)
        assertTrue(BEANR.unlocode!! in result)
        assertTrue(USNYC.unlocode!! !in result)
    }

    private fun testMainPortBerths() = Stream.of(
        Arguments.of(createBerth(mainPort = testMainPortUnlocode)),
        Arguments.of(createBerth(mainPort = testMainPortId)),
        Arguments.of(createBerth(ports = listOf(testMainPortUnlocode))),
        Arguments.of(createBerth(ports = listOf(testMainPortId))),
        Arguments.of(createBerth(ports = listOf(testSubPortUnlocode1, testSubPortUnlocode2, testMainPortUnlocode))),
        Arguments.of(createBerth(ports = listOf(testSubPortId1, testSubPortId2, testMainPortId))),
    )

    @ParameterizedTest
    @MethodSource("testMainPortBerths")
    fun `should find main port from berth`(berth: Berth) {
        whenever(pomaClient.getAllPorts()).thenReturn(listOf(testMainPort, testSubPortWithMainId, testSubPortWithMainId))
        whenever(pomaClient.getAllBerths()).thenReturn(listOf(berth))

        infraService = InfraService(infraCacheService).also {
            runBlocking { it.onStartup() }
        }

        val result = infraService.getMainPortFromBerth(berth)
        assertEquals(testMainPort, result)
    }

    private fun testClassifiedStops() = Stream.of(
        Arguments.of(Location(0.01, 0.01), ClassifiedStop(type = NewStopType.BERTH, areaId = testBerth._id)),
        Arguments.of(Location(1.1, 1.1), ClassifiedStop(type = NewStopType.ANCHOR_AREA, areaId = testAnchorage._id)),
        Arguments.of(Location(2.01, 2.01), ClassifiedStop(type = NewStopType.LOCK, areaId = testLock._id)),
        Arguments.of(Location(3.01, 3.01), ClassifiedStop(type = NewStopType.UNCLASSIFIED, areaId = null))
    )

    @ParameterizedTest
    @MethodSource("testClassifiedStops")
    fun `should resolve stop classification`(stopLocation: Location, expected: ClassifiedStop) {
        whenever(pomaClient.getAllBerths()).thenReturn(listOf(testBerth))
        whenever(pomaClient.getAllAnchorages()).thenReturn(listOf(testAnchorage))
        whenever(pomaClient.getAllLocks()).thenReturn(listOf(testLock))
        infraService = InfraService(infraCacheService).also {
            runBlocking { it.onStartup() }
        }

        val result = infraService.findStopClassification(stopLocation)
        assertEquals(expected, result)
    }
}

private val LOCATION_5_A = Location(5.0, 5.0)
private val LOCATION_5_B = Location(55.0, 55.0)
private val LOCATION_NLRTM = Location(52.0, 4.0)
private val LOCATION_NLVLA = Location(52.5, 4.0)
private val LOCATION_BEANR = Location(50.5, 4.0)
private val LOCATION_USBAY = Location(40.66, -74.10)
private val LOCATION_USNYC = Location(40.67, -74.04)

private val ANCHORAGE_5_A = Anchorage(
    name = "5",
    ports = listOf(),
    location = LOCATION_5_A,
    area = emptyList(),
    _id = "5_A",
)

private val ANCHORAGE_5_B = Anchorage(
    name = "5",
    ports = listOf(),
    location = LOCATION_5_B,
    area = emptyList(),
    _id = "5_B",
)

private val NLRTM = Port(
    name = "Rotterdam",
    unlocode = "NLRTM",
    location = LOCATION_NLRTM,
    _id = "NLRTMID",
    mainPort = null,
    eosArea = null
)

private val NLVLA = Port(
    name = "Vlaardingen",
    unlocode = "NLVLA",
    location = LOCATION_NLVLA,
    _id = "NLVLAID",
    mainPort = NLRTM.unlocode,
    eosArea = null
)

private val BEANR = Port(
    name = "Antwerp",
    unlocode = "BEANR",
    location = LOCATION_BEANR,
    _id = "BEANRID",
    mainPort = null,
    eosArea = null
)

private val USNYC = Port(
    name = "New York",
    unlocode = "USNYC",
    location = LOCATION_USNYC,
    _id = "USNYCID",
    mainPort = null,
    eosArea = null
)

private val USBAY = Port(
    name = "Bayonne",
    unlocode = "USBAY",
    location = LOCATION_USBAY,
    _id = "USBAYID",
    mainPort = USNYC.unlocode,
    eosArea = null
)
