package nl.teqplay.vesselvoyage.service.merge

import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.whenever
import nl.teqplay.skeleton.model.TimeWindow
import nl.teqplay.vesselvoyage.apiv2.model.Stop
import nl.teqplay.vesselvoyage.logic.PORT_NLRTM
import nl.teqplay.vesselvoyage.logic.TestPort
import nl.teqplay.vesselvoyage.logic.createEncounter
import nl.teqplay.vesselvoyage.logic.createPortAreaVisit
import nl.teqplay.vesselvoyage.logic.createStop
import nl.teqplay.vesselvoyage.logic.createVisit
import nl.teqplay.vesselvoyage.logic.createVoyage
import nl.teqplay.vesselvoyage.model.ESof
import nl.teqplay.vesselvoyage.model.Encounter
import nl.teqplay.vesselvoyage.model.Entry
import nl.teqplay.vesselvoyage.model.Location
import nl.teqplay.vesselvoyage.model.PortAreaVisit
import nl.teqplay.vesselvoyage.model.StopType
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.model.Voyage
import nl.teqplay.vesselvoyage.service.EntryService
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import java.time.Instant
import java.time.ZonedDateTime

private val entryService = mock<EntryService>()
private val entriesMergeService = EntriesMergeV1Service(entryService)

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class EntriesMergeV1ServiceTest : EntriesMergeBaseServiceTest<Visit, Voyage, Entry>(entriesMergeService) {

    override fun Entry.toTestModel(): TestEntry = when (this) {
        is Visit -> TestVisit(
            _id = this._id,
            previousEntryId = this.previousEntryId,
            nextEntryId = this.nextEntryId,
            portAreas = this.portAreas.map {
                TestVisit.PortArea(
                    portId = it.portId,
                    startTime = it.startTime,
                    endTime = it.endTime
                )
            },
            finished = this.finished,
            regenerated = this.regenerated
        )

        is Voyage -> TestVoyage(
            _id = this._id,
            previousEntryId = this.previousEntryId,
            nextEntryId = this.nextEntryId,
            finished = this.finished,
            startTime = this.startTime,
            startPortIds = this.startPortIds,
            endTime = this.endTime,
            endPortIds = this.endPortIds,
            regenerated = this.regenerated
        )
    }

    override fun TestEntry.fromTestModel(): Entry = when (this) {
        is TestVisit -> createVisit(
            _id = this._id,
            previousEntryId = this.previousEntryId,
            nextEntryId = this.nextEntryId,
            finished = this.finished,
            portAreas = this.portAreas.map {
                PortAreaVisit(
                    portId = it.portId,
                    startEventId = "startEventId",
                    startTime = it.startTime,
                    startLocation = Location(0.0, 0.0),
                    startDraught = null,
                    endEventId = "endEventId",
                    endTime = it.endTime,
                    endLocation = null,
                    endDraught = null
                )
            }
        ).copy(regenerated = this.regenerated)

        is TestVoyage -> createVoyage(
            _id = this._id,
            previousEntryId = this.previousEntryId,
            nextEntryId = this.nextEntryId,
            finished = this.finished,
            startTime = this.startTime,
            startPortIds = this.startPortIds,
            endTime = this.endTime,
            endPortIds = this.endPortIds
        ).copy(regenerated = this.regenerated)
    }

    override fun wheneverEntryServiceContainingEntries(entries: List<Entry>) {
        whenever(entryService.findEntriesByIMO(any(), any(), any(), any())).thenReturn(entries)
        whenever(entryService.findEntry(any())).thenAnswer {
            val entryId = it.getArgument<String>(0)
            entries.find { entry -> entry._id == entryId }
        }
    }

    private fun createPortAreas(
        port: TestPort,
        startTime: ZonedDateTime,
        endTime: ZonedDateTime,
    ): List<PortAreaVisit> = listOf(
        createPortAreaVisit(port = port, startTime = startTime, endTime = endTime)
            .copy(startEventId = "startEventId", endEventId = "endEventId")
    )

    private val encounterLeft = createEncounter("1970-01-02T00:00:00Z", "1970-01-03T00:00:00Z")
    private val encounterRight = createEncounter("1970-01-04T00:00:00Z", "1970-01-05T00:00:00Z")
    private val stopLeft = createStop(StopType.ANCHOR_AREA, "1970-01-02T00:00:00Z", "1970-01-03T00:00:00Z")
    private val stopRight = createStop(StopType.ANCHOR_AREA, "1970-01-04T00:00:00Z", "1970-01-05T00:00:00Z")

    @Test
    fun `merge - should split ESOF entries in overlapping voyages`() {
        val oldVoyage = createVoyage(
            startPortIds = emptyList(),
            startTime = ZonedDateTime.parse("1970-01-01T00:00:00Z"),
            endTime = ZonedDateTime.parse("1970-01-08T00:00:00Z"),
            finished = true,
            esof = ESof(
                encounters = listOf(encounterLeft, encounterRight),
                stops = listOf(stopLeft, stopRight),
                slowMovingPeriods = null
            )
        )
        val newVisit = createVisit(
            portAreas = createPortAreas(
                port = PORT_NLRTM,
                startTime = ZonedDateTime.parse("1970-01-03T00:00:00Z"),
                endTime = ZonedDateTime.parse("1970-01-04T00:00:00Z"),
            ),
            finished = true
        )

        val window = TimeWindow(
            from = oldVoyage.startTime.toInstant(),
            to = oldVoyage.endTime!!.toInstant()
        )
        wheneverEntryServiceContainingEntries(listOf(oldVoyage))
        val res = entriesMergeService.merge(imo, window, listOf(newVisit))
        assertEquals(3, res.entries.size)

        val startVoyage = res.entries[0]
        val endVoyage = res.entries[2]

        // Assert ESOF encounters are split over both voyages
        assertEquals(listOf(encounterLeft), startVoyage.esof?.encounters)
        assertEquals(listOf(encounterRight), endVoyage.esof?.encounters)

        // Assert ESOF stops are split over both voyages
        assertEquals(listOf(stopLeft), startVoyage.esof?.stops)
        assertEquals(listOf(stopRight), endVoyage.esof?.stops)
    }

    @Test
    fun `merge - should remove ESOF entries if they don't overlap with voyages`() {
        val oldVoyage = createVoyage(
            startPortIds = emptyList(),
            startTime = ZonedDateTime.parse("1970-01-01T00:00:00Z"),
            endTime = ZonedDateTime.parse("1970-01-08T00:00:00Z"),
            finished = true,
            esof = ESof(
                encounters = listOf(encounterLeft, encounterRight),
                stops = listOf(stopLeft, stopRight),
                slowMovingPeriods = null
            )
        )
        val newVisit = createVisit(
            portAreas = createPortAreas(
                port = PORT_NLRTM,
                startTime = ZonedDateTime.parse("1970-01-01T23:00:00Z"),
                endTime = ZonedDateTime.parse("1970-01-05T01:00:00Z"),
            ),
            finished = true
        )

        val window = TimeWindow(
            from = oldVoyage.startTime.toInstant(),
            to = oldVoyage.endTime!!.toInstant()
        )
        wheneverEntryServiceContainingEntries(listOf(oldVoyage))
        val res = entriesMergeService.merge(imo, window, listOf(newVisit))
        assertEquals(3, res.entries.size)

        val startVoyage = res.entries[0]
        val endVoyage = res.entries[2]

        // Assert ESOF encounters are removed, since they don't overlap with the voyages
        assertEquals(emptyList<Encounter>(), startVoyage.esof?.encounters)
        assertEquals(emptyList<Encounter>(), endVoyage.esof?.encounters)

        // Assert ESOF stops are removed, since they don't overlap with the voyages
        assertEquals(emptyList<Stop>(), startVoyage.esof?.stops)
        assertEquals(emptyList<Stop>(), endVoyage.esof?.stops)
    }

    @Test
    fun `merge - should finish a voyage if it gets split`() {
        val oldVoyage = createVoyage(
            startPortIds = emptyList(),
            startTime = ZonedDateTime.parse("1970-01-01T00:00:00Z"),
            endTime = null,
            finished = false,
            esof = ESof(
                encounters = listOf(encounterLeft, encounterRight),
                stops = listOf(stopLeft, stopRight),
                slowMovingPeriods = null
            )
        )
        val newVisit = createVisit(
            portAreas = createPortAreas(
                port = PORT_NLRTM,
                startTime = ZonedDateTime.parse("1970-01-03T00:00:00Z"),
                endTime = ZonedDateTime.parse("1970-01-04T00:00:00Z"),
            ),
            finished = true
        )

        val window = TimeWindow(
            from = Instant.parse("1970-01-01T00:00:00Z"),
            to = Instant.parse("1970-01-08T00:00:00Z")
        )
        wheneverEntryServiceContainingEntries(listOf(oldVoyage))
        val res = entriesMergeService.merge(imo, window, listOf(newVisit))
        assertEquals(3, res.entries.size)

        assertTrue(res.entries[0].finished) // first voyage should be finished
        assertTrue(res.entries[1].finished) // visit should be finished, since it's based on new data
        assertFalse(res.entries[2].finished) // voyage, although split, should remain unfinished
    }
}
