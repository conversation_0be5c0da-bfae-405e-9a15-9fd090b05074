package nl.teqplay.vesselvoyage.service.merge

import com.nhaarman.mockitokotlin2.mock
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType
import nl.teqplay.skeleton.model.Location
import nl.teqplay.vesselvoyage.logic.IMO_1
import nl.teqplay.vesselvoyage.logic.MMSI_1
import nl.teqplay.vesselvoyage.logic.PORT_NLRTM
import nl.teqplay.vesselvoyage.logic.createEncounterEvent
import nl.teqplay.vesselvoyage.model.EventStatus
import nl.teqplay.vesselvoyage.model.v2.NewESoF
import nl.teqplay.vesselvoyage.model.v2.NewEncounter
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import nl.teqplay.vesselvoyage.service.processing.encounter.EncounterProcessor
import nl.teqplay.vesselvoyage.util.createLocationTime
import nl.teqplay.vesselvoyage.util.createNewESoF
import nl.teqplay.vesselvoyage.util.toSkeletonLocation
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.time.Instant
import java.time.ZoneOffset
import java.util.UUID
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ESoFMergeV2ServiceTest {

    private val encounterEventProcessor = EncounterProcessor(mock(), SimpleMeterRegistry())
    private val eSoFMergeV2Service = ESoFMergeV2Service(encounterEventProcessor)

    data class EncountersTestData(
        val description: String,
        val entries: List<NewEntry>,
        val encounters: List<EncounterEvent>,
        val expectedESoFs: List<NewESoF>,
        val error: Boolean = false,
    )

    private val time1 = Instant.EPOCH
    private val time2 = time1.plusSeconds(30)
    private val time3 = time2.plusSeconds(30)
    private val time4 = time3.plusSeconds(30)

    private fun createEmptyESoFsWithEncountersTestData() = Stream.of(
        run {
            EncountersTestData(
                description = "empty entries",
                entries = emptyList(),
                encounters = listOf(mock()),
                expectedESoFs = emptyList()
            )
        },
        run {
            EncountersTestData(
                description = "empty encounters",
                entries = listOf(mock<NewVisit>()),
                encounters = emptyList(),
                expectedESoFs = emptyList()
            )
        },
        run {
            val voyage = createVoyage(time1, time2)
            EncountersTestData(
                description = "invalid: voyages not interleaved",
                error = true,
                entries = listOf(voyage, voyage),
                encounters = listOf(mock()),
                expectedESoFs = emptyList()
            )
        },
        run {
            val visit = createVisit(time1, time2)
            EncountersTestData(
                description = "invalid: visits not interleaved",
                error = true,
                entries = listOf(visit, visit),
                encounters = listOf(mock()),
                expectedESoFs = emptyList()
            )
        },
        run {
            val (encounterStartEvent, encounterEndEvent) = createEncounterPair(
                type = EncounterType.TUG,
                start = time1,
                end = time2
            )
            EncountersTestData(
                description = "invalid: encounters not ordered",
                error = true,
                entries = listOf(mock<NewVisit>()),
                encounters = listOf(encounterEndEvent, encounterStartEvent),
                expectedESoFs = emptyList()
            )
        },
        run {
            val voyage = createVoyage(time1, time2)
            val (encounterStartEvent, encounterEndEvent) = createEncounterPair(
                type = EncounterType.TUG,
                start = time1,
                end = time2
            )
            EncountersTestData(
                description = "starts with voyage",
                entries = listOf(voyage),
                encounters = listOf(encounterStartEvent, encounterEndEvent),
                expectedESoFs = listOf(
                    createNewESoF(
                        _id = voyage._id,
                        encounters = listOf(
                            createEncounter(startEvent = encounterStartEvent, end = time2)
                        )
                    )
                )
            )
        },
        run {
            val voyage = createVoyage(time1, time2)
            val visit = createVisit(time2, time3)
            val (encounterStartEvent, encounterEndEvent) = createEncounterPair(
                type = EncounterType.TUG,
                start = time1,
                end = time2
            )
            EncountersTestData(
                description = "only voyage gets ESoF",
                entries = listOf(voyage, visit),
                encounters = listOf(encounterStartEvent, encounterEndEvent),
                expectedESoFs = listOf(
                    createNewESoF(
                        _id = voyage._id,
                        encounters = listOf(
                            createEncounter(
                                startEvent = encounterStartEvent,
                                end = time2,
                                endLocation = Location(0.0, 0.0)
                            )
                        )
                    )
                )
            )
        },
        run {
            val visit = createVisit(time1, time2)
            val voyage = createVoyage(time2, time3)
            val (encounterStartEvent, encounterEndEvent) = createEncounterPair(
                type = EncounterType.TUG,
                start = time1,
                end = time2
            )
            EncountersTestData(
                description = "only visit gets ESoF",
                entries = listOf(visit, voyage),
                encounters = listOf(encounterStartEvent, encounterEndEvent),
                expectedESoFs = listOf(
                    createNewESoF(
                        _id = visit._id,
                        encounters = listOf(
                            createEncounter(
                                startEvent = encounterStartEvent,
                                end = time2,
                                endLocation = Location(0.0, 0.0)
                            )
                        )
                    )
                )
            )
        },
        run {
            val voyage = createVoyage(time1, time2)
            val visit = createVisit(time2, time3)
            val (encounterStartEvent, encounterEndEvent) = createEncounterPair(
                type = EncounterType.TUG,
                start = time1,
                end = time3
            )
            EncountersTestData(
                description = "encounter continues from voyage into visit",
                entries = listOf(voyage, visit),
                encounters = listOf(encounterStartEvent, encounterEndEvent),
                expectedESoFs = listOf(
                    createNewESoF(
                        _id = voyage._id,
                        encounters = listOf(
                            createEncounter(
                                startEvent = encounterStartEvent,
                                start = time1,
                                end = time2,
                                endLocation = Location(0.0, 0.0)
                            )
                        )
                    ),
                    createNewESoF(
                        _id = visit._id,
                        encounters = listOf(
                            createEncounter(
                                startEvent = encounterStartEvent,
                                start = time2,
                                startLocation = Location(0.0, 0.0),
                                end = time3
                            )
                        )
                    )
                )
            )
        },
        run {
            val visit = createVisit(time1, time2)
            val voyage = createVoyage(time2, time3)
            val (encounterStartEvent, encounterEndEvent) = createEncounterPair(
                type = EncounterType.TUG,
                start = time1,
                end = time3
            )
            EncountersTestData(
                description = "encounter continues from visit into voyage",
                entries = listOf(visit, voyage),
                encounters = listOf(encounterStartEvent, encounterEndEvent),
                expectedESoFs = listOf(
                    createNewESoF(
                        _id = visit._id,
                        encounters = listOf(
                            createEncounter(
                                startEvent = encounterStartEvent,
                                end = time2,
                                endLocation = Location(0.0, 0.0)
                            )
                        )
                    ),
                    createNewESoF(
                        _id = voyage._id,
                        encounters = listOf(
                            createEncounter(
                                startEvent = encounterStartEvent,
                                start = time2,
                                startLocation = Location(0.0, 0.0),
                                end = time3
                            )
                        )
                    )
                )
            )
        },
        run {
            val voyage1 = createVoyage(time1, time2)
            val visit = createVisit(time2, time3)
            val voyage2 = createVoyage(time3, time4)
            val (encounterStartEvent, encounterEndEvent) = createEncounterPair(
                type = EncounterType.TUG,
                start = time1,
                end = time4
            )
            EncountersTestData(
                description = "encounter continues from voyage to voyage, with a visit within",
                entries = listOf(voyage1, visit, voyage2),
                encounters = listOf(encounterStartEvent, encounterEndEvent),
                expectedESoFs = listOf(
                    createNewESoF(
                        _id = voyage1._id,
                        encounters = listOf(
                            createEncounter(
                                startEvent = encounterStartEvent,
                                end = time2,
                                endLocation = Location(0.0, 0.0)
                            )
                        )
                    ),
                    createNewESoF(
                        _id = visit._id,
                        encounters = listOf(
                            createEncounter(
                                startEvent = encounterStartEvent,
                                start = time2,
                                startLocation = Location(0.0, 0.0),
                                end = time3,
                                endLocation = Location(0.0, 0.0),
                            )
                        )
                    ),
                    createNewESoF(
                        _id = voyage2._id,
                        encounters = listOf(
                            createEncounter(
                                startEvent = encounterStartEvent,
                                start = time3,
                                startLocation = Location(0.0, 0.0),
                                end = time4
                            )
                        )
                    )
                )
            )
        },
        run {
            val visit1 = createVisit(time1, time2)
            val voyage = createVoyage(time2, time3)
            val visit2 = createVisit(time3, time4)
            val (encounterStartEvent, encounterEndEvent) = createEncounterPair(
                type = EncounterType.TUG,
                start = time1,
                end = time4
            )
            EncountersTestData(
                description = "encounter continues from visit to visit, with a voyage within",
                entries = listOf(visit1, voyage, visit2),
                encounters = listOf(encounterStartEvent, encounterEndEvent),
                expectedESoFs = listOf(
                    createNewESoF(
                        _id = visit1._id,
                        encounters = listOf(
                            createEncounter(
                                startEvent = encounterStartEvent,
                                end = time2,
                                endLocation = Location(0.0, 0.0)
                            )
                        )
                    ),
                    createNewESoF(
                        _id = voyage._id,
                        encounters = listOf(
                            createEncounter(
                                startEvent = encounterStartEvent,
                                start = time2,
                                startLocation = Location(0.0, 0.0),
                                end = time3,
                                endLocation = Location(0.0, 0.0),
                            )
                        )
                    ),
                    createNewESoF(
                        _id = visit2._id,
                        encounters = listOf(
                            createEncounter(
                                startEvent = encounterStartEvent,
                                start = time3,
                                startLocation = Location(0.0, 0.0),
                                end = time4
                            )
                        )
                    )
                )
            )
        },
    )

    @ParameterizedTest
    @MethodSource("createEmptyESoFsWithEncountersTestData")
    fun createEmptyESoFsWithEncounters(data: EncountersTestData) {
        if (data.error) {
            assertThrows<IllegalArgumentException> {
                eSoFMergeV2Service.createEmptyESoFsWithEncounters(data.entries, data.encounters)
            }
            return
        }
        val result = eSoFMergeV2Service.createEmptyESoFsWithEncounters(data.entries, data.encounters)
        assertThat(result)
            .usingRecursiveFieldByFieldElementComparatorIgnoringFields("updatedAt")
            .isEqualTo(data.expectedESoFs)
    }

    private fun createEncounterPair(
        type: EncounterType,
        start: Instant,
        end: Instant,
    ): Pair<EncounterEvent, EncounterEvent> {
        val startEvent = createEncounterStartEvent(
            eventId = UUID.randomUUID().toString(),
            type = type,
            time = start
        )
        val endEvent = createEncounterEndEvent(
            eventId = UUID.randomUUID().toString(),
            relatedEvent = startEvent._id,
            type = type,
            time = end
        )
        return startEvent to endEvent
    }

    private fun createEncounterStartEvent(
        eventId: String,
        type: EncounterType,
        time: Instant
    ): EncounterEvent {
        return createEncounterEvent(
            eventId = eventId,
            type = type,
            status = EventStatus.START,
            otherMmsi = MMSI_1.toString(),
            otherImo = IMO_1.toString(),
            time = time.atZone(ZoneOffset.UTC)
        )
    }

    private fun createEncounterEndEvent(
        eventId: String,
        relatedEvent: String,
        type: EncounterType,
        time: Instant
    ): EncounterEvent {
        return createEncounterEvent(
            eventId = eventId,
            relatedEvent = relatedEvent,
            type = type,
            status = EventStatus.END,
            otherMmsi = MMSI_1.toString(),
            otherImo = IMO_1.toString(),
            time = time.atZone(ZoneOffset.UTC)
        )
    }

    private fun createVoyage(
        start: Instant,
        end: Instant
    ): NewVoyage {
        return NewVoyage(
            _id = UUID.randomUUID().toString(),
            imo = IMO_1.toInt(),
            start = createLocationTime(time = start),
            end = createLocationTime(time = end),
            stops = emptyList(),
            destination = null,
            previous = null,
            next = null,
            actualStart = null,
            originPort = null,
            destinationPort = null
        )
    }

    private fun createVisit(
        start: Instant,
        end: Instant
    ): NewVisit {
        return NewVisit(
            _id = UUID.randomUUID().toString(),
            imo = IMO_1.toInt(),
            start = createLocationTime(time = start),
            end = createLocationTime(time = end),
            stops = emptyList(),
            destination = null,
            previous = null,
            next = null,
            eospAreaActivity = mock(),
        )
    }

    private fun createEncounter(
        startEvent: EncounterEvent,
        start: Instant = startEvent.actualTime,
        end: Instant,

        startLocation: Location? = null,
        endLocation: Location? = null,
    ): NewEncounter {
        return NewEncounter(
            type = startEvent.encounterType,
            otherMmsi = MMSI_1.toInt(),
            otherImo = IMO_1.toInt(),
            startEventId = startEvent._id,
            start = createLocationTime(
                location = startLocation ?: PORT_NLRTM.location.toSkeletonLocation(),
                time = start
            ),
            end = createLocationTime(
                location = endLocation ?: PORT_NLRTM.location.toSkeletonLocation(),
                time = end
            )
        )
    }
}
