package nl.teqplay.vesselvoyage.service.merge

import nl.teqplay.skeleton.model.TimeWindow
import nl.teqplay.vesselvoyage.logic.BEANR
import nl.teqplay.vesselvoyage.logic.NLRTM
import nl.teqplay.vesselvoyage.logic.PORT_BEANR
import nl.teqplay.vesselvoyage.logic.PORT_NLRTM
import nl.teqplay.vesselvoyage.logic.TestPort
import nl.teqplay.vesselvoyage.model.Location
import nl.teqplay.vesselvoyage.service.merge.EntriesMergeService.MergeResult
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.time.Duration
import java.time.Instant
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
abstract class EntriesMergeBaseServiceTest<Visit : Entry, Voyage : Entry, Entry : Any>(
    private val entriesMergeService: EntriesMergeService<Visit, Voyage, Entry>
) {

    protected val imoInt = 0
    protected val imo = imoInt.toString()

    companion object {
        private val NEW_PORT = TestPort("NEW_PORT", Location(0.0, 0.0))
        private val OLD_PORT = TestPort("OLD_PORT", Location(0.0, 0.0))

        /**
         * A marker used to indicate we expected a new identifier to be generated.
         * Since the generated identifier is random, this marker is used to update that
         * identifier to this value. Making it possible to compare against.
         */
        const val CREATES_NEW_ID = "CREATES_NEW_ID"
    }

    private fun at(duration: Duration) = Instant.EPOCH.plus(duration).atZone(ZoneOffset.UTC)

    abstract fun Entry.toTestModel(): TestEntry
    abstract fun TestEntry.fromTestModel(): Entry
    abstract fun wheneverEntryServiceContainingEntries(entries: List<Entry>)

    private fun MergeResult<Entry>.toTestModel(): MergeResult<TestEntry> = MergeResult(
        entries = this.entries.map { it.toTestModel() },
        deleteVisitIds = deleteVisitIds,
        deleteVoyageIds = deleteVoyageIds
    )

    sealed interface TestEntry {
        val _id: String
        val previousEntryId: String?
        val nextEntryId: String?
        val finished: Boolean
        val startTime: ZonedDateTime
        val endTime: ZonedDateTime?
    }

    data class TestVisit(
        override val _id: String,
        override val previousEntryId: String? = null,
        override val nextEntryId: String? = null,
        val portAreas: List<PortArea>,
        override val finished: Boolean,

        val regenerated: Boolean = false
    ) : TestEntry {

        override val startTime: ZonedDateTime
            get() = portAreas.minOf { it.startTime }

        override val endTime: ZonedDateTime?
            get() = if (finished) portAreas.mapNotNull { it.endTime }.maxOrNull() else null

        data class PortArea(
            val portId: String,
            val startTime: ZonedDateTime,
            val endTime: ZonedDateTime? = null
        )
    }

    data class TestVoyage(
        override val _id: String,
        override val previousEntryId: String? = null,
        override val nextEntryId: String? = null,
        override val startTime: ZonedDateTime,
        val startPortIds: List<String>,
        override val endTime: ZonedDateTime? = null,
        val endPortIds: List<String>? = null,
        override val finished: Boolean,

        val regenerated: Boolean = false
    ) : TestEntry

    data class MergeTestData(
        /**
         * Description of the test.
         */
        val description: String,

        /**
         * An encoded string that is parsed by [parseEntries].
         * Must be the same length as [new].
         */
        val old: String,

        /**
         * An encoded string that is parsed by [parseEntries].
         * Must be the same length as [old].
         */
        val new: String,

        /**
         * The expectations, which visits/voyages should be deleted and
         * which entries are expected to result from merging.
         */
        val expectedDeleteVisitIds: List<String> = emptyList(),
        val expectedDeleteVoyageIds: List<String> = emptyList(),
        val expectedEntries: List<TestEntry> = emptyList(),
        val expectedEntriesAfterMerge: List<TestEntry> = expectedEntries,

        /**
         * Whether merging should not be performed and is expected to error out.
         */
        val error: Boolean = false,
    )

    @ParameterizedTest
    @MethodSource("mergeTestData")
    fun merge(data: MergeTestData) {
        // Before parsing, check that both streams are the same length.
        assertThat(data.old.length).isEqualTo(data.new.length)

        val oldEntries = parseEntries(data.old, old = true)
        val newEntries = parseEntries(data.new, old = false)
        val window = TimeWindow(
            from = at(Duration.ZERO).toInstant(),
            to = at(Duration.ofDays(data.old.length.toLong())).toInstant()
        )

        val convertedOldEntries = oldEntries.map { it.fromTestModel() }
        val convertedNewEntries = newEntries.map { it.fromTestModel() }
        wheneverEntryServiceContainingEntries(convertedOldEntries)

        if (data.error) {
            assertThrows<IllegalArgumentException> {
                entriesMergeService.merge(imo, window, convertedNewEntries)
            }
            return
        }

        val actual = entriesMergeService.merge(imo, window, convertedNewEntries).toTestModel()

        val expectedEntries = data.expectedEntries.applyExpectations()
        val expectedEntriesAfterMerge = data.expectedEntriesAfterMerge.applyExpectations()

        // Actual should be the same size as expected. If not, we can't zip them below.
        if (actual.entries.size != expectedEntries.size) {
            assertThat(actual.entries).isEqualTo(expectedEntries)
            return
        }

        val correctedActual = zipActualWithExpectedToReplaceRandomIds(actual, expectedEntries)
        assertThat(correctedActual).isEqualTo(expectedEntries)
        assertThat(actual.deleteVisitIds).isEqualTo(data.expectedDeleteVisitIds)
        assertThat(actual.deleteVoyageIds).isEqualTo(data.expectedDeleteVoyageIds)

        // Merging should be idempotent. Merging again should result in no changes (only updates).
        // First apply the merging results to the old entries, then attempt merging again on the result after merge.
        val afterMergingMap = oldEntries.associateBy { it._id }.toMutableMap()
        actual.deleteVisitIds.forEach { deleteVisitId -> afterMergingMap.remove(deleteVisitId) }
        actual.deleteVoyageIds.forEach { deleteVoyageId -> afterMergingMap.remove(deleteVoyageId) }
        correctedActual.forEach { entry -> afterMergingMap[entry._id] = entry }

        val afterMergingEntries = afterMergingMap.values.sortedBy { it.startTime }
        val convertedAfterMergingEntries = afterMergingEntries.map { it.fromTestModel() }
        wheneverEntryServiceContainingEntries(convertedAfterMergingEntries)
        val actual2 = entriesMergeService.merge(imo, window, convertedNewEntries).toTestModel()
        assertThat(actual2).isEqualTo(MergeResult(entries = expectedEntriesAfterMerge))
    }

    private fun List<TestEntry>.applyExpectations(): List<TestEntry> = map { entry ->
        // Expect all entries to have the regenerated flag set.
        when (entry) {
            is TestVisit -> entry.copy(regenerated = true)
            is TestVoyage -> entry.copy(regenerated = true)
        }
    }

    private fun zipActualWithExpectedToReplaceRandomIds(
        actual: MergeResult<TestEntry>,
        expectedEntries: List<TestEntry>
    ): List<TestEntry> = actual.entries.zip(expectedEntries).map { (actualEntry, expectedEntry) ->
        val _id = actualEntry.toEntryId(expectedEntry) { it._id } ?: "NO_ENTRY_ID_FOUND"
        val previousEntryId = actualEntry.toEntryId(expectedEntry) { it.previousEntryId }
        val nextEntryId = actualEntry.toEntryId(expectedEntry) { it.nextEntryId }

        when (actualEntry) {
            is TestVisit -> actualEntry.copy(_id = _id, previousEntryId = previousEntryId, nextEntryId = nextEntryId)
            is TestVoyage -> actualEntry.copy(_id = _id, previousEntryId = previousEntryId, nextEntryId = nextEntryId)
        }
    }

    private fun TestEntry?.toEntryId(
        expectedEntry: TestEntry,
        getId: (TestEntry) -> String?
    ): String? {
        if (this == null || getId(expectedEntry) == CREATES_NEW_ID) {
            return CREATES_NEW_ID
        }
        return getId(this)
    }

    /**
     * Parses a [line] of visits/voyages, consisting of tokens.
     *
     * Tokens:
     * - '|' => visit
     * - '-' => voyage
     * - ' ' => empty/space (only to be used at the start or end, not in-between)
     *
     * Repeating the same token increases the duration of the visit/voyage.
     *
     * For example: `---|--`
     *
     * Indicates:
     * - voyage, duration of 3 days (start: 0d, end: 3d)
     * - visit, duration of 1 day (start: 3d, end: 4d)
     * - voyage, duration of 2 days (start 4d, end: 6d)
     *
     * Used in [MergeTestData.old] and [MergeTestData.new] to compare two parsed [line].
     *
     * For example:
     * ```
     * old = "---|||----"
     * new = " ---|||-- "
     * ```
     *
     * Indicates:
     * - new scenario has fewer data (which is normal / to be expected), it is
     *   surrounded by spaces indicating there is no additional data there.
     * - the first voyage ends later, since the visit is moved back in time
     * - because of that, the second voyage starts later as well
     *
     * The [line] is parsed and results in a [List] of [TestEntry]. By automatically creating the
     * appropriate voyages with [TestVoyage] and visits with [TestVisit].
     *
     * These results are to be passed into the [EntriesMergeService.merge], and then compared
     * against the [MergeTestData.expectedEntries].
     */
    private fun parseEntries(
        line: String,
        old: Boolean
    ): List<TestEntry> {
        require(!line.trimStart(' ').trimEnd(' ').contains(' ')) {
            "Visits/voyages should be continuous, can't have empty space in-between."
        }

        val entries = mutableListOf<TestEntry>()
        val entryIdPrefix = if (old) "OLD" else "NEW"
        var duration = Duration.ZERO
        line.forEach { token ->
            when (token) {
                '|' -> parseCreateOrUpdateVisit(duration, entries, old, entryIdPrefix)
                '-' -> parseCreateOrUpdateVoyage(duration, entries, old, entryIdPrefix)
                ' ' -> {} // Does nothing, only increments duration.
                else -> throw Exception("Token $token not recognized.")
            }
            duration += Duration.ofDays(1)
        }

        val output = entries.mapIndexed { index, entry ->
            val previousEntryId = entries.getOrNull(index - 1)?._id ?: "$entryIdPrefix.-1"
            val nextEntryId = entries.getOrNull(index + 1)?._id ?: "$entryIdPrefix.+1"
            when (entry) {
                is TestVisit -> entry.copy(previousEntryId = previousEntryId, nextEntryId = nextEntryId)
                is TestVoyage -> entry.copy(previousEntryId = previousEntryId, nextEntryId = nextEntryId)
            }
        }

        return output
    }

    private fun parseCreateOrUpdateVisit(
        duration: Duration,
        entries: MutableList<TestEntry>,
        old: Boolean,
        entryIdPrefix: String
    ) {
        val lastEntry = entries.lastOrNull()
        if (lastEntry == null || lastEntry !is TestVisit) {
            val port = if (old) OLD_PORT else NEW_PORT
            val newVisit = TestVisit(
                _id = "$entryIdPrefix.${entries.size}.VISIT",
                portAreas = createPortAreas(
                    port = port,
                    startTime = at(duration),
                    endTime = at(duration + Duration.ofDays(1)),
                ),
                finished = true
            )
            entries.add(newVisit)
        } else {
            val updatedPortArea = lastEntry.portAreas.first().copy(
                endTime = at(duration + Duration.ofDays(1))
            )
            entries[entries.size - 1] = lastEntry.copy(portAreas = listOf(updatedPortArea))
        }
    }

    private fun parseCreateOrUpdateVoyage(
        duration: Duration,
        entries: MutableList<TestEntry>,
        old: Boolean,
        entryIdPrefix: String
    ) {
        val lastEntry = entries.lastOrNull()
        if (lastEntry == null || lastEntry !is TestVoyage) {
            val portIds = if (old) listOf(OLD_PORT.portId) else listOf(NEW_PORT.portId)
            val newVoyage = TestVoyage(
                _id = "$entryIdPrefix.${entries.size}.VOYAGE",
                startTime = at(duration),
                startPortIds = portIds,
                endTime = at(duration + Duration.ofDays(1)),
                endPortIds = portIds,
                finished = true
            )
            entries.add(newVoyage)
        } else {
            entries[entries.size - 1] = lastEntry.copy(
                endTime = at(duration + Duration.ofDays(1))
            )
        }
    }

    private fun createPortAreas(
        port: TestPort,
        startTime: ZonedDateTime,
        endTime: ZonedDateTime,
    ): List<TestVisit.PortArea> = listOf(
        TestVisit.PortArea(
            portId = port.portId,
            startTime = startTime,
            endTime = endTime
        )
    )

    private fun mergeTestData() = Stream.of(
        MergeTestData(
            description = "empty returns nothing",
            old = "",
            new = "",
            expectedEntries = emptyList(),
        ),
        MergeTestData(
            description = "exact matching voyage",
            old = "-",
            new = "-",
            expectedEntries = listOf(
                TestVoyage(
                    _id = "OLD.0.VOYAGE",
                    previousEntryId = "OLD.-1",
                    nextEntryId = "OLD.+1",
                    startTime = at(Duration.ZERO),
                    startPortIds = listOf(OLD_PORT.portId),
                    endTime = at(Duration.ofDays(1)),
                    endPortIds = listOf(OLD_PORT.portId),
                    finished = true
                )
            )
        ),
        MergeTestData(
            description = "error, partially matching voyage, not enough information",
            old = "---",
            new = " - ",
            error = true
        ),
        MergeTestData(
            description = "exact matching visits and voyages",
            old = "-|-",
            new = "-|-",
            expectedEntries = listOf(
                TestVoyage(
                    _id = "OLD.0.VOYAGE",
                    previousEntryId = "OLD.-1",
                    nextEntryId = "OLD.1.VISIT",
                    startTime = at(Duration.ZERO),
                    startPortIds = listOf(OLD_PORT.portId),
                    endTime = at(Duration.ofDays(1)),
                    endPortIds = listOf(NEW_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "OLD.1.VISIT",
                    previousEntryId = "OLD.0.VOYAGE",
                    nextEntryId = "OLD.2.VOYAGE",
                    portAreas = createPortAreas(
                        port = NEW_PORT,
                        startTime = at(Duration.ofDays(1)),
                        endTime = at(Duration.ofDays(2))
                    ),
                    finished = true
                ),
                TestVoyage(
                    _id = "OLD.2.VOYAGE",
                    previousEntryId = "OLD.1.VISIT",
                    nextEntryId = "OLD.+1",
                    startTime = at(Duration.ofDays(2)),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(3)),
                    endPortIds = listOf(OLD_PORT.portId),
                    finished = true
                ),
            )
        ),
        MergeTestData(
            description = "remove visit",
            old = "-|-",
            new = "---",
            expectedDeleteVisitIds = listOf("OLD.1.VISIT"),
            expectedDeleteVoyageIds = listOf("OLD.2.VOYAGE"),
            expectedEntries = listOf(
                TestVoyage(
                    _id = "OLD.0.VOYAGE",
                    previousEntryId = "OLD.-1",
                    nextEntryId = "OLD.+1",
                    startTime = at(Duration.ZERO),
                    startPortIds = listOf(OLD_PORT.portId),
                    endTime = at(Duration.ofDays(3)),
                    endPortIds = listOf(OLD_PORT.portId),
                    finished = true
                ),
            )
        ),
        MergeTestData(
            description = "error, remove visit, partial voyage",
            old = "--|--",
            new = " --- ",
            error = true,
        ),
        MergeTestData(
            description = "add visit",
            old = "---",
            new = "-|-",
            expectedEntries = listOf(
                TestVoyage(
                    _id = "OLD.0.VOYAGE",
                    previousEntryId = "OLD.-1",
                    nextEntryId = "NEW.1.VISIT",
                    startTime = at(Duration.ZERO),
                    startPortIds = listOf(OLD_PORT.portId),
                    endTime = at(Duration.ofDays(1)),
                    endPortIds = listOf(NEW_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "NEW.1.VISIT",
                    previousEntryId = "OLD.0.VOYAGE",
                    nextEntryId = "NEW.2.VOYAGE",
                    portAreas = createPortAreas(
                        port = NEW_PORT,
                        startTime = at(Duration.ofDays(1)),
                        endTime = at(Duration.ofDays(2))
                    ),
                    finished = true
                ),
                TestVoyage(
                    _id = "NEW.2.VOYAGE",
                    previousEntryId = "NEW.1.VISIT",
                    nextEntryId = "OLD.+1",
                    startTime = at(Duration.ofDays(2)),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(3)),
                    endPortIds = listOf(OLD_PORT.portId),
                    finished = true
                ),
            )
        ),
        MergeTestData(
            description = "add visit (not surrounded by voyages), will create a new voyage which needs to be attached to the last old visit",
            old = "---|",
            new = " |  ",
            // After merging we don't need to perform fixups, and can update purely the visit.
            expectedEntries = listOf(
                TestVoyage(
                    _id = "OLD.0.VOYAGE",
                    previousEntryId = "OLD.-1",
                    nextEntryId = "NEW.0.VISIT",
                    startTime = at(Duration.ZERO),
                    startPortIds = listOf(OLD_PORT.portId),
                    endTime = at(Duration.ofDays(1)),
                    endPortIds = listOf(NEW_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "NEW.0.VISIT",
                    previousEntryId = "OLD.0.VOYAGE",
                    nextEntryId = CREATES_NEW_ID,
                    portAreas = createPortAreas(
                        port = NEW_PORT,
                        startTime = at(Duration.ofDays(1)),
                        endTime = at(Duration.ofDays(2))
                    ),
                    finished = true
                ),
                TestVoyage(
                    _id = CREATES_NEW_ID,
                    previousEntryId = "NEW.0.VISIT",
                    nextEntryId = "OLD.1.VISIT",
                    startTime = at(Duration.ofDays(2)),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(3)),
                    endPortIds = listOf(OLD_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "OLD.1.VISIT",
                    previousEntryId = CREATES_NEW_ID,
                    nextEntryId = "OLD.+1",
                    portAreas = createPortAreas(
                        port = OLD_PORT,
                        startTime = at(Duration.ofDays(3)),
                        endTime = at(Duration.ofDays(4))
                    ),
                    finished = true
                ),
            ),
            expectedEntriesAfterMerge = listOf(
                TestVoyage(
                    _id = "OLD.0.VOYAGE",
                    previousEntryId = "OLD.-1",
                    nextEntryId = "NEW.0.VISIT",
                    startTime = at(Duration.ZERO),
                    startPortIds = listOf(OLD_PORT.portId),
                    endTime = at(Duration.ofDays(1)),
                    endPortIds = listOf(NEW_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "NEW.0.VISIT",
                    previousEntryId = "OLD.0.VOYAGE",
                    nextEntryId = CREATES_NEW_ID,
                    portAreas = createPortAreas(
                        port = NEW_PORT,
                        startTime = at(Duration.ofDays(1)),
                        endTime = at(Duration.ofDays(2))
                    ),
                    finished = true
                ),
                TestVoyage(
                    _id = CREATES_NEW_ID,
                    previousEntryId = "NEW.0.VISIT",
                    nextEntryId = "OLD.1.VISIT",
                    startTime = at(Duration.ofDays(2)),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(3)),
                    endPortIds = listOf(OLD_PORT.portId),
                    finished = true
                ),
            )
        ),
        MergeTestData(
            description = "shorten visit",
            old = "-|||-",
            new = "--|--",
            expectedEntries = listOf(
                TestVoyage(
                    _id = "OLD.0.VOYAGE",
                    previousEntryId = "OLD.-1",
                    nextEntryId = "OLD.1.VISIT",
                    startTime = at(Duration.ZERO),
                    startPortIds = listOf(OLD_PORT.portId),
                    endTime = at(Duration.ofDays(2)),
                    endPortIds = listOf(NEW_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "OLD.1.VISIT",
                    previousEntryId = "OLD.0.VOYAGE",
                    nextEntryId = "OLD.2.VOYAGE",
                    portAreas = createPortAreas(
                        port = NEW_PORT,
                        startTime = at(Duration.ofDays(2)),
                        endTime = at(Duration.ofDays(3))
                    ),
                    finished = true
                ),
                TestVoyage(
                    _id = "OLD.2.VOYAGE",
                    previousEntryId = "OLD.1.VISIT",
                    nextEntryId = "OLD.+1",
                    startTime = at(Duration.ofDays(3)),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(5)),
                    endPortIds = listOf(OLD_PORT.portId),
                    finished = true
                ),
            )
        ),
        MergeTestData(
            description = "lengthen visit",
            old = "--|--",
            new = "-|||-",
            expectedEntries = listOf(
                TestVoyage(
                    _id = "OLD.0.VOYAGE",
                    previousEntryId = "OLD.-1",
                    nextEntryId = "OLD.1.VISIT",
                    startTime = at(Duration.ZERO),
                    startPortIds = listOf(OLD_PORT.portId),
                    endTime = at(Duration.ofDays(1)),
                    endPortIds = listOf(NEW_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "OLD.1.VISIT",
                    previousEntryId = "OLD.0.VOYAGE",
                    nextEntryId = "OLD.2.VOYAGE",
                    portAreas = createPortAreas(
                        port = NEW_PORT,
                        startTime = at(Duration.ofDays(1)),
                        endTime = at(Duration.ofDays(4))
                    ),
                    finished = true
                ),
                TestVoyage(
                    _id = "OLD.2.VOYAGE",
                    previousEntryId = "OLD.1.VISIT",
                    nextEntryId = "OLD.+1",
                    startTime = at(Duration.ofDays(4)),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(5)),
                    endPortIds = listOf(OLD_PORT.portId),
                    finished = true
                ),
            )
        ),
        MergeTestData(
            description = "split visit",
            old = "-|||-",
            new = "-|-|-",
            expectedEntries = listOf(
                TestVoyage(
                    _id = "OLD.0.VOYAGE",
                    previousEntryId = "OLD.-1",
                    nextEntryId = "OLD.1.VISIT",
                    startTime = at(Duration.ZERO),
                    startPortIds = listOf(OLD_PORT.portId),
                    endTime = at(Duration.ofDays(1)),
                    endPortIds = listOf(NEW_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "OLD.1.VISIT",
                    previousEntryId = "OLD.0.VOYAGE",
                    nextEntryId = "NEW.2.VOYAGE",
                    portAreas = createPortAreas(
                        port = NEW_PORT,
                        startTime = at(Duration.ofDays(1)),
                        endTime = at(Duration.ofDays(2))
                    ),
                    finished = true
                ),
                TestVoyage(
                    _id = "NEW.2.VOYAGE",
                    previousEntryId = "OLD.1.VISIT",
                    nextEntryId = "NEW.3.VISIT",
                    startTime = at(Duration.ofDays(2)),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(3)),
                    endPortIds = listOf(NEW_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "NEW.3.VISIT",
                    previousEntryId = "NEW.2.VOYAGE",
                    nextEntryId = "OLD.2.VOYAGE",
                    portAreas = createPortAreas(
                        port = NEW_PORT,
                        startTime = at(Duration.ofDays(3)),
                        endTime = at(Duration.ofDays(4))
                    ),
                    finished = true
                ),
                TestVoyage(
                    _id = "OLD.2.VOYAGE",
                    previousEntryId = "NEW.3.VISIT",
                    nextEntryId = "OLD.+1",
                    startTime = at(Duration.ofDays(4)),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(5)),
                    endPortIds = listOf(OLD_PORT.portId),
                    finished = true
                ),
            )
        ),
        MergeTestData(
            description = "combine visit",
            old = "-|-|-",
            new = "-|||-",
            expectedDeleteVisitIds = listOf("OLD.3.VISIT"),
            expectedDeleteVoyageIds = listOf("OLD.2.VOYAGE"),
            expectedEntries = listOf(
                TestVoyage(
                    _id = "OLD.0.VOYAGE",
                    previousEntryId = "OLD.-1",
                    nextEntryId = "OLD.1.VISIT",
                    startTime = at(Duration.ZERO),
                    startPortIds = listOf(OLD_PORT.portId),
                    endTime = at(Duration.ofDays(1)),
                    endPortIds = listOf(NEW_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "OLD.1.VISIT",
                    previousEntryId = "OLD.0.VOYAGE",
                    nextEntryId = "OLD.4.VOYAGE",
                    portAreas = createPortAreas(
                        port = NEW_PORT,
                        startTime = at(Duration.ofDays(1)),
                        endTime = at(Duration.ofDays(4))
                    ),
                    finished = true
                ),
                TestVoyage(
                    _id = "OLD.4.VOYAGE",
                    previousEntryId = "OLD.1.VISIT",
                    nextEntryId = "OLD.+1",
                    startTime = at(Duration.ofDays(4)),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(5)),
                    endPortIds = listOf(OLD_PORT.portId),
                    finished = true
                ),
            )
        ),
        MergeTestData(
            description = "exact match, new has less data",
            old = "--|--",
            new = " -|- ",
            expectedEntries = listOf(
                TestVoyage(
                    _id = "OLD.0.VOYAGE",
                    previousEntryId = "OLD.-1",
                    nextEntryId = "OLD.1.VISIT",
                    startTime = at(Duration.ZERO),
                    startPortIds = listOf(OLD_PORT.portId),
                    endTime = at(Duration.ofDays(2)),
                    endPortIds = listOf(NEW_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "OLD.1.VISIT",
                    previousEntryId = "OLD.0.VOYAGE",
                    nextEntryId = "OLD.2.VOYAGE",
                    portAreas = createPortAreas(
                        port = NEW_PORT,
                        startTime = at(Duration.ofDays(2)),
                        endTime = at(Duration.ofDays(3))
                    ),
                    finished = true
                ),
                TestVoyage(
                    _id = "OLD.2.VOYAGE",
                    previousEntryId = "OLD.1.VISIT",
                    nextEntryId = "OLD.+1",
                    startTime = at(Duration.ofDays(3)),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(5)),
                    endPortIds = listOf(OLD_PORT.portId),
                    finished = true
                ),
            )
        ),
        MergeTestData(
            description = "match, new has less data and shorter visit",
            old = "--|||--",
            new = " --|-- ",
            expectedEntries = listOf(
                TestVoyage(
                    _id = "OLD.0.VOYAGE",
                    previousEntryId = "OLD.-1",
                    nextEntryId = "OLD.1.VISIT",
                    startTime = at(Duration.ZERO),
                    startPortIds = listOf(OLD_PORT.portId),
                    endTime = at(Duration.ofDays(3)),
                    endPortIds = listOf(NEW_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "OLD.1.VISIT",
                    previousEntryId = "OLD.0.VOYAGE",
                    nextEntryId = "OLD.2.VOYAGE",
                    portAreas = createPortAreas(
                        port = NEW_PORT,
                        startTime = at(Duration.ofDays(3)),
                        endTime = at(Duration.ofDays(4))
                    ),
                    finished = true
                ),
                TestVoyage(
                    _id = "OLD.2.VOYAGE",
                    previousEntryId = "OLD.1.VISIT",
                    nextEntryId = "OLD.+1",
                    startTime = at(Duration.ofDays(4)),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(7)),
                    endPortIds = listOf(OLD_PORT.portId),
                    finished = true
                ),
            )
        ),
        MergeTestData(
            description = "exact match, new has no surrounding voyages",
            old = "--|||--",
            new = "  |||  ",
            expectedEntries = listOf(
                TestVoyage(
                    _id = "OLD.0.VOYAGE",
                    previousEntryId = "OLD.-1",
                    nextEntryId = "OLD.1.VISIT",
                    startTime = at(Duration.ZERO),
                    startPortIds = listOf(OLD_PORT.portId),
                    endTime = at(Duration.ofDays(2)),
                    endPortIds = listOf(NEW_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "OLD.1.VISIT",
                    previousEntryId = "OLD.0.VOYAGE",
                    nextEntryId = "OLD.2.VOYAGE",
                    portAreas = createPortAreas(
                        port = NEW_PORT,
                        startTime = at(Duration.ofDays(2)),
                        endTime = at(Duration.ofDays(5))
                    ),
                    finished = true
                ),
                TestVoyage(
                    _id = "OLD.2.VOYAGE",
                    previousEntryId = "OLD.1.VISIT",
                    nextEntryId = "OLD.+1",
                    startTime = at(Duration.ofDays(5)),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(7)),
                    endPortIds = listOf(OLD_PORT.portId),
                    finished = true
                ),
            )
        ),
        MergeTestData(
            description = "match, new has no surrounding voyages and shorter visit",
            old = "--|||--",
            new = "   |   ",
            expectedEntries = listOf(
                TestVoyage(
                    _id = "OLD.0.VOYAGE",
                    previousEntryId = "OLD.-1",
                    nextEntryId = "OLD.1.VISIT",
                    startTime = at(Duration.ZERO),
                    startPortIds = listOf(OLD_PORT.portId),
                    endTime = at(Duration.ofDays(3)),
                    endPortIds = listOf(NEW_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "OLD.1.VISIT",
                    previousEntryId = "OLD.0.VOYAGE",
                    nextEntryId = "OLD.2.VOYAGE",
                    portAreas = createPortAreas(
                        port = NEW_PORT,
                        startTime = at(Duration.ofDays(3)),
                        endTime = at(Duration.ofDays(4))
                    ),
                    finished = true
                ),
                TestVoyage(
                    _id = "OLD.2.VOYAGE",
                    previousEntryId = "OLD.1.VISIT",
                    nextEntryId = "OLD.+1",
                    startTime = at(Duration.ofDays(4)),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(7)),
                    endPortIds = listOf(OLD_PORT.portId),
                    finished = true
                ),
            )
        ),
        MergeTestData(
            description = "match, new has no surrounding voyages and larger visit",
            old = "---|---",
            new = "  |||  ",
            expectedEntries = listOf(
                TestVoyage(
                    _id = "OLD.0.VOYAGE",
                    previousEntryId = "OLD.-1",
                    nextEntryId = "OLD.1.VISIT",
                    startTime = at(Duration.ZERO),
                    startPortIds = listOf(OLD_PORT.portId),
                    endTime = at(Duration.ofDays(2)),
                    endPortIds = listOf(NEW_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "OLD.1.VISIT",
                    previousEntryId = "OLD.0.VOYAGE",
                    nextEntryId = "OLD.2.VOYAGE",
                    portAreas = createPortAreas(
                        port = NEW_PORT,
                        startTime = at(Duration.ofDays(2)),
                        endTime = at(Duration.ofDays(5))
                    ),
                    finished = true
                ),
                TestVoyage(
                    _id = "OLD.2.VOYAGE",
                    previousEntryId = "OLD.1.VISIT",
                    nextEntryId = "OLD.+1",
                    startTime = at(Duration.ofDays(5)),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(7)),
                    endPortIds = listOf(OLD_PORT.portId),
                    finished = true
                ),
            )
        ),
        MergeTestData(
            description = "match, surrounded with old visits, larger visit, enough space for a voyage",
            old = "|--|--|",
            new = "--|||--",
            expectedEntries = listOf(
                TestVoyage(
                    _id = "OLD.1.VOYAGE",
                    previousEntryId = "OLD.0.VISIT",
                    nextEntryId = "OLD.2.VISIT",
                    startTime = at(Duration.ofDays(1)),
                    startPortIds = listOf(OLD_PORT.portId),
                    endTime = at(Duration.ofDays(2)),
                    endPortIds = listOf(NEW_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "OLD.2.VISIT",
                    previousEntryId = "OLD.1.VOYAGE",
                    nextEntryId = "OLD.3.VOYAGE",
                    portAreas = createPortAreas(
                        port = NEW_PORT,
                        startTime = at(Duration.ofDays(2)),
                        endTime = at(Duration.ofDays(5))
                    ),
                    finished = true
                ),
                TestVoyage(
                    _id = "OLD.3.VOYAGE",
                    previousEntryId = "OLD.2.VISIT",
                    nextEntryId = "OLD.4.VISIT",
                    startTime = at(Duration.ofDays(5)),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(6)),
                    endPortIds = listOf(OLD_PORT.portId),
                    finished = true
                ),
            )
        ),
        MergeTestData(
            description = "match, surrounded with old visits, larger visit, minimal space for empty voyage",
            old = "|-|-|",
            new = "-|||-",
            expectedEntries = listOf(
                TestVoyage(
                    _id = "OLD.1.VOYAGE",
                    previousEntryId = "OLD.0.VISIT",
                    nextEntryId = "OLD.2.VISIT",
                    startTime = at(Duration.ofDays(1)),
                    startPortIds = listOf(OLD_PORT.portId),
                    endTime = at(Duration.ofDays(1)),
                    endPortIds = listOf(NEW_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "OLD.2.VISIT",
                    previousEntryId = "OLD.1.VOYAGE",
                    nextEntryId = "OLD.3.VOYAGE",
                    portAreas = createPortAreas(
                        port = NEW_PORT,
                        startTime = at(Duration.ofDays(1)),
                        endTime = at(Duration.ofDays(4))
                    ),
                    finished = true
                ),
                TestVoyage(
                    _id = "OLD.3.VOYAGE",
                    previousEntryId = "OLD.2.VISIT",
                    nextEntryId = "OLD.4.VISIT",
                    startTime = at(Duration.ofDays(4)),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(4)),
                    endPortIds = listOf(OLD_PORT.portId),
                    finished = true
                ),
            )
        ),
        MergeTestData(
            description = "error, no voyage to merge into",
            old = "|---|",
            new = "|||||",
            error = true
        ),
        MergeTestData(
            description = "error, no voyage to merge into (left)",
            old = "|---|",
            new = "|||--",
            error = true
        ),
        MergeTestData(
            description = "error, no voyage to merge into (right)",
            old = "|---|",
            new = "--|||",
            error = true
        ),
        MergeTestData(
            description = "match, new surrounded with visits, inner smaller visit, will create a new voyage which needs to be attached to the last old visit",
            old = "---|||---|",
            new = " |--|--|  ",
            expectedEntries = listOf(
                TestVoyage(
                    _id = "OLD.0.VOYAGE",
                    previousEntryId = "OLD.-1",
                    nextEntryId = "NEW.0.VISIT",
                    startTime = at(Duration.ZERO),
                    startPortIds = listOf(OLD_PORT.portId),
                    endTime = at(Duration.ofDays(1)),
                    endPortIds = listOf(NEW_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "NEW.0.VISIT",
                    previousEntryId = "OLD.0.VOYAGE",
                    nextEntryId = "NEW.1.VOYAGE",
                    portAreas = createPortAreas(
                        port = NEW_PORT,
                        startTime = at(Duration.ofDays(1)),
                        endTime = at(Duration.ofDays(2))
                    ),
                    finished = true
                ),
                TestVoyage(
                    _id = "NEW.1.VOYAGE",
                    previousEntryId = "NEW.0.VISIT",
                    nextEntryId = "OLD.1.VISIT",
                    startTime = at(Duration.ofDays(2)),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(4)),
                    endPortIds = listOf(NEW_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "OLD.1.VISIT",
                    previousEntryId = "NEW.1.VOYAGE",
                    nextEntryId = "OLD.2.VOYAGE",
                    portAreas = createPortAreas(
                        port = NEW_PORT,
                        startTime = at(Duration.ofDays(4)),
                        endTime = at(Duration.ofDays(5))
                    ),
                    finished = true
                ),
                TestVoyage(
                    _id = "OLD.2.VOYAGE",
                    previousEntryId = "OLD.1.VISIT",
                    nextEntryId = "NEW.4.VISIT",
                    startTime = at(Duration.ofDays(5)),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(7)),
                    endPortIds = listOf(NEW_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "NEW.4.VISIT",
                    previousEntryId = "OLD.2.VOYAGE",
                    nextEntryId = CREATES_NEW_ID,
                    portAreas = createPortAreas(
                        port = NEW_PORT,
                        startTime = at(Duration.ofDays(7)),
                        endTime = at(Duration.ofDays(8))
                    ),
                    finished = true
                ),
                TestVoyage(
                    _id = CREATES_NEW_ID,
                    previousEntryId = "NEW.4.VISIT",
                    nextEntryId = "OLD.3.VISIT",
                    startTime = at(Duration.ofDays(8)),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(9)),
                    endPortIds = listOf(OLD_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "OLD.3.VISIT",
                    previousEntryId = CREATES_NEW_ID,
                    nextEntryId = "OLD.+1",
                    portAreas = createPortAreas(
                        port = OLD_PORT,
                        startTime = at(Duration.ofDays(9)),
                        endTime = at(Duration.ofDays(10))
                    ),
                    finished = true
                ),
            ),
            expectedEntriesAfterMerge = listOf(
                TestVoyage(
                    _id = "OLD.0.VOYAGE",
                    previousEntryId = "OLD.-1",
                    nextEntryId = "NEW.0.VISIT",
                    startTime = at(Duration.ZERO),
                    startPortIds = listOf(OLD_PORT.portId),
                    endTime = at(Duration.ofDays(1)),
                    endPortIds = listOf(NEW_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "NEW.0.VISIT",
                    previousEntryId = "OLD.0.VOYAGE",
                    nextEntryId = "NEW.1.VOYAGE",
                    portAreas = createPortAreas(
                        port = NEW_PORT,
                        startTime = at(Duration.ofDays(1)),
                        endTime = at(Duration.ofDays(2))
                    ),
                    finished = true
                ),
                TestVoyage(
                    _id = "NEW.1.VOYAGE",
                    previousEntryId = "NEW.0.VISIT",
                    nextEntryId = "OLD.1.VISIT",
                    startTime = at(Duration.ofDays(2)),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(4)),
                    endPortIds = listOf(NEW_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "OLD.1.VISIT",
                    previousEntryId = "NEW.1.VOYAGE",
                    nextEntryId = "OLD.2.VOYAGE",
                    portAreas = createPortAreas(
                        port = NEW_PORT,
                        startTime = at(Duration.ofDays(4)),
                        endTime = at(Duration.ofDays(5))
                    ),
                    finished = true
                ),
                TestVoyage(
                    _id = "OLD.2.VOYAGE",
                    previousEntryId = "OLD.1.VISIT",
                    nextEntryId = "NEW.4.VISIT",
                    startTime = at(Duration.ofDays(5)),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(7)),
                    endPortIds = listOf(NEW_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "NEW.4.VISIT",
                    previousEntryId = "OLD.2.VOYAGE",
                    nextEntryId = CREATES_NEW_ID,
                    portAreas = createPortAreas(
                        port = NEW_PORT,
                        startTime = at(Duration.ofDays(7)),
                        endTime = at(Duration.ofDays(8))
                    ),
                    finished = true
                ),
                TestVoyage(
                    _id = CREATES_NEW_ID,
                    previousEntryId = "NEW.4.VISIT",
                    nextEntryId = "OLD.3.VISIT",
                    startTime = at(Duration.ofDays(8)),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(9)),
                    endPortIds = listOf(OLD_PORT.portId),
                    finished = true
                ),
            )
        ),
        MergeTestData(
            description = "large windows require extra pruning of old data",
            old = "--|--|--|--",
            new = "    -|-    ",
            expectedDeleteVisitIds = listOf("OLD.1.VISIT", "OLD.5.VISIT"),
            expectedDeleteVoyageIds = listOf("OLD.2.VOYAGE", "OLD.4.VOYAGE"),
            expectedEntries = listOf(
                TestVoyage(
                    _id = "OLD.0.VOYAGE",
                    previousEntryId = "OLD.-1",
                    nextEntryId = "OLD.3.VISIT",
                    startTime = at(Duration.ZERO),
                    startPortIds = listOf(OLD_PORT.portId),
                    endTime = at(Duration.ofDays(5)),
                    endPortIds = listOf(NEW_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "OLD.3.VISIT",
                    previousEntryId = "OLD.0.VOYAGE",
                    nextEntryId = "OLD.6.VOYAGE",
                    portAreas = createPortAreas(
                        port = NEW_PORT,
                        startTime = at(Duration.ofDays(5)),
                        endTime = at(Duration.ofDays(6))
                    ),
                    finished = true
                ),
                TestVoyage(
                    _id = "OLD.6.VOYAGE",
                    previousEntryId = "OLD.3.VISIT",
                    nextEntryId = "OLD.+1",
                    startTime = at(Duration.ofDays(6)),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(11)),
                    endPortIds = listOf(OLD_PORT.portId),
                    finished = true
                ),
            )
        ),
        MergeTestData(
            description = "large windows require extra pruning of old data, only a visit",
            old = "--|--|--|--",
            new = "     |     ",
            expectedDeleteVisitIds = listOf("OLD.1.VISIT", "OLD.5.VISIT"),
            expectedDeleteVoyageIds = listOf("OLD.2.VOYAGE", "OLD.4.VOYAGE"),
            expectedEntries = listOf(
                TestVoyage(
                    _id = "OLD.0.VOYAGE",
                    previousEntryId = "OLD.-1",
                    nextEntryId = "OLD.3.VISIT",
                    startTime = at(Duration.ZERO),
                    startPortIds = listOf(OLD_PORT.portId),
                    endTime = at(Duration.ofDays(5)),
                    endPortIds = listOf(NEW_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "OLD.3.VISIT",
                    previousEntryId = "OLD.0.VOYAGE",
                    nextEntryId = "OLD.6.VOYAGE",
                    portAreas = createPortAreas(
                        port = NEW_PORT,
                        startTime = at(Duration.ofDays(5)),
                        endTime = at(Duration.ofDays(6))
                    ),
                    finished = true
                ),
                TestVoyage(
                    _id = "OLD.6.VOYAGE",
                    previousEntryId = "OLD.3.VISIT",
                    nextEntryId = "OLD.+1",
                    startTime = at(Duration.ofDays(6)),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(11)),
                    endPortIds = listOf(OLD_PORT.portId),
                    finished = true
                ),
            )
        ),
        MergeTestData(
            description = "longer voyage, gets shortened",
            old = "||---||",
            new = " ----- ",
            expectedEntries = listOf(
                TestVoyage(
                    _id = "OLD.1.VOYAGE",
                    previousEntryId = "OLD.0.VISIT",
                    nextEntryId = "OLD.2.VISIT",
                    startTime = at(Duration.ofDays(2)),
                    startPortIds = listOf(OLD_PORT.portId),
                    endTime = at(Duration.ofDays(5)),
                    endPortIds = listOf(OLD_PORT.portId),
                    finished = true
                ),
            )
        ),
        MergeTestData(
            description = "error, longer voyage on left, too short on the right",
            old = "||---||",
            new = " ---   ",
            error = true,
        ),
        MergeTestData(
            description = "error, longer voyage on right, too short on the left",
            old = "||---||",
            new = "   --- ",
            error = true,
        ),
        MergeTestData(
            description = "error, data missing on left, no overlap",
            old = "   --",
            new = "--   ",
            error = true,
        ),
        MergeTestData(
            description = "data missing on left, no overlap, but can merge with visit",
            old = "   |--",
            new = "--|   ",
            expectedDeleteVisitIds = listOf("OLD.0.VISIT"),
            expectedEntries = listOf(
                TestVoyage(
                    _id = "NEW.0.VOYAGE",
                    previousEntryId = null,
                    nextEntryId = "NEW.1.VISIT",
                    startTime = at(Duration.ZERO),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(2)),
                    endPortIds = listOf(NEW_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "NEW.1.VISIT",
                    previousEntryId = "NEW.0.VOYAGE",
                    nextEntryId = "OLD.1.VOYAGE",
                    portAreas = createPortAreas(
                        port = NEW_PORT,
                        startTime = at(Duration.ofDays(2)),
                        endTime = at(Duration.ofDays(3)),
                    ),
                    finished = true
                ),
                TestVoyage(
                    _id = "OLD.1.VOYAGE",
                    previousEntryId = "NEW.1.VISIT",
                    nextEntryId = "OLD.+1",
                    startTime = at(Duration.ofDays(3)),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(6)),
                    endPortIds = listOf(OLD_PORT.portId),
                    finished = true
                ),
            )
        ),
        MergeTestData(
            description = "data missing on left, has overlap, reuse old visit ID and stitch voyage",
            old = "   ||--",
            new = "--||   ",
            expectedEntries = listOf(
                TestVoyage(
                    _id = "NEW.0.VOYAGE",
                    previousEntryId = null,
                    nextEntryId = "OLD.0.VISIT",
                    startTime = at(Duration.ZERO),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(2)),
                    endPortIds = listOf(NEW_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "OLD.0.VISIT",
                    previousEntryId = "NEW.0.VOYAGE",
                    nextEntryId = "OLD.1.VOYAGE",
                    portAreas = createPortAreas(
                        port = NEW_PORT,
                        startTime = at(Duration.ofDays(2)),
                        endTime = at(Duration.ofDays(4)),
                    ),
                    finished = true
                ),
                TestVoyage(
                    _id = "OLD.1.VOYAGE",
                    previousEntryId = "OLD.0.VISIT",
                    nextEntryId = "OLD.+1",
                    startTime = at(Duration.ofDays(4)),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(7)),
                    endPortIds = listOf(OLD_PORT.portId),
                    finished = true
                ),
            )
        ),
        MergeTestData(
            description = "error, data missing on right, no overlap",
            old = "--   ",
            new = "   --",
            error = true,
        ),
        MergeTestData(
            description = "error, data missing on right, no overlap (with visit)",
            old = "--|   ",
            new = "   |--",
            error = true,
        ),
        MergeTestData(
            description = "error, data missing on right, has overlap, could stitch together but requires checking there's no other data",
            old = "--|   ",
            new = "  ||--",
            error = true
        ),
        MergeTestData(
            description = "data missing on left, starts with visit, new voyage should be added in front",
            old = "    --",
            new = "|--|  ",
            expectedEntries = listOf(
                TestVoyage(
                    _id = CREATES_NEW_ID,
                    // Previous entry ID should be unset, since patching for first visit.
                    previousEntryId = null,
                    nextEntryId = "NEW.0.VISIT",
                    startTime = at(Duration.ofDays(0)),
                    startPortIds = emptyList(),
                    endTime = at(Duration.ofDays(0)),
                    endPortIds = listOf(NEW_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "NEW.0.VISIT",
                    previousEntryId = CREATES_NEW_ID,
                    nextEntryId = "NEW.1.VOYAGE",
                    portAreas = createPortAreas(
                        port = NEW_PORT,
                        startTime = at(Duration.ofDays(0)),
                        endTime = at(Duration.ofDays(1)),
                    ),
                    finished = true
                ),
                TestVoyage(
                    _id = "NEW.1.VOYAGE",
                    previousEntryId = "NEW.0.VISIT",
                    nextEntryId = "NEW.2.VISIT",
                    startTime = at(Duration.ofDays(1)),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(3)),
                    endPortIds = listOf(NEW_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "NEW.2.VISIT",
                    previousEntryId = "NEW.1.VOYAGE",
                    nextEntryId = "OLD.0.VOYAGE",
                    portAreas = createPortAreas(
                        port = NEW_PORT,
                        startTime = at(Duration.ofDays(3)),
                        endTime = at(Duration.ofDays(4)),
                    ),
                    finished = true
                ),
                TestVoyage(
                    _id = "OLD.0.VOYAGE",
                    previousEntryId = "NEW.2.VISIT",
                    nextEntryId = "OLD.+1",
                    startTime = at(Duration.ofDays(4)),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(6)),
                    endPortIds = listOf(OLD_PORT.portId),
                    finished = true
                )
            )
        ),
        MergeTestData(
            description = "data misses at start, ensure voyage gets updated and its ID reused, visit is kept and empty voyage is added",
            old = "    --",
            new = "|-----",
            expectedEntries = listOf(
                TestVoyage(
                    _id = CREATES_NEW_ID,
                    // Previous entry ID should be unset, since patching for first visit.
                    previousEntryId = null,
                    nextEntryId = "NEW.0.VISIT",
                    startTime = at(Duration.ofDays(0)),
                    startPortIds = emptyList(),
                    endTime = at(Duration.ofDays(0)),
                    endPortIds = listOf(NEW_PORT.portId),
                    finished = true
                ),
                TestVisit(
                    _id = "NEW.0.VISIT",
                    previousEntryId = CREATES_NEW_ID,
                    nextEntryId = "OLD.0.VOYAGE",
                    portAreas = createPortAreas(
                        port = NEW_PORT,
                        startTime = at(Duration.ofDays(0)),
                        endTime = at(Duration.ofDays(1)),
                    ),
                    finished = true
                ),
                TestVoyage(
                    _id = "OLD.0.VOYAGE",
                    previousEntryId = "NEW.0.VISIT",
                    nextEntryId = "OLD.+1",
                    startTime = at(Duration.ofDays(1)),
                    startPortIds = listOf(NEW_PORT.portId),
                    endTime = at(Duration.ofDays(6)),
                    endPortIds = listOf(OLD_PORT.portId),
                    finished = true
                ),
            )
        ),
    )

    @Test
    fun `merge - pre-emptively remove corrupted visit`() {
        val oldEntries = listOf(
            // purposely incorrectly ordered, should be after the voyage
            TestVisit(
                _id = "1",
                portAreas = listOf(
                    TestVisit.PortArea(
                        portId = NLRTM,
                        startTime = ZonedDateTime.parse("2023-12-30T02:40:00Z")
                        // purposely doesn't have an endTime
                    ),
                ),
                finished = false
            ),
            TestVoyage(
                _id = "0",
                startPortIds = emptyList(),
                startTime = ZonedDateTime.parse("2023-12-30T02:40:00Z"),
                // purposely set this endTime to be too far in the future
                endTime = ZonedDateTime.parse("2024-03-12T20:46:00Z"),
                finished = true
            ),
            // first visit should be moved here, purposely missing a voyage
            TestVisit(
                _id = "2",
                portAreas = createPortAreas(
                    port = PORT_BEANR,
                    startTime = ZonedDateTime.parse("2024-03-12T20:46:00Z"),
                    endTime = ZonedDateTime.parse("2024-03-15T11:23:00Z")
                ),
                finished = true
            ),
            TestVoyage(
                _id = "3",
                startPortIds = emptyList(),
                startTime = ZonedDateTime.parse("2024-03-15T11:23:00Z"),
                endTime = ZonedDateTime.parse("2024-03-17T13:10:00Z"),
                finished = true
            )
        )

        val newEntries = listOf(
            TestVisit(
                _id = "new.1",
                portAreas = listOf(
                    TestVisit.PortArea(
                        portId = NLRTM,
                        startTime = ZonedDateTime.parse("2023-12-30T02:40:00Z"),
                        endTime = ZonedDateTime.parse("2024-01-01T00:00:00Z")
                    ),
                ),
                finished = true
            ),
            TestVoyage(
                _id = "new.2",
                startPortIds = emptyList(),
                startTime = ZonedDateTime.parse("2024-01-01T00:00:00Z"),
                endTime = oldEntries[2].startTime,
                finished = true
            ),
            (oldEntries[2] as TestVisit).copy(_id = "new.3"),
        )

        val convertedOldEntries = oldEntries.map { it.fromTestModel() }
        val convertedNewEntries = newEntries.map { it.fromTestModel() }
        wheneverEntryServiceContainingEntries(convertedOldEntries)

        val window = TimeWindow(
            from = newEntries.first().startTime.toInstant(),
            to = newEntries.last().endTime!!.toInstant()
        )
        val result = entriesMergeService.merge(imo, window, convertedNewEntries).toTestModel()

        val expected = MergeResult(
            entries = listOf(
                (oldEntries[1] as TestVoyage).copy(
                    endTime = ZonedDateTime.parse("2023-12-30T02:40:00Z"),
                    endPortIds = listOf(NLRTM),
                    nextEntryId = newEntries[0]._id,
                    regenerated = true
                ),
                (newEntries[0] as TestVisit).copy(
                    previousEntryId = oldEntries[1]._id,
                    nextEntryId = newEntries[1]._id,
                    regenerated = true
                ),
                (newEntries[1] as TestVoyage).copy(
                    previousEntryId = newEntries[0]._id,
                    nextEntryId = oldEntries[2]._id,
                    regenerated = true
                ),
                (newEntries[2] as TestVisit).copy(
                    _id = oldEntries[2]._id,
                    previousEntryId = newEntries[1]._id,
                    nextEntryId = oldEntries[3]._id,
                    regenerated = true
                ),
                (oldEntries[3] as TestVoyage).copy(
                    previousEntryId = oldEntries[2]._id,
                    startPortIds = listOf(BEANR),
                    regenerated = true
                )
            ),
            deleteVisitIds = listOf("1")
        )
        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `merge - pre-emptively remove corrupted voyage`() {
        val oldEntries = listOf(
            TestVoyage(
                _id = "CORRUPT",
                startPortIds = emptyList(),
                startTime = ZonedDateTime.parse("2023-01-01T00:00:00Z"),
                // purposely doesn't have an endTime
                finished = false
            ),
            TestVoyage(
                _id = "0",
                startPortIds = emptyList(),
                startTime = ZonedDateTime.parse("2023-01-01T00:00:00Z"),
                endTime = ZonedDateTime.parse("2023-03-01T00:00:00Z"),
                finished = true
            )
        )

        val newEntries = listOf(
            TestVisit(
                _id = "new",
                portAreas = createPortAreas(
                    port = PORT_NLRTM,
                    startTime = ZonedDateTime.parse("2023-02-01T00:00:00Z"),
                    endTime = ZonedDateTime.parse("2023-02-02T00:00:00Z")
                ),
                finished = true
            )
        )

        val convertedOldEntries = oldEntries.map { it.fromTestModel() }
        val convertedNewEntries = newEntries.map { it.fromTestModel() }
        wheneverEntryServiceContainingEntries(convertedOldEntries)

        val window = TimeWindow(
            from = newEntries.first().startTime.toInstant(),
            to = newEntries.last().endTime!!.toInstant()
        )
        val result = entriesMergeService.merge(imo, window, convertedNewEntries).toTestModel()

        val expectedEntries = listOf(
            TestVoyage(
                _id = "0",
                startPortIds = emptyList(),
                startTime = ZonedDateTime.parse("2023-01-01T00:00:00Z"),
                endTime = ZonedDateTime.parse("2023-02-01T00:00:00Z"),
                finished = true,
                nextEntryId = newEntries[0]._id,
                endPortIds = listOf(NLRTM)
            ).copy(regenerated = true),
            (newEntries[0] as TestVisit).copy(
                previousEntryId = "0",
                nextEntryId = CREATES_NEW_ID,
                regenerated = true
            ),
            TestVoyage(
                _id = CREATES_NEW_ID,
                startPortIds = listOf(NLRTM),
                startTime = ZonedDateTime.parse("2023-02-02T00:00:00Z"),
                endTime = ZonedDateTime.parse("2023-03-01T00:00:00Z"),
                finished = true,
                previousEntryId = newEntries[0]._id
            ).copy(regenerated = true)
        )

        val expected = MergeResult(
            entries = expectedEntries,
            deleteVoyageIds = listOf("CORRUPT")
        )
        val actual = result.copy(
            entries = zipActualWithExpectedToReplaceRandomIds(result, expectedEntries)
        )
        assertThat(actual).isEqualTo(expected)
    }

    @Test
    fun `merge - when creating an empty voyage, ensure the first visit links correctly to this voyage`() {
        val oldEntries = listOf(
            TestVoyage(
                _id = "0",
                startPortIds = emptyList(),
                startTime = ZonedDateTime.parse("2024-01-02T00:00:00Z"),
                endTime = ZonedDateTime.parse("2024-01-03T00:00:00Z"),
                finished = true
            ),
        )

        val newEntries = listOf(
            TestVisit(
                _id = "new.1",
                portAreas = listOf(
                    TestVisit.PortArea(
                        portId = NLRTM,
                        startTime = ZonedDateTime.parse("2024-01-01T00:00:00Z"),
                        endTime = ZonedDateTime.parse("2024-01-02T00:00:00Z"),
                    ),
                ),
                finished = true
            ),
        )

        val convertedOldEntries = oldEntries.map { it.fromTestModel() }
        val convertedNewEntries = newEntries.map { it.fromTestModel() }
        wheneverEntryServiceContainingEntries(convertedOldEntries)

        val window = TimeWindow(
            from = newEntries.first().startTime.toInstant(),
            to = newEntries.last().endTime!!.toInstant()
        )
        val result = entriesMergeService.merge(imo, window, convertedNewEntries).toTestModel()
        assertThat(result.entries).hasSize(3)

        val (emptyVoyage, newVisit, oldVoyage) = result.entries
        assertThat(newVisit.previousEntryId).isEqualTo(emptyVoyage._id)
        assertThat(newVisit.nextEntryId).isEqualTo(oldVoyage._id)
    }
}
