package nl.teqplay.vesselvoyage.service.merge

import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.anyOrNull
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.whenever
import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.TimeWindow
import nl.teqplay.vesselvoyage.datasource.NewESoFDataSource
import nl.teqplay.vesselvoyage.model.v2.AreaActivity
import nl.teqplay.vesselvoyage.model.v2.EntryESoFWrapper
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewESoF
import nl.teqplay.vesselvoyage.model.v2.NewEncounter
import nl.teqplay.vesselvoyage.model.v2.NewStop
import nl.teqplay.vesselvoyage.model.v2.NewStopType
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import nl.teqplay.vesselvoyage.service.api.EntryV2Service
import nl.teqplay.vesselvoyage.util.createNewVisit
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import java.time.Instant
import java.time.ZoneOffset

private val entryV2Service = mock<EntryV2Service>()
private val eSoFDataSource = mock<NewESoFDataSource>()
private val entriesMergeV2Service = EntriesMergeV2Service(entryV2Service, eSoFDataSource)

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class EntriesMergeV2ServiceTest :
    EntriesMergeBaseServiceTest<EntryESoFWrapper<NewVisit>, EntryESoFWrapper<NewVoyage>, EntryESoFWrapper<*>>(
        entriesMergeV2Service
    ) {

    companion object {
        private val LOC = Location(0.0, 0.0)
    }

    override fun EntryESoFWrapper<*>.toTestModel(): TestEntry = when (val entry = this.entry) {
        is NewVisit -> TestVisit(
            _id = entry._id,
            previousEntryId = entry.previous,
            nextEntryId = entry.next,
            portAreas = entry.portAreaActivities.map {
                TestVisit.PortArea(
                    portId = it.areaId,
                    startTime = it.start.time.atZone(ZoneOffset.UTC),
                    endTime = it.end?.time?.atZone(ZoneOffset.UTC)
                )
            },
            finished = entry.end != null,
            regenerated = entry.regenerated
        )

        is NewVoyage -> TestVoyage(
            _id = entry._id,
            previousEntryId = entry.previous,
            nextEntryId = entry.next,
            finished = entry.end != null,
            startTime = entry.start.time.atZone(ZoneOffset.UTC),
            startPortIds = listOfNotNull(entry.originPort),
            endTime = entry.end?.time?.atZone(ZoneOffset.UTC),
            endPortIds = listOfNotNull(entry.destinationPort).ifEmpty { null },
            regenerated = entry.regenerated
        )
    }

    override fun TestEntry.fromTestModel(): EntryESoFWrapper<*> = when (this) {
        is TestVisit -> {
            val portAreaActivities = this.portAreas.map {
                AreaActivity(
                    id = it.portId,
                    start = LocationTime(LOC, it.startTime.toInstant()),
                    end = it.endTime?.toInstant()?.let { end -> LocationTime(LOC, end) },
                    areaId = it.portId
                )
            }
            EntryESoFWrapper(
                entry = createNewVisit(
                    _id = this._id,
                    imo = imoInt,
                    previous = this.previousEntryId,
                    next = this.nextEntryId,
                    start = LocationTime(LOC, this.startTime.toInstant()),
                    end = this.endTime?.toInstant()?.let { end -> LocationTime(LOC, end) },
                    portAreaActivities = portAreaActivities,
                    eospAreaActivity = AreaActivity(
                        id = portAreaActivities.first().id,
                        start = portAreaActivities.first().start,
                        end = portAreaActivities.last().end,
                        areaId = portAreaActivities.first().areaId
                    ),
                    destination = null,
                    stops = emptyList(),
                    regenerated = this.regenerated
                ),
                esof = null
            )
        }

        is TestVoyage -> {
            EntryESoFWrapper(
                entry = NewVoyage(
                    _id = this._id,
                    imo = imoInt,
                    previous = this.previousEntryId,
                    next = this.nextEntryId,
                    start = LocationTime(LOC, this.startTime.toInstant()),
                    end = this.endTime?.toInstant()?.let { end -> LocationTime(LOC, end) },
                    destination = null,
                    stops = emptyList(),
                    actualStart = null,
                    originPort = this.startPortIds.firstOrNull(),
                    destinationPort = this.endPortIds?.firstOrNull(),
                    regenerated = this.regenerated
                ),
                esof = null
            )
        }
    }

    override fun wheneverEntryServiceContainingEntries(entries: List<EntryESoFWrapper<*>>) {
        val innerEntries = entries.map { it.entry }
        whenever(entryV2Service.findByImoAndTimeRange(any(), any(), any(), any(), anyOrNull())).thenReturn(innerEntries)
        whenever(entryV2Service.findEntry(any())).thenAnswer {
            val entryId = it.getArgument<String>(0)
            innerEntries.find { entry -> entry._id == entryId }
        }

        val innerEsofs = entries.mapNotNull { it.esof }
        whenever(eSoFDataSource.findByIds(any())).thenAnswer {
            val entryIds = it.getArgument<List<String>>(0)
            innerEsofs.filter { esof -> esof._id in entryIds }
        }
        whenever(eSoFDataSource.findById(any())).thenAnswer {
            val entryId = it.getArgument<String>(0)
            innerEsofs.find { esof -> esof._id == entryId }
        }
    }

    private fun createStop(
        startTime: Instant,
        endTime: Instant,
    ): NewStop = NewStop(
        startEventId = "startEventId",
        endEventId = "endEventId",
        location = LOC,
        start = LocationTime(LOC, startTime),
        end = LocationTime(LOC, endTime),
        type = NewStopType.UNCLASSIFIED,
        areaId = null,
        accuracy = null
    )

    private fun createEncounter(
        startTime: Instant,
        endTime: Instant,
    ): NewEncounter = NewEncounter(
        startEventId = "startEventId",
        start = LocationTime(LOC, startTime),
        end = LocationTime(LOC, endTime),
        type = EncounterEvent.EncounterType.UNCLASSIFIED,
        otherMmsi = 1,
        otherImo = null
    )

    private fun createStopEncounterPair(
        startTime: Instant,
        endTime: Instant,
    ): Pair<NewStop, NewEncounter> = createStop(startTime, endTime) to createEncounter(startTime, endTime)

    @Test
    fun `merge - should split ESOF entries in overlapping voyages`() {
        val (leftStopInside, leftEncounterInside) = createStopEncounterPair(
            startTime = Instant.parse("1970-01-01T00:00:00Z"),
            endTime = Instant.parse("1970-01-02T00:00:00Z")
        )
        val (leftStopShortened, leftEncounterNotShortened) = createStopEncounterPair(
            startTime = Instant.parse("1970-01-02T12:00:00Z"),
            endTime = Instant.parse("1970-01-03T12:00:00Z")
        )
        val (leftStopRemoved, leftEncounterRemoved) = createStopEncounterPair(
            startTime = Instant.parse("1970-01-03T00:00:00Z"),
            endTime = Instant.parse("1970-01-03T04:00:00Z")
        )

        val (rightStopInside, rightEncounterInside) = createStopEncounterPair(
            startTime = Instant.parse("1970-01-05T00:00:00Z"),
            endTime = Instant.parse("1970-01-06T00:00:00Z")
        )
        val (rightStopShortened, rightEncounterNotShortened) = createStopEncounterPair(
            startTime = Instant.parse("1970-01-03T12:00:00Z"),
            endTime = Instant.parse("1970-01-04T12:00:00Z")
        )
        val (rightStopRemoved, rightEncounterRemoved) = createStopEncounterPair(
            startTime = Instant.parse("1970-01-03T20:00:00Z"),
            endTime = Instant.parse("1970-01-04T00:00:00Z")
        )

        val oldVoyage = EntryESoFWrapper(
            entry = NewVoyage(
                _id = "OLD.VOYAGE",
                imo = imoInt,
                start = LocationTime(LOC, Instant.parse("1970-01-01T00:00:00Z")),
                end = LocationTime(LOC, Instant.parse("1970-01-08T00:00:00Z")),
                stops = listOf(
                    leftStopInside,
                    leftStopShortened,
                    leftStopRemoved,
                    rightStopInside,
                    rightStopShortened,
                    rightStopRemoved
                ),
                destination = null,
                previous = null,
                next = null,
                actualStart = null,
                originPort = null,
                destinationPort = null
            ),
            esof = NewESoF(
                _id = "OLD.VOYAGE",
                encounters = listOf(
                    leftEncounterInside,
                    leftEncounterNotShortened,
                    leftEncounterRemoved,
                    rightEncounterInside,
                    rightEncounterNotShortened,
                    rightEncounterRemoved
                ),
                slowMovingPeriods = null,
                shipToShipTransfers = emptyList()
            )
        )
        val newVisit = EntryESoFWrapper(
            entry = NewVisit(
                _id = "NEW.VISIT",
                imo = imoInt,
                start = LocationTime(LOC, Instant.parse("1970-01-03T00:00:00Z")),
                end = LocationTime(LOC, Instant.parse("1970-01-04T00:00:00Z")),
                eospAreaActivity = mock(),
                stops = emptyList(),
                destination = null,
                previous = null,
                next = null
            ),
            esof = null
        )

        val window = TimeWindow(
            from = oldVoyage.entry.start.time,
            to = oldVoyage.entry.end!!.time
        )
        wheneverEntryServiceContainingEntries(listOf(oldVoyage))
        val res = entriesMergeV2Service.merge(imo, window, listOf(newVisit))
        assertEquals(3, res.entries.size)

        val startVoyage = res.entries[0]
        val endVoyage = res.entries[2]

        // Assert stops are split over both voyages
        assertEquals(
            listOf(
                leftStopInside,
                leftStopShortened.copy(end = newVisit.entry.start)
            ),
            startVoyage.entry.stops
        )
        assertEquals(
            listOf(
                rightStopInside,
                rightStopShortened.copy(start = newVisit.entry.end!!)
            ),
            endVoyage.entry.stops
        )

        // Assert ESOF encounters are split over both voyages
        assertEquals(startVoyage.entry._id, startVoyage.esof?._id)
        assertEquals(endVoyage.entry._id, endVoyage.esof?._id)
        assertEquals(
            listOf(
                leftEncounterInside,
                leftEncounterNotShortened
            ),
            startVoyage.esof?.encounters
        )
        assertEquals(
            listOf(
                rightEncounterInside,
                rightEncounterNotShortened
            ),
            endVoyage.esof?.encounters
        )
    }
}
