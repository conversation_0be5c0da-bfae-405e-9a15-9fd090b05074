package nl.teqplay.vesselvoyage.service

import org.junit.jupiter.api.Assertions.assertDoesNotThrow
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

class ImoLockServiceTest {

    private lateinit var imoLockService: ImoLockService
    private val testImo = 12345

    @BeforeEach
    fun setup() {
        // Initialize a fresh instance of ImoLockService before each test
        imoLockService = ImoLockService()
    }

    @Test
    fun `should create lock for new IMO`() {
        val lockCreated = imoLockService.lock(testImo)
        assertTrue(lockCreated, "Lock should be created for a new IMO")
    }

    @Test
    fun `should create lock for locked IMO in sequence on same thead`() {
        imoLockService.lock(testImo) // event 1
        val lockCreated = imoLockService.lock(testImo) // event 2
        assertTrue(lockCreated, "Lock should be created when in the same thread")
    }

    @Test
    fun `should unlock IMO and allow re-locking`() {
        imoLockService.lock(testImo)
        imoLockService.unlock(testImo)
        val lockCreated = imoLockService.lock(testImo)
        assertTrue(lockCreated, "IMO should be re-lockable after unlocking")
    }

    @Test
    fun `should execute blocking logic when lock is available`() {
        var executed = false
        imoLockService.executeBlocking(testImo) {
            executed = true
        }
        assertTrue(executed, "Blocking logic should execute when lock is available")
    }

    @Test
    fun `should block execution until lock is released`() {
        val executor = Executors.newSingleThreadExecutor()

        // Lock the IMO
        imoLockService.lock(testImo)

        // Attempt to execute blocking logic in a separate thread
        var executed = false
        executor.submit {
            imoLockService.executeBlocking(testImo) {
                executed = true
            }
        }

        // Ensure the logic has not executed yet
        Thread.sleep(100)
        assertFalse(executed, "Blocking logic should not execute while lock is held")

        // Unlock the IMO and wait for the thread to complete
        imoLockService.unlock(testImo)
        executor.shutdown()
        executor.awaitTermination(1, TimeUnit.SECONDS)

        assertTrue(executed, "Blocking logic should execute after lock is released")
    }

    @Test
    fun `should handle unlocking an IMO that is not locked`() {
        assertDoesNotThrow {
            imoLockService.unlock(testImo)
        }
    }

    @Test
    fun `should clean up lock after unlocking`() {
        imoLockService.lock(testImo)
        imoLockService.unlock(testImo)
        val lockExists = imoLockService.lock(testImo)
        assertTrue(lockExists, "Lock should be cleaned up after unlocking")
    }
}
