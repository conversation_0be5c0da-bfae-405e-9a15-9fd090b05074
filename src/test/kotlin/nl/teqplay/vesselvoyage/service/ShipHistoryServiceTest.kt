package nl.teqplay.vesselvoyage.service

import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.argumentCaptor
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.times
import com.nhaarman.mockitokotlin2.verify
import nl.teqplay.skeleton.common.BaseTest
import nl.teqplay.skeleton.model.TimeWindow
import nl.teqplay.vesselvoyage.client.AisHistoryClient
import nl.teqplay.vesselvoyage.properties.AisFetchingProperties
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.Duration
import java.time.Instant
import java.time.ZonedDateTime

class ShipHistoryServiceTest : BaseTest() {
    private val aisFetchingProperties = AisFetchingProperties(
        maxDays = 99999,
        chunkDuration = Duration.ofDays(1)
    )

    @Test
    fun `should query ais history in chunks`() {
        val aisHistoryClient: AisHistoryClient = mock()
        val shipHistoryService = ShipHistoryService(aisFetchingProperties, aisHistoryClient)

        shipHistoryService.queryAisHistoryInChunks(
            mmsis = setOf("0"),
            from = ZonedDateTime.parse("2022-02-09T10:00:00Z"),
            to = ZonedDateTime.parse("2022-02-11T20:00:00Z"),
            maxDays = 30,
            transformChunk = { it },
            accumulateChunks = { it.flatten() }
        )

        val queryCaptor = argumentCaptor<List<AisHistoryClient.AisHistoricMessageMmsiQuery>>()
        verify(aisHistoryClient, times(3)).queryAisHistory(queryCaptor.capture(), any())
        assertEquals(
            listOf(
                listOf(
                    AisHistoryClient.AisHistoricMessageMmsiQuery(
                        mmsi = 0,
                        window = TimeWindow(
                            from = Instant.parse("2022-02-09T10:00:00Z"),
                            to = Instant.parse("2022-02-10T10:00:00Z"),
                        )
                    )
                ),
                listOf(
                    AisHistoryClient.AisHistoricMessageMmsiQuery(
                        mmsi = 0,
                        window = TimeWindow(
                            from = Instant.parse("2022-02-10T10:00:00Z"),
                            to = Instant.parse("2022-02-11T10:00:00Z"),
                        )
                    )
                ),
                listOf(
                    AisHistoryClient.AisHistoricMessageMmsiQuery(
                        mmsi = 0,
                        window = TimeWindow(
                            from = Instant.parse("2022-02-11T10:00:00Z"),
                            to = Instant.parse("2022-02-11T20:00:00Z"),
                        )
                    )
                )
            ),
            queryCaptor.allValues
        )
    }
}
