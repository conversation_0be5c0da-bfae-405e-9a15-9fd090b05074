package nl.teqplay.vesselvoyage.service

import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.anyOrNull
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.whenever
import nl.teqplay.csi.model.ship.mapping.ImoMmsiMapping
import nl.teqplay.vesselvoyage.logic.DEFAULT_START_TIME
import nl.teqplay.vesselvoyage.logic.IMO_1
import nl.teqplay.vesselvoyage.model.internal.ShipStory
import nl.teqplay.vesselvoyage.service.api.EsofV2Service
import nl.teqplay.vesselvoyage.service.api.VisitV2Service
import nl.teqplay.vesselvoyage.service.api.VoyageV2Service
import nl.teqplay.vesselvoyage.service.trace.ProcessingTraceService
import nl.teqplay.vesselvoyage.util.createLocationTime
import nl.teqplay.vesselvoyage.util.createNewVisit
import nl.teqplay.vesselvoyage.util.createNewVoyage
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.temporal.ChronoUnit

class StoryServiceTest {
    private val eventService = mock<EventFetchingService>()
    private val entryProcessingService = mock<EntryProcessingService>()
    private val infraService = mock<InfraService>()
    private val aisFetchingService = mock<AisFetchingService>()
    private val processingTraceService = mock<ProcessingTraceService>()
    private val visitService = mock<VisitV2Service>()
    private val voyageService = mock<VoyageV2Service>()
    private val esofService = mock<EsofV2Service>()
    private val staticShipInfoService = mock<StaticShipInfoService>()
    private val storyService = StoryService(
        eventService = eventService,
        entryProcessingService = entryProcessingService,
        infraService = infraService,
        processingTraceService = processingTraceService,
        visitService = visitService,
        voyageService = voyageService,
        esofService = esofService,
        staticShipInfoService = staticShipInfoService
    )

    private val TEST_TIME_1 = DEFAULT_START_TIME.toInstant()
    private val TEST_TIME_2 = TEST_TIME_1.plus(7, ChronoUnit.DAYS)
    private val TEST_TIME_3 = TEST_TIME_2.plus(7, ChronoUnit.DAYS)

    private val TEST_VISIT_1 = createNewVisit(
        _id = "VISIT_1",
        next = "VOYAGE_2",
        start = createLocationTime(time = TEST_TIME_1),
        end = createLocationTime(time = TEST_TIME_2)
    )
    private val TEST_VOYAGE_2 = createNewVoyage(
        _id = "VOYAGE_2",
        previous = "VISIT_1",
        next = "VIIST_3",
        start = createLocationTime(time = TEST_TIME_2),
        end = createLocationTime(time = TEST_TIME_2)
    )
    private val TEST_VISIT_3_ONGOING = createNewVisit(
        _id = "VISIT_3",
        previous = "VOYAGE_2",
        start = createLocationTime(time = TEST_TIME_2)
    )
    private val TEST_VISIT_3_FINISHED = TEST_VISIT_3_ONGOING.copy(
        next = "VOYAGE_4",
        end = createLocationTime(time = TEST_TIME_3)
    )
    private val TEST_VOYAGE_4 = createNewVoyage(
        _id = "VOYAGE_4",
        previous = "VISIT_3",
        start = createLocationTime(time = TEST_TIME_3)
    )

    @Test
    fun `should correctly sort entries when getting story with ongoing voyage`() {
        whenever(visitService.findByImoAndTimeRange(any(), any(), any(), any(), anyOrNull()))
            .thenReturn(listOf(TEST_VISIT_1, TEST_VISIT_3_FINISHED))
        whenever(voyageService.findByImoAndTimeRange(any(), any(), any(), any(), anyOrNull()))
            .thenReturn(listOf(TEST_VOYAGE_2, TEST_VOYAGE_4))
        whenever(esofService.findById(any())).thenReturn(null)
        whenever(processingTraceService.getTraceById(any(), any(), any())).thenReturn(null)
        whenever(infraService.getById(any(), any())).thenReturn(null)
        whenever(aisFetchingService.getShipTrace(any(), any(), any())).thenReturn(emptyList())
        whenever(staticShipInfoService.getMmsiMappingFromImoAndDateRange(any(), any(), anyOrNull()))
            .thenReturn(listOf(ImoMmsiMapping("111111111", 0, 0, 0, 0, 0)))

        val result = storyService.getStory(IMO_1.toInt(), TEST_TIME_1, TEST_TIME_3)
        val expected = ShipStory(
            IMO_1.toInt(),
            items = listOf(
                ShipStory.ShipStoryItem(entry = TEST_VISIT_1, esof = null, trace = null, mmsis = listOf(111111111)),
                ShipStory.ShipStoryItem(entry = TEST_VOYAGE_2, esof = null, trace = null, mmsis = listOf(111111111)),
                ShipStory.ShipStoryItem(entry = TEST_VISIT_3_FINISHED, esof = null, trace = null, mmsis = listOf(111111111)),
                ShipStory.ShipStoryItem(entry = TEST_VOYAGE_4, esof = null, trace = null, mmsis = listOf(111111111)),
            ),
            pomaEntities = emptyMap()
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should correctly sort entries when getting story with ongoing visit`() {
        whenever(visitService.findByImoStartingAtOrAfter(any(), any(), any(), anyOrNull()))
            .thenReturn(listOf(TEST_VISIT_1, TEST_VISIT_3_ONGOING))
        whenever(voyageService.findByImoStartingAtOrAfter(any(), any(), any(), anyOrNull()))
            .thenReturn(listOf(TEST_VOYAGE_2))
        whenever(esofService.findById(any())).thenReturn(null)
        whenever(processingTraceService.getTraceById(any(), any(), any())).thenReturn(null)
        whenever(infraService.getById(any(), any())).thenReturn(null)
        whenever(aisFetchingService.getShipTrace(any(), any(), any())).thenReturn(emptyList())
        whenever(staticShipInfoService.getMmsiMappingFromImoAndDateRange(any(), any(), anyOrNull()))
            .thenReturn(listOf(ImoMmsiMapping("111111111", 0, 0, 0, 0, 0)))

        val result = storyService.getStory(IMO_1.toInt(), TEST_TIME_1, null)
        val expected = ShipStory(
            IMO_1.toInt(),
            items = listOf(
                ShipStory.ShipStoryItem(entry = TEST_VISIT_1, esof = null, trace = null, mmsis = listOf(111111111)),
                ShipStory.ShipStoryItem(entry = TEST_VOYAGE_2, esof = null, trace = null, mmsis = listOf(111111111)),
                ShipStory.ShipStoryItem(entry = TEST_VISIT_3_ONGOING, esof = null, trace = null, mmsis = listOf(111111111))
            ),
            pomaEntities = emptyMap()
        )

        assertEquals(expected, result)
    }
}
