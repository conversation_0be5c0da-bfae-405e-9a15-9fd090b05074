package nl.teqplay.vesselvoyage.service

import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.anyOrNull
import com.nhaarman.mockitokotlin2.eq
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.whenever
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import nl.teqplay.vesselvoyage.datasource.NewESoFDataSource
import nl.teqplay.vesselvoyage.datasource.NewShipStatusStateDataSource
import nl.teqplay.vesselvoyage.datasource.NewVisitDataSource
import nl.teqplay.vesselvoyage.datasource.NewVoyageDataSource
import nl.teqplay.vesselvoyage.model.v2.EntryESoFWrapper
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import nl.teqplay.vesselvoyage.util.createLocationTime
import nl.teqplay.vesselvoyage.util.createNewESoF
import nl.teqplay.vesselvoyage.util.createNewVisit
import nl.teqplay.vesselvoyage.util.createNewVoyage
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.Duration
import java.time.Instant

class ShipStatusServiceTest {
    val visitDataSource = mock<NewVisitDataSource>()
    val voyageDataSource = mock<NewVoyageDataSource>()
    val esofDataSource = mock<NewESoFDataSource>()
    val shipStatusStateDataSource = mock<NewShipStatusStateDataSource>()
    val meterRegistry = SimpleMeterRegistry()
    val service = ProcessingShipStatusService(
        visitDataSource = mock(),
        voyageDataSource = mock(),
        newVisitDataSource = visitDataSource,
        newVoyageDataSource = voyageDataSource,
        newESoFDataSource = esofDataSource,
        newShipStatusStateDataSource = shipStatusStateDataSource,
        meterRegistry = meterRegistry
    )

    @Test
    fun `should return initial ship status on no entries`() {
        whenever(visitDataSource.findLastByImo(eq(0), eq(2), any(), anyOrNull()))
            .thenReturn(emptyList())
        whenever(voyageDataSource.findLastByImo(eq(0), eq(2), any(), anyOrNull()))
            .thenReturn(emptyList())

        val expected = NewInitialShipStatus()
        val result = service.getStatus(0)

        assertEquals(expected, result)
    }

    @Test
    fun `should return visit ship status on one visit`() {
        val visit = createNewVisit()
        whenever(visitDataSource.findLastByImo(eq(0), eq(2), any(), anyOrNull()))
            .thenReturn(listOf(visit))
        whenever(voyageDataSource.findLastByImo(eq(0), eq(2), any(), anyOrNull()))
            .thenReturn(emptyList())
        whenever(esofDataSource.findById(any()))
            .thenReturn(null)

        val expected = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = visit, esof = null),
            previousVoyage = null,
            previousVisit = null
        )
        val result = service.getStatus(0)

        assertEquals(expected, result)
    }

    @Test
    fun `should return load in esof for visit ship status`() {
        val visitEntryId = "ID"
        val visit = createNewVisit(_id = visitEntryId)
        val visitEsof = createNewESoF(_id = visitEntryId)
        whenever(visitDataSource.findLastByImo(eq(0), eq(2), any(), anyOrNull()))
            .thenReturn(listOf(visit))
        whenever(voyageDataSource.findLastByImo(eq(0), eq(2), any(), anyOrNull()))
            .thenReturn(emptyList())
        whenever(esofDataSource.findById(eq(visitEntryId)))
            .thenReturn(visitEsof)

        val expected = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = visit, esof = visitEsof),
            previousVoyage = null,
            previousVisit = null
        )
        val result = service.getStatus(0)

        assertEquals(expected, result)
    }

    @Test
    fun `should return voyage ship status on currently ongoing voyage`() {
        val startTime = Instant.ofEpochMilli(1704067200000)
        val endTime = startTime.plus(Duration.ofDays(1))
        val start = createLocationTime(time = startTime)
        val end = createLocationTime(time = endTime)
        val visit = createNewVisit(start = start, end = end)
        val voyage = createNewVoyage(start = end)
        whenever(visitDataSource.findLastByImo(eq(0), eq(2), any(), anyOrNull()))
            .thenReturn(listOf(visit))
        whenever(voyageDataSource.findLastByImo(eq(0), eq(2), any(), anyOrNull()))
            .thenReturn(listOf(voyage))
        whenever(esofDataSource.findById(any()))
            .thenReturn(null)

        val expected = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(entry = voyage, esof = null),
            previousVisit = EntryESoFWrapper(entry = visit, esof = null),
            previousVoyage = null
        )
        val result = service.getStatus(0)

        assertEquals(expected, result)
    }

    @Test
    fun `should return visit ship status on currently ongoing visit`() {
        val startTimePreviousVisit = Instant.ofEpochMilli(1704067200000)
        val endTimePreviousVisit = startTimePreviousVisit.plus(Duration.ofDays(1))
        val startTimeCurrentVisit = startTimePreviousVisit.plus(Duration.ofDays(2))
        val startPreviousVisit = createLocationTime(time = startTimePreviousVisit)
        val endPreviousVisit = createLocationTime(time = endTimePreviousVisit)
        val startCurrentVisit = createLocationTime(time = startTimeCurrentVisit)
        val previousVisit = createNewVisit(start = startPreviousVisit, end = endPreviousVisit)
        val inBetweenVoyage = createNewVoyage(start = endPreviousVisit, end = startCurrentVisit)
        val currentVisit = createNewVisit(start = startCurrentVisit)
        whenever(visitDataSource.findLastByImo(eq(0), eq(2), any(), anyOrNull()))
            .thenReturn(listOf(currentVisit, previousVisit))
        whenever(voyageDataSource.findLastByImo(eq(0), eq(2), any(), anyOrNull()))
            .thenReturn(listOf(inBetweenVoyage))
        whenever(esofDataSource.findById(any()))
            .thenReturn(null)

        val expected = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVoyage = EntryESoFWrapper(entry = inBetweenVoyage, esof = null),
            previousVisit = EntryESoFWrapper(entry = previousVisit, esof = null),
        )
        val result = service.getStatus(0)

        assertEquals(expected, result)
    }

    @Test
    fun `should return visit ship status on currently ongoing visit with zero second voyage`() {
        val startTimePreviousVisit = Instant.ofEpochMilli(1704067200000)
        val endTimePreviousVisit = startTimePreviousVisit.plus(Duration.ofDays(1))
        val startPreviousVisit = createLocationTime(time = startTimePreviousVisit)
        val endPreviousVisit = createLocationTime(time = endTimePreviousVisit)
        val previousVisit = createNewVisit(start = startPreviousVisit, end = endPreviousVisit)
        val inBetweenVoyage = createNewVoyage(start = endPreviousVisit, end = endPreviousVisit)
        val currentVisit = createNewVisit(start = endPreviousVisit)
        whenever(visitDataSource.findLastByImo(eq(0), eq(2), any(), anyOrNull()))
            .thenReturn(listOf(currentVisit, previousVisit))
        whenever(voyageDataSource.findLastByImo(eq(0), eq(2), any(), anyOrNull()))
            .thenReturn(listOf(inBetweenVoyage))
        whenever(esofDataSource.findById(any()))
            .thenReturn(null)

        val expected = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVoyage = EntryESoFWrapper(entry = inBetweenVoyage, esof = null),
            previousVisit = EntryESoFWrapper(entry = previousVisit, esof = null),
        )
        val result = service.getStatus(0)

        assertEquals(expected, result)
    }
}
