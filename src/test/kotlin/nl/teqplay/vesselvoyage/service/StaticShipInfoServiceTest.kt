package nl.teqplay.vesselvoyage.service

import com.fasterxml.jackson.core.type.TypeReference
import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.argumentCaptor
import com.nhaarman.mockitokotlin2.clearInvocations
import com.nhaarman.mockitokotlin2.doReturn
import com.nhaarman.mockitokotlin2.eq
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.never
import com.nhaarman.mockitokotlin2.times
import com.nhaarman.mockitokotlin2.verify
import com.nhaarman.mockitokotlin2.whenever
import kotlinx.coroutines.runBlocking
import nl.teqplay.csi.model.ship.info.ShipRegisterInfoCache
import nl.teqplay.csi.model.ship.info.component.ShipCalculated
import nl.teqplay.csi.model.ship.info.component.ShipCategories
import nl.teqplay.csi.model.ship.info.component.ShipCategoryV1
import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2
import nl.teqplay.csi.model.ship.info.component.ShipSpecification
import nl.teqplay.csi.model.ship.mapping.ShipRegisterMapping
import nl.teqplay.vesselvoyage.client.csi.CSIClient
import nl.teqplay.vesselvoyage.datasource.TempFileDataSource
import nl.teqplay.vesselvoyage.model.Filter
import nl.teqplay.vesselvoyage.properties.ApplicationProperties
import nl.teqplay.vesselvoyage.properties.DiskCacheProperties
import nl.teqplay.vesselvoyage.util.createMmsiMapping
import nl.teqplay.vesselvoyage.util.createShipMmsiMapping
import nl.teqplay.vesselvoyage.util.createShipRegisterInfoCache
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class StaticShipInfoServiceTest {
    companion object {
        private const val IMO_1 = "IMO_1"
        private const val MMSI_1_A = "MMSI_1_A"
        private const val MMSI_1_B = "MMSI_1_B"
        private val CATEGORY_1 = ShipCategories(v1 = ShipCategoryV1.CONTAINER, v2 = ShipCategoryV2.CONTAINER)
        private val SPECIFICATION_1 = ShipSpecification(deadWeightTonnage = 1.0)
        private const val IMO_2 = "IMO_2"
        private const val MMSI_2 = "MMSI_2"
        private val CATEGORY_2 = ShipCategories(v1 = ShipCategoryV1.BULK, v2 = ShipCategoryV2.BULK_CARRIER)
        private val SPECIFICATION_2 = ShipSpecification(deadWeightTonnage = 2.0)
        private const val IMO_3 = "IMO_3"
        private const val MMSI_3 = "MMSI_3"
        private val CATEGORY_3 = ShipCategories(v1 = ShipCategoryV1.CARGO, v2 = ShipCategoryV2.CONTAINER)
        private val SPECIFICATION_3 = ShipSpecification(deadWeightTonnage = 3.0)
        private val CALCULATED_3 = ShipCalculated(twentyFootEquivalentUnit = 3.0)
        private const val IMO_4 = "IMO_4"
        private const val MMSI_4 = "MMSI_4"
        private const val MMSI_4_B = "MMSI_4_B"
        private val CATEGORY_4 = ShipCategories(v1 = ShipCategoryV1.TANKER, v2 = ShipCategoryV2.TANKER)
        private val SPECIFICATION_4 = ShipSpecification(deadWeightTonnage = 4.0)
        private const val IMO_5 = "IMO_5"
        private const val MMSI_5 = "MMSI_5"
        private val CATEGORY_5 = ShipCategories(v1 = ShipCategoryV1.TANKER, v2 = ShipCategoryV2.TANKER)
        private val SPECIFICATION_5 = ShipSpecification(deadWeightTonnage = 5.0)
        private const val MMSI_6 = "MMSI_6"
        private const val MMSI_7 = "MMSI_7"

        private val TIMESTAMP_1 = ZonedDateTime.of(LocalDate.of(2022, 1, 1), LocalTime.MIDNIGHT, ZoneOffset.UTC)
        private val TIMESTAMP_2 = ZonedDateTime.of(LocalDate.of(2022, 2, 1), LocalTime.MIDNIGHT, ZoneOffset.UTC)
        private val TIMESTAMP_3 = ZonedDateTime.of(LocalDate.of(2022, 3, 1), LocalTime.MIDNIGHT, ZoneOffset.UTC)
        private val TIMESTAMP_4 = ZonedDateTime.of(LocalDate.of(2022, 4, 1), LocalTime.MIDNIGHT, ZoneOffset.UTC)

        private val allCSIShips = listOf(
            createShipRegisterInfoCache(IMO_1, MMSI_1_A, CATEGORY_1, SPECIFICATION_1),
            createShipRegisterInfoCache(IMO_1, MMSI_1_B, CATEGORY_1, SPECIFICATION_1),
            createShipRegisterInfoCache(IMO_2, MMSI_2, CATEGORY_2, SPECIFICATION_2),
            createShipRegisterInfoCache(IMO_3, MMSI_3, CATEGORY_3, SPECIFICATION_3, CALCULATED_3),
            createShipRegisterInfoCache(IMO_4, MMSI_4, CATEGORY_4, SPECIFICATION_4),
            createShipRegisterInfoCache(IMO_5, MMSI_5, CATEGORY_5, SPECIFICATION_5),
            createShipRegisterInfoCache(null, MMSI_6, CATEGORY_1, SPECIFICATION_1),
            createShipRegisterInfoCache(null, MMSI_7, CATEGORY_2, SPECIFICATION_2)
        )

        private val customMapping = ShipRegisterMapping(
            IMO_4,
            mapping = listOf(
                createMmsiMapping(null, TIMESTAMP_2, MMSI_4),
                createMmsiMapping(TIMESTAMP_2, null, MMSI_4_B)
            ),
            ignored = emptySet(),
            state = ""
        )

        private val allCSIMmsiMapping = listOf(
            createShipMmsiMapping(IMO_1, TIMESTAMP_1, TIMESTAMP_3, MMSI_1_A, MMSI_1_B),
            createShipMmsiMapping(IMO_2, TIMESTAMP_2, TIMESTAMP_3, MMSI_2),
            createShipMmsiMapping(IMO_3, TIMESTAMP_1, null, MMSI_3),
            customMapping
        )

        private val testDiskCacheProperties = DiskCacheProperties(
            enabled = true,
            csiShipsFile = "vesselVoyageCSIShipsTest.json",
            csiShipRegistryFile = "vesselVoyageCSIShipsRegistryTest.json"
        )
        private val testAppProperties = ApplicationProperties(
            environment = "UNIT_TEST"
        )
    }

    @Test
    fun `should create correct mappings between imo, mmsi, type, category`() {
        val csiClient: CSIClient = mock()
        val slackMessageService: SlackMessageService = mock()
        val tempFileDataSource: TempFileDataSource = mock()

        doReturn(allCSIShips).whenever(csiClient).listShipRegisterInfoCache()

        val shipCacheService = ShipCacheService()
        val staticShipInfoService = StaticShipInfoService(
            testDiskCacheProperties,
            testAppProperties,
            csiClient,
            slackMessageService,
            shipCacheService,
            tempFileDataSource
        ).also {
            runBlocking { it.onStartup() }
        }

        assertEquals(IMO_1, staticShipInfoService.getImoFromMmsi(MMSI_1_A))
        assertEquals(IMO_1, staticShipInfoService.getImoFromMmsi(MMSI_1_B))
        assertEquals(IMO_2, staticShipInfoService.getImoFromMmsi(MMSI_2))

        assertEquals(MMSI_1_B, staticShipInfoService.getCurrentMmsiFromImo(IMO_1))
        assertEquals(MMSI_2, staticShipInfoService.getCurrentMmsiFromImo(IMO_2))
        assertEquals(MMSI_3, staticShipInfoService.getCurrentMmsiFromImo(IMO_3))
        assertEquals(MMSI_4, staticShipInfoService.getCurrentMmsiFromImo(IMO_4))
        assertEquals(MMSI_5, staticShipInfoService.getCurrentMmsiFromImo(IMO_5))

        assertEquals(CATEGORY_3, staticShipInfoService.getShipCategoriesByIMO(IMO_3))
        assertEquals(CATEGORY_4, staticShipInfoService.getShipCategoriesByIMO(IMO_4))
        assertEquals(null, staticShipInfoService.getShipCategoriesByIMO("foo"))

        assertEquals(CATEGORY_3.v2, staticShipInfoService.getShipCategoryByIMO(IMO_3))
        assertEquals(CATEGORY_4.v2, staticShipInfoService.getShipCategoryByIMO(IMO_4))
        assertEquals(null, staticShipInfoService.getShipCategoryByIMO("foo"))

        assertEquals(setOf(IMO_1, IMO_3), staticShipInfoService.getShipsByCategory(ShipCategoryV2.CONTAINER))
        assertEquals(setOf(IMO_4, IMO_5), staticShipInfoService.getShipsByCategory(ShipCategoryV2.TANKER))

        assertEquals(setOf(IMO_3, IMO_4), staticShipInfoService.getShipsByDeadWeightTonnages(listOf(Filter.Range(3.0, 4.0))))

        assertEquals(setOf(IMO_3, IMO_4, IMO_5, IMO_1, IMO_2), staticShipInfoService.getAllImos())
    }

    @Test
    fun `should create csi history imo mmsi mapping`() {
        val csiClient: CSIClient = mock()
        val slackMessageService: SlackMessageService = mock()
        val tempFileDataSource: TempFileDataSource = mock()

        doReturn(emptyList<ShipRegisterInfoCache>()).whenever(csiClient).listShipRegisterInfoCache()
        doReturn(allCSIMmsiMapping).whenever(csiClient).listShipRegisterMapping()

        val shipCacheService = ShipCacheService()
        val staticShipInfoService = StaticShipInfoService(
            testDiskCacheProperties,
            testAppProperties,
            csiClient,
            slackMessageService,
            shipCacheService,
            tempFileDataSource
        ).also {
            runBlocking { it.onStartup() }
        }

        assertEquals(listOf(MMSI_1_A, MMSI_1_B), staticShipInfoService.getMmsiMappingFromImoAndDateRange(IMO_1, TIMESTAMP_1, TIMESTAMP_2)?.map { it.mmsi })
        assertEquals(listOf(MMSI_1_A, MMSI_1_B), staticShipInfoService.getMmsiMappingFromImo(IMO_1)?.map { it.mmsi })
        assertEquals(emptyList<String>(), staticShipInfoService.getMmsiMappingFromImoAndDateRange(IMO_2, TIMESTAMP_1, TIMESTAMP_2)?.map { it.mmsi })
        assertEquals(listOf(MMSI_2), staticShipInfoService.getMmsiMappingFromImoAndDateRange(IMO_2, TIMESTAMP_1, TIMESTAMP_4)?.map { it.mmsi })
        assertEquals(listOf(MMSI_2), staticShipInfoService.getMmsiMappingFromImo(IMO_2)?.map { it.mmsi })
        assertEquals(listOf(MMSI_4, MMSI_4_B), staticShipInfoService.getMmsiMappingFromImoAndDateRange(IMO_4, TIMESTAMP_1, TIMESTAMP_4)?.map { it.mmsi })
        assertEquals(listOf(MMSI_4, MMSI_4_B), staticShipInfoService.getMmsiMappingFromImoAndDateRange(IMO_4, TIMESTAMP_1)?.map { it.mmsi })
        assertEquals(listOf(MMSI_4_B), staticShipInfoService.getMmsiMappingFromImoAndDateRange(IMO_4, TIMESTAMP_2, TIMESTAMP_4)?.map { it.mmsi })
        assertEquals(listOf(MMSI_4_B), staticShipInfoService.getMmsiMappingFromImoAndDateRange(IMO_4, TIMESTAMP_3, TIMESTAMP_4)?.map { it.mmsi })
        assertEquals(listOf(MMSI_4_B), staticShipInfoService.getMmsiMappingFromImoAndDateRange(IMO_4, TIMESTAMP_3)?.map { it.mmsi })
    }

    @Test
    fun `should use DiskCache - load without cache`() {
        val csiClient: CSIClient = mock()
        val slackMessageService: SlackMessageService = mock()
        val tempFileDataSource: TempFileDataSource = mock()

        doReturn(listOf<ShipRegisterInfoCache>()).whenever(csiClient).listShipRegisterInfoCache()
        doReturn(listOf<ShipRegisterMapping>()).whenever(csiClient).listShipRegisterMapping()

        val shipCacheService = ShipCacheService()
        StaticShipInfoService(
            testDiskCacheProperties,
            testAppProperties,
            csiClient,
            slackMessageService,
            shipCacheService,
            tempFileDataSource
        ).also {
            runBlocking { it.onStartup() }
        }

        // should invoke both readFromFile and fetching from the clients
        verify(csiClient, times(1)).listShipRegisterInfoCache()
        verify(csiClient, times(1)).listShipRegisterMapping()
        verify(tempFileDataSource, times(2)).readFromFile(any(), any<TypeReference<Any>>())
    }

    @Test
    fun `should use DiskCache - refresh`() {
        val csiClient: CSIClient = mock()
        val slackMessageService: SlackMessageService = mock()
        val tempFileDataSource: TempFileDataSource = mock()

        doReturn(listOf<ShipRegisterInfoCache>()).whenever(csiClient).listShipRegisterInfoCache()

        val shipCacheService = ShipCacheService()
        val staticShipInfoService = StaticShipInfoService(
            testDiskCacheProperties,
            testAppProperties,
            csiClient,
            slackMessageService,
            shipCacheService,
            tempFileDataSource
        ).also {
            runBlocking { it.onStartup() }
        }

        clearInvocations(csiClient)
        clearInvocations(tempFileDataSource)

        // refresh StaticShipInfoService
        staticShipInfoService.refresh()

        // should invoke both fetching from the clients and writeToFile, and not readFromFile
        verify(csiClient, times(1)).listShipRegisterInfoCache()
        verify(tempFileDataSource, never()).readFromFile(any(), any<TypeReference<Any>>())
        verify(tempFileDataSource, times(2)).writeToFile(any(), any<Any>())
    }

    @Test
    fun `should use DiskCache - load with cache`() {
        val csiClient: CSIClient = mock()
        val slackMessageService: SlackMessageService = mock()
        val tempFileDataSource: TempFileDataSource = mock()

        doReturn(listOf<ShipRegisterInfoCache>()).whenever(csiClient).listShipRegisterInfoCache()
        doReturn(listOf<ShipRegisterMapping>()).whenever(csiClient).listShipRegisterMapping()
        doReturn(listOf<ShipRegisterInfoCache>()).whenever(tempFileDataSource)
            .readFromFile(eq(testDiskCacheProperties.csiShipsFile), any<TypeReference<Any>>())
        doReturn(listOf<ShipRegisterMapping>()).whenever(tempFileDataSource)
            .readFromFile(eq(testDiskCacheProperties.csiShipRegistryFile), any<TypeReference<Any>>())

        val shipCacheService = ShipCacheService()
        StaticShipInfoService(
            testDiskCacheProperties,
            testAppProperties,
            csiClient,
            slackMessageService,
            shipCacheService,
            tempFileDataSource
        ).also {
            runBlocking { it.onStartup() }
        }

        // should invoke readFromFile and not fetch from the clients
        verify(csiClient, times(0)).listShipRegisterInfoCache()
        verify(csiClient, times(0)).listShipRegisterMapping()
        val expectedFilenames = listOf(testDiskCacheProperties.csiShipsFile, testDiskCacheProperties.csiShipRegistryFile)
        val readCaptor = argumentCaptor<String>()
        verify(tempFileDataSource, times(2)).readFromFile(readCaptor.capture(), any<TypeReference<Any>>())
        assertEquals(expectedFilenames.sorted(), readCaptor.allValues.sorted())
    }

    private fun applyFilterTestData() = Stream.of(
        Arguments.of("no filter applied", null, null),
        Arguments.of(
            "categories filter",
            Filter(categoriesV2 = setOf(ShipCategoryV2.TANKER, ShipCategoryV2.CONTAINER)),
            setOf(IMO_4, IMO_5, IMO_1, IMO_3)
        ),
        Arguments.of(
            "DWT filter",
            Filter(deadWeightTonnages = listOf(Filter.Range(3.0, 4.0))),
            setOf(IMO_3, IMO_4)
        ),
        Arguments.of(
            "per-category filter, no DWT or TEU",
            Filter(
                perCategory = listOf(
                    Filter.PerCategory(v2 = ShipCategoryV2.CONTAINER),
                    Filter.PerCategory(v2 = ShipCategoryV2.TANKER)
                )
            ),
            setOf(IMO_1, IMO_3, IMO_4, IMO_5)
        ),
        Arguments.of(
            "per-category filter, check DWT",
            Filter(
                perCategory = listOf(
                    Filter.PerCategory(
                        v2 = ShipCategoryV2.TANKER,
                        deadWeightTonnages = listOf(Filter.Range(3.0, 4.0))
                    )
                )
            ),
            setOf(IMO_4)
        ),
        Arguments.of(
            "per-category filter, check TEU",
            Filter(
                perCategory = listOf(
                    Filter.PerCategory(
                        v2 = ShipCategoryV2.CONTAINER,
                        twentyFootEquivalentUnits = listOf(Filter.Range(3.0, 4.0))
                    )
                )
            ),
            setOf(IMO_3)
        )
    )

    @ParameterizedTest
    @MethodSource("applyFilterTestData")
    fun `apply filter`(
        message: String,
        filter: Filter?,
        imos: Set<String>?
    ) {
        val csiClient: CSIClient = mock()
        val slackMessageService: SlackMessageService = mock()
        val tempFileDataSource: TempFileDataSource = mock()

        doReturn(allCSIShips).whenever(csiClient).listShipRegisterInfoCache()

        val shipCacheService = ShipCacheService()
        val staticShipInfoService = StaticShipInfoService(
            testDiskCacheProperties,
            testAppProperties,
            csiClient,
            slackMessageService,
            shipCacheService,
            tempFileDataSource
        ).also {
            runBlocking { it.onStartup() }
        }

        val res = staticShipInfoService.applyFilter(filter)
        assertEquals(imos, res, message)
    }

    @Test
    fun `Make sure ships without an IMO are correctly added to the MMSI cache`() {
        val csiClient: CSIClient = mock()
        val slackMessageService: SlackMessageService = mock()
        val tempFileDataSource: TempFileDataSource = mock()

        doReturn(allCSIShips).whenever(csiClient).listShipRegisterInfoCache()
        doReturn(allCSIMmsiMapping).whenever(csiClient).listShipRegisterMapping()

        val shipCacheService = ShipCacheService()
        val staticShipInfoService = StaticShipInfoService(
            testDiskCacheProperties,
            testAppProperties,
            csiClient,
            slackMessageService,
            shipCacheService,
            tempFileDataSource
        ).also {
            runBlocking { it.onStartup() }
        }

        // Ships with IMO
        assertEquals(MMSI_2, staticShipInfoService.getShipDetailsByMMSI(MMSI_2)?.mmsi)
        assertEquals(MMSI_3, staticShipInfoService.getShipDetailsByMMSI(MMSI_3)?.mmsi)

        // Ships without IMO
        assertEquals(MMSI_6, staticShipInfoService.getShipDetailsByMMSI(MMSI_6)?.mmsi)
        assertEquals(MMSI_7, staticShipInfoService.getShipDetailsByMMSI(MMSI_7)?.mmsi)
    }
}
