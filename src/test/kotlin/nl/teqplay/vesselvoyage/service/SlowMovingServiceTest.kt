package nl.teqplay.vesselvoyage.service

import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.whenever
import nl.teqplay.skeleton.model.Location
import nl.teqplay.vesselvoyage.client.driftpredictor.DriftPredictorClient
import nl.teqplay.vesselvoyage.model.internal.DriftSegment
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewSlowMovingPeriod
import nl.teqplay.vesselvoyage.model.v2.Speed
import nl.teqplay.vesselvoyage.util.createAisHistoricMessage
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.Duration
import java.time.Instant
import java.time.ZoneOffset

class SlowMovingServiceTest {
    val aisFetchingService = mock<AisFetchingService>()
    val driftPredictorClient = mock<DriftPredictorClient>()
    val slowMovingService = SlowMovingService(
        driftPredictorClient = driftPredictorClient,
        aisFetchingService = aisFetchingService
    )

    @Test
    fun `should return accurate slow moving period`() {
        val imo = 9235610
        val start = Instant.parse("2024-07-13T03:37:01Z")
        val end = Instant.parse("2024-07-13T12:05:28Z")
        val startLocation = Location(lat = 55.680245, lon = 21.137398333333334)
        val endLocation = Location(lat = 55.67643833333333, lon = 21.142736666666668)
        val aisHistory = listOf(
            createAisHistoricMessage(messageTime = Instant.parse("2024-07-13T03:37:01Z"), location = startLocation),
            createAisHistoricMessage(messageTime = Instant.parse("2024-07-13T04:53:19Z"), location = startLocation),
            createAisHistoricMessage(messageTime = Instant.parse("2024-07-13T10:44:08Z"), location = endLocation),
            createAisHistoricMessage(messageTime = Instant.parse("2024-07-13T12:04:23Z"), location = endLocation)
        )
        val driftSegments = listOf(
            DriftSegment(
                startTime = Instant.parse("2024-07-13T03:37:01Z"),
                endTime = Instant.parse("2024-07-13T04:52:19Z"),
                drifting = false
            ),
            DriftSegment(
                startTime = Instant.parse("2024-07-13T04:53:19Z"),
                endTime = Instant.parse("2024-07-13T10:44:08Z"),
                drifting = true
            ),
            DriftSegment(
                startTime = Instant.parse("2024-07-13T10:46:15Z"),
                endTime = Instant.parse("2024-07-13T12:04:23Z"),
                drifting = false
            )
        )

        whenever(aisFetchingService.getShipTrace(imo.toString(), start.atZone(ZoneOffset.UTC), end.atZone(ZoneOffset.UTC)))
            .thenReturn(aisHistory)
        whenever(driftPredictorClient.getSegmentPredictions(aisHistory))
            .thenReturn(driftSegments)

        val result = slowMovingService.determineSlowMovingSegments(imo, start, end)

        val expectedStart = Instant.parse("2024-07-13T04:53:19Z")
        val expectedEnd = Instant.parse("2024-07-13T10:44:08Z")
        val expected = listOf(
            NewSlowMovingPeriod(
                id = result.first().id,
                start = LocationTime(location = startLocation, time = expectedStart),
                end = LocationTime(location = endLocation, time = expectedEnd),
                speed = Speed(
                    min = 15.0F,
                    max = 15.0F,
                    avg = 15.0F,
                    count = 2,
                    lastSpeedOverGround = 15.0F,
                    lastSpeedOverGroundTimestamp = Instant.parse("2024-07-13T10:44:08Z"),
                    duration = Duration.between(expectedStart, expectedEnd)
                )
            )
        )
        assertEquals(expected, result)
    }

    @Test
    fun `should return no slow moving period`() {
        val imo = 9235610
        val start = Instant.parse("2024-07-13T03:37:01Z")
        val end = Instant.parse("2024-07-13T04:52:19Z")
        val startLocation = Location(lat = 55.680245, lon = 21.137398333333334)
        val endLocation = Location(lat = 55.67643833333333, lon = 21.142736666666668)
        val aisHistory = listOf(
            createAisHistoricMessage(messageTime = Instant.parse("2024-07-13T03:37:01Z"), location = startLocation),
            createAisHistoricMessage(messageTime = Instant.parse("2024-07-13T04:52:19Z"), location = endLocation)
        )
        val driftSegments = listOf(
            DriftSegment(
                startTime = Instant.parse("2024-07-13T03:37:01Z"),
                endTime = Instant.parse("2024-07-13T04:52:19Z"),
                drifting = false
            )
        )

        whenever(aisFetchingService.getShipTrace(imo.toString(), start.atZone(ZoneOffset.UTC), end.atZone(ZoneOffset.UTC)))
            .thenReturn(aisHistory)
        whenever(driftPredictorClient.getSegmentPredictions(aisHistory))
            .thenReturn(driftSegments)

        val result = slowMovingService.determineSlowMovingSegments(imo, start, end)

        val expected = emptyList<NewSlowMovingPeriod>()
        assertEquals(expected, result)
    }
}
