package nl.teqplay.vesselvoyage.service.queue.events

import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.whenever
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import nl.teqplay.aisengine.event.model.StopStartEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.model.identifier.GeneralShipIdentifier
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusAtaEvent
import nl.teqplay.skeleton.model.Location
import nl.teqplay.vesselvoyage.datasource.ShipBlacklistDatasource
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.v2.VisitChange
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.EntryProcessingService
import nl.teqplay.vesselvoyage.service.ImoLockService
import nl.teqplay.vesselvoyage.util.createNewVisit
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.Instant

class EventsDefaultMessageProcessorTest {
    private val testMeterRegistry = SimpleMeterRegistry()
    private val eventProcessingProperties = mock<EventProcessingProperties>().apply {
        whenever(this.enableNewDefinition).thenReturn(true)
        whenever(this.totalThreads).thenReturn(1)
    }
    private val entryProcessingService = mock<EntryProcessingService>()
    private val imoLockService = ImoLockService()
    private val messageProcessor = EventsDefaultMessageProcessor(
        processingProperties = eventProcessingProperties,
        entryProcessingService = entryProcessingService,
        imoLockService = imoLockService,
        meterRegistry = testMeterRegistry,
        shipBlacklistDatasource = mock<ShipBlacklistDatasource>()
    )

    @Test
    fun `should process supported event`() {
        whenever(entryProcessingService.insert(any(), any(), any(), any(), any()))
            .thenReturn(emptyList())
        whenever(entryProcessingService.insertNew(any(), any()))
            .thenReturn(listOf(VisitChange(Action.CREATE, createNewVisit())))

        val event = StopStartEvent(
            _id = "TEST_ID",
            ship = AisShipIdentifier(111111111, 1234567),
            location = Location(0.0, 0.0),
            actualTime = Instant.now()
        )
        messageProcessor.processEvent(event, 1234567)

        assertEquals(1L, messageProcessor.eventInputCount.get())
        assertEquals(1L, messageProcessor.eventProcessedCount.get())
        assertEquals(0L, messageProcessor.eventDroppedCount.get())
        assertEquals(0L, messageProcessor.oldChangesOutputCount.get())
        assertEquals(1L, messageProcessor.changesOutputCount.get())
    }

    @Test
    fun `should process supported event no new definition`() {
        whenever(entryProcessingService.insert(any(), any(), any(), any(), any()))
            .thenReturn(emptyList())
        whenever(eventProcessingProperties.enableNewDefinition).thenReturn(false)

        val event = StopStartEvent(
            _id = "TEST_ID",
            ship = AisShipIdentifier(111111111, 1234567),
            location = Location(0.0, 0.0),
            actualTime = Instant.now()
        )
        messageProcessor.processEvent(event, 1234567)

        assertEquals(1L, messageProcessor.eventInputCount.get())
        assertEquals(1L, messageProcessor.eventProcessedCount.get())
        assertEquals(0L, messageProcessor.eventDroppedCount.get())
        assertEquals(0L, messageProcessor.oldChangesOutputCount.get())
        assertEquals(0L, messageProcessor.changesOutputCount.get())
    }

    @Test
    fun `should drop unsupported event`() {
        whenever(entryProcessingService.insert(any(), any(), any(), any(), any()))
            .thenReturn(emptyList())
        whenever(entryProcessingService.insertNew(any(), any()))
            .thenReturn(listOf(VisitChange(Action.CREATE, createNewVisit())))

        val event = PortcallPlusAtaEvent(
            _id = "TEST_ID",
            portcallId = "TEST_PORTCALL",
            ship = GeneralShipIdentifier(111111111, 1234567),
            area = AreaIdentifier(null, AreaIdentifier.AreaType.BERTH, "TEST_BERTH"),
            port = AreaIdentifier(null, AreaIdentifier.AreaType.PORT, "TEST_PORT"),
            actualTime = Instant.now()
        )
        messageProcessor.processEvent(event, 1234567)

        assertEquals(1L, messageProcessor.eventInputCount.get())
        assertEquals(0L, messageProcessor.eventProcessedCount.get())
        assertEquals(1L, messageProcessor.eventDroppedCount.get())
        assertEquals(0L, messageProcessor.oldChangesOutputCount.get())
        assertEquals(0L, messageProcessor.changesOutputCount.get())
    }
}
