package nl.teqplay.vesselvoyage.service.processing.dsl

import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.vesselvoyage.logic.TestPort
import nl.teqplay.vesselvoyage.logic.createAnchorEvent
import nl.teqplay.vesselvoyage.logic.createEndOfSeaPassageEvent
import nl.teqplay.vesselvoyage.logic.createPortEventEnd
import nl.teqplay.vesselvoyage.logic.createPortEventStart
import nl.teqplay.vesselvoyage.logic.createStopEvent
import nl.teqplay.vesselvoyage.logic.createUniqueBerthEvent
import nl.teqplay.vesselvoyage.model.EventStatus
import nl.teqplay.vesselvoyage.model.Location
import nl.teqplay.vesselvoyage.util.toSkeletonLocation
import java.time.Duration
import java.time.Instant
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit
import java.util.UUID
import nl.teqplay.skeleton.model.Location as SkeletonLocation

fun given() = ScenarioBuilder()

val PORT_MAIN = TestPort(portId = "AAAAA", location = Location(0.0, 0.0))
val PORT_MAIN_2 = TestPort(portId = "BBBBB", location = Location(1.0, 1.0))
val PORT_MAIN_3 = TestPort(portId = "CCCCC", location = Location(2.0, 2.0))

data class ScenarioBuilder(
    val events: MutableList<MutableList<Event>> = mutableListOf(mutableListOf()),
    var time: ZonedDateTime = Instant.EPOCH.atZone(ZoneOffset.UTC),
    val concurrent: Boolean = false
) {
    var lastStopStartEventId: String = "NOT_SET"

    fun at(time: String): ScenarioBuilder {
        require(!concurrent) { "Can't call `at` within `concurrent` block" }
        val instant = ZonedDateTime.parse(time)
        require(instant.isAfter(this.time)) { "Time should not move backwards." }
        this.time = instant
        return this
    }

    fun fastForward(amount: Long, unit: ChronoUnit): ScenarioBuilder {
        require(!concurrent) { "Can't call `fastForward` within `concurrent` block" }
        require(amount > 0) { "Must have positive amount to move forward." }
        time += Duration.of(amount, unit)
        return this
    }

    fun concurrent(action: ScenarioBuilder.() -> Unit): ScenarioBuilder {
        require(!concurrent) { "Can't call `concurrent` within another `concurrent` block" }
        val concurrentBuilder = ScenarioBuilder(concurrent = true, time = time)
        action(concurrentBuilder)
        calculatePermutations(events, concurrentBuilder.events.flatten())
        return this
    }

    private fun addEvent(event: Event) {
        events.forEach { it.add(event) }
    }

    private fun generateRandomEventId(): String {
        return UUID.randomUUID().toString()
    }

    fun enterEos(port: TestPort = PORT_MAIN): ScenarioBuilder {

        addEvent(createEndOfSeaPassageEvent(generateRandomEventId(), EventStatus.START, time, port, "${port.portId}.eosp"))
        return this
    }

    fun exitEos(port: TestPort = PORT_MAIN): ScenarioBuilder {
        addEvent(createEndOfSeaPassageEvent(generateRandomEventId(), EventStatus.END, time, port, "${port.portId}.eosp"))
        return this
    }

    fun startStop(location: SkeletonLocation = PORT_MAIN.location.toSkeletonLocation()): ScenarioBuilder {
        lastStopStartEventId = generateRandomEventId()
        val stopEvent = createStopEvent(
            _id = lastStopStartEventId,
            status = EventStatus.START,
            time = time,
            location = location
        )
        addEvent(stopEvent)
        return this
    }

    fun endStop(location: SkeletonLocation = PORT_MAIN.location.toSkeletonLocation()): ScenarioBuilder {
        val stopEvent = createStopEvent(
            _id = generateRandomEventId(),
            startEventId = lastStopStartEventId,
            status = EventStatus.END,
            time = time,
            location = location,
            actualLocation = location
        )
        addEvent(stopEvent)
        return this
    }

    fun enterPort(port: TestPort = PORT_MAIN): ScenarioBuilder {
        addEvent(createPortEventStart(port, time))
        return this
    }

    fun exitPort(port: TestPort = PORT_MAIN): ScenarioBuilder {
        addEvent(createPortEventEnd(port, time))
        return this
    }

    fun enterAnchorage(): ScenarioBuilder {
        addEvent(createAnchorEvent(generateRandomEventId(), EventStatus.START, time))
        return this
    }

    fun exitAnchorage(): ScenarioBuilder {
        addEvent(createAnchorEvent(generateRandomEventId(), EventStatus.END, time))
        return this
    }

    fun enterBerth(): ScenarioBuilder {
        addEvent(createUniqueBerthEvent(generateRandomEventId(), EventStatus.START, time, port = PORT_MAIN))
        return this
    }

    fun exitBerth(): ScenarioBuilder {
        addEvent(createUniqueBerthEvent(generateRandomEventId(), EventStatus.END, time, port = PORT_MAIN))
        return this
    }

    fun build(): List<List<Event>> = events

    private fun calculatePermutations(
        permutations: MutableList<MutableList<Event>>,
        groupedEvents: List<Event>
    ) {
        if (groupedEvents.size == 1) {
            permutations.forEach { permutation -> permutation.add(groupedEvents.first()) }
        } else {
            val basePermutations = permutations.toList()
            permutations.clear()

            val groupPermutations = groupedEvents.permutations()
            groupPermutations.forEach { groupPermutation ->
                basePermutations.forEach {
                    permutations.add((it + groupPermutation).toMutableList())
                }
            }
        }
    }

    /**
     * Resource: https://stackoverflow.com/a/76639299
     */
    private fun <E> List<E>.permutations(builtSequence: List<E> = listOf()): List<List<E>> =
        if (isEmpty()) listOf(builtSequence)
        else flatMap { (this - it).permutations(builtSequence + it) }
}
