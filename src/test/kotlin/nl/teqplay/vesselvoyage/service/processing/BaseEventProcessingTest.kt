package nl.teqplay.vesselvoyage.service.processing

import com.nhaarman.mockitokotlin2.mock
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import nl.teqplay.vesselvoyage.datasource.ProcessorLogDatasource
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.AisFetchingService
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.StaticShipInfoService
import nl.teqplay.vesselvoyage.service.processing.anchor.AnchorProcessor
import nl.teqplay.vesselvoyage.service.processing.anchorarea.AnchorAreaProcessor
import nl.teqplay.vesselvoyage.service.processing.approach.ApproachAreaProcessor
import nl.teqplay.vesselvoyage.service.processing.berth.UniqueBerthProcessor
import nl.teqplay.vesselvoyage.service.processing.destination.DestinationChangedProcessor
import nl.teqplay.vesselvoyage.service.processing.encounter.EncounterProcessor
import nl.teqplay.vesselvoyage.service.processing.eosp.EndOfSeaPassageProcessor
import nl.teqplay.vesselvoyage.service.processing.eta.EtaProcessor
import nl.teqplay.vesselvoyage.service.processing.lock.LockAreaProcessor
import nl.teqplay.vesselvoyage.service.processing.movement.MovementProcessor
import nl.teqplay.vesselvoyage.service.processing.pilot.PilotAreaProcessor
import nl.teqplay.vesselvoyage.service.processing.port.PortProcessor
import nl.teqplay.vesselvoyage.service.processing.shiptoship.ShipToShipTransferProcessor
import nl.teqplay.vesselvoyage.service.processing.status.StatusChangedProcessor
import nl.teqplay.vesselvoyage.service.processing.stop.StopProcessor
import nl.teqplay.vesselvoyage.service.processing.stop.merging.AnchorStopMergingTactic
import nl.teqplay.vesselvoyage.service.processing.stop.merging.JitterStopMergingTactic
import nl.teqplay.vesselvoyage.service.processing.stop.merging.SimpleStopMergingTactic
import nl.teqplay.vesselvoyage.service.processing.terminalmooring.TerminalMooringAreaProcessor
import java.time.Duration

abstract class BaseEventProcessingTest {
    protected val config = EventProcessingProperties(
        maxSpeedMps = 1.0,
        minDuration = Duration.ofMinutes(30),
        enableTraceCalculations = true,
        enableSlowMovingPeriods = false,
        newStopDetection = true,
        enableNewDefinition = true,
        enableOldDefinition = true,
        totalThreads = 5,
        logResults = false,
        activenessMonitoring = EventProcessingProperties.ActivenessMonitoring(
            enabled = false,
            interval = 1000,
            maxIdleTime = Duration.ofMinutes(5)
        )
    )

    protected val infraService = mock<InfraService>()
    protected val aisFetchingService = mock<AisFetchingService>()
    protected val staticShipInfoService = mock<StaticShipInfoService>()
    protected val processorLogDatasource = mock<ProcessorLogDatasource>()

    protected val service = EventProcessingService(
        AnchorProcessor(config, infraService),
        DestinationChangedProcessor(config),
        EncounterProcessor(config, SimpleMeterRegistry()),
        ShipToShipTransferProcessor(config, SimpleMeterRegistry()),
        EtaProcessor(config),
        MovementProcessor(config, infraService, aisFetchingService),
        PortProcessor(config, infraService, aisFetchingService, staticShipInfoService),
        StatusChangedProcessor(config),
        UniqueBerthProcessor(config, infraService, aisFetchingService),
        EndOfSeaPassageProcessor(config, infraService),
        StopProcessor(config, SimpleMeterRegistry(), infraService, listOf(SimpleStopMergingTactic(), JitterStopMergingTactic(), AnchorStopMergingTactic())),
        PilotAreaProcessor(config),
        AnchorAreaProcessor(config),
        TerminalMooringAreaProcessor(config),
        LockAreaProcessor(config),
        ApproachAreaProcessor(config),
        processorLogDatasource,
        eventProcessingProperties = mock()
    )
}
