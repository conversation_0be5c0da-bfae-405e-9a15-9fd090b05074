package nl.teqplay.vesselvoyage.service.processing

import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.eq
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.whenever
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import nl.teqplay.aisengine.event.interfaces.AnchoredEvent
import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.interfaces.StopEvent
import nl.teqplay.aisengine.event.interfaces.UniqueBerthEvent
import nl.teqplay.aisengine.event.model.AnchoredStartEvent
import nl.teqplay.aisengine.event.model.AreaStartEvent
import nl.teqplay.aisengine.event.model.StopStartEvent
import nl.teqplay.aisengine.event.model.UniqueBerthStartEvent
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.vesselvoyage.datasource.ProcessorLogDatasource
import nl.teqplay.vesselvoyage.logic.createBerth
import nl.teqplay.vesselvoyage.logic.createPort
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.internal.ClassifiedStop
import nl.teqplay.vesselvoyage.model.internal.InfraAreaType
import nl.teqplay.vesselvoyage.model.v2.ESoFChange
import nl.teqplay.vesselvoyage.model.v2.EntryESoFWrapper
import nl.teqplay.vesselvoyage.model.v2.NewChange
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewStopType
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import nl.teqplay.vesselvoyage.model.v2.VisitChange
import nl.teqplay.vesselvoyage.model.v2.VoyageChange
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.AisFetchingService
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.StaticShipInfoService
import nl.teqplay.vesselvoyage.service.processing.anchor.AnchorProcessor
import nl.teqplay.vesselvoyage.service.processing.anchorarea.AnchorAreaProcessor
import nl.teqplay.vesselvoyage.service.processing.approach.ApproachAreaProcessor
import nl.teqplay.vesselvoyage.service.processing.berth.UniqueBerthProcessor
import nl.teqplay.vesselvoyage.service.processing.destination.DestinationChangedProcessor
import nl.teqplay.vesselvoyage.service.processing.dsl.ExpectedActivity
import nl.teqplay.vesselvoyage.service.processing.dsl.ExpectedEntry
import nl.teqplay.vesselvoyage.service.processing.dsl.ExpectedVisit
import nl.teqplay.vesselvoyage.service.processing.dsl.ExpectedVoyage
import nl.teqplay.vesselvoyage.service.processing.dsl.ExpectedWrapper
import nl.teqplay.vesselvoyage.service.processing.dsl.PORT_MAIN
import nl.teqplay.vesselvoyage.service.processing.dsl.PORT_MAIN_2
import nl.teqplay.vesselvoyage.service.processing.dsl.PORT_MAIN_3
import nl.teqplay.vesselvoyage.service.processing.dsl.expect
import nl.teqplay.vesselvoyage.service.processing.dsl.given
import nl.teqplay.vesselvoyage.service.processing.encounter.EncounterProcessor
import nl.teqplay.vesselvoyage.service.processing.eosp.EndOfSeaPassageProcessor
import nl.teqplay.vesselvoyage.service.processing.eta.EtaProcessor
import nl.teqplay.vesselvoyage.service.processing.lock.LockAreaProcessor
import nl.teqplay.vesselvoyage.service.processing.movement.MovementProcessor
import nl.teqplay.vesselvoyage.service.processing.pilot.PilotAreaProcessor
import nl.teqplay.vesselvoyage.service.processing.port.PortProcessor
import nl.teqplay.vesselvoyage.service.processing.shiptoship.ShipToShipTransferProcessor
import nl.teqplay.vesselvoyage.service.processing.status.StatusChangedProcessor
import nl.teqplay.vesselvoyage.service.processing.stop.StopProcessor
import nl.teqplay.vesselvoyage.service.processing.stop.merging.AnchorStopMergingTactic
import nl.teqplay.vesselvoyage.service.processing.stop.merging.JitterStopMergingTactic
import nl.teqplay.vesselvoyage.service.processing.stop.merging.SimpleStopMergingTactic
import nl.teqplay.vesselvoyage.service.processing.terminalmooring.TerminalMooringAreaProcessor
import nl.teqplay.vesselvoyage.util.toSkeletonLocation
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.time.Duration
import java.time.YearMonth
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit.DAYS
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class EventBufferScenarioTest {

    private val processingTestTime = YearMonth.of(2025, 1)
        .atDay(1)
        .atStartOfDay()
        .atZone(ZoneOffset.UTC)
        .toInstant()

    data class Scenario(
        val usecase: String,
        val events: List<List<Event>>,
        val expected: List<ExpectedWrapper>,
    )

    data class FlatScenario(
        val usecase: String,
        val events: List<Event>,
        val expected: List<ExpectedWrapper>,
    )

    private fun inputScenarios() = listOf(
        Scenario(
            usecase = "ship enters/leaves EOS and port at the same time (initial status)",
            events = given()
                .concurrent { enterEos().enterPort() }
                .fastForward(1, DAYS)
                .startStop()
                .fastForward(1, DAYS)
                .endStop()
                .fastForward(1, DAYS)
                .concurrent { exitPort().exitEos() }
                .build(),
            expected = expect()
                .startVisit()
                .enterPort()
                .fastForward(1, DAYS)
                .startStop()
                .fastForward(1, DAYS)
                .endStop()
                .fastForward(1, DAYS)
                .exitPort()
                .endVisit()
                .startVoyage()
                .build()
        ),
        Scenario(
            usecase = "ship enters/leaves EOS, port and berth at the same time (voyage status)",
            events = given()
                // setup to start with voyage
                .concurrent { enterEos().enterPort() }.fastForward(1, DAYS)
                .startStop().fastForward(1, DAYS)
                .concurrent { endStop().exitEos().exitPort() }.fastForward(1, DAYS)
                .concurrent { enterEos().enterPort().enterBerth() }.fastForward(1, DAYS)
                .startStop().fastForward(1, DAYS)
                .endStop().fastForward(1, DAYS)
                .concurrent { exitBerth().exitPort().exitEos() }
                .build(),
            expected = expect()
                .startVisit()
                .enterPort()
                .fastForward(1, DAYS)
                .startStop().fastForward(1, DAYS)
                .endStop()
                .exitPort()
                .endVisit()
                .startVoyage().fastForward(1, DAYS).endVoyage()
                .startVisit()
                .enterPort()
                .enterBerth()
                .fastForward(1, DAYS)
                .startStop()
                .fastForward(1, DAYS)
                .endStop()
                .fastForward(1, DAYS)
                .exitBerth()
                .exitPort()
                .endVisit()
                .startVoyage()
                .build()
        ),
        Scenario(
            usecase = "ship enters/leaves EOS and berth at the same time (initial status)",
            events = given()
                .concurrent { enterEos().enterBerth().startStop() }
                .fastForward(1, DAYS)
                .concurrent { endStop().exitBerth().exitEos() }
                .build(),
            expected = expect()
                .startVisit()
                .enterBerth()
                .startStop()
                .fastForward(1, DAYS)
                .endStop()
                .exitBerth()
                .endVisit()
                .startVoyage()
                .build()
        ),
        Scenario(
            usecase = "ship enters/leaves EOS and berth at the same time (voyage status)",
            events = given()
                // setup to start with voyage
                .enterEos().fastForward(1, DAYS)
                .startStop().fastForward(1, DAYS)
                .endStop()
                .exitEos().fastForward(1, DAYS)
                .concurrent { enterEos().enterBerth() }.fastForward(1, DAYS)
                .startStop().fastForward(1, DAYS)
                .endStop().fastForward(1, DAYS)
                .concurrent { exitBerth().exitEos() }
                .build(),
            expected = expect()
                .startVisit().fastForward(1, DAYS)
                .startStop().fastForward(1, DAYS)
                .endStop().fastForward(1, DAYS)
                .enterBerth()
                .fastForward(1, DAYS)
                .startStop()
                .fastForward(1, DAYS)
                .endStop()
                .fastForward(1, DAYS)
                .exitBerth()
                .endVisit()
                .startVoyage()
                .build()
        ),
        Scenario(
            usecase = "ship enters/leaves EOS and anchorage at the same time (initial status)",
            events = given()
                .concurrent { enterEos().enterAnchorage() }
                .fastForward(1, DAYS)
                .startStop().fastForward(1, DAYS)
                .endStop().fastForward(1, DAYS)
                .concurrent { exitAnchorage().exitEos() }
                .build(),
            expected = expect()
                .startVisit()
                .enterAnchorage()
                .fastForward(1, DAYS)
                .startStop()
                .fastForward(1, DAYS)
                .endStop()
                .fastForward(1, DAYS)
                .exitAnchorage()
                .endVisit()
                .startVoyage()
                .build()
        ),
        Scenario(
            usecase = "ship enters/leaves EOS and anchorage at the same time (voyage status)",
            events = given()
                // setup to start with voyage
                .enterEos().fastForward(1, DAYS)
                .startStop().fastForward(1, DAYS)
                .endStop()
                .exitEos().fastForward(1, DAYS)
                .concurrent { enterEos().enterAnchorage() }
                .fastForward(1, DAYS)
                .startStop().fastForward(1, DAYS)
                .endStop().fastForward(1, DAYS)
                .concurrent { exitAnchorage().exitEos() }
                .build(),
            expected = expect()
                .startVisit().fastForward(1, DAYS)
                .startStop().fastForward(1, DAYS)
                .endStop()
                .fastForward(1, DAYS)
                .enterAnchorage()
                .fastForward(1, DAYS)
                .startStop()
                .fastForward(1, DAYS)
                .endStop()
                .fastForward(1, DAYS)
                .exitAnchorage()
                .endVisit()
                .startVoyage()
                .build()
        ),

        Scenario(
            usecase = "ship enters/leaves EOS of overlapping ports",
            events = given()
                .concurrent { enterEos().enterPort() }
                .fastForward(1, DAYS)
                .startStop()
                .fastForward(1, DAYS)
                .endStop()
                .fastForward(1, DAYS)
                .exitPort()
                .fastForward(1, DAYS)
                .concurrent { enterEos(PORT_MAIN_2).enterEos(PORT_MAIN_3) }
                .fastForward(1, DAYS)
                .concurrent { exitEos() }
                .fastForward(1, DAYS)
                .enterPort(PORT_MAIN_2)
                .fastForward(1, DAYS)
                .startStop(PORT_MAIN_2.location.toSkeletonLocation())
                .fastForward(1, DAYS)
                .endStop(PORT_MAIN_2.location.toSkeletonLocation())
                .fastForward(1, DAYS)
                .exitPort(PORT_MAIN_2)
                .fastForward(1, DAYS)
                .exitEos(PORT_MAIN_2)
                .fastForward(1, DAYS)
                .exitEos(PORT_MAIN_3)
                .build(),
            expected = expect()
                .startVisit()
                .enterPort()
                .fastForward(1, DAYS)
                .startStop()
                .fastForward(1, DAYS)
                .endStop()
                .fastForward(1, DAYS)
                .exitPort()
                .fastForward(2, DAYS)
                .endVisit()
                .startVoyage()
                .endVoyage()
                .startVisit()
                .fastForward(1, DAYS)
                .enterPort()
                .fastForward(1, DAYS)
                .startStop()
                .fastForward(1, DAYS)
                .endStop()
                .fastForward(1, DAYS)
                .exitPort()
                .fastForward(1, DAYS)
                .endVisit()
                .startVoyage()
                .build()
        )
    )

    fun scenarios(): Stream<FlatScenario> {
        // Make a flat scenario for each permutation as we want to ensure each possible order the events can be ingested is tested
        return inputScenarios().flatMap { inputScenario ->
            inputScenario.events.map { permutation ->
                FlatScenario(
                    usecase = inputScenario.usecase,
                    events = permutation,
                    expected = inputScenario.expected
                )
            }
        }.stream()
    }

    @ParameterizedTest
    @MethodSource("scenarios")
    fun testScenario(scenario: FlatScenario) {
        val initialStatus = NewInitialShipStatus()
        val actual = processEvents(initialStatus, scenario.events)
        val eventsDescription = scenario.events.describe()

        assertThat(actual)
            .describedAs("${scenario.usecase}: $eventsDescription")
            .isEqualTo(scenario.expected)
    }

    private fun List<Event>.describe(): String = mapNotNull {
        when (it) {
            is UniqueBerthEvent -> if (it is UniqueBerthStartEvent) "berth(start)" else "berth(end)"
            is AreaEvent -> when (it.area.type) {
                AreaIdentifier.AreaType.END_OF_SEA_PASSAGE -> if (it is AreaStartEvent) "EOS(start)" else "EOS(end)"
                AreaIdentifier.AreaType.PORT -> if (it is AreaStartEvent) "port(start)" else "port(end)"
                else -> null
            }
            is AnchoredEvent -> if (it is AnchoredStartEvent) "anchor(start)" else "anchor(end)"
            is StopEvent -> if (it is StopStartEvent) "stop(start)" else "stop(end)"
            else -> null
        }
    }.joinToString(" > ")

    private fun processEvents(
        initialStatus: NewShipStatus,
        events: List<Event>
    ): List<ExpectedWrapper> {
        var status = initialStatus
        val wrappers = mutableListOf<EntryESoFWrapper<out NewEntry>>()
        events.forEach { event ->
            val result = service.onEventAndBuffer(status, event, processingTestTime)
            status = result.status
            result.changes.forEach { change ->
                val changeEntryId = when (change) {
                    is ESoFChange -> change.value._id
                    is VisitChange -> change.value._id
                    is VoyageChange -> change.value._id
                }
                val index = wrappers.indexOfLast { it.entry._id == changeEntryId }.takeUnless { it == -1 }
                when (change.action) {
                    Action.CREATE,
                    Action.UPDATE,
                    Action.REVISE -> createOrUpdate(change, index, wrappers)

                    Action.DELETE -> {
                        requireNotNull(index) { "Delete requires entry to exist." }
                        wrappers.removeAt(index)
                    }

                    Action.DISCONTINUE -> {}
                }
            }
        }
        return wrappers.map { ExpectedWrapper(it.entry.convertToExpected(), it.esof) }
    }

    private fun createOrUpdate(
        change: NewChange<*>,
        index: Int?,
        wrappers: MutableList<EntryESoFWrapper<out NewEntry>>
    ) {
        when (change) {
            is ESoFChange -> {
                requireNotNull(index) { "ESoF ${change.action} before entry exists." }
                wrappers[index] = wrappers[index].copy(esof = change.value)
            }

            is VisitChange -> {
                if (index == null) {
                    wrappers.add(EntryESoFWrapper(change.value, null))
                } else {
                    wrappers[index] = EntryESoFWrapper(change.value, wrappers[index].esof)
                }
            }

            is VoyageChange -> {
                if (index == null) {
                    wrappers.add(EntryESoFWrapper(change.value, null))
                } else {
                    wrappers[index] = EntryESoFWrapper(change.value, wrappers[index].esof)
                }
            }
        }
    }

    private fun NewEntry.convertToExpected(): ExpectedEntry = when (this) {
        is NewVisit -> ExpectedVisit(
            eosp = ExpectedActivity(
                start = start.time,
                end = end?.time
            ),
            port = portAreaActivities.map { ExpectedActivity(start = it.start.time, end = it.end?.time) },
            anchor = anchorAreaActivities.map { ExpectedActivity(start = it.start.time, end = it.end?.time) },
            berth = berthAreaActivities.map { ExpectedActivity(start = it.start.time, end = it.end?.time) },
            stops = stops.map { ExpectedActivity(start = it.start.time, end = it.end?.time) },
        )

        is NewVoyage -> ExpectedVoyage(
            start = start.time,
            end = end?.time
        )
    }

    private val config = EventProcessingProperties(
        maxSpeedMps = 1.0,
        minDuration = Duration.ofMinutes(30),
        enableTraceCalculations = true,
        enableSlowMovingPeriods = false,
        newStopDetection = true,
        enableNewDefinition = true,
        enableOldDefinition = true,
        totalThreads = 5,
        logResults = false,
        activenessMonitoring = EventProcessingProperties.ActivenessMonitoring(
            enabled = false,
            interval = 1000,
            maxIdleTime = Duration.ofMinutes(5)
        )
    )

    private val infraService = mock<InfraService>().apply {
        whenever(isPortMainPort(any())).thenReturn(true)
        whenever(getPortByUnlocode(eq(PORT_MAIN.portId))).thenReturn(createPort(_id = PORT_MAIN.portId))
        whenever(getPortByUnlocode(eq(PORT_MAIN_2.portId))).thenReturn(createPort(_id = PORT_MAIN_2.portId))

        whenever(findStopClassification(eq(PORT_MAIN.location.toSkeletonLocation())))
            .thenReturn(ClassifiedStop(areaId = "TEST_STOP", type = NewStopType.BERTH))
        whenever(findStopClassification(eq(PORT_MAIN_2.location.toSkeletonLocation())))
            .thenReturn(ClassifiedStop(areaId = "TEST_STOP_2", type = NewStopType.BERTH))

        whenever(getById(eq("TEST_STOP"), eq(InfraAreaType.BERTH)))
            .thenReturn(createBerth(mainPort = PORT_MAIN.portId))
        whenever(getById(eq("TEST_STOP_2"), eq(InfraAreaType.BERTH)))
            .thenReturn(createBerth(mainPort = PORT_MAIN_2.portId))

        whenever(getMainPortFromBerth(any())).thenCallRealMethod()
    }
    private val aisFetchingService = mock<AisFetchingService>()
    private val staticShipInfoService = mock<StaticShipInfoService>()
    private val processorLogDatasource = mock<ProcessorLogDatasource>()

    private val service = EventProcessingService(
        AnchorProcessor(config, infraService),
        DestinationChangedProcessor(config),
        EncounterProcessor(config, SimpleMeterRegistry()),
        ShipToShipTransferProcessor(config, SimpleMeterRegistry()),
        EtaProcessor(config),
        MovementProcessor(config, infraService, aisFetchingService),
        PortProcessor(config, infraService, aisFetchingService, staticShipInfoService),
        StatusChangedProcessor(config),
        UniqueBerthProcessor(config, infraService, aisFetchingService),
        EndOfSeaPassageProcessor(config, infraService),
        StopProcessor(config, SimpleMeterRegistry(), infraService, listOf(SimpleStopMergingTactic(), JitterStopMergingTactic(), AnchorStopMergingTactic())),
        PilotAreaProcessor(config),
        AnchorAreaProcessor(config),
        TerminalMooringAreaProcessor(config),
        LockAreaProcessor(config),
        ApproachAreaProcessor(config),
        processorLogDatasource,
        eventProcessingProperties = mock()
    )
}
