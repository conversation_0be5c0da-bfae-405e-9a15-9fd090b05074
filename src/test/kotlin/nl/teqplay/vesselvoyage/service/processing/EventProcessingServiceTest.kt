package nl.teqplay.vesselvoyage.service.processing

import com.fasterxml.jackson.module.kotlin.readValue
import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.eq
import com.nhaarman.mockitokotlin2.reset
import com.nhaarman.mockitokotlin2.whenever
import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.model.AnchoredEndEvent
import nl.teqplay.aisengine.event.model.AnchoredStartEvent
import nl.teqplay.aisengine.event.model.AreaEndEvent
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.platform.model.ShipInfo.ShipStatus
import nl.teqplay.skeleton.model.Location
import nl.teqplay.vesselvoyage.logic.ANCHOR_AREA_1
import nl.teqplay.vesselvoyage.logic.DEFAULT_ACTUAL_TIME
import nl.teqplay.vesselvoyage.logic.DEFAULT_END_TIME
import nl.teqplay.vesselvoyage.logic.DEFAULT_START_TIME
import nl.teqplay.vesselvoyage.logic.IMO_1
import nl.teqplay.vesselvoyage.logic.IMO_2
import nl.teqplay.vesselvoyage.logic.IMO_3
import nl.teqplay.vesselvoyage.logic.MMSI_1
import nl.teqplay.vesselvoyage.logic.MMSI_2
import nl.teqplay.vesselvoyage.logic.MMSI_3
import nl.teqplay.vesselvoyage.logic.PORT_BEANR
import nl.teqplay.vesselvoyage.logic.PORT_NLRTM
import nl.teqplay.vesselvoyage.logic.createAnchorAreaEvent
import nl.teqplay.vesselvoyage.logic.createAnchorEvent
import nl.teqplay.vesselvoyage.logic.createAnchorEventEnd
import nl.teqplay.vesselvoyage.logic.createApproachAreaEvent
import nl.teqplay.vesselvoyage.logic.createAreaPortEvent
import nl.teqplay.vesselvoyage.logic.createDestinationChangedEvent
import nl.teqplay.vesselvoyage.logic.createEncounterEvent
import nl.teqplay.vesselvoyage.logic.createEndOfSeaPassageEvent
import nl.teqplay.vesselvoyage.logic.createEtaEvent
import nl.teqplay.vesselvoyage.logic.createLockAreaEvent
import nl.teqplay.vesselvoyage.logic.createMovementEvent
import nl.teqplay.vesselvoyage.logic.createPilotAreaEvent
import nl.teqplay.vesselvoyage.logic.createPort
import nl.teqplay.vesselvoyage.logic.createShipToShipTransferEvent
import nl.teqplay.vesselvoyage.logic.createStatusChangedEvent
import nl.teqplay.vesselvoyage.logic.createStopEvent
import nl.teqplay.vesselvoyage.logic.createTerminalMooringAreaEvent
import nl.teqplay.vesselvoyage.logic.createUniqueBerthEvent
import nl.teqplay.vesselvoyage.logic.generateUniqueId
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.EventStatus
import nl.teqplay.vesselvoyage.model.MovementStatus
import nl.teqplay.vesselvoyage.model.NewEventProcessingResult
import nl.teqplay.vesselvoyage.model.createVisitId
import nl.teqplay.vesselvoyage.model.internal.ClassifiedStop
import nl.teqplay.vesselvoyage.model.v2.AreaActivity
import nl.teqplay.vesselvoyage.model.v2.Destination
import nl.teqplay.vesselvoyage.model.v2.ESoFChange
import nl.teqplay.vesselvoyage.model.v2.EntryESoFWrapper
import nl.teqplay.vesselvoyage.model.v2.FallbackType
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewESoF
import nl.teqplay.vesselvoyage.model.v2.NewEncounter
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewStop
import nl.teqplay.vesselvoyage.model.v2.NewStopType
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.ShipToShipTransfer
import nl.teqplay.vesselvoyage.model.v2.VisitChange
import nl.teqplay.vesselvoyage.model.v2.VoyageChange
import nl.teqplay.vesselvoyage.service.processing.anchor.AnchorEndProcessor
import nl.teqplay.vesselvoyage.service.processing.anchor.AnchorStartProcessor
import nl.teqplay.vesselvoyage.service.processing.anchorarea.AnchorAreaBaseProcessor
import nl.teqplay.vesselvoyage.service.processing.approach.ApproachAreaBaseProcessor
import nl.teqplay.vesselvoyage.service.processing.berth.UniqueBerthEndProcessor
import nl.teqplay.vesselvoyage.service.processing.berth.UniqueBerthStartProcessor
import nl.teqplay.vesselvoyage.service.processing.encounter.EncounterBaseProcessor
import nl.teqplay.vesselvoyage.service.processing.eosp.EndOfSeaPassageEndProcessor
import nl.teqplay.vesselvoyage.service.processing.lock.LockAreaBaseProcessor
import nl.teqplay.vesselvoyage.service.processing.pilot.PilotAreaBaseProcessor
import nl.teqplay.vesselvoyage.service.processing.port.PortEndProcessor
import nl.teqplay.vesselvoyage.service.processing.port.PortStartProcessor
import nl.teqplay.vesselvoyage.service.processing.stop.StopBaseProcessor
import nl.teqplay.vesselvoyage.service.processing.terminalmooring.TerminalMooringAreaBaseProcessor
import nl.teqplay.vesselvoyage.util.DEFAULT_TEST_PORT_ID
import nl.teqplay.vesselvoyage.util.Timeline
import nl.teqplay.vesselvoyage.util.createAreaActivity
import nl.teqplay.vesselvoyage.util.createEmptyEsof
import nl.teqplay.vesselvoyage.util.createLocationTime
import nl.teqplay.vesselvoyage.util.createNewESoF
import nl.teqplay.vesselvoyage.util.createNewStop
import nl.teqplay.vesselvoyage.util.createNewVisit
import nl.teqplay.vesselvoyage.util.createNewVoyage
import nl.teqplay.vesselvoyage.util.createNewVoyageShipStatus
import nl.teqplay.vesselvoyage.util.globalObjectMapper
import nl.teqplay.vesselvoyage.util.loadResource
import nl.teqplay.vesselvoyage.util.toSkeletonLocation
import nl.teqplay.vesselvoyage.util.toZonedDateTime
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Assertions.fail
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.time.Instant
import java.time.YearMonth
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit
import java.util.stream.Stream
import kotlin.reflect.KProperty1
import kotlin.reflect.jvm.javaField

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class EventProcessingServiceTest : BaseEventProcessingTest() {
    private val processingTestTime = YearMonth.of(2025, 1)
        .atDay(1)
        .atStartOfDay()
        .atZone(ZoneOffset.UTC)
        .toInstant()

    companion object {
        private const val eventId = "TEST_EVENT_ID"
        private const val eventId2 = "TEST_EVENT_ID2"
        private const val eospAreaId1 = "aaaaa-bbbbb-ccccc.eosp"
        private const val eospAreaId2 = "aaaaa-bbbbb-ddddd.eosp"
        private const val eospAreaId3 = "aaaaa-bbbbb-eeeee.eosp"
        private const val eospAreaId4 = "aaaaa-bbbbb-fffff.eosp"
        private const val eospAreaId5 = "aaaaa-bbbbb-ggggg.eosp"
        private val testEventLocation = Location(1.2345, 1.2345)
        private val testActualLocation = Location(2.2222, 2.2222)

        private val testDestination1 = Destination(
            time = DEFAULT_ACTUAL_TIME.toInstant(),
            ais = "Rotterdam",
            actual = "NLRTM"
        )
        private val testDestination2 = Destination(
            time = DEFAULT_START_TIME.toInstant(),
            ais = "Antwerp",
            actual = "BEANR"
        )
        private val testDestinationChangedEvent = createDestinationChangedEvent(
            time = DEFAULT_ACTUAL_TIME,
            oldDestination = null,
            newDestination = testDestination1.ais,
            newTrueDestination = testDestination1.actual
        )

        val portStartTime = DEFAULT_START_TIME.plusMonths(1).toInstant()
        val dayBeforePortStart = portStartTime.minus(1, ChronoUnit.DAYS)
        val dayAfterPortStart = portStartTime.plus(1, ChronoUnit.DAYS)
        val portEndTime = DEFAULT_START_TIME.plusMonths(2).toInstant()
        val dayBeforePortEnd = portEndTime.minus(1, ChronoUnit.DAYS)
        val dayAfterPortEnd = portEndTime.plus(1, ChronoUnit.DAYS)
    }

    @BeforeEach
    fun setUpMocks() {
        reset(infraService)
        infraService.apply {
            whenever(this.isPortMainPort(any())).thenReturn(true)
            whenever(this.findStopClassification(any())).thenReturn(ClassifiedStop(NewStopType.BERTH, "TEST_AREA_ID"))
            whenever(this.findStopClassification(eq(testActualLocation))).thenReturn(ClassifiedStop(NewStopType.ANCHOR_AREA, "TEST_ACTUAL_AREA_ID"))
        }
    }

    @Test
    fun `should process end of sea passage start event into visit status for new ship`() {
        val event = createEndOfSeaPassageEvent(
            _id = eventId,
            status = EventStatus.START,
            time = DEFAULT_START_TIME,
            areaId = eospAreaId1
        )

        val result = service.onEventAndBuffer(NewInitialShipStatus(), event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val visitStart = LocationTime(
            location = PORT_NLRTM.location.toSkeletonLocation(),
            time = DEFAULT_START_TIME.toInstant()
        )
        val expectedVisit = createNewVisit(
            _id = "$eventId.VISIT",
            imo = IMO_1.toInt(),
            start = visitStart,
            eospAreaActivity = AreaActivity(id = eventId, start = visitStart, null, eospAreaId1)
        )
        val expected = NewEventProcessingResult(
            status = NewVisitShipStatus(
                visit = EntryESoFWrapper(
                    entry = expectedVisit,
                    esof = null
                ),
                previousVoyage = null,
                previousVisit = null
            ),
            changes = listOf(
                VisitChange(Action.CREATE, expectedVisit)
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process end of sea passage start event into visit status on voyage status`() {
        val eospTestAreaId = "EOSP_TEST_AREA_ID"
        val eventTime = DEFAULT_START_TIME.plusDays(1)
        val event = createEndOfSeaPassageEvent(eventId, EventStatus.START, eventTime)
        val visitStart = LocationTime(
            location = PORT_NLRTM.location.toSkeletonLocation(),
            time = eventTime.toInstant()
        )
        val currentVoyage = createNewVoyage()
        val currentStatus = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(entry = currentVoyage, esof = null),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedUpdatedVoyage = currentVoyage.copy(
            end = visitStart,
            next = "$eventId.VISIT",
            destinationPort = eospTestAreaId
        )
        val expectedVisit = createNewVisit(
            _id = "$eventId.VISIT",
            imo = IMO_1.toInt(),
            start = visitStart,
            eospAreaActivity = AreaActivity(id = eventId, start = visitStart, null, eospTestAreaId),
            previous = "TEST_VOYAGE_ID"
        )
        val expected = NewEventProcessingResult(
            status = NewVisitShipStatus(
                visit = EntryESoFWrapper(
                    entry = expectedVisit,
                    esof = null
                ),
                previousVoyage = EntryESoFWrapper(
                    entry = expectedUpdatedVoyage,
                    esof = null
                ),
                previousVisit = null
            ),
            changes = listOf(
                VoyageChange(Action.UPDATE, expectedUpdatedVoyage),
                VisitChange(Action.CREATE, expectedVisit)
            ),
            readyForPostProcessing = listOf(expectedUpdatedVoyage._id)
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process end of sea passage end event into voyage status and setting a fallback on the stop on visit status with stop`() {
        val eventTime = DEFAULT_START_TIME.plusDays(1)
        val event = createEndOfSeaPassageEvent(
            _id = eventId,
            status = EventStatus.END,
            time = eventTime,
            areaId = eospAreaId1
        )
        val visitEnd = LocationTime(
            location = PORT_NLRTM.location.toSkeletonLocation(),
            time = eventTime.toInstant()
        )
        val currentStop = createNewStop(NewStopType.BERTH)
        val currentVisit = createNewVisit(
            eospAreaActivity = AreaActivity(id = eventId, start = createLocationTime(), end = null, areaId = eospAreaId1),
            stops = listOf(currentStop)
        )
        val currentOngoingEncounter = NewEncounter(
            type = EncounterType.PILOT,
            otherMmsi = MMSI_2.toInt(),
            otherImo = IMO_2.toInt(),
            startEventId = "test-event-id",
            start = LocationTime(
                location = PORT_NLRTM.location.toSkeletonLocation(),
                time = DEFAULT_START_TIME.toInstant()
            ),
            end = null
        )
        val currentVisitEsof = createNewESoF(_id = currentVisit._id, encounters = listOf(currentOngoingEncounter))
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = currentVisitEsof),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedStop = currentStop.copy(
            end = visitEnd.copy(fallback = FallbackType.ACTIVITY_END_BY_EOSP)
        )
        val expectedUpdatedVisit = currentVisit.copy(
            end = visitEnd,
            eospAreaActivity = currentVisit.eospAreaActivity.copy(
                end = visitEnd
            ),
            stops = listOf(expectedStop),
            next = "$eventId.VOYAGE"
        )
        val expectedUpdatedEncounter = currentOngoingEncounter.copy(end = visitEnd.copy(fallback = FallbackType.ACTIVITY_END_BY_EOSP))
        val expectedUpdatedVisitEsof = currentVisitEsof.copy(encounters = listOf(expectedUpdatedEncounter))
        val expectedVoyage = createNewVoyage(
            _id = "$eventId.VOYAGE",
            imo = IMO_1.toInt(),
            start = visitEnd,
            actualStart = visitEnd,
            previous = "TEST_VISIT_ID",
            originPort = eospAreaId1
        )
        val expected = NewEventProcessingResult(
            status = NewVoyageShipStatus(
                voyage = EntryESoFWrapper(
                    entry = expectedVoyage,
                    esof = null
                ),
                previousVisit = EntryESoFWrapper(
                    entry = expectedUpdatedVisit,
                    esof = expectedUpdatedVisitEsof
                ),
                previousVoyage = null
            ),
            changes = listOf(
                VisitChange(Action.UPDATE, expectedUpdatedVisit),
                ESoFChange(Action.UPDATE, expectedUpdatedVisitEsof),
                VoyageChange(Action.CREATE, expectedVoyage)
            ),
            readyForPostProcessing = listOf(expectedUpdatedVisit._id)
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process end of sea passage start event on visit status when main port`() {
        val eventTime = DEFAULT_START_TIME.plusDays(1)
        val event = createEndOfSeaPassageEvent(
            _id = eventId,
            status = EventStatus.START,
            time = eventTime,
            areaId = eospAreaId2
        )
        val currentVisit = createNewVisit(
            eospAreaActivity = AreaActivity(id = eventId, start = createLocationTime(), end = null, areaId = eospAreaId1),
            stops = listOf(createNewStop(NewStopType.BERTH))
        )
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedNewEospAreaActivities = AreaActivity(
            id = eventId,
            start = LocationTime(
                location = PORT_NLRTM.location.toSkeletonLocation(),
                time = eventTime.toInstant()
            ),
            end = null,
            areaId = eospAreaId2
        )
        val expectedUpdatedVisit = currentVisit.copy(
            otherOngoingEospAreaActivities = listOf(
                expectedNewEospAreaActivities
            )
        )
        val expected = NewEventProcessingResult(
            status = NewVisitShipStatus(
                visit = EntryESoFWrapper(
                    entry = expectedUpdatedVisit,
                    esof = null
                ),
                previousVisit = null,
                previousVoyage = null
            ),
            changes = listOf(
                VisitChange(Action.UPDATE, expectedUpdatedVisit)
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process end of sea passage end event on visit status exiting other ongoing eosp dropping activity`() {
        val eventTime = DEFAULT_END_TIME
        val event = createEndOfSeaPassageEvent(
            _id = eventId,
            status = EventStatus.END,
            time = eventTime,
            areaId = eospAreaId2
        )
        val otherEospAreaActivity = AreaActivity(
            id = eventId2,
            start = LocationTime(
                location = PORT_NLRTM.location.toSkeletonLocation(),
                time = eventTime.toInstant()
            ),
            end = null,
            areaId = eospAreaId2
        )
        val otherEospAreaActivity2 = otherEospAreaActivity.copy(
            areaId = eospAreaId3
        )
        val currentVisit = createNewVisit(
            eospAreaActivity = AreaActivity(id = eventId, start = createLocationTime(), end = null, areaId = eospAreaId1),
            otherOngoingEospAreaActivities = listOf(otherEospAreaActivity, otherEospAreaActivity2),
            stops = listOf(createNewStop(NewStopType.BERTH))
        )
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields
        val expectedFinishedOtherEospAreaActivity = otherEospAreaActivity.copy(
            end = createLocationTime(
                location = event.location,
                time = eventTime.toInstant()
            )
        )
        val expectedUpdatedVisit = currentVisit.copy(
            otherOngoingEospAreaActivities = listOf(otherEospAreaActivity2),
            passThroughEosp = listOf(expectedFinishedOtherEospAreaActivity)
        )
        val expected = NewEventProcessingResult(
            status = NewVisitShipStatus(
                visit = EntryESoFWrapper(
                    entry = expectedUpdatedVisit,
                    esof = null
                ),
                previousVisit = null,
                previousVoyage = null
            ),
            changes = listOf(
                VisitChange(Action.UPDATE, expectedUpdatedVisit)
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process end of sea passage end event on visit status exiting visit eosp starting other visit with 0 second voyage`() {
        val visit1Id = "TEST_VISIT_INITIAL"
        val voyage2Id = "$eventId.VOYAGE"
        val visit3Id = "$eventId.VISIT"
        val otherEospStartTime = DEFAULT_START_TIME.plusDays(1).toInstant()
        val eventTime = DEFAULT_END_TIME
        val event = createEndOfSeaPassageEvent(
            _id = eventId,
            status = EventStatus.END,
            time = eventTime,
            areaId = eospAreaId1
        )
        val otherEospAreaActivity = AreaActivity(
            id = eventId,
            start = LocationTime(
                location = PORT_NLRTM.location.toSkeletonLocation(),
                time = otherEospStartTime
            ),
            end = null,
            areaId = eospAreaId2
        )
        val otherEospAreaActivity2 = otherEospAreaActivity.copy(
            areaId = eospAreaId3
        )
        val currentPortActivity = createAreaActivity(areaId = "PORT_1")
        val currentOtherPortAreaActivity = createAreaActivity(
            areaId = eospAreaId2.removeSuffix(".eosp"),
            start = createLocationTime(time = otherEospStartTime)
        )
        val currentOngoingEncounter = NewEncounter(
            type = EncounterType.PILOT,
            otherMmsi = MMSI_2.toInt(),
            otherImo = IMO_2.toInt(),
            startEventId = "test-event-id",
            start = LocationTime(
                location = PORT_NLRTM.location.toSkeletonLocation(),
                time = DEFAULT_START_TIME.toInstant()
            ),
            end = null
        )
        val currentVisitEsof = createNewESoF(_id = visit1Id, encounters = listOf(currentOngoingEncounter))
        val currentVisit = createNewVisit(
            _id = visit1Id,
            eospAreaActivity = AreaActivity(id = eventId2, start = createLocationTime(), end = null, areaId = eospAreaId1),
            otherOngoingEospAreaActivities = listOf(otherEospAreaActivity, otherEospAreaActivity2),
            portAreaActivities = listOf(currentPortActivity, currentOtherPortAreaActivity),
            stops = listOf(createNewStop(type = NewStopType.BERTH, end = createLocationTime())),
            confirmed = true
        )
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = currentVisitEsof),
            previousVoyage = null,
            previousVisit = null
        )

        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields
        val expectedNewVisitStartLocationTime = createLocationTime(
            time = eventTime.toInstant(),
            location = PORT_NLRTM.location.toSkeletonLocation()
        )
        val expectedOriginalStartLocationTime = createLocationTime(
            time = otherEospStartTime,
            location = PORT_NLRTM.location.toSkeletonLocation()
        )
        val expectedFinishedPortActivity = currentPortActivity.copy(
            end = expectedNewVisitStartLocationTime.copy(fallback = FallbackType.ACTIVITY_END_BY_EOSP)
        )
        val expectedUpdatedEncounter = currentOngoingEncounter.copy(end = expectedNewVisitStartLocationTime.copy(fallback = FallbackType.ACTIVITY_END_BY_EOSP))
        val expectedUpdatedVisitEsof = currentVisitEsof.copy(encounters = listOf(expectedUpdatedEncounter))
        val expectedUpdatedVisit = currentVisit.copy(
            eospAreaActivity = currentVisit.eospAreaActivity.copy(end = expectedNewVisitStartLocationTime),
            otherOngoingEospAreaActivities = emptyList(),
            portAreaActivities = mutableListOf(expectedFinishedPortActivity),
            end = expectedNewVisitStartLocationTime,
            next = voyage2Id
        )
        val expectedZeroSecondVoyage = createNewVoyage(
            _id = voyage2Id,
            start = expectedNewVisitStartLocationTime,
            actualStart = expectedNewVisitStartLocationTime,
            end = expectedNewVisitStartLocationTime,
            previous = visit1Id,
            next = visit3Id,
            originPort = event.area.id,
            destinationPort = otherEospAreaActivity.areaId
        )
        val expectedNewVisit = createNewVisit(
            _id = visit3Id,
            start = expectedNewVisitStartLocationTime,
            eospAreaActivity = AreaActivity(
                id = eventId,
                start = expectedOriginalStartLocationTime,
                end = null,
                areaId = eospAreaId2
            ),
            portAreaActivities = listOf(currentOtherPortAreaActivity),
            otherOngoingEospAreaActivities = listOf(otherEospAreaActivity2),
            previous = voyage2Id
        )

        val expected = NewEventProcessingResult(
            status = NewVisitShipStatus(
                visit = EntryESoFWrapper(entry = expectedNewVisit, esof = null),
                previousVoyage = EntryESoFWrapper(entry = expectedZeroSecondVoyage, esof = null),
                previousVisit = EntryESoFWrapper(entry = expectedUpdatedVisit, esof = expectedUpdatedVisitEsof)
            ),
            changes = listOf(
                VisitChange(Action.UPDATE, expectedUpdatedVisit),
                ESoFChange(Action.UPDATE, expectedUpdatedVisitEsof),
                VoyageChange(Action.CREATE, expectedZeroSecondVoyage),
                VisitChange(Action.CREATE, expectedNewVisit)
            ),
            readyForPostProcessing = listOf(expectedUpdatedVisit._id)
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process end of sea passage end event on visit status exiting visit eosp as pass through setting back voyage actual time`() {
        val visit1Id = "TEST_VISIT_1"
        val voyage2Id = "TEST_VOYAGE_2"
        val visit3Id = "TEST_VISIT_3"
        val testLocation = PORT_NLRTM.location.toSkeletonLocation()

        // EOSP times overlapping each other
        val eosp1StartTime = DEFAULT_START_TIME.toInstant()
        val eosp1EndTime = eosp1StartTime.plus(7, ChronoUnit.DAYS)
        val eosp2StartTime = eosp1StartTime.plus(1, ChronoUnit.DAYS)
        val eosp1AreaActivity = AreaActivity(
            id = eventId,
            start = LocationTime(
                location = testLocation,
                time = eosp1StartTime
            ),
            end = LocationTime(
                location = testLocation,
                time = eosp1EndTime
            ),
            areaId = eospAreaId1
        )
        val eosp2AreaActivity = AreaActivity(
            id = eventId2,
            start = LocationTime(
                location = testLocation,
                time = eosp2StartTime
            ),
            end = null,
            areaId = eospAreaId2
        )
        val currentVisit = createNewVisit(
            _id = visit1Id,
            start = eosp2AreaActivity.start,
            eospAreaActivity = eosp2AreaActivity
        )
        val currentVisitEsof = createEmptyEsof(currentVisit, processingTestTime)
        val zeroSecondVoyage = createNewVoyage(
            _id = voyage2Id,
            start = eosp2AreaActivity.start,
            actualStart = eosp1AreaActivity.end!!,
            end = eosp2AreaActivity.start
        )
        val previousVisit = createNewVisit(
            _id = visit3Id,
            start = eosp1AreaActivity.start,
            end = eosp2AreaActivity.start,
            eospAreaActivity = eosp1AreaActivity
        )
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = currentVisitEsof),
            previousVoyage = EntryESoFWrapper(entry = zeroSecondVoyage, esof = null),
            previousVisit = EntryESoFWrapper(entry = previousVisit, esof = null)
        )

        val eventTime = DEFAULT_START_TIME.plusDays(10)
        val event = createEndOfSeaPassageEvent(
            _id = eventId,
            status = EventStatus.END,
            time = eventTime,
            areaId = eospAreaId2
        )
        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedNewlyFinishedEospAreaActivity = eosp2AreaActivity.copy(
            end = createLocationTime(
                time = eventTime.toInstant(),
                location = testLocation
            )
        )
        val expectedOngoingVoyage = zeroSecondVoyage.copy(
            passThroughEosp = listOf(expectedNewlyFinishedEospAreaActivity),
            start = eosp1AreaActivity.end!!,
            end = null
        )
        val expectedMergedEsof = currentVisitEsof.copy(_id = expectedOngoingVoyage._id)
        val expectedFinishedPreviousVisit = previousVisit.copy(
            end = eosp1AreaActivity.end
        )

        val expected = NewEventProcessingResult(
            status = NewVoyageShipStatus(
                voyage = EntryESoFWrapper(entry = expectedOngoingVoyage, esof = expectedMergedEsof),
                previousVisit = EntryESoFWrapper(entry = expectedFinishedPreviousVisit, esof = null),
                previousVoyage = null
            ),
            changes = listOf(
                ESoFChange(Action.CREATE, expectedMergedEsof),
                VisitChange(Action.UPDATE, expectedFinishedPreviousVisit),
                VoyageChange(Action.UPDATE, expectedOngoingVoyage),
                VisitChange(Action.DELETE, currentVisit),
                ESoFChange(Action.DELETE, currentVisitEsof)
            ),
            readyForPostProcessing = listOf(expectedFinishedPreviousVisit._id)
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process end of sea passage end event canceling ongoing visit when having no stops`() {
        val eventTime = DEFAULT_START_TIME.plusDays(1)
        val event = createEndOfSeaPassageEvent(
            _id = eventId,
            status = EventStatus.END,
            time = eventTime,
            areaId = eospAreaId1
        )
        val visitEnd = LocationTime(
            location = PORT_NLRTM.location.toSkeletonLocation(),
            time = eventTime.toInstant()
        )
        val currentVisit = createNewVisit(
            eospAreaActivity = AreaActivity(id = eventId, start = createLocationTime(), end = null, areaId = eospAreaId1),
            stops = emptyList()
        )
        val currentVisitEsof = createEmptyEsof(currentVisit, processingTestTime)
        val previousVoyage = createNewVoyage(
            _id = "$eventId.VOYAGE",
            imo = IMO_1.toInt(),
            start = createLocationTime(),
            end = currentVisit.start,
            next = currentVisit._id
        )
        val previousVoyageEsof = createEmptyEsof(previousVoyage, processingTestTime)
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = currentVisitEsof),
            previousVoyage = EntryESoFWrapper(entry = previousVoyage, esof = previousVoyageEsof),
            previousVisit = null
        )

        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedResumedVoyage = previousVoyage.copy(
            end = null,
            next = null,
            passThroughEosp = listOf(
                AreaActivity(id = eventId, currentVisit.start, visitEnd, eospAreaId1)
            ),
            updatedAt = processingTestTime
        )
        val expectedMergedEsof = NewESoF(
            _id = expectedResumedVoyage._id,
            encounters = currentVisitEsof.encounters + previousVoyageEsof.encounters,
            slowMovingPeriods = null,
            shipToShipTransfers = emptyList(),
            updatedAt = processingTestTime
        )

        val expected = NewEventProcessingResult(
            status = NewVoyageShipStatus(
                voyage = EntryESoFWrapper(
                    entry = expectedResumedVoyage,
                    esof = expectedMergedEsof
                ),
                previousVisit = null,
                previousVoyage = null
            ),
            changes = listOf(
                ESoFChange(Action.UPDATE, expectedMergedEsof),
                VoyageChange(Action.UPDATE, expectedResumedVoyage),
                VisitChange(Action.DELETE, currentVisit),
                ESoFChange(Action.DELETE, currentVisitEsof)
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process end of sea passage end event canceling ongoing visit not marking as pass through with zero second visit`() {
        val eventTime = DEFAULT_START_TIME.plusDays(1)
        val event = createEndOfSeaPassageEvent(
            _id = eventId,
            status = EventStatus.END,
            time = eventTime,
            areaId = eospAreaId1
        )
        val visitStart = createLocationTime(time = eventTime.toInstant())
        val currentVisit = createNewVisit(
            start = visitStart,
            eospAreaActivity = AreaActivity(id = eventId, start = visitStart, end = null, areaId = eospAreaId1),
            stops = emptyList()
        )
        val previousVoyage = createNewVoyage(
            _id = "$eventId.VOYAGE",
            imo = IMO_1.toInt(),
            start = createLocationTime(),
            end = visitStart,
            next = currentVisit._id
        )
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVoyage = EntryESoFWrapper(entry = previousVoyage, esof = null),
            previousVisit = null
        )

        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedResumedVoyage = previousVoyage.copy(
            end = null,
            next = null,
            passThroughEosp = emptyList()
        )
        val expected = NewEventProcessingResult(
            status = NewVoyageShipStatus(
                voyage = EntryESoFWrapper(
                    entry = expectedResumedVoyage,
                    esof = null
                ),
                previousVisit = null,
                previousVoyage = null
            ),
            changes = listOf(
                VoyageChange(Action.UPDATE, expectedResumedVoyage),
                VisitChange(Action.DELETE, currentVisit)
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process end of sea passage end event replacing current visit with other ongoing eosp when having no stops`() {
        val otherEospStartTime = DEFAULT_START_TIME.plusDays(1).toInstant()
        val eventTime = DEFAULT_END_TIME
        val event = createEndOfSeaPassageEvent(
            _id = eventId,
            status = EventStatus.END,
            time = eventTime,
            areaId = eospAreaId1
        )
        val portActivityBeforeOtherEosp = createAreaActivity(
            start = createLocationTime(),
            end = createLocationTime(time = DEFAULT_START_TIME.plusHours(1).toInstant()),
            areaId = "${eospAreaId1}_PORT"
        )
        val portActivityForOtherEosp = createAreaActivity(
            start = createLocationTime(time = DEFAULT_START_TIME.plusHours(6).toInstant()),
            end = null,
            areaId = "${eospAreaId2}_PORT"
        )
        val visitEnd = LocationTime(
            location = PORT_NLRTM.location.toSkeletonLocation(),
            time = eventTime.toInstant()
        )
        val otherEospStart = LocationTime(
            location = PORT_NLRTM.location.toSkeletonLocation(),
            time = otherEospStartTime
        )
        val otherEospAreaActivity = AreaActivity(
            id = eventId2,
            start = otherEospStart,
            end = null,
            areaId = eospAreaId2
        )
        val otherEospAreaActivity2 = otherEospAreaActivity.copy(
            areaId = eospAreaId3
        )
        val currentVisit = createNewVisit(
            eospAreaActivity = AreaActivity(id = eventId, start = createLocationTime(), end = null, areaId = eospAreaId1),
            otherOngoingEospAreaActivities = listOf(otherEospAreaActivity, otherEospAreaActivity2),
            portAreaActivities = listOf(portActivityBeforeOtherEosp, portActivityForOtherEosp),
            stops = emptyList()
        )
        val currentVisitEsof = createEmptyEsof(currentVisit, processingTestTime)
        val previousVoyage = createNewVoyage(
            _id = "$eventId.VOYAGE",
            imo = IMO_1.toInt(),
            start = createLocationTime(),
            end = currentVisit.start,
            next = currentVisit._id,
            destinationPort = otherEospAreaActivity.areaId
        )
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = currentVisitEsof),
            previousVoyage = EntryESoFWrapper(entry = previousVoyage, esof = null),
            previousVisit = null
        )

        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedNewVisitId = "$eventId2.VISIT"
        val expectedPreviousVoyage = previousVoyage.copy(
            next = expectedNewVisitId,
            end = otherEospStart
        )
        val expectedCurrentVisitPassThrough = currentVisit.eospAreaActivity.copy(end = visitEnd)
        val expectedUpdatedVisit = currentVisit.copy(
            _id = expectedNewVisitId,
            start = otherEospStart,
            eospAreaActivity = otherEospAreaActivity,
            passThroughEosp = listOf(expectedCurrentVisitPassThrough),
            otherOngoingEospAreaActivities = listOf(otherEospAreaActivity2)
        )
        val expectedUpdatedEsof = currentVisitEsof.copy(_id = expectedNewVisitId)

        val expected = NewEventProcessingResult(
            status = NewVisitShipStatus(
                visit = EntryESoFWrapper(
                    entry = expectedUpdatedVisit,
                    esof = expectedUpdatedEsof
                ),
                previousVoyage = EntryESoFWrapper(entry = expectedPreviousVoyage, esof = null),
                previousVisit = null
            ),
            changes = listOf(
                VisitChange(Action.DELETE, currentVisit),
                ESoFChange(Action.DELETE, currentVisitEsof),
                VisitChange(Action.CREATE, expectedUpdatedVisit),
                ESoFChange(Action.CREATE, expectedUpdatedEsof),
                VoyageChange(Action.UPDATE, expectedPreviousVoyage)
            ),
            readyForPostProcessing = listOf(expectedPreviousVoyage._id)
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process end of sea passage start event on visit status when previous end event was missed`() {
        val testEosp = listOf(
            Location(lat = 5.0, lon = 5.0),
            Location(lat = 6.0, lon = 5.0),
            Location(lat = 6.0, lon = 6.0),
            Location(lat = 5.0, lon = 6.0),
            Location(lat = 5.0, lon = 5.0)
        )

        whenever(infraService.getPortByAreaId(any()))
            .thenReturn(createPort(eosArea = testEosp))

        val eventTime = DEFAULT_START_TIME.plusDays(1)
        val event = createEndOfSeaPassageEvent(
            _id = eventId,
            status = EventStatus.START,
            time = eventTime,
            areaId = eospAreaId2
        )
        val newVisitStart = createLocationTime(
            location = PORT_NLRTM.location.toSkeletonLocation(),
            time = eventTime.toInstant()
        )
        val currentVisitStart = createLocationTime(time = DEFAULT_START_TIME.toInstant())
        val currentVisit = createNewVisit(
            _id = "VISIT_1",
            start = currentVisitStart,
            eospAreaActivity = createAreaActivity(
                start = currentVisitStart,
                areaId = eospAreaId1,
            )
        )
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedVisitEnd = newVisitStart.copy(fallback = FallbackType.ACTIVITY_END_BY_EOSP_START_EVENT)
        val expectedUpdatedVisit = currentVisit.copy(
            end = expectedVisitEnd,
            eospAreaActivity = currentVisit.eospAreaActivity.copy(end = expectedVisitEnd),
            next = "$eventId.VOYAGE"
        )
        val expectedInBetweenVoyage = createNewVoyage(
            _id = "$eventId.VOYAGE",
            previous = "VISIT_1",
            next = "$eventId.VISIT",
            actualStart = newVisitStart.copy(fallback = FallbackType.ACTIVITY_END_BY_EOSP_START_EVENT),
            start = newVisitStart.copy(fallback = FallbackType.ACTIVITY_END_BY_EOSP_START_EVENT),
            end = newVisitStart,
            originPort = eospAreaId1,
            destinationPort = eospAreaId2
        )
        val expectedNewVisit = createNewVisit(
            _id = "$eventId.VISIT",
            imo = IMO_1.toInt(),
            start = newVisitStart,
            eospAreaActivity = createAreaActivity(id = eventId, start = newVisitStart, areaId = eospAreaId2),
            previous = "$eventId.VOYAGE"
        )
        val expected = NewEventProcessingResult(
            status = NewVisitShipStatus(
                visit = EntryESoFWrapper(
                    entry = expectedNewVisit,
                    esof = null
                ),
                previousVoyage = EntryESoFWrapper(
                    entry = expectedInBetweenVoyage,
                    esof = null
                ),
                previousVisit = EntryESoFWrapper(
                    entry = expectedUpdatedVisit,
                    esof = null
                )
            ),
            changes = listOf(
                VisitChange(Action.UPDATE, expectedUpdatedVisit),
                VoyageChange(Action.CREATE, expectedInBetweenVoyage),
                VisitChange(Action.CREATE, expectedNewVisit)
            ),
            readyForPostProcessing = listOf("VISIT_1")
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process end of sea passage start event on visit status when previous end event was missed switching to already ongoing eosp`() {
        val testLeftEosp = listOf(
            Location(lat = 5.0, lon = 5.0),
            Location(lat = 6.0, lon = 5.0),
            Location(lat = 6.0, lon = 6.0),
            Location(lat = 5.0, lon = 6.0),
            Location(lat = 5.0, lon = 5.0)
        )

        val testStillOngoingEosp = listOf(
            Location(lat = 0.0, lon = 0.0),
            Location(lat = 2.0, lon = 0.0),
            Location(lat = 2.0, lon = 2.0),
            Location(lat = 0.0, lon = 2.0),
            Location(lat = 0.0, lon = 0.0)
        )

        whenever(infraService.getPortByAreaId(eq(eospAreaId1)))
            .thenReturn(createPort(eosArea = testLeftEosp))
        whenever(infraService.getPortByAreaId(eq(eospAreaId5)))
            .thenReturn(createPort(eosArea = testLeftEosp))

        whenever(infraService.getPortByAreaId(eq(eospAreaId2)))
            .thenReturn(createPort(eosArea = testStillOngoingEosp))
        whenever(infraService.getPortByAreaId(eq(eospAreaId4)))
            .thenReturn(createPort(eosArea = testStillOngoingEosp))

        val eventTime = DEFAULT_START_TIME.plusDays(1)
        val event = createEndOfSeaPassageEvent(
            _id = eventId,
            status = EventStatus.START,
            time = eventTime,
            areaId = eospAreaId3
        )
        val newlyEnteredEospStart = createLocationTime(
            location = PORT_NLRTM.location.toSkeletonLocation(),
            time = eventTime.toInstant()
        )
        val currentVisitStart = createLocationTime(time = DEFAULT_START_TIME.toInstant())
        val currentOngoingToBeNewVisitMain = createAreaActivity(
            id = "OTHER_VISIT_2",
            start = currentVisitStart,
            areaId = eospAreaId2
        )
        val currentOngoingToBePutOnNewVisit = createAreaActivity(
            id = "OTHER_VISIT_3",
            start = currentVisitStart,
            areaId = eospAreaId4
        )
        val currentOngoingToBeFinished = createAreaActivity(
            id = "OTHER_VISIT_4",
            start = currentVisitStart,
            areaId = eospAreaId5
        )
        val currentVisit = createNewVisit(
            _id = "VISIT_1.VISIT",
            start = currentVisitStart,
            eospAreaActivity = createAreaActivity(
                id = "VISIT_1",
                start = currentVisitStart,
                areaId = eospAreaId1,
            ),
            otherOngoingEospAreaActivities = listOf(
                currentOngoingToBeNewVisitMain,
                currentOngoingToBePutOnNewVisit,
                currentOngoingToBeFinished
            )
        )
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedPassThroughEnd = newlyEnteredEospStart.copy(fallback = FallbackType.ACTIVITY_END_BY_EOSP_START_EVENT)
        val expectedUpdatedVisit = currentVisit.copy(
            end = currentVisitStart,
            eospAreaActivity = currentVisit.eospAreaActivity.copy(end = currentVisitStart),
            otherOngoingEospAreaActivities = emptyList(),
            passThroughEosp = listOf(currentOngoingToBeFinished.copy(end = expectedPassThroughEnd)),
            next = "OTHER_VISIT_2.VOYAGE"
        )
        val expectedInBetweenVoyage = createNewVoyage(
            _id = "OTHER_VISIT_2.VOYAGE",
            previous = "VISIT_1.VISIT",
            next = "OTHER_VISIT_2.VISIT",
            actualStart = currentVisitStart,
            start = currentVisitStart,
            end = currentVisitStart,
            originPort = eospAreaId1,
            destinationPort = eospAreaId2
        )
        val expectedNewVisit = createNewVisit(
            _id = "OTHER_VISIT_2.VISIT",
            imo = IMO_1.toInt(),
            start = currentVisitStart,
            eospAreaActivity = currentOngoingToBeNewVisitMain,
            otherOngoingEospAreaActivities = listOf(
                currentOngoingToBePutOnNewVisit,
                createAreaActivity(
                    id = eventId,
                    start = newlyEnteredEospStart,
                    areaId = eospAreaId3
                )
            ),
            previous = "OTHER_VISIT_2.VOYAGE"
        )
        val expected = NewEventProcessingResult(
            status = NewVisitShipStatus(
                visit = EntryESoFWrapper(
                    entry = expectedNewVisit,
                    esof = null
                ),
                previousVoyage = EntryESoFWrapper(
                    entry = expectedInBetweenVoyage,
                    esof = null
                ),
                previousVisit = EntryESoFWrapper(
                    entry = expectedUpdatedVisit,
                    esof = null
                )
            ),
            changes = listOf(
                VisitChange(Action.UPDATE, expectedUpdatedVisit),
                VoyageChange(Action.CREATE, expectedInBetweenVoyage),
                VisitChange(Action.CREATE, expectedNewVisit)
            ),
            readyForPostProcessing = listOf("VISIT_1.VISIT")
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process end of sea passage end event on voyage status when previous end event was missed`() {
        val eventTime = DEFAULT_START_TIME.plusDays(7)
        val event = createEndOfSeaPassageEvent(
            _id = eventId,
            status = EventStatus.END,
            time = eventTime,
            areaId = eospAreaId1
        )
        val previousVisitStart = createLocationTime(time = DEFAULT_START_TIME.toInstant())
        val previousVisitEnd = createLocationTime(time = DEFAULT_END_TIME.toInstant(), fallback = FallbackType.ACTIVITY_END_BY_EOSP_START_EVENT)
        val currentVoyage = createNewVoyage(
            _id = "VOYAGE_2",
            start = previousVisitEnd
        )
        val previousVisit = createNewVisit(
            _id = "VISIT_1",
            start = previousVisitStart,
            eospAreaActivity = createAreaActivity(
                start = previousVisitStart,
                end = previousVisitEnd,
                areaId = eospAreaId1,
            )
        )
        val currentStatus = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(entry = currentVoyage, esof = null),
            previousVisit = EntryESoFWrapper(entry = previousVisit, esof = null),
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedVisitEnd = createLocationTime(
            location = PORT_NLRTM.location.toSkeletonLocation(),
            time = eventTime.toInstant()
        )
        val expectedUpdatedVoyage = currentVoyage.copy(
            start = expectedVisitEnd
        )
        val expectedUpdatedVisit = previousVisit.copy(
            end = expectedVisitEnd,
            eospAreaActivity = previousVisit.eospAreaActivity.copy(
                end = expectedVisitEnd
            )
        )

        val expected = NewEventProcessingResult(
            status = NewVoyageShipStatus(
                voyage = EntryESoFWrapper(
                    entry = expectedUpdatedVoyage,
                    esof = null
                ),
                previousVisit = EntryESoFWrapper(
                    entry = expectedUpdatedVisit,
                    esof = null
                ),
                previousVoyage = null
            ),
            changes = listOf(
                VisitChange(Action.UPDATE, expectedUpdatedVisit),
                VoyageChange(Action.UPDATE, expectedUpdatedVoyage)
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process end of sea passage end event on visit status when previous end event was missed`() {
        val eventTime = DEFAULT_START_TIME.plusDays(7)
        val event = createEndOfSeaPassageEvent(
            _id = eventId,
            status = EventStatus.END,
            time = eventTime,
            areaId = eospAreaId1
        )
        val previousVisitStart = createLocationTime(time = DEFAULT_START_TIME.toInstant())
        val previousVisitEnd = createLocationTime(time = DEFAULT_END_TIME.toInstant(), fallback = FallbackType.ACTIVITY_END_BY_EOSP_START_EVENT)
        val currentVisit = createNewVisit(
            _id = "VISIT_3",
            start = previousVisitEnd
        )
        val previousVoyage = createNewVoyage(
            _id = "VOYAGE_2",
            start = previousVisitEnd,
            end = previousVisitEnd
        )
        val previousVisit = createNewVisit(
            _id = "VISIT_1",
            start = previousVisitStart,
            eospAreaActivity = createAreaActivity(
                start = previousVisitStart,
                end = previousVisitEnd,
                areaId = eospAreaId1,
            )
        )
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVoyage = EntryESoFWrapper(entry = previousVoyage, esof = null),
            previousVisit = EntryESoFWrapper(entry = previousVisit, esof = null)
        )

        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedVisitEnd = createLocationTime(
            location = PORT_NLRTM.location.toSkeletonLocation(),
            time = eventTime.toInstant()
        )
        val expectedUpdatedPreviousVoyage = previousVoyage.copy(
            start = expectedVisitEnd
        )
        val expectedUpdatedPreviousVisit = previousVisit.copy(
            end = expectedVisitEnd,
            eospAreaActivity = previousVisit.eospAreaActivity.copy(
                end = expectedVisitEnd
            )
        )

        val expected = NewEventProcessingResult(
            status = NewVisitShipStatus(
                visit = currentStatus.visit,
                previousVoyage = EntryESoFWrapper(
                    entry = expectedUpdatedPreviousVoyage,
                    esof = null
                ),
                previousVisit = EntryESoFWrapper(
                    entry = expectedUpdatedPreviousVisit,
                    esof = null
                )
            ),
            changes = listOf(
                VisitChange(Action.UPDATE, expectedUpdatedPreviousVisit),
                VoyageChange(Action.UPDATE, expectedUpdatedPreviousVoyage)
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should go back to initial state when visit is marked as pass through and canceled`() {
        val event = createEndOfSeaPassageEvent(
            _id = eventId2,
            status = EventStatus.END,
            time = DEFAULT_END_TIME,
            areaId = eospAreaId1
        )
        val currentVisit = createNewVisit(
            eospAreaActivity = createAreaActivity(areaId = eospAreaId1)
        )
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVoyage = null,
            previousVisit = null
        )

        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields
        val expected = NewEventProcessingResult(
            status = NewInitialShipStatus(),
            changes = listOf(
                VisitChange(Action.DELETE, currentVisit),
            )
        )

        assertEquals(expected, result)
    }

    private fun areaActivityStartEventsTestData(): Stream<Arguments> {
        val testingAreaId = "aaaaa-bbbbb-ccccc"

        return Stream.of(
            Arguments.of(
                testingAreaId,
                createAnchorEvent(
                    eventId = eventId,
                    status = EventStatus.START,
                    time = DEFAULT_START_TIME,
                    areaId = testingAreaId
                ),
                NewVisit::anchorAreaActivities
            ),
            Arguments.of(
                testingAreaId,
                createPilotAreaEvent(
                    eventId = eventId,
                    status = EventStatus.START,
                    time = DEFAULT_START_TIME,
                    areaId = testingAreaId
                ),
                NewVisit::pilotAreaActivities
            ),
            Arguments.of(
                testingAreaId,
                createAnchorAreaEvent(
                    eventId = eventId,
                    status = EventStatus.START,
                    time = DEFAULT_START_TIME,
                    areaId = testingAreaId
                ),
                NewVisit::anchorAreaAreaActivities
            ),
            Arguments.of(
                testingAreaId,
                createTerminalMooringAreaEvent(
                    eventId = eventId,
                    status = EventStatus.START,
                    time = DEFAULT_START_TIME,
                    areaId = testingAreaId
                ),
                NewVisit::terminalMooringAreaActivities
            ),
            Arguments.of(
                testingAreaId,
                createLockAreaEvent(
                    eventId = eventId,
                    status = EventStatus.START,
                    time = DEFAULT_START_TIME,
                    areaId = testingAreaId
                ),
                NewVisit::lockAreaActivities
            ),
            Arguments.of(
                testingAreaId,
                createApproachAreaEvent(
                    eventId = eventId,
                    status = EventStatus.START,
                    time = DEFAULT_START_TIME,
                    areaId = testingAreaId
                ),
                NewVisit::approachAreaActivities
            )
        )
    }

    @ParameterizedTest
    @MethodSource("areaActivityStartEventsTestData")
    fun `should process area activity start event updating visit status`(
        areaId: String,
        event: Event,
        expectedUpdatedActivitiesField: KProperty1<NewVisit, List<AreaActivity>>
    ) {
        val currentVisit = createNewVisit()
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedStartLocationTime = LocationTime(
            location = PORT_NLRTM.location.toSkeletonLocation(),
            time = DEFAULT_START_TIME.toInstant()
        )
        val expectedAreaActivity = AreaActivity(
            id = eventId,
            start = expectedStartLocationTime,
            end = null,
            areaId = areaId
        )
        val expectedVisit = currentVisit.copy()

        val javaField = expectedUpdatedActivitiesField.javaField!!
        javaField.isAccessible = true
        javaField.set(expectedVisit, listOf(expectedAreaActivity))
        javaField.isAccessible = false

        val expected = NewEventProcessingResult(
            status = currentStatus.copy(
                visit = EntryESoFWrapper(
                    entry = expectedVisit,
                    esof = null
                )
            ),
            changes = listOf(
                VisitChange(Action.UPDATE, expectedVisit)
            )
        )

        assertEquals(expected, result)
    }

    @ParameterizedTest
    @MethodSource("areaActivityStartEventsTestData")
    fun `should ignore area activity start event when visit is already stopping for the area`(
        areaId: String,
        event: Event,
        activitiesField: KProperty1<NewVisit, List<AreaActivity>>
    ) {
        val currentAreaActivity = AreaActivity(
            id = eventId,
            start = LocationTime(
                location = PORT_NLRTM.location.toSkeletonLocation(),
                time = DEFAULT_START_TIME.toInstant()
            ),
            end = null,
            areaId = areaId
        )

        val currentVisit = createNewVisit()

        val javaField = activitiesField.javaField!!
        javaField.isAccessible = true
        javaField.set(currentVisit, listOf(currentAreaActivity))
        javaField.isAccessible = false
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields
        val expected = NewEventProcessingResult(
            status = currentStatus,
            changes = emptyList(),
            issues = listOf(
                EventProcessingIssue(
                    eventId = eventId,
                    description = buildString {
                        append("Visit already has an activity ongoing for this. ")
                        append("Will ignore the event (imo: $IMO_1, area id: $areaId)")
                    }
                )
            )
        )

        assertEquals(expected, result)
    }

    private fun areaActivityEndEventsTestData(): Stream<Arguments> {
        val testingAreaId = "aaaaa-bbbbb-ccccc"

        val currentAreaActivity = createAreaActivity(areaId = testingAreaId)
        val expectedEndLocationTime = createLocationTime(
            location = Location(1.0, 1.0),
            time = DEFAULT_END_TIME.toInstant()
        )

        val endWithFallback = createLocationTime(
            time = DEFAULT_ACTUAL_TIME.toInstant(),
            fallback = FallbackType.ACTIVITY_END_BY_EOSP
        )
        val currentAreaActivityWithFallback = currentAreaActivity.copy(end = endWithFallback)

        return Stream.of(
            Arguments.of(
                testingAreaId,
                createAnchorEvent(
                    eventId = eventId,
                    status = EventStatus.END,
                    time = DEFAULT_END_TIME,
                    areaId = testingAreaId
                ),
                currentAreaActivity,
                currentAreaActivityWithFallback,
                expectedEndLocationTime,
                NewVisit::anchorAreaActivities
            ),
            Arguments.of(
                testingAreaId,
                createPilotAreaEvent(
                    eventId = eventId,
                    status = EventStatus.END,
                    time = DEFAULT_END_TIME,
                    areaId = testingAreaId
                ),
                currentAreaActivity,
                currentAreaActivityWithFallback,
                expectedEndLocationTime,
                NewVisit::pilotAreaActivities
            ),
            Arguments.of(
                testingAreaId,
                createAnchorAreaEvent(
                    eventId = eventId,
                    status = EventStatus.END,
                    time = DEFAULT_END_TIME,
                    areaId = testingAreaId
                ),
                currentAreaActivity,
                currentAreaActivityWithFallback,
                expectedEndLocationTime,
                NewVisit::anchorAreaAreaActivities
            ),
            Arguments.of(
                testingAreaId,
                createTerminalMooringAreaEvent(
                    eventId = eventId,
                    status = EventStatus.END,
                    time = DEFAULT_END_TIME,
                    areaId = testingAreaId
                ),
                currentAreaActivity,
                currentAreaActivityWithFallback,
                expectedEndLocationTime,
                NewVisit::terminalMooringAreaActivities
            ),
            Arguments.of(
                testingAreaId,
                createLockAreaEvent(
                    eventId = eventId,
                    status = EventStatus.END,
                    time = DEFAULT_END_TIME,
                    areaId = testingAreaId
                ),
                currentAreaActivity,
                currentAreaActivityWithFallback,
                expectedEndLocationTime,
                NewVisit::lockAreaActivities
            ),
            Arguments.of(
                testingAreaId,
                createApproachAreaEvent(
                    eventId = eventId,
                    status = EventStatus.END,
                    time = DEFAULT_END_TIME,
                    areaId = testingAreaId
                ),
                currentAreaActivity,
                currentAreaActivityWithFallback,
                expectedEndLocationTime,
                NewVisit::approachAreaActivities
            )
        )
    }

    @ParameterizedTest
    @MethodSource("areaActivityEndEventsTestData")
    fun `should process area activity end event updating visit status`(
        @Suppress("unused") areaId: String,
        event: Event,
        currentAreaActivity: AreaActivity,
        currentAreaActivityWithFallback: AreaActivity,
        expectedEndLocationTime: LocationTime,
        activitiesField: KProperty1<NewVisit, MutableList<AreaActivity>>
    ) {
        val currentStop = createNewStop(type = NewStopType.UNCLASSIFIED)
        val currentVisit = createNewVisit(stops = listOf(currentStop))
        val currentVisitWithFallback = createNewVisit()

        val javaField = activitiesField.javaField!!
        javaField.isAccessible = true
        javaField.set(currentVisit, mutableListOf(currentAreaActivity))
        javaField.set(currentVisitWithFallback, mutableListOf(currentAreaActivityWithFallback))

        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVisit = null,
            previousVoyage = null
        )
        val currentStatusWithFallback = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(entry = createNewVoyage(), esof = null),
            previousVisit = EntryESoFWrapper(entry = currentVisitWithFallback, esof = null),
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields
        val resultWithFallback = service.onEventAndBuffer(currentStatusWithFallback, event, processingTestTime)
        val expectedAreaActivity = currentAreaActivity.copy(
            end = expectedEndLocationTime
        )
        val expectedVisit = currentVisit.copy()
        val expectedVisitWithFallback = currentVisitWithFallback.copy()
        javaField.set(expectedVisit, listOf(expectedAreaActivity))
        javaField.set(expectedVisitWithFallback, listOf(expectedAreaActivity))
        javaField.isAccessible = false

        val expected1 = NewEventProcessingResult(
            status = currentStatus.copy(
                visit = EntryESoFWrapper(
                    entry = expectedVisit,
                    esof = null
                )
            ),
            changes = listOf(
                VisitChange(Action.UPDATE, expectedVisit)
            )
        )
        val expected2 = NewEventProcessingResult(
            status = currentStatusWithFallback.copy(
                previousVisit = EntryESoFWrapper(
                    entry = expectedVisitWithFallback,
                    esof = null
                )
            ),
            changes = listOf(
                VisitChange(Action.UPDATE, expectedVisitWithFallback)
            )
        )

        assertEquals(expected1, result)
        assertEquals(expected2, resultWithFallback)
    }

    @ParameterizedTest
    @MethodSource("areaActivityEndEventsTestData")
    fun `should ignore area activity end event when visit is not stopped for the area`(
        areaId: String,
        event: Event
    ) {
        val currentVisit = createNewVisit()
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields
        val expected = NewEventProcessingResult(
            status = currentStatus,
            changes = emptyList(),
            issues = listOf(
                EventProcessingIssue(
                    eventId = eventId,
                    description = "Currently known area activities don't match the area id or are already finished. (area id: $areaId)"
                )
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should limit visit when starting the 50th area activity`() {
        val currentLockAreaActivities = repeatCreate(49) {
            createAreaActivity(
                areaId = "TEST_ID",
                end = createLocationTime()
            )
        }
        val currentVisit = createNewVisit(lockAreaActivities = currentLockAreaActivities)
        val currentVisitStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVoyage = null,
            previousVisit = null
        )

        val event = createLockAreaEvent(
            eventId = eventId,
            status = EventStatus.START,
            time = DEFAULT_ACTUAL_TIME,
            areaId = "aaaaa-bbbbb-ccccc"
        )
        val result = service.onEventAndBuffer(currentVisitStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedNewLockAreaActivity = createAreaActivity(
            id = eventId,
            start = createLocationTime(location = event.location, time = DEFAULT_ACTUAL_TIME.toInstant()),
            areaId = "aaaaa-bbbbb-ccccc"
        )
        val expectedVisit = currentVisit.copy(
            lockAreaActivities = (currentLockAreaActivities + expectedNewLockAreaActivity).toMutableList(),
            limited = true
        )
        val expected = NewEventProcessingResult(
            status = NewVisitShipStatus(
                visit = EntryESoFWrapper(entry = expectedVisit, esof = null),
                previousVoyage = null,
                previousVisit = null
            ),
            changes = listOf(
                VisitChange(Action.UPDATE, expectedVisit)
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should not start new area activity when already limited`() {
        val currentVisit = createNewVisit(limited = true)
        val currentVisitStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVoyage = null,
            previousVisit = null
        )

        val event = createLockAreaEvent(
            eventId = eventId,
            status = EventStatus.START,
            time = DEFAULT_START_TIME,
            areaId = "aaaaa-bbbbb-ccccc"
        )
        val result = service.onEventAndBuffer(currentVisitStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields
        val expected = NewEventProcessingResult(
            status = NewVisitShipStatus(
                visit = EntryESoFWrapper(entry = currentVisit, esof = null),
                previousVoyage = null,
                previousVisit = null,
                eventBuffer = mutableListOf(event)
            ),
            changes = emptyList(),
            issues = listOf(
                EventProcessingIssue(eventId, "Could not create any new activities because the current entry is already limited.")
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should allow visit creation if voyage is limited`() {
        val currentVoyage = createNewVoyage(limited = true)
        val currentVisitStatus = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(entry = currentVoyage, esof = null),
            previousVisit = null,
            previousVoyage = null
        )

        val event = createEndOfSeaPassageEvent(
            _id = eventId,
            status = EventStatus.START,
            time = DEFAULT_START_TIME
        )

        val result = service.onEventAndBuffer(currentVisitStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val locationTime = LocationTime(event.location, event.actualTime)
        val visit = createNewVisit(
            _id = createVisitId(event._id),
            start = locationTime,
            previous = currentVoyage._id,
            eospAreaActivity = createAreaActivity(
                start = locationTime,
                areaId = event.area.id ?: ""
            )
        )
        val expectedStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = visit, esof = null),
            previousVoyage = EntryESoFWrapper(
                entry = currentVoyage.copy(
                    end = locationTime,
                    next = visit._id,
                    destinationPort = event.area.id
                ),
                esof = null
            ),
            previousVisit = null
        )

        assertEquals(expectedStatus, result.status)
    }

    @Test
    fun `should end ongoing lock area activity when already limited`() {
        val currentLock = createAreaActivity(areaId = "aaaaa-bbbbb-ccccc")
        val currentVisit = createNewVisit(limited = true, lockAreaActivities = listOf(currentLock))
        val currentVisitStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVoyage = null,
            previousVisit = null
        )

        val event = (
            createLockAreaEvent(
                eventId = eventId,
                status = EventStatus.END,
                time = DEFAULT_END_TIME,
                areaId = "aaaaa-bbbbb-ccccc"
            ) as AreaEndEvent
            ).copy(location = testEventLocation)
        val result = service.onEventAndBuffer(currentVisitStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields
        val expectedLock = currentLock.copy(
            end = createLocationTime(
                location = testEventLocation,
                time = DEFAULT_END_TIME.toInstant()
            )
        )
        val expectedVisit = currentVisit.copy(lockAreaActivities = mutableListOf(expectedLock))
        val expected = NewEventProcessingResult(
            status = NewVisitShipStatus(
                visit = EntryESoFWrapper(entry = expectedVisit, esof = null),
                previousVoyage = null,
                previousVisit = null
            ),
            changes = listOf(
                VisitChange(Action.UPDATE, expectedVisit)
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should not try to override AreaActivity when event is a start event at voyage status`() {
        val event = createLockAreaEvent(
            eventId = eventId,
            status = EventStatus.START,
            time = DEFAULT_START_TIME,
            areaId = "aaaaa-bbbbb-ccccc"
        )

        val currentVoyage = createNewVoyage()
        val previusVisit = createNewVisit(previous = currentVoyage._id)
        val currentStatus = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(entry = currentVoyage, esof = null),
            previousVisit = EntryESoFWrapper(entry = previusVisit, esof = null),
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields
        val expected = NewEventProcessingResult(
            status = currentStatus,
            changes = emptyList(),
            issues = listOf(
                EventProcessingIssue(
                    eventId = eventId,
                    description = "Can't process start events when dealing with a previous Visit."
                )
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should not try to override AreaActivity when end event is not matching any activities at voyage status`() {
        val event = createLockAreaEvent(
            eventId = eventId,
            status = EventStatus.END,
            time = DEFAULT_START_TIME,
            areaId = "aaaaa-bbbbb-ccccc"
        )

        val otherLockAreaActivity = createAreaActivity(
            areaId = "xxxxx-yyyyy-zzzzz",
            end = createLocationTime(fallback = FallbackType.ACTIVITY_END_BY_EOSP)
        )

        val correctlyFinishedLockAreaActivityWithMatchingAreaId = createAreaActivity(
            areaId = "aaaaa-bbbbb-ccccc",
            end = createLocationTime(fallback = null)
        )

        val currentVoyage = createNewVoyage()
        val previusVisit = createNewVisit(
            previous = currentVoyage._id,
            lockAreaActivities = listOf(otherLockAreaActivity, correctlyFinishedLockAreaActivityWithMatchingAreaId)
        )
        val currentStatus = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(entry = currentVoyage, esof = null),
            previousVisit = EntryESoFWrapper(entry = previusVisit, esof = null),
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields
        val expected = NewEventProcessingResult(
            status = currentStatus,
            changes = emptyList(),
            issues = listOf(
                EventProcessingIssue(
                    eventId = eventId,
                    description = "Currently known area activities don't match the area id or didn't use a fallback. (area id: aaaaa-bbbbb-ccccc)"
                )
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should not try to override AreaActivity when end event doesn't contain area id at voyage status`() {
        val event = createAnchorEventEnd(ANCHOR_AREA_1, "2023-07-01T00:00:00Z")

        val currentVoyage = createNewVoyage()
        val previusVisit = createNewVisit(previous = currentVoyage._id)
        val currentStatus = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(entry = currentVoyage, esof = null),
            previousVisit = EntryESoFWrapper(entry = previusVisit, esof = null),
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields
        val expected = NewEventProcessingResult(
            status = currentStatus,
            changes = emptyList(),
            issues = listOf(
                EventProcessingIssue(
                    eventId = event._id,
                    description = "Can't process event because the area id is null."
                )
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process berth start event updating visit status`() {
        val testBerthAreaId = "aaaaa-bbbbb-ccccc"
        val event = createUniqueBerthEvent(
            eventId = eventId,
            status = EventStatus.START,
            time = DEFAULT_START_TIME,
            areaId = testBerthAreaId
        )
        val currentVisit = createNewVisit()
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedBerthStartLocationTime = LocationTime(
            location = PORT_NLRTM.location.toSkeletonLocation(),
            time = DEFAULT_START_TIME.toInstant()
        )
        val expectedBerthAreaActivity = AreaActivity(
            id = eventId,
            start = expectedBerthStartLocationTime,
            end = null,
            areaId = testBerthAreaId
        )
        val expectedVisit = currentVisit.copy(
            berthAreaActivities = mutableListOf(expectedBerthAreaActivity)
        )
        val expected = NewEventProcessingResult(
            status = currentStatus.copy(
                visit = EntryESoFWrapper(
                    entry = expectedVisit,
                    esof = null
                )
            ),
            changes = listOf(
                VisitChange(Action.UPDATE, expectedVisit)
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should ignore berth start event is already stopped at the berth area`() {
        val testBerthAreaId = "aaaaa-bbbbb-ccccc"
        val event = createUniqueBerthEvent(
            eventId = eventId,
            status = EventStatus.START,
            time = DEFAULT_START_TIME,
            areaId = testBerthAreaId
        )

        val currentBerthAreaActivity = AreaActivity(
            id = eventId,
            start = LocationTime(
                location = PORT_NLRTM.location.toSkeletonLocation(),
                time = DEFAULT_START_TIME.toInstant()
            ),
            end = null,
            areaId = testBerthAreaId
        )

        val currentVisit = createNewVisit(
            berthAreaActivities = listOf(currentBerthAreaActivity)
        )
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields
        val expected = NewEventProcessingResult(
            status = currentStatus,
            changes = emptyList(),
            issues = listOf(
                EventProcessingIssue(
                    eventId = eventId,
                    description = buildString {
                        append("Visit already has an activity ongoing for this. ")
                        append("Will ignore the event (imo: $IMO_1, area id: $testBerthAreaId)")
                    }
                )
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process berth end event updating visit status`() {
        val testBerthId = "aaaaa-bbbbb-ccccc"
        val event = createUniqueBerthEvent(
            eventId = eventId,
            status = EventStatus.END,
            time = DEFAULT_END_TIME,
            areaId = testBerthId
        )
        val currentBerthAreaActivity = AreaActivity(
            id = eventId,
            start = createLocationTime(),
            end = null,
            areaId = testBerthId
        )
        val currentVisit = createNewVisit(
            berthAreaActivities = listOf(currentBerthAreaActivity)
        )
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedBerthEndLocationTime = LocationTime(
            location = PORT_NLRTM.location.toSkeletonLocation(),
            time = DEFAULT_END_TIME.toInstant()
        )
        val expectedBerthAreaActivity = currentBerthAreaActivity.copy(
            end = expectedBerthEndLocationTime
        )
        val expectedVisit = currentVisit.copy(
            berthAreaActivities = mutableListOf(expectedBerthAreaActivity)
        )
        val expected = NewEventProcessingResult(
            status = currentStatus.copy(
                visit = EntryESoFWrapper(
                    entry = expectedVisit,
                    esof = null
                )
            ),
            changes = listOf(
                VisitChange(Action.UPDATE, expectedVisit)
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should ignore berth end event when not stopped at the berth area`() {
        val testBerthAreaId = "aaaaa-bbbbb-ccccc"
        val event = createUniqueBerthEvent(
            eventId = eventId,
            status = EventStatus.END,
            time = DEFAULT_START_TIME,
            areaId = testBerthAreaId
        )

        val currentVisit = createNewVisit()
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields
        val expected = NewEventProcessingResult(
            status = currentStatus,
            changes = emptyList(),
            issues = listOf(
                EventProcessingIssue(
                    eventId = eventId,
                    description = "Currently known area activities don't match the area id or are already finished. (area id: $testBerthAreaId)"
                )
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process port start event updating visit status`() {
        val event = createAreaPortEvent(
            eventId = eventId,
            status = EventStatus.START,
            time = DEFAULT_START_TIME,
            port = PORT_NLRTM
        )
        val currentVisit = createNewVisit()
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedPortStartLocationTime = LocationTime(
            location = PORT_NLRTM.location.toSkeletonLocation(),
            time = DEFAULT_START_TIME.toInstant()
        )
        val expectedPortAreaActivity = AreaActivity(
            id = eventId,
            start = expectedPortStartLocationTime,
            end = null,
            areaId = PORT_NLRTM.portId
        )
        val expectedVisit = currentVisit.copy(
            portAreaActivities = mutableListOf(expectedPortAreaActivity)
        )
        val expected = NewEventProcessingResult(
            status = currentStatus.copy(
                visit = EntryESoFWrapper(
                    entry = expectedVisit,
                    esof = null
                )
            ),
            changes = listOf(
                VisitChange(Action.UPDATE, expectedVisit)
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should ignore port start event if already stopped at port`() {
        val event = createAreaPortEvent(
            eventId = eventId,
            status = EventStatus.START,
            time = DEFAULT_START_TIME,
            port = PORT_NLRTM
        )

        val currentPortAreaActivity = AreaActivity(
            id = eventId,
            start = LocationTime(
                location = PORT_NLRTM.location.toSkeletonLocation(),
                time = DEFAULT_START_TIME.toInstant()
            ),
            end = null,
            areaId = PORT_NLRTM.portId
        )

        val currentVisit = createNewVisit(
            portAreaActivities = listOf(currentPortAreaActivity)
        )
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields
        val expected = NewEventProcessingResult(
            status = currentStatus,
            changes = emptyList(),
            issues = listOf(
                EventProcessingIssue(
                    eventId = eventId,
                    description = buildString {
                        append("Visit already has an activity ongoing for this. ")
                        append("Will ignore the event (imo: $IMO_1, area id: ${PORT_NLRTM.portId})")
                    }
                )
            )
        )

        assertEquals(expected, result)
    }

    private fun overlappingStops(): Stream<NewStop> {
        val stopEndingInsidePort = createNewStop(
            type = NewStopType.BERTH,
            end = createLocationTime(time = dayBeforePortEnd)
        )
        val stopStartingInsidePort = createNewStop(
            type = NewStopType.BERTH,
            start = createLocationTime(time = dayBeforePortEnd),
            end = createLocationTime(time = dayAfterPortEnd)
        )
        val stopStartingBeforeAndEndingAfterPort = createNewStop(
            type = NewStopType.BERTH,
            start = createLocationTime(time = dayBeforePortStart),
            end = createLocationTime(time = dayAfterPortEnd)
        )
        val stopStartingOnPortStart = createNewStop(
            type = NewStopType.BERTH,
            start = createLocationTime(time = portStartTime),
            end = createLocationTime(time = dayAfterPortEnd)
        )
        val stopEndingOnPortStart = createNewStop(
            type = NewStopType.BERTH,
            start = createLocationTime(time = dayBeforePortStart),
            end = createLocationTime(time = portStartTime)
        )
        val stopStartingOnPortEnd = createNewStop(
            type = NewStopType.BERTH,
            start = createLocationTime(time = portStartTime),
            end = createLocationTime(time = dayAfterPortEnd)
        )
        val stopEndingOnPortEnd = createNewStop(
            type = NewStopType.BERTH,
            start = createLocationTime(time = portStartTime),
            end = createLocationTime(time = portEndTime)
        )
        val stopStartingAndEndingOnPortStartAndEnd = createNewStop(
            type = NewStopType.BERTH,
            start = createLocationTime(time = portStartTime),
            end = createLocationTime(time = portEndTime)
        )
        val stopStartingAfterPortStartAndEndingBeforePortEnd = createNewStop(
            type = NewStopType.BERTH,
            start = createLocationTime(time = dayAfterPortStart),
            end = createLocationTime(time = dayBeforePortEnd)
        )
        val stopStartingAfterPorStartAndEndingAfterPortEnd = createNewStop(
            type = NewStopType.BERTH,
            start = createLocationTime(time = dayAfterPortStart),
            end = createLocationTime(time = dayBeforePortEnd)
        )
        val stopStartingBeforePortStartAndEndingBeforePortEnd = createNewStop(
            type = NewStopType.BERTH,
            start = createLocationTime(time = dayBeforePortStart),
            end = createLocationTime(time = dayBeforePortEnd)
        )
        val ongoingStopStartingOnPortStart = createNewStop(
            type = NewStopType.BERTH,
            start = createLocationTime(time = portStartTime)
        )
        val ongoingStopStartingAfterPortStart = createNewStop(
            type = NewStopType.BERTH,
            start = createLocationTime(time = dayAfterPortStart)
        )
        val ongoingStopStartingOnPortEnd = createNewStop(
            type = NewStopType.BERTH,
            start = createLocationTime(time = portEndTime)
        )
        val ongoingStopStartingBeforePortEnd = createNewStop(
            type = NewStopType.BERTH,
            start = createLocationTime(time = dayBeforePortEnd)
        )

        return Stream.of(
            // Test case of finished stops
            stopStartingInsidePort,
            stopStartingOnPortStart,
            stopStartingOnPortEnd,

            stopStartingBeforeAndEndingAfterPort, // Full overlap
            stopStartingAfterPortStartAndEndingBeforePortEnd, // Port fully overlapping stop
            stopStartingAndEndingOnPortStartAndEnd,
            stopStartingAfterPorStartAndEndingAfterPortEnd,
            stopStartingBeforePortStartAndEndingBeforePortEnd,

            stopEndingInsidePort,
            stopEndingOnPortStart,
            stopEndingOnPortEnd,

            // Test cases of stops that are still ongoing
            ongoingStopStartingOnPortStart,
            ongoingStopStartingAfterPortStart,
            ongoingStopStartingOnPortEnd,
            ongoingStopStartingBeforePortEnd
        )
    }

    @ParameterizedTest
    @MethodSource("overlappingStops")
    fun `should process port end event updating visit with overlapping stops`(stop: NewStop) {
        val portEndTime = DEFAULT_START_TIME.plusMonths(2)
        val event = createAreaPortEvent(
            eventId = eventId,
            status = EventStatus.END,
            time = portEndTime,
            port = PORT_NLRTM
        )
        val currentPortAreaActivity = AreaActivity(
            id = eventId,
            start = createLocationTime(),
            end = null,
            areaId = PORT_NLRTM.portId
        )
        val currentVisit = createNewVisit(
            portAreaActivities = listOf(currentPortAreaActivity),
            stops = listOf(stop)
        )
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedPortEndLocationTime = LocationTime(
            location = PORT_NLRTM.location.toSkeletonLocation(),
            time = portEndTime.toInstant()
        )
        val expectedPortAreaActivity = currentPortAreaActivity.copy(
            end = expectedPortEndLocationTime
        )
        val expectedVisit = currentVisit.copy(
            portAreaActivities = mutableListOf(expectedPortAreaActivity)
        )
        val expected = NewEventProcessingResult(
            status = currentStatus.copy(
                visit = EntryESoFWrapper(
                    entry = expectedVisit,
                    esof = null
                )
            ),
            changes = listOf(
                VisitChange(Action.UPDATE, expectedVisit)
            )
        )

        assertEquals(expected, result)
    }

    private fun notOverlappingStops(): Stream<List<NewStop>> {
        val stopEndingBeforePort = createNewStop(
            type = NewStopType.BERTH,
            start = createLocationTime(time = dayBeforePortStart.minus(1, ChronoUnit.DAYS)),
            end = createLocationTime(time = dayBeforePortStart)
        )
        val stopStartingAfterPort = createNewStop(
            type = NewStopType.BERTH,
            start = createLocationTime(time = dayAfterPortEnd),
            end = createLocationTime(time = dayAfterPortEnd.plus(1, ChronoUnit.DAYS))
        )

        return Stream.of(
            listOf(stopEndingBeforePort),
            listOf(stopEndingBeforePort, stopStartingAfterPort),
            listOf(stopStartingAfterPort)
        )
    }

    @ParameterizedTest
    @MethodSource("notOverlappingStops")
    fun `should process port end event making activity a port pass through`(visitStops: List<NewStop>) {
        val portEndTime = DEFAULT_START_TIME.plusMonths(2)
        val event = createAreaPortEvent(
            eventId = eventId,
            status = EventStatus.END,
            time = portEndTime,
            port = PORT_NLRTM
        )
        val currentPortAreaActivity = AreaActivity(
            id = eventId,
            start = createLocationTime(time = DEFAULT_START_TIME.plusMonths(1).toInstant()),
            end = null,
            areaId = PORT_NLRTM.portId
        )
        val currentVisit = createNewVisit(
            portAreaActivities = listOf(currentPortAreaActivity),
            stops = visitStops
        )
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedPortEndLocationTime = LocationTime(
            location = PORT_NLRTM.location.toSkeletonLocation(),
            time = portEndTime.toInstant()
        )
        val expectedPortPassThrough = currentPortAreaActivity.copy(
            end = expectedPortEndLocationTime
        )
        val expectedVisit = currentVisit.copy(
            portAreaActivities = mutableListOf(),
            passThroughPort = listOf(expectedPortPassThrough)
        )
        val expected = NewEventProcessingResult(
            status = currentStatus.copy(
                visit = EntryESoFWrapper(
                    entry = expectedVisit,
                    esof = null
                )
            ),
            changes = listOf(
                VisitChange(Action.UPDATE, expectedVisit)
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should ignore port end event when we never received a start event for the same port`() {
        val event = createAreaPortEvent(
            eventId = eventId,
            status = EventStatus.END,
            time = DEFAULT_START_TIME,
            port = PORT_NLRTM
        )

        val currentVisit = createNewVisit()
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields
        val expected = NewEventProcessingResult(
            status = currentStatus,
            changes = emptyList(),
            issues = listOf(
                EventProcessingIssue(
                    eventId = eventId,
                    description = "Currently known area activities don't match the area id or are already finished. (area id: ${PORT_NLRTM.portId})"
                )
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process encounter start event and update esof of ongoing visit`() {
        val pilotEvent = createEncounterEvent(
            eventId = eventId,
            status = EventStatus.START,
            time = DEFAULT_START_TIME,
            type = EncounterType.PILOT,
            otherMmsi = MMSI_2.toString(),
            otherImo = IMO_2.toString()
        )

        val currentVisit = createNewVisit()
        val currentEsof = createNewESoF(_id = currentVisit._id)
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(currentVisit, currentEsof),
            previousVoyage = null,
            previousVisit = null
        )

        val result = service.onEventAndBuffer(currentStatus, pilotEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedEncounter = NewEncounter(
            type = EncounterType.PILOT,
            otherMmsi = MMSI_2.toInt(),
            otherImo = IMO_2.toInt(),
            startEventId = eventId,
            start = LocationTime(
                location = PORT_NLRTM.location.toSkeletonLocation(),
                time = DEFAULT_START_TIME.toInstant()
            ),
            end = null
        )
        val expectedEsof = currentEsof.copy(
            encounters = listOf(expectedEncounter)
        )
        val expectedStatus = currentStatus.copy(
            visit = EntryESoFWrapper(currentVisit, expectedEsof)
        )

        val expected = NewEventProcessingResult(
            status = expectedStatus,
            changes = listOf(
                ESoFChange(Action.UPDATE, expectedEsof)
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process encounter start event and create new esof when non existing`() {
        val pilotEvent = createEncounterEvent(
            eventId = eventId,
            status = EventStatus.START,
            time = DEFAULT_START_TIME,
            type = EncounterType.PILOT,
            otherMmsi = MMSI_2.toString(),
            otherImo = IMO_2.toString()
        )

        val currentVisit = createNewVisit()
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(currentVisit, null),
            previousVoyage = null,
            previousVisit = null
        )

        val result = service.onEventAndBuffer(currentStatus, pilotEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedEncounter = NewEncounter(
            type = EncounterType.PILOT,
            otherMmsi = MMSI_2.toInt(),
            otherImo = IMO_2.toInt(),
            startEventId = eventId,
            start = LocationTime(
                location = PORT_NLRTM.location.toSkeletonLocation(),
                time = DEFAULT_START_TIME.toInstant()
            ),
            end = null
        )
        val expectedEsof = createNewESoF(
            _id = currentVisit._id,
            encounters = listOf(expectedEncounter)
        )
        val expectedStatus = currentStatus.copy(
            visit = EntryESoFWrapper(currentVisit, expectedEsof)
        )

        val expected = NewEventProcessingResult(
            status = expectedStatus,
            changes = listOf(
                ESoFChange(Action.CREATE, expectedEsof)
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process encounter start event and create new esof when non existing for flipped imo on event`() {
        val pilotEvent = createEncounterEvent(
            eventId = eventId,
            status = EventStatus.START,
            time = DEFAULT_START_TIME,
            type = EncounterType.PILOT,
            otherMmsi = MMSI_1.toString(),
            otherImo = IMO_1.toString(),
            mmsi = MMSI_2,
            imo = IMO_2
        )

        val currentVisit = createNewVisit()
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(currentVisit, null),
            previousVoyage = null,
            previousVisit = null
        )

        val result = service.onEventAndBuffer(currentStatus, pilotEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedEncounter = NewEncounter(
            type = EncounterType.PILOT,
            otherMmsi = MMSI_2.toInt(),
            otherImo = IMO_2.toInt(),
            startEventId = eventId,
            start = LocationTime(
                location = PORT_NLRTM.location.toSkeletonLocation(),
                time = DEFAULT_START_TIME.toInstant()
            ),
            end = null
        )
        val expectedEsof = createNewESoF(
            _id = currentVisit._id,
            encounters = listOf(expectedEncounter)
        )
        val expectedStatus = currentStatus.copy(
            visit = EntryESoFWrapper(currentVisit, expectedEsof)
        )

        val expected = NewEventProcessingResult(
            status = expectedStatus,
            changes = listOf(
                ESoFChange(Action.CREATE, expectedEsof)
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should ignore encounter event when provided IMO is not of vessel`() {
        val pilotEvent = createEncounterEvent(
            eventId = eventId,
            status = EventStatus.START,
            time = DEFAULT_START_TIME,
            type = EncounterType.PILOT,
            mmsi = MMSI_3,
            imo = IMO_3,
            otherMmsi = MMSI_2.toString(),
            otherImo = null
        )

        val currentVisit = createNewVisit()
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(currentVisit, null),
            previousVoyage = null,
            previousVisit = null
        )

        val result = service.onEventAndBuffer(currentStatus, pilotEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expected = NewEventProcessingResult(
            status = currentStatus,
            changes = emptyList(),
            issues = listOf(
                EventProcessingIssue(
                    eventId = eventId,
                    description = buildString {
                        append("Event has mismatching IMO: ship status has IMO $IMO_1, event has IMO $IMO_3. ")
                        append("New event will be ignored (eventId: $eventId)")
                    }
                )
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should ignore encounter start event when already having an ongoing encounter`() {
        val pilotEvent = createEncounterEvent(
            eventId = eventId,
            status = EventStatus.START,
            time = DEFAULT_START_TIME.plusDays(2),
            type = EncounterType.PILOT,
            otherMmsi = MMSI_2.toString(),
            otherImo = IMO_2.toString()
        )

        val currentVisit = createNewVisit()
        val currentEncounter = NewEncounter(
            type = EncounterType.PILOT,
            otherMmsi = MMSI_2.toInt(),
            otherImo = IMO_2.toInt(),
            startEventId = eventId,
            start = LocationTime(
                location = PORT_NLRTM.location.toSkeletonLocation(),
                time = DEFAULT_START_TIME.toInstant()
            ),
            end = null
        )
        val currentEsof = createNewESoF(
            _id = currentVisit._id,
            encounters = listOf(currentEncounter)
        )
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(currentVisit, currentEsof),
            previousVoyage = null,
            previousVisit = null
        )

        val result = service.onEventAndBuffer(currentStatus, pilotEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expected = NewEventProcessingResult(
            status = currentStatus,
            changes = emptyList(),
            issues = listOf(
                EventProcessingIssue(
                    eventId = eventId,
                    description = "Cannot process encounter event, same encounter already ongoing (other mmsi: $MMSI_2)"
                )
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process encounter start event and update esof of ongoing voyage`() {
        val pilotEvent = createEncounterEvent(
            eventId = eventId,
            status = EventStatus.START,
            time = DEFAULT_START_TIME,
            type = EncounterType.PILOT,
            otherMmsi = MMSI_2.toString(),
            otherImo = IMO_2.toString()
        )

        val currentVoyage = createNewVoyage()
        val currentEsof = createNewESoF(_id = currentVoyage._id)
        val currentStatus = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(currentVoyage, currentEsof),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, pilotEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedEncounter = NewEncounter(
            type = EncounterType.PILOT,
            otherMmsi = MMSI_2.toInt(),
            otherImo = IMO_2.toInt(),
            startEventId = eventId,
            start = LocationTime(
                location = PORT_NLRTM.location.toSkeletonLocation(),
                time = DEFAULT_START_TIME.toInstant()
            ),
            end = null
        )
        val expectedEsof = currentEsof.copy(
            encounters = listOf(expectedEncounter)
        )
        val expectedStatus = currentStatus.copy(
            voyage = EntryESoFWrapper(currentVoyage, expectedEsof)
        )

        val expected = NewEventProcessingResult(
            status = expectedStatus,
            changes = listOf(
                ESoFChange(Action.UPDATE, expectedEsof)
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process encounter end event and update esof of ongoing visit`() {
        val startEventTestId = "TEST_START_EVENT_ID"
        val pilotEvent = createEncounterEvent(
            eventId = eventId,
            relatedEvent = startEventTestId,
            status = EventStatus.END,
            time = DEFAULT_END_TIME,
            type = EncounterType.PILOT,
            otherMmsi = MMSI_2.toString(),
            otherImo = IMO_2.toString()
        )

        val currentVisit = createNewVisit()
        val currentEncounter = NewEncounter(
            type = EncounterType.PILOT,
            otherMmsi = MMSI_2.toInt(),
            otherImo = IMO_2.toInt(),
            startEventId = startEventTestId,
            start = LocationTime(
                location = PORT_NLRTM.location.toSkeletonLocation(),
                time = DEFAULT_START_TIME.toInstant()
            ),
            end = null
        )
        val currentEsof = createNewESoF(
            _id = currentVisit._id,
            encounters = listOf(currentEncounter)
        )
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(currentVisit, currentEsof),
            previousVoyage = null,
            previousVisit = null
        )

        val result = service.onEventAndBuffer(currentStatus, pilotEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedEncounter = currentEncounter.copy(
            end = LocationTime(
                location = PORT_NLRTM.location.toSkeletonLocation(),
                time = DEFAULT_END_TIME.toInstant()
            )
        )
        val expectedEsof = currentEsof.copy(
            encounters = listOf(expectedEncounter)
        )
        val expectedStatus = currentStatus.copy(
            visit = EntryESoFWrapper(currentVisit, expectedEsof)
        )

        val expected = NewEventProcessingResult(
            status = expectedStatus,
            changes = listOf(
                ESoFChange(Action.UPDATE, expectedEsof)
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process encounter end event and update esof of previous visit on ongoing voyage`() {
        val startEventTestId = "TEST_START_EVENT_ID"
        val pilotEvent = createEncounterEvent(
            eventId = eventId,
            relatedEvent = startEventTestId,
            status = EventStatus.END,
            time = DEFAULT_END_TIME,
            type = EncounterType.PILOT,
            otherMmsi = MMSI_2.toString(),
            otherImo = IMO_2.toString()
        )

        val previousVisit = createNewVisit()
        val currentVoyage = createNewVoyage(previous = previousVisit._id)
        val fallbackFinishedEncounter = NewEncounter(
            type = EncounterType.PILOT,
            otherMmsi = MMSI_2.toInt(),
            otherImo = IMO_2.toInt(),
            startEventId = startEventTestId,
            start = LocationTime(
                location = PORT_NLRTM.location.toSkeletonLocation(),
                time = DEFAULT_START_TIME.toInstant()
            ),
            end = createLocationTime(fallback = FallbackType.ACTIVITY_END_BY_EOSP)
        )
        val currentEsof = createNewESoF(
            _id = previousVisit._id,
            encounters = listOf(fallbackFinishedEncounter)
        )
        val currentStatus = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(currentVoyage, null),
            previousVisit = EntryESoFWrapper(previousVisit, currentEsof),
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, pilotEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedEncounter = fallbackFinishedEncounter.copy(
            end = LocationTime(
                location = PORT_NLRTM.location.toSkeletonLocation(),
                time = DEFAULT_END_TIME.toInstant()
            )
        )
        val expectedEsof = currentEsof.copy(
            encounters = listOf(expectedEncounter)
        )
        val expectedStatus = currentStatus.copy(
            previousVisit = EntryESoFWrapper(previousVisit, expectedEsof)
        )

        val expected = NewEventProcessingResult(
            status = expectedStatus,
            changes = listOf(
                ESoFChange(Action.UPDATE, expectedEsof)
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should ignore encounter end event when having no fallbacked encounters on the previous visit on a ongoing voyage`() {
        val startEventTestId = "TEST_START_EVENT_ID"
        val pilotEvent = createEncounterEvent(
            eventId = eventId,
            relatedEvent = startEventTestId,
            status = EventStatus.END,
            time = DEFAULT_END_TIME,
            type = EncounterType.PILOT,
            otherMmsi = MMSI_2.toString(),
            otherImo = IMO_2.toString()
        )

        val previousVisit = createNewVisit()
        val currentVoyage = createNewVoyage(previous = previousVisit._id)
        val finishedEncounter = NewEncounter(
            type = EncounterType.PILOT,
            otherMmsi = MMSI_2.toInt(),
            otherImo = IMO_2.toInt(),
            startEventId = startEventTestId,
            start = createLocationTime(),
            end = createLocationTime()
        )
        val currentEsof = createNewESoF(
            _id = previousVisit._id,
            encounters = listOf(finishedEncounter)
        )
        val currentStatus = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(currentVoyage, null),
            previousVisit = EntryESoFWrapper(previousVisit, currentEsof),
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, pilotEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expected = NewEventProcessingResult(
            status = currentStatus,
            changes = emptyList(),
            issues = listOf(
                EventProcessingIssue(
                    eventId = eventId,
                    description = "Cannot process encounter event, we never received an encounter start"
                )
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should ignore encounter end event when having no esof on visit`() {
        val pilotEvent = createEncounterEvent(
            eventId = eventId,
            status = EventStatus.END,
            time = DEFAULT_START_TIME.plusDays(2),
            type = EncounterType.PILOT,
            otherMmsi = MMSI_2.toString(),
            otherImo = IMO_2.toString()
        )

        val currentVisit = createNewVisit()
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(currentVisit, null),
            previousVoyage = null,
            previousVisit = null
        )

        val result = service.onEventAndBuffer(currentStatus, pilotEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expected = NewEventProcessingResult(
            status = currentStatus,
            changes = emptyList(),
            issues = listOf(
                EventProcessingIssue(
                    eventId = eventId,
                    description = "Cannot process encounter event, we never received an encounter start"
                )
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should ignore encounter end event when having other encounter ongoing on visit`() {
        val pilotEvent = createEncounterEvent(
            eventId = eventId,
            status = EventStatus.END,
            time = DEFAULT_START_TIME.plusDays(2),
            type = EncounterType.PILOT,
            otherMmsi = MMSI_2.toString(),
            otherImo = IMO_2.toString()
        )

        val otherOngoingEncounter = NewEncounter(
            type = EncounterType.PILOT,
            otherMmsi = MMSI_3.toInt(),
            otherImo = IMO_3.toInt(),
            startEventId = "TEST_OTHER_SHIP_ENCOUNTER_ID",
            start = LocationTime(
                location = PORT_NLRTM.location.toSkeletonLocation(),
                time = DEFAULT_START_TIME.toInstant()
            ),
            end = null
        )
        val currentVisit = createNewVisit()
        val currentEsof = createNewESoF(
            _id = currentVisit._id,
            encounters = listOf(otherOngoingEncounter)
        )
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(currentVisit, currentEsof),
            previousVoyage = null,
            previousVisit = null
        )

        val result = service.onEventAndBuffer(currentStatus, pilotEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expected = NewEventProcessingResult(
            status = currentStatus,
            changes = emptyList(),
            issues = listOf(
                EventProcessingIssue(
                    eventId = eventId,
                    description = "Cannot process encounter event, we never received an encounter start"
                )
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should ignore encounter end event when having no ongoing encounter on voyage`() {
        val pilotEvent = createEncounterEvent(
            eventId = eventId,
            status = EventStatus.END,
            time = DEFAULT_START_TIME.plusDays(2),
            type = EncounterType.PILOT,
            otherMmsi = MMSI_2.toString(),
            otherImo = IMO_2.toString()
        )

        val otherOngoingEncounter = NewEncounter(
            type = EncounterType.PILOT,
            otherMmsi = MMSI_3.toInt(),
            otherImo = IMO_3.toInt(),
            startEventId = "TEST_OTHER_SHIP_ENCOUNTER_ID",
            start = LocationTime(
                location = PORT_NLRTM.location.toSkeletonLocation(),
                time = DEFAULT_START_TIME.toInstant()
            ),
            end = null
        )
        val currentVoyage = createNewVoyage()
        val currentEsof = createNewESoF(
            _id = currentVoyage._id,
            encounters = listOf(otherOngoingEncounter)
        )
        val currentStatus = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(currentVoyage, currentEsof),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, pilotEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expected = NewEventProcessingResult(
            status = currentStatus,
            changes = emptyList(),
            issues = listOf(
                EventProcessingIssue(
                    eventId = eventId,
                    description = "Cannot process encounter event, we never received an encounter start"
                )
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process stop start event when at visit status having no ongoing stop`() {
        val stopEvent = createStopEvent(eventId, EventStatus.START, DEFAULT_START_TIME, testEventLocation)
        val currentVisit = createNewVisit()
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(currentVisit, null),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, stopEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedStop = createNewStop(
            type = NewStopType.BERTH,
            areaId = "TEST_AREA_ID",
            startEventId = eventId,
            start = createLocationTime(testEventLocation, DEFAULT_START_TIME.toInstant())
        )
        val expectedVisit = createNewVisit(stops = listOf(expectedStop))
        val expectedStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(expectedVisit, null),
            previousVisit = null,
            previousVoyage = null
        )
        val expected = NewEventProcessingResult(
            status = expectedStatus,
            changes = listOf(VisitChange(Action.UPDATE, expectedVisit))
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process stop start event when at voyage status having no ongoing stop`() {
        val stopEvent = createStopEvent(eventId, EventStatus.START, DEFAULT_START_TIME, testEventLocation)
        val currentVoyage = createNewVoyage()
        val currentStatus = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(currentVoyage, null),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, stopEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedStop = createNewStop(
            type = NewStopType.BERTH,
            areaId = "TEST_AREA_ID",
            startEventId = eventId,
            start = createLocationTime(testEventLocation, DEFAULT_START_TIME.toInstant())
        )
        val expectedVoyage = createNewVoyage(stops = listOf(expectedStop))
        val expectedStatus = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(expectedVoyage, null),
            previousVisit = null,
            previousVoyage = null
        )
        val expected = NewEventProcessingResult(
            status = expectedStatus,
            changes = listOf(VoyageChange(Action.UPDATE, expectedVoyage))
        )

        assertEquals(expected, result)
    }

    @Test
    fun `process stop start event when at visit status having already an ongoing stop creating a new one`() {
        val newStopTime = DEFAULT_START_TIME.plusDays(10)
        val stopEvent = createStopEvent(eventId, EventStatus.START, newStopTime, testEventLocation)
        val currentOngoingStop = createNewStop(
            type = NewStopType.BERTH,
            areaId = "TEST_AREA_ID",
            startEventId = "OTHER_TEST_EVENT_ID",
            start = createLocationTime(testEventLocation, DEFAULT_START_TIME.toInstant())
        )
        val currentVisit = createNewVisit(stops = listOf(currentOngoingStop))
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(currentVisit, null),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, stopEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedFallbackFinishedStop = currentOngoingStop.copy(
            location = testEventLocation,
            end = createLocationTime(testEventLocation, newStopTime.toInstant(), FallbackType.ACTIVITY_END_BY_STOP_START)
        )
        val expectedNewStop = createNewStop(
            type = NewStopType.BERTH,
            areaId = "TEST_AREA_ID",
            startEventId = eventId,
            start = createLocationTime(testEventLocation, newStopTime.toInstant())
        )
        val expectedVisit = currentVisit.copy(
            stops = listOf(expectedFallbackFinishedStop, expectedNewStop)
        )
        val expected = NewEventProcessingResult(
            status = currentStatus.copy(
                visit = currentStatus.visit.copy(entry = expectedVisit)
            ),
            changes = listOf(VisitChange(Action.UPDATE, expectedVisit)),
            issues = emptyList()
        )

        assertEquals(expected, result)
    }

    @Test
    fun `process stop start event when at voyage status having already an ongoing stop creating a new one`() {
        val newStopTime = DEFAULT_START_TIME.plusDays(10)
        val stopEvent = createStopEvent(eventId, EventStatus.START, newStopTime, testEventLocation)
        val currentOngoingStop = createNewStop(
            type = NewStopType.BERTH,
            areaId = "TEST_AREA_ID",
            startEventId = "OTHER_TEST_EVENT_ID",
            start = createLocationTime(testEventLocation, DEFAULT_START_TIME.toInstant())
        )
        val currentVoyage = createNewVoyage(stops = listOf(currentOngoingStop))
        val currentStatus = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(currentVoyage, null),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, stopEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedFallbackFinishedStop = currentOngoingStop.copy(
            location = testEventLocation,
            end = createLocationTime(testEventLocation, newStopTime.toInstant(), FallbackType.ACTIVITY_END_BY_STOP_START)
        )
        val expectedNewStop = createNewStop(
            type = NewStopType.BERTH,
            areaId = "TEST_AREA_ID",
            startEventId = eventId,
            start = createLocationTime(testEventLocation, newStopTime.toInstant())
        )
        val expectedVoyage = currentVoyage.copy(
            stops = listOf(expectedFallbackFinishedStop, expectedNewStop)
        )
        val expected = NewEventProcessingResult(
            status = currentStatus.copy(
                voyage = currentStatus.voyage.copy(entry = expectedVoyage)
            ),
            changes = listOf(VoyageChange(Action.UPDATE, expectedVoyage)),
            issues = emptyList()
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should confirm visit when start stopping inside port`() {
        // Mock as if we would confirm the stop as unclassified
        whenever(infraService.findStopClassification(any()))
            .thenReturn(ClassifiedStop(NewStopType.UNCLASSIFIED, null))
        whenever(infraService.isPortMainPortById(any()))
            .thenReturn(true)

        // We stopped 1 day after we entered the port
        val portStartTime = DEFAULT_START_TIME.plusDays(2)
        val stopStartTime = portStartTime.plusDays(1)
        val stopEvent = createStopEvent(eventId, EventStatus.START, stopStartTime, testEventLocation)

        val ongoingPort = createAreaActivity(
            areaId = DEFAULT_TEST_PORT_ID,
            start = createLocationTime(time = portStartTime.toInstant())
        )
        val currentVisit = createNewVisit(portAreaActivities = listOf(ongoingPort))
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(currentVisit, null),
            previousVoyage = null,
            previousVisit = null
        )

        val result = service.onEventAndBuffer(currentStatus, stopEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedNewStop = createNewStop(
            type = NewStopType.UNCLASSIFIED,
            areaId = null,
            startEventId = eventId,
            start = createLocationTime(testEventLocation, stopStartTime.toInstant())
        )
        val expectedConfirmedVisit = currentVisit.copy(
            stops = listOf(expectedNewStop),
            confirmed = true
        )
        val expected = NewEventProcessingResult(
            status = currentStatus.copy(
                visit = currentStatus.visit.copy(entry = expectedConfirmedVisit)
            ),
            changes = listOf(VisitChange(Action.UPDATE, expectedConfirmedVisit)),
            issues = emptyList()
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should confirm and switch visit when start stopping inside other main port with overlapping eosps`() {
        // Mock as if we would confirm the stop as unclassified
        whenever(infraService.findStopClassification(any()))
            .thenReturn(ClassifiedStop(NewStopType.UNCLASSIFIED, null))
        whenever(infraService.isPortMainPortById(any()))
            .thenReturn(true)

        // We stopped 1 day after we entered the port
        val portStartTime = DEFAULT_START_TIME.plusDays(2)
        val stopStartTime = portStartTime.plusDays(1)
        val stopEvent = createStopEvent(eventId, EventStatus.START, stopStartTime, testEventLocation)

        val otherOngoingEosp = createAreaActivity(id = "OTHER_EOSP_ID", areaId = eospAreaId1)
        val otherOngoingPortId = "aaaaa-bbbbb-ccccc"
        val ongoingPort = createAreaActivity(
            areaId = otherOngoingPortId,
            start = createLocationTime(time = portStartTime.toInstant())
        )
        val currentVisit = createNewVisit(
            portAreaActivities = listOf(ongoingPort),
            otherOngoingEospAreaActivities = listOf(otherOngoingEosp)
        )
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(currentVisit, null),
            previousVoyage = null,
            previousVisit = null
        )

        val result = service.onEventAndBuffer(currentStatus, stopEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedNewStop = createNewStop(
            type = NewStopType.UNCLASSIFIED,
            areaId = null,
            startEventId = eventId,
            start = createLocationTime(testEventLocation, stopStartTime.toInstant())
        )

        // EOSP is switched because we stopped in the other port
        val expectedConfirmedOtherPortVisit = currentVisit.copy(
            _id = "OTHER_EOSP_ID.VISIT",
            otherOngoingEospAreaActivities = listOf(currentVisit.eospAreaActivity),
            eospAreaActivity = otherOngoingEosp,
            stops = listOf(expectedNewStop),
            confirmed = true
        )
        val expected = NewEventProcessingResult(
            status = currentStatus.copy(
                visit = currentStatus.visit.copy(entry = expectedConfirmedOtherPortVisit)
            ),
            changes = listOf(
                VisitChange(Action.CREATE, expectedConfirmedOtherPortVisit),
                VisitChange(Action.DELETE, currentVisit),
            ),
            issues = emptyList()
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should ignore event when receiving eosp end event of never entered port`() {
        val currentVisit = createNewVisit()
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(currentVisit, null),
            previousVoyage = null,
            previousVisit = null
        )

        val eospEndEvent = createEndOfSeaPassageEvent(
            _id = eventId,
            status = EventStatus.END,
            time = DEFAULT_END_TIME,
            areaId = eospAreaId1
        )

        val result = service.onEventAndBuffer(currentStatus, eospEndEvent, processingTestTime)

        val expected = NewEventProcessingResult(
            status = currentStatus,
            changes = emptyList(),
            issues = listOf(
                EventProcessingIssue(
                    eventId = eventId,
                    description = "End event received is not for any of the main ports we are currently visiting"
                )
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should limit visit when starting the 50th stop`() {
        val currentStops = repeatCreate(49) {
            createNewStop(
                type = NewStopType.BERTH,
                startEventId = "TEST_START_EVENT_ID",
                endEventId = "TEST_END_EVENT_ID",
                end = createLocationTime()
            )
        }
        val currentVisit = createNewVisit(stops = currentStops)
        val currentVisitStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVoyage = null,
            previousVisit = null
        )

        val event = createStopEvent(eventId, EventStatus.START, DEFAULT_START_TIME.plusDays(1), testEventLocation)
        val result = service.onEventAndBuffer(currentVisitStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedStop = createNewStop(
            type = NewStopType.BERTH,
            areaId = "TEST_AREA_ID",
            startEventId = eventId,
            start = createLocationTime(testEventLocation, DEFAULT_START_TIME.plusDays(1).toInstant())
        )
        val expectedVisit = currentVisit.copy(
            stops = currentStops + expectedStop,
            limited = true
        )
        val expected = NewEventProcessingResult(
            status = NewVisitShipStatus(
                visit = EntryESoFWrapper(entry = expectedVisit, esof = null),
                previousVoyage = null,
                previousVisit = null
            ),
            changes = listOf(
                VisitChange(Action.UPDATE, expectedVisit)
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should not start new stop when already limited`() {
        val currentVisit = createNewVisit(limited = true)
        val currentVisitStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVoyage = null,
            previousVisit = null
        )

        val event = createStopEvent(eventId, EventStatus.START, DEFAULT_START_TIME, testEventLocation)
        val result = service.onEventAndBuffer(currentVisitStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields
        val expected = NewEventProcessingResult(
            status = NewVisitShipStatus(
                visit = EntryESoFWrapper(entry = currentVisit, esof = null),
                previousVoyage = null,
                previousVisit = null,
                eventBuffer = mutableListOf(event)
            ),
            changes = emptyList(),
            issues = listOf(
                EventProcessingIssue(eventId, "Could not create any new activities because the current entry is already limited.")
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should end ongoing stop when already limited`() {
        val currentStop = createNewStop(type = NewStopType.BERTH, areaId = "TEST_AREA_ID")
        val currentVisit = createNewVisit(limited = true, stops = listOf(currentStop))
        val currentVisitStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVoyage = null,
            previousVisit = null
        )

        val event = createStopEvent(
            _id = eventId,
            status = EventStatus.END,
            time = DEFAULT_END_TIME,
            location = testEventLocation,
            startEventId = currentStop.startEventId,
            actualLocation = testEventLocation
        )
        val result = service.onEventAndBuffer(currentVisitStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields
        val expectedStop = currentStop.copy(
            endEventId = eventId,
            end = createLocationTime(
                location = testEventLocation,
                time = DEFAULT_END_TIME.toInstant()
            ),
            location = testEventLocation
        )
        val expectedVisit = currentVisit.copy(stops = listOf(expectedStop))
        val expected = NewEventProcessingResult(
            status = NewVisitShipStatus(
                visit = EntryESoFWrapper(entry = expectedVisit, esof = null),
                previousVoyage = null,
                previousVisit = null
            ),
            changes = listOf(
                VisitChange(Action.UPDATE, expectedVisit)
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should limit visit when reaching too many eosp pass throughs`() {
        val passThroughEosps = mutableListOf<AreaActivity>()
        repeat(49) {
            passThroughEosps.add(createAreaActivity(areaId = "TEST_AREA_ID.eosp", end = createLocationTime()))
        }

        val currentOtherEosp = createAreaActivity(id = "TEST_EOSP_START_EVENT_ID", areaId = eospAreaId1)
        val currentVisit = createNewVisit(
            otherOngoingEospAreaActivities = listOf(currentOtherEosp),
            passThroughEosp = passThroughEosps
        )
        val currentVisitStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVoyage = null,
            previousVisit = null
        )

        val eventTime = DEFAULT_START_TIME.plusDays(1)
        val event = createEndOfSeaPassageEvent(
            _id = eventId2,
            status = EventStatus.END,
            time = eventTime,
            areaId = eospAreaId1
        )

        val result = service.onEventAndBuffer(currentVisitStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields
        val expectedOtherEosp = currentOtherEosp.copy(end = createLocationTime(location = event.location, time = eventTime.toInstant()))
        val expectedEospPassThroughs = currentVisit.passThroughEosp + expectedOtherEosp
        val expectedVisit = currentVisit.copy(
            otherOngoingEospAreaActivities = emptyList(),
            passThroughEosp = expectedEospPassThroughs,
            limited = true
        )
        val expected = NewEventProcessingResult(
            status = NewVisitShipStatus(
                visit = EntryESoFWrapper(entry = expectedVisit, esof = null),
                previousVoyage = null,
                previousVisit = null
            ),
            changes = listOf(
                VisitChange(Action.UPDATE, expectedVisit)
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should limit visit when reaching too many port pass throughs`() {
        val passThroughPorts = mutableListOf<AreaActivity>()
        repeat(49) {
            passThroughPorts.add(createAreaActivity(areaId = "TEST_AREA_ID.eosp", end = createLocationTime()))
        }

        val currentPortAreaActivity = createAreaActivity(id = "TEST_PORT_START_EVENT_ID", areaId = PORT_NLRTM.portId)
        val currentVisit = createNewVisit(
            portAreaActivities = listOf(currentPortAreaActivity),
            passThroughPort = passThroughPorts
        )
        val currentVisitStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = currentVisit, esof = null),
            previousVoyage = null,
            previousVisit = null
        )

        val eventTime = DEFAULT_START_TIME.plusDays(1)
        val event = createAreaPortEvent(
            eventId = eventId2,
            status = EventStatus.END,
            time = eventTime,
            port = PORT_NLRTM
        )

        val result = service.onEventAndBuffer(currentVisitStatus, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields
        val expectedPortPassThrough = currentPortAreaActivity.copy(
            end = createLocationTime(location = PORT_NLRTM.location.toSkeletonLocation(), time = eventTime.toInstant())
        )
        val expectedPortPassThroughs = currentVisit.passThroughPort + expectedPortPassThrough
        val expectedVisit = currentVisit.copy(
            portAreaActivities = mutableListOf(),
            passThroughPort = expectedPortPassThroughs,
            limited = true
        )
        val expected = NewEventProcessingResult(
            status = NewVisitShipStatus(
                visit = EntryESoFWrapper(entry = expectedVisit, esof = null),
                previousVoyage = null,
                previousVisit = null
            ),
            changes = listOf(
                VisitChange(Action.UPDATE, expectedVisit)
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process stop end event when at visit status having matching ongoing stop`() {
        val stopEvent = createStopEvent(eventId, EventStatus.END, DEFAULT_END_TIME, testEventLocation, "TEST_START_EVENT_ID", testActualLocation)
        val currentOngoingStop = createNewStop(
            type = NewStopType.BERTH,
            areaId = "TEST_AREA_ID",
            startEventId = "TEST_START_EVENT_ID",
            start = createLocationTime(testEventLocation, DEFAULT_START_TIME.toInstant())
        )
        val currentVisit = createNewVisit(stops = listOf(currentOngoingStop))
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(currentVisit, null),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, stopEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedStop = currentOngoingStop.copy(
            type = NewStopType.ANCHOR_AREA,
            areaId = "TEST_ACTUAL_AREA_ID",
            location = testActualLocation,
            endEventId = eventId,
            end = createLocationTime(testEventLocation, DEFAULT_END_TIME.toInstant())
        )
        val expectedVisit = createNewVisit(stops = listOf(expectedStop))
        val expectedStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(expectedVisit, null),
            previousVisit = null,
            previousVoyage = null
        )
        val expected = NewEventProcessingResult(
            status = expectedStatus,
            changes = listOf(VisitChange(Action.UPDATE, expectedVisit))
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process stop end event when at voyage status having matching ongoing stop`() {
        val stopEvent = createStopEvent(eventId, EventStatus.END, DEFAULT_END_TIME, testEventLocation, "TEST_START_EVENT_ID", testActualLocation)
        val currentOngoingStop = createNewStop(
            type = NewStopType.BERTH,
            areaId = "TEST_AREA_ID",
            startEventId = "TEST_START_EVENT_ID",
            start = createLocationTime(testEventLocation, DEFAULT_START_TIME.toInstant())
        )
        val currentVoyage = createNewVoyage(stops = listOf(currentOngoingStop))
        val currentStatus = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(currentVoyage, null),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, stopEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedStop = currentOngoingStop.copy(
            type = NewStopType.ANCHOR_AREA,
            areaId = "TEST_ACTUAL_AREA_ID",
            location = testActualLocation,
            endEventId = eventId,
            end = createLocationTime(testEventLocation, DEFAULT_END_TIME.toInstant())
        )
        val expectedVoyage = createNewVoyage(stops = listOf(expectedStop))
        val expectedStatus = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(expectedVoyage, null),
            previousVisit = null,
            previousVoyage = null
        )
        val expected = NewEventProcessingResult(
            status = expectedStatus,
            changes = listOf(VoyageChange(Action.UPDATE, expectedVoyage))
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process stop end event when at visit status having matching ongoing stop in previous voyage`() {
        val stopEvent = createStopEvent(eventId, EventStatus.END, DEFAULT_END_TIME, testEventLocation, "TEST_START_EVENT_ID", testActualLocation)
        val currentOngoingStop = createNewStop(
            type = NewStopType.BERTH,
            areaId = "TEST_AREA_ID",
            startEventId = "TEST_START_EVENT_ID",
            start = createLocationTime(testEventLocation, DEFAULT_START_TIME.toInstant())
        )
        val previousVoyage = createNewVoyage(stops = listOf(currentOngoingStop))
        val currentVisit = createNewVisit(previous = previousVoyage._id)
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(currentVisit, null),
            previousVoyage = EntryESoFWrapper(previousVoyage, null),
            previousVisit = null
        )

        val result = service.onEventAndBuffer(currentStatus, stopEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedVoyageStop = currentOngoingStop.copy(
            type = NewStopType.ANCHOR_AREA,
            areaId = "TEST_ACTUAL_AREA_ID",
            endEventId = eventId,
            end = currentVisit.start
        )
        val expectedVisitStop = createNewStop(
            type = NewStopType.ANCHOR_AREA,
            areaId = "TEST_ACTUAL_AREA_ID",
            startEventId = "TEST_START_EVENT_ID",
            start = currentVisit.start,
            endEventId = eventId,
            end = createLocationTime(testEventLocation, DEFAULT_END_TIME.toInstant())
        )

        val expectedPreviousVoyage = createNewVoyage(stops = listOf(expectedVoyageStop))
        val expectedVisit = createNewVisit(
            stops = listOf(expectedVisitStop),
            previous = expectedPreviousVoyage._id
        )
        val expectedStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(expectedVisit, null),
            previousVoyage = EntryESoFWrapper(expectedPreviousVoyage, null),
            previousVisit = null
        )
        val expected = NewEventProcessingResult(
            status = expectedStatus,
            changes = listOf(
                VoyageChange(Action.UPDATE, expectedPreviousVoyage),
                VisitChange(Action.UPDATE, expectedVisit)
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process stop end event when at voyage status having matching ongoing stop in previous visit`() {
        val stopEvent = createStopEvent(eventId, EventStatus.END, DEFAULT_END_TIME, testEventLocation, "TEST_START_EVENT_ID", testActualLocation)
        val currentOngoingStop = createNewStop(
            type = NewStopType.BERTH,
            areaId = "TEST_AREA_ID",
            startEventId = "TEST_START_EVENT_ID",
            start = createLocationTime(testEventLocation, DEFAULT_START_TIME.toInstant())
        )
        val previousVisit = createNewVisit(stops = listOf(currentOngoingStop))
        val currentVoyage = createNewVoyage(previous = previousVisit._id)
        val currentStatus = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(currentVoyage, null),
            previousVisit = EntryESoFWrapper(previousVisit, null),
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, stopEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedVoyageStop = currentOngoingStop.copy(
            type = NewStopType.ANCHOR_AREA,
            areaId = "TEST_ACTUAL_AREA_ID",
            endEventId = eventId,
            end = currentVoyage.start
        )
        val expectedVisitStop = createNewStop(
            type = NewStopType.ANCHOR_AREA,
            areaId = "TEST_ACTUAL_AREA_ID",
            startEventId = "TEST_START_EVENT_ID",
            start = currentVoyage.start,
            endEventId = eventId,
            end = createLocationTime(testEventLocation, DEFAULT_END_TIME.toInstant())
        )

        val expectedPreviousVisit = createNewVisit(stops = listOf(expectedVoyageStop))
        val expectedVoyage = createNewVoyage(
            stops = listOf(expectedVisitStop),
            previous = expectedPreviousVisit._id
        )
        val expectedStatus = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(expectedVoyage, null),
            previousVisit = EntryESoFWrapper(expectedPreviousVisit, null),
            previousVoyage = null,
        )
        val expected = NewEventProcessingResult(
            status = expectedStatus,
            changes = listOf(
                VisitChange(Action.UPDATE, expectedPreviousVisit),
                VoyageChange(Action.UPDATE, expectedVoyage)
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should ignore stop end event when at visit status with no matching ongoing stop`() {
        val stopEvent = createStopEvent(eventId, EventStatus.END, DEFAULT_END_TIME, testEventLocation, "TEST_START_EVENT_ID", testActualLocation)
        val currentOngoingStop = createNewStop(
            type = NewStopType.BERTH,
            areaId = "TEST_AREA_ID",
            startEventId = "TEST_OTHER_START_EVENT_ID",
            start = createLocationTime(testEventLocation, DEFAULT_START_TIME.toInstant())
        )
        val currentVisit = createNewVisit(stops = listOf(currentOngoingStop))
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(currentVisit, null),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, stopEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields
        val expected = NewEventProcessingResult(
            status = currentStatus,
            changes = emptyList(),
            issues = listOf(EventProcessingIssue(eventId, "Cannot process stop event, stop was never started or already finished in current entry"))
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should ignore stop end event when at visit status having no ongoing stop in previous voyage`() {
        val stopEvent = createStopEvent(eventId, EventStatus.END, DEFAULT_END_TIME, testEventLocation, "TEST_START_EVENT_ID", testActualLocation)
        val currentOngoingStop = createNewStop(
            type = NewStopType.BERTH,
            areaId = "TEST_AREA_ID",
            startEventId = "TEST_OTHER_START_EVENT_ID",
            start = createLocationTime(testEventLocation, DEFAULT_START_TIME.toInstant())
        )
        val previousVoyage = createNewVoyage(stops = listOf(currentOngoingStop))
        val currentVisit = createNewVisit(previous = previousVoyage._id)
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(currentVisit, null),
            previousVoyage = EntryESoFWrapper(previousVoyage, null),
            previousVisit = null
        )

        val result = service.onEventAndBuffer(currentStatus, stopEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields
        val expected = NewEventProcessingResult(
            status = currentStatus,
            changes = emptyList(),
            issues = listOf(EventProcessingIssue(eventId, "Cannot process stop event, stop was never started or already finished in previous and current entry"))
        )

        assertEquals(expected, result)
    }

    @ParameterizedTest
    @MethodSource("ignoredEventsTestData")
    fun `should ignore event on unsupported status`(status: NewShipStatus, event: Event, expectedIssues: List<EventProcessingIssue>) {
        val result = service.onEventAndBuffer(status, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        assertTrue(result.changes.isEmpty())
        assertEquals(expectedIssues, result.issues)
        assertEquals(status, result.status)
    }

    private fun ignoredEventsTestData(): Stream<Arguments> {
        val visitStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = createNewVisit(), esof = null),
            previousVoyage = null,
            previousVisit = null
        )
        val voyageStatus = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(entry = createNewVoyage(), esof = null),
            previousVisit = null,
            previousVoyage = null
        )
        val initialStatus = NewInitialShipStatus()

        val endOfSeaPassageEndEvent = createEndOfSeaPassageEvent(eventId, EventStatus.END, DEFAULT_END_TIME)
        val anchorStartEvent = createAnchorEvent(eventId, EventStatus.START, DEFAULT_START_TIME)
        val anchorEndEvent = createAnchorEvent(eventId, EventStatus.END, DEFAULT_END_TIME)
        val berthStartEvent = createUniqueBerthEvent(eventId, EventStatus.START, DEFAULT_START_TIME)
        val berthEndEvent = createUniqueBerthEvent(eventId, EventStatus.END, DEFAULT_END_TIME)
        val portStartEvent = createAreaPortEvent(eventId, EventStatus.START, DEFAULT_START_TIME)
        val portEndEvent = createAreaPortEvent(eventId, EventStatus.END, DEFAULT_END_TIME)
        val encounterStartEvent = createEncounterEvent(eventId, EventStatus.START, DEFAULT_START_TIME, EncounterType.PILOT, MMSI_2.toString(), IMO_2.toString())
        val encounterEndEvent = createEncounterEvent(eventId, EventStatus.END, DEFAULT_END_TIME, EncounterType.PILOT, MMSI_2.toString(), IMO_2.toString())
        val pilotAreaStartEvent = createPilotAreaEvent(eventId, EventStatus.START, DEFAULT_START_TIME)
        val pilotAreaEndEvent = createPilotAreaEvent(eventId, EventStatus.END, DEFAULT_END_TIME)
        val anchorAreaStartEvent = createAnchorAreaEvent(eventId, EventStatus.START, DEFAULT_START_TIME)
        val anchorAreaEndEvent = createAnchorAreaEvent(eventId, EventStatus.END, DEFAULT_END_TIME)
        val terminalMooringAreaStartEvent = createTerminalMooringAreaEvent(eventId, EventStatus.START, DEFAULT_START_TIME)
        val terminalMooringAreaEndEvent = createTerminalMooringAreaEvent(eventId, EventStatus.END, DEFAULT_END_TIME)
        val lockAreaStartEvent = createLockAreaEvent(eventId, EventStatus.START, DEFAULT_START_TIME)
        val lockAreaEndEvent = createLockAreaEvent(eventId, EventStatus.END, DEFAULT_END_TIME)
        val approachAreaStartEvent = createApproachAreaEvent(eventId, EventStatus.START, DEFAULT_START_TIME)
        val approachAreaEndEvent = createApproachAreaEvent(eventId, EventStatus.END, DEFAULT_END_TIME)

        val testLocation = Location(0.0, 0.0)
        val stopStartEvent = createStopEvent(eventId, EventStatus.START, DEFAULT_START_TIME, testLocation)
        val stopEndEvent = createStopEvent(eventId, EventStatus.END, DEFAULT_END_TIME, testLocation)

        return Stream.of(
            // Unsupported ship status
            createIgnoredEventTestArguments(voyageStatus, endOfSeaPassageEndEvent, EndOfSeaPassageEndProcessor.END_EVENT_VOYAGE_ALREADY_ONGOING_ISSUE),
            createIgnoredEventTestArguments(initialStatus, endOfSeaPassageEndEvent, EndOfSeaPassageEndProcessor.END_EVENT_NEW_SHIP_ISSUE),
            createIgnoredEventTestArguments(voyageStatus, anchorStartEvent, AnchorStartProcessor.START_EVENT_IGNORED_ISSUE),
            createIgnoredEventTestArguments(initialStatus, anchorStartEvent, AnchorStartProcessor.START_EVENT_IGNORED_ISSUE),
            createIgnoredEventTestArguments(voyageStatus, anchorEndEvent, ActivityEventProcessor.NO_PREVIOUS_VISIT_STATUS),
            createIgnoredEventTestArguments(initialStatus, anchorEndEvent, AnchorEndProcessor.END_EVENT_IGNORED_ISSUE),
            createIgnoredEventTestArguments(voyageStatus, berthStartEvent, UniqueBerthStartProcessor.START_EVENT_IGNORED_ISSUE),
            createIgnoredEventTestArguments(initialStatus, berthStartEvent, UniqueBerthStartProcessor.START_EVENT_IGNORED_ISSUE),
            createIgnoredEventTestArguments(voyageStatus, berthEndEvent, ActivityEventProcessor.NO_PREVIOUS_VISIT_STATUS),
            createIgnoredEventTestArguments(initialStatus, berthEndEvent, UniqueBerthEndProcessor.END_EVENT_IGNORED_ISSUE),
            createIgnoredEventTestArguments(voyageStatus, portStartEvent, PortStartProcessor.START_EVENT_IGNORED_ISSUE),
            createIgnoredEventTestArguments(initialStatus, portStartEvent, PortStartProcessor.START_EVENT_IGNORED_ISSUE),
            createIgnoredEventTestArguments(voyageStatus, portEndEvent, ActivityEventProcessor.NO_PREVIOUS_VISIT_STATUS),
            createIgnoredEventTestArguments(initialStatus, portEndEvent, PortEndProcessor.END_EVENT_IGNORED_ISSUE),
            createIgnoredEventTestArguments(initialStatus, encounterStartEvent, EncounterBaseProcessor.UNSUPPORTED_STATUS),
            createIgnoredEventTestArguments(initialStatus, encounterEndEvent, EncounterBaseProcessor.UNSUPPORTED_STATUS),
            createIgnoredEventTestArguments(initialStatus, stopStartEvent, StopBaseProcessor.UNSUPPORTED_STATUS),
            createIgnoredEventTestArguments(initialStatus, stopEndEvent, StopBaseProcessor.UNSUPPORTED_STATUS),
            createIgnoredEventTestArguments(voyageStatus, pilotAreaStartEvent, ActivityEventProcessor.NO_PREVIOUS_VISIT_STATUS),
            createIgnoredEventTestArguments(initialStatus, pilotAreaStartEvent, PilotAreaBaseProcessor.EVENT_IGNORED_ISSUE),
            createIgnoredEventTestArguments(voyageStatus, pilotAreaEndEvent, ActivityEventProcessor.NO_PREVIOUS_VISIT_STATUS),
            createIgnoredEventTestArguments(initialStatus, pilotAreaEndEvent, PilotAreaBaseProcessor.EVENT_IGNORED_ISSUE),
            createIgnoredEventTestArguments(voyageStatus, anchorAreaStartEvent, ActivityEventProcessor.NO_PREVIOUS_VISIT_STATUS),
            createIgnoredEventTestArguments(initialStatus, anchorAreaStartEvent, AnchorAreaBaseProcessor.EVENT_IGNORED_ISSUE),
            createIgnoredEventTestArguments(voyageStatus, anchorAreaEndEvent, ActivityEventProcessor.NO_PREVIOUS_VISIT_STATUS),
            createIgnoredEventTestArguments(initialStatus, anchorAreaEndEvent, AnchorAreaBaseProcessor.EVENT_IGNORED_ISSUE),
            createIgnoredEventTestArguments(voyageStatus, terminalMooringAreaStartEvent, ActivityEventProcessor.NO_PREVIOUS_VISIT_STATUS),
            createIgnoredEventTestArguments(initialStatus, terminalMooringAreaStartEvent, TerminalMooringAreaBaseProcessor.EVENT_IGNORED_ISSUE),
            createIgnoredEventTestArguments(voyageStatus, terminalMooringAreaEndEvent, ActivityEventProcessor.NO_PREVIOUS_VISIT_STATUS),
            createIgnoredEventTestArguments(initialStatus, terminalMooringAreaEndEvent, TerminalMooringAreaBaseProcessor.EVENT_IGNORED_ISSUE),
            createIgnoredEventTestArguments(voyageStatus, lockAreaStartEvent, ActivityEventProcessor.NO_PREVIOUS_VISIT_STATUS),
            createIgnoredEventTestArguments(initialStatus, lockAreaStartEvent, LockAreaBaseProcessor.EVENT_IGNORED_ISSUE),
            createIgnoredEventTestArguments(voyageStatus, lockAreaEndEvent, ActivityEventProcessor.NO_PREVIOUS_VISIT_STATUS),
            createIgnoredEventTestArguments(initialStatus, lockAreaEndEvent, LockAreaBaseProcessor.EVENT_IGNORED_ISSUE),
            createIgnoredEventTestArguments(voyageStatus, approachAreaStartEvent, ActivityEventProcessor.NO_PREVIOUS_VISIT_STATUS),
            createIgnoredEventTestArguments(initialStatus, approachAreaStartEvent, ApproachAreaBaseProcessor.EVENT_IGNORED_ISSUE),
            createIgnoredEventTestArguments(voyageStatus, approachAreaEndEvent, ActivityEventProcessor.NO_PREVIOUS_VISIT_STATUS),
            createIgnoredEventTestArguments(initialStatus, approachAreaEndEvent, ApproachAreaBaseProcessor.EVENT_IGNORED_ISSUE),

            // Events with a missing area id
            createIgnoredEventTestArguments(visitStatus, (anchorStartEvent as AnchoredStartEvent).copy(area = anchorStartEvent.area.copy(id = null)), ActivityEventProcessor.MISSING_AREA_ID_VALUE_ISSUE),
            createIgnoredEventTestArguments(visitStatus, (anchorEndEvent as AnchoredEndEvent).copy(area = anchorEndEvent.area.copy(id = null)), ActivityEventProcessor.MISSING_AREA_ID_VALUE_ISSUE)
        )
    }

    @ParameterizedTest
    @MethodSource("unsupportedEventTestData")
    fun `should ignore event needed for old definition`(status: NewShipStatus, event: Event) {
        val result = service.onEventAndBuffer(status, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        assertTrue(result.changes.isEmpty())
        assertTrue(result.issues.isEmpty())
        assertEquals(status, result.status)
    }

    private fun unsupportedEventTestData(): Stream<Arguments> {
        val visitStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = createNewVisit(), esof = null),
            previousVoyage = null,
            previousVisit = null
        )
        val voyageStatus = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(entry = createNewVoyage(), esof = null),
            previousVisit = null,
            previousVoyage = null
        )
        val initialStatus = NewInitialShipStatus()

        val destinationChangedEvent = createDestinationChangedEvent(DEFAULT_START_TIME, PORT_NLRTM.portId)
        val etaEvent = createEtaEvent(PORT_NLRTM, DEFAULT_START_TIME, DEFAULT_ACTUAL_TIME)
        val movementStartEvent = createMovementEvent(MovementStatus.START, DEFAULT_START_TIME)
        val movementStopEvent = createMovementEvent(MovementStatus.STOP, DEFAULT_START_TIME)
        val statusChangedEvent = createStatusChangedEvent(AisMessage.ShipStatus.MOORED, DEFAULT_START_TIME)

        return Stream.of(
            Arguments.of(visitStatus, etaEvent),
            Arguments.of(visitStatus, movementStartEvent),
            Arguments.of(visitStatus, movementStopEvent),
            Arguments.of(visitStatus, statusChangedEvent),

            Arguments.of(voyageStatus, etaEvent),
            Arguments.of(voyageStatus, movementStartEvent),
            Arguments.of(voyageStatus, movementStopEvent),
            Arguments.of(voyageStatus, statusChangedEvent),

            Arguments.of(initialStatus, destinationChangedEvent),
            Arguments.of(initialStatus, etaEvent),
            Arguments.of(initialStatus, movementStartEvent),
            Arguments.of(initialStatus, movementStopEvent),
            Arguments.of(initialStatus, statusChangedEvent)
        )
    }

    private fun createIgnoredEventTestArguments(status: NewShipStatus, event: Event, vararg issues: String): Arguments {
        val expectedIssues = issues.map { issue ->
            EventProcessingIssue(
                eventId = event._id,
                description = issue
            )
        }

        return Arguments.of(status, event, expectedIssues)
    }

    @Test
    fun `when transitioning from visit to voyage, should keep destination set`() {
        val eosEndEvent = createEndOfSeaPassageEvent(eventId, EventStatus.END, DEFAULT_END_TIME)
        val currentVisit = createNewVisit(
            eospAreaActivity = AreaActivity(
                id = eventId2,
                start = createLocationTime(),
                end = null,
                areaId = eosEndEvent.area.id ?: ""
            ),
            stops = listOf(createNewStop(type = NewStopType.BERTH)),
            destination = testDestination1
        )

        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(currentVisit, null),
            previousVoyage = null,
            previousVisit = null
        )

        val result = service.onEventAndBuffer(currentStatus, eosEndEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields
        val status = result.status as? NewVoyageShipStatus ?: fail("should be in voyage status")
        assertEquals(testDestination1, status.voyage.entry.destination, "should keep destination set")
    }

    @Test
    fun `when transitioning from voyage to visit, should keep destination set`() {
        val eosStartEvent = createEndOfSeaPassageEvent(eventId, EventStatus.START, DEFAULT_END_TIME)
        val currentVoyage = createNewVoyage(destination = testDestination1)
        val currentStatus = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(currentVoyage, null),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, eosStartEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields
        val status = result.status as? NewVisitShipStatus ?: fail("should be in visit status")
        assertEquals(testDestination1, status.visit.entry.destination, "should keep destination set")
    }

    @Test
    fun `when resuming voyage, should update voyage based on updated destination from visit`() {
        val previousVoyage = createNewVoyage(
            start = createLocationTime(time = DEFAULT_START_TIME.toInstant()),
            end = createLocationTime(time = DEFAULT_END_TIME.toInstant()),
            destination = testDestination1
        )
        val eosEndEvent = createEndOfSeaPassageEvent(eventId, EventStatus.END, DEFAULT_END_TIME)
        val currentVisit = createNewVisit(
            eospAreaActivity = AreaActivity(
                id = eventId2,
                start = createLocationTime(time = DEFAULT_END_TIME.toInstant()),
                end = null,
                areaId = eosEndEvent.area.id ?: ""
            ),
            destination = testDestination2
        )
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(currentVisit, null),
            previousVoyage = EntryESoFWrapper(previousVoyage, null),
            previousVisit = null,
        )

        val result = service.onEventAndBuffer(currentStatus, eosEndEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields
        val status = result.status as? NewVoyageShipStatus ?: fail("should be in voyage status")
        assertEquals(testDestination2, status.voyage.entry.destination, "should get destination of cancelled visit")
    }

    @Test
    fun `when receiving new destination, should be applied to visit`() {
        val currentVisit = createNewVisit()
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(currentVisit, null),
            previousVoyage = null,
            previousVisit = null
        )

        val result = service.onEventAndBuffer(currentStatus, testDestinationChangedEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields
        val status = result.status as? NewVisitShipStatus ?: fail("should be in visit status")
        assertEquals(testDestination1, status.visit.entry.destination, "should create destination")
    }

    @Test
    fun `when receiving updated destination, should be applied to visit`() {
        val currentVisit = createNewVisit(destination = testDestination2)
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(currentVisit, null),
            previousVoyage = null,
            previousVisit = null
        )

        val result = service.onEventAndBuffer(currentStatus, testDestinationChangedEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields
        val status = result.status as? NewVisitShipStatus ?: fail("should be in visit status")
        assertEquals(testDestination1, status.visit.entry.destination, "should update destination")
    }

    @Test
    fun `when receiving new destination, should be applied to voyage`() {
        val currentVoyage = createNewVoyage()
        val currentStatus = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(currentVoyage, null),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, testDestinationChangedEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields
        val status = result.status as? NewVoyageShipStatus ?: fail("should be in voyage status")
        assertEquals(testDestination1, status.voyage.entry.destination, "should create destination")
    }

    @Test
    fun `when receiving updated destination, should be applied to voyage`() {
        val currentVoyage = createNewVoyage(destination = testDestination2)
        val currentStatus = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(currentVoyage, null),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, testDestinationChangedEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields
        val status = result.status as? NewVoyageShipStatus ?: fail("should be in voyage status")
        assertEquals(testDestination1, status.voyage.entry.destination, "should update destination")
    }

    @Test
    fun `should process shipToShip encounter start event and update esof of ongoing visit`() {
        val shipToShipTransferEvent = createShipToShipTransferEvent(
            eventId = eventId,
            status = EventStatus.START,
            time = DEFAULT_START_TIME,
            otherMmsi = MMSI_2.toString(),
            otherImo = IMO_2.toString(),
            areaId = "area51"
        )

        val currentVisit = createNewVisit()
        val currentEsof = createNewESoF(_id = currentVisit._id)
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(currentVisit, currentEsof),
            previousVoyage = null,
            previousVisit = null
        )

        val result = service.onEventAndBuffer(currentStatus, shipToShipTransferEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedTransfer = ShipToShipTransfer(
            otherMmsi = MMSI_2.toInt(),
            otherImo = IMO_2.toInt(),
            startEventId = eventId,
            start = LocationTime(
                location = PORT_NLRTM.location.toSkeletonLocation(),
                time = DEFAULT_START_TIME.toInstant()
            ),
            end = null,
            areaId = "area51"
        )
        val expectedEsof = currentEsof.copy(
            shipToShipTransfers = listOf(expectedTransfer)
        )
        val expectedStatus = currentStatus.copy(
            visit = EntryESoFWrapper(currentVisit, expectedEsof)
        )

        val expected = NewEventProcessingResult(
            status = expectedStatus,
            changes = listOf(
                ESoFChange(Action.UPDATE, expectedEsof)
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process shipToShip encounter start event and create new esof when non existing`() {
        val shipToShipTransferEvent = createShipToShipTransferEvent(
            eventId = eventId,
            status = EventStatus.START,
            time = DEFAULT_START_TIME,
            otherMmsi = MMSI_2.toString(),
            otherImo = IMO_2.toString(),
            areaId = "area51"
        )

        val currentVisit = createNewVisit()
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(currentVisit, null),
            previousVoyage = null,
            previousVisit = null
        )

        val result = service.onEventAndBuffer(currentStatus, shipToShipTransferEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedTransfer = ShipToShipTransfer(
            otherMmsi = MMSI_2.toInt(),
            otherImo = IMO_2.toInt(),
            startEventId = eventId,
            start = LocationTime(
                location = PORT_NLRTM.location.toSkeletonLocation(),
                time = DEFAULT_START_TIME.toInstant()
            ),
            end = null,
            areaId = "area51"
        )
        val expectedEsof = createNewESoF(
            _id = currentVisit._id,
            shipToShipTransfers = listOf(expectedTransfer)
        )
        val expectedStatus = currentStatus.copy(
            visit = EntryESoFWrapper(currentVisit, expectedEsof)
        )

        val expected = NewEventProcessingResult(
            status = expectedStatus,
            changes = listOf(
                ESoFChange(Action.CREATE, expectedEsof)
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should ignore shipToShip encounter start event when already having an ongoing encounter`() {
        val shipToShipTransferEvent = createShipToShipTransferEvent(
            eventId = eventId,
            status = EventStatus.START,
            time = DEFAULT_START_TIME.plusDays(2),
            otherMmsi = MMSI_2.toString(),
            otherImo = IMO_2.toString(),
            areaId = "area51"
        )

        val currentVisit = createNewVisit()
        val currentTransfer = ShipToShipTransfer(
            otherMmsi = MMSI_2.toInt(),
            otherImo = IMO_2.toInt(),
            startEventId = eventId,
            start = LocationTime(
                location = PORT_NLRTM.location.toSkeletonLocation(),
                time = DEFAULT_START_TIME.toInstant()
            ),
            end = null,
            areaId = "area51"
        )
        val currentEsof = createNewESoF(
            _id = currentVisit._id,
            shipToShipTransfers = listOf(currentTransfer)
        )
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(currentVisit, currentEsof),
            previousVoyage = null,
            previousVisit = null
        )

        val result = service.onEventAndBuffer(currentStatus, shipToShipTransferEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expected = NewEventProcessingResult(
            status = currentStatus,
            changes = emptyList(),
            issues = listOf(
                EventProcessingIssue(
                    eventId = eventId,
                    description = "Cannot process ShipToShipTransfer event, same encounter already ongoing (other " +
                        "mmsi: $MMSI_2)"
                )
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should ignore shipToShip encounter start event during voyage`() {
        val shipToShipTransferEvent = createShipToShipTransferEvent(
            eventId = eventId,
            status = EventStatus.START,
            time = DEFAULT_START_TIME,
            otherMmsi = MMSI_2.toString(),
            otherImo = IMO_2.toString(),
            areaId = "area51"
        )

        val currentVoyage = createNewVoyage()
        val currentEsof = createNewESoF(_id = currentVoyage._id)
        val currentStatus = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(currentVoyage, currentEsof),
            previousVisit = null,
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, shipToShipTransferEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedEsof = currentEsof.copy(
            shipToShipTransfers = emptyList()
        )
        val expectedStatus = currentStatus.copy(
            voyage = EntryESoFWrapper(currentVoyage, expectedEsof)
        )

        val expected = NewEventProcessingResult(
            status = expectedStatus,
            changes = emptyList(),
            issues = listOf(
                EventProcessingIssue(
                    eventId = eventId,
                    description = "ShipToShipTransfer events are only allowed when in Visit status"
                )
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process shipToShip encounter end event and update esof of ongoing visit`() {
        val startEventTestId = "TEST_START_EVENT_ID"
        val shipToShipTransferEvent = createShipToShipTransferEvent(
            eventId = eventId,
            status = EventStatus.END,
            time = DEFAULT_END_TIME,
            otherMmsi = MMSI_2.toString(),
            otherImo = IMO_2.toString(),
            areaId = "area51",
            relatedEvent = startEventTestId,
        )

        val currentVisit = createNewVisit()
        val currentTransfer = ShipToShipTransfer(
            otherMmsi = MMSI_2.toInt(),
            otherImo = IMO_2.toInt(),
            startEventId = startEventTestId,
            start = LocationTime(
                location = PORT_NLRTM.location.toSkeletonLocation(),
                time = DEFAULT_START_TIME.toInstant()
            ),
            end = null,
            areaId = "area51"
        )
        val currentEsof = createNewESoF(
            _id = currentVisit._id,
            shipToShipTransfers = listOf(currentTransfer)
        )
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(currentVisit, currentEsof),
            previousVoyage = null,
            previousVisit = null
        )

        val result = service.onEventAndBuffer(currentStatus, shipToShipTransferEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedTransfer = currentTransfer.copy(
            end = LocationTime(
                location = PORT_NLRTM.location.toSkeletonLocation(),
                time = DEFAULT_END_TIME.toInstant()
            )
        )
        val expectedEsof = currentEsof.copy(
            shipToShipTransfers = listOf(expectedTransfer)
        )
        val expectedStatus = currentStatus.copy(
            visit = EntryESoFWrapper(currentVisit, expectedEsof)
        )

        val expected = NewEventProcessingResult(
            status = expectedStatus,
            changes = listOf(
                ESoFChange(Action.UPDATE, expectedEsof)
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should process shipToShip encounter end event and update esof of previous visit on ongoing voyage`() {
        val startEventTestId = "TEST_START_EVENT_ID"
        val shipToShipTransferEvent = createShipToShipTransferEvent(
            eventId = eventId,
            relatedEvent = startEventTestId,
            status = EventStatus.END,
            time = DEFAULT_END_TIME,
            otherMmsi = MMSI_2.toString(),
            otherImo = IMO_2.toString(),
            areaId = "area51"
        )

        val previousVisit = createNewVisit()
        val currentVoyage = createNewVoyage(previous = previousVisit._id)
        val fallbackFinishedTransfer = ShipToShipTransfer(
            otherMmsi = MMSI_2.toInt(),
            otherImo = IMO_2.toInt(),
            startEventId = startEventTestId,
            start = LocationTime(
                location = PORT_NLRTM.location.toSkeletonLocation(),
                time = DEFAULT_START_TIME.toInstant()
            ),
            end = createLocationTime(fallback = FallbackType.ACTIVITY_END_BY_EOSP),
            areaId = "area51"
        )
        val currentEsof = createNewESoF(
            _id = previousVisit._id,
            shipToShipTransfers = listOf(fallbackFinishedTransfer)
        )
        val currentStatus = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(currentVoyage, null),
            previousVisit = EntryESoFWrapper(previousVisit, currentEsof),
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, shipToShipTransferEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedTransfer = fallbackFinishedTransfer.copy(
            end = LocationTime(
                location = PORT_NLRTM.location.toSkeletonLocation(),
                time = DEFAULT_END_TIME.toInstant()
            )
        )
        val expectedEsof = currentEsof.copy(
            shipToShipTransfers = listOf(expectedTransfer)
        )
        val expectedStatus = currentStatus.copy(
            previousVisit = EntryESoFWrapper(previousVisit, expectedEsof)
        )

        val expected = NewEventProcessingResult(
            status = expectedStatus,
            changes = listOf(
                ESoFChange(Action.UPDATE, expectedEsof)
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should ignore shipToShip encounter end event when having no fallbacked on the previous visit on a ongoing voyage`() {
        val startEventTestId = "TEST_START_EVENT_ID"
        val shipToShipTransferEvent = createShipToShipTransferEvent(
            eventId = eventId,
            status = EventStatus.END,
            time = DEFAULT_END_TIME,
            otherMmsi = MMSI_2.toString(),
            otherImo = IMO_2.toString(),
            areaId = "area51",
            relatedEvent = startEventTestId,
        )

        val previousVisit = createNewVisit()
        val currentVoyage = createNewVoyage(previous = previousVisit._id)
        val finishedTransfer = ShipToShipTransfer(
            otherMmsi = MMSI_2.toInt(),
            otherImo = IMO_2.toInt(),
            startEventId = startEventTestId,
            start = createLocationTime(),
            end = createLocationTime(),
            areaId = "area51"
        )
        val currentEsof = createNewESoF(
            _id = previousVisit._id,
            shipToShipTransfers = listOf(finishedTransfer)
        )
        val currentStatus = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(currentVoyage, null),
            previousVisit = EntryESoFWrapper(previousVisit, currentEsof),
            previousVoyage = null
        )

        val result = service.onEventAndBuffer(currentStatus, shipToShipTransferEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expected = NewEventProcessingResult(
            status = currentStatus,
            changes = emptyList(),
            issues = listOf(
                EventProcessingIssue(
                    eventId = eventId,
                    description = "No matching ship-to-ship transfer found. Either the start event was never " +
                        "recorded on the previous visit or the transfer.end was not set by a fallback mechanism"
                )
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should ignore shipToShip encounter end event when having no esof on visit`() {
        val pilotEvent = createShipToShipTransferEvent(
            eventId = eventId,
            status = EventStatus.END,
            time = DEFAULT_START_TIME.plusDays(2),
            otherMmsi = MMSI_2.toString(),
            otherImo = IMO_2.toString(),
            areaId = "area51"
        )

        val currentVisit = createNewVisit()
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(currentVisit, null),
            previousVoyage = null,
            previousVisit = null
        )

        val result = service.onEventAndBuffer(currentStatus, pilotEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expected = NewEventProcessingResult(
            status = currentStatus,
            changes = emptyList(),
            issues = listOf(
                EventProcessingIssue(
                    eventId = eventId,
                    description = "Cannot process ShipToShipTransfer event, we never received a start"
                )
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `should ignore shipToShip encounter end event when having other encounter ongoing on visit`() {
        val shipToShipTransferEvent = createShipToShipTransferEvent(
            eventId = eventId,
            status = EventStatus.END,
            time = DEFAULT_START_TIME.plusDays(2),
            otherMmsi = MMSI_2.toString(),
            otherImo = IMO_2.toString(),
            areaId = "area51"
        )

        val otherOngoingTransfer = ShipToShipTransfer(
            otherMmsi = MMSI_3.toInt(),
            otherImo = IMO_3.toInt(),
            startEventId = "TEST_OTHER_SHIP_TRANSFER_ID",
            start = LocationTime(
                location = PORT_NLRTM.location.toSkeletonLocation(),
                time = DEFAULT_START_TIME.toInstant()
            ),
            end = null,
            areaId = "area51"
        )
        val currentVisit = createNewVisit()
        val currentEsof = createNewESoF(
            _id = currentVisit._id,
            shipToShipTransfers = listOf(otherOngoingTransfer)
        )
        val currentStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(currentVisit, currentEsof),
            previousVoyage = null,
            previousVisit = null
        )

        val result = service.onEventAndBuffer(currentStatus, shipToShipTransferEvent, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expected = NewEventProcessingResult(
            status = currentStatus,
            changes = emptyList(),
            issues = listOf(
                EventProcessingIssue(
                    eventId = eventId,
                    description = "Cannot process ShipToShipTransfer event, we never received a start"
                )
            )
        )

        assertEquals(expected, result)
    }

    @Test
    fun `mark other ongoing main port eosp from previous visit as passthrough when in voyage`() {
        val visitId = "TEST_VISIT"
        val voyageId = "TEST_VOYAGE"
        val timeline = Timeline(start = DEFAULT_START_TIME.toInstant())
        val (visitStart, ongoingEospStart, visitEndVoyageStart) = timeline.generateN(3)
        val mainPortEosp = createAreaActivity(start = visitStart, end = visitEndVoyageStart, areaId = PORT_BEANR.portId)
        val otherOngoingEosp = createAreaActivity(start = ongoingEospStart, end = null, areaId = PORT_NLRTM.portId)
        val previousVisit = createNewVisit(
            _id = "visit1",
            start = visitStart,
            end = visitEndVoyageStart,
            eospAreaActivity = mainPortEosp,
            otherOngoingEospAreaActivities = listOf(otherOngoingEosp)
        )
        val ongoingVoyage = createNewVoyage(
            _id = voyageId,
            previous = visitId,
            start = visitEndVoyageStart
        )
        val status = createNewVoyageShipStatus(
            voyage = ongoingVoyage,
            previousVisit = previousVisit
        )
        val event = createEndOfSeaPassageEvent(
            status = EventStatus.END,
            areaId = PORT_NLRTM.portId,
            time = toZonedDateTime(visitEndVoyageStart.time), // hmm, tricky
            _id = generateUniqueId()
        )
        val result = service.onEventAndBuffer(status, event, processingTestTime)
            .copy(decision = null, origin = null) // ignore debug fields

        val expectedPassthrough = otherOngoingEosp.copy(end = LocationTime(event.location, visitEndVoyageStart.time))

        // other ongoing main port eosp should have been removed and added to passthroughEosp list
        val resultPreviousVisit = (result.status as? NewVoyageShipStatus)?.previousVisit?.entry
            ?: fail("Expected voyage status with previous visit entry")

        assertEquals(
            emptyList<AreaActivity>(),
            resultPreviousVisit.otherOngoingEospAreaActivities,
            "expected to move eosp to passthrough"
        )
        assertEquals(
            listOf(expectedPassthrough),
            resultPreviousVisit.passThroughEosp,
            "expected eosp in this list"
        )
    }

    @Test
    fun `expect an event with a processing issue to be removed from eventBuffer when next event has a later eventTime`() {
        val om = globalObjectMapper
        // nlrtm
        val previousVisit = om.readValue<NewVisit>(loadResource("events/eventbuffer-removal-previousvisit.json"))
        // nlrtm -> gbimm
        val voyage = om.readValue<NewVoyage>(loadResource("events/eventbuffer-removal-voyage.json"))

        val events = om.readValue<List<Event>>(loadResource("events/eventbuffer-removal-events.json"))
        // pilot just east of gbimm eosp
        val pilotEvent = events.find { it is AreaEvent && it.area.type == AreaIdentifier.AreaType.PILOT_BOARDING_PLACE } ?: fail("Expected pilot area event")
        // gbimm eosp start
        val eospEvent = events.find { it is AreaEvent && it.area.type == AreaIdentifier.AreaType.END_OF_SEA_PASSAGE } ?: fail("Expected EOSP start event")

        val status = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(entry = voyage, esof = null),
            previousVisit = EntryESoFWrapper(entry = previousVisit, esof = null),
            previousVoyage = null
        )

        // pilot event causes an issue, as it is expected inside a visit, but happens during voyage
        // the event is placed in the eventBuffer, to process again on the next event, but that will not happen,
        // as the eosp start does not have the same eventTime
        val statusAfterPilot = service.onEventAndBuffer(status, pilotEvent, Instant.now())
        val expectedIssue = EventProcessingIssue(
            eventId = pilotEvent._id,
            description = ActivityEventProcessor.NO_END_EVENT_ON_PREVIOUS_VISIT
        )
        assertEquals(listOf(expectedIssue), statusAfterPilot.issues)
        assertEquals(listOf(pilotEvent), statusAfterPilot.status.eventBuffer)

        // the pilot event issue should be removed from the ship status, as it is older than the EOSP start
        val statusAfterEospStart = service.onEventAndBuffer(statusAfterPilot.status, eospEvent, Instant.now())
        assertTrue(
            statusAfterEospStart.issues.isEmpty(),
            "expected pilot event to be removed from issue list, as pilotEvent.eventTime != eospStart.eventTime"
        )
        assertTrue(
            statusAfterEospStart.status.eventBuffer.isEmpty(),
            "expected pilot event to be removed from eventBuffer, as pilotEvent.eventTime != eospStart.eventTime"
        )
        assertTrue(
            statusAfterEospStart.changes.any { change ->
                change is VoyageChange && change.action == Action.UPDATE && change.value.end != null
            },
            "expected a finished voyage change"
        )
        assertTrue(
            statusAfterEospStart.changes.any { change ->
                change is VisitChange && change.action == Action.CREATE && change.value.end == null
            },
            "expected a started visit change"
        )
    }

    private fun <T> repeatCreate(times: Int, create: () -> T): List<T> {
        val result = mutableListOf<T>()
        repeat(times) {
            val created = create()
            result.add(created)
        }

        return result
    }
}
