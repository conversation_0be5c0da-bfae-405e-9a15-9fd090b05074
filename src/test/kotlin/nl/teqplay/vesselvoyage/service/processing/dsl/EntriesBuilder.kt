package nl.teqplay.vesselvoyage.service.processing.dsl

import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType
import nl.teqplay.skeleton.model.Location
import nl.teqplay.vesselvoyage.model.v2.AreaActivity
import nl.teqplay.vesselvoyage.model.v2.Destination
import nl.teqplay.vesselvoyage.model.v2.EntryESoFWrapper
import nl.teqplay.vesselvoyage.model.v2.FallbackType
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewESoF
import nl.teqplay.vesselvoyage.model.v2.NewEncounter
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewStop
import nl.teqplay.vesselvoyage.model.v2.NewStopType
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import java.time.Instant

/**
 * Used to create [EntryESoFWrapper] for [NewVisit] and [NewVoyage].
 *
 * Example:
 *
 * ```
 * EntriesBuilder()
 *     .shipImo(1234567)
 *     .visit {
 *         starting(atLocation lat 2.0 lon 2.0 atTime "ISO_TIMESTAMP")
 *         withId("aaaaaaaaaaaa.VISIT")
 *         port {
 *             withId("eventId")
 *             atArea("port-id")
 *             starting(atLocation lat 0.0 lon 0.0 atTime "ISO_TIMESTAMP")
 *             ending(atLocation lat 0.0 lon 0.0 atTime "ISO_TIMESTAMP")
 *         }
 *     }
 *     .voyage {
 *         withId("aaaaaaaaaaaa.VOYAGE")
 *         starting(atLocation lat 1.0 lon 1.0 atTime "ISO_TIMESTAMP")
 *     }
 *     .build()
 * ```
 */
class EntriesBuilder {
    private var imo: Int? = null
    private val builders = mutableListOf<EntryBuilder<*>>()

    fun shipImo(input: Int) = apply { imo = input }

    fun visit(init: VisitBuilder.() -> Unit) = apply {
        builders += VisitBuilder(init)
    }

    fun voyage(init: VoyageBuilder.() -> Unit) = apply {
        builders += VoyageBuilder(init)
    }

    fun build(): List<EntryESoFWrapper<*>> {
        var previousEntryId: String? = null
        var previousVisitPort: String? = null
        var nextStart: LocationTime? = null
        var nextEntryId: String? = null
        var nextVisitPort: String? = null
        val builderEntries = builders.map { entryBuilder -> entryBuilder.build(imo = imo!!) }

        // Prepare all visits and voyages to be chained
        return builderEntries.map { builderEntry ->
            val (entry, esof) = builderEntry

            val result = when (entry) {
                is NewVisit -> {
                    EntryESoFWrapper(
                        entry = entry.copy(
                            previous = previousEntryId
                        ),
                        esof = esof
                    )
                }
                is NewVoyage -> {
                    EntryESoFWrapper(
                        entry = entry.copy(
                            previous = previousEntryId,
                            originPort = previousVisitPort
                        ),
                        esof = esof
                    )
                }
                else -> throw Exception("Unsupported, no Visit or Voyage")
            }

            previousEntryId = entry._id
            (entry as? NewVisit)?.let { previousVisitPort = entry.eospAreaActivity.areaId }

            result
        }.reversed().map { builderEntry ->
            val (entry, esof) = builderEntry

            val result = when (entry) {
                is NewVisit -> {
                    val actualEosp = entry.eospAreaActivity.copy(end = nextStart)
                    EntryESoFWrapper(
                        entry = entry.copy(
                            next = nextEntryId,
                            end = nextStart,
                            eospAreaActivity = actualEosp
                        ),
                        esof = esof
                    )
                }
                is NewVoyage -> {
                    EntryESoFWrapper(
                        entry = entry.copy(
                            next = nextEntryId,
                            destinationPort = nextVisitPort,
                            end = nextStart
                        ),
                        esof = esof
                    )
                }
                else -> throw Exception("Unsupported, no Visit or Voyage")
            }

            nextEntryId = entry._id
            nextStart = entry.start
            (entry as? NewVisit)?.let { nextVisitPort = entry.eospAreaActivity.areaId }

            result
        }.reversed()
    }
}

val atLocation: CoordStart
    get() = CoordStart()

class CoordStart {
    private fun coord(lat: Double, lon: Double): Location {
        return Location(lat = lat, lon = lon)
    }
    infix fun lat(lat: Double) = CoordLat(lat)

    inner class CoordLat(private val lat: Double) {
        infix fun lon(lon: Double): Location {
            return coord(lat = lat, lon = lon)
        }
    }
    infix fun defaultWithTime(time: String): LocationTime {
        return LocationTime(
            // We don't use the actual coordinates here, but a default one
            location = coord(lat = 0.0, lon = 0.0),
            time = Instant.parse(time)
        )
    }
}

fun List<AreaActivityBuilder>.build(): MutableList<AreaActivity> {
    return this.map { it.build() }.toMutableList()
}

interface EntryBuilder<T : NewEntry> : ActivityBuilder {
    fun build(imo: Int): EntryESoFWrapper<T>
}

interface ActivityBuilder {
    infix fun Location.atTime(time: String): LocationTime {
        return LocationTime(
            location = this,
            time = Instant.parse(time)
        )
    }

    infix fun LocationTime.withFallback(fallback: FallbackType): LocationTime {
        return this.copy(fallback = fallback)
    }
}

class ESoFBuilder(private val init: ESoFBuilder.() -> Unit) {
    private val encounters = mutableListOf<EncounterBuilder>()

    fun encounter(encounterType: EncounterType, init: EncounterBuilder.() -> Unit) = apply { encounters += EncounterBuilder(encounterType, init) }

    fun build(entryId: String): NewESoF {
        init()
        return NewESoF(
            _id = entryId,
            encounters = encounters.map { it.build() },
            slowMovingPeriods = emptyList(),
            shipToShipTransfers = emptyList()
        )
    }
}

class EncounterBuilder(private val type: EncounterType, private val init: EncounterBuilder.() -> Unit) : ActivityBuilder {
    private var serviceVessel: EncounterServiceVesselBuilder? = null
    private var startEventId: String? = null
    private var start: LocationTime? = null
    private var end: LocationTime? = null

    fun startEventId(input: String) = apply { startEventId = input }
    fun starting(locationTime: LocationTime) = apply { start = locationTime }
    fun ending(locationTime: LocationTime) = apply { end = locationTime }
    fun serviceVessel(init: EncounterServiceVesselBuilder.() -> Unit) = apply { serviceVessel = EncounterServiceVesselBuilder(init) }

    fun build(): NewEncounter {
        init()
        val (mmsi, imo) = serviceVessel!!.build()
        return NewEncounter(
            type = type,
            otherMmsi = mmsi,
            otherImo = imo,
            startEventId = startEventId!!,
            start = start!!,
            end = end
        )
    }

    inner class EncounterServiceVesselBuilder(private val init: EncounterServiceVesselBuilder.() -> Unit) {
        private var mmsi: Int? = null
        private var imo: Int? = null

        fun withMmsi(input: Int) = apply { mmsi = input }
        fun withImo(input: Int) = apply { imo = input }

        fun build(): Pair<Int, Int?> {
            init()
            return mmsi!! to imo
        }
    }
}

class StopBuilder(private val stopType: NewStopType, private val init: StopBuilder.() -> Unit) : ActivityBuilder {
    private var startEventId: String? = null
    private var endEventId: String? = null
    private var location: Location? = null
    private var start: LocationTime? = null
    private var end: LocationTime? = null
    private var areaId: String? = null
    private var accuracy: Float? = null

    fun startEventId(input: String) = apply { startEventId = input }
    fun endEventId(input: String) = apply { endEventId = input }
    fun location(input: Location) = apply { location = input }
    fun atArea(input: String) = apply { areaId = input }
    fun accuracy(input: Float) = apply { accuracy = input }
    fun starting(locationTime: LocationTime) = apply { start = locationTime }
    fun ending(locationTime: LocationTime) = apply { end = locationTime }

    fun build(): NewStop {
        init()
        return NewStop(
            startEventId = startEventId!!,
            endEventId = endEventId,
            location = location,
            start = start!!,
            end = end,
            type = stopType,
            areaId = areaId,
            accuracy = accuracy
        )
    }
}

class DestinationBuilder(private val init: DestinationBuilder.() -> Unit) {
    private var time: Instant? = null
    private var ais: String? = null
    private var actual: String? = null

    fun atTime(input: String) = apply { time = Instant.parse(input) }
    fun ais(input: String) = apply { ais = input }
    fun actual(input: String) = apply { actual = input }

    fun build(): Destination {
        init()
        return Destination(
            time = time!!,
            ais = ais!!,
            actual = actual
        )
    }
}

class AreaActivityBuilder(private val init: AreaActivityBuilder.() -> Unit) : ActivityBuilder {
    private var id: String? = null
    private var start: LocationTime? = null
    private var end: LocationTime? = null
    private var areaId: String? = null

    fun withId(input: String) = apply { id = input }
    fun starting(locationTime: LocationTime) = apply { start = locationTime }
    fun ending(locationTime: LocationTime) = apply { end = locationTime }
    fun atArea(input: String) = apply { areaId = input }

    fun build(): AreaActivity {
        init()
        return AreaActivity(
            id = id!!,
            start = start!!,
            end = end,
            areaId = areaId!!
        )
    }
}

class VisitBuilder(private val init: VisitBuilder.() -> Unit) : EntryBuilder<NewVisit> {
    private var id: String? = null
    private var start: LocationTime? = null
    private val passThroughEosp = mutableListOf<AreaActivityBuilder>()
    private val passThroughPort = mutableListOf<AreaActivityBuilder>()
    private var eosp: AreaActivityBuilder? = null
    private val otherOngoing = mutableListOf<AreaActivityBuilder>()
    private val port = mutableListOf<AreaActivityBuilder>()
    private val anchor = mutableListOf<AreaActivityBuilder>()
    private val berth = mutableListOf<AreaActivityBuilder>()
    private val pilot = mutableListOf<AreaActivityBuilder>()
    private val anchorArea = mutableListOf<AreaActivityBuilder>()
    private val terminalMooring = mutableListOf<AreaActivityBuilder>()
    private val lock = mutableListOf<AreaActivityBuilder>()
    private val approach = mutableListOf<AreaActivityBuilder>()
    private val stops = mutableListOf<StopBuilder>()
    private var confirmed: Boolean? = null
    private var esof: ESoFBuilder? = null
    private var destination: Destination? = null

    fun starting(input: LocationTime, initEosp: AreaActivityBuilder.() -> Unit) = apply {
        start = input
        eosp = AreaActivityBuilder(initEosp)
            .starting(input)
    }

    fun withId(input: String) = apply { id = input }
    fun passThroughEosp(init: AreaActivityBuilder.() -> Unit) = apply { passThroughEosp += AreaActivityBuilder(init) }
    fun passThroughPort(init: AreaActivityBuilder.() -> Unit) = apply { passThroughPort += AreaActivityBuilder(init) }
    fun withDestination(init: DestinationBuilder.() -> Unit) = apply { destination = DestinationBuilder(init).build() }
    fun isConfirmed() = apply { confirmed = true }
    fun esof(init: ESoFBuilder.() -> Unit) = apply { esof = ESoFBuilder(init) }
    fun otherOngoing(init: AreaActivityBuilder.() -> Unit) = apply { otherOngoing += AreaActivityBuilder(init) }
    fun port(init: AreaActivityBuilder.() -> Unit) = apply { port += AreaActivityBuilder(init) }
    fun anchor(init: AreaActivityBuilder.() -> Unit) = apply { anchor += AreaActivityBuilder(init) }
    fun berth(init: AreaActivityBuilder.() -> Unit) = apply { berth += AreaActivityBuilder(init) }
    fun pilot(init: AreaActivityBuilder.() -> Unit) = apply { pilot += AreaActivityBuilder(init) }
    fun anchorArea(init: AreaActivityBuilder.() -> Unit) = apply { anchorArea += AreaActivityBuilder(init) }
    fun terminalMooring(init: AreaActivityBuilder.() -> Unit) = apply { terminalMooring += AreaActivityBuilder(init) }
    fun lock(init: AreaActivityBuilder.() -> Unit) = apply { lock += AreaActivityBuilder(init) }
    fun approach(init: AreaActivityBuilder.() -> Unit) = apply { approach += AreaActivityBuilder(init) }
    fun stop(stopType: NewStopType, init: StopBuilder.() -> Unit) = apply { stops += StopBuilder(stopType, init) }

    override fun build(imo: Int): EntryESoFWrapper<NewVisit> {
        init()
        val eosp = eosp!!.build()
        val visitId = id!!
        val visit = NewVisit(
            _id = visitId,
            imo = imo,
            start = start!!,
            eospAreaActivity = eosp,
            otherOngoingEospAreaActivities = otherOngoing.build(),
            portAreaActivities = port.build(),
            anchorAreaActivities = anchor.build(),
            pilotAreaActivities = pilot.build(),
            anchorAreaAreaActivities = anchorArea.build(),
            terminalMooringAreaActivities = terminalMooring.build(),
            berthAreaActivities = berth.build(),
            lockAreaActivities = lock.build(),
            approachAreaActivities = approach.build(),
            confirmed = confirmed ?: false,
            destination = destination,
            stops = stops.map { it.build() },
            passThroughEosp = passThroughEosp.build(),
            passThroughPort = passThroughPort.build(),

            // Resolved by the builder
            previous = null,
            next = null,
            end = null
        )
        val esof = esof?.build(visitId)
        return EntryESoFWrapper(
            entry = visit,
            esof = esof
        )
    }
}

class VoyageBuilder(private val init: VoyageBuilder.() -> Unit) : EntryBuilder<NewVoyage> {
    private var id: String? = null
    private var start: LocationTime? = null
    private var actualStart: LocationTime? = null
    private val passThroughEosp = mutableListOf<AreaActivityBuilder>()
    private val passThroughPort = mutableListOf<AreaActivityBuilder>()
    private val stops = mutableListOf<StopBuilder>()
    private var esof: ESoFBuilder? = null
    private var destination: Destination? = null

    fun starting(input: LocationTime) = apply { start = input }
    fun withId(input: String) = apply { id = input }
    fun passThroughEosp(init: AreaActivityBuilder.() -> Unit) = apply { passThroughEosp += AreaActivityBuilder(init) }
    fun passThroughPort(init: AreaActivityBuilder.() -> Unit) = apply { passThroughPort += AreaActivityBuilder(init) }
    fun withDestination(init: DestinationBuilder.() -> Unit) = apply { destination = DestinationBuilder(init).build() }
    fun stop(stopType: NewStopType, init: StopBuilder.() -> Unit) = apply { stops += StopBuilder(stopType, init) }
    fun esof(init: ESoFBuilder.() -> Unit) = apply { esof = ESoFBuilder(init) }

    override fun build(imo: Int): EntryESoFWrapper<NewVoyage> {
        init()
        val voyageId = id!!
        val voyage = NewVoyage(
            _id = voyageId,
            imo = imo,
            start = start!!,
            actualStart = actualStart ?: start,
            destination = destination,
            stops = stops.map { it.build() },
            passThroughEosp = passThroughEosp.build(),
            passThroughPort = passThroughPort.build(),

            // Resolved by the builder
            previous = null,
            next = null,
            end = null,
            originPort = null,
            destinationPort = null
        )
        val esof = esof?.build(voyageId)
        return EntryESoFWrapper(
            entry = voyage,
            esof = esof
        )
    }
}
