package nl.teqplay.vesselvoyage.service.processing

import com.fasterxml.jackson.module.kotlin.readValue
import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.eq
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.reset
import com.nhaarman.mockitokotlin2.whenever
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType
import nl.teqplay.skeleton.model.Location
import nl.teqplay.vesselvoyage.logic.createAnchorage
import nl.teqplay.vesselvoyage.logic.createBerth
import nl.teqplay.vesselvoyage.logic.createPort
import nl.teqplay.vesselvoyage.model.internal.ClassifiedStop
import nl.teqplay.vesselvoyage.model.internal.InfraAreaType
import nl.teqplay.vesselvoyage.model.v2.EntryESoFWrapper
import nl.teqplay.vesselvoyage.model.v2.NewStopType
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import nl.teqplay.vesselvoyage.service.EntryProcessingService
import nl.teqplay.vesselvoyage.service.processing.dsl.EntriesBuilder
import nl.teqplay.vesselvoyage.service.processing.dsl.atLocation
import nl.teqplay.vesselvoyage.util.globalObjectMapper
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Assertions.fail
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import nl.teqplay.aisengine.event.interfaces.Event as AisEngineEvent

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class EventProcessingScenarioTest : BaseEventProcessingTest() {
    private val entryProcessingService = EntryProcessingService(
        entryService = mock(),
        eventProcessingProperties = config,
        traceProperties = mock(),
        eventFetchingService = mock(),
        outgoingEventsSender = mock(),
        visitDataSource = mock(),
        voyageDataSource = mock(),
        v1TraceService = mock(),
        processingTraceService = mock(),
        shipStatuses = mock(),
        eventProcessingService = service,
        slowMovingService = mock(),
        postProcessingService = mock(),
        persistChangesService = mock(),
        changesPublisherService = mock()
    )

    data class TestCase(
        val eventFile: String,
        val expected: List<EntryESoFWrapper<*>>
    )

    private fun executeTestFromFile(eventFile: String): List<EntryESoFWrapper<*>> {
        val url = javaClass.classLoader.getResource(eventFile) ?: fail("can't load $eventFile")
        val events = globalObjectMapper.readValue<List<AisEngineEvent>>(url)
        return entryProcessingService.processAisEngineEventsDryRunForStoryV2(events.asSequence())
    }

    private fun executeTestCase(testCase: TestCase) {
        val result = executeTestFromFile(testCase.eventFile)

        assertThat(result)
            .usingRecursiveFieldByFieldElementComparatorIgnoringFields("entry.updatedAt", "esof.updatedAt")
            .isEqualTo(testCase.expected)
    }

    @BeforeEach
    fun setUpMockDefaults() {
        reset(infraService)
        infraService.apply {
            whenever(this.findStopClassification(any())).thenReturn(ClassifiedStop(NewStopType.UNCLASSIFIED, null))
        }
    }

    @Test
    fun `should confirm visit when entering DKAAR stopping at a berth`() {
        infraService.apply {
            val testBerth = createBerth().copy(mainPort = "DKAAR")
            whenever(this.isPortMainPort(eq("DKAAR"))).thenReturn(true)
            whenever(this.findStopClassification(eq(atLocation lat 56.152650925925926 lon 10.237822592592593)))
                .thenReturn(ClassifiedStop(NewStopType.BERTH, "44DC07078B0313C9369C9766F4D7C119CAC1B4D2"))
            whenever(this.getById(eq("44DC07078B0313C9369C9766F4D7C119CAC1B4D2"), eq(InfraAreaType.BERTH)))
                .thenReturn(testBerth)
            whenever(this.getMainPortFromBerth(eq(testBerth)))
                .thenReturn(createPort(_id = "DF485E5B618093BE40A8CC41BDC5C4ED3F209860", unlocode = "DKAAR"))
        }

        val testCase = TestCase(
            eventFile = "./scenarios/9502960-june-01_june-03.json",
            expected = EntriesBuilder()
                .shipImo(9502960)
                .visit {
                    withId("22461079-1eae-49ec-aff8-1fa062732fc4.VISIT")
                    starting(atLocation lat 56.133805 lon 10.327673333333333 atTime "2024-06-02T03:17:25Z") {
                        withId("22461079-1eae-49ec-aff8-1fa062732fc4")
                        atArea("DF485E5B618093BE40A8CC41BDC5C4ED3F209860.eosp")
                    }
                    isConfirmed()
                    withDestination {
                        ais("DEHAM")
                        actual("DEHAM")
                        atTime("2024-06-02T18:31:07.099Z")
                    }

                    stop(NewStopType.UNCLASSIFIED) {
                        startEventId("cff079a5-efd9-46c8-b6ca-cf474413ef0e")
                        endEventId("2399edc0-868c-4142-8905-92b96e2da83a")
                        location(atLocation lat 56.16032129629629 lon 10.241634629629631)
                        starting(atLocation lat 56.160828333333335 lon 10.241893333333334 atTime "2024-06-02T03:56:55.297Z")
                        ending(atLocation lat 56.158945 lon 10.240665 atTime "2024-06-02T04:06:05.549Z")
                    }
                    stop(NewStopType.BERTH) {
                        atArea("44DC07078B0313C9369C9766F4D7C119CAC1B4D2")
                        startEventId("71d88ae9-ed66-4857-a5df-65477e258a4d")
                        endEventId("dd2c8e17-31e8-40b7-9f8f-cff1d67581e5")
                        atArea("44DC07078B0313C9369C9766F4D7C119CAC1B4D2")
                        location(atLocation lat 56.152650925925926 lon 10.237822592592593)
                        starting(atLocation lat 56.152635000000004 lon 10.237771666666667 atTime "2024-06-02T04:32:25.186Z")
                        ending(atLocation lat 56.15482333333333 lon 10.238925 atTime "2024-06-02T20:36:01Z")
                    }

                    port {
                        withId("1cbfc20c-4a37-4a14-9f97-33eb2db2bb53")
                        atArea("DF485E5B618093BE40A8CC41BDC5C4ED3F209860")
                        starting(atLocation lat 56.159020000000005 lon 10.249718333333334 atTime "2024-06-02T03:48:36Z")

                        terminalMooring {
                            withId("25309353-7297-4648-9438-8d9cdf7d63cc")
                            atArea("88927E2E5ECEA644B536FE8F86E0DD474E109E2F.mooringarea")
                            starting(atLocation lat 56.153335 lon 10.237658333333334 atTime "2024-06-02T04:20:15.584Z")

                            berth {
                                withId("e129b335-539b-4fb6-90d6-ed2887a31b98")
                                atArea("44DC07078B0313C9369C9766F4D7C119CAC1B4D2")
                                starting(atLocation lat 56.152635000000004 lon 10.237771666666667 atTime "2024-06-02T04:32:25.186Z")
                                ending(atLocation lat 56.152635000000004 lon 10.237771666666667 atTime "2024-06-02T20:31:39.242Z")
                            }

                            ending(atLocation lat 56.15596166666667 lon 10.239736666666666 atTime "2024-06-02T20:38:01Z")
                        }

                        terminalMooring {
                            withId("e37a4ab0-e803-4056-abc8-87951b3e2b74")
                            atArea("08967F6CC7BDCFC800CAB1FD3DDD51EBE45708F3.mooringarea")
                            starting(atLocation lat 56.153335 lon 10.237658333333334 atTime "2024-06-02T04:20:15.584Z")
                            ending(atLocation lat 56.15669666666667 lon 10.240141666666666 atTime "2024-06-02T20:39:09Z")
                        }

                        ending(atLocation lat 56.15871333333334 lon 10.250850000000002 atTime "2024-06-02T20:53:10.076Z")
                    }

                    esof {
                        encounter(EncounterType.TUG) {
                            startEventId("af52fec1-571d-4049-8040-df762664afdb")
                            serviceVessel {
                                withMmsi(219029751)
                                withImo(9913705)
                            }

                            starting(atLocation lat 56.147304999999996 lon 10.293276666666666 atTime "2024-06-02T03:28:16Z")
                            ending(atLocation lat 56.152656666666665 lon 10.23781 atTime "2024-06-02T04:51:24.639Z")
                        }
                        encounter(EncounterType.TUG) {
                            startEventId("254dab4a-9787-49ee-98e6-77c89c46ab5c")
                            serviceVessel {
                                withMmsi(219002416)
                                withImo(9259238)
                            }

                            starting(atLocation lat 56.150495 lon 10.281226666666667 atTime "2024-06-02T03:32:13Z")
                            ending(atLocation lat 56.15264666666667 lon 10.237805 atTime "2024-06-02T04:52:24Z")
                        }
                        encounter(EncounterType.TUG_WAITING_DEPARTURE) {
                            startEventId("2c15be65-8486-4540-bcfb-e579d753fe85")
                            serviceVessel {
                                withMmsi(219029751)
                                withImo(9913705)
                            }

                            starting(atLocation lat 56.152658333333335 lon 10.237771666666667 atTime "2024-06-02T19:59:10Z")
                            ending(atLocation lat 56.15596166666667 lon 10.239736666666666 atTime "2024-06-02T20:35:06Z")
                        }
                        encounter(EncounterType.TUG_WAITING_DEPARTURE) {
                            startEventId("c74c2386-d874-443e-981f-8379d685beac")
                            serviceVessel {
                                withMmsi(219002416)
                                withImo(9259238)
                            }

                            starting(atLocation lat 56.15270666666667 lon 10.237868333333333 atTime "2024-06-02T20:07:43.664Z")
                            ending(atLocation lat 56.15482333333333 lon 10.238925 atTime "2024-06-02T20:32:39.372Z")
                        }
                        encounter(EncounterType.TUG) {
                            startEventId("1e0fef8f-943b-4e16-a3f5-ac995eb846ca")
                            serviceVessel {
                                withMmsi(219029751)
                                withImo(9913705)
                            }

                            starting(atLocation lat 56.15821666666667 lon 10.252559999999999 atTime "2024-06-02T20:52:09.940Z")
                            ending(atLocation lat 56.154311666666665 lon 10.265985 atTime "2024-06-02T20:56:43Z")
                        }
                        encounter(EncounterType.PILOT) {
                            startEventId("ee368e13-df70-4c91-82b9-2e6b3685f816")
                            serviceVessel {
                                withMmsi(219005477)
                            }

                            starting(atLocation lat 56.157405000000004 lon 10.255353333333334 atTime "2024-06-02T20:55:19.600Z")
                            ending(atLocation lat 56.15293333333333 lon 10.270800000000001 atTime "2024-06-02T21:01:30Z")
                        }
                    }

                    pilot {
                        withId("1e4dbc9c-6cfa-406e-8f56-171906923b4a")
                        atArea("0CEA9680783D46BFB534A0CB01524F1B73C17C35")
                        starting(atLocation lat 56.14117 lon 10.311213333333335 atTime "2024-06-02T03:23:35Z")
                        ending(atLocation lat 56.150495 lon 10.281226666666667 atTime "2024-06-02T03:35:55Z")
                    }
                    pilot {
                        withId("e2623757-c39e-4713-a358-8fcb3b7a5f0e")
                        atArea("0CEA9680783D46BFB534A0CB01524F1B73C17C35")
                        starting(atLocation lat 56.14933166666666 lon 10.283351666666666 atTime "2024-06-02T21:07:30Z")
                        ending(atLocation lat 56.14002166666667 lon 10.316166666666668 atTime "2024-06-02T21:14:41Z")
                    }

                    anchorArea {
                        withId("17cd39e3-6d3b-4aa9-aa97-99142d729b6f")
                        atArea("0E868EF5BDDFA69AEACEEC336A99F01AC2E27684")
                        starting(atLocation lat 56.15121166666667 lon 10.278618333333332 atTime "2024-06-02T03:37:06Z")
                        ending(atLocation lat 56.15180333333333 lon 10.276433333333333 atTime "2024-06-02T03:38:06Z")
                    }

                    anchorArea {
                        withId("813f5171-1703-4ff9-9de4-e9d398c29370")
                        atArea("0E868EF5BDDFA69AEACEEC336A99F01AC2E27684")
                        starting(atLocation lat 56.150331666666666 lon 10.279835 atTime "2024-06-02T21:06:30Z")
                        ending(atLocation lat 56.14933166666666 lon 10.283351666666666 atTime "2024-06-02T21:07:30Z")
                    }
                }
                .voyage {
                    withId("5266bf84-6395-40bc-9849-2eac8e449e32.VOYAGE")
                    withDestination {
                        ais("DEHAM")
                        actual("DEHAM")
                        atTime("2024-06-02T18:31:07.099Z")
                    }

                    starting(atLocation lat 56.1346 lon 10.336456666666667 atTime "2024-06-02T21:19:00Z")
                }
                .build()
        )

        executeTestCase(testCase)
    }

    @Test
    fun `should have DEBRV visit instead of DEHAM when stopping in port`() {
        infraService.apply {
            val testBerth = createBerth().copy(mainPort = "DEBRV")
            whenever(this.isPortMainPort(eq("DEBRV"))).thenReturn(true)
            whenever(this.isPortMainPort(eq("DEHAM"))).thenReturn(true)
            whenever(this.findStopClassification(eq(atLocation lat 53.576000000000015 lon 8.53741888888889)))
                .thenReturn(ClassifiedStop(NewStopType.BERTH, "933C5598514357762E339F04449CF663F9ED77D2"))
            whenever(this.getById(eq("933C5598514357762E339F04449CF663F9ED77D2"), eq(InfraAreaType.BERTH)))
                .thenReturn(testBerth)
            whenever(this.getMainPortFromBerth(eq(testBerth)))
                .thenReturn(createPort(_id = "A9238D8A07733C8BDB707B8FF24FD73BC03A020A", unlocode = "DEBRV"))
        }

        val testCase = TestCase(
            eventFile = "./scenarios/9299642-may-25_may-28.json",
            expected = EntriesBuilder()
                .shipImo(9299642)
                .visit {
                    withId("8d8b2662-3a92-4a7b-9f4c-23e6bccda0c9.VISIT")
                    starting(atLocation lat 54.02815 lon 7.5167 atTime "2024-05-25T23:15:44Z") {
                        withId("8d8b2662-3a92-4a7b-9f4c-23e6bccda0c9")
                        atArea("A9238D8A07733C8BDB707B8FF24FD73BC03A020A.eosp")
                    }
                    isConfirmed()
                    withDestination {
                        ais("FRLEH")
                        actual("FRLEH")
                        atTime("2024-05-27T09:03:46.798Z")
                    }

                    stop(NewStopType.UNCLASSIFIED) {
                        startEventId("3220f08c-d026-4dfc-afdc-34e82361fc38")
                        endEventId("2f97625c-0114-4997-a90c-28bc3ee79441")
                        location(atLocation lat 54.02967166666667 lon 7.485516666666667)
                        starting(atLocation lat 54.029599999999995 lon 7.482733333333333 atTime "2024-05-25T08:07:57Z")
                        ending(atLocation lat 54.03146666666667 lon 7.486416666666667 atTime "2024-05-25T22:58:49Z")
                    }
                    stop(NewStopType.BERTH) {
                        atArea("933C5598514357762E339F04449CF663F9ED77D2")
                        startEventId("8854fdd7-fe44-40bd-b234-a31a4e68edf5")
                        endEventId("acc94a1c-a3fb-433b-bccd-a99198b131e6")
                        atArea("933C5598514357762E339F04449CF663F9ED77D2")
                        location(atLocation lat 53.576000000000015 lon 8.53741888888889)
                        starting(atLocation lat 53.576033333333335 lon 8.537366666666665 atTime "2024-05-26T03:30:03Z")
                        ending(atLocation lat 53.57735 lon 8.530316666666668 atTime "2024-05-27T11:16:13.395Z")
                    }

                    port {
                        withId("5c181f6d-cdf8-42b3-ac86-e8db251a9c75")
                        atArea("A9238D8A07733C8BDB707B8FF24FD73BC03A020A")
                        starting(atLocation lat 53.704883333333335 lon 8.3173 atTime "2024-05-26T02:20:26Z")

                        terminalMooring {
                            withId("89e7090a-1c0b-421c-aa6f-3e0de6ad50b7")
                            atArea("BC520A16EA84B858D779365E5FC531EFCDCED54A.mooringarea")
                            starting(atLocation lat 53.58728333333333 lon 8.5226 atTime "2024-05-26T03:12:31.789Z")

                            berth {
                                withId("1ac2dc69-2ec2-47c7-a10c-abadc0a76a39")
                                atArea("933C5598514357762E339F04449CF663F9ED77D2")
                                starting(atLocation lat 53.576966666666664 lon 8.535300000000001 atTime "2024-05-26T03:23:32Z")
                                ending(atLocation lat 53.576966666666664 lon 8.535300000000001 atTime "2024-05-27T11:05:53.661Z")
                            }

                            ending(atLocation lat 53.594516666666664 lon 8.514466666666667 atTime "2024-05-27T11:39:31Z")
                        }

                        ending(atLocation lat 53.70891666666667 lon 8.31095 atTime "2024-05-27T12:28:32Z")
                    }

                    esof {
                        encounter(EncounterType.PILOT) {
                            startEventId("19a40c1a-1879-484f-be89-4a449ac76b5d")
                            serviceVessel {
                                withMmsi(211464260)
                                withImo(9514808)
                            }

                            starting(atLocation lat 53.87146666666667 lon 7.818816666666667 atTime "2024-05-26T00:52:07Z")
                            ending(atLocation lat 53.86881666666667 lon 7.842516666666667 atTime "2024-05-26T01:00:01Z")
                        }
                        encounter(EncounterType.TUG) {
                            startEventId("0eeda433-5c19-4d5e-9229-4edad57444a6")
                            serviceVessel {
                                withMmsi(244661000)
                                withImo(9691369)
                            }

                            starting(atLocation lat 53.594633333333334 lon 8.512783333333333 atTime "2024-05-26T03:08:12Z")
                            ending(atLocation lat 53.576 lon 8.537416666666667 atTime "2024-05-26T03:59:24Z")
                        }
                        encounter(EncounterType.TUG_WAITING_DEPARTURE) {
                            startEventId("a90067be-fc8e-4b03-b9eb-f2305299a62a")
                            serviceVessel {
                                withMmsi(211466070)
                                withImo(9454319)
                            }
                            starting(atLocation lat 53.576 lon 8.537433333333333 atTime "2024-05-27T10:18:23Z")
                            ending(atLocation lat 53.5757 lon 8.536533333333333 atTime "2024-05-27T10:57:55Z")
                        }
                        encounter(EncounterType.TUG) {
                            startEventId("d547d8bb-8e79-4810-830f-e9db746b06f7")
                            serviceVessel {
                                withMmsi(211466070)
                                withImo(9454319)
                            }

                            starting(atLocation lat 53.57886666666666 lon 8.532633333333333 atTime "2024-05-27T11:05:53.661Z")
                            ending(atLocation lat 53.58278333333333 lon 8.527266666666668 atTime "2024-05-27T11:26:22.393Z")
                        }
                        encounter(EncounterType.PILOT) {
                            startEventId("7bc6cad6-ad1d-41d6-a60f-afa4333398a9")
                            serviceVessel {
                                withMmsi(211536810)
                            }

                            starting(atLocation lat 53.579100000000004 lon 8.530883333333334 atTime "2024-05-27T11:26:22.393Z")
                            ending(atLocation lat 53.5869 lon 8.523100000000001 atTime "2024-05-27T11:31:07Z")
                        }
                    }

                    pilot {
                        withId("86cbd9ee-b7b5-4a76-a56b-5039c6ab6a39")
                        atArea("B6C4D5E53D28803B5A4A51242150941337CD0D1B")
                        starting(atLocation lat 53.90148333333333 lon 7.392133333333334 atTime "2024-05-25T06:45:57Z")
                        ending(atLocation lat 53.90775 lon 7.438133333333334 atTime "2024-05-25T06:53:09Z")
                    }

                    pilot {
                        withId("cb85c8ed-cb5a-40f7-a8fa-d3b118f44045")
                        atArea("5D163EFAC806F9EC7060C2381F1AF13C0B84B80D")
                        starting(atLocation lat 53.96106666666667 lon 7.515366666666667 atTime "2024-05-25T07:19:17Z")
                        ending(atLocation lat 54.00871666666667 lon 7.48225 atTime "2024-05-25T07:45:37Z")
                    }

                    pilot {
                        withId("aac20c2c-e6c0-453c-959e-15d77119a4de")
                        atArea("13A53CB7B3725B723D36430DF5F76D30042C895A")
                        starting(atLocation lat 53.88641666666667 lon 7.737533333333333 atTime "2024-05-26T00:32:20Z")
                        ending(atLocation lat 53.87146666666667 lon 7.818816666666667 atTime "2024-05-26T00:53:31Z")
                    }

                    pilot {
                        withId("4f819a9a-e38b-4a48-b57a-9d3020395383")
                        atArea("13A53CB7B3725B723D36430DF5F76D30042C895A")
                        starting(atLocation lat 53.878883333333334 lon 7.819249999999999 atTime "2024-05-27T13:56:30Z")
                        ending(atLocation lat 53.93175 lon 7.719483333333333 atTime "2024-05-27T14:09:30Z")
                    }

                    pilot {
                        withId("4c007db0-f1ec-4016-93cc-47a9454137aa")
                        atArea("5D163EFAC806F9EC7060C2381F1AF13C0B84B80D")
                        starting(atLocation lat 53.987116666666665 lon 7.536066666666667 atTime "2024-05-27T14:40:02Z")
                        ending(atLocation lat 53.97943333333333 lon 7.4536999999999995 atTime "2024-05-27T14:50:31Z")
                    }

                    anchorArea {
                        withId("23298ae2-8135-408c-8662-cb1330102cb8")
                        atArea("C90AB609A326D30B36DA78157BACF6E791F66DDC")
                        starting(atLocation lat 53.87368333333333 lon 7.79225 atTime "2024-05-26T00:45:10Z")
                        ending(atLocation lat 53.86098333333334 lon 7.9024 atTime "2024-05-26T01:10:33Z")
                    }

                    anchorArea {
                        withId("96ebc9dd-ae01-43ff-b97d-121e78dc0940")
                        atArea("C90AB609A326D30B36DA78157BACF6E791F66DDC")
                        starting(atLocation lat 53.869083333333336 lon 7.883683333333334 atTime "2024-05-27T13:48:16Z")
                        ending(atLocation lat 53.88623333333333 lon 7.773016666666666 atTime "2024-05-27T14:02:36Z")
                    }

                    passThroughEosp {
                        withId("e0cc79f8-446b-4422-a5d1-a88935c61774")
                        atArea("A9238D8A07733C8BDB707B8FF24FD73BC03A020A.eosp")
                        starting(atLocation lat 53.9434 lon 7.522283333333333 atTime "2024-05-25T07:13:47Z")
                        ending(atLocation lat 54.010016666666665 lon 7.48125 atTime "2024-05-25T07:46:49Z")
                    }
                }
                .voyage {
                    withId("f1f0f042-b0cc-4de7-88e2-e3ddb1e210a1.VOYAGE")
                    withDestination {
                        ais("FRLEH")
                        actual("FRLEH")
                        atTime("2024-05-27T09:03:46.798Z")
                    }

                    starting(atLocation lat 53.97943333333333 lon 7.4536999999999995 atTime "2024-05-27T14:50:31Z")

                    passThroughEosp {
                        withId("f1f0f042-b0cc-4de7-88e2-e3ddb1e210a1")
                        atArea("E459ABCBA1D6760D8B34A3C1E55982454DF8F489.eosp")
                        starting(atLocation lat 53.72341666666667 lon 6.245116666666666 atTime "2024-05-25T02:40:39Z")
                        ending(atLocation lat 53.81335 lon 6.260566666666667 atTime "2024-05-27T17:28:18Z")
                    }
                }
                .build()
        )

        executeTestCase(testCase)
    }

    @Test
    fun `when handling non-confirmed visit, ensure we don't move backward in time`() {
        infraService.apply {
            whenever(this.isPortMainPort(any())).thenReturn(true)
            whenever(this.findStopClassification(atLocation lat 51.3226648958333 lon 3.19411072916667))
                .thenReturn(ClassifiedStop(NewStopType.BERTH, "1DBB2EC3654D3A7D46D2F2C048E63AB890E9F45F"))
            whenever(this.getById(eq("1DBB2EC3654D3A7D46D2F2C048E63AB890E9F45F"), eq(InfraAreaType.BERTH)))
                .thenReturn(createBerth().copy(mainPort = "BEZEE"))
            whenever(this.getPortByUnlocode(eq("BEZEE")))
                .thenReturn(createPort(_id = "A20A5489147E060A89A0DA5591671E13C6C2631C", unlocode = "BEZEE"))

            whenever(this.findStopClassification(atLocation lat 51.2999416666667 lon 3.84173833333333))
                .thenReturn(ClassifiedStop(NewStopType.BERTH, "5760FA1A43560F0FAC43635C505CDC4CBB148ADC"))
            whenever(this.getById(eq("5760FA1A43560F0FAC43635C505CDC4CBB148ADC"), eq(InfraAreaType.BERTH)))
                .thenReturn(createBerth().copy(mainPort = "NLTNZ"))
            whenever(this.getPortByUnlocode(eq("NLTNZ")))
                .thenReturn(createPort(_id = "43CD8DDAB8A64D55B47F8226210FE5655812B1F9", unlocode = "NLTNZ"))
        }

        val result = executeTestFromFile("./scenarios/8509820-july-14_july-20.json")
        assertTrue(result.all { it.entry.end == null || it.entry.start.time <= it.entry.end!!.time })
    }

    @Test
    fun `visit must also be marked as pass through if no time was spent`() {
        infraService.apply {
            whenever(this.isPortMainPort(any())).thenReturn(true)

            whenever(this.findStopClassification(any())).thenReturn(ClassifiedStop(NewStopType.UNCLASSIFIED, null))
            whenever(this.findStopClassification(eq(atLocation lat -5.39753145833333 lon 12.1948733333333)))
                .thenReturn(ClassifiedStop(NewStopType.BERTH, "4C858E6FAD7143D42B211D9A4F575697EA936291"))
            whenever(this.findStopClassification(eq(atLocation lat -5.39538833333333 lon 12.1503116666667)))
                .thenReturn(ClassifiedStop(NewStopType.ANCHOR_AREA, "557AE765D343FE23405D62B17C99487F1DB6B2EE"))
            whenever(this.findStopClassification(eq(atLocation lat -5.395805 lon 12.1496488888889)))
                .thenReturn(ClassifiedStop(NewStopType.ANCHOR_AREA, "557AE765D343FE23405D62B17C99487F1DB6B2EE"))
            whenever(this.findStopClassification(eq(atLocation lat -5.39965666666667 lon 12.1652383333333)))
                .thenReturn(ClassifiedStop(NewStopType.ANCHOR_AREA, "557AE765D343FE23405D62B17C99487F1DB6B2EE"))
            whenever(this.findStopClassification(eq(atLocation lat -5.39706666666667 lon 12.194115)))
                .thenReturn(ClassifiedStop(NewStopType.BERTH, "EC87909B9B478B0C74B232DFA3751EB19882DF50"))
            whenever(this.findStopClassification(eq(atLocation lat -5.39668916666667 lon 12.1868875)))
                .thenReturn(ClassifiedStop(NewStopType.ANCHOR_AREA, "557AE765D343FE23405D62B17C99487F1DB6B2EE"))
            whenever(this.findStopClassification(eq(atLocation lat -5.39704 lon 12.1941333333333)))
                .thenReturn(ClassifiedStop(NewStopType.BERTH, "EC87909B9B478B0C74B232DFA3751EB19882DF50"))

            whenever(this.getById(eq("4C858E6FAD7143D42B211D9A4F575697EA936291"), eq(InfraAreaType.BERTH)))
                .thenReturn(createBerth(mainPort = "AOCAB"))
            whenever(this.getById(eq("EC87909B9B478B0C74B232DFA3751EB19882DF50"), eq(InfraAreaType.BERTH)))
                .thenReturn(createBerth(mainPort = "AOMAL"))

            whenever(this.getPortByUnlocode(eq("AOCAB")))
                .thenReturn(createPort(_id = "9A7C535C0A1F126DFAE2C90829836A75E1A0C33C", unlocode = "AOCAB"))
            whenever(this.getPortByUnlocode(eq("AOMAL")))
                .thenReturn(createPort(_id = "91D4EC5A26859D4A959776449082BC7D6BDCC12C", unlocode = "AOMAL"))
        }

        val result = executeTestFromFile("./scenarios/9663221-june-16_july-07.json")
        assertTrue(
            result.all {
                it.entry.end == null ||
                    (it.entry is NewVoyage && it.entry.start.time <= it.entry.end!!.time) ||
                    (it.entry is NewVisit && it.entry.start.time < it.entry.end!!.time)
            }
        )
    }

    @Test
    fun `should merge NLRTM stops inside anchorage together`() {
        infraService.apply {
            val testAnchorage = createAnchorage()
            val testBerth = createBerth()
            whenever(this.isPortMainPort(eq("NLRTM"))).thenReturn(true)
            whenever(this.findStopClassification(eq(atLocation lat 51.95366666666666 lon 3.7338216666666666)))
                .thenReturn(ClassifiedStop(NewStopType.ANCHOR_AREA, "0C750A29F34EE1BE21D21AF319FAC52E064BB4F4"))
            whenever(this.findStopClassification(eq(atLocation lat 51.68185916666666 lon 4.578539791666667)))
                .thenReturn(ClassifiedStop(NewStopType.BERTH, "TEST_BERTH_ID"))
            whenever(this.getById(eq("0C750A29F34EE1BE21D21AF319FAC52E064BB4F4"), eq(InfraAreaType.ANCHOR)))
                .thenReturn(testAnchorage)
            whenever(this.getById(eq("TEST_BERTH_ID"), eq(InfraAreaType.BERTH)))
                .thenReturn(testBerth)
        }

        val testCase = TestCase(
            eventFile = "./scenarios/9006394-aug-23_aug-28.json",
            expected = EntriesBuilder()
                .shipImo(9006394)
                .visit {
                    withId("959f98d7-3e46-41d3-b052-7e9cf5dbf1d9.VISIT")
                    starting(atLocation lat 51.83016333333333 lon 2.9347100000000004 atTime "2024-08-24T12:34:56.890Z") {
                        withId("959f98d7-3e46-41d3-b052-7e9cf5dbf1d9")
                        atArea("E57297BAEF6EE30BCDE04E8DFAED7E389A8AF210.eosp")
                    }

                    port {
                        withId("0c27fe4d-a5f3-4371-bdc8-91b3e585e289")
                        atArea("E57297BAEF6EE30BCDE04E8DFAED7E389A8AF210")
                        starting(atLocation lat 51.989691666666666 lon 4.046478333333334 atTime "2024-08-25T19:57:56Z")

                        stop(NewStopType.ANCHOR_AREA) {
                            atArea("0C750A29F34EE1BE21D21AF319FAC52E064BB4F4")
                            startEventId("abef5cf1-58f2-42fe-90d5-c14796e82c48")
                            endEventId("e49ffcc5-2d36-44d9-9fd2-5c51f868f524")
                            location(atLocation lat 51.95862385416666 lon 3.7379475)
                            starting(atLocation lat 51.95366666666666 lon 3.7338216666666666 atTime "2024-08-24T16:08:45Z")
                            ending(atLocation lat 51.957883333333335 lon 3.73626 atTime "2024-08-25T18:01:24.087Z")
                        }

                        stop(NewStopType.BERTH) {
                            atArea("TEST_BERTH_ID")
                            startEventId("ae5d30cd-832b-4f99-84be-4fa969e953ad")
                            endEventId("3346a0d8-90ed-47cb-9f52-59a2d1a424f2")
                            location(atLocation lat 51.68185916666666 lon 4.578539791666667)
                            starting(atLocation lat 51.681810000000006 lon 4.578823333333333 atTime "2024-08-26T01:02:47.705Z")
                            ending(atLocation lat 51.68273833333333 lon 4.577778333333333 atTime "2024-08-27T17:44:50Z")
                        }

                        ending(atLocation lat 51.69532 lon 4.486036666666666 atTime "2024-08-27T18:30:49.463Z")
                    }
                }
                .build()
        )

        executeTestCase(testCase)
    }

    @Test
    fun `should merge NLAMS stops inside anchorage when drifting`() {
        infraService.apply {
            val testAnchorage = createAnchorage(
                _id = "D179CA84F8E8860DEA37C39D245C419B721648A3",
                name = "ANCHORPLACE 7",
                ports = listOf("NLAMS"),
                area = listOf(
                    Location(lat = 52.406615331286595, lon = 3.9063841693115195),
                    Location(lat = 52.42588537701523, lon = 3.9074141693115196),
                    Location(lat = 52.441995415254105, lon = 3.9582241693115194),
                    Location(lat = 52.47187133161729, lon = 4.158644730224594),
                    Location(lat = 52.44717523354421, lon = 4.161473883666932),
                    Location(lat = 52.43448496267316, lon = 4.032209185180634),
                    Location(lat = 52.42483537452322, lon = 4.024834169311519),
                    Location(lat = 52.406615331286595, lon = 3.9063841693115195)
                )
            )
            whenever(this.isPortMainPort(eq("NLAMS"))).thenReturn(true)
            whenever(this.getAnchoragesInLocationGrid(eq(Location(lat = 52.0, lon = 4.0))))
                .thenReturn(listOf(testAnchorage))
            whenever(this.getAnchoragesInLocationGrid(eq(Location(lat = 52.0, lon = 3.9))))
                .thenReturn(listOf(testAnchorage))
            whenever(this.findStopClassification(any()))
                .thenCallRealMethod()
            whenever(this.getById(eq("D179CA84F8E8860DEA37C39D245C419B721648A3"), eq(InfraAreaType.ANCHOR)))
                .thenReturn(testAnchorage)
        }

        val testCase = TestCase(
            eventFile = "./scenarios/9797735-oct-12_act-14.json",
            expected = EntriesBuilder()
                .shipImo(9797735)
                .visit {
                    withId("3c07a0e6-0253-435f-a6eb-3ad892019b9e.VISIT")
                    starting(atLocation lat 52.42344666666667 lon 3.7995900000000002 atTime "2024-10-12T13:03:43.290Z") {
                        withId("3c07a0e6-0253-435f-a6eb-3ad892019b9e")
                        atArea("0B47238B9551350D59BE2A7C38480F53FB85ECC1.eosp")
                    }

                    stop(NewStopType.ANCHOR_AREA) {
                        atArea("D179CA84F8E8860DEA37C39D245C419B721648A3")
                        startEventId("d71d6501-5037-49a8-85ab-64312557f0fe")
                        endEventId("79ea562a-e2c8-40a7-9810-2fc19dd0cb47")
                        location(atLocation lat 52.4492634375 lon 4.054731354166666)
                        starting(atLocation lat 52.44671833333334 lon 4.049675 atTime "2024-10-12T14:08:03.913Z")
                        ending(atLocation lat 52.450316666666666 lon 4.051283333333333 atTime "2024-10-13T20:18:04.004Z")
                    }
                }
                .build()
        )

        executeTestCase(testCase)
    }

    @Test
    fun `should only have one GEPTI visit when drifting outside EOSP but not entering Port`() {
        infraService.apply {
            val testAnchorage = createAnchorage(
                _id = "D179CA84F8E8860DEA37C39D245C419B721648A3",
                name = "ANCHORPLACE 7",
                ports = listOf("GEPTI"),
                area = listOf(
                    Location(lat = 52.406615331286595, lon = 3.9063841693115195),
                    Location(lat = 52.42588537701523, lon = 3.9074141693115196),
                    Location(lat = 52.441995415254105, lon = 3.9582241693115194),
                    Location(lat = 52.47187133161729, lon = 4.158644730224594),
                    Location(lat = 52.44717523354421, lon = 4.161473883666932),
                    Location(lat = 52.43448496267316, lon = 4.032209185180634),
                    Location(lat = 52.42483537452322, lon = 4.024834169311519),
                    Location(lat = 52.406615331286595, lon = 3.9063841693115195)
                )
            )
            whenever(this.isPortMainPort(eq("GEPTI"))).thenReturn(true)
            whenever(this.findStopClassification(any()))
                .thenReturn(ClassifiedStop(type = NewStopType.ANCHOR_AREA, areaId = "D179CA84F8E8860DEA37C39D245C419B721648A3"))
            whenever(this.getById(eq("D179CA84F8E8860DEA37C39D245C419B721648A3"), eq(InfraAreaType.ANCHOR)))
                .thenReturn(testAnchorage)
        }

        val testCase = TestCase(
            eventFile = "./scenarios/9511442-nov-05_nov-09.json",
            expected = EntriesBuilder()
                .shipImo(9511442)
                .visit {
                    withId("2f2e2b21-f6ec-441e-98bf-9baa4f4c66a8.VISIT")
                    starting(atLocation lat 42.19256333333333 lon 41.589508333333335 atTime "2024-11-05T10:22:00Z") {
                        withId("2f2e2b21-f6ec-441e-98bf-9baa4f4c66a8")
                        atArea("0EE9A959EBA31268DA5BFCCAAD408E22F2CB4FF0.eosp")
                    }

                    stop(NewStopType.ANCHOR_AREA) {
                        atArea("D179CA84F8E8860DEA37C39D245C419B721648A3")
                        startEventId("4474fd6c-bde3-4450-8c5e-6aa660fad44f")
                        endEventId("134a331a-d45d-430d-b940-3d366ed3be24")
                        location(atLocation lat 42.189960151515145 lon 41.60298287878788)
                        starting(atLocation lat 42.18982333333334 lon 41.60245666666666 atTime "2024-11-05T12:02:35Z")
                        ending(atLocation lat 42.18968333333333 lon 41.602385 atTime "2024-11-05T20:56:30Z")
                    }

                    anchorArea {
                        withId("f8fff19d-f2d2-48b4-bd38-907204186821")
                        atArea("EA2AD2036D9F824823E4E0D9F771108CA842B127")
                        starting(atLocation lat 42.19256333333333 lon 41.589508333333335 atTime "2024-11-05T10:22:00Z")
                        ending(atLocation lat 42.19357166666667 lon 41.58703333333334 atTime "2024-11-06T13:18:14Z")
                    }

                    // Anchoring happens while inside anchor area
                    anchor {
                        withId("47f7781c-395e-4a24-9045-9714e80f8412")
                        atArea("EA2AD2036D9F824823E4E0D9F771108CA842B127")
                        starting(atLocation lat 42.19027833333334 lon 41.602545 atTime "2024-11-05T10:44:26Z")
                        ending(atLocation lat 42.192456666666665 lon 41.59171166666667 atTime "2024-11-06T13:16:14Z")
                    }

                    // We leave the anchor area and EOSP. Later coming back into the anchor area as we need to pass it to enter the port
                    anchorArea {
                        withId("ec2f8c60-0896-4b36-afdb-dadcd3e33099")
                        atArea("EA2AD2036D9F824823E4E0D9F771108CA842B127")
                        starting(atLocation lat 42.17682 lon 41.596250000000005 atTime "2024-11-06T13:38:54Z")
                        ending(atLocation lat 42.16796 lon 41.62289666666666 atTime "2024-11-06T13:50:48Z")
                    }

                    pilot {
                        withId("79ca24fb-79dd-49d7-b7e6-f3fabc0f8177")
                        atArea("40CF4006AAD3631BA79CE530F4149EF90BCE53FB")
                        starting(atLocation lat 42.16796 lon 41.62289666666666 atTime "2024-11-06T13:50:48Z")
                    }

                    port {
                        withId("460a5788-c208-4dd4-a63b-67e2ba4f6ea6")
                        atArea("0EE9A959EBA31268DA5BFCCAAD408E22F2CB4FF0")
                        starting(atLocation lat 42.15490333333333 lon 41.650285000000004 atTime "2024-11-06T14:17:28Z")

                        // The same AIS point we enter the port and terminal
                        terminalMooring {
                            withId("7d5cdd10-f2fc-404d-9e3b-8913231a3712")
                            atArea("C632C475290FDD6DD2C524D61D69F492D31E3DC7.mooringarea")
                            starting(atLocation lat 42.15490333333333 lon 41.650285000000004 atTime "2024-11-06T14:17:28Z")
                        }
                    }
                }
                .build()
        )

        executeTestCase(testCase)
    }
}
