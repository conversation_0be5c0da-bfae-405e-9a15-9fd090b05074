package nl.teqplay.vesselvoyage.service.processing.dsl

import nl.teqplay.vesselvoyage.model.v2.NewESoF
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit

fun expect(startTime: Instant = Instant.EPOCH) = ExpectBuilder(time = startTime)

data class ExpectBuilder(
    val wrappers: MutableList<ExpectedWrapper> = mutableListOf(),
    var time: Instant,
) {
    fun build(): List<ExpectedWrapper> = wrappers

    fun startVisit(): ExpectVisitBuilder = ExpectVisitBuilder(this)
    fun startVoyage(): ExpectVoyageBuilder = ExpectVoyageBuilder(this)
}

data class ExpectVisitBuilder(
    private val builder: ExpectBuilder,
) {

    private var visit = ExpectedVisit(eosp = ExpectedActivity(start = builder.time))

    fun at(time: String): ExpectVisitBuilder {
        val instant = Instant.parse(time)
        require(instant.isAfter(builder.time)) { "Time should not move backwards." }
        builder.time = instant
        return this
    }

    fun fastForward(amount: Long, unit: ChronoUnit): ExpectVisitBuilder {
        require(amount > 0) { "Must have positive amount to move forward." }
        builder.time += Duration.of(amount, unit)
        return this
    }

    fun startStop(): ExpectVisitBuilder {
        visit = visit.copy(stops = visit.stops + ExpectedActivity(start = builder.time))
        return this
    }

    fun endStop(): ExpectVisitBuilder {
        visit = visit.copy(stops = visit.stops.map { it.copy(end = it.end ?: builder.time) })
        return this
    }

    fun enterPort(): ExpectVisitBuilder {
        visit = visit.copy(port = visit.port + ExpectedActivity(start = builder.time))
        return this
    }

    fun exitPort(): ExpectVisitBuilder {
        visit = visit.copy(port = visit.port.map { it.copy(end = it.end ?: builder.time) })
        return this
    }

    fun enterAnchorage(): ExpectVisitBuilder {
        visit = visit.copy(anchor = visit.anchor + ExpectedActivity(start = builder.time))
        return this
    }

    fun exitAnchorage(): ExpectVisitBuilder {
        visit = visit.copy(anchor = visit.anchor.map { it.copy(end = it.end ?: builder.time) })
        return this
    }

    fun enterBerth(): ExpectVisitBuilder {
        visit = visit.copy(berth = visit.berth + ExpectedActivity(start = builder.time))
        return this
    }

    fun exitBerth(): ExpectVisitBuilder {
        visit = visit.copy(berth = visit.berth.map { it.copy(end = it.end ?: builder.time) })
        return this
    }

    fun endVisit(): ExpectBuilder {
        builder.wrappers.add(ExpectedWrapper(visit.copy(eosp = visit.eosp.copy(end = builder.time))))
        return builder
    }

    fun build(): List<ExpectedWrapper> {
        builder.wrappers.add(ExpectedWrapper(visit))
        return builder.build()
    }
}

data class ExpectVoyageBuilder(
    private val builder: ExpectBuilder,
) {

    private var voyage = ExpectedVoyage(start = builder.time)

    fun at(time: String): ExpectVoyageBuilder {
        val instant = Instant.parse(time)
        require(instant.isAfter(builder.time)) { "Time should not move backwards." }
        builder.time = instant
        return this
    }

    fun fastForward(amount: Long, unit: ChronoUnit): ExpectVoyageBuilder {
        require(amount > 0) { "Must have positive amount to move forward." }
        builder.time += Duration.of(amount, unit)
        return this
    }

    fun endVoyage(): ExpectBuilder {
        builder.wrappers.add(ExpectedWrapper(voyage.copy(end = builder.time)))
        return builder
    }

    fun build(): List<ExpectedWrapper> {
        builder.wrappers.add(ExpectedWrapper(voyage))
        return builder.build()
    }
}

data class ExpectedWrapper(
    val entry: ExpectedEntry,
    val esof: NewESoF? = null
)

sealed interface ExpectedEntry

data class ExpectedVisit(
    val eosp: ExpectedActivity,
    val port: List<ExpectedActivity> = emptyList(),
    val anchor: List<ExpectedActivity> = emptyList(),
    val berth: List<ExpectedActivity> = emptyList(),
    val stops: List<ExpectedActivity> = emptyList()
) : ExpectedEntry

data class ExpectedVoyage(
    val start: Instant,
    val end: Instant? = null
) : ExpectedEntry

data class ExpectedActivity(
    val start: Instant,
    val end: Instant? = null,
)
