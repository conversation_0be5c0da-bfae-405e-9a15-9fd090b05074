package nl.teqplay.vesselvoyage.service

import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.eq
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.never
import com.nhaarman.mockitokotlin2.times
import com.nhaarman.mockitokotlin2.verify
import com.nhaarman.mockitokotlin2.whenever
import nl.teqplay.vesselvoyage.logic.IMO_1
import nl.teqplay.vesselvoyage.logic.createEndOfSeaPassageEvent
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.EventStatus
import nl.teqplay.vesselvoyage.model.NewEventProcessingResult
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewShipStatus
import nl.teqplay.vesselvoyage.model.v2.VisitChange
import nl.teqplay.vesselvoyage.model.v2.VoyageChange
import nl.teqplay.vesselvoyage.service.processing.EventProcessingService
import nl.teqplay.vesselvoyage.util.createLocationTime
import nl.teqplay.vesselvoyage.util.createNewVisit
import nl.teqplay.vesselvoyage.util.createNewVisitShipStatus
import nl.teqplay.vesselvoyage.util.createNewVoyage
import nl.teqplay.vesselvoyage.util.createNewVoyageShipStatus
import org.junit.jupiter.api.Test
import java.time.YearMonth
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit

class EntryProcessingServiceTest {
    private val eventProcessingService = mock<EventProcessingService>()
    private val postProcessingService = mock<PostProcessingService>()
    private val processingShipStatusService = mock<ProcessingShipStatusService>()
    private val service = EntryProcessingService(
        entryService = mock(),
        traceProperties = mock(),
        eventProcessingProperties = mock(),
        visitDataSource = mock(),
        voyageDataSource = mock(),
        v1TraceService = mock(),
        processingTraceService = mock(),
        eventFetchingService = mock(),
        outgoingEventsSender = mock(),
        shipStatuses = processingShipStatusService,
        eventProcessingService = eventProcessingService,
        slowMovingService = mock(),
        postProcessingService = postProcessingService,
        persistChangesService = mock(),
        changesPublisherService = mock()
    )

    private val visitId = "VISIT_1"
    private val voyageId = "VOYAGE_2"
    private val visitStartTime = YearMonth.of(2024, 1)
        .atDay(1)
        .atStartOfDay()
        .toInstant(ZoneOffset.UTC)
    private val voyageStartTime = visitStartTime.plus(1, ChronoUnit.DAYS)

    private val visitStart = createLocationTime(time = visitStartTime)
    private val voyageStart = createLocationTime(time = voyageStartTime)

    private val ongoingVisit = createNewVisit(
        _id = visitId,
        start = visitStart
    )
    private val currentVisitStatus = createNewVisitShipStatus(
        visit = ongoingVisit
    )
    private val finishedVisit = ongoingVisit.copy(
        next = voyageId,
        end = voyageStart
    )
    private val ongoingVoyage = createNewVoyage(
        _id = voyageId,
        previous = visitId,
        start = voyageStart
    )
    private val testEvent = createEndOfSeaPassageEvent(
        _id = "TEST_ID",
        status = EventStatus.END,
        time = ZonedDateTime.now()
    )

    @Test
    fun `should trigger post processing service on finishing a Visit with newly created Voyage`() {
        val newVoyageStatus = createNewVoyageShipStatus(
            voyage = ongoingVoyage,
            previousVisit = finishedVisit,
            previousVoyage = null
        )
        val eventProcessingResult = NewEventProcessingResult(
            status = newVoyageStatus,
            changes = listOf(
                VisitChange(Action.UPDATE, finishedVisit),
                VoyageChange(Action.CREATE, ongoingVoyage)
            ),
            readyForPostProcessing = listOf(finishedVisit._id)
        )
        whenever(eventProcessingService.onEventAndBuffer(any<NewShipStatus>(), any(), any()))
            .thenReturn(eventProcessingResult)
        whenever(processingShipStatusService.getStatus(any()))
            .thenReturn(currentVisitStatus)

        service.insertNew(testEvent, IMO_1)

        verify(postProcessingService, times(1)).schedulePostProcessing(eq(finishedVisit._id))
    }

    @Test
    fun `should NOT trigger post processing service when canceling Visit`() {
        val newStatus = NewInitialShipStatus()
        val eventProcessingResult = NewEventProcessingResult(
            status = newStatus,
            changes = listOf(
                VisitChange(Action.DELETE, ongoingVisit)
            )
        )
        whenever(eventProcessingService.onEventAndBuffer(any<NewShipStatus>(), any(), any()))
            .thenReturn(eventProcessingResult)
        whenever(processingShipStatusService.getStatus(any()))
            .thenReturn(currentVisitStatus)

        service.insertNew(testEvent, IMO_1)

        verify(postProcessingService, never()).schedulePostProcessing(any())
    }

    @Test
    fun `should NOT trigger post processing service when only updating Visit`() {
        val eventProcessingResult = NewEventProcessingResult(
            status = currentVisitStatus,
            changes = listOf(
                VisitChange(Action.UPDATE, ongoingVisit)
            )
        )
        whenever(eventProcessingService.onEventAndBuffer(any<NewShipStatus>(), any(), any()))
            .thenReturn(eventProcessingResult)
        whenever(processingShipStatusService.getStatus(any()))
            .thenReturn(currentVisitStatus)

        service.insertNew(testEvent, IMO_1)

        verify(postProcessingService, never()).schedulePostProcessing(any())
    }

    @Test
    fun `should NOT trigger post processing service when only updating Voyage`() {
        val currentVoyageStatus = createNewVoyageShipStatus(
            voyage = ongoingVoyage,
            previousVisit = finishedVisit
        )
        val eventProcessingResult = NewEventProcessingResult(
            status = currentVoyageStatus,
            changes = listOf(
                VoyageChange(Action.UPDATE, ongoingVoyage)
            )
        )
        whenever(eventProcessingService.onEventAndBuffer(any<NewShipStatus>(), any(), any()))
            .thenReturn(eventProcessingResult)
        whenever(processingShipStatusService.getStatus(any()))
            .thenReturn(currentVoyageStatus)

        service.insertNew(testEvent, IMO_1)

        verify(postProcessingService, never()).schedulePostProcessing(any())
    }

    @Test
    fun `should trigger post processing service on finishing a Voyage with newly created Visit`() {
        val currentVoyageStatus = createNewVoyageShipStatus(
            voyage = ongoingVoyage,
            previousVisit = finishedVisit
        )
        val newVisitId = "VISIT_3"
        val finishedVoyage = createNewVoyage(
            _id = voyageId,
            previous = visitId,
            next = newVisitId,
            start = voyageStart,
            end = voyageStart
        )
        val newOngoingVisit = createNewVisit(
            _id = newVisitId,
            previous = voyageId,
            start = voyageStart
        )
        val newVisitStatus = createNewVisitShipStatus(
            visit = newOngoingVisit,
            previousVoyage = finishedVoyage,
            previousVisit = finishedVisit
        )
        val eventProcessingResult = NewEventProcessingResult(
            status = newVisitStatus,
            changes = listOf(
                VoyageChange(Action.UPDATE, finishedVoyage),
                VisitChange(Action.CREATE, newOngoingVisit)
            ),
            readyForPostProcessing = listOf(finishedVoyage._id)
        )
        whenever(eventProcessingService.onEventAndBuffer(any<NewShipStatus>(), any(), any()))
            .thenReturn(eventProcessingResult)
        whenever(processingShipStatusService.getStatus(any()))
            .thenReturn(currentVoyageStatus)

        service.insertNew(testEvent, IMO_1)

        verify(postProcessingService, times(1)).schedulePostProcessing(eq(finishedVoyage._id))
    }

    @Test
    fun `should trigger post processing service on finishing a Visit with 0-second Voyage`() {
        val newVisitId = "VISIT_3"
        val newFinishedVoyage = createNewVoyage(
            _id = voyageId,
            previous = visitId,
            next = newVisitId,
            start = voyageStart,
            end = voyageStart
        )
        val newOngoingVisit = createNewVisit(
            _id = newVisitId,
            previous = voyageId,
            start = voyageStart
        )
        val newVisitStatus = createNewVisitShipStatus(
            visit = newOngoingVisit,
            previousVoyage = newFinishedVoyage,
            previousVisit = finishedVisit
        )
        val eventProcessingResult = NewEventProcessingResult(
            status = newVisitStatus,
            changes = listOf(
                VisitChange(Action.UPDATE, finishedVisit),
                VoyageChange(Action.CREATE, newFinishedVoyage),
                VisitChange(Action.CREATE, newOngoingVisit)
            ),
            readyForPostProcessing = listOf(finishedVisit._id)
        )
        whenever(eventProcessingService.onEventAndBuffer(any<NewShipStatus>(), any(), any()))
            .thenReturn(eventProcessingResult)
        whenever(processingShipStatusService.getStatus(any()))
            .thenReturn(currentVisitStatus)

        service.insertNew(testEvent, IMO_1)

        verify(postProcessingService, times(1)).schedulePostProcessing(eq(finishedVisit._id))
    }
}
