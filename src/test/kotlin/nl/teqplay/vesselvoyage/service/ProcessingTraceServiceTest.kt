package nl.teqplay.vesselvoyage.service

import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.argumentCaptor
import com.nhaarman.mockitokotlin2.eq
import com.nhaarman.mockitokotlin2.inOrder
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.never
import com.nhaarman.mockitokotlin2.reset
import com.nhaarman.mockitokotlin2.times
import com.nhaarman.mockitokotlin2.verify
import com.nhaarman.mockitokotlin2.whenever
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polyline
import nl.teqplay.vesselvoyage.datasource.NewTraceDataSource
import nl.teqplay.vesselvoyage.logic.DEFAULT_END_TIME
import nl.teqplay.vesselvoyage.logic.DEFAULT_START_TIME
import nl.teqplay.vesselvoyage.logic.IMO_1
import nl.teqplay.vesselvoyage.model.internal.TraceItem
import nl.teqplay.vesselvoyage.model.v2.NewTrace
import nl.teqplay.vesselvoyage.model.v2.Speed
import nl.teqplay.vesselvoyage.model.v2.TraceDistance
import nl.teqplay.vesselvoyage.model.v2.TraceStatistic
import nl.teqplay.vesselvoyage.properties.TraceProperties
import nl.teqplay.vesselvoyage.service.api.EntryV2Service
import nl.teqplay.vesselvoyage.service.trace.ProcessingTraceCacheService
import nl.teqplay.vesselvoyage.service.trace.ProcessingTraceService
import nl.teqplay.vesselvoyage.service.trace.TraceSimplifyService
import nl.teqplay.vesselvoyage.util.DEFAULT_TEST_VISIT_ID
import nl.teqplay.vesselvoyage.util.DEFAULT_TEST_VOYAGE_ID
import nl.teqplay.vesselvoyage.util.Timeline
import nl.teqplay.vesselvoyage.util.createAisHistoricMessage
import nl.teqplay.vesselvoyage.util.createLocationTime
import nl.teqplay.vesselvoyage.util.createNewVisit
import nl.teqplay.vesselvoyage.util.createNewVoyage
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Answers
import org.mockito.Spy
import java.time.Duration
import java.time.temporal.ChronoUnit

class ProcessingTraceServiceTest {
    @Spy
    private val dataSource = mock<NewTraceDataSource> {
        whenever(it.findById(any())).thenReturn(null)
    }

    private val simplifyService = TraceSimplifyService(
        traceProperties = mock<TraceProperties> {
            whenever(it.tolerance).thenReturn(0.0001)
        }
    )
    private val aisFetchingService = mock<AisFetchingService>()
    private val entryService = mock<EntryV2Service>()
    private val traceProperties = mock<TraceProperties>(defaultAnswer = Answers.RETURNS_DEEP_STUBS) {
        whenever(it.maxRequestLengthDays).thenReturn(90)
    }

    private var service = ProcessingTraceService(
        dataSource = dataSource,
        simplifyService = simplifyService,
        aisFetchingService = aisFetchingService,
        entryService = entryService,
        traceCacheService = ProcessingTraceCacheService(dataSource),
        traceProperties = traceProperties,
        slackMessageService = null,
    )

    private val TEST_IMO = IMO_1.toInt()
    private val TEST_VISIT_ID = "TEST_ONGOING_VISIT_ID"
    private val TEST_VOYAGE_ID = "TEST_PREVIOUS_VOYAGE_ID"
    private val TEST_VISIT = createNewVisit(_id = TEST_VISIT_ID, previous = TEST_VOYAGE_ID)
    private val timeline = Timeline()
    private val testTraceItem1 = TraceItem(Location(1.0, 0.0), 1.0f, timeline.generate().time, draught = null)
    private val testTraceItem2 = TraceItem(Location(2.0, 0.0), 2.5f, timeline.generate().time, draught = null)
    private val testTraceItem3 = TraceItem(Location(3.0, 2.0), 4.5f, timeline.generate().time, draught = null)
    private val testTraceItem4 = TraceItem(Location(3.0, 3.0), 5.0f, timeline.generate().time, draught = null)
    private val testTraceItems = listOf(
        testTraceItem1, testTraceItem2, testTraceItem3, testTraceItem4
    )

    @BeforeEach
    fun setUp() {
        reset(dataSource, aisFetchingService, entryService)
        service = ProcessingTraceService(
            dataSource = dataSource,
            simplifyService = simplifyService,
            aisFetchingService = aisFetchingService,
            entryService = entryService,
            // Refresh the cache each time so we start fresh
            traceCacheService = ProcessingTraceCacheService(dataSource),
            traceProperties = traceProperties,
            slackMessageService = null,
        )
    }

    @Test
    fun `should not create trace when inital ship status`() {
        val result = testTraceItems.map { testTraceItem ->
            service.insertIntoCurrentTrace(
                ongoingEntry = null,
                traceItem = testTraceItem
            )
        }.last()

        assertFalse(result)
    }

    @Test
    fun `should create new trace when missing ongoing trace`() {
        val result = testTraceItems.map { testTraceItem ->
            service.insertIntoCurrentTrace(
                ongoingEntry = TEST_VISIT,
                traceItem = testTraceItem
            )
        }.all { it }

        val expectedNewTrace = NewTrace(
            _id = TEST_VISIT_ID,
            polyline = Polyline(
                encoded = "_ibE?_ibE?_ibE_seK?_ibE",
                lastLocation = Location(3.0, 3.0)
            ),
            totalOngoingTraceItems = 4,
            simplifiedPolyline = null,
            speed = Speed(
                min = 1.0f,
                max = 5f,
                avg = 2.6666667f, // hmm, this is not very nice for a unit test
                count = 4,
                lastSpeedOverGround = 5.0f,
                lastSpeedOverGroundTimestamp = testTraceItem4.timestamp,
                duration = durationBetween(testTraceItem1, testTraceItem4)
            ),
            distance = TraceDistance(distanceMeters = 471210, lastLocation = Location(lat = 3.0, lon = 3.0)),
            draught = null
        )
        assertTrue(result)
        verify(dataSource, never()).deleteById(any())
        verify(dataSource, times(1)).insertOrUpdate(any())
        val captor = argumentCaptor { verify(dataSource, times(3)).replace(capture()) }

        assertEquals(expectedNewTrace, captor.lastValue)
    }

    @Test
    fun `should create new trace with simplifications deduping locations when missing ongoing trace`() {
        val longPolylineTraceItems = mutableListOf(testTraceItem1.copy(speedOverGround = 0.0f))
        repeat(97) {
            longPolylineTraceItems.add(
                testTraceItem2.copy(speedOverGround = 2.5f, timestamp = timeline.generate().time)
            )
        }
        longPolylineTraceItems.add(testTraceItem3.copy(speedOverGround = 0.0f, timestamp = timeline.generate().time))
        longPolylineTraceItems.add(testTraceItem4.copy(timestamp = timeline.generate().time))
        longPolylineTraceItems.add(testTraceItem4.copy(timestamp = timeline.generate().time))

        val result = longPolylineTraceItems.map { testTraceItem ->
            service.insertIntoCurrentTrace(
                ongoingEntry = TEST_VISIT,
                traceItem = testTraceItem
            )
        }.all { it }

        assertTrue(result)
        verify(dataSource, times(1)).insertOrUpdate(any())
        val captor = argumentCaptor<NewTrace> { verify(dataSource, times(100)).replace(capture()) }
        verify(dataSource, never()).deleteById(any())

        val trace = captor.lastValue
        val expectedTrace = NewTrace(
            _id = TEST_VISIT_ID,
            polyline = Polyline(encoded = "_}hQ_}hQ", lastLocation = Location(3.0, 3.0)),
            simplifiedPolyline = Polyline(encoded = "_ibE?_ibE?_ibE_seK?_ibE", lastLocation = Location(3.0, 3.0)),
            totalOngoingTraceItems = 1,
            speed = Speed(
                min = 0.0f,
                max = 5.0f,
                avg = 2.4029124f,
                count = 101,
                lastSpeedOverGround = 5.0f,
                lastSpeedOverGroundTimestamp = longPolylineTraceItems.last().timestamp,
                duration = durationBetween(longPolylineTraceItems.first(), longPolylineTraceItems.last())
            ),
            distance = TraceDistance(distanceMeters = 471210, lastLocation = longPolylineTraceItems.last().location),
            draught = null
        )

        assertEquals(expectedTrace, trace)
    }

    @Test
    fun `should create new trace when having no speed over ground`() {
        val result = testTraceItems.map { testTraceItem ->
            service.insertIntoCurrentTrace(
                ongoingEntry = TEST_VISIT,
                traceItem = testTraceItem
            )
        }.all { it }

        val expectedNewTrace = NewTrace(
            _id = TEST_VISIT_ID,
            polyline = Polyline(
                encoded = "_ibE?_ibE?_ibE_seK?_ibE",
                lastLocation = Location(3.0, 3.0)
            ),
            totalOngoingTraceItems = 4,
            simplifiedPolyline = null,
            speed = Speed(
                min = 1.0f,
                max = 5.0f,
                avg = 2.6666667f,
                count = 4,
                lastSpeedOverGround = 5.0f,
                duration = Duration.ofMinutes(3),
                lastSpeedOverGroundTimestamp = testTraceItem4.timestamp
            ),
            distance = TraceDistance(distanceMeters = 471210, lastLocation = Location(lat = 3.0, lon = 3.0)),
            draught = null
        )
        assertTrue(result)

        dataSource.inOrder {
            val argumentCaptor = argumentCaptor<NewTrace>()
            verify(dataSource).insertOrUpdate(any())
            verify(dataSource, times(3)).replace(argumentCaptor.capture())
            verifyNoMoreInteractions()

            assertEquals(expectedNewTrace, argumentCaptor.lastValue)
        }
    }

    @Test
    fun `should create new trace when having no speed over ground on first trace item`() {
        service.insertIntoCurrentTrace(
            ongoingEntry = TEST_VISIT,
            traceItem = testTraceItem1.copy(speedOverGround = null),
        )
        service.insertIntoCurrentTrace(
            ongoingEntry = TEST_VISIT,
            traceItem = testTraceItem2
        )
        service.insertIntoCurrentTrace(
            ongoingEntry = TEST_VISIT,
            traceItem = testTraceItem3
        )
        val result = service.insertIntoCurrentTrace(
            ongoingEntry = TEST_VISIT,
            traceItem = testTraceItem4
        )

        val expectedNewTrace = NewTrace(
            _id = TEST_VISIT_ID,
            polyline = Polyline(
                encoded = "_ibE?",
                lastLocation = Location(1.0, 0.0)
            ),
            totalOngoingTraceItems = 1,
            simplifiedPolyline = null,
            speed = null, // no first speed over ground, so no way to create a speed object!
            distance = TraceDistance(distanceMeters = 0, lastLocation = Location(lat = 1.0, lon = 0.0)),
            draught = null
        )
        assertTrue(result)
        verify(dataSource, times(3)).replace(any())
        verify(dataSource, never()).deleteById(any())

        // compare trace.speed separately, to correctly assert float precision
        // there was no first speed over ground, so no way to create a speed object!
        val captor = argumentCaptor<NewTrace> { verify(dataSource, times(1)).insertOrUpdate(capture()) }
        assertNull(captor.lastValue.speed)
        assertEquals(expectedNewTrace, captor.lastValue)
    }

    @Test
    fun `should update trace when having ongoing trace`() {
        // based on test trace item 1-4
        val ongoingTrace = NewTrace(
            _id = TEST_VISIT_ID,
            polyline = Polyline(
                encoded = "_ibE?_ibE?_ibE_seK?_ibE",
                lastLocation = Location(3.0, 3.0)
            ),
            totalOngoingTraceItems = 4,
            simplifiedPolyline = null,
            speed = Speed(
                min = 1.0f,
                max = 5.0f,
                avg = 3.625f,
                count = 4,
                lastSpeedOverGround = 5.0f,
                lastSpeedOverGroundTimestamp = testTraceItem4.timestamp,
                duration = durationBetween(testTraceItem1, testTraceItem4)
            ),
            distance = TraceDistance(distanceMeters = 471210, lastLocation = Location(lat = 3.0, lon = 3.0)),
            draught = null
        )

        whenever(dataSource.findById(eq(TEST_VISIT_ID))).thenReturn(ongoingTrace)

        val newTraceItem1 = TraceItem(
            Location(4.0, 3.0), speedOverGround = null, timeline.generate().time, draught = null
        )
        val newTraceItem2 = TraceItem(
            Location(4.0, 2.0), speedOverGround = 8.0f, timeline.generate().time, draught = null
        )
        val newTraceItem3 = TraceItem(
            Location(4.0, 1.0), speedOverGround = null, timeline.generate().time, draught = null
        )
        service.insertIntoCurrentTrace(
            ongoingEntry = TEST_VISIT,
            traceItem = newTraceItem1
        )
        service.insertIntoCurrentTrace(
            ongoingEntry = TEST_VISIT,
            traceItem = newTraceItem2
        )
        val result = service.insertIntoCurrentTrace(
            ongoingEntry = TEST_VISIT,
            traceItem = newTraceItem3
        )

        val expectedNewTrace = NewTrace(
            _id = TEST_VISIT_ID,
            polyline = Polyline(
                encoded = "_ibE?_ibE?_ibE_seK?_ibE_ibE??~hbE?~hbE",
                lastLocation = Location(4.0, 1.0)
            ),
            totalOngoingTraceItems = 7,
            simplifiedPolyline = null,
            speed = Speed(
                min = 1.0f,
                max = 8.0f,
                avg = 4.8125f,
                count = 7,
                lastSpeedOverGround = 8.0f,
                lastSpeedOverGroundTimestamp = newTraceItem3.timestamp,
                duration = Duration.between(testTraceItem1.timestamp, newTraceItem3.timestamp)
            ),
            distance = TraceDistance(distanceMeters = 804625, lastLocation = Location(lat = 4.0, lon = 1.0)),
            draught = null
        )
        assertTrue(result)
        verify(dataSource, never()).insertOrUpdate(any())
        verify(dataSource, never()).deleteById(any())

        // compare trace.speed separately, to correctly assert float precision
        val captor = argumentCaptor<NewTrace> { verify(dataSource, times(3)).replace(capture()) }
        assertEquals(expectedNewTrace, captor.lastValue)
    }

    @Test
    fun `should update and simplify trace when having more than 100 ongoing trace items`() {
        val ongoingTrace = NewTrace(
            _id = TEST_VISIT_ID,
            polyline = Polyline(
                encoded = "_ibE?_ibE?_ibE_seK?_ibE",
                lastLocation = Location(3.0, 3.0)
            ),
            totalOngoingTraceItems = 100,
            simplifiedPolyline = null,
            speed = Speed(
                min = 1.0f,
                max = 5.0f,
                avg = 3.25f,
                count = 100,
                lastSpeedOverGround = 3.25f,
                lastSpeedOverGroundTimestamp = testTraceItem4.timestamp
            ),
            distance = TraceDistance(distanceMeters = 471210, lastLocation = Location(lat = 3.0, lon = 3.0)),
            draught = null
        )

        whenever(dataSource.findById(eq(TEST_VISIT_ID))).thenReturn(ongoingTrace)

        // note that this item is not counted in the speed object, as speedOverGround is null
        val newTraceItem1 = TraceItem(
            location = Location(4.0, 3.0),
            speedOverGround = null,
            timestamp = testTraceItem4.timestamp,
            draught = null
        )
        val result = service.insertIntoCurrentTrace(
            ongoingEntry = TEST_VISIT,
            traceItem = newTraceItem1
        )

        val expectedNewTrace = NewTrace(
            _id = TEST_VISIT_ID,
            polyline = Polyline(
                encoded = "_glW_}hQ",
                lastLocation = Location(4.0, 3.0)
            ),
            totalOngoingTraceItems = 1,
            simplifiedPolyline = Polyline(
                encoded = "_ibE?_ibE?_ibE_seK?_ibE",
                lastLocation = Location(3.0, 3.0)
            ),
            speed = Speed(
                min = 1.0f,
                max = 5.0f,
                avg = 3.25f,
                count = 100, // note the inserted item is not counted in the speed object, as speedOverGround was null
                lastSpeedOverGround = 3.25f,
                lastSpeedOverGroundTimestamp = testTraceItem4.timestamp
            ),
            distance = TraceDistance(distanceMeters = 582529, lastLocation = Location(lat = 4.0, lon = 3.0)),
            draught = null
        )
        assertTrue(result)
        verify(dataSource, never()).insertOrUpdate(any())
        verify(dataSource, never()).deleteById(any())

        // compare trace.speed separately, to correctly assert float precision
        val captor = argumentCaptor<NewTrace> { verify(dataSource, times(1)).replace(capture()) }
        assertEquals(expectedNewTrace.copy(speed = null), captor.firstValue.copy(speed = null))
        assertSpeedEquals(expectedNewTrace.speed!!, captor.firstValue.speed!!, allowedSpeedDelta = 0.001F)
    }

    @Test
    fun `should merge traces of entries as expected`() {
        val voyageTrace = NewTrace(
            _id = TEST_VOYAGE_ID,
            polyline = Polyline(
                encoded = "_ibE?_ibE?_ibE_seK?_ibE",
                lastLocation = Location(3.0, 3.0)
            ),
            totalOngoingTraceItems = 4,
            simplifiedPolyline = null,
            speed = Speed(
                min = 1.0f,
                max = 5.0f,
                avg = 3.25f,
                count = 4,
                lastSpeedOverGround = 5.0f,
                lastSpeedOverGroundTimestamp = testTraceItem4.timestamp
            ),
            distance = TraceDistance(distanceMeters = 471210, lastLocation = Location(lat = 3.0, lon = 3.0)),
            draught = null
        )
        val visitTrace = NewTrace(
            _id = TEST_VISIT_ID,
            polyline = Polyline(
                encoded = "~hbE??~hbE?~hbE",
                lastLocation = Location(-1.0, -2.0)
            ),
            totalOngoingTraceItems = 4,
            simplifiedPolyline = null,
            speed = Speed(
                min = 6.0f,
                max = 9.0f,
                avg = 8.0f,
                count = 3,
                lastSpeedOverGround = 9.0f,
                lastSpeedOverGroundTimestamp = testTraceItem4.timestamp
            ),
            distance = TraceDistance(distanceMeters = 471210, lastLocation = Location(lat = 3.0, lon = 3.0)),
            draught = null
        )

        whenever(dataSource.findById(eq(TEST_VISIT_ID))).thenReturn(visitTrace)
        whenever(dataSource.findById(eq(TEST_VOYAGE_ID))).thenReturn(voyageTrace)

        service.mergeCanceledVisitTraceWithResumedVoyage(
            imo = TEST_IMO,
            canceledVisitId = TEST_VISIT_ID,
            resumedVoyageId = TEST_VOYAGE_ID
        )

        val expectedMergedTrace = NewTrace(
            _id = TEST_VOYAGE_ID,
            polyline = Polyline(
                encoded = "_ibE?_ibE?_ibE_seK?_ibE~flW~|hQ?~hbE?~hbE",
                lastLocation = Location(-1.0, -2.0)
            ),
            totalOngoingTraceItems = 4,
            simplifiedPolyline = null,
            speed = Speed(
                min = 1.0f,
                max = 9.0f,
                avg = 5.625f,
                count = 7,
                lastSpeedOverGround = 9.0f,
                lastSpeedOverGroundTimestamp = testTraceItem4.timestamp
            ),
            distance = TraceDistance(distanceMeters = 471210, lastLocation = Location(lat = 3.0, lon = 3.0)),
            draught = null
        )

        verify(dataSource, never()).insertOrUpdate(any())
        verify(dataSource, times(1)).deleteById(eq(TEST_VISIT_ID))

        // compare trace.speed separately, to correctly assert float precision
        val captor = argumentCaptor<NewTrace> { verify(dataSource, times(1)).replace(capture()) }
        assertEquals(expectedMergedTrace.copy(speed = null), captor.firstValue.copy(speed = null))
        assertSpeedEquals(expectedMergedTrace.speed!!, captor.firstValue.speed!!, allowedSpeedDelta = 0.001F)
    }

    @Test
    fun `should append location of previous entry when creating new trace for current`() {
        val voyageTrace = NewTrace(
            _id = TEST_VOYAGE_ID,
            polyline = Polyline(
                encoded = "_ibE?_ibE?_ibE_seK?_ibE",
                lastLocation = Location(3.0, 3.0)
            ),
            totalOngoingTraceItems = 4,
            simplifiedPolyline = null,
            speed = Speed(
                min = 1.0f,
                max = 5.0f,
                avg = 3.25f,
                count = 4,
                lastSpeedOverGround = 5.0f,
                lastSpeedOverGroundTimestamp = testTraceItem4.timestamp
            ),
            distance = TraceDistance(distanceMeters = 471210, lastLocation = Location(lat = 3.0, lon = 3.0)),
            draught = null
        )

        whenever(dataSource.findById(eq(TEST_VISIT_ID))).thenReturn(null)
        whenever(dataSource.findById(eq(TEST_VOYAGE_ID))).thenReturn(voyageTrace)

        val newTraceItem1 = TraceItem(Location(4.0, 3.0), 1.0f, testTraceItem4.timestamp, draught = null)
        val result = service.insertIntoCurrentTrace(
            ongoingEntry = TEST_VISIT,
            traceItem = newTraceItem1
        )

        val expectedNewTrace = NewTrace(
            _id = TEST_VISIT_ID,
            polyline = Polyline(
                encoded = "_}hQ_}hQ_ibE?",
                lastLocation = Location(4.0, 3.0)
            ),
            totalOngoingTraceItems = 2,
            simplifiedPolyline = null,
            speed = Speed(
                min = 1.0f,
                max = 1.0f,
                avg = 1.0f,
                count = 1,
                lastSpeedOverGround = 1.0f,
                lastSpeedOverGroundTimestamp = testTraceItem4.timestamp
            ),
            distance = TraceDistance(distanceMeters = 111319, lastLocation = Location(lat = 4.0, lon = 3.0)),
            draught = null
        )

        assert(result)
        verify(dataSource, never()).replace(any())
        verify(dataSource, never()).deleteById(any())

        // compare trace.speed separately, to correctly assert float precision
        val captor = argumentCaptor<NewTrace> { verify(dataSource, times(1)).insertOrUpdate(capture()) }
        assertEquals(expectedNewTrace.copy(speed = null), captor.firstValue.copy(speed = null))
        assertSpeedEquals(expectedNewTrace.speed!!, captor.firstValue.speed!!, allowedSpeedDelta = 0.0001F)
    }

    @Test
    fun `should generate trace of scheduled vessel for visit without previous entry`() {
        val startTime = DEFAULT_START_TIME.toInstant()
        val endTime = DEFAULT_END_TIME.toInstant()
        val endLocation = Location(2.0, 2.0)
        val finishedVisit = createNewVisit(
            previous = null,
            start = createLocationTime(time = startTime),
            end = createLocationTime(location = endLocation, time = endTime)
        )
        val traceLocation = Location(1.0, 1.0)

        whenever(entryService.findEntry(eq(DEFAULT_TEST_VISIT_ID))).thenReturn(finishedVisit)
        whenever(dataSource.exists(eq(DEFAULT_TEST_VISIT_ID))).thenReturn(false)
        whenever(aisFetchingService.getShipTrace(eq(IMO_1.toString()), eq(DEFAULT_START_TIME), eq(DEFAULT_END_TIME)))
            .thenReturn(
                listOf(
                    createAisHistoricMessage(
                        messageTime = startTime,
                        location = traceLocation,
                        draught = 1.0f
                    )
                )
            )

        val input = listOf(finishedVisit._id)
        service.scheduleTraceCalculation(entryIds = input, force = false)
        service.generateTraces()

        val expectedDuration = Duration.between(startTime, endTime)
        val expectedTrace = NewTrace(
            _id = DEFAULT_TEST_VISIT_ID,
            polyline = Polyline.of(
                listOf(
                    traceLocation,
                    endLocation
                )
            ),
            totalOngoingTraceItems = 2,
            simplifiedPolyline = null,
            speed = Speed(
                min = 15.0f,
                max = 15.0f,
                avg = 15.0f,
                count = 2,
                lastSpeedOverGround = 15.0f,
                duration = expectedDuration,
                lastSpeedOverGroundTimestamp = endTime
            ),
            distance = TraceDistance(
                distanceMeters = 157401,
                lastLocation = Location(lat = 2.0, lon = 2.0)
            ),
            draught = TraceStatistic(
                min = 1.0f,
                max = 1.0f,
                avg = 1.0f,
                last = 1.0f,
                duration = expectedDuration,
                lastTimestamp = endTime
            )
        )
        verify(dataSource, never()).findById(any())
        verify(dataSource).exists(eq(DEFAULT_TEST_VISIT_ID))
        verify(dataSource).insertOrUpdate(eq(expectedTrace))
    }

    @Test
    fun `should generate trace of scheduled vessel for voyage with previous visit`() {
        val previusVisitEndLocation = Location(-2.0, -2.0)
        val startTime = DEFAULT_START_TIME.toInstant()
        val endTime = DEFAULT_END_TIME.toInstant()
        val endLocation = Location(2.0, 2.0)
        val previousVisit = createNewVisit(
            end = createLocationTime(location = previusVisitEndLocation, time = startTime)
        )
        val previousVisitTrace = NewTrace(
            _id = DEFAULT_TEST_VISIT_ID,
            polyline = Polyline.of(listOf(previusVisitEndLocation)),
            totalOngoingTraceItems = 1,
            simplifiedPolyline = null,
            speed = Speed(
                min = 10.0f,
                max = 10.0f,
                avg = 10.0f,
                count = 1,
                lastSpeedOverGround = 10.0f,
                duration = Duration.ZERO,
                lastSpeedOverGroundTimestamp = startTime
            ),
            distance = null,
            draught = TraceStatistic(
                min = 2.0f,
                max = 2.0f,
                avg = 2.0f,
                last = 2.0f,
                duration = Duration.ZERO,
                lastTimestamp = startTime
            )
        )

        val finishedVoyage = createNewVoyage(
            previous = previousVisit._id,
            start = createLocationTime(time = startTime),
            end = createLocationTime(location = endLocation, time = endTime)
        )
        val traceLocation = Location(1.0, 1.0)

        whenever(dataSource.findById(eq(DEFAULT_TEST_VISIT_ID))).thenReturn(previousVisitTrace)
        whenever(entryService.findEntry(eq(DEFAULT_TEST_VOYAGE_ID))).thenReturn(finishedVoyage)
        whenever(dataSource.exists(eq(DEFAULT_TEST_VOYAGE_ID))).thenReturn(false)
        whenever(aisFetchingService.getShipTrace(eq(IMO_1.toString()), eq(DEFAULT_START_TIME), eq(DEFAULT_END_TIME)))
            .thenReturn(
                listOf(
                    createAisHistoricMessage(
                        messageTime = startTime,
                        location = traceLocation,
                        draught = 1.0f
                    )
                )
            )

        val input = listOf(finishedVoyage._id)
        service.scheduleTraceCalculation(entryIds = input, force = false)
        service.generateTraces()

        val expectedDuration = Duration.between(startTime, endTime)
        val expectedTrace = NewTrace(
            _id = DEFAULT_TEST_VOYAGE_ID,
            polyline = Polyline.of(
                listOf(
                    previusVisitEndLocation,
                    traceLocation,
                    endLocation
                )
            ),
            totalOngoingTraceItems = 3,
            simplifiedPolyline = null,
            speed = Speed(
                min = 15.0f,
                max = 15.0f,
                avg = 15.0f,
                count = 2,
                lastSpeedOverGround = 15.0f,
                duration = expectedDuration,
                lastSpeedOverGroundTimestamp = endTime
            ),
            distance = TraceDistance(
                distanceMeters = 629653,
                lastLocation = Location(lat = 2.0, lon = 2.0)
            ),
            draught = TraceStatistic(
                min = 1.0f,
                max = 1.0f,
                avg = 1.0f,
                last = 1.0f,
                duration = expectedDuration,
                lastTimestamp = endTime
            )
        )
        verify(dataSource).findById(eq(DEFAULT_TEST_VISIT_ID))
        verify(dataSource).exists(eq(DEFAULT_TEST_VOYAGE_ID))
        verify(dataSource).insertOrUpdate(eq(expectedTrace))
    }

    @Test
    fun `should update previous trace when AIS point is before previous entry end time`() {
        val previousEndTime = DEFAULT_END_TIME.toInstant()
        val traceItemTime = DEFAULT_START_TIME.toInstant()
        val lastTraceItemTime = traceItemTime.minus(1, ChronoUnit.DAYS)
        val ongoingVisit = createNewVisit(
            _id = TEST_VISIT_ID,
            previous = TEST_VOYAGE_ID
        )
        val previousVoyage = createNewVoyage(
            _id = TEST_VOYAGE_ID,
            next = TEST_VISIT_ID,
            end = createLocationTime(time = previousEndTime)
        )
        val previousVoyageEndLocation = Location(-2.0, -2.0)
        val previousVoyageTrace = NewTrace(
            _id = TEST_VOYAGE_ID,
            polyline = Polyline.of(listOf(previousVoyageEndLocation)),
            totalOngoingTraceItems = 1,
            simplifiedPolyline = null,
            speed = Speed(
                min = 10.0f,
                max = 10.0f,
                avg = 10.0f,
                count = 1,
                lastSpeedOverGround = 10.0f,
                duration = Duration.ZERO,
                lastSpeedOverGroundTimestamp = lastTraceItemTime
            ),
            distance = null,
            draught = TraceStatistic(
                min = 2.0f,
                max = 2.0f,
                avg = 2.0f,
                last = 2.0f,
                duration = Duration.ZERO,
                lastTimestamp = lastTraceItemTime
            )
        )
        val newTraceItem = TraceItem(
            location = Location(1.0, 1.0),
            speedOverGround = 5.0f,
            timestamp = traceItemTime,
            draught = 1.0f
        )

        whenever(entryService.findEntry(eq(TEST_VOYAGE_ID))).thenReturn(previousVoyage)
        whenever(dataSource.findById(eq(TEST_VOYAGE_ID))).thenReturn(previousVoyageTrace)

        val result = service.insertIntoCurrentTrace(
            ongoingEntry = ongoingVisit,
            traceItem = newTraceItem
        )

        val expectedDuration = Duration.between(lastTraceItemTime, traceItemTime)
        val expectedTrace = NewTrace(
            _id = TEST_VOYAGE_ID,
            polyline = Polyline.of(
                listOf(
                    previousVoyageEndLocation,
                    newTraceItem.location
                )
            ),
            totalOngoingTraceItems = 2,
            simplifiedPolyline = null,
            speed = Speed(
                min = 5.0f,
                max = 10.0f,
                avg = 10.0f,
                count = 2,
                lastSpeedOverGround = 5.0f,
                duration = expectedDuration,
                lastSpeedOverGroundTimestamp = traceItemTime
            ),
            distance = TraceDistance(
                distanceMeters = 0,
                lastLocation = Location(lat = 1.0, lon = 1.0)
            ),
            draught = TraceStatistic(
                min = 1.0f,
                max = 2.0f,
                avg = 2.0f,
                last = 1.0f,
                duration = expectedDuration,
                lastTimestamp = traceItemTime
            )
        )

        assertTrue(result)
        verify(dataSource).findById(eq(TEST_VOYAGE_ID))
        verify(dataSource).replace(eq(expectedTrace))
    }

    private fun durationBetween(a: TraceItem, b: TraceItem): Duration {
        return Duration.between(a.timestamp, b.timestamp)
    }

    private fun assertSpeedEquals(expected: Speed, actual: Speed, allowedSpeedDelta: Float) {
        assertEquals(expected.min, actual.min, allowedSpeedDelta, "min")
        assertEquals(expected.max, actual.max, allowedSpeedDelta, "max")
        assertEquals(expected.avg, actual.avg, allowedSpeedDelta, "avg")
        assertEquals(expected.lastSpeedOverGround, actual.lastSpeedOverGround, allowedSpeedDelta, "lastSpeedOverGround")
        assertEquals(expected.count, actual.count, "count")
        assertEquals(
            expected.lastSpeedOverGroundTimestamp,
            actual.lastSpeedOverGroundTimestamp,
            "lastSpeedOverGroundTimestamp"
        )
        assertEquals(expected.duration, actual.duration, "duration")
    }
}
