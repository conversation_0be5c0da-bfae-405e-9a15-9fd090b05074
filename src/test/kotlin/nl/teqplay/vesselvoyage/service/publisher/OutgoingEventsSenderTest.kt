package nl.teqplay.vesselvoyage.service.publisher

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.whenever
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.ChangeMetadata
import nl.teqplay.vesselvoyage.model.OutgoingChange
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.properties.EventPublishingProperties
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import java.io.IOException

class OutgoingEventsSenderTest {

    private val properties = mock<EventPublishingProperties>().apply {
        val rabbitMqMock = EventPublishingProperties.RabbitMQ(
            enabled = true,
            exchange = "TEST",
            v1Exchange = "TEST",
            uri = "TEST"
        )

        whenever(this.rabbitMq).thenReturn(rabbitMqMock)
    }
    private val objectMapper = jacksonObjectMapper()
    private val rabbitMqEventsSender = mock<RabbitMqEventSender>().apply {
        whenever(this.send(any(), any(), any()))
            // Our RabbitMQ sender doesn't annotate that it can throw IOExceptions
            // Doing it via a thenAnswer as a workaround
            .thenAnswer { IOException() }
    }
    private val outgoingEventsSender = OutgoingEventsSender(
        properties = properties,
        objectMapper = objectMapper,
        rabbitMqEventSender = rabbitMqEventsSender
    )

    @Test
    fun `should catch IOException when it can't connect to RabbitMQ`() {
        val change = OutgoingChange(
            action = mock<Action>(),
            entry = mock<Visit>(),
            metadata = mock<ChangeMetadata>()
        )

        assertDoesNotThrow { outgoingEventsSender.send(change) }
    }
}
