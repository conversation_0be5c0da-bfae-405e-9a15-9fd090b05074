package nl.teqplay.vesselvoyage.service.publisher

import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.anyOrNull
import com.nhaarman.mockitokotlin2.argumentCaptor
import com.nhaarman.mockitokotlin2.eq
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.times
import com.nhaarman.mockitokotlin2.verify
import com.nhaarman.mockitokotlin2.whenever
import nl.teqplay.skeleton.nats.NatsClientMock
import nl.teqplay.skeleton.nats.queue
import nl.teqplay.vesselvoyage.ApplicationTestConfig
import nl.teqplay.vesselvoyage.apiv2.model.OutgoingChange
import nl.teqplay.vesselvoyage.apiv2.model.OutgoingEntryChange
import nl.teqplay.vesselvoyage.apiv2.model.OutgoingSofChange
import nl.teqplay.vesselvoyage.apiv2.model.sof.StatementOfFactsViewName.PORTREPORTER
import nl.teqplay.vesselvoyage.apiv2.model.sof.StatementOfFactsViewName.PTO
import nl.teqplay.vesselvoyage.config.NatsConfiguration
import nl.teqplay.vesselvoyage.mapper.EntryV2Mapper
import nl.teqplay.vesselvoyage.model.Action.CREATE
import nl.teqplay.vesselvoyage.model.Action.DELETE
import nl.teqplay.vesselvoyage.model.Action.UPDATE
import nl.teqplay.vesselvoyage.model.v2.ESoFChange
import nl.teqplay.vesselvoyage.model.v2.NewChange
import nl.teqplay.vesselvoyage.model.v2.NewESoF
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.VisitChange
import nl.teqplay.vesselvoyage.model.v2.VoyageChange
import nl.teqplay.vesselvoyage.service.api.EsofV2Service
import nl.teqplay.vesselvoyage.service.api.PortReporterStatementOfFactsViewGenerator
import nl.teqplay.vesselvoyage.service.api.PtoStatementOfFactsViewGenerator
import nl.teqplay.vesselvoyage.util.createNewESoF
import nl.teqplay.vesselvoyage.util.createNewVisit
import nl.teqplay.vesselvoyage.util.createNewVisitShipStatus
import nl.teqplay.vesselvoyage.util.createNewVoyage
import nl.teqplay.vesselvoyage.util.createNewVoyageShipStatus
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.TestConstructor
import org.springframework.test.context.bean.override.mockito.MockitoBean
import java.util.stream.Stream

@SpringBootTest
@ActiveProfiles("processing")
@Import(ApplicationTestConfig::class)
@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@MockitoBean(types = [RabbitMqOutgoingChangeSender::class])
class ChangesPublisherServiceTest(
    private val service: ChangesPublisherService,
    private val natsClientMock: NatsClientMock,
    private val rabbitMqEventSender: RabbitMqOutgoingChangeSender,
    private val entryV2Mapper: EntryV2Mapper,
    private val ptoSofGenerator: PtoStatementOfFactsViewGenerator,
    private val prpSofGenerator: PortReporterStatementOfFactsViewGenerator
) {
    private val testVisit = createNewVisit()
    private val testVisitEsof = createNewESoF(testVisit._id)
    private val testVoyage = createNewVoyage()
    private val testVoyageEsof = createNewESoF(testVoyage._id)
    private val esofV2Service: EsofV2Service = mock()
    init {
        whenever(esofV2Service.produce(eq(PTO), any<NewVisit>(), anyOrNull<NewESoF>())).thenAnswer { invocation ->
            ptoSofGenerator.generate(
                visit = invocation.getArgument(1),
                esof = invocation.getArgument(2),
                previousPortAreaId = null
            )
        }
        whenever(esofV2Service.produce(eq(PORTREPORTER), any<NewVisit>(), anyOrNull<NewESoF>())).thenAnswer { inv ->
            prpSofGenerator.generate(
                visit = inv.getArgument(1),
                esof = inv.getArgument(2)
            )
        }
    }
    data class TestCase(
        val oldStatus: NewShipStatus,
        val updatedStatus: NewShipStatus,
        val internalChanges: List<NewChange<*>>,
        val expected: List<Expect>
    )
    data class Expect(
        val nats: String,
        val rabbit: String,
        val change: OutgoingChange<*>
    )
    private fun testCases(): Stream<Arguments> {

        return listOf(
            // / Visit Cases
            // 1. Creating Visit when coming from an initial ship state
            TestCase(
                oldStatus = NewInitialShipStatus(),
                updatedStatus = createNewVisitShipStatus(visit = testVisit, visitEsof = testVisitEsof),
                internalChanges = listOf(
                    VisitChange(CREATE, testVisit),
                    ESoFChange(CREATE, testVisitEsof)
                ),
                expected = listOf(
                    Expect(
                        nats = "vessel-voyage.outgoing-changes.entry.TEST_VISIT_ID",
                        rabbit = "VISIT.1111111.CREATE",
                        change = OutgoingEntryChange(CREATE, entryV2Mapper.toApi(testVisit))
                    ),
                    Expect(
                        nats = "vessel-voyage.outgoing-changes.sof.TEST_VISIT_ID",
                        rabbit = "SOFVIEW.PTO.1111111.CREATE",
                        change = OutgoingSofChange(
                            CREATE,
                            esofV2Service.produce(PTO, testVisit, testVisitEsof)
                        )
                    ),
                    Expect(
                        nats = "vessel-voyage.outgoing-changes.sof.TEST_VISIT_ID",
                        rabbit = "SOFVIEW.PORTREPORTER.1111111.CREATE",
                        change = OutgoingSofChange(
                            CREATE,
                            esofV2Service.produce(PORTREPORTER, testVisit, testVisitEsof)
                        )
                    )
                )
            ),

            // 2. Updating current Visit
            TestCase(
                oldStatus = createNewVisitShipStatus(visit = testVisit, visitEsof = null),
                updatedStatus = createNewVisitShipStatus(visit = testVisit, visitEsof = testVisitEsof),
                internalChanges = listOf(
                    VisitChange(UPDATE, testVisit),
                    ESoFChange(CREATE, testVisitEsof)
                ),
                expected = listOf(
                    Expect(
                        nats = "vessel-voyage.outgoing-changes.entry.TEST_VISIT_ID",
                        rabbit = "VISIT.1111111.UPDATE",
                        change = OutgoingEntryChange(UPDATE, entryV2Mapper.toApi(testVisit))
                    ),
                    Expect(
                        nats = "vessel-voyage.outgoing-changes.sof.TEST_VISIT_ID",
                        rabbit = "SOFVIEW.PTO.1111111.UPDATE",
                        change = OutgoingSofChange(UPDATE, esofV2Service.produce(PTO, testVisit, testVisitEsof))
                    ),
                    Expect(
                        nats = "vessel-voyage.outgoing-changes.sof.TEST_VISIT_ID",
                        rabbit = "SOFVIEW.PORTREPORTER.1111111.UPDATE",
                        change = OutgoingSofChange(UPDATE, esofV2Service.produce(PORTREPORTER, testVisit, testVisitEsof))
                    )
                )
            ),

            // 3. Deleting current Visit
            TestCase(
                oldStatus = createNewVisitShipStatus(visit = testVisit, visitEsof = testVisitEsof),
                updatedStatus = NewInitialShipStatus(),
                internalChanges = listOf(
                    VisitChange(DELETE, testVisit),
                    ESoFChange(DELETE, testVisitEsof)
                ),
                expected = listOf(
                    Expect(
                        nats = "vessel-voyage.outgoing-changes.entry.TEST_VISIT_ID",
                        rabbit = "VISIT.1111111.DELETE",
                        change = OutgoingEntryChange(DELETE, entryV2Mapper.toApi(testVisit))
                    ),
                    Expect(
                        nats = "vessel-voyage.outgoing-changes.sof.TEST_VISIT_ID",
                        rabbit = "SOFVIEW.PTO.1111111.DELETE",
                        change = OutgoingSofChange(DELETE, esofV2Service.produce(PTO, testVisit, testVisitEsof))
                    ),
                    Expect(
                        nats = "vessel-voyage.outgoing-changes.sof.TEST_VISIT_ID",
                        rabbit = "SOFVIEW.PORTREPORTER.1111111.DELETE",
                        change = OutgoingSofChange(DELETE, esofV2Service.produce(PORTREPORTER, testVisit, testVisitEsof))
                    )
                )
            ),

            // 4. Only the esof changed of the existing visit
            TestCase(
                oldStatus = createNewVisitShipStatus(visit = testVisit, visitEsof = null),
                updatedStatus = createNewVisitShipStatus(visit = testVisit, visitEsof = testVisitEsof),
                internalChanges = listOf(
                    ESoFChange(CREATE, testVisitEsof)
                ),
                expected = listOf(
                    Expect(
                        nats = "vessel-voyage.outgoing-changes.sof.TEST_VISIT_ID",
                        rabbit = "SOFVIEW.PTO.1111111.UPDATE",
                        change = OutgoingSofChange(UPDATE, esofV2Service.produce(PTO, testVisit, testVisitEsof))
                    ),
                    Expect(
                        nats = "vessel-voyage.outgoing-changes.sof.TEST_VISIT_ID",
                        rabbit = "SOFVIEW.PORTREPORTER.1111111.UPDATE",
                        change = OutgoingSofChange(UPDATE, esofV2Service.produce(PORTREPORTER, testVisit, testVisitEsof))
                    )
                )
            ),

            // / Voyage Cases
            // 5. Updating current Voyage
            TestCase(
                oldStatus = createNewVoyageShipStatus(voyage = testVoyage, voyageEsof = null),
                updatedStatus = createNewVoyageShipStatus(voyage = testVoyage, voyageEsof = testVoyageEsof),
                internalChanges = listOf(
                    VoyageChange(UPDATE, testVoyage),
                    ESoFChange(CREATE, testVoyageEsof)
                ),
                expected = listOf(
                    Expect(
                        nats = "vessel-voyage.outgoing-changes.entry.TEST_VOYAGE_ID",
                        rabbit = "VOYAGE.1111111.UPDATE",
                        change = OutgoingEntryChange(UPDATE, entryV2Mapper.toApi(testVoyage))
                    )
                )
            ),

            // 6. Deleting current Voyage
            TestCase(
                oldStatus = createNewVoyageShipStatus(
                    voyage = testVoyage,
                    voyageEsof = testVoyageEsof,
                    previousVisit = testVisit,
                    previousVisitEsof = testVisitEsof
                ),
                updatedStatus = createNewVisitShipStatus(visit = testVisit, visitEsof = testVisitEsof),
                internalChanges = listOf(
                    VoyageChange(DELETE, testVoyage),
                    ESoFChange(DELETE, testVoyageEsof)
                ),
                expected = listOf(
                    Expect(
                        nats = "vessel-voyage.outgoing-changes.entry.TEST_VOYAGE_ID",
                        rabbit = "VOYAGE.1111111.DELETE",
                        change = OutgoingEntryChange(DELETE, entryV2Mapper.toApi(testVoyage))
                    )
                )
            ),

            // 7. We don't have any changes to publish when we only update the esof of a Voyage
            TestCase(
                oldStatus = createNewVoyageShipStatus(voyage = testVoyage, voyageEsof = null),
                updatedStatus = createNewVoyageShipStatus(voyage = testVoyage, voyageEsof = testVoyageEsof),
                internalChanges = listOf(
                    ESoFChange(CREATE, testVoyageEsof)
                ),
                expected = emptyList()
            ),

            // / Combined test cases

            // 8. Creating new Voyage when we were before in Visit
            TestCase(
                oldStatus = createNewVisitShipStatus(
                    visit = testVisit,
                    visitEsof = testVisitEsof,
                ),
                updatedStatus = createNewVoyageShipStatus(
                    voyage = testVoyage,
                    voyageEsof = testVoyageEsof,
                    previousVisit = testVisit,
                    previousVisitEsof = testVisitEsof
                ),
                internalChanges = listOf(
                    VoyageChange(CREATE, testVoyage),
                    ESoFChange(CREATE, testVoyageEsof),
                    VisitChange(UPDATE, testVisit),
                    ESoFChange(UPDATE, testVisitEsof)
                ),
                expected = listOf(
                    Expect(
                        nats = "vessel-voyage.outgoing-changes.entry.TEST_VOYAGE_ID",
                        rabbit = "VOYAGE.1111111.CREATE",
                        change = OutgoingEntryChange(CREATE, entryV2Mapper.toApi(testVoyage))
                    ),
                    Expect(
                        nats = "vessel-voyage.outgoing-changes.entry.TEST_VISIT_ID",
                        rabbit = "VISIT.1111111.UPDATE",
                        change = OutgoingEntryChange(UPDATE, entryV2Mapper.toApi(testVisit))
                    ),
                    Expect(
                        nats = "vessel-voyage.outgoing-changes.sof.TEST_VISIT_ID",
                        rabbit = "SOFVIEW.PTO.1111111.UPDATE",
                        change = OutgoingSofChange(UPDATE, esofV2Service.produce(PTO, testVisit, testVisitEsof))
                    ),
                    Expect(
                        nats = "vessel-voyage.outgoing-changes.sof.TEST_VISIT_ID",
                        rabbit = "SOFVIEW.PORTREPORTER.1111111.UPDATE",
                        change = OutgoingSofChange(UPDATE, esofV2Service.produce(PORTREPORTER, testVisit, testVisitEsof))
                    )
                )
            ),

            // 9. We were in Voyage status but changed to a Visit
            TestCase(
                oldStatus = createNewVoyageShipStatus(
                    voyage = testVoyage,
                    voyageEsof = testVoyageEsof
                ),
                updatedStatus = createNewVisitShipStatus(
                    visit = testVisit,
                    visitEsof = testVisitEsof,
                    previousVoyage = testVoyage,
                    previousVoyageEsof = testVoyageEsof
                ),
                internalChanges = listOf(
                    VisitChange(CREATE, testVisit),
                    ESoFChange(CREATE, testVisitEsof),
                    VoyageChange(UPDATE, testVoyage),
                    ESoFChange(UPDATE, testVoyageEsof)
                ),
                expected = listOf(
                    Expect(
                        nats = "vessel-voyage.outgoing-changes.entry.TEST_VISIT_ID",
                        rabbit = "VISIT.1111111.CREATE",
                        change = OutgoingEntryChange(CREATE, entryV2Mapper.toApi(testVisit))
                    ),
                    Expect(
                        nats = "vessel-voyage.outgoing-changes.sof.TEST_VISIT_ID",
                        rabbit = "SOFVIEW.PTO.1111111.CREATE",
                        change = OutgoingSofChange(CREATE, esofV2Service.produce(PTO, testVisit, testVisitEsof))
                    ),
                    Expect(
                        nats = "vessel-voyage.outgoing-changes.sof.TEST_VISIT_ID",
                        rabbit = "SOFVIEW.PORTREPORTER.1111111.CREATE",
                        change = OutgoingSofChange(CREATE, esofV2Service.produce(PORTREPORTER, testVisit, testVisitEsof))
                    ),
                    Expect(
                        nats = "vessel-voyage.outgoing-changes.entry.TEST_VOYAGE_ID",
                        rabbit = "VOYAGE.1111111.UPDATE",
                        change = OutgoingEntryChange(UPDATE, entryV2Mapper.toApi(testVoyage))
                    )
                )
            ),

            // 10. Visit is canceled, going back into previous Voyage
            TestCase(
                oldStatus = createNewVisitShipStatus(
                    visit = testVisit,
                    visitEsof = testVisitEsof,
                    previousVoyage = testVoyage,
                    previousVoyageEsof = testVoyageEsof
                ),
                updatedStatus = createNewVoyageShipStatus(
                    voyage = testVoyage,
                    voyageEsof = testVoyageEsof
                ),
                internalChanges = listOf(
                    VisitChange(DELETE, testVisit),
                    ESoFChange(DELETE, testVisitEsof),
                    VoyageChange(UPDATE, testVoyage),
                    ESoFChange(UPDATE, testVoyageEsof)
                ),
                expected = listOf(
                    Expect(
                        nats = "vessel-voyage.outgoing-changes.entry.TEST_VISIT_ID",
                        rabbit = "VISIT.1111111.DELETE",
                        change = OutgoingEntryChange(
                            DELETE, entryV2Mapper.toApi(testVisit)
                        )
                    ),
                    Expect(
                        nats = "vessel-voyage.outgoing-changes.sof.TEST_VISIT_ID",
                        rabbit = "SOFVIEW.PTO.1111111.DELETE",
                        change = OutgoingSofChange(DELETE, esofV2Service.produce(PTO, testVisit, testVisitEsof))
                    ),
                    Expect(
                        nats = "vessel-voyage.outgoing-changes.sof.TEST_VISIT_ID",
                        rabbit = "SOFVIEW.PORTREPORTER.1111111.DELETE",
                        change = OutgoingSofChange(DELETE, esofV2Service.produce(PORTREPORTER, testVisit, testVisitEsof))
                    ),
                    Expect(
                        nats = "vessel-voyage.outgoing-changes.entry.TEST_VOYAGE_ID",
                        rabbit = "VOYAGE.1111111.UPDATE",
                        change = OutgoingEntryChange(UPDATE, entryV2Mapper.toApi(testVoyage))
                    )
                )
            ),
        ).map { Arguments.of(it) }.stream()
    }

    @ParameterizedTest
    @MethodSource("testCases")
    fun `should publish outgoing nats changes`(testCase: TestCase) {
        val (
            oldStatus: NewShipStatus,
            updatedStatus: NewShipStatus,
            internalChanges: List<NewChange<*>>,
            expected: List<Expect>
        ) = testCase

        val outgoingChangesQueue = natsClientMock.queue<OutgoingChange<*>>(NatsConfiguration.OUTGOING_CHANGES_STREAM)

        service.tryPublishNewChanges(
            oldStatus = oldStatus,
            updatedStatus = updatedStatus,
            changes = internalChanges
        )

        val result: List<Pair<String, OutgoingChange<*>>> = outgoingChangesQueue.toList()
        val expectedList: List<Pair<String, OutgoingChange<*>>> = expected.map { exp -> exp.nats to exp.change }

        // Remove any items from the queue for any test cases after this one
        outgoingChangesQueue.clear()

        assertThat(result)
            // The StatementOfFacts views have a generation ID that can't be compared
            .usingRecursiveFieldByFieldElementComparatorIgnoringFields("second.value.generationId")
            .isEqualTo(expectedList)
    }

    @ParameterizedTest
    @MethodSource("testCases")
    fun `should publish outgoing rabbitmq changes`(testCase: TestCase) {
        val (
            oldStatus: NewShipStatus,
            updatedStatus: NewShipStatus,
            internalChanges: List<NewChange<*>>,
            expected: List<Expect>
        ) = testCase

        val changeCaptor = argumentCaptor<OutgoingChange<*>>()
        val routingKeyCaptor = argumentCaptor<String>()

        service.tryPublishNewChanges(
            oldStatus = oldStatus,
            updatedStatus = updatedStatus,
            changes = internalChanges
        )

        verify(rabbitMqEventSender, times(expected.size)).send(any(), changeCaptor.capture(), routingKeyCaptor.capture())
        assertTrue(changeCaptor.allValues.size == routingKeyCaptor.allValues.size)

        val result: List<Pair<String, OutgoingChange<*>>> = routingKeyCaptor.allValues.zip(changeCaptor.allValues)
        val expectedList: List<Pair<String, OutgoingChange<*>>> = expected.map { exp -> exp.rabbit to exp.change }

        assertThat(result)
            // The StatementOfFacts views have a generation ID that can't be compared
            .usingRecursiveFieldByFieldElementComparatorIgnoringFields("second.value.generationId")
            .isEqualTo(expectedList)
    }
}
