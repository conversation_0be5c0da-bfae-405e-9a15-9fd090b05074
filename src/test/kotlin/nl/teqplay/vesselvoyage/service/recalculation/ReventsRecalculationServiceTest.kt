package nl.teqplay.vesselvoyage.service.recalculation

import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.argumentCaptor
import com.nhaarman.mockitokotlin2.atLeastOnce
import com.nhaarman.mockitokotlin2.eq
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.never
import com.nhaarman.mockitokotlin2.reset
import com.nhaarman.mockitokotlin2.times
import com.nhaarman.mockitokotlin2.verify
import com.nhaarman.mockitokotlin2.whenever
import nl.teqplay.aisengine.revents.client.ReventsClient
import nl.teqplay.aisengine.revents.client.ReventsVesselVoyageMergingClient
import nl.teqplay.aisengine.revents.client.ReventsVesselVoyageMergingV2Client
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioCreateRequest
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioGuarantee
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioResponse
import nl.teqplay.aisengine.reventsengine.model.vesselvoyage.MergeEntry
import nl.teqplay.aisengine.reventsengine.model.vesselvoyage.MergeEntryV2
import nl.teqplay.skeleton.common.exception.BadRequestException
import nl.teqplay.skeleton.common.exception.NotFoundException
import nl.teqplay.skeleton.model.TimeWindow
import nl.teqplay.vesselvoyage.datasource.AutomaticRecalculationShipDataSource
import nl.teqplay.vesselvoyage.datasource.NewESoFDataSource
import nl.teqplay.vesselvoyage.datasource.NewVisitDataSource
import nl.teqplay.vesselvoyage.datasource.NewVoyageDataSource
import nl.teqplay.vesselvoyage.datasource.ReventsRecalculationsDataSource
import nl.teqplay.vesselvoyage.datasource.VisitDataSource
import nl.teqplay.vesselvoyage.datasource.VoyageDataSource
import nl.teqplay.vesselvoyage.logic.PORT_NLRTM
import nl.teqplay.vesselvoyage.logic.createPortAreaVisit
import nl.teqplay.vesselvoyage.logic.createVisit
import nl.teqplay.vesselvoyage.logic.createVoyage
import nl.teqplay.vesselvoyage.model.Entry
import nl.teqplay.vesselvoyage.model.RecalculationPortResult
import nl.teqplay.vesselvoyage.model.RecalculationResult
import nl.teqplay.vesselvoyage.model.RecalculationShipResult
import nl.teqplay.vesselvoyage.model.RecalculationShipsResult
import nl.teqplay.vesselvoyage.model.RecalculationTrackResult
import nl.teqplay.vesselvoyage.model.ReventsRecalculationStatus
import nl.teqplay.vesselvoyage.model.ReventsRecalculationStatus.ErrorType.ILLEGAL_MERGE
import nl.teqplay.vesselvoyage.model.ReventsRecalculationStatus.ErrorType.REQUEST_FOR_ENTRIES_FAILED
import nl.teqplay.vesselvoyage.model.ReventsRecalculationStatus.ErrorType.REQUEST_FOR_INTERESTS_FAILED
import nl.teqplay.vesselvoyage.model.ReventsRecalculationStatus.ErrorType.STOPPED_EXCEPTION
import nl.teqplay.vesselvoyage.model.internal.AutomaticRecalculationShip.RecalculationState
import nl.teqplay.vesselvoyage.model.lightweight.poma.Port
import nl.teqplay.vesselvoyage.model.v2.EntryESoFWrapper
import nl.teqplay.vesselvoyage.service.EntryProcessingService
import nl.teqplay.vesselvoyage.service.ImoLockService
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.PostProcessingService
import nl.teqplay.vesselvoyage.service.ShipStatusService
import nl.teqplay.vesselvoyage.service.V1TraceService
import nl.teqplay.vesselvoyage.service.merge.EntriesMergeService
import nl.teqplay.vesselvoyage.service.merge.EntriesMergeV1Service
import nl.teqplay.vesselvoyage.service.merge.EntriesMergeV2Service
import nl.teqplay.vesselvoyage.service.trace.ProcessingTraceService
import nl.teqplay.vesselvoyage.util.createNewVisit
import nl.teqplay.vesselvoyage.util.createNewVoyage
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.web.client.HttpClientErrorException.NotFound
import java.time.Duration
import java.time.Instant
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ReventsRecalculationServiceTest {

    private val imoLockService = ImoLockService()
    private val reventsRecalculationsDataSource = mock<ReventsRecalculationsDataSource>()
    private val reventsConversionService = mock<ReventsConversionService>()
    private val reventsClient = mock<ReventsClient>()
    private val entryProcessingService = mock<EntryProcessingService>()
    private val shipStatusService = mock<ShipStatusService>()

    private val reventsMergingClient = mock<ReventsVesselVoyageMergingClient>()
    private val entriesMergeV1Service = mock<EntriesMergeV1Service>()
    private val visitDataSource = mock<VisitDataSource>()
    private val voyageDataSource = mock<VoyageDataSource>()
    private val v1TraceService = mock<V1TraceService>()

    private val reventsMergingV2Client = mock<ReventsVesselVoyageMergingV2Client>()
    private val entriesMergeV2Service = mock<EntriesMergeV2Service>()
    private val newVisitDataSource = mock<NewVisitDataSource>()
    private val newVoyageDataSource = mock<NewVoyageDataSource>()
    private val newESoFDataSource = mock<NewESoFDataSource>()
    private val processingTraceService = mock<ProcessingTraceService>()
    private val automaticRecalculationShipDataSource = mock<AutomaticRecalculationShipDataSource>()
    private val recalculationEnlargeTimeWindowService = mock<RecalculationEnlargeTimeWindowService>()
    private val infraService = mock<InfraService>()
    private val postProcessingService = mock<PostProcessingService>()

    private var reventsRecalculationService = setService()

    private val from = ZonedDateTime.now().minusHours(1)
    private val to = ZonedDateTime.now()

    val timeWindow = TimeWindow(from.toInstant(), to.toInstant())

    private val imo = 1234567
    private val unlocode = "NLRTM"
    private val start = Instant.EPOCH
    private val end = start.plus(30, ChronoUnit.DAYS)
    private val guarantee = ReventsRecalculationStatus.Guarantee.V2
    private val username = "piet"

    private val scenarioId = "scenario"
    private val defaultWindow = TimeWindow(Instant.EPOCH, Duration.ofDays(7))
    private val mockedMergeEntry = MergeEntry(
        window = defaultWindow,
        entries = emptyList()
    )
    private val mockedMergeEntryV2 = MergeEntryV2(
        window = defaultWindow,
        entries = emptyList()
    )

    @BeforeEach
    fun beforeEach() {
        reset(
            reventsRecalculationsDataSource,
            reventsConversionService,
            reventsClient,
            entryProcessingService,
            shipStatusService,
            reventsMergingClient,
            entriesMergeV1Service,
            visitDataSource,
            voyageDataSource,
            v1TraceService,
            reventsMergingV2Client,
            entriesMergeV2Service,
            newVisitDataSource,
            newVoyageDataSource,
            newESoFDataSource,
            processingTraceService,
            automaticRecalculationShipDataSource,
            infraService,
            recalculationEnlargeTimeWindowService
        )
        reventsRecalculationService = setService()
    }

    fun setService() = ReventsRecalculationService(
        properties = mock(),
        imoLockService = imoLockService,
        reventsRecalculationsDataSource = reventsRecalculationsDataSource,
        reventsConversionService = reventsConversionService,
        reventsClient = reventsClient,
        shipStatusService = shipStatusService,
        reventsMergingClient = reventsMergingClient,
        entriesMergeV1Service = entriesMergeV1Service,
        visitDataSource = visitDataSource,
        voyageDataSource = voyageDataSource,
        v1TraceService = v1TraceService,
        reventsMergingV2Client = reventsMergingV2Client,
        entriesMergeV2Service = entriesMergeV2Service,
        newVisitDataSource = newVisitDataSource,
        newVoyageDataSource = newVoyageDataSource,
        newESoFDataSource = newESoFDataSource,
        processingTraceService = processingTraceService,
        automaticRecalculationShipDataSource = automaticRecalculationShipDataSource,
        recalculationEnlargeTimeWindowService = recalculationEnlargeTimeWindowService,
        infraService = infraService,
        postProcessingService = postProcessingService,
        slackMessageService = null,
    )

    data class RunScenarioTestData(
        val message: String,
        val mockScenario: ScenarioResponse.() -> Unit,
        val verify: (lastKnownStatus: ReventsRecalculationStatus?) -> Unit
    )

    private fun existingScenarios(): Stream<Arguments> {
        val queuedAt = ZonedDateTime.now()

        return Stream.of(
            Arguments.of(
                RecalculationShipResult(
                    imo = 0,
                    revents = ReventsRecalculationStatus(
                        queuedAt = queuedAt,
                        scenarioId = scenarioId,
                        phase = ReventsRecalculationStatus.Phase.QUEUED,
                        guarantees = setOf(ReventsRecalculationStatus.Guarantee.V2),
                        username = username,
                        from = from,
                        to = to
                    ),
                    automated = false
                )
            ),
            Arguments.of(
                RecalculationShipsResult(
                    imos = listOf(1),
                    revents = ReventsRecalculationStatus(
                        queuedAt = queuedAt,
                        scenarioId = scenarioId,
                        phase = ReventsRecalculationStatus.Phase.QUEUED,
                        guarantees = setOf(ReventsRecalculationStatus.Guarantee.V2),
                        username = username,
                        from = from,
                        to = to
                    ),
                    automated = false
                )
            ),
            Arguments.of(
                RecalculationPortResult(
                    unlocode = "NLRTM",
                    revents = ReventsRecalculationStatus(
                        queuedAt = queuedAt,
                        scenarioId = scenarioId,
                        phase = ReventsRecalculationStatus.Phase.QUEUED,
                        guarantees = setOf(ReventsRecalculationStatus.Guarantee.V2),
                        username = username,
                        from = from,
                        to = to
                    ),
                    automated = false
                )
            ),
            Arguments.of(
                RecalculationTrackResult(
                    revents = ReventsRecalculationStatus(
                        queuedAt = queuedAt,
                        scenarioId = scenarioId,
                        phase = ReventsRecalculationStatus.Phase.QUEUED,
                        guarantees = setOf(ReventsRecalculationStatus.Guarantee.V2),
                        username = username,
                        from = from,
                        to = to
                    )
                )
            )
        )
    }

    @ParameterizedTest
    @MethodSource("existingScenarios")
    fun `init - should load existing running scenarios`(recalculationResult: RecalculationResult) {
        val queuedAt = recalculationResult.revents.queuedAt
        val results = listOf(recalculationResult)

        val scenarioResponse = mock<ScenarioResponse>().apply {
            // Set defaults that should result in successful execution.
            whenever(id).thenReturn(scenarioId)
            whenever(queued).thenReturn(queuedAt.toInstant())
            whenever(phase).thenReturn(ScenarioResponse.Phase.FINISHED)
            whenever(guarantees).thenReturn(setOf(ScenarioGuarantee.VESSEL_VOYAGE_MERGING))
            whenever(window).thenReturn(timeWindow)
        }

        whenever(reventsRecalculationsDataSource.findAllRunning()).thenReturn(results)
        whenever(reventsClient.getScenario(any())).thenReturn(scenarioResponse)
        whenever(reventsConversionService.convertToStatus(any())).thenCallRealMethod()
        whenever(reventsMergingClient.getInterestsForMerging(any())).thenReturn(emptyArray())

        reventsRecalculationService.init()
        reventsRecalculationService.checkScenarioProgress()

        verify(reventsClient, times(1)).getScenario(any())
        verify(reventsRecalculationsDataSource).updateStatus(any(), any())
    }

    @Test
    fun `should update automated scenario to finished when having no issues`() {
        val queuedAt = ZonedDateTime.now()
        val result = RecalculationShipsResult(
            imos = listOf(1),
            revents = ReventsRecalculationStatus(
                queuedAt = queuedAt,
                scenarioId = scenarioId,
                phase = ReventsRecalculationStatus.Phase.PROGRESSING,
                guarantees = setOf(ReventsRecalculationStatus.Guarantee.V2),
                username = username,
                from = from,
                to = to
            ),
            automated = true
        )
        val results = listOf(result)

        val scenarioResponse = mock<ScenarioResponse>().apply {
            // Set defaults that should result in successful execution.
            whenever(id).thenReturn(scenarioId)
            whenever(queued).thenReturn(queuedAt.toInstant())
            whenever(phase).thenReturn(ScenarioResponse.Phase.FINISHED)
            whenever(guarantees).thenReturn(setOf(ScenarioGuarantee.VESSEL_VOYAGE_MERGING_V2))
            whenever(window).thenReturn(timeWindow)
        }

        whenever(reventsRecalculationsDataSource.findAllRunning()).thenReturn(results)
        whenever(reventsClient.getScenario(any())).thenReturn(scenarioResponse)
        whenever(reventsConversionService.convertToStatus(any())).thenCallRealMethod()
        whenever(reventsMergingV2Client.getInterestsForMerging(any())).thenReturn(emptyArray())
        whenever(reventsRecalculationsDataSource.findById(any())).thenReturn(result)

        reventsRecalculationService.init()
        reventsRecalculationService.checkScenarioProgress()

        verify(reventsClient, times(1)).getScenario(any())
        verify(reventsRecalculationsDataSource).updateStatus(any(), any())
        verify(automaticRecalculationShipDataSource).updateState(eq(1), eq(RecalculationState.FINISHED))
    }

    @Test
    fun `should update automated scenario to error when failed with ship specific error`() {
        val queuedAt = ZonedDateTime.now()
        val result = RecalculationShipsResult(
            imos = listOf(1, 2),
            revents = ReventsRecalculationStatus(
                queuedAt = queuedAt,
                scenarioId = scenarioId,
                phase = ReventsRecalculationStatus.Phase.PROGRESSING,
                guarantees = setOf(ReventsRecalculationStatus.Guarantee.V2),
                errors = listOf(),
                username = username,
                from = from,
                to = to
            ),
            automated = true
        )
        val results = listOf(result)

        val scenarioResponse = mock<ScenarioResponse>().apply {
            // Set defaults that should result in successful execution.
            whenever(id).thenReturn(scenarioId)
            whenever(queued).thenReturn(queuedAt.toInstant())
            whenever(phase).thenReturn(ScenarioResponse.Phase.FINISHED)
            whenever(guarantees).thenReturn(setOf(ScenarioGuarantee.VESSEL_VOYAGE_MERGING_V2))
            whenever(window).thenReturn(timeWindow)
        }

        whenever(reventsRecalculationsDataSource.findAllRunning()).thenReturn(results)
        whenever(reventsClient.getScenario(any())).thenReturn(scenarioResponse)
        whenever(reventsConversionService.convertToStatus(any())).thenCallRealMethod()
        whenever(reventsMergingV2Client.getInterestsForMerging(any())).thenReturn(arrayOf(1, 2))
        whenever(reventsMergingV2Client.fetchEntriesForMerging(any(), eq(1))).thenReturn(emptyArray())
        whenever(reventsMergingV2Client.fetchEntriesForMerging(any(), eq(2))).thenThrow(Error("HTTP request failed"))
        whenever(reventsRecalculationsDataSource.findById(any())).thenReturn(result)

        reventsRecalculationService.init()
        reventsRecalculationService.checkScenarioProgress()

        verify(reventsClient, times(1)).getScenario(any())
        verify(reventsRecalculationsDataSource).updateStatus(any(), any())
        verify(automaticRecalculationShipDataSource).updateState(eq(1), eq(RecalculationState.FINISHED))
        verify(automaticRecalculationShipDataSource).updateState(eq(2), eq(RecalculationState.ERROR))
    }

    @Test
    fun `should run post-processing when merging back changes`() {
        val visitId = "visit1"
        val voyageId = "voyage1"

        val visit = EntryESoFWrapper(
            entry = createNewVisit(visitId),
            esof = null
        )

        val voyage = EntryESoFWrapper(
            entry = createNewVoyage(voyageId),
            esof = null
        )

        val mergeResult = EntriesMergeService.MergeResult(
            entries = listOf(visit, voyage),
            deleteVisitIds = emptyList(),
            deleteVoyageIds = emptyList()
        )

        val mockScenario = mock<RecalculationResult>().apply {
            whenever(revents).thenReturn(mock<ReventsRecalculationStatus>())
            whenever(revents.scenarioId).thenReturn(scenarioId)
        }
        val mockResponse = mock<ScenarioResponse>().apply {
            whenever(id).thenReturn(scenarioId)
            whenever(guarantees).thenReturn(setOf(ScenarioGuarantee.VESSEL_VOYAGE_MERGING_V2))
            whenever(phase).thenReturn(ScenarioResponse.Phase.FINISHED)
        }
        val mockRecalculationResult = mock<ReventsRecalculationStatus>().apply {
            whenever(scenarioId).thenReturn("scenarioId")
        }

        val recalculationStatus = ReventsRecalculationStatus(
            queuedAt = ZonedDateTime.now(),
            scenarioId = scenarioId,
            phase = ReventsRecalculationStatus.Phase.FINISHED,
            guarantees = setOf(ReventsRecalculationStatus.Guarantee.V2),
            errors = listOf(),
            username = username,
            from = from,
            to = to
        )

        whenever(reventsRecalculationsDataSource.findAllRunning()).thenReturn(listOf(mockScenario))
        whenever(reventsClient.getScenario(scenarioId)).thenReturn(mockResponse)
        whenever(reventsConversionService.convertToStatus(mockResponse)).thenReturn(mockRecalculationResult)
        whenever(reventsMergingV2Client.getInterestsForMerging(scenarioId)).thenReturn(arrayOf(1))
        whenever(reventsMergingV2Client.fetchEntriesForMerging(scenarioId, 1)).thenReturn(arrayOf(mockedMergeEntryV2))
        whenever(entriesMergeV2Service.merge(any(), any(), any())).thenReturn(mergeResult)
        whenever(reventsConversionService.convertToStatus(any())).thenReturn(recalculationStatus)
        whenever(reventsMergingV2Client.getInterestsForMerging(any())).thenReturn(arrayOf(1))
        whenever(reventsMergingV2Client.fetchEntriesForMerging(scenarioId, 1)).thenReturn(arrayOf(mockedMergeEntryV2))

        reventsRecalculationService.init()
        reventsRecalculationService.checkScenarioProgress()

        verify(postProcessingService, times(1)).schedulePostProcessing(eq(visitId))
        verify(postProcessingService, times(1)).schedulePostProcessing(eq(voyageId))
    }

    @Test
    fun `should update automated scenario to error when failed with general error`() {
        val queuedAt = ZonedDateTime.now()
        val result = RecalculationShipsResult(
            imos = listOf(1, 2),
            revents = ReventsRecalculationStatus(
                queuedAt = queuedAt,
                scenarioId = scenarioId,
                phase = ReventsRecalculationStatus.Phase.PROGRESSING,
                guarantees = setOf(ReventsRecalculationStatus.Guarantee.V2),
                errors = listOf(),
                username = username,
                from = from,
                to = to
            ),
            automated = true
        )
        val results = listOf(result)

        val scenarioResponse = mock<ScenarioResponse>().apply {
            // Set defaults that should result in successful execution.
            whenever(id).thenReturn(scenarioId)
            whenever(queued).thenReturn(queuedAt.toInstant())
            whenever(phase).thenReturn(ScenarioResponse.Phase.FINISHED)
            whenever(guarantees).thenReturn(setOf(ScenarioGuarantee.VESSEL_VOYAGE_MERGING_V2))
            whenever(window).thenReturn(timeWindow)
        }

        whenever(reventsRecalculationsDataSource.findAllRunning()).thenReturn(results)
        whenever(reventsClient.getScenario(any())).thenReturn(scenarioResponse)
        whenever(reventsConversionService.convertToStatus(any())).thenCallRealMethod()
        whenever(reventsMergingClient.getInterestsForMerging(any()))
            .thenThrow(Error("HTTP request failed"))
        whenever(reventsRecalculationsDataSource.findById(any())).thenReturn(result)

        reventsRecalculationService.init()
        reventsRecalculationService.checkScenarioProgress()

        verify(reventsClient, times(1)).getScenario(any())
        verify(reventsRecalculationsDataSource).updateStatus(any(), any())
        verify(automaticRecalculationShipDataSource).updateState(eq(1), eq(RecalculationState.ERROR))
        verify(automaticRecalculationShipDataSource).updateState(eq(2), eq(RecalculationState.ERROR))
    }

    @Test
    fun addScenario() {
        val testStatus = ReventsRecalculationStatus(
            queuedAt = ZonedDateTime.now(),
            scenarioId = "testAddScenario",
            phase = ReventsRecalculationStatus.Phase.QUEUED,
            guarantees = emptySet(),
            username = username,
            from = from,
            to = to
        )
        // New scenario should be added
        assertTrue(reventsRecalculationService.addScenario(testStatus))
        // Scenario already added, should not be re-added
        assertFalse(reventsRecalculationService.addScenario(testStatus))

        val (_, scenarioResponse) = mockScenarioData(emptySet())
        whenever(reventsConversionService.convertToStatus(any())).thenCallRealMethod()
        whenever(reventsClient.getScenario(any())).thenReturn(scenarioResponse)

        // Cleanup by progressing until finalized.
        while (!reventsRecalculationService.checkScenarioProgress()) {
            // Nothing
        }
    }

    private fun mockScenarioData(
        testGuarantees: Set<ScenarioGuarantee>
    ): Pair<ScenarioCreateRequest, ScenarioResponse> {
        val scenarioCreateRequest = mock<ScenarioCreateRequest>()
        val scenarioResponse = mock<ScenarioResponse>().apply {
            // Set defaults that should result in successful execution.
            whenever(id).thenReturn(scenarioId)
            whenever(queued).thenReturn(Instant.EPOCH)
            whenever(phase).thenReturn(ScenarioResponse.Phase.FINISHED)
            whenever(guarantees).thenReturn(testGuarantees)
            whenever(window).thenReturn(timeWindow)
        }
        return scenarioCreateRequest to scenarioResponse
    }

    private fun runScenarioShared(
        data: RunScenarioTestData,
        testGuarantee: ScenarioGuarantee,
    ) {
        val (scenarioCreateRequest, scenarioResponse) = mockScenarioData(setOf(testGuarantee))

        // Allow mocking to change above defaults, as well as simulating interactions with the revents API.
        data.mockScenario(scenarioResponse)

        whenever(reventsConversionService.convertToStatus(any())).thenCallRealMethod()
        whenever(reventsClient.createScenario(any())).thenReturn(scenarioResponse)
        whenever(reventsClient.getScenario(any())).thenReturn(scenarioResponse)

        var lastKnownStatus: ReventsRecalculationStatus? = null
        whenever(reventsRecalculationsDataSource.updateStatus(any(), any())).thenAnswer {
            lastKnownStatus = it.getArgument(1)
            return@thenAnswer Unit
        }

        reventsRecalculationService.runScenario(scenarioCreateRequest)

        // Keep progressing while we are not finalized yet.
        while (!reventsRecalculationService.checkScenarioProgress()) {
            // Nothing
        }

        data.verify(lastKnownStatus)

        // Ensure we've saved the data from (r)events in the meantime as well.
        verify(reventsRecalculationsDataSource, atLeastOnce()).updateStatus(eq(scenarioId), any())
    }

    @ParameterizedTest
    @MethodSource("runScenarioV1TestData")
    fun runScenarioV1(data: RunScenarioTestData) {
        runScenarioShared(data, ScenarioGuarantee.VESSEL_VOYAGE_MERGING)
    }

    private fun runScenarioV1TestData() = Stream.of(
        RunScenarioTestData(
            message = "scenario without merging guarantee, should be skipped",
            mockScenario = {
                whenever(guarantees).thenReturn(emptySet())
                whenever(window).thenReturn(timeWindow)
            },
            verify = {
                verify(reventsMergingClient, never()).getInterestsForMerging(any())
                verify(reventsMergingClient, never()).fetchEntriesForMerging(any(), any())
            }
        ),
        RunScenarioTestData(
            message = "scenario that eventually crashes, should be skipped",
            mockScenario = {
                whenever(phase)
                    .thenReturn(ScenarioResponse.Phase.QUEUED)
                    .thenReturn(ScenarioResponse.Phase.INITIALIZING)
                    .thenReturn(ScenarioResponse.Phase.PROGRESSING)
                    .thenReturn(ScenarioResponse.Phase.CRASHED)
            },
            verify = {
                verify(reventsMergingClient, never()).getInterestsForMerging(any())
                verify(reventsMergingClient, never()).fetchEntriesForMerging(any(), any())
            }
        ),
        RunScenarioTestData(
            message = "successful scenario, merging fails",
            mockScenario = {
                val mergeEntries = arrayOf(
                    MergeEntry(
                        window = defaultWindow,
                        entries = emptyList()
                    )
                )

                whenever(reventsMergingClient.getInterestsForMerging(any())).thenReturn(arrayOf("imo"))
                whenever(reventsMergingClient.fetchEntriesForMerging(any(), any())).thenReturn(mergeEntries)
                whenever(entriesMergeV1Service.merge(any(), any(), any())).thenThrow(IllegalArgumentException())
            },
            verify = {
                verify(reventsMergingClient, times(1)).getInterestsForMerging(any())
                verify(reventsMergingClient, times(1)).fetchEntriesForMerging(any(), eq("imo"))

                verify(visitDataSource, never()).createOrReplace(any())
                verify(visitDataSource, never()).deleteById(any())

                verify(voyageDataSource, never()).createOrReplace(any())
                verify(voyageDataSource, never()).deleteById(any())
            }
        ),
        RunScenarioTestData(
            message = "successful scenario, incorrect merge, upserts and deletes visit",
            mockScenario = {
                val time = Instant.EPOCH.atZone(ZoneOffset.UTC)
                val mergeEntries = arrayOf(
                    MergeEntry(
                        window = defaultWindow,
                        entries = listOf<Entry>(
                            createVisit(
                                _id = "deleteVisitId",
                                portAreas = listOf(createPortAreaVisit(PORT_NLRTM, time, time))
                            ),
                        )
                    )
                )

                val mergeResult = EntriesMergeService.MergeResult(
                    entries = mergeEntries.flatMap { it.entries },
                    deleteVisitIds = listOf("deleteVisitId"),
                    deleteVoyageIds = emptyList(),
                )

                whenever(reventsMergingClient.getInterestsForMerging(any())).thenReturn(arrayOf("imo"))
                whenever(reventsMergingClient.fetchEntriesForMerging(any(), any())).thenReturn(mergeEntries)
                whenever(entriesMergeV1Service.merge(any(), any(), any())).thenReturn(mergeResult)
            },
            verify = {
                verify(reventsMergingClient, times(1)).getInterestsForMerging(any())
                verify(reventsMergingClient, times(1)).fetchEntriesForMerging(any(), eq("imo"))

                verify(visitDataSource, never()).createOrReplace(any())
                verify(visitDataSource, never()).deleteById(any())

                verify(voyageDataSource, never()).createOrReplace(any())
                verify(voyageDataSource, never()).deleteById(any())
            }
        ),
        RunScenarioTestData(
            message = "successful scenario, incorrect merge, upserts and deletes voyage",
            mockScenario = {
                val time = Instant.EPOCH.atZone(ZoneOffset.UTC)
                val mergeEntries = arrayOf(
                    MergeEntry(
                        window = defaultWindow,
                        entries = listOf<Entry>(
                            createVoyage(
                                _id = "deleteVoyageId",
                                startTime = time,
                                startPortIds = emptyList(),
                                endTime = time,
                                endPortIds = emptyList()
                            ),
                        )
                    )
                )

                val mergeResult = EntriesMergeService.MergeResult(
                    entries = mergeEntries.flatMap { it.entries },
                    deleteVisitIds = emptyList(),
                    deleteVoyageIds = listOf("deleteVoyageId"),
                )

                whenever(reventsMergingClient.getInterestsForMerging(any())).thenReturn(arrayOf("imo"))
                whenever(reventsMergingClient.fetchEntriesForMerging(any(), any())).thenReturn(mergeEntries)
                whenever(entriesMergeV1Service.merge(any(), any(), any())).thenReturn(mergeResult)
            },
            verify = {
                verify(reventsMergingClient, times(1)).getInterestsForMerging(any())
                verify(reventsMergingClient, times(1)).fetchEntriesForMerging(any(), eq("imo"))

                verify(visitDataSource, never()).createOrReplace(any())
                verify(visitDataSource, never()).deleteById(any())

                verify(voyageDataSource, never()).createOrReplace(any())
                verify(voyageDataSource, never()).deleteById(any())
            }
        ),
        RunScenarioTestData(
            message = "add error when request for interests fails",
            mockScenario = {
                whenever(phase).thenReturn(ScenarioResponse.Phase.FINISHED)
                whenever(reventsMergingClient.getInterestsForMerging(any()))
                    .thenThrow(Error("HTTP request failed"))
            },
            verify = { status ->
                assertThat(status?.errors).isEqualTo(
                    listOf(
                        ReventsRecalculationStatus.Error(
                            type = REQUEST_FOR_INTERESTS_FAILED,
                            version = 1,
                            message = "HTTP request failed"
                        )
                    )
                )
            }
        ),
        RunScenarioTestData(
            message = "add error when request for merge entries fails",
            mockScenario = {
                whenever(phase).thenReturn(ScenarioResponse.Phase.FINISHED)
                whenever(reventsMergingClient.getInterestsForMerging(any()))
                    .thenReturn(arrayOf("0", "1"))
                whenever(reventsMergingClient.fetchEntriesForMerging(any(), any()))
                    .thenThrow(Error("HTTP request failed")) // IMO:0 fails
                    .thenReturn(arrayOf(mockedMergeEntry)) // IMO:1 passes
                whenever(entriesMergeV1Service.merge(any(), any(), any()))
                    .thenReturn(EntriesMergeService.MergeResult())
            },
            verify = { status ->
                assertThat(status?.errors).isEqualTo(
                    listOf(
                        ReventsRecalculationStatus.Error(
                            type = REQUEST_FOR_ENTRIES_FAILED,
                            version = 1,
                            imo = "0",
                            message = "HTTP request failed"
                        )
                    )
                )
                verify(entriesMergeV1Service, times(1)).merge(any(), any(), any())
            }
        ),
        RunScenarioTestData(
            message = "add error when merge action fails",
            mockScenario = {
                whenever(phase).thenReturn(ScenarioResponse.Phase.FINISHED)
                whenever(reventsMergingClient.getInterestsForMerging(any())).thenReturn(arrayOf("0"))
                whenever(reventsMergingClient.fetchEntriesForMerging(any(), any()))
                    .thenReturn(arrayOf(mockedMergeEntry))
                whenever(entriesMergeV1Service.merge(any(), any(), any()))
                    .thenThrow(IllegalArgumentException("Simulate merge failure"))
            },
            verify = { status ->
                assertThat(status?.errors).isEqualTo(
                    listOf(
                        ReventsRecalculationStatus.Error(
                            type = ILLEGAL_MERGE,
                            version = 1,
                            imo = "0",
                            message = "Simulate merge failure"
                        )
                    )
                )
            }
        ),
    )

    @ParameterizedTest
    @MethodSource("runScenarioV2TestData")
    fun runScenarioV2(data: RunScenarioTestData) {
        runScenarioShared(data, ScenarioGuarantee.VESSEL_VOYAGE_MERGING_V2)
    }

    private fun runScenarioV2TestData() = Stream.of(
        RunScenarioTestData(
            message = "scenario without merging guarantee, should be skipped",
            mockScenario = {
                whenever(guarantees).thenReturn(emptySet())
            },
            verify = {
                verify(reventsMergingV2Client, never()).getInterestsForMerging(any())
                verify(reventsMergingV2Client, never()).fetchEntriesForMerging(any(), any())
            }
        ),
        RunScenarioTestData(
            message = "scenario that eventually crashes, should be skipped",
            mockScenario = {
                whenever(phase)
                    .thenReturn(ScenarioResponse.Phase.QUEUED)
                    .thenReturn(ScenarioResponse.Phase.INITIALIZING)
                    .thenReturn(ScenarioResponse.Phase.PROGRESSING)
                    .thenReturn(ScenarioResponse.Phase.CRASHED)
            },
            verify = {
                verify(reventsMergingV2Client, never()).getInterestsForMerging(any())
                verify(reventsMergingV2Client, never()).fetchEntriesForMerging(any(), any())
            }
        ),
        RunScenarioTestData(
            message = "successful scenario but no entries to merge, shouldn't merge",
            mockScenario = {
                whenever(phase)
                    .thenReturn(ScenarioResponse.Phase.QUEUED)
                    .thenReturn(ScenarioResponse.Phase.INITIALIZING)
                    .thenReturn(ScenarioResponse.Phase.PROGRESSING)
                    .thenReturn(ScenarioResponse.Phase.FINISHED)

                val mergeEntries = arrayOf(
                    MergeEntryV2(
                        window = defaultWindow,
                        entries = emptyList()
                    )
                )

                val emptyMergeResult = EntriesMergeService.MergeResult<EntryESoFWrapper<*>>()

                whenever(reventsMergingV2Client.getInterestsForMerging(any())).thenReturn(arrayOf(0))
                whenever(reventsMergingV2Client.fetchEntriesForMerging(any(), any())).thenReturn(mergeEntries)
                whenever(entriesMergeV2Service.merge(any(), any(), any())).thenReturn(emptyMergeResult)
            },
            verify = {
                verify(newVisitDataSource, never()).createOrReplace(any())
                verify(newVisitDataSource, never()).deleteById(any())
                verify(newVoyageDataSource, never()).createOrReplace(any())
                verify(newVoyageDataSource, never()).deleteById(any())
                verify(newESoFDataSource, never()).createOrReplace(any())
                verify(newESoFDataSource, never()).deleteById(any())
                verify(processingTraceService, never()).deleteHistoricTraces(any(), any())
            }
        ),
        RunScenarioTestData(
            message = "successful scenario, misses encounters, if merge fails ESoF is not overwritten",
            mockScenario = {
                whenever(phase).thenReturn(ScenarioResponse.Phase.FINISHED)

                val mergeEntries = arrayOf(
                    MergeEntryV2(
                        window = defaultWindow,
                        entries = listOf(
                            EntryESoFWrapper(createNewVisit(_id = "visit"), null)
                        )
                    )
                )

                whenever(reventsMergingV2Client.getInterestsForMerging(any())).thenReturn(arrayOf(0))
                whenever(reventsMergingV2Client.fetchEntriesForMerging(any(), any())).thenReturn(mergeEntries)
                whenever(entriesMergeV2Service.merge(any(), any(), any())).thenThrow(IllegalArgumentException())
            },
            verify = {
                verify(reventsMergingV2Client, times(1)).getInterestsForMerging(any())
                verify(reventsMergingV2Client, times(1)).fetchEntriesForMerging(any(), eq(0))

                verify(newVisitDataSource, never()).createOrReplace(any())
                verify(newVisitDataSource, never()).deleteById(any())
                verify(newVoyageDataSource, never()).createOrReplace(any())
                verify(newVoyageDataSource, never()).deleteById(any())
                verify(newESoFDataSource, never()).createOrReplace(any())
                verify(newESoFDataSource, never()).deleteById(any())
                verify(processingTraceService, never()).deleteHistoricTraces(any(), any())
            }
        ),
        RunScenarioTestData(
            message = "successful scenario, misses encounters, results in merge and overwriting ESoF",
            mockScenario = {
                whenever(phase).thenReturn(ScenarioResponse.Phase.FINISHED)

                val mergeEntries = arrayOf(
                    MergeEntryV2(
                        window = defaultWindow,
                        entries = listOf(
                            EntryESoFWrapper(createNewVisit(_id = "visit"), null)
                        )
                    )
                )

                val mergeResult = EntriesMergeService.MergeResult(
                    entries = mergeEntries.flatMap { it.entries },
                )

                whenever(reventsMergingV2Client.getInterestsForMerging(any())).thenReturn(arrayOf(0))
                whenever(reventsMergingV2Client.fetchEntriesForMerging(any(), any())).thenReturn(mergeEntries)
                whenever(entriesMergeV2Service.merge(any(), any(), any())).thenReturn(mergeResult)
            },
            verify = {
                verify(reventsMergingV2Client, times(1)).getInterestsForMerging(any())
                verify(reventsMergingV2Client, times(1)).fetchEntriesForMerging(any(), eq(0))

                verify(newVisitDataSource, times(1)).createOrReplace(any())
                verify(newESoFDataSource, never()).createOrReplace(any())
                verify(shipStatusService, times(1)).removeStatus(eq(0))
            }
        ),
        RunScenarioTestData(
            message = "successful scenario, merging fails",
            mockScenario = {
                val mergeEntries = arrayOf(
                    MergeEntryV2(
                        window = defaultWindow,
                        entries = emptyList()
                    )
                )

                whenever(reventsMergingV2Client.getInterestsForMerging(any())).thenReturn(arrayOf(0))
                whenever(reventsMergingV2Client.fetchEntriesForMerging(any(), any())).thenReturn(mergeEntries)
                whenever(entriesMergeV2Service.merge(any(), any(), any())).thenThrow(IllegalArgumentException())
            },
            verify = {
                verify(reventsMergingV2Client, times(1)).getInterestsForMerging(any())
                verify(reventsMergingV2Client, times(1)).fetchEntriesForMerging(any(), eq(0))

                verify(newVisitDataSource, never()).createOrReplace(any())
                verify(newVisitDataSource, never()).deleteById(any())
                verify(newVoyageDataSource, never()).createOrReplace(any())
                verify(newVoyageDataSource, never()).deleteById(any())
                verify(newVoyageDataSource, never()).createOrReplace(any())
                verify(newVoyageDataSource, never()).deleteById(any())
                verify(processingTraceService, never()).deleteHistoricTraces(any(), any())
            }
        ),
        RunScenarioTestData(
            message = "successful scenario, incorrect merge, upserts and deletes visit",
            mockScenario = {
                val mergeEntries = arrayOf(
                    MergeEntryV2(
                        window = defaultWindow,
                        entries = listOf(
                            EntryESoFWrapper(createNewVisit(_id = "deleteVisitId"), null),
                        )
                    )
                )

                val mergeResult = EntriesMergeService.MergeResult(
                    entries = mergeEntries.flatMap { it.entries },
                    deleteVisitIds = listOf("deleteVisitId"),
                    deleteVoyageIds = emptyList(),
                )

                whenever(reventsMergingV2Client.getInterestsForMerging(any())).thenReturn(arrayOf(0))
                whenever(reventsMergingV2Client.fetchEntriesForMerging(any(), any())).thenReturn(mergeEntries)
                whenever(entriesMergeV2Service.merge(any(), any(), any())).thenReturn(mergeResult)
            },
            verify = {
                verify(reventsMergingV2Client, times(1)).getInterestsForMerging(any())
                verify(reventsMergingV2Client, times(1)).fetchEntriesForMerging(any(), eq(0))

                verify(newVisitDataSource, never()).createOrReplace(any())
                verify(newVisitDataSource, never()).deleteById(any())
                verify(newVoyageDataSource, never()).createOrReplace(any())
                verify(newVoyageDataSource, never()).deleteById(any())
                verify(newVoyageDataSource, never()).createOrReplace(any())
                verify(newVoyageDataSource, never()).deleteById(any())
                verify(processingTraceService, never()).deleteHistoricTraces(any(), any())
            }
        ),
        RunScenarioTestData(
            message = "successful scenario, incorrect merge, upserts and deletes voyage",
            mockScenario = {
                val mergeEntries = arrayOf(
                    MergeEntryV2(
                        window = defaultWindow,
                        entries = listOf(
                            EntryESoFWrapper(createNewVoyage(_id = "deleteVoyageId"), null)
                        )
                    )
                )

                val mergeResult = EntriesMergeService.MergeResult(
                    entries = mergeEntries.flatMap { it.entries },
                    deleteVisitIds = emptyList(),
                    deleteVoyageIds = listOf("deleteVoyageId"),
                )

                whenever(reventsMergingV2Client.getInterestsForMerging(any())).thenReturn(arrayOf(0))
                whenever(reventsMergingV2Client.fetchEntriesForMerging(any(), any())).thenReturn(mergeEntries)
                whenever(entriesMergeV2Service.merge(any(), any(), any())).thenReturn(mergeResult)
            },
            verify = {
                verify(reventsMergingV2Client, times(1)).getInterestsForMerging(any())
                verify(reventsMergingV2Client, times(1)).fetchEntriesForMerging(any(), eq(0))

                verify(newVisitDataSource, never()).createOrReplace(any())
                verify(newVisitDataSource, never()).deleteById(any())
                verify(newVoyageDataSource, never()).createOrReplace(any())
                verify(newVoyageDataSource, never()).deleteById(any())
                verify(newVoyageDataSource, never()).createOrReplace(any())
                verify(newVoyageDataSource, never()).deleteById(any())
                verify(processingTraceService, never()).deleteHistoricTraces(any(), any())
            }
        ),
        RunScenarioTestData(
            message = "add error when request for interests fails",
            mockScenario = {
                whenever(phase).thenReturn(ScenarioResponse.Phase.FINISHED)
                whenever(reventsMergingV2Client.getInterestsForMerging(any()))
                    .thenThrow(Error("HTTP request failed"))
            },
            verify = { status ->
                assertThat(status?.errors).isEqualTo(
                    listOf(
                        ReventsRecalculationStatus.Error(
                            type = REQUEST_FOR_INTERESTS_FAILED,
                            version = 2,
                            message = "HTTP request failed"
                        )
                    )
                )
            }
        ),
        RunScenarioTestData(
            message = "add error when request for merge entries fails",
            mockScenario = {
                whenever(phase).thenReturn(ScenarioResponse.Phase.FINISHED)
                whenever(reventsMergingV2Client.getInterestsForMerging(any()))
                    .thenReturn(arrayOf(0, 1))
                whenever(reventsMergingV2Client.fetchEntriesForMerging(any(), any()))
                    .thenThrow(Error("HTTP request failed")) // IMO:0 fails
                    .thenReturn(arrayOf(mockedMergeEntryV2)) // IMO:1 passes
                whenever(entriesMergeV2Service.merge(any(), any(), any()))
                    .thenReturn(EntriesMergeService.MergeResult())
            },
            verify = { status ->
                assertThat(status?.errors).isEqualTo(
                    listOf(
                        ReventsRecalculationStatus.Error(
                            type = REQUEST_FOR_ENTRIES_FAILED,
                            version = 2,
                            imo = "0",
                            message = "HTTP request failed",
                            level = ReventsRecalculationStatus.ErrorLevel.WARNING
                        )
                    )
                )
                verify(entriesMergeV2Service, times(1)).merge(any(), any(), any())
            }
        ),
        RunScenarioTestData(
            message = "add error when merge action fails",
            mockScenario = {
                whenever(phase).thenReturn(ScenarioResponse.Phase.FINISHED)
                whenever(reventsMergingV2Client.getInterestsForMerging(any())).thenReturn(arrayOf(0))
                whenever(reventsMergingV2Client.fetchEntriesForMerging(any(), any()))
                    .thenReturn(arrayOf(mockedMergeEntryV2))
                whenever(entriesMergeV2Service.merge(any(), any(), any()))
                    .thenThrow(IllegalArgumentException("Simulate merge failure"))
            },
            verify = { status ->
                assertThat(status?.errors).isEqualTo(
                    listOf(
                        ReventsRecalculationStatus.Error(
                            type = ILLEGAL_MERGE,
                            version = 2,
                            imo = "0",
                            message = "Simulate merge failure",
                            level = ReventsRecalculationStatus.ErrorLevel.WARNING
                        )
                    )
                )
            }
        ),
    )

    @Test
    fun `Ignore scenario when not avaialble in vesselvoyage db`() {
        whenever(reventsClient.getScenario(any())).thenThrow(mock<NotFound>())
        whenever(reventsRecalculationsDataSource.findById(any())).thenReturn(null)

        assertNull(reventsRecalculationService.getScenario("test"))
    }

    @Test
    fun `Make sure scenario gets stopped when not available in revents`() {
        val result = RecalculationShipsResult(
            imos = listOf(1),
            revents = ReventsRecalculationStatus(
                queuedAt = ZonedDateTime.now(),
                scenarioId = scenarioId,
                phase = ReventsRecalculationStatus.Phase.PROGRESSING,
                guarantees = setOf(ReventsRecalculationStatus.Guarantee.V2),
                username = username,
                from = from,
                to = to
            ),
            automated = true
        )

        whenever(reventsClient.getScenario(any())).thenThrow(mock<NotFound>())
        whenever(reventsRecalculationsDataSource.findById(any())).thenReturn(result)

        val captor = argumentCaptor<ReventsRecalculationStatus>()

        val expected = result.copy(
            revents = result.revents.copy(
                phase = ReventsRecalculationStatus.Phase.STOPPED,
                errors = listOf(ReventsRecalculationStatus.Error(STOPPED_EXCEPTION, 2))
            )
        )

        assertNull(reventsRecalculationService.getScenario("test"))

        verify(reventsRecalculationsDataSource).updateStatus(eq("test"), captor.capture())

        assertEquals(captor.firstValue, expected.revents)
    }

    @Test
    fun `recalculateByShip - start equals end - start should be less than end`() {
        assertThrows<BadRequestException> {
            val time = Instant.EPOCH
            reventsRecalculationService.recalculateByShip(imo, time, time, guarantee, username)
        }
    }

    @Test
    fun `recalculateByShip - start larger than end - start should be less than end`() {
        assertThrows<BadRequestException> {
            // Start/end are flipped.
            reventsRecalculationService.recalculateByShip(imo, start = end, end = start, guarantee, username)
        }
    }

    @Test
    fun `recalculateByShip - creates scenario - V1`() {
        val scenarioId = "scenarioId"
        val scenarioCreateRequest = mock<ScenarioCreateRequest>()
        val window = TimeWindow(
            from = start.minus(1, ChronoUnit.DAYS),
            to = end.plus(1, ChronoUnit.DAYS)
        )
        val reventsRecalculationStatus = ReventsRecalculationStatus(
            queuedAt = ZonedDateTime.now(),
            scenarioId = scenarioId,
            phase = ReventsRecalculationStatus.Phase.QUEUED,
            guarantees = setOf(ReventsRecalculationStatus.Guarantee.V1),
            username = username,
            from = from,
            to = to
        )

        whenever(recalculationEnlargeTimeWindowService.enlargeTimeWindowBasedOnVoyagesV1(any(), any(), any()))
            .thenReturn(window)
        whenever(reventsConversionService.createScenarioRequestForShip(any(), any(), any(), any(), any()))
            .thenReturn(scenarioCreateRequest)
        whenever(reventsConversionService.convertToStatus(any()))
            .thenCallRealMethod()
        whenever(reventsConversionService.convertToStatus(any()))
            .thenReturn(reventsRecalculationStatus)
        val scenarioResponse = mock<ScenarioResponse>()
        whenever(scenarioResponse.id).thenReturn(scenarioId)
        whenever(reventsClient.createScenario(any())).thenReturn(scenarioResponse)

        val result = reventsRecalculationService.recalculateByShip(imo, start, end, ReventsRecalculationStatus.Guarantee.V1, username)

        val expected = RecalculationShipResult(
            _id = reventsRecalculationStatus.scenarioId,
            imo = imo,
            revents = reventsRecalculationStatus,
            startedAt = reventsRecalculationStatus.queuedAt,
            external = false,
            automated = false,
            username = username,
            from = from,
            to = to
        )
        assertThat(result).isEqualTo(expected)

        verify(recalculationEnlargeTimeWindowService, times(1))
            .enlargeTimeWindowBasedOnVoyagesV1(eq(imo), eq(start), eq(end))
        verify(reventsConversionService, times(1))
            .createScenarioRequestForShip(eq(imo), eq(window), eq(ReventsRecalculationStatus.Guarantee.V1), eq(username), any())
        verify(reventsClient, times(1))
            .createScenario(eq(scenarioCreateRequest))
        verify(reventsRecalculationsDataSource, times(1))
            .save(any())
    }

    @Test
    fun `recalculateByShip - creates scenario - V2`() {
        val scenarioId = "scenarioId"
        val scenarioCreateRequest = mock<ScenarioCreateRequest>()
        val window = TimeWindow(
            from = start.minus(1, ChronoUnit.DAYS),
            to = end.plus(1, ChronoUnit.DAYS)
        )
        val reventsRecalculationStatus = ReventsRecalculationStatus(
            queuedAt = ZonedDateTime.now(),
            scenarioId = scenarioId,
            phase = ReventsRecalculationStatus.Phase.QUEUED,
            guarantees = setOf(ReventsRecalculationStatus.Guarantee.V2),
            username = username,
            from = from,
            to = to
        )

        whenever(recalculationEnlargeTimeWindowService.enlargeTimeWindowBasedOnVoyagesV2(any(), any(), any()))
            .thenReturn(window)
        whenever(reventsConversionService.createScenarioRequestForShip(any(), any(), any(), any(), any()))
            .thenReturn(scenarioCreateRequest)
        whenever(reventsConversionService.convertToStatus(any()))
            .thenCallRealMethod()
        whenever(reventsConversionService.convertToStatus(any()))
            .thenReturn(reventsRecalculationStatus)
        val scenarioResponse = mock<ScenarioResponse>()
        whenever(scenarioResponse.id).thenReturn(scenarioId)
        whenever(reventsClient.createScenario(any())).thenReturn(scenarioResponse)

        val result = reventsRecalculationService.recalculateByShip(imo, start, end, ReventsRecalculationStatus.Guarantee.V2, username)

        val expected = RecalculationShipResult(
            _id = reventsRecalculationStatus.scenarioId,
            imo = imo,
            revents = reventsRecalculationStatus,
            startedAt = reventsRecalculationStatus.queuedAt,
            external = false,
            automated = false,
            username = username,
            from = from,
            to = to
        )
        assertThat(result).isEqualTo(expected)

        verify(recalculationEnlargeTimeWindowService, times(1))
            .enlargeTimeWindowBasedOnVoyagesV2(eq(imo), eq(start), eq(end))
        verify(reventsConversionService, times(1))
            .createScenarioRequestForShip(eq(imo), eq(window), eq(ReventsRecalculationStatus.Guarantee.V2), eq(username), any())
        verify(reventsClient, times(1))
            .createScenario(eq(scenarioCreateRequest))
        verify(reventsRecalculationsDataSource, times(1))
            .save(any())
    }

    @Test
    fun `recalculateByPort - start equals end - start should be less than end`() {
        assertThrows<BadRequestException> {
            val time = Instant.EPOCH
            reventsRecalculationService.recalculateByPort(unlocode, time, time, guarantee, username)
        }
    }

    @Test
    fun `recalculateByPort - start larger than end - start should be less than end`() {
        assertThrows<BadRequestException> {
            // Start/end are flipped.
            reventsRecalculationService.recalculateByPort(unlocode, start = end, end = start, guarantee, username)
        }
    }

    @Test
    fun `recalculateByPort - port not found`() {
        assertThrows<BadRequestException> {
            whenever(infraService.getPortByUnlocode(any())).thenReturn(null)
            reventsRecalculationService.recalculateByPort(unlocode, start, end, guarantee, username)
        }
    }

    @Test
    fun `recalculateByPort - creates scenario`() {
        val scenarioId = "scenarioId"
        val scenarioCreateRequest = mock<ScenarioCreateRequest>()
        val reventsRecalculationStatus = ReventsRecalculationStatus(
            queuedAt = ZonedDateTime.now(),
            scenarioId = scenarioId,
            phase = ReventsRecalculationStatus.Phase.QUEUED,
            guarantees = setOf(guarantee),
            username = username,
            from = from,
            to = to
        )
        val port = mock<Port>().apply {
            whenever(_id).thenReturn(<EMAIL>)
        }

        whenever(infraService.getPortByUnlocode(any())).thenReturn(port)
        whenever(reventsConversionService.createScenarioRequestForPort(any(), any(), any(), any(), any()))
            .thenReturn(scenarioCreateRequest)
        whenever(reventsConversionService.convertToStatus(any()))
            .thenReturn(reventsRecalculationStatus)
        val scenarioResponse = mock<ScenarioResponse>()
        whenever(scenarioResponse.id).thenReturn(scenarioId)
        whenever(reventsClient.createScenario(any())).thenReturn(scenarioResponse)

        val result = reventsRecalculationService.recalculateByPort(unlocode, start, end, guarantee, username)

        val expected = RecalculationPortResult(
            _id = reventsRecalculationStatus.scenarioId,
            unlocode = unlocode,
            revents = reventsRecalculationStatus,
            startedAt = reventsRecalculationStatus.queuedAt,
            external = false,
            automated = false,
            username = username,
            from = from,
            to = to
        )
        assertThat(result).isEqualTo(expected)

        verify(infraService, times(1))
            .getPortByUnlocode(eq(unlocode))
        verify(reventsConversionService, times(1))
            .createScenarioRequestForPort(eq(unlocode), eq(TimeWindow(start, end)), eq(guarantee), eq(username), any())
        verify(reventsRecalculationsDataSource, times(1))
            .save(any())
    }

    @Test
    fun trackScenario() {
        val scenarioId = "scenarioId"
        val reventsRecalculationStatus = ReventsRecalculationStatus(
            queuedAt = ZonedDateTime.now(),
            scenarioId = scenarioId,
            phase = ReventsRecalculationStatus.Phase.QUEUED,
            guarantees = setOf(guarantee),
            username = username,
            from = from,
            to = to
        )

        whenever(reventsClient.getScenario(any())).thenReturn(mock())
        whenever(reventsConversionService.convertToStatus(any())).thenReturn(reventsRecalculationStatus)

        val result = reventsRecalculationService.trackScenario(scenarioId)

        val expected = RecalculationTrackResult(
            _id = reventsRecalculationStatus.scenarioId,
            revents = reventsRecalculationStatus,
            startedAt = reventsRecalculationStatus.queuedAt,
            external = true,
            automated = false,
            username = username,
            from = from,
            to = to
        )
        assertThat(result).isEqualTo(expected)

        verify(reventsClient, times(1)).getScenario(any())
        verify(reventsConversionService, times(1)).convertToStatus(any())
        verify(reventsRecalculationsDataSource, times(1)).save(any())
    }

    @Test
    fun trackScenarioNotFound() {
        val scenarioId = "scenarioId"

        whenever(reventsClient.getScenario(any())).thenReturn(null)

        assertThrows<NotFoundException> { reventsRecalculationService.trackScenario(scenarioId) }
    }

    @Test
    fun `merge single IMO with provided scenario`() {
        val recalculationShipResult = mock<RecalculationShipResult>()
        whenever(recalculationShipResult.imo).thenReturn(1234567)
        whenever(recalculationShipResult._id).thenReturn("scenario1")
        whenever(reventsRecalculationsDataSource.findById("scenario1")).thenReturn(recalculationShipResult)

        val result = reventsRecalculationService.mergeShipRecalculation(1234567, "scenario1", "user")

        assertTrue(result)
        verify(reventsRecalculationsDataSource).save(any())
    }

    @Test
    fun `merge single IMO with scenario resolution`() {
        whenever(reventsRecalculationsDataSource.firstScenarioIdByImo(1234567)).thenReturn("resolvedScenario")

        val result = reventsRecalculationService.mergeShipRecalculation(1234567, null, "user")

        assertTrue(result)
        verify(reventsRecalculationsDataSource).firstScenarioIdByImo(1234567)
        verify(reventsRecalculationsDataSource).save(any())
    }

    @Test
    fun `merge single IMO with no scenario found throws exception`() {
        whenever(reventsRecalculationsDataSource.firstScenarioIdByImo(1234567)).thenReturn(null)

        assertThrows<BadRequestException> {
            reventsRecalculationService.mergeShipRecalculation(1234567, null, "user")
        }
    }

    @Test
    fun `merge multiple IMOs with scenario provided`() {
        val recalculationShipsResult = mock<RecalculationShipsResult>()
        whenever(recalculationShipsResult.imos).thenReturn(listOf(1234567, 7654321))
        whenever(reventsRecalculationsDataSource.findById("scenarioX")).thenReturn(recalculationShipsResult)

        val result = reventsRecalculationService.mergeShipRecalculation(listOf(1234567, 7654321), "scenarioX", "user")

        assertTrue(result)
        verify(reventsRecalculationsDataSource).save(any())
    }

    @Test
    fun `merge multiple IMOs with scenarios resolved`() {
        val mockMap = mapOf(
            1234567 to mock<RecalculationShipResult>().apply { whenever(_id).thenReturn("scenario1") },
            7654321 to mock<RecalculationShipResult>().apply { whenever(_id).thenReturn("scenario2") }
        )
        whenever(reventsRecalculationsDataSource.findMostRecentResultsByImos(listOf(1234567, 7654321))).thenReturn(mockMap)

        val result = reventsRecalculationService.mergeShipRecalculation(listOf(1234567, 7654321), null, "user")

        assertTrue(result)
        verify(reventsRecalculationsDataSource).findMostRecentResultsByImos(listOf(1234567, 7654321))
        verify(reventsRecalculationsDataSource).save(any())
    }

    @Test
    fun `merge scenario-based merging with interests found`() {
        whenever(reventsMergingV2Client.getInterestsForMerging("scenarioA")).thenReturn(arrayOf(1111111, 2222222))

        val result = reventsRecalculationService.mergeShipRecalculation("scenarioA", "user")

        assertTrue(result)
        verify(reventsMergingV2Client).getInterestsForMerging("scenarioA")
        verify(reventsRecalculationsDataSource).save(any())
    }

    @Test
    fun `merge scenario-based merging with no interests throws exception`() {
        whenever(reventsMergingV2Client.getInterestsForMerging("scenarioA")).thenReturn(emptyArray())

        assertThrows<BadRequestException> {
            reventsRecalculationService.mergeShipRecalculation("scenarioA", "user")
        }
        verify(reventsMergingV2Client).getInterestsForMerging("scenarioA")
    }

    @Test
    fun `merge scenario-based merging with client error returns false`() {
        whenever(reventsMergingV2Client.getInterestsForMerging("scenarioA")).thenThrow(RuntimeException("Client error"))

        val result = reventsRecalculationService.mergeShipRecalculation("scenarioA", "user")

        assertFalse(result)
        verify(reventsMergingV2Client).getInterestsForMerging("scenarioA")
    }
}
