package nl.teqplay.vesselvoyage.service.recalculation

import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.eq
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.reset
import com.nhaarman.mockitokotlin2.times
import com.nhaarman.mockitokotlin2.verify
import com.nhaarman.mockitokotlin2.whenever
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.TimeWindow
import nl.teqplay.vesselvoyage.datasource.NewVoyageDataSource
import nl.teqplay.vesselvoyage.model.Voyage
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import nl.teqplay.vesselvoyage.service.VoyageService
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.time.Instant
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class RecalculationEnlargeTimeWindowServiceTest {

    private val voyageService = mock<VoyageService>()
    private val newVoyageDataSource = mock<NewVoyageDataSource>()
    private val recalculationEnlargeTimeWindowService = RecalculationEnlargeTimeWindowService(
        voyageService,
        newVoyageDataSource
    )

    private val imo = 0
    private val startTime = Instant.EPOCH
    private val endTime = startTime.plus(30, ChronoUnit.DAYS)
    private val loc = Location(0.0, 0.0)

    data class EnlargeTimeWindowTestDataV1(
        val message: String,
        val voyageAtStart: Voyage?,
        val voyageAtEnd: Voyage?,
        val output: TimeWindow
    )

    data class EnlargeTimeWindowTestDataV2(
        val message: String,
        val voyageAtStart: NewVoyage?,
        val voyageAtEnd: NewVoyage?,
        val output: TimeWindow
    )

    @AfterEach
    fun afterEach() {
        reset(voyageService, newVoyageDataSource)
    }

    @ParameterizedTest
    @MethodSource("enlargeTimeWindowTestDataV1")
    fun enlargeTimeWindowBasedOnVoyagesV1(data: EnlargeTimeWindowTestDataV1) {
        whenever(voyageService.findNewestStartedBeforeByImo(any(), any())).thenReturn(data.voyageAtStart)
        whenever(voyageService.findOldestEndedAfterByImo(any(), any(), any())).thenReturn(data.voyageAtEnd)

        assertEquals(
            data.output,
            recalculationEnlargeTimeWindowService.enlargeTimeWindowBasedOnVoyagesV1(imo, startTime, endTime),
            data.message
        )

        verify(voyageService, times(1))
            .findNewestStartedBeforeByImo(
                eq(imo.toString()),
                eq(startTime.atZone(ZoneOffset.UTC))
            )
        verify(voyageService, times(1))
            .findOldestEndedAfterByImo(
                eq(imo.toString()),
                eq(startTime.atZone(ZoneOffset.UTC)),
                eq(endTime.atZone(ZoneOffset.UTC))
            )
    }

    @ParameterizedTest
    @MethodSource("enlargeTimeWindowTestDataV2")
    fun enlargeTimeWindowBasedOnVoyagesV2(data: EnlargeTimeWindowTestDataV2) {
        whenever(newVoyageDataSource.findNewestStartedBeforeByImo(any(), any())).thenReturn(data.voyageAtStart)
        whenever(newVoyageDataSource.findOldestEndedAfterByImo(any(), any(), any())).thenReturn(data.voyageAtEnd)

        assertEquals(
            data.output,
            recalculationEnlargeTimeWindowService.enlargeTimeWindowBasedOnVoyagesV2(imo, startTime, endTime),
            data.message
        )

        verify(newVoyageDataSource, times(1))
            .findNewestStartedBeforeByImo(eq(imo), eq(startTime))
        verify(newVoyageDataSource, times(1))
            .findOldestEndedAfterByImo(eq(imo), eq(startTime), eq(endTime))
    }

    private fun enlargeTimeWindowTestDataV1() = Stream.of(
        EnlargeTimeWindowTestDataV1(
            message = "if no voyages found, simply return input",
            voyageAtStart = null,
            voyageAtEnd = null,
            output = TimeWindow(startTime, endTime)
        ),
        EnlargeTimeWindowTestDataV1(
            message = "voyage at start moves start time",
            voyageAtStart = mock<Voyage>().also {
                whenever(it.startTime).thenReturn(startTime.minus(1, ChronoUnit.DAYS).atZone(ZoneOffset.UTC))
            },
            voyageAtEnd = null,
            output = TimeWindow(startTime.minus(1, ChronoUnit.DAYS), endTime)
        ),
        EnlargeTimeWindowTestDataV1(
            message = "voyage at end moves end time",
            voyageAtStart = null,
            voyageAtEnd = mock<Voyage>().also {
                whenever(it.endTime).thenReturn(endTime.plus(1, ChronoUnit.DAYS).atZone(ZoneOffset.UTC))
            },
            output = TimeWindow(startTime, endTime.plus(1, ChronoUnit.DAYS))
        ),
        EnlargeTimeWindowTestDataV1(
            message = "voyage at end doesn't have end time",
            voyageAtStart = null,
            voyageAtEnd = mock<Voyage>().also {
                whenever(it.endTime).thenReturn(null)
            },
            output = TimeWindow(startTime, endTime)
        ),
        EnlargeTimeWindowTestDataV1(
            message = "both voyages at start and end move enlarge window",
            voyageAtStart = mock<Voyage>().also {
                whenever(it.startTime).thenReturn(startTime.minus(1, ChronoUnit.DAYS).atZone(ZoneOffset.UTC))
            },
            voyageAtEnd = mock<Voyage>().also {
                whenever(it.endTime).thenReturn(endTime.plus(1, ChronoUnit.DAYS).atZone(ZoneOffset.UTC))
            },
            output = TimeWindow(startTime.minus(1, ChronoUnit.DAYS), endTime.plus(1, ChronoUnit.DAYS))
        ),
    )

    private fun enlargeTimeWindowTestDataV2() = Stream.of(
        EnlargeTimeWindowTestDataV2(
            message = "if no voyages found, simply return input",
            voyageAtStart = null,
            voyageAtEnd = null,
            output = TimeWindow(startTime, endTime)
        ),
        EnlargeTimeWindowTestDataV2(
            message = "voyage at start moves start time",
            voyageAtStart = mock<NewVoyage>().also {
                whenever(it.start).thenReturn(LocationTime(loc, startTime.minus(1, ChronoUnit.DAYS)))
                whenever(it.start).thenReturn(LocationTime(loc, startTime.minus(1, ChronoUnit.DAYS)))
            },
            voyageAtEnd = null,
            output = TimeWindow(startTime.minus(1, ChronoUnit.DAYS), endTime)
        ),
        EnlargeTimeWindowTestDataV2(
            message = "voyage at end moves end time",
            voyageAtStart = null,
            voyageAtEnd = mock<NewVoyage>().also {
                whenever(it.end).thenReturn(LocationTime(loc, endTime.plus(1, ChronoUnit.DAYS)))
            },
            output = TimeWindow(startTime, endTime.plus(1, ChronoUnit.DAYS))
        ),
        EnlargeTimeWindowTestDataV2(
            message = "voyage at end doesn't have end time",
            voyageAtStart = null,
            voyageAtEnd = mock<NewVoyage>().also {
                whenever(it.end).thenReturn(null)
            },
            output = TimeWindow(startTime, endTime)
        ),
        EnlargeTimeWindowTestDataV2(
            message = "both voyages at start and end move enlarge window",
            voyageAtStart = mock<NewVoyage>().also {
                whenever(it.start).thenReturn(LocationTime(loc, startTime.minus(1, ChronoUnit.DAYS)))
            },
            voyageAtEnd = mock<NewVoyage>().also {
                whenever(it.end).thenReturn(LocationTime(loc, endTime.plus(1, ChronoUnit.DAYS)))
            },
            output = TimeWindow(startTime.minus(1, ChronoUnit.DAYS), endTime.plus(1, ChronoUnit.DAYS))
        ),
    )
}
