package nl.teqplay.vesselvoyage.service.recalculation

import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.anyOrNull
import com.nhaarman.mockitokotlin2.atLeastOnce
import com.nhaarman.mockitokotlin2.eq
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.never
import com.nhaarman.mockitokotlin2.spy
import com.nhaarman.mockitokotlin2.times
import com.nhaarman.mockitokotlin2.verify
import com.nhaarman.mockitokotlin2.whenever
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import nl.teqplay.aisengine.event.model.AreaStartEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.skeleton.model.Location
import nl.teqplay.vesselvoyage.datasource.NewESoFDataSource
import nl.teqplay.vesselvoyage.datasource.NewTraceDataSource
import nl.teqplay.vesselvoyage.datasource.NewVisitDataSource
import nl.teqplay.vesselvoyage.datasource.NewVoyageDataSource
import nl.teqplay.vesselvoyage.logic.IMO_1
import nl.teqplay.vesselvoyage.logic.MMSI_1
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.v2.ESoFChange
import nl.teqplay.vesselvoyage.model.v2.EntryESoFWrapper
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.VisitChange
import nl.teqplay.vesselvoyage.model.v2.VoyageChange
import nl.teqplay.vesselvoyage.service.EntryProcessingService
import nl.teqplay.vesselvoyage.service.EventFetchingService
import nl.teqplay.vesselvoyage.service.ImoLockService
import nl.teqplay.vesselvoyage.service.PersistChangesService
import nl.teqplay.vesselvoyage.service.ProcessingShipStatusService
import nl.teqplay.vesselvoyage.service.publisher.ChangesPublisherService
import nl.teqplay.vesselvoyage.util.createNewESoF
import nl.teqplay.vesselvoyage.util.createNewVisit
import nl.teqplay.vesselvoyage.util.createNewVisitShipStatus
import nl.teqplay.vesselvoyage.util.createNewVoyage
import org.junit.jupiter.api.Test
import java.time.Instant

class ManualRecalculationServiceTest {
    private val testImo = IMO_1.toInt()
    private val testMmsi = MMSI_1.toInt()
    private val testEventTime = Instant.parse("2024-04-11T13:15:50Z")
    private val testEventLocation = Location(
        lat = 27.848973333333333,
        lon = -97.05764833333333
    )
    private val testEvents = sequenceOf(
        AreaStartEvent(
            _id = "TEST_EVENT_ID",
            ship = AisShipIdentifier(
                mmsi = testMmsi,
                imo = testImo
            ),
            area = AreaIdentifier(
                id = "62E46DB1FE5D90DDCE6B69E8C679F912B455DA3D.eosp",
                type = AreaIdentifier.AreaType.END_OF_SEA_PASSAGE,
                name = "PORT ARANSAS end of sea passage",
                unlocode = "USTZS"
            ),
            heading = 189,
            draught = 3.1000001f,
            location = testEventLocation,
            actualTime = testEventTime,
            createdTime = Instant.parse("2024-04-11T13:15:50.830Z"),
            berth = null,
            speedOverGround = null
        )
    )

    private val visitDataSource = mock<NewVisitDataSource> {
        whenever(mock.findLastByImo(any(), any(), any(), anyOrNull()))
            .thenReturn(emptyList())
    }
    private val voyageDataSource = mock<NewVoyageDataSource> {
        whenever(mock.findLastByImo(any(), any(), any(), anyOrNull()))
            .thenReturn(emptyList())
    }
    private val esofDataSource = mock<NewESoFDataSource>()
    private val traceDataSource = mock<NewTraceDataSource>()
    private val changesPublisherService = mock<ChangesPublisherService>()
    private val eventFetchingService = mock<EventFetchingService>()
    private val entryProcessingService = mock<EntryProcessingService>()
    private val persistChangesService = mock<PersistChangesService>()
    private val shipStatusService = spy(
        ProcessingShipStatusService(
            visitDataSource = mock(),
            voyageDataSource = mock(),
            newVisitDataSource = visitDataSource,
            newVoyageDataSource = voyageDataSource,
            newESoFDataSource = esofDataSource,
            newShipStatusStateDataSource = mock(),
            meterRegistry = SimpleMeterRegistry()
        )
    )
    private val service = ManualRecalculationService(
        lockService = ImoLockService(),
        shipStatusService = shipStatusService,
        visitDataSource = visitDataSource,
        voyageDataSource = voyageDataSource,
        esofDataSource = esofDataSource,
        traceDataSource = traceDataSource,
        changesPublisherService = changesPublisherService,
        eventFetchingService = eventFetchingService,
        entryProcessingService = entryProcessingService,
        persistChangesService = persistChangesService
    )

    @Test
    fun `should recreate story of ship deleting current data`() {
        val testPreviousVisit = createNewVisit("VISIT_1")
        val testPreviousVoyage = createNewVoyage("VISIT_2")
        val testVisit = createNewVisit("VISIT_3")
        val testVisitEsof = createNewESoF("VISIT_3")
        val testShipStatus = createNewVisitShipStatus(
            visit = testVisit,
            visitEsof = testVisitEsof,
            previousVoyage = testPreviousVoyage,
            previousVisit = testPreviousVisit
        )

        val allEntryIds = listOf(
            testVisit._id,
            testPreviousVisit._id,
            testPreviousVoyage._id
        )
        val emptyInBetweenStatus = NewInitialShipStatus()

        val recalculatedVisit = createNewVisit(_id = "VISIT_6")
        val recalculatedVisitEsof = createNewESoF(_id = "VISIT_6")
        val recalculatedPreviousVoyage = createNewVoyage(_id = "VOYAGE_5")
        val recalculatedPreviousVoyageEsof = createNewESoF(_id = "VISIT_5")
        val recalculatedPreviousVisit = createNewVisit(_id = "VISIT_4")
        val recalculatedPreviousVisitEsof = createNewESoF(_id = "VISIT_4")

        // Mock all interactions where we request data from the database or event history
        whenever(eventFetchingService.fetchAisEngineEventsByIMO(eq(testImo), any(), any()))
            .thenReturn(testEvents)
        whenever(entryProcessingService.processAisEngineEventsDryRunForStoryV2(eq(testEvents)))
            .thenReturn(
                listOf(
                    EntryESoFWrapper(recalculatedVisit, recalculatedVisitEsof),
                    EntryESoFWrapper(recalculatedPreviousVoyage, recalculatedPreviousVoyageEsof),
                    EntryESoFWrapper(recalculatedPreviousVisit, recalculatedPreviousVisitEsof),
                )
            )
        whenever(visitDataSource.findAllByImo(eq(testImo)))
            .thenReturn(listOf(testVisit, testPreviousVisit))
        whenever(visitDataSource.findLastByImo(eq(testImo), eq(2), any(), anyOrNull()))
            .thenReturn(listOf(testVisit, testPreviousVisit))
        whenever(voyageDataSource.findAllByImo(eq(testImo)))
            .thenReturn(listOf(testPreviousVoyage))
        whenever(voyageDataSource.findLastByImo(eq(testImo), eq(2), any(), anyOrNull()))
            .thenReturn(listOf(testPreviousVoyage))
        whenever(esofDataSource.findByIds(eq(allEntryIds)))
            .thenReturn(listOf(testVisitEsof))
        whenever(esofDataSource.findById(eq(testVisitEsof._id)))
            .thenReturn(testVisitEsof)

        // Update the status first so the in-memory status contains our mocked ship status
        shipStatusService.updateStatus(imo = testImo, updatedStatus = testShipStatus)

        service.recreateFullShipStory(testImo)

        val expectedDeleteChanges = listOf(
            VisitChange(action = Action.DELETE, value = testVisit),
            VisitChange(action = Action.DELETE, value = testPreviousVisit),
            VoyageChange(action = Action.DELETE, value = testPreviousVoyage),
            ESoFChange(action = Action.DELETE, value = testVisitEsof)
        )
        val expectedRecalculatedChanges = listOf(
            VisitChange(action = Action.CREATE, value = recalculatedVisit),
            ESoFChange(action = Action.CREATE, value = recalculatedVisitEsof),
            VoyageChange(action = Action.CREATE, value = recalculatedPreviousVoyage),
            ESoFChange(action = Action.CREATE, value = recalculatedPreviousVoyageEsof),
            VisitChange(action = Action.CREATE, value = recalculatedPreviousVisit),
            ESoFChange(action = Action.CREATE, value = recalculatedPreviousVisitEsof)
        )

        verify(changesPublisherService, atLeastOnce())
            .tryPublishNewChanges(
                oldStatus = eq(testShipStatus),
                updatedStatus = eq(emptyInBetweenStatus),
                changes = eq(expectedDeleteChanges)
            )
        verify(visitDataSource).deleteAllByImo(eq(testImo))
        verify(voyageDataSource).deleteAllByImo(eq(testImo))
        verify(esofDataSource).deleteAllByIds(eq(allEntryIds))
        verify(traceDataSource).deleteByEntryIds(eq(allEntryIds))
        verify(shipStatusService).removeStatus(eq(testImo))
        verify(persistChangesService).persistChanges(eq(expectedRecalculatedChanges))
        verify(shipStatusService, times(3)).getStatus(eq(testImo))

        verify(changesPublisherService, atLeastOnce())
            .tryPublishNewChanges(
                oldStatus = eq(emptyInBetweenStatus),
                updatedStatus = any(),
                changes = eq(expectedRecalculatedChanges)
            )
    }

    @Test
    fun `should recreate story of ship not deleting any data when ship is still in initial status`() {
        val testShipStatus = NewInitialShipStatus()

        val recalculatedVisit = createNewVisit(_id = "VISIT_6")
        val recalculatedVisitEsof = createNewESoF(_id = "VISIT_6")
        val recalculatedPreviousVoyage = createNewVoyage(_id = "VOYAGE_5")
        val recalculatedPreviousVoyageEsof = createNewESoF(_id = "VISIT_5")
        val recalculatedPreviousVisit = createNewVisit(_id = "VISIT_4")
        val recalculatedPreviousVisitEsof = createNewESoF(_id = "VISIT_4")

        // Mock all interactions where we request data from the database or event history
        whenever(eventFetchingService.fetchAisEngineEventsByIMO(eq(testImo), any(), any()))
            .thenReturn(testEvents)
        whenever(entryProcessingService.processAisEngineEventsDryRunForStoryV2(eq(testEvents)))
            .thenReturn(
                listOf(
                    EntryESoFWrapper(recalculatedVisit, recalculatedVisitEsof),
                    EntryESoFWrapper(recalculatedPreviousVoyage, recalculatedPreviousVoyageEsof),
                    EntryESoFWrapper(recalculatedPreviousVisit, recalculatedPreviousVisitEsof),
                )
            )

        // Update the status first so the in-memory status contains our mocked ship status
        shipStatusService.updateStatus(imo = testImo, updatedStatus = testShipStatus)

        service.recreateFullShipStory(testImo)

        val expectedRecalculatedChanges = listOf(
            VisitChange(action = Action.CREATE, value = recalculatedVisit),
            ESoFChange(action = Action.CREATE, value = recalculatedVisitEsof),
            VoyageChange(action = Action.CREATE, value = recalculatedPreviousVoyage),
            ESoFChange(action = Action.CREATE, value = recalculatedPreviousVoyageEsof),
            VisitChange(action = Action.CREATE, value = recalculatedPreviousVisit),
            ESoFChange(action = Action.CREATE, value = recalculatedPreviousVisitEsof)
        )

        verify(visitDataSource, never()).deleteAllByImo(any())
        verify(voyageDataSource, never()).deleteAllByImo(any())
        verify(esofDataSource, never()).deleteAllByIds(any())
        verify(traceDataSource, never()).deleteByEntryIds(any())

        verify(shipStatusService).removeStatus(eq(testImo))

        verify(persistChangesService).persistChanges(eq(expectedRecalculatedChanges))
        verify(shipStatusService, times(3)).getStatus(eq(testImo))

        verify(changesPublisherService, atLeastOnce())
            .tryPublishNewChanges(
                oldStatus = eq(testShipStatus),
                updatedStatus = any(),
                changes = eq(expectedRecalculatedChanges)
            )
    }
}
