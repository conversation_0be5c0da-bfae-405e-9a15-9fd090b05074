package nl.teqplay.vesselvoyage.service.recalculation

import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.whenever
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.InterestArea
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.InterestShip
import nl.teqplay.aisengine.reventsengine.model.scenario.Scenario
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioCreateRequest
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioGuarantee
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioGuarantee.VESSEL_VOYAGE_MERGING
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioGuarantee.VESSEL_VOYAGE_MERGING_V2
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioResponse
import nl.teqplay.skeleton.model.TimeWindow
import nl.teqplay.vesselvoyage.model.ReventsRecalculationStatus
import nl.teqplay.vesselvoyage.model.ReventsRecalculationStatus.Guarantee
import nl.teqplay.vesselvoyage.service.recalculation.ReventsConversionService.Companion.VESSEL_VOYAGE
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ReventsConversionServiceTest {

    private val reventsConversionService = ReventsConversionService()

    private val imo = 1234567
    private val pomaPortId = "pomaPortId"
    private val start = Instant.EPOCH
    private val end = start.plus(30, ChronoUnit.DAYS)
    private val timeWindow = TimeWindow(start, end)
    private val user = "piet"

    @ParameterizedTest
    @MethodSource("createScenarioGuarantees")
    fun createScenarioRequestForShip(guarantee: Guarantee, expectedGuarantee: ScenarioGuarantee) {
        val actual = reventsConversionService.createScenarioRequestForShip(imo, timeWindow, guarantee, user)

        val expected = ScenarioCreateRequest(
            window = timeWindow,
            guarantees = setOf(expectedGuarantee),
            interests = listOf(
                InterestShip(ship = InterestShip.Identifier(imo = imo))
            ),
            settings = Scenario.Settings(
                filterHistory = Scenario.FilterHistory.ONLY_REAL_TIME,
            ),
            metaData = Scenario.MetaData(
                createdBy = user,
                requestFromSystem = VESSEL_VOYAGE
            )
        )

        assertThat(actual).isEqualTo(expected)
    }

    @ParameterizedTest
    @MethodSource("createScenarioGuarantees")
    fun createScenarioRequestForPort(guarantee: Guarantee, expectedGuarantee: ScenarioGuarantee) {
        val actual = reventsConversionService.createScenarioRequestForPort(pomaPortId, timeWindow, guarantee, user)

        val expected = ScenarioCreateRequest(
            window = TimeWindow(
                from = start.minus(30, ChronoUnit.DAYS),
                to = end.plus(30, ChronoUnit.DAYS)
            ),
            guarantees = setOf(expectedGuarantee),
            interests = listOf(
                InterestArea(
                    area = InterestArea.Area(
                        type = AreaIdentifier.AreaType.PORT,
                        id = pomaPortId
                    )
                )
            ),
            settings = Scenario.Settings(
                filterHistory = Scenario.FilterHistory.ONLY_REAL_TIME,
            ),
            metaData = Scenario.MetaData(
                createdBy = user,
                requestFromSystem = VESSEL_VOYAGE
            )
        )

        assertThat(actual).isEqualTo(expected)
    }

    private fun createScenarioGuarantees() = Stream.of(
        Arguments.of(Guarantee.V1, VESSEL_VOYAGE_MERGING),
        Arguments.of(Guarantee.V2, VESSEL_VOYAGE_MERGING_V2),
    )

    @ParameterizedTest
    @EnumSource(ScenarioResponse.Phase::class)
    fun convert(reventsPhase: ScenarioResponse.Phase) {
        val response = mock<ScenarioResponse>().apply {
            whenever(id).thenReturn("id")
            whenever(queued).thenReturn(Instant.EPOCH)
            whenever(phase).thenReturn(reventsPhase)
            whenever(window).thenReturn(timeWindow)
        }
        val actual = reventsConversionService.convertToStatus(response)
        val expectedPhase = when (reventsPhase) {
            ScenarioResponse.Phase.QUEUED -> ReventsRecalculationStatus.Phase.QUEUED

            ScenarioResponse.Phase.INITIALIZING,
            ScenarioResponse.Phase.PROGRESSING,
            ScenarioResponse.Phase.POST_PROCESSING -> ReventsRecalculationStatus.Phase.PROGRESSING

            ScenarioResponse.Phase.FINISHED -> ReventsRecalculationStatus.Phase.FINISHED

            ScenarioResponse.Phase.CRASHED,
            ScenarioResponse.Phase.PRUNED,
            ScenarioResponse.Phase.CANCELLED -> ReventsRecalculationStatus.Phase.STOPPED
        }
        assertThat(actual.phase).isEqualTo(expectedPhase)
    }
}
