package nl.teqplay.vesselvoyage.mapper

import com.nhaarman.mockitokotlin2.eq
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.whenever
import nl.teqplay.skeleton.model.Location
import nl.teqplay.vesselvoyage.ApplicationTestConfig
import nl.teqplay.vesselvoyage.apiv2.model.Area
import nl.teqplay.vesselvoyage.apiv2.model.OtherEntryPointer
import nl.teqplay.vesselvoyage.apiv2.model.PassThrough
import nl.teqplay.vesselvoyage.apiv2.model.Port
import nl.teqplay.vesselvoyage.apiv2.model.Stop
import nl.teqplay.vesselvoyage.apiv2.model.Visit
import nl.teqplay.vesselvoyage.logic.DEFAULT_START_TIME
import nl.teqplay.vesselvoyage.model.internal.InfraAreaType
import nl.teqplay.vesselvoyage.model.v2.Destination
import nl.teqplay.vesselvoyage.model.v2.FallbackType
import nl.teqplay.vesselvoyage.model.v2.NewStopType
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.util.Timeline
import nl.teqplay.vesselvoyage.util.component10
import nl.teqplay.vesselvoyage.util.component6
import nl.teqplay.vesselvoyage.util.component7
import nl.teqplay.vesselvoyage.util.component8
import nl.teqplay.vesselvoyage.util.component9
import nl.teqplay.vesselvoyage.util.createAreaActivity
import nl.teqplay.vesselvoyage.util.createLocationTime
import nl.teqplay.vesselvoyage.util.createNewVisit
import nl.teqplay.vesselvoyage.util.createNewVoyage
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Import
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.TestConstructor
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit
import nl.teqplay.vesselvoyage.apiv2.model.Destination as ApiDestination
import nl.teqplay.vesselvoyage.apiv2.model.LocationTime as ApiLocationTime
import nl.teqplay.vesselvoyage.apiv2.model.Visit as ApiVisit
import nl.teqplay.vesselvoyage.apiv2.model.Voyage as ApiVoyage
import nl.teqplay.vesselvoyage.model.lightweight.poma.Port as PomaPort
import nl.teqplay.vesselvoyage.model.v2.LocationTime as InternalLocationTime
import nl.teqplay.vesselvoyage.model.v2.NewStop as InternalStop
import nl.teqplay.vesselvoyage.model.v2.NewVisit as InternalVisit
import nl.teqplay.vesselvoyage.model.v2.NewVoyage as InternalVoyage

@Import(ApplicationTestConfig::class)
@SpringBootTest
@ActiveProfiles("api")
@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
@ContextConfiguration
class EntryV2MapperTest(
    private val entryV2Mapper: EntryV2Mapper,
    private val infraService: InfraService
) {

    @Configuration
    class Config {
        @Bean
        fun infraService(): InfraService = mock()
    }

    private val defaultLocation = Location(0.0, 0.0)
    private val exactly5Minutes = Duration.ofMinutes(5)
    private val lessThan5Minutes = exactly5Minutes.minusSeconds(1)

    @Test
    fun `visit to api`() {
        val timeline = Timeline(timeBetweenEvents = Duration.ofMinutes(10L)) // need higher minimum stop duration
        val now = Instant.now()
        val (time1, time2, time3, time4, time5, time6, time7, time8, time9, time10) = timeline.generateN(10)
        val stopLocation = Location(2.0, 2.0)

        val port: PomaPort = mock()
        whenever(port._id).thenReturn("a1")
        whenever(port.unlocode).thenReturn("NLRTM")
        whenever(infraService.getById(eq("a1"), eq(InfraAreaType.PORT))).thenReturn(port)

        val stop = InternalStop(
            startEventId = "",
            endEventId = "",
            location = stopLocation,
            start = time1,
            end = time2,
            type = NewStopType.BERTH,
            areaId = "a4",
            accuracy = null
        )
        val passThroughEosp = createAreaActivity(start = time3, end = time4, areaId = "a2")
        val passThroughPort = createAreaActivity(start = time5, end = time6, areaId = "a3")
        val source = InternalVisit(
            _id = "entry1",
            imo = 123,
            start = time7,
            end = time10,
            stops = listOf(stop),
            portAreaActivities = mutableListOf(
                createAreaActivity(
                    start = time8,
                    end = time9,
                    areaId = port._id!!
                )
            ),
            passThroughEosp = listOf(passThroughEosp),
            passThroughPort = listOf(passThroughPort),
            destination = Destination(time = now, ais = "NLAMS", actual = "NLAMS"),
            previous = null, // has additional logic, tested in another function
            next = null, // has additional logic, tested in another function
            eospAreaActivity = createAreaActivity(areaId = "a1")
        )

        val expected = ApiVisit(
            entryId = "entry1",
            imo = 123,
            start = entryV2Mapper.toApi(time7),
            end = entryV2Mapper.toApi(time10),
            port = Port("NLRTM", sub = emptyList()),
            portTimes = listOf(
                Visit.PortTime(
                    id = port._id!!,
                    unlocode = port.unlocode,
                    start = entryV2Mapper.toApi(time8),
                    end = entryV2Mapper.toApi(time9)
                )
            ),
            trace = null,
            stop = listOf(
                Stop(
                    start = entryV2Mapper.toApi(time1),
                    end = entryV2Mapper.toApi(time2),
                    location = stopLocation,
                    area = Area("Berth", "a4"),
                    accuracy = null
                )
            ),
            passThrough = listOf(
                // PassThrough.port is not mapped yet!
                PassThrough(port = null, start = entryV2Mapper.toApi(time5), end = entryV2Mapper.toApi(time6))
            ),
            previous = null,
            next = null,
            destination = ApiDestination(time = now, ais = "NLAMS", port = "NLAMS"),
            finished = true
        )

        assertEquals(expected, entryV2Mapper.toApi(source))
    }

    @Test
    fun `voyage to api`() {
        val timeline = Timeline(timeBetweenEvents = Duration.ofMinutes(10L)) // need higher minimum stop duration
        val now = Instant.now()
        val (time1, time2, time3, time4, time5, time6, time7, time8, time9) = timeline.generateN(9)
        val stopLocation = Location(2.0, 2.0)

        val port: PomaPort = mock()
        whenever(port._id).thenReturn("a1")
        whenever(port.unlocode).thenReturn("NLRTM")
        whenever(infraService.getById(eq("a1"), eq(InfraAreaType.PORT))).thenReturn(port)

        val stop = InternalStop(
            startEventId = "",
            endEventId = "",
            location = stopLocation,
            start = time1,
            end = time2,
            type = NewStopType.BERTH,
            areaId = "a4",
            accuracy = null
        )
        val passThroughEosp = createAreaActivity(start = time3, end = time4, areaId = "a2")
        val passThroughPort = createAreaActivity(start = time5, end = time6, areaId = "a3")
        val source = InternalVoyage(
            _id = "entry1",
            imo = 123,
            start = time7,
            end = time8,
            stops = listOf(stop),
            passThroughEosp = listOf(passThroughEosp),
            passThroughPort = listOf(passThroughPort),
            destination = Destination(time = now, ais = "NLRTM", actual = "NLRTM"),
            previous = null, // has additional logic, tested in another function
            next = null, // has additional logic, tested in another function
            actualStart = time9,
            originPort = "BEANR",
            destinationPort = "NLRTM"
        )

        val expected = ApiVoyage(
            entryId = "entry1",
            imo = 123,
            start = entryV2Mapper.toApi(time7),
            end = entryV2Mapper.toApi(time8),
            trace = null,
            stop = listOf(
                Stop(
                    start = entryV2Mapper.toApi(time1),
                    end = entryV2Mapper.toApi(time2),
                    location = stopLocation,
                    area = Area("Berth", "a4"),
                    accuracy = null
                )
            ),
            passThrough = listOf(
                // PassThrough.port is not mapped yet!
                PassThrough(port = null, start = entryV2Mapper.toApi(time5), end = entryV2Mapper.toApi(time6))
            ),
            previous = null,
            next = null,
            destination = ApiDestination(time = now, ais = "NLRTM", port = "NLRTM"),
            finished = true
        )

        assertEquals(expected, entryV2Mapper.toApi(source))
    }

    @Test
    fun `passThrough to api`() {
        val timeline = Timeline()
        val (start, end) = timeline.generatePair()
        val activity = createAreaActivity(start = start, end = end, areaId = "")
        // port is not yet mapped/resolved
        val expected = PassThrough(port = null, start = entryV2Mapper.toApi(start), end = entryV2Mapper.toApi(end))
        assertEquals(expected, entryV2Mapper.passThroughToApi(activity))
    }

    @Test
    fun `location time to api with fallback null`() {
        val location = Location(1.0, 1.0)
        val time = Instant.now()

        assertEquals(
            ApiLocationTime(location, time),
            entryV2Mapper.toApi(InternalLocationTime(location, time, fallback = null))
        )
    }

    @ParameterizedTest
    @EnumSource(FallbackType::class,)
    fun `location time to api with fallback type`(fallbackType: FallbackType) {
        val location = Location(1.0, 1.0)
        val time = Instant.now()

        assertEquals(
            ApiLocationTime(location, time, fallback = fallbackType.name),
            entryV2Mapper.toApi(InternalLocationTime(location, time, fallbackType))
        )
    }

    @Test
    fun determinePrevious() {
        // visit
        assertNull(entryV2Mapper.determinePrevious(createNewVisit(previous = null)))
        assertEquals(
            OtherEntryPointer(port = null, entryId = "previous"),
            entryV2Mapper.determinePrevious(createNewVisit(previous = "previous"))
        )

        // voyage
        assertNull(entryV2Mapper.determinePrevious(createNewVoyage(previous = null)))
        assertEquals(
            OtherEntryPointer(port = null, entryId = "previous"),
            entryV2Mapper.determinePrevious(createNewVoyage(originPort = null, previous = "previous"))
        )

        whenever(infraService.getById(eq("notfound"), eq(InfraAreaType.PORT))).thenReturn(null)
        val port: PomaPort = mock()
        whenever(port.unlocode).thenReturn("NLRTM")
        whenever(infraService.getById(eq("nlrtm"), eq(InfraAreaType.PORT))).thenReturn(port)

        // voyage: expect .eosp to be removed from the origin port before infra service lookup
        // port=null, port not in infraservice
        assertEquals(
            OtherEntryPointer(port = null, entryId = "previous"),
            entryV2Mapper.determinePrevious(createNewVoyage(originPort = "notfound.eosp", previous = "previous"))
        )
        // port=NLRTM: port resolves via infra service
        assertEquals(
            OtherEntryPointer(port = "NLRTM", entryId = "previous"),
            entryV2Mapper.determinePrevious(createNewVoyage(originPort = "nlrtm.eosp", previous = "previous"))
        )
    }

    @Test
    fun determineNext() {
        // visit
        assertNull(entryV2Mapper.determineNext(createNewVisit(next = null)))
        assertEquals(
            OtherEntryPointer(port = null, entryId = "next"),
            entryV2Mapper.determineNext(createNewVisit(next = "next"))
        )

        // voyage
        assertNull(entryV2Mapper.determineNext(createNewVoyage(next = null)))
        assertEquals(
            OtherEntryPointer(port = null, entryId = "next"),
            entryV2Mapper.determineNext(createNewVoyage(destinationPort = null, next = "next"))
        )

        whenever(infraService.getById(eq("notfound"), eq(InfraAreaType.PORT))).thenReturn(null)
        val port: PomaPort = mock()
        whenever(port.unlocode).thenReturn("NLRTM")
        whenever(infraService.getById(eq("nlrtm"), eq(InfraAreaType.PORT))).thenReturn(port)

        // voyage: expect .eosp to be removed from the origin port before infra service lookup
        // port=null, port not in infraservice
        assertEquals(
            OtherEntryPointer(port = null, entryId = "next"),
            entryV2Mapper.determineNext(createNewVoyage(destinationPort = "notfound.eosp", next = "next"))
        )
        // port=NLRTM: port resolves via infra service
        assertEquals(
            OtherEntryPointer(port = "NLRTM", entryId = "next"),
            entryV2Mapper.determineNext(createNewVoyage(destinationPort = "nlrtm.eosp", next = "next"))
        )
    }

    @Test
    fun determineFinished() {
        // visit
        assertTrue(entryV2Mapper.determineFinished(createNewVisit(end = createLocationTime())))
        assertFalse(entryV2Mapper.determineFinished(createNewVisit(end = null)))

        // voyage
        assertTrue(entryV2Mapper.determineFinished(createNewVoyage(end = createLocationTime())))
        assertFalse(entryV2Mapper.determineFinished(createNewVoyage(end = null)))
    }

    @Test
    fun `should map destination to api model`() {
        val destination = Destination(
            time = DEFAULT_START_TIME.toInstant(),
            ais = "test",
            actual = "NLRTM"
        )

        val result = entryV2Mapper.toApi(destination)
        val expected = ApiDestination(
            time = DEFAULT_START_TIME.toInstant(),
            ais = "test",
            port = "NLRTM"
        )

        assertEquals(expected, result)
    }

    @Test
    fun `toApi - stop - simple transformation`() {
        val startTime2 = Instant.EPOCH.plus(15, ChronoUnit.MINUTES)
        val endTime2 = Instant.EPOCH.plus(20, ChronoUnit.MINUTES)

        val stops = listOf(
            createInternalStop(),
            createInternalStop(startTime = startTime2, endTime = endTime2),
        )
        val apiStops = entryV2Mapper.toApi(stops)
        val expectedStops = listOf(
            createApiStop(),
            createApiStop(startTime = startTime2, endTime = endTime2),
        )
        assertEquals(expectedStops, apiStops)
    }

    @Test
    fun `toApi - stop - remove stops taking less than 5 minutes`() {
        val stops = listOf(
            createInternalStop(
                startTime = Instant.EPOCH,
                endTime = Instant.EPOCH.plus(lessThan5Minutes)
            )
        )

        val apiStops = entryV2Mapper.toApi(stops)
        assertEquals(emptyList<Stop>(), apiStops)
    }

    @Test
    fun `toApi - stop - merge nearby-in-time stops`() {
        val startTime1 = Instant.EPOCH
        val endTime1 = startTime1.plus(exactly5Minutes)
        val startTime2 = endTime1.plus(lessThan5Minutes)
        val endTime2 = startTime2.plus(exactly5Minutes)

        val otherLocation = Location(1.0, 1.0)
        val stops = listOf(
            createInternalStop(
                startEventId = "firstStart",
                startTime = startTime1,
                endTime = endTime1
            ),
            createInternalStop(
                endEventId = "lastEnd",
                startTime = startTime2,
                endTime = endTime2,
                location = otherLocation
            )
        )

        val apiStops = entryV2Mapper.toApi(stops)
        val expectedStops = listOf(
            createApiStop(
                startTime = startTime1,
                endTime = endTime2,
                location = otherLocation
            )
        )
        assertEquals(expectedStops, apiStops)
    }

    @Test
    fun `toApi - stop - merge nearby-in-time stops, unless different area types`() {
        val startTime1 = Instant.EPOCH
        val endTime1 = startTime1.plus(exactly5Minutes)
        val startTime2 = endTime1.plus(lessThan5Minutes)
        val endTime2 = startTime2.plus(exactly5Minutes)

        val otherLocation = Location(1.0, 1.0)
        val stops = listOf(
            createInternalStop(
                startEventId = "firstStart",
                startTime = startTime1,
                endTime = endTime1,
                type = NewStopType.BERTH,
                areaId = null
            ),
            createInternalStop(
                endEventId = "lastEnd",
                startTime = startTime2,
                endTime = endTime2,
                location = otherLocation,
                type = NewStopType.ANCHOR_AREA,
                areaId = null
            )
        )

        val apiStops = entryV2Mapper.toApi(stops)
        val expectedStops = listOf(
            createApiStop(
                startTime = startTime1,
                endTime = endTime1,
                location = defaultLocation,
                area = Area("Berth", null)
            ),
            createApiStop(
                startTime = startTime2,
                endTime = endTime2,
                location = otherLocation,
                area = Area("Anchor", null)
            )
        )
        assertEquals(expectedStops, apiStops)
    }

    @Test
    fun `toApi - stop - merge nearby-in-time stops, unless different area IDs`() {
        val startTime1 = Instant.EPOCH
        val endTime1 = startTime1.plus(exactly5Minutes)
        val startTime2 = endTime1.plus(lessThan5Minutes)
        val endTime2 = startTime2.plus(exactly5Minutes)

        val otherLocation = Location(1.0, 1.0)
        val stops = listOf(
            createInternalStop(
                startEventId = "firstStart",
                startTime = startTime1,
                endTime = endTime1,
                type = NewStopType.BERTH,
                areaId = "ID1"
            ),
            createInternalStop(
                endEventId = "lastEnd",
                startTime = startTime2,
                endTime = endTime2,
                location = otherLocation,
                type = NewStopType.BERTH,
                areaId = "ID2"
            )
        )

        val apiStops = entryV2Mapper.toApi(stops)
        val expectedStops = listOf(
            createApiStop(
                startTime = startTime1,
                endTime = endTime1,
                location = defaultLocation,
                area = Area("Berth", "ID1")
            ),
            createApiStop(
                startTime = startTime2,
                endTime = endTime2,
                location = otherLocation,
                area = Area("Berth", "ID2")
            )
        )
        assertEquals(expectedStops, apiStops)
    }

    @Test
    fun `toApi - stop - merge nearby-in-time stops, don't remove too short stops before merging`() {
        val startTime1 = Instant.EPOCH
        val endTime1 = startTime1.plus(lessThan5Minutes)
        val startTime2 = endTime1.plus(lessThan5Minutes)
        val endTime2 = startTime2.plus(lessThan5Minutes)

        val otherLocation = Location(1.0, 1.0)
        val stops = listOf(
            createInternalStop(
                startEventId = "firstStart",
                startTime = startTime1,
                endTime = endTime1
            ),
            createInternalStop(
                endEventId = "lastEnd",
                startTime = startTime2,
                endTime = endTime2,
                location = otherLocation
            )
        )

        val apiStops = entryV2Mapper.toApi(stops)
        val expectedStops = listOf(
            createApiStop(
                startTime = startTime1,
                endTime = endTime2,
                location = otherLocation
            )
        )
        assertEquals(expectedStops, apiStops)
    }

    private fun createInternalStop(
        startEventId: String = "start",
        endEventId: String = "end",
        location: Location? = defaultLocation,
        startTime: Instant = Instant.EPOCH,
        endTime: Instant? = Instant.EPOCH.plus(exactly5Minutes),
        type: NewStopType = NewStopType.UNCLASSIFIED,
        areaId: String? = null,
    ) = InternalStop(
        startEventId = startEventId,
        endEventId = endEventId,
        location = location,
        start = InternalLocationTime(defaultLocation, startTime),
        end = endTime?.let { InternalLocationTime(defaultLocation, it) },
        type = type,
        areaId = areaId,
        accuracy = null,
    )

    private fun createApiStop(
        startTime: Instant = Instant.EPOCH,
        endTime: Instant? = Instant.EPOCH.plus(exactly5Minutes),
        location: Location? = defaultLocation,
        area: Area = Area("Unclassified", null)
    ) = Stop(
        start = ApiLocationTime(defaultLocation, startTime),
        end = endTime?.let { ApiLocationTime(defaultLocation, it) },
        location = location,
        area = area,
        accuracy = null,
    )
}
