package nl.teqplay.vesselvoyage.mapper

import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polyline
import nl.teqplay.vesselvoyage.model.v2.NewTrace
import nl.teqplay.vesselvoyage.model.v2.Speed
import nl.teqplay.vesselvoyage.model.v2.TraceDistance
import nl.teqplay.vesselvoyage.model.v2.TraceStatistic
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import java.time.Duration
import java.time.Instant

class TraceMapperTest {

    private val mapper = TraceMapperImpl()
    private val polyline1 = Polyline.of(
        listOf(
            Location(0.0, 0.0),
            Location(0.0, 1.0),
            Location(1.0, 0.0),
            Location(1.0, 1.0)
        )
    )
    private val polyline2 = Polyline.of(
        listOf(
            Location(2.0, 2.0),
            Location(2.0, 3.0),
            Location(3.0, 2.0),
            Location(3.0, 3.0)
        )
    )
    private val combinedPolyline = Polyline("???_ibE_ibE~hbE?_ibE_ibE_ibE?_ibE_ibE~hbE?_ibE", Location(3.0, 3.0))

    private val trace = NewTrace(
        _id = "id",
        simplifiedPolyline = polyline1,
        polyline = polyline2,
        totalOngoingTraceItems = 1,
        speed = Speed(
            min = 1.1f,
            avg = 2.1f,
            max = 3.1f,
            count = 5,
            lastSpeedOverGround = 3.1f,
            duration = Duration.ofSeconds(60),
            lastSpeedOverGroundTimestamp = Instant.now()
        ),
        distance = TraceDistance(
            distanceMeters = 1_234,
            lastLocation = polyline2.lastLocation
        ),
        draught = TraceStatistic(
            min = 4.1f,
            avg = 5.1f,
            max = 6.1f,
            last = 6.1f,
            duration = Duration.ofSeconds(60),
            lastTimestamp = Instant.now()
        )
    )

    @Test
    fun mapsFieldsCorrectly() {
        // note that 'speed' and 'draught' use different numbers to make sure they're correctly mapped
        val actual = mapper.toApi(trace)
        assertEquals("id", actual.entryId)
        assertEquals(1.1f, actual.speedMin)
        assertEquals(2.1f, actual.speedAvg)
        assertEquals(3.1f, actual.speedMax)
        assertEquals(1234, actual.distanceMeters)
        assertEquals(4.1f, actual.draughtMin)
        assertEquals(5.1f, actual.draughtAvg)
        assertEquals(6.1f, actual.draughtMax)
    }

    @Test
    fun mapsNullableFieldsCorrectly() {
        // note that 'speed' and 'draught' use different numbers to make sure they're correctly mapped
        val actual = mapper.toApi(trace.copy(speed = null, distance = null, draught = null))
        assertNull(actual.speedMin)
        assertNull(actual.speedAvg)
        assertNull(actual.speedMax)
        assertNull(actual.distanceMeters)
        assertNull(actual.draughtMin)
        assertNull(actual.draughtAvg)
        assertNull(actual.draughtMax)
    }

    @Test
    fun combinePolyline() {
        val trace = NewTrace(
            _id = "id",
            simplifiedPolyline = polyline1,
            polyline = polyline2,
            totalOngoingTraceItems = 1,
            speed = null,
            distance = null,
            draught = null
        )
        println("combined: ${combinedPolyline.encoded}")
        println("mapper: ${combinedPolyline.encoded}")
        assertEquals(combinedPolyline, mapper.combinePolyline(trace))

        // no simplified polyline
        assertEquals(polyline2, mapper.combinePolyline(trace.copy(simplifiedPolyline = null)))
    }
}
