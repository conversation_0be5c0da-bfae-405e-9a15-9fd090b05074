package nl.teqplay.vesselvoyage.mapper

import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.whenever
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType
import nl.teqplay.csi.model.ship.info.component.ShipCategories
import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2
import nl.teqplay.poma.api.v1.CargoCategoryType
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.AreaMeta
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.EncounterMeta
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.Pilot
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.Ship
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.Tug
import nl.teqplay.vesselvoyage.logic.IMO_1
import nl.teqplay.vesselvoyage.logic.MMSI_1
import nl.teqplay.vesselvoyage.logic.createAnchorage
import nl.teqplay.vesselvoyage.logic.createApproachArea
import nl.teqplay.vesselvoyage.logic.createBerth
import nl.teqplay.vesselvoyage.logic.createLock
import nl.teqplay.vesselvoyage.logic.createPort
import nl.teqplay.vesselvoyage.logic.createShipToShipArea
import nl.teqplay.vesselvoyage.logic.createTerminal
import nl.teqplay.vesselvoyage.model.ShipDetails
import nl.teqplay.vesselvoyage.model.esof.ptoview.AnchorStopInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.BerthVisitInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.EncounterInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.PilotInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.PortAreaInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.TerminalVisitInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.TugInfo
import nl.teqplay.vesselvoyage.model.lightweight.poma.PilotBoardingPlace
import nl.teqplay.vesselvoyage.model.v2.NewEncounter
import nl.teqplay.vesselvoyage.model.v2.NewStopType
import nl.teqplay.vesselvoyage.util.PtoSofHelperFunctions.createBerthVisitInfo
import nl.teqplay.vesselvoyage.util.PtoSofHelperFunctions.createTerminalVisitInfo
import nl.teqplay.vesselvoyage.util.Timeline
import nl.teqplay.vesselvoyage.util.createAreaActivity
import nl.teqplay.vesselvoyage.util.createLocationTime
import nl.teqplay.vesselvoyage.util.createNewEncounter
import nl.teqplay.vesselvoyage.util.createNewStop
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource
import java.time.Instant
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class PtoStatementOfFactsMapperTest {
    private val mapper = PtoStatementOfFactsMapperImpl()
    private val entryMapper = EntryV2MapperImpl()

    private val start = createLocationTime(time = Instant.parse("2024-07-05T14:00:00Z"))
    private val end = createLocationTime(time = Instant.parse("2024-07-05T18:00:00Z"))

    @Test
    fun toBerthVisit() {
        val berth = createBerth(cargoCategoryType = setOf(CargoCategoryType.CONTAINER))
        val port = createPort()
        val portArea = PortAreaInfo(
            ref = 1,
            isPassThrough = false,
            createAreaActivity(start = start, end = end, areaId = ""),
            area = port
        )
        val firstLineSecured = createLocationTime(time = Instant.parse("2024-07-05T14:15:00Z"))
        val allFast = createLocationTime(time = Instant.parse("2024-07-05T14:30:00Z"))
        val lastLineReleased = createLocationTime(time = Instant.parse("2024-07-05T14:45:00Z"))
        val terminalVisit = TerminalVisitInfo(
            ref = 3,
            activity = createAreaActivity(start = start, end = end, areaId = ""),
            mooringActivity = null,
            area = createTerminal(),
            portArea = portArea,
            berthVisits = emptyList()
        )
        val info = BerthVisitInfo(
            id = "BERTH_STOP_TEST_ID",
            ref = 2,
            activity = createAreaActivity(start = start, end = end, areaId = berth._id!!),
            area = berth,
            portArea = portArea,
            arrivalTugs = emptyList(),
            departureTugs = emptyList(),
            firstLineSecured = firstLineSecured,
            allFast = allFast,
            lastLineReleased = lastLineReleased,
            terminalVisit = terminalVisit
        )
        val result = mapper.toBerthVisit(info)
        assertEquals("BERTH_STOP_TEST_ID", result.id)
        assertEquals(2, result.ref)
        assertEquals(1, result.portAreaRef)
        assertEquals(start.time, result.start.time)
        assertEquals(end.time, result.end?.time)
        assertEquals(firstLineSecured.time, result.firstLineSecured?.time)
        assertEquals(allFast.time, result.allFast?.time)
        assertEquals(lastLineReleased.time, result.lastLineReleased?.time)
        assertEquals(3, result.terminalVisitRef)
        // not checking AreaMeta as it is tested separately
        // TODO: verify ship type, cargoCategoryType, mooringType
        // tugs are tested in a separate method
    }

    @Test
    fun `toAreaMeta - port`() {
        val port = createPort(_id = "123", unlocode = "NLRTM", name = "PORT OF ROTTERDAM")
        assertEquals(
            AreaMeta(areaId = "123", unlocode = "NLRTM", name = "PORT OF ROTTERDAM", type = "port"),
            mapper.toAreaMeta(port)
        )
    }

    @Test
    fun `toAreaMeta - berth`() {
        val port = createBerth(_id = "123", name = "BERTH OF ROTTERDAM")
        assertEquals(
            AreaMeta(areaId = "123", unlocode = null, name = "BERTH OF ROTTERDAM", type = "berth"),
            mapper.toAreaMeta(port)
        )
    }

    @Test
    fun toTug() {
        val tugInfo = TugInfo(
            id = "0.TUG_START_EVENT_ID",
            ref = 0,
            encounter = NewEncounter(
                type = EncounterType.TUG,
                otherMmsi = 123,
                otherImo = 456,
                startEventId = "TUG_START_EVENT_ID",
                start = createLocationTime(time = Instant.parse("2024-07-05T14:15:00Z")),
                end = createLocationTime(time = Instant.parse("2024-07-05T14:30:00Z"))
            )
        )
        assertEquals(
            Tug(
                id = "0.TUG_START_EVENT_ID",
                ship = EncounterMeta(
                    imo = tugInfo.encounter.otherImo,
                    mmsi = tugInfo.encounter.otherMmsi,
                    type = EncounterType.TUG.name
                ),
                start = entryMapper.toApi(tugInfo.start),
                end = entryMapper.toApi(tugInfo.end!!)
            ),
            mapper.toTug(tugInfo)
        )
    }

    @Test
    fun tugToEncounterMeta() {
        val tugInfo = TugInfo(
            id = "0.",
            ref = 0,
            encounter = NewEncounter(
                type = EncounterType.TUG,
                otherMmsi = 123,
                otherImo = 456,
                startEventId = "",
                start = createLocationTime(time = Instant.parse("2024-07-05T14:15:00Z")),
                end = createLocationTime(time = Instant.parse("2024-07-05T14:30:00Z"))
            )
        )
        assertEquals(
            EncounterMeta(
                imo = tugInfo.encounter.otherImo,
                mmsi = tugInfo.encounter.otherMmsi,
                type = EncounterType.TUG.name
            ),
            mapper.tugToEncounterMeta(tugInfo)
        )
    }

    @Test
    fun toTerminalVisit() {
        val terminal = createTerminal()
        val port = createPort()
        val portArea = PortAreaInfo(
            ref = 1,
            isPassThrough = false,
            createAreaActivity(start = start, end = end, areaId = ""),
            area = port
        )
        val mooringStart = start.copy(time = start.time.plusSeconds(60))
        val mooringEnd = end.copy(time = end.time.plusSeconds(60))
        val info = TerminalVisitInfo(
            ref = 0,
            activity = createAreaActivity(start = start, end = end, areaId = terminal._id!!),
            mooringActivity = createAreaActivity(
                start = mooringStart,
                end = mooringEnd,
                areaId = "${terminal._id!!}.mooringarea"
            ),
            area = terminal,
            portArea = portArea,
            berthVisits = emptyList() // not testing berth visits, other methods are testing this
        )
        val result = mapper.toTerminalVisit(info)
        assertEquals(0, result.ref)
        assertEquals(1, result.portAreaRef)
        assertEquals(start.time, result.start.time)
        assertEquals(end.time, result.end!!.time)
        assertEquals(mooringStart.time, result.mooringStart!!.time)
        assertEquals(mooringEnd.time, result.mooringEnd!!.time)
    }

    @Test
    fun toAnchorStop() {
        val timeline = Timeline()
        val portArea = PortAreaInfo(
            ref = 1,
            isPassThrough = false,
            createAreaActivity(
                id = "PORT_AREA_START_EVENT_ID",
                start = start,
                end = end,
                areaId = ""
            ),
            area = createPort()
        )
        val (start, end) = timeline.generatePair()
        val stopInfo = AnchorStopInfo(
            id = "0.ANCHOR_STOP_START_EVENT_ID",
            stop = createNewStop(
                startEventId = "ANCHOR_STOP_START_EVENT_ID",
                type = NewStopType.ANCHOR_AREA,
                start = start,
                end = end
            ),
            area = createAnchorage(),
            portArea = portArea
        )
        val result = mapper.toAnchorStop(stopInfo)

        assertEquals("0.ANCHOR_STOP_START_EVENT_ID", result.id)
        assertEquals("PORT_AREA_START_EVENT_ID", result.portVisitId)
        assertEquals(1, result.portAreaRef)
        assertEquals(start.time, result.start.time)
        assertEquals(end.time, result.end!!.time)
        assertEquals("anchorage", result.area?.type)
    }

    private fun generateEncounterArguments(): Stream<Arguments> = EncounterType.values().map { encounterType ->
        val timeline = Timeline()
        val activity = createAreaActivity(start = timeline.generate(), end = timeline.generate(), areaId = "")
        val encounter = NewEncounter(
            type = encounterType,
            otherMmsi = 1,
            otherImo = 2,
            startEventId = "",
            start = timeline.generate(),
            end = timeline.generate()
        )
        Arguments.of(
            EncounterInfo(
                id = "0.",
                encounter = encounter,
                portAreaInfo = PortAreaInfo(
                    ref = 1,
                    isPassThrough = false,
                    createAreaActivity(start = start, end = end, areaId = ""),
                    area = createPort()
                ),
                berthVisitInfo = createBerthVisitInfo(id = "", ref = 2, activity),
                terminalVisitInfo = createTerminalVisitInfo(ref = 3, activity)
            )
        )
    }.stream()

    @ParameterizedTest
    @MethodSource("generateEncounterArguments")
    fun toEncounter(encounterInfo: EncounterInfo) {

        val encounter = encounterInfo.encounter
        val result = mapper.toEncounter(encounterInfo)

        assertEquals(encounter.type.name, result.type)
        assertEquals(encounter.start.time, result.start.time)
        assertEquals(encounter.end?.time, result.end?.time)
        assertEquals(encounter.otherMmsi, result.ship.mmsi)
        assertEquals(encounter.otherImo, result.ship.imo)
        assertEquals(1, result.portAreaRef)
        assertEquals(2, result.berthVisitRef)
        assertEquals(3, result.terminalVisitRef)
    }

    @Test
    fun toPilot() {
        val timeline = Timeline()
        val (start, end) = timeline.generatePair()
        val pbp: PilotBoardingPlace = mock()
        whenever(pbp._id).thenReturn("pbp-1")
        whenever(pbp.name).thenReturn("test")
        val info = PilotInfo.fromPilotShipEncounter(
            visitId = "0",
            encounter = createNewEncounter(
                startEventId = "PILOT_START_EVENT_ID",
                type = EncounterType.PILOT,
                otherImo = IMO_1.toInt(),
                otherMmsi = MMSI_1.toInt(),
                start = start,
                end = end
            ),
            pilotArea = pbp
        )
        val expected = Pilot(
            id = "0.PILOT_START_EVENT_ID",
            start = entryMapper.toApi(start),
            end = entryMapper.toApi(end),
            ship = EncounterMeta(imo = IMO_1.toInt(), mmsi = MMSI_1.toInt(), type = EncounterType.PILOT.name),
            area = AreaMeta(areaId = "pbp-1", unlocode = null, name = "test", type = "pilotBoardingPlace")
        )
        assertEquals(
            expected,
            mapper.toPilot(info)
        )
    }

    @Test
    fun `toShip with nulls`() {
        val source = ShipDetails(
            mmsi = "",
            imo = null,
            name = null,
            type = null,
            categories = null,
            length = null,
            beam = null,
            maxDraught = null,
            dwt = null
        )

        assertEquals(
            Ship(name = null, imo = 0, type = null),
            mapper.toShip(source)
        )
    }

    @ParameterizedTest
    @EnumSource(ShipCategoryV2::class)
    fun toShip(shipCategoryV2: ShipCategoryV2) {
        val name = "Boat"
        val imo = "1234567"
        val imoInt = 1234567
        val source = ShipDetails(
            mmsi = "",
            imo = imo,
            name = name,
            type = null,
            categories = ShipCategories(v1 = null, v2 = shipCategoryV2),
            length = null,
            beam = null,
            maxDraught = null,
            dwt = null
        )

        assertEquals(
            Ship(name = name, imo = imoInt, type = shipCategoryV2),
            mapper.toShip(source)
        )
    }

    @Test
    fun `toAreaMeta - lock`() {
        val name = "BOUDEWIJNSLUIS"
        val lock = createLock(_id = "123", name = name)
        assertEquals(
            AreaMeta(areaId = "123", unlocode = null, name = name, type = "lock"),
            mapper.toAreaMeta(lock)
        )
    }

    @Test
    fun `toAreaMeta - approach area`() {
        val name = "LAGELICHT"
        val approachArea = createApproachArea(_id = "123", name = name)
        assertEquals(
            AreaMeta(areaId = "123", unlocode = null, name = name, type = "approachArea"),
            mapper.toAreaMeta(approachArea)
        )
    }

    @Test
    fun `toAreaMeta - shiptoship area`() {
        val name = "SHIPTOSHIP POCCA 1"
        val shipToShip = createShipToShipArea(_id = "area51", name = name)
        assertEquals(
            AreaMeta(areaId = "area51", unlocode = null, name = name, type = "shipToShip"),
            mapper.toAreaMeta(shipToShip)
        )
    }
}
