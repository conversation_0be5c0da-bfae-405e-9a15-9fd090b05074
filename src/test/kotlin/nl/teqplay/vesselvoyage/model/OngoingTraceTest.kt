package nl.teqplay.vesselvoyage.model

import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import java.time.ZonedDateTime

class OngoingTraceTest {
    @Test
    fun `should insert a new location at the right position, ordered`() {
        val location1 = LocationTime(latitude = 0.0, longitude = 0.0, time = ZonedDateTime.parse("2021-06-29T01:00:00Z"))
        val location2 = LocationTime(latitude = 0.0, longitude = 0.0, time = ZonedDateTime.parse("2021-06-29T02:00:00Z"))
        val location3 = LocationTime(latitude = 0.0, longitude = 0.0, time = ZonedDateTime.parse("2021-06-29T03:00:00Z"))
        val location3b = location3.copy(latitude = 1.0, longitude = 1.0)
        val location4 = LocationTime(latitude = 0.0, longitude = 0.0, time = ZonedDateTime.parse("2021-06-29T04:00:00Z"))
        val location5 = LocationTime(latitude = 0.0, longitude = 0.0, time = ZonedDateTime.parse("2021-06-29T05:00:00Z"))

        // helper function
        fun createOngoingTrace(vararg locations: LocationTime) = OngoingTrace(
            _id = "1",
            imo = "IMO",
            mmsi = "MMSI",
            locations = locations.toMutableList(),
            minTime = locations.firstOrNull()?.time,
            maxTime = locations.lastOrNull()?.time,
            simplifiedCount = 0
        )

        // insert in the start
        val ongoingTraceA = createOngoingTrace(location2, location4)
        ongoingTraceA.insert(location1)
        Assertions.assertEquals(createOngoingTrace(location1, location2, location4), ongoingTraceA)

        // insert in the middle
        val ongoingTraceB = createOngoingTrace(location2, location4)
        ongoingTraceB.insert(location3)
        Assertions.assertEquals(createOngoingTrace(location2, location3, location4), ongoingTraceB)

        // insert after when there is an equal time
        val ongoingTraceB2 = createOngoingTrace(location2, location3, location4)
        ongoingTraceB2.insert(location3b)
        Assertions.assertEquals(createOngoingTrace(location2, location3, location3b, location4), ongoingTraceB2)

        // insert in at the end
        val ongoingTraceC = createOngoingTrace(location2, location4)
        ongoingTraceC.insert(location5)
        Assertions.assertEquals(createOngoingTrace(location2, location4, location5), ongoingTraceC)

        // insert in empty list
        val ongoingTraceD = createOngoingTrace()
        ongoingTraceD.insert(location5)
        Assertions.assertEquals(createOngoingTrace(location5), ongoingTraceD)
    }
}
