package nl.teqplay.vesselvoyage.model

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class EntryTest {
    @Test
    fun `should create a visit id from an event id`() {
        assertEquals("123.VISIT", createVisitId("123"))
    }

    @Test
    fun `should create a voyage id from an event id`() {
        assertEquals("123.VOYAGE", createVoyageId("123"))
    }

    @Test
    fun `should test whether an id is from a visit`() {
        assertTrue(isVisitId("123.VISIT"))
        assertFalse(isVisitId("123.VOYAGE"))
        assertFalse(isVisitId("123"))
    }

    @Test
    fun `should test whether an id is from a voyage`() {
        assertTrue(isVoyageId("123.VOYAGE"))
        assertFalse(isVoyageId("123.VISIT"))
        assertFalse(isVoyageId("123"))
    }
}
