package nl.teqplay.vesselvoyage.model.v2

import nl.teqplay.skeleton.model.Location
import nl.teqplay.vesselvoyage.model.v2.StartEndTest.StartEndImpl
import nl.teqplay.vesselvoyage.util.Timeline
import nl.teqplay.vesselvoyage.util.contains
import nl.teqplay.vesselvoyage.util.minus
import nl.teqplay.vesselvoyage.util.overlaps
import nl.teqplay.vesselvoyage.util.plus
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.time.Instant
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class StartEndTest {
    data class StartEndImpl(override val start: LocationTime, override val end: LocationTime?) : StartEnd

    private val outerStartTime = Instant.ofEpochSecond(15)
    private val outerEndTime = Instant.ofEpochSecond(60)

    private val outerStart = LocationTime(Location(0.0, 0.0), outerStartTime)
    private val outerEnd = LocationTime(Location(0.0, 0.0), outerEndTime)

    private val outer = StartEndImpl(outerStart, outerEnd)
    private val outerOnlyStart = StartEndImpl(outerStart, end = null)

    private fun containsInstant(): Stream<Arguments> {
        return Stream.of(
            // check contains start time
            Arguments.of(outer, outerStartTime - 1, false),
            Arguments.of(outer, outerStartTime, true),
            Arguments.of(outer, outerStartTime + 1, true),
            Arguments.of(outerOnlyStart, outerStartTime - 1, false),
            Arguments.of(outerOnlyStart, outerStartTime, true),
            Arguments.of(outerOnlyStart, outerStartTime + 1, true),

            // check end exclusive
            Arguments.of(outer, outerEndTime - 1, true),
            Arguments.of(outer, outerEndTime, false),
            Arguments.of(outer, outerEndTime + 1, false),

            // when outer.end is not set, everything after start is considered 'contained'
            Arguments.of(outerOnlyStart, outerEndTime - 1, true),
            Arguments.of(outerOnlyStart, outerEndTime, true),
            Arguments.of(outerOnlyStart, outerEndTime + 1, true)
        )
    }

    @ParameterizedTest
    @MethodSource("containsInstant")
    fun `should correctly contains instant`(outer: StartEnd, time: Instant, expected: Boolean) {
        val result = outer.contains(time)
        assertEquals(expected, result)
    }

    private fun containsLocationTime(): Stream<Arguments> {
        return Stream.of(
            // check contains start time
            // ---[ foo ]---
            // --i----------
            // ---i---------
            // ----i--------
            Arguments.of(outer, outerStart - 1, false),
            Arguments.of(outer, outerStart, true),
            Arguments.of(outer, outerStart + 1, true),

            // check contains start time
            // ---[ foo  (no .end)
            // --i|---------
            // ---i---------
            // ---|i--------
            Arguments.of(outerOnlyStart, outerStart - 1, false),
            Arguments.of(outerOnlyStart, outerStart, true),
            Arguments.of(outerOnlyStart, outerStart + 1, true),

            // check contains start time
            // ---[ foo ]---
            // --------i|---
            // ---------i---
            // ---------|i--
            Arguments.of(outer, outerEnd - 1, true),
            Arguments.of(outer, outerEnd, false),
            Arguments.of(outer, outerEnd + 1, false),

            // when outer.end is not set, everything after start is considered 'contained'
            // check contains
            // ---[ foo (no .end)
            // --------i----
            // ---------i---
            // ----------i--
            Arguments.of(outerOnlyStart, outerEnd - 1, true),
            Arguments.of(outerOnlyStart, outerEnd, true),
            Arguments.of(outerOnlyStart, outerEnd + 1, true)
        )
    }

    @ParameterizedTest
    @MethodSource("containsLocationTime")
    fun `should correctly contains location time`(outer: StartEnd, locationTime: LocationTime, expected: Boolean) {
        val result = outer.contains(locationTime)
        assertEquals(expected, result)
    }

    @Test
    fun containsStartEnd() {
        // [inner] is totally outside [outer], should fail
        // -----------[outer]---------
        // -[inner]-------------------
        // -------------------[inner]-
        assertFalse(outer.contains(StartEndImpl(start = outerStart - 150, end = outerEnd - 150)))
        assertFalse(outer.contains(StartEndImpl(start = outerStart + 150, end = outerEnd + 150)))

        // [inner] is overlaps [outer], should fail
        // -----------[outer]---------
        // -------[inner]-------------
        // --------------[inner]------
        assertFalse(outer.contains(StartEndImpl(start = outerStart - 20, end = outerEnd - 20)))
        assertFalse(outer.contains(StartEndImpl(start = outerStart + 20, end = outerEnd + 20)))

        // [inner] contains [outer]
        // --[  outer  ]--
        // --[inner]---|-- -> true, start inclusive and inside
        // --|-[inner]-|-- -> true, inside outer
        // --|---[inner]-- -> false, inner.end == outer.end, end exclusive means inner.end should be < than outer.end
        // --[  inner  ]-- -> false, inner == outer, meaning inner is not inside outer
        assertTrue(outer.contains(StartEndImpl(start = outerStart, end = outerEnd - 5)))
        assertTrue(outer.contains(StartEndImpl(start = outerStart + 5, end = outerEnd - 5)))
        assertFalse(outer.contains(StartEndImpl(start = outerStart + 5, end = outerEnd)))
        assertFalse(outer.contains(StartEndImpl(start = outerStart, end = outerEnd)))
    }

    private fun ongoingScenarios(): Stream<Arguments> {
        val ongoingOuter = outer.copy(end = null)
        val inner = StartEndImpl(start = outerStart + 5, end = outerEnd - 5)
        val ongoingInner = StartEndImpl(start = outerStart + 5, end = null)
        assertTrue(ongoingOuter.contains(inner))
        assertTrue(ongoingOuter.contains(ongoingInner))
        assertFalse(outer.contains(ongoingInner))

        return Stream.of(
            Arguments.of(ongoingOuter, inner, true),
            Arguments.of(ongoingOuter, ongoingInner, true),
            Arguments.of(outer, ongoingInner, false)
        )
    }

    @ParameterizedTest
    @MethodSource("ongoingScenarios")
    fun `should correctly contains ongoing`(outer: StartEnd, inner: StartEnd, expected: Boolean) {
        val result = outer.contains(inner)
        assertEquals(expected, result)
    }

    @Test
    fun overlaps() {
        val timeline = Timeline()

        // overlaps: true
        // 1     2
        // [  a  ]
        // [  b  ]
        run {
            val (one, two) = timeline.generateN(2)
            val a = SimpleStartEnd(one, two)
            val b = a.copy()
            assertTrue(a.overlaps(b))
            assertTrue(b.overlaps(a))
        }

        // overlaps: true
        // 1  2  3  4
        //    [  a  ]
        // [  b  ]
        run {
            val (one, two, three, four) = timeline.generateN(4)
            val a = SimpleStartEnd(two, four)
            val b = SimpleStartEnd(one, three)
            assertTrue(a.overlaps(b))
            assertTrue(b.overlaps(a))
        }

        // overlaps: true
        // 1  2  3  4
        // [  a  ]
        //    [  b  ]
        run {
            val (one, two, three, four) = timeline.generateN(4)
            val a = SimpleStartEnd(one, three)
            val b = SimpleStartEnd(two, four)
            assertTrue(a.overlaps(b))
            assertTrue(b.overlaps(a))
        }

        // overlaps: false (a is end exclusive)
        // 1  2  3  4
        // [  a  ]
        //       [  b  ]
        run {
            val (one, two, three) = timeline.generateN(3)
            val a = SimpleStartEnd(one, two)
            val b = SimpleStartEnd(two, three)
            assertFalse(a.overlaps(b))
            assertFalse(b.overlaps(a))
        }

        // overlaps: false
        // 1     2  3     4
        // [  a  ]
        //          [  b  ]
        run {
            val (one, two, three, four) = timeline.generateN(4)
            val a = SimpleStartEnd(one, two)
            val b = SimpleStartEnd(three, four)
            assertFalse(a.overlaps(b))
            assertFalse(b.overlaps(a))
        }

        // overlaps: true
        // 1  2  3  4
        //    [a ]
        // [   b    ]
        run {
            val (one, two, three, four) = timeline.generateN(4)
            val a = SimpleStartEnd(two, three)
            val b = SimpleStartEnd(one, four)
            assertTrue(a.overlaps(b))
            assertTrue(b.overlaps(a))
        }

        // overlaps: true
        // 1 2 3 4
        // [  a  ]
        //   [b]
        run {
            val (one, two, three, four) = timeline.generateN(4)
            val a = SimpleStartEnd(one, four)
            val b = SimpleStartEnd(two, three)
            assertTrue(a.overlaps(b))
            assertTrue(b.overlaps(a))
        }

        // overlaps: true
        // 1 2 3 4
        // [  a  ]
        //   [b  ]
        run {
            val (one, two, three, four) = timeline.generateN(4)
            val a = SimpleStartEnd(one, four)
            val b = SimpleStartEnd(two, four)
            assertTrue(a.overlaps(b))
            assertTrue(b.overlaps(a))
        }

        // overlaps: true
        // 1 2 3 4
        // [  a  ]
        // [  b]
        run {
            val (one, two, three, four) = timeline.generateN(4)
            val a = SimpleStartEnd(one, three)
            val b = SimpleStartEnd(one, four)
            assertTrue(a.overlaps(b))
            assertTrue(b.overlaps(a))
        }
    }
}

// shifts start and end (when not null) to left
private operator fun StartEndImpl.minus(seconds: Long) = copy(start = start - seconds, end = end?.let { it - seconds })
// shifts start and end (when not null) to the right
private operator fun StartEndImpl.plus(seconds: Long) = copy(start = start + seconds, end = end?.let { it + seconds })
