package nl.teqplay.vesselvoyage.model.v2

import nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter.ANY
import nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter.FINISHED
import nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter.ONGOING
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class NewEntryFinishedFilterTest {

    @Test
    fun ofBoolean() {
        assertEquals(FINISHED, NewEntryFinishedFilter.ofBoolean(true))
        assertEquals(ONGOING, NewEntryFinishedFilter.ofBoolean(false))
        assertEquals(ANY, NewEntryFinishedFilter.ofBoolean(null))
    }
}
