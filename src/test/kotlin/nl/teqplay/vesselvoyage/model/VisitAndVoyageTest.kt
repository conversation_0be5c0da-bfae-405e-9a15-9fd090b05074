package nl.teqplay.vesselvoyage.model

import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.vesselvoyage.util.globalObjectMapper
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import java.time.Instant
import java.time.ZoneOffset

class VisitAndVoyageTest {
    @Test
    fun `should correctly determine startTime, endTime, startTimePort`() {
        assertEquals(START_TIME_ANCHOR, VISIT_1_ANCHOR_START.startTime)
        assertEquals(null, VISIT_1_ANCHOR_START.startTimePort)
        assertNull(VISIT_1_ANCHOR_START.endTime)

        assertEquals(START_TIME_ANCHOR, VISIT_1_ANCHOR_END.startTime)
        assertEquals(null, VISIT_1_ANCHOR_END.startTimePort)
        assertNull(VISIT_1_ANCHOR_END.endTime)

        assertEquals(START_TIME_ANCHOR, VISIT_1_PORT_START.startTime)
        assertEquals(START_TIME_PORT, VISIT_1_PORT_START.startTimePort)
        assertNull(VISIT_1_PORT_START.endTime)

        assertEquals(START_TIME_ANCHOR, VISIT_1_PORT_END.startTime)
        assertEquals(START_TIME_PORT, VISIT_1_PORT_END.startTimePort)
        assertEquals(END_TIME_PORT, VISIT_1_PORT_END.endTime)

        assertEquals(START_TIME_PORT, VISIT_1_PORT_START_NO_ANCHOR.startTime)
        assertEquals(START_TIME_PORT, VISIT_1_PORT_START_NO_ANCHOR.startTimePort)
        assertNull(VISIT_1_PORT_START_NO_ANCHOR.endTime)
    }

    @Test
    fun `should correctly serialize and deserialize a Visit`() {
        val originalVisit = VISIT_1_PORT_END

        val serializedVisit = globalObjectMapper.writeValueAsString(originalVisit)

        // validate some of the (computed) properties
        assertTrue(serializedVisit.contains("\"_type\":\"VISIT\""))
        assertTrue(serializedVisit.contains("\"startTime\""))
        assertTrue(serializedVisit.contains("\"startTimePort\""))
        assertTrue(serializedVisit.contains("\"endTime\""))

        val deserializedVisit: Entry = globalObjectMapper.readValue(serializedVisit)

        assertEquals(originalVisit, deserializedVisit)
    }

    @Test
    fun `should correctly serialize and deserialize a Voyage`() {
        val originalVoyage = VOYAGE_1_START

        val serializedVoyage = globalObjectMapper.writeValueAsString(originalVoyage)

        // validate some of the (computed) properties
        assertTrue(serializedVoyage.contains("\"_type\":\"VOYAGE\""))
        assertTrue(serializedVoyage.contains("\"startTime\""))
        assertTrue(serializedVoyage.contains("\"endTime\""))

        val deserializedVoyage: Entry = globalObjectMapper.readValue(serializedVoyage)

        assertEquals(originalVoyage, deserializedVoyage)
    }
}

private const val VISIT_1_ID = "TEST_VISIT_1"
private const val MMSI_1 = "TEST_MMSI_1"
private const val IMO_1 = "TEST_IMO_1"
private val LOCATION_1 = Location(1.0, 1.0)
private const val PORT_1 = "NLRTM"

private val START_TIME_ANCHOR = parseInstant("2021-03-08T00:00:00Z")
private val END_TIME_ANCHOR = parseInstant("2021-03-08T12:00:00Z")
private val START_TIME_PORT = parseInstant("2021-03-08T14:00:00Z")
private val END_TIME_PORT = parseInstant("2021-03-11T18:00:00Z")

private val ANCHOR_AREA_VISIT_1_START = AnchorAreaVisit(
    anchorAreaId = "TEST_ANCHORAGE_1",
    startEventId = "ANCHOR_AREA_VISIT_1_START",
    startTime = START_TIME_ANCHOR,
    destinations = setOf(PORT_1),
    endEventId = null,
    endTime = null
)

private val ANCHOR_AREA_VISIT_1_END = ANCHOR_AREA_VISIT_1_START.copy(
    endEventId = "ANCHOR_AREA_VISIT_1_END",
    endTime = END_TIME_ANCHOR
)

private val PORT_AREA_VISIT_1_START = PortAreaVisit(
    portId = PORT_1,
    startEventId = "PORT_AREA_VISIT_1",
    startTime = START_TIME_PORT,
    startLocation = LOCATION_1,
    startDraught = 1.0,
    endEventId = null,
    endTime = null,
    endLocation = null,
    endDraught = null
)

private val PORT_AREA_VISIT_1_END = PORT_AREA_VISIT_1_START.copy(
    endEventId = "PORT_AREA_VISIT_1_END",
    endTime = END_TIME_PORT,
    endLocation = LOCATION_1,
    endDraught = 1.0
)

private val VISIT_1_ANCHOR_START = Visit(
    _id = VISIT_1_ID,
    mmsi = MMSI_1,
    imo = IMO_1,
    anchorAreas = listOf(ANCHOR_AREA_VISIT_1_START),
    portAreas = listOf(),
    berthAreas = listOf(),
    dest = null,
    eta = null,
    esof = null,
    passThroughAreas = null,
    finished = false,
    previousEntryId = null,
    nextEntryId = null
)

private val VISIT_1_ANCHOR_END = Visit(
    _id = "VISIT_1",
    mmsi = MMSI_1,
    imo = IMO_1,
    anchorAreas = listOf(ANCHOR_AREA_VISIT_1_END),
    portAreas = listOf(),
    berthAreas = listOf(),
    dest = null,
    eta = null,
    esof = null,
    passThroughAreas = null,
    finished = false,
    previousEntryId = null,
    nextEntryId = null
)

private val VISIT_1_PORT_START = Visit(
    _id = "VISIT_1",
    mmsi = MMSI_1,
    imo = IMO_1,
    anchorAreas = listOf(ANCHOR_AREA_VISIT_1_END),
    portAreas = listOf(PORT_AREA_VISIT_1_START),
    berthAreas = listOf(),
    dest = null,
    eta = null,
    esof = null,
    passThroughAreas = null,
    finished = false,
    previousEntryId = null,
    nextEntryId = null
)

private val VISIT_1_PORT_END = Visit(
    _id = "VISIT_1",
    mmsi = MMSI_1,
    imo = IMO_1,
    anchorAreas = listOf(ANCHOR_AREA_VISIT_1_END),
    portAreas = listOf(PORT_AREA_VISIT_1_END),
    berthAreas = listOf(),
    dest = null,
    eta = null,
    esof = null,
    passThroughAreas = null,
    finished = true,
    previousEntryId = null,
    nextEntryId = null
)

private val VISIT_1_PORT_START_NO_ANCHOR = Visit(
    _id = "VISIT_1",
    mmsi = MMSI_1,
    imo = IMO_1,
    anchorAreas = listOf(),
    portAreas = listOf(PORT_AREA_VISIT_1_END),
    berthAreas = listOf(),
    dest = null,
    eta = null,
    esof = null,
    passThroughAreas = null,
    finished = false,
    previousEntryId = null,
    nextEntryId = null
)

private val VOYAGE_1_START = Voyage(
    _id = "VOYAGE_1_START",
    mmsi = MMSI_1,
    imo = IMO_1,

    startPortIds = listOf(PORT_1),
    startTime = END_TIME_PORT,

    dest = null,
    eta = null,
    esof = null,
    nonMatchingAnchorAreas = null,
    passThroughAreas = null,

    endPortIds = null,
    endTime = null,

    finished = false,
    previousEntryId = null,
    nextEntryId = null
)

private fun parseInstant(datetime: String) = Instant.parse(datetime).atZone(ZoneOffset.UTC)
