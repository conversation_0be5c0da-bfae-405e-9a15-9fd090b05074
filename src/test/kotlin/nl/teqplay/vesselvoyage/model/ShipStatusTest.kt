package nl.teqplay.vesselvoyage.model

import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.aisengine.event.model.AnchoredStartEvent
import nl.teqplay.aisengine.event.model.AreaEndEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.vesselvoyage.util.globalObjectMapper
import nl.teqplay.vesselvoyage.util.toSkeletonLocation
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import java.time.Instant
import java.time.ZoneOffset

class ShipStatusTest {
    @Test
    fun `should correctly serialize and deserialized ShipStatus`() {
        val originalStatus = VisitShipStatus(VISIT_2_START, VOYAGE_1_END, null)
        val serializedStatus = globalObjectMapper.writeValueAsString(originalStatus)
        val deserializedStatus: ShipStatus = globalObjectMapper.readValue(serializedStatus)

        Assertions.assertEquals(originalStatus, deserializedStatus)
    }
}

private const val MMSI_1 = 1
private const val IMO_1 = 2
private val LOCATION_1 = Location(1.0, 1.0)
private const val PORT_1 = "NLRTM"
private const val PORT_2 = "BEANR"
private const val ANCHOR_AREA_1 = "Schouwenbank"

private val EVENT_1_END = AreaEndEvent(
    _id = "EVENT_1_END",
    startEventId = null,
    ship = AisShipIdentifier(mmsi = MMSI_1, imo = IMO_1),
    area = AreaIdentifier(PORT_1, AreaIdentifier.AreaType.PORT, PORT_1),
    createdTime = parseUTC("2021-03-09T04:00:00Z").toInstant(),
    actualTime = parseUTC("2021-03-09T04:00:00Z").toInstant(),
    location = LOCATION_1.toSkeletonLocation(),
    berth = null,
    draught = 1.0F,
    heading = null,
    speedOverGround = null
)

private val VOYAGE_1_START = Voyage(
    _id = EVENT_1_END._id,
    mmsi = MMSI_1.toString(),
    imo = IMO_1.toString(),

    startPortIds = listOf(EVENT_1_END.area.unlocode ?: ""),
    startTime = EVENT_1_END.actualTime.atZone(ZoneOffset.UTC),

    dest = null,
    eta = null,
    esof = null,
    nonMatchingAnchorAreas = null,
    passThroughAreas = null,

    endPortIds = null,
    endTime = null,

    finished = false,
    previousEntryId = null,
    nextEntryId = null
)

private val EVENT_ANCHOR_1_START = AnchoredStartEvent(
    _id = "EVENT_ANCHOR_1_START",
    ship = AisShipIdentifier(mmsi = MMSI_1, imo = IMO_1),
    createdTime = parseInstant("2021-03-09T08:00:00Z"),
    actualTime = parseInstant("2021-03-09T08:00:00Z"),
    area = AreaIdentifier(ANCHOR_AREA_1, AreaIdentifier.AreaType.ANCHOR),
    location = LOCATION_1.toSkeletonLocation()
)

private val VOYAGE_1_END = VOYAGE_1_START.copy(
    dest = Destination(
        aisDestination = PORT_2,
        trueDestination = PORT_2,
        updatedAt = EVENT_ANCHOR_1_START.actualTime.atZone(ZoneOffset.UTC)
    ),
    endPortIds = null,
    endTime = EVENT_ANCHOR_1_START.actualTime.atZone(ZoneOffset.UTC),

    finished = true
)

private val VISIT_2_START = Visit(
    _id = EVENT_ANCHOR_1_START._id,
    mmsi = MMSI_1.toString(),
    imo = IMO_1.toString(),
    anchorAreas = listOf(
        AnchorAreaVisit(
            anchorAreaId = ANCHOR_AREA_1,
            startEventId = EVENT_ANCHOR_1_START._id,
            startTime = EVENT_ANCHOR_1_START.actualTime.atZone(ZoneOffset.UTC),
            destinations = setOf(PORT_2),
            endEventId = null,
            endTime = null
        )
    ),
    portAreas = listOf(),
    berthAreas = listOf(),
    dest = null,
    eta = null,
    esof = null,
    passThroughAreas = null,
    finished = false,
    previousEntryId = null,
    nextEntryId = null
)

private fun parseUTC(datetime: String) = Instant.parse(datetime).atZone(ZoneOffset.UTC)
private fun parseInstant(datetime: String) = Instant.parse(datetime)
