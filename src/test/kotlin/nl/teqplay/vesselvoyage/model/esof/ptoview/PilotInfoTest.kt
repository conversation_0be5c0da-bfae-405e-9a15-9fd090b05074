package nl.teqplay.vesselvoyage.model.esof.ptoview

import nl.teqplay.skeleton.model.Location
import nl.teqplay.vesselvoyage.model.lightweight.poma.PilotBoardingPlace
import nl.teqplay.vesselvoyage.model.v2.AreaActivity
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.util.minus
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class PilotInfoTest {
    @ParameterizedTest
    @MethodSource("providePilotAreaActivityData")
    fun testFromPilotAreaActivity(visitId: String, pilotAreaActivity: AreaActivity, pilotArea: PilotBoardingPlace?, expected: PilotInfo) {
        val result = PilotInfo.fromPilotAreaActivity(visitId, pilotAreaActivity, pilotArea)
        assertEquals(expected, result)
    }

    private fun providePilotAreaActivityData(): Stream<Arguments> {
        val startTime = Instant.now()
        val endTime = startTime.plus(30, ChronoUnit.MINUTES)
        val adjustedStartTime = endTime.minus(15, ChronoUnit.MINUTES)
        val startLoc = LocationTime(Location(0.0, 0.0), startTime)
        val endLoc = LocationTime(Location(0.1, 0.1), endTime)

        val areaActivity1 = AreaActivity(
            start = startLoc,
            end = endLoc,
            areaId = "PilotArea1",
            id = "PILOT_AREA_START_EVENT_1"
        )

        val pilotBoardingPlace = PilotBoardingPlace(_id = "1", name = "PilotArea1", location = Location(0.0, 0.0))

        val expectedPilotInfo1 = PilotInfo(
            id = "TEST_VISIT_ID.PILOT_AREA_START_EVENT_1",
            encounter = null,
            pilotArea = pilotBoardingPlace,
            start = LocationTime(endLoc.location, endTime.minus(15, ChronoUnit.MINUTES)),
            end = endLoc,
            fallbackDetectionType = PilotFallbackDetectionType.PILOT_AREA
        )

        return Stream.of(
            Arguments.of("TEST_VISIT_ID", areaActivity1, pilotBoardingPlace, expectedPilotInfo1),
        )
    }
}
