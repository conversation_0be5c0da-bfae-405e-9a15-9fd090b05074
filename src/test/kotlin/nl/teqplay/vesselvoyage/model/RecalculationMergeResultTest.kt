package nl.teqplay.vesselvoyage.model

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.ZonedDateTime

class RecalculationMergeResultTest {

    @Test
    fun `should create RecalculationMergeResult with correct mode and suffix`() {
        val scenarioShip = RecalculationMergeResult.ScenarioShip("scenario123.MERGE", 1234567)
        val status = ReventsRecalculationStatus(
            queuedAt = ZonedDateTime.now(),
            scenarioId = "scenario123.MERGE",
            phase = ReventsRecalculationStatus.Phase.PROGRESSING,
            guarantees = emptySet(),
            username = "user",
            from = null,
            to = null,
            errors = emptyList()
        )

        val result = RecalculationMergeResult(
            _id = "scenario123.MERGE",
            ships = listOf(scenarioShip),
            revents = status,
            startedAt = ZonedDateTime.now()
        )

        assertEquals(RecalculationMode.MERGE_ONLY, result.mode)
        assertEquals(listOf(scenarioShip), result.ships)
        assertEquals(status, result.revents)
    }
}
