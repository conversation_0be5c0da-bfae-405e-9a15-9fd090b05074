package nl.teqplay.vesselvoyage.controller

import nl.teqplay.skeleton.auth.credentials.mongo.MongoUserDataSource
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.core.Authentication
import org.springframework.security.core.authority.SimpleGrantedAuthority
import org.springframework.security.core.context.SecurityContextHolder

abstract class BaseControllerTest {
    val developerAuthentication = mockAuthentication("test-developer-user", "ROLE_DEVELOPER")
    val userAuthentication = mockAuthentication("test-normal-user", "ROLE_USER")
    val reventsAuthentication = mockAuthentication("test-revents-user", "ROLE_REVENTS")

    fun mockAuthentication(username: String, vararg roles: String): Authentication {
        val user = MongoUserDataSource.UserImpl(username, roles.toSet())
        val authorities = roles.toSet().map { SimpleGrantedAuthority(it.removePrefix("ROLE_")) }
        return UsernamePasswordAuthenticationToken(user, null, authorities)
    }

    fun withMockedAuthentication(authentication: Authentication, onTest: () -> Unit) {
        SecurityContextHolder.getContext().authentication = authentication
        onTest()
        SecurityContextHolder.getContext().authentication = null
    }

    /**
     * Execute a test for every provided authentication
     */
    fun withMockedAuthentication(vararg authentications: Authentication, onTest: () -> Unit) {
        for (authentication in authentications) {
            withMockedAuthentication(authentication, onTest)
        }
    }
}
