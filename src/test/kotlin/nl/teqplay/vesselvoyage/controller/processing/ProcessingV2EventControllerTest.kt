package nl.teqplay.vesselvoyage.controller.processing

import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.whenever
import nl.teqplay.vesselvoyage.config.AuthorizationConfiguration
import nl.teqplay.vesselvoyage.controller.BaseControllerTest
import nl.teqplay.vesselvoyage.logic.DEFAULT_END_TIME
import nl.teqplay.vesselvoyage.logic.DEFAULT_START_TIME
import nl.teqplay.vesselvoyage.service.EntryProcessingService
import nl.teqplay.vesselvoyage.service.EventFetchingService
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.junit.runner.RunWith
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Import
import org.springframework.http.MediaType
import org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.TestConstructor
import org.springframework.test.context.junit4.SpringRunner
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.util.stream.Stream

@RunWith(SpringRunner::class)
@WebMvcTest(ProcessingV2EventController::class)
@Import(ProcessingV2EventController::class, AuthorizationConfiguration::class)
@ActiveProfiles("processing")
@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ProcessingV2EventControllerTest(
    private val mockMvc: MockMvc
) : BaseControllerTest() {

    @TestConfiguration
    class TestConfig {
        @Bean
        fun testEventFetchingService(): EventFetchingService {
            return mock<EventFetchingService> {
                whenever(it.fetchEventsByIMO(any(), any(), any())).thenReturn(emptySequence())
                whenever(it.fetchAisEngineEventsByIMO(any(), any(), any())).thenReturn(emptySequence())
            }
        }

        @Bean
        fun testEntryProcessingService(): EntryProcessingService {
            return mock<EntryProcessingService> {
                whenever(it.processTeqplayEventsDryRun(any(), any())).thenReturn(emptyList())
                whenever(it.processAisEngineEventsDryRunForStory(any())).thenReturn(emptyList())
            }
        }
    }

    private val eventsProcessedRequest = MockMvcRequestBuilders.get("/v2/events/{imo}/processed", 1)
        .param("start", DEFAULT_START_TIME.toString())
        .param("end", DEFAULT_END_TIME.toString())

    private val eventsProcessedAisEngineRequest = MockMvcRequestBuilders.post("/v2/events/processed/aisengine")
        .content("[]")
        .contentType(MediaType.APPLICATION_JSON)
        .with(csrf())

    private fun developerAllowedEndpoints(): Stream<Arguments> {
        return Stream.of(
            Arguments.of(eventsProcessedRequest),
            Arguments.of(eventsProcessedAisEngineRequest)
        )
    }

    private fun reventsAllowedEndpoints(): Stream<Arguments> {
        return Stream.of(
            Arguments.of(eventsProcessedRequest),
            Arguments.of(eventsProcessedAisEngineRequest)
        )
    }

    private fun userDisallowedEndpoints(): Stream<Arguments> {
        return Stream.of(
            Arguments.of(eventsProcessedRequest),
            Arguments.of(eventsProcessedAisEngineRequest)
        )
    }

    @ParameterizedTest
    @MethodSource("developerAllowedEndpoints")
    fun `developer should be allowed to call event endpoint`(request: MockHttpServletRequestBuilder) {
        withMockedAuthentication(developerAuthentication) {
            mockMvc.perform(request)
                .andExpect(status().isOk())
        }
    }

    @ParameterizedTest
    @MethodSource("reventsAllowedEndpoints")
    fun `revents should be allowed to call event endpoint`(request: MockHttpServletRequestBuilder) {
        withMockedAuthentication(reventsAuthentication) {
            mockMvc.perform(request)
                .andExpect(status().isOk())
        }
    }

    @ParameterizedTest
    @MethodSource("userDisallowedEndpoints")
    fun `user should not be allowed to call event endpoint`(request: MockHttpServletRequestBuilder) {
        withMockedAuthentication(userAuthentication) {
            mockMvc.perform(request)
                .andExpect(status().isForbidden())
        }
    }
}
