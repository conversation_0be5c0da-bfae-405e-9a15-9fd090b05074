package nl.teqplay.vesselvoyage.controller.api

import com.fasterxml.jackson.databind.ObjectMapper
import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.eq
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.whenever
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polyline
import nl.teqplay.vesselvoyage.apiv2.model.Trace
import nl.teqplay.vesselvoyage.config.AuthorizationConfiguration
import nl.teqplay.vesselvoyage.controller.BaseControllerTest
import nl.teqplay.vesselvoyage.mapper.TraceMapperImpl
import nl.teqplay.vesselvoyage.model.v2.NewTrace
import nl.teqplay.vesselvoyage.model.v2.Speed
import nl.teqplay.vesselvoyage.model.v2.TraceDistance
import nl.teqplay.vesselvoyage.model.v2.TraceStatistic
import nl.teqplay.vesselvoyage.service.trace.TraceService
import org.junit.jupiter.api.Test
import org.junit.runner.RunWith
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Import
import org.springframework.http.MediaType
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.TestConstructor
import org.springframework.test.context.junit4.SpringRunner
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.time.Instant

@RunWith(SpringRunner::class)
@WebMvcTest(ApiTraceV2Controller::class)
@Import(ApiTraceV2Controller::class, AuthorizationConfiguration::class)
@ActiveProfiles("processing")
@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
class ApiTraceV2ControllerTest(
    private val mockMvc: MockMvc,
    private val objectMapper: ObjectMapper
) : BaseControllerTest() {
    companion object {
        val testPolyline = Polyline(
            encoded = "_ibE?_ibE?_ibE_seK?_ibE",
            lastLocation = Location(3.0, 3.0)
        )
        val testTrace = NewTrace(
            _id = "TEST_ID",
            polyline = testPolyline,
            totalOngoingTraceItems = 4,
            simplifiedPolyline = null,
            speed = Speed(
                min = 1.0f,
                max = 5.0f,
                avg = 3.25f,
                count = 4,
                lastSpeedOverGround = 5.0f,
                lastSpeedOverGroundTimestamp = Instant.now()
            ),
            distance = TraceDistance(
                distanceMeters = 1500L,
                lastLocation = Location(1.0, 1.0)
            ),
            draught = TraceStatistic(
                min = 2.0f,
                max = 4.0f,
                avg = 3.27f,
                last = 3.26f,
                lastTimestamp = Instant.now()
            )
        )
        val apiTrace = Trace(
            entryId = "TEST_ID",
            polyline = testPolyline,
            speedMin = 1.0f,
            speedMax = 5.0f,
            speedAvg = 3.25f,
            distanceMeters = 1500L,
            draughtMin = 2.0f,
            draughtMax = 4.0f,
            draughtAvg = 3.27f,
        )
    }

    @TestConfiguration
    class TestConfig {

        private val traceMapper = TraceMapperImpl()

        @Bean
        fun traceMapper() = traceMapper

        @Bean
        fun testTraceService(): TraceService {
            return mock<TraceService> {
                whenever(it.getTraceById(eq("TEST_ID"), any(), any())).thenReturn(testTrace)
                whenever(it.getCombinedPolyline(eq("TEST_ID"))).thenReturn(testPolyline)
            }
        }
    }

    private val traceRequest = MockMvcRequestBuilders.get("/v2/traces/{entryId}", "TEST_ID")
    private val missingTraceRequest = MockMvcRequestBuilders.get("/v2/traces/{entryId}", "UNKNOWN_ID")
    private val polylineRequest = MockMvcRequestBuilders.get("/v2/traces/{entryId}/polyline", "TEST_ID")
    private val missingPolylineRequest = MockMvcRequestBuilders.get("/v2/traces/{entryId}/polyline", "UNKNOWN_ID")

    @Test
    fun `should return trace when requesting valid entry id`() {
        withMockedAuthentication(developerAuthentication, userAuthentication) {
            mockMvc.perform(traceRequest)
                .andExpect(status().isOk)
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(content().json(objectMapper.writeValueAsString(apiTrace)))
        }
    }

    @Test
    fun `should return polyline when requesting valid entry id`() {
        withMockedAuthentication(developerAuthentication, userAuthentication) {
            mockMvc.perform(polylineRequest)
                .andExpect(status().isOk)
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(content().json(objectMapper.writeValueAsString(testPolyline)))
        }
    }

    @Test
    fun `should return 404 when requesting trace of unknown entry`() {
        withMockedAuthentication(developerAuthentication, userAuthentication) {
            mockMvc.perform(missingTraceRequest)
                .andExpect(status().isNotFound)
        }
    }

    @Test
    fun `should return 404 when requesting polyline of unknown entry`() {
        withMockedAuthentication(developerAuthentication, userAuthentication) {
            mockMvc.perform(missingPolylineRequest)
                .andExpect(status().isNotFound)
        }
    }
}
