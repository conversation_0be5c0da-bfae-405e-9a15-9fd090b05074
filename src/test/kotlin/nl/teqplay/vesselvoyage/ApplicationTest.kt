package nl.teqplay.vesselvoyage

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.mongodb.kotlin.client.FindIterable
import com.mongodb.kotlin.client.MongoCollection
import com.mongodb.kotlin.client.MongoDatabase
import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.doReturn
import com.nhaarman.mockitokotlin2.eq
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.whenever
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.nats.stream.EventStreamAutoConfiguration
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.aisengine.nats.stream.EventStreamServiceImpl
import nl.teqplay.csi.model.ship.info.component.ShipCategories
import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2
import nl.teqplay.csi.model.ship.info.component.ShipSpecification
import nl.teqplay.skeleton.common.BaseTest
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.nats.NatsClientMock
import nl.teqplay.skeleton.nats.NatsConsumerStream
import nl.teqplay.skeleton.nats.NatsProducerStream
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.annotation.ProfileRevents
import nl.teqplay.vesselvoyage.apiv2.model.OutgoingChange
import nl.teqplay.vesselvoyage.client.csi.CSIClient
import nl.teqplay.vesselvoyage.client.poma.PomaClient
import nl.teqplay.vesselvoyage.config.NatsConfiguration
import nl.teqplay.vesselvoyage.datasource.NewVisitDataSource
import nl.teqplay.vesselvoyage.datasource.NewVoyageDataSource
import nl.teqplay.vesselvoyage.datasource.ReventsRecalculationsDataSource
import nl.teqplay.vesselvoyage.logic.IMO_1
import nl.teqplay.vesselvoyage.logic.MMSI_1
import nl.teqplay.vesselvoyage.logic.createPort
import nl.teqplay.vesselvoyage.mapper.EntryV2Mapper
import nl.teqplay.vesselvoyage.model.internal.InfraAreaType
import nl.teqplay.vesselvoyage.model.lightweight.poma.Anchorage
import nl.teqplay.vesselvoyage.model.v2.NewChange
import nl.teqplay.vesselvoyage.properties.EventPublishingProperties
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.api.EsofV2Service
import nl.teqplay.vesselvoyage.service.publisher.ChangesPublisherService
import nl.teqplay.vesselvoyage.service.publisher.RabbitMqOutgoingChangeSender
import nl.teqplay.vesselvoyage.util.DEFAULT_TEST_PORT_ID
import nl.teqplay.vesselvoyage.util.createShipMmsiMapping
import nl.teqplay.vesselvoyage.util.createShipRegisterInfoCache
import nl.teqplay.vesselvoyage.util.globalObjectMapper
import nl.teqplay.vesselvoyage.util.loadResource
import org.bson.conversions.Bson
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Import
import org.springframework.context.annotation.Primary
import org.springframework.test.context.ActiveProfiles
import java.time.ZonedDateTime

abstract class BaseApplicationTest : BaseTest() {
    @Test
    fun contextLoads() {
    }
}

@TestConfiguration
@Import(Application::class)
class ApplicationTestConfig {
    @ProfileApi
    @ProfileProcessing
    @Bean
    @Primary
    fun mongoDatabase() = getMockMongoDB()

    @Bean
    @Primary
    fun pomaClient() = getMockPomaClient()

    @Bean
    @Primary
    fun csiClient() = getMockCSIClient()

    @Bean
    @Primary
    fun reventsRecalculationsDataSource() = getMockReventsRecalculationsDataSource()

    @Bean
    @ProfileProcessing
    @ProfileRevents
    fun natsClientMock() = NatsClientMock()

    @Bean
    @ProfileRevents
    fun testNatsChangeProducer(
        natsClientMock: NatsClientMock,
        objectMapper: ObjectMapper
    ): NatsProducerStream<NewChange<*>> {
        return natsClientMock.producerStream(
            stream = "vesselvoyage:change",
            subjects = listOf("vesselvoyage.change.>"),
            serializer = { objectMapper.writeValueAsBytes(it) }
        )
    }

    @Bean
    @ProfileProcessing
    @ProfileRevents
    fun testNatsEventProducer(
        natsClientMock: NatsClientMock,
        objectMapper: ObjectMapper
    ): NatsProducerStream<Event> {
        return natsClientMock.producerStream(
            stream = EventStreamAutoConfiguration.EVENT_STREAM,
            subjects = listOf(EventStreamAutoConfiguration.EVENT_STREAM_SUBJECT),
            serializer = { objectMapper.writeValueAsBytes(it) }
        )
    }

    @Bean
    @ProfileProcessing
    @ProfileRevents
    fun testEventStreamService(
        @Qualifier("testNatsEventProducer") producer: NatsProducerStream<Event>
    ): EventStreamService {
        return EventStreamServiceImpl(producer)
    }

    @Bean
    @ProfileProcessing
    @ProfileRevents
    fun eventConsumer(
        natsClientMock: NatsClientMock,
        objectMapper: ObjectMapper
    ): NatsConsumerStream<Event> {
        return natsClientMock.consumerStream(
            stream = EventStreamAutoConfiguration.EVENT_STREAM,
            deserializer = { objectMapper.readValue(it) }
        )
    }

    @Bean
    @ProfileProcessing
    fun testNatsChangeProducerStream(
        natsClientMock: NatsClientMock,
        objectMapper: ObjectMapper
    ): NatsProducerStream<OutgoingChange<*>> {
        return natsClientMock.producerStream(
            stream = NatsConfiguration.OUTGOING_CHANGES_STREAM,
            subjects = listOf(NatsConfiguration.OUTGOING_CHANGES_SUBJECT),
            serializer = objectMapper::writeValueAsBytes
        )
    }

    @Bean
    @ProfileProcessing
    fun testRabbitmqOutgoingEventsSender(): RabbitMqEventSender {
        return mock<RabbitMqEventSender>()
    }

    @Bean
    @ProfileProcessing
    fun testChangesPublisherService(
        properties: EventPublishingProperties,
        stream: NatsProducerStream<OutgoingChange<*>>,
        eventSender: RabbitMqOutgoingChangeSender,
        entryMapper: EntryV2Mapper,
        esofV2Service: EsofV2Service,
    ): ChangesPublisherService {
        return ChangesPublisherService(
            eventPublishingProperties = properties,
            natsProducerStream = stream,
            rabbitMqEventSender = eventSender,
            entryMapper = entryMapper,
            esofV2Service = esofV2Service
        )
    }

    @Primary
    @Bean
    @ProfileProcessing
    fun testInfraService(): InfraService {
        return mock<InfraService>().apply {
            whenever(getById(eq(DEFAULT_TEST_PORT_ID), eq(InfraAreaType.PORT)))
                .thenReturn(createPort())
        }
    }

    @Bean
    fun newVisitDataSource(): NewVisitDataSource {
        return mock {
            whenever(it.distinctImos(any())).thenReturn(emptySet())
        }
    }

    @Bean
    fun newVoyageDataSource(): NewVoyageDataSource {
        return mock {
            whenever(it.distinctImos(any())).thenReturn(emptySet())
        }
    }
}

@Import(ApplicationTestConfig::class)
@SpringBootTest
@ActiveProfiles("processing")
class ProcessingApplicationTest : BaseApplicationTest()

@Import(ApplicationTestConfig::class)
@SpringBootTest
@ActiveProfiles("processing", "api")
class ProcessingAndApiApplicationTest : BaseApplicationTest()

@Import(ApplicationTestConfig::class)
@SpringBootTest
@ActiveProfiles("api")
class ApiApplicationTest : BaseApplicationTest()

@Import(ApplicationTestConfig::class)
@SpringBootTest
@ActiveProfiles("revents")
class ReventsApplicationTest : BaseApplicationTest()

private fun <T : Any> getMockIterable() = mock<FindIterable<T>>()

fun getMockMongoDB(): MongoDatabase {
    return mock {
        val mockCollection = mock<MongoCollection<Any>> {
            val iterable = getMockIterable<Any>()
            on { createIndex(any<Bson>(), any()) } doReturn ""
            on { find() } doReturn iterable
            on { find(any<Bson>()) } doReturn iterable
        }
        on { getCollection(any(), any<Class<Any>>()) } doReturn mockCollection
        val iterable = getMockIterable<String>()
        on { listCollectionNames() } doReturn iterable
    }
}

fun getMockPomaClient(): PomaClient {
    return mock {
        on { getAllPorts() } doReturn listOf(
            globalObjectMapper.readValue(loadResource("ports/nlrtm.json"))
        )

        on { getAllAnchorages() } doReturn listOf(
            Anchorage(
                name = "5",
                ports = emptyList(),
                location = Location(0.0, 0.0),
                area = emptyList(),
                _id = "5_A"
            )
        )
    }
}

fun getMockCSIClient(): CSIClient {
    return mock {
        on { listShipRegisterInfoCache() } doReturn listOf(
            createShipRegisterInfoCache(IMO_1.toString(), MMSI_1.toString(), ShipCategories(v2 = ShipCategoryV2.CONTAINER), ShipSpecification())
        )

        on { listShipRegisterMapping() } doReturn listOf(
            createShipMmsiMapping(IMO_1.toString(), ZonedDateTime.parse("2021-01-28T00:00:00Z"), ZonedDateTime.parse("2021-01-29T00:00:00Z"), MMSI_1.toString())
        )
    }
}

fun getMockReventsRecalculationsDataSource(): ReventsRecalculationsDataSource {
    return mock {
        on { findAllRunning() } doReturn emptyList()
    }
}
