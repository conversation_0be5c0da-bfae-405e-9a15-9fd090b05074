package nl.teqplay.vesselvoyage.datasource

import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2
import nl.teqplay.vesselvoyage.datasource.util.resolveQueryImos
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test

class ResolveQueryImosTest {
    @Test
    fun `resolve query imos based on no imos and no shipTypes`() {
        fun findShipsByCategory(shipCategory: ShipCategoryV2) = emptySet<String>()

        assertNull(resolveQueryImos(null, null, emptySet(), ::findShipsByCategory))
    }

    @Test
    fun `resolve query imos based on imos`() {
        fun findShipsByCategory(shipCategory: ShipCategoryV2) = emptySet<String>()

        assertEquals(setOf("1", "2"), resolveQueryImos(setOf("1", "2"), null, emptySet(), ::findShipsByCategory))
    }

    @Test
    fun `resolve query imos based on shipTypes`() {
        fun findShipsByType(shipCategory: ShipCategoryV2) = when (shipCategory) {
            ShipCategoryV2.CONTAINER -> setOf("1", "2")
            ShipCategoryV2.TANKER -> setOf("3", "4")
            else -> emptySet()
        }

        val shipCategories = setOf(ShipCategoryV2.CONTAINER, ShipCategoryV2.TANKER)
        assertEquals(setOf("1", "2", "3", "4"), resolveQueryImos(null, shipCategories, emptySet(), ::findShipsByType))
    }

    @Test
    fun `resolve query imos based on both IMO's and shipTypes`() {
        fun findShipsByType(shipCategory: ShipCategoryV2) = when (shipCategory) {
            ShipCategoryV2.CONTAINER -> setOf("1", "2")
            ShipCategoryV2.TANKER -> setOf("3", "4")
            else -> emptySet()
        }

        val shipCategories = setOf(ShipCategoryV2.CONTAINER, ShipCategoryV2.TANKER)
        assertEquals(setOf("1", "4"), resolveQueryImos(setOf("1", "4", "5"), shipCategories, emptySet(), ::findShipsByType))
    }
}
