package nl.teqplay.vesselvoyage.datasource

import com.mongodb.kotlin.client.FindIterable
import com.mongodb.kotlin.client.MongoCollection
import com.mongodb.kotlin.client.MongoDatabase
import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.doReturn
import com.nhaarman.mockitokotlin2.eq
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.never
import com.nhaarman.mockitokotlin2.verify
import nl.teqplay.skeleton.datasource.kmongo.and
import nl.teqplay.skeleton.datasource.kmongo.div
import nl.teqplay.skeleton.datasource.kmongo.gte
import nl.teqplay.skeleton.datasource.kmongo.lte
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter.ANY
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Answers
import java.time.Instant
import nl.teqplay.skeleton.datasource.kmongo.eq as mongoEq

class NewVisitDataSourceTest {

    private lateinit var dataSource: NewVisitDataSource
    private lateinit var dbMock: MongoDatabase
    private lateinit var collectionMock: MongoCollection<NewVisit>

    @BeforeEach
    fun setUp() {
        dbMock = mock {
            collectionMock = mock(defaultAnswer = Answers.RETURNS_DEEP_STUBS)
            on { getCollection(any(), any<Class<NewVisit>>()) } doReturn collectionMock
            val iterable = getMockIterable<String>()
            on { listCollectionNames() } doReturn iterable
        }

        dataSource = NewVisitDataSource(dbMock)
    }

    @Test
    fun `look around - in history`() {
        val imo = 12345678
        val timestamp = Instant.now()

        dataSource.findByImoLookAround(imo, timestamp, limit = -3, finished = ANY, confirmed = null)

        // finished and confirmed are null, so not in the filter
        val expectedFilters = and(
            NewVisit::imo mongoEq imo,
            NewVisit::start / LocationTime::time lte timestamp
        )
        verify(collectionMock).find(eq(expectedFilters))
    }

    @Test
    fun `look around - in the future`() {
        val imo = 12345678
        val timestamp = Instant.now()

        dataSource.findByImoLookAround(imo, timestamp, limit = 3, finished = ANY, confirmed = null)

        // finished and confirmed are null, so not in the filter
        val expectedFilters = and(
            NewVisit::imo mongoEq imo,
            NewVisit::start / LocationTime::time gte timestamp
        )
        verify(collectionMock).find(eq(expectedFilters))
    }

    @Test
    fun `look around - limit of 0 gives an empty list`() {
        val imo = 12345678
        val timestamp = Instant.now()

        dataSource.findByImoLookAround(imo, timestamp, limit = 3, finished = ANY, confirmed = null)

        verify(collectionMock, never()).find(eq(and()))
    }

    private fun <T : Any> getMockIterable() = mock<FindIterable<T>>()
}
