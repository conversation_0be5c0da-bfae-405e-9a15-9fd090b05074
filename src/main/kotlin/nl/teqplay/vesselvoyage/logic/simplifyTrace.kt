package nl.teqplay.vesselvoyage.logic

import com.goebl.simplify.Point
import com.goebl.simplify.Simplify
import nl.teqplay.aisengine.aisstream.model.AisLongRangeMessage
import nl.teqplay.aisengine.aisstream.model.AisPositionMessage
import nl.teqplay.aisengine.aisstream.model.AisWrapper
import nl.teqplay.aisengine.aisstream.model.BaseAisMessage
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.platform.model.ShipInfo
import nl.teqplay.vesselvoyage.model.LocationTime
import nl.teqplay.vesselvoyage.model.OngoingTrace
import nl.teqplay.vesselvoyage.model.StopDetectionLocation
import nl.teqplay.vesselvoyage.util.toZonedDateTime

/**
 * Simplify a trace with locations
 *
 * The tolerance is the maximum deviation from the original trace in rad. To get a *rough* idea:
 * 0.01 rad -> roughly 1 km
 * 0.001 rad -> roughly 100 m
 * 0.0001 rad -> roughly 10 m
 *
 * Source:
 * - https://mourner.github.io/simplify-js/
 * - https://github.com/hgoebl/simplify-java
 */
fun List<LocationTime>.simplifyTrace(tolerance: Double): List<LocationTime> {
    if (this.isEmpty()) {
        return emptyList()
    }

    // create an instance of the simplifier (empty array needed by List.toArray)
    val simplify = Simplify(arrayOf<SimplifyPoint>())

    // true for Douglas-Peucker (high quality), false for Radial-Distance (high speed)
    val highQuality = true

    val all = this.map { SimplifyPoint(it) }
        .toTypedArray()

    return simplify.simplify(all, tolerance, highQuality)
        .map { it.locationTime }
        .toList()
}

fun simplifyOngoingTrace(ongoingTrace: OngoingTrace, tolerance: Double): OngoingTrace {
    val locations = ongoingTrace.locations.sortedBy { it.time }

    val simplifiedLocations = locations.simplifyTrace(tolerance)

    return ongoingTrace.copy(
        locations = simplifiedLocations.toMutableList(),
        simplifiedCount = simplifiedLocations.size
    )
}

fun AisHistoricMessage.toLocationTime() = LocationTime(
    latitude = location.lat,
    longitude = location.lon,
    time = toZonedDateTime(messageTime)
)

fun AisHistoricMessage.toLocationTimeHeading() = StopDetectionLocation(
    latitude = this.location.lat,
    longitude = this.location.lon,
    time = toZonedDateTime(this.messageTime),
    heading = this.heading?.takeIf { it != 511 }
)

fun AisWrapper<out BaseAisMessage>.toLocationTime(): LocationTime? {
    val location = when (val message = this.message) {
        is AisPositionMessage -> message.location
        is AisLongRangeMessage -> message.location
        else -> null
    } ?: return null

    return LocationTime(
        latitude = location.lat,
        longitude = location.lon,
        time = toZonedDateTime(this.timestamp)
    )
}

fun ShipInfo.toLocationTime() = LocationTime(
    latitude = location.latitude,
    longitude = location.longitude,
    time = toZonedDateTime(timeLastUpdate)
)

private data class SimplifyPoint(
    val locationTime: LocationTime
) : Point {
    override fun getX() = locationTime.latitude
    override fun getY() = locationTime.longitude
}
