package nl.teqplay.vesselvoyage.logic

import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.vesselvoyage.model.MINIMUM_SLOW_MOVEMENT_TIME
import nl.teqplay.vesselvoyage.model.SLOW_MOVING_SPEED
import nl.teqplay.vesselvoyage.model.SlowMovingPeriod
import nl.teqplay.vesselvoyage.util.toVesselVoyageLocation
import java.time.Duration
import java.time.ZoneOffset
import java.util.UUID

/**
 * Detect all timeframes where the ship sailed slower than the configured speed
 */
fun detectSlowMovingPeriods(ais: List<AisHistoricMessage>): List<SlowMovingPeriod> {
    if (ais.isEmpty()) {
        return emptyList()
    }

    var currentSlowMoving: AisHistoricMessage? = null
    val slowMovingPeriods = mutableListOf<SlowMovingPeriod>()
    // Fill the list with slow moving periods
    for (aisPoint in ais) {
        // We can't determine any slow moving periods if the ais point has no speed over ground
        if (aisPoint.speedOverGround == null) {
            continue
        }

        if (currentSlowMoving == null && requireNotNull(aisPoint.speedOverGround) < SLOW_MOVING_SPEED) {
            // We found a new first slow moving point
            currentSlowMoving = aisPoint
        } else if (currentSlowMoving != null && requireNotNull(aisPoint.speedOverGround) >= SLOW_MOVING_SPEED) {
            // The first point after we have found a slow moving period that goes faster than SLOW_MOVING_SPEED
            slowMovingPeriods.add(
                SlowMovingPeriod(
                    startTime = currentSlowMoving.messageTime.atZone(ZoneOffset.UTC),
                    startEventId = UUID.randomUUID().toString(),
                    startLocation = currentSlowMoving.location.toVesselVoyageLocation(),
                    endTime = aisPoint.messageTime.atZone(ZoneOffset.UTC),
                    endEventId = UUID.randomUUID().toString(),
                    endLocation = aisPoint.location.toVesselVoyageLocation(),
                )
            )

            // Reset the current slow moving, so we can detect a new one in the remaining ais points
            currentSlowMoving = null
        }
    }

    // If there is still an open slow moving period, close it with the last ais point we have
    if (currentSlowMoving != null) {
        slowMovingPeriods.add(
            SlowMovingPeriod(
                startTime = currentSlowMoving.messageTime.atZone(ZoneOffset.UTC),
                startEventId = UUID.randomUUID().toString(),
                startLocation = currentSlowMoving.location.toVesselVoyageLocation(),
                endTime = ais.last().messageTime.atZone(ZoneOffset.UTC),
                endEventId = UUID.randomUUID().toString(),
                endLocation = ais.last().location.toVesselVoyageLocation()
            )
        )
    }

    return if (slowMovingPeriods.isNotEmpty()) {
        // Only remove jitter if our result list contains items
        removeJitterFromSlowMovingPeriods(slowMovingPeriods)
    } else {
        // We had no slow moving periods
        emptyList()
    }
}

/**
 * This function tries to filter out the jitter in the ais that caused extra slow moving periods.
 * And slow moving periods that have been caused by the ship very temporary slowing down.
 *
 * This is done by comparing to the [MINIMUM_SLOW_MOVEMENT_TIME] which is the time that describes the minimum amount of time
 * is needed for us to be interested in the time period.
 *
 * If there is an entry that is less than the minimum we will remove it.
 * If there are 2 slow moving periods very close to each other we will merge them
 */
private fun removeJitterFromSlowMovingPeriods(
    slowMovingPeriods: List<SlowMovingPeriod>
): List<SlowMovingPeriod> {
    val joinedSlowMovingPeriods = mutableListOf<SlowMovingPeriod>()
    if (slowMovingPeriods.size == 1) {
        joinedSlowMovingPeriods.addAll(slowMovingPeriods)
    } else {
        val mergedSlowMovingPeriods = slowMovingPeriods.toMutableList()
        var cursor = 0
        while (cursor < mergedSlowMovingPeriods.size) {
            val current = mergedSlowMovingPeriods[cursor]

            // No need to continue if this is the last item in the list
            if (cursor + 1 >= mergedSlowMovingPeriods.size) {
                break
            }

            val next = mergedSlowMovingPeriods[cursor + 1]
            val timeBetweenPeriods = Duration.between(current.endTime, next.startTime)

            if (timeBetweenPeriods < MINIMUM_SLOW_MOVEMENT_TIME && !timeBetweenPeriods.isNegative) {
                val mergedSlowMovingPeriod = SlowMovingPeriod(
                    startTime = current.startTime,
                    startEventId = current.startEventId,
                    startLocation = current.startLocation,
                    endTime = next.endTime,
                    endEventId = next.endEventId,
                    endLocation = next.endLocation
                )

                // Remove the first item and replace with the merged one
                mergedSlowMovingPeriods.removeAt(cursor)
                mergedSlowMovingPeriods[cursor] = mergedSlowMovingPeriod
            } else {
                // Only go to the next item if we don't have to merge our current item anymore
                cursor++
            }
        }

        // Add the merged slow moving periods to the result list
        joinedSlowMovingPeriods.addAll(mergedSlowMovingPeriods)
    }

    // Ignore all slow moving periods that are less than the minimum time we are interested in
    return joinedSlowMovingPeriods.filter { slowMovingPeriod ->
        val duration = Duration.between(slowMovingPeriod.startTime, slowMovingPeriod.endTime)
        duration >= MINIMUM_SLOW_MOVEMENT_TIME
    }
}
