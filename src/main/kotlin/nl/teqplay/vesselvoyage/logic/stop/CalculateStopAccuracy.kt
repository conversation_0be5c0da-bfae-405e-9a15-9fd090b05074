package nl.teqplay.vesselvoyage.logic.stop

import nl.teqplay.vesselvoyage.model.Stop
import nl.teqplay.vesselvoyage.model.StopDetectionVersion
import nl.teqplay.vesselvoyage.model.StopType

/**
 * List of all checks used to calculate the accuracy of a [Stop].
 */
private val availableAccuracyChecks = listOf(
    AccuracyCheck(1, ::isNotBasedOnMovementEvent),
    AccuracyCheck(2, ::hasBerthStartEvent),
    AccuracyCheck(2, ::hasBerthEndEvent),
    AccuracyCheck(5, ::isPomaClassifiedStop)
)

/**
 * Calculate the accuracy of the [Stop].
 *
 * @return the calculated accuracy which is a value between 0.0 and 1.0
 */
fun Stop.calculateAccuracy(): Float {
    return evaluateAccuracyChecks(this, availableAccuracyChecks)
}

/**
 * Calculate the accuracy of the [Stop]. Copy the provided [Stop] and replace the [Stop.accuracy] field with the calculated value.
 *
 * @return the updated [Stop] with the updated [Stop.accuracy] field.
 */
fun Stop.calculateAndSetAccuracy(): Stop {
    val calculatedAccuracy = this.calculateAccuracy()

    return this.copy(
        accuracy = calculatedAccuracy
    )
}

private fun isNotBasedOnMovementEvent(stop: Stop): Boolean {
    return stop.detectionVersion != StopDetectionVersion.MOVEMENT_EVENT
}

private fun isPomaClassifiedStop(stop: Stop): Boolean {
    return stop.pomaId != null && stop.pomaType != StopType.UNCLASSIFIED
}

private fun hasBerthStartEvent(stop: Stop): Boolean {
    return stop.berthStart != null
}

private fun hasBerthEndEvent(stop: Stop): Boolean {
    return stop.berthEnd != null
}

private fun evaluateAccuracyChecks(stop: Stop, checks: List<AccuracyCheck>): Float {
    val totalWeight = checks.sumOf(AccuracyCheck::weight)
    var weight = 0.0f

    checks.forEach { check ->
        if (check.predicate(stop)) {
            weight += check.weight
        }
    }

    return weight / totalWeight
}

private data class AccuracyCheck(
    /**
     * How heavy the check counts towards the resulting accuracy
     */
    val weight: Int,

    /**
     * The check if the stop is accurate for its provided weight
     */
    val predicate: (Stop) -> Boolean
)
