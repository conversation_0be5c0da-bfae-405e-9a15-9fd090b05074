package nl.teqplay.vesselvoyage.logic.stop

import nl.teqplay.aisengine.event.interfaces.StartEvent
import nl.teqplay.aisengine.event.interfaces.UniqueBerthEvent
import nl.teqplay.vesselvoyage.logic.CalculateStops.Companion.findActualStops
import nl.teqplay.vesselvoyage.logic.CalculateStops.Companion.prepareDetectedStops
import nl.teqplay.vesselvoyage.logic.FindAnchoragesByLocation
import nl.teqplay.vesselvoyage.logic.FindBerthById
import nl.teqplay.vesselvoyage.logic.FindBerthsByLocation
import nl.teqplay.vesselvoyage.logic.FindShipTraceForStopDetection
import nl.teqplay.vesselvoyage.model.Location
import nl.teqplay.vesselvoyage.model.Stop
import nl.teqplay.vesselvoyage.model.StopDetectionVersion
import nl.teqplay.vesselvoyage.model.StopEndDetectionInfo
import nl.teqplay.vesselvoyage.model.StopStartDetectionInfo
import nl.teqplay.vesselvoyage.model.StopType
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.model.lightweight.poma.Berth
import nl.teqplay.vesselvoyage.util.haversineDistance
import nl.teqplay.vesselvoyage.util.isFinished
import nl.teqplay.vesselvoyage.util.pointInPolygon
import nl.teqplay.vesselvoyage.util.toVesselVoyageLocation
import java.time.Duration
import java.time.ZoneOffset
import java.time.ZonedDateTime

/**
 * The distance we still want to merge the stop and berth event together
 */
private const val MERGE_DISTANCE_STOP_AND_BERTH_EVENT = 200.0

/**
 * The duration we still want to merge a finished stop with a berth event
 */
private val MERGE_FINISHED_TIME_STOP_AND_BERTH_EVENT = Duration.ofHours(3)

/**
 * Go over all the provided [Stop]s. When one matches, merge them together, otherwise create a new Stop when possible.
 */
fun List<Stop>.tryMergeWithBerthEvent(
    berthEvent: UniqueBerthEvent,
    findBerthById: FindBerthById,
    currentVisit: Visit,
    findShipTraceForStopDetection: FindShipTraceForStopDetection,
    findBerthsByLocation: FindBerthsByLocation,
    findAnchoragesByLocation: FindAnchoragesByLocation,
    berthId: String
): List<Stop> {
    val isStartEvent = berthEvent is StartEvent
    val berth = findBerthById(berthId)

    // When we don't have any stops yet, try to create a new Stop instead based on the berth event
    if (this.isEmpty()) {
        val newStop = createNewStop(
            berthEvent = berthEvent,
            isStartEvent = isStartEvent,
            berth = berth,
            currentStops = this,
            currentVisit = currentVisit,
            findShipTraceForStopDetection = findShipTraceForStopDetection,
            findBerthsByLocation = findBerthsByLocation,
            findAnchoragesByLocation = findAnchoragesByLocation
        )
        // Return the new stop as a list or an empty list when we couldn't create one
        return listOfNotNull(newStop)
    }

    val strategy = this.getMergeStrategy()
    val (matchingStop, shouldDropEvent) = this.findFirstMatchingStop(berthEvent, isStartEvent, strategy, berth)

    // Check if the berth event should be dropped
    if (shouldDropEvent || matchingStop.shouldDropEventIfAlreadyFilled(isStartEvent)) {
        // Returning existing list of stops
        return this
    }

    // Only merge the stop when we have a matching one
    if (matchingStop != null) {
        val mergedStop = matchingStop.mergeWithBerthEvent(berthEvent, isStartEvent)
        val mergedStopWithPomaInfo = setPomaInfoOnStop(mergedStop, berth)
        return this.replaceStop(matchingStop, mergedStopWithPomaInfo)
    }

    // Create a new stop when not matching
    val newStop = createNewStop(
        berthEvent = berthEvent,
        isStartEvent = isStartEvent,
        berth = berth,
        currentStops = this,
        currentVisit = currentVisit,
        findShipTraceForStopDetection = findShipTraceForStopDetection,
        findBerthsByLocation = findBerthsByLocation,
        findAnchoragesByLocation = findAnchoragesByLocation
    )

    if (newStop != null) {
        return this + newStop
    }

    // Couldn't create a new stop, returning the existing list
    return this
}

/**
 * Check if we should drop the event when the [Stop] already contains the data.
 */
private fun Stop?.shouldDropEventIfAlreadyFilled(isStartEvent: Boolean): Boolean {
    if (this == null) {
        // We don't drop the event because we might have to create a new stop instead
        return false
    }

    return if (isStartEvent) {
        this.berthStart != null
    } else {
        this.berthEnd != null
    }
}

/**
 * Update the poma fields on the [stop] with the provided [berth].
 *
 * @param stop The stop we want to add the berth info on.
 * @param berth The berth we want to use the info of to set on the [stop]
 * @return The [stop] updated with the [berth] information or the provided [stop] if [berth] is null.
 */
private fun setPomaInfoOnStop(stop: Stop, berth: Berth?): Stop {
    if (berth == null) {
        return stop
    }

    return stop.copy(
        type = StopType.BERTH,
        pomaType = StopType.BERTH,
        pomaId = berth._id,
        name = berth.nameLong ?: berth.name,
    )
}

private fun createNewStop(
    berthEvent: UniqueBerthEvent,
    isStartEvent: Boolean,
    berth: Berth?,
    currentStops: List<Stop>?,
    currentVisit: Visit,
    findShipTraceForStopDetection: FindShipTraceForStopDetection,
    findBerthsByLocation: FindBerthsByLocation,
    findAnchoragesByLocation: FindAnchoragesByLocation
): Stop? {
    val newStop = if (isStartEvent) {
        createNewStopOnStartEvent(berthEvent)
    } else {
        createNewStopOnEndEvent(berthEvent, currentStops, currentVisit, findShipTraceForStopDetection, findBerthsByLocation, findAnchoragesByLocation)
            // Could not create stop from end event
            ?: return null
    }

    return setPomaInfoOnStop(newStop, berth)
}

private fun createNewStopOnStartEvent(berthEvent: UniqueBerthEvent): Stop {
    return Stop(
        type = StopType.BERTH,
        startEventId = berthEvent._id,
        startTime = berthEvent.actualTime.atZone(ZoneOffset.UTC),
        startLocation = berthEvent.location.toVesselVoyageLocation(),
        endEventId = null,
        endTime = null,
        endLocation = null,
        actualLocation = null,
        actualTime = null,
        detectionVersion = StopDetectionVersion.BERTH_EVENT,
        accuracy = null,
        berthStart = berthEvent.toStopStartDetectionInfo(),
        berthEnd = null,
        aisStart = null,
        aisEnd = null
    )
}

private fun createNewStopOnEndEvent(
    berthEvent: UniqueBerthEvent,
    currentStops: List<Stop>?,
    currentVisit: Visit,
    findShipTraceForStopDetection: FindShipTraceForStopDetection,
    findBerthsByLocation: FindBerthsByLocation,
    findAnchoragesByLocation: FindAnchoragesByLocation
): Stop? {
    // Determine the maximum start time we can possible have for the stop we are about to create
    val potentialStartTime = currentStops?.lastOrNull()?.endTime
        ?: currentVisit.startTime

    // Create our fake stop as we need this to calculate the accurate stop start with
    val inaccurateStop = Stop(
        type = StopType.BERTH,
        startEventId = berthEvent._id,
        startTime = potentialStartTime,
        startLocation = berthEvent.location.toVesselVoyageLocation(),
        endEventId = berthEvent._id,
        endTime = berthEvent.actualTime.atZone(ZoneOffset.UTC),
        endLocation = berthEvent.location.toVesselVoyageLocation(),
        actualLocation = null,
        actualTime = null,
        detectionVersion = StopDetectionVersion.BERTH_EVENT,
        accuracy = null,
        berthStart = null,
        berthEnd = null,
        aisStart = null,
        aisEnd = null
    )

    // Find all trances needed to do our stop detection
    // We use the stop start time and end time to find the AIS traces related to our potential stop
    val potentialStopTraces = findShipTraceForStopDetection(currentVisit.imo, inaccurateStop)

    // We can't do any merging when we don't have a trace for this vessel, so we can just drop it
    if (potentialStopTraces.isEmpty()) {
        return null
    }

    // Use the stop finding mechanism to find all possible stop locations
    val actualStops = findActualStops(inaccurateStop, potentialStopTraces, berthEvent._id)

    // Prepare all found stops, so we can combine any stops together
    val preparedActualStops = prepareDetectedStops(
        detectedStops = actualStops,
        eventId = berthEvent._id,
        eventTime = berthEvent.actualTime.atZone(ZoneOffset.UTC),
        eventLocation = berthEvent.location.toVesselVoyageLocation(),
        findBerthsByLocation = findBerthsByLocation,
        findAnchoragesByLocation = findAnchoragesByLocation
    )

    // We can take the last stop because the stop for the berth event must be the latest one
    //  as the used AIS trace is ending on the end time of the berth event
    val lastStop = preparedActualStops.lastOrNull()

    // Only set the berth event specific data when we have a stop
    if (lastStop != null) {
        return lastStop.copy(
            type = StopType.BERTH,
            detectionVersion = StopDetectionVersion.BERTH_EVENT,
            berthEnd = berthEvent.toStopEndDetectionInfo()
        )
    }

    // We couldn't detect a stop from the AIS trace
    return null
}

/**
 * Determine the [MergeScenarioStrategy] of the provided [Stop]s.
 * This is later used to find the relevant stop for the given scenario and merge the relevant stop with the [UniqueBerthEvent].
 *
 * @return The selected [MergeScenarioStrategy].
 */
private fun List<Stop>.getMergeStrategy(): MergeScenarioStrategy {
    val latestStop = this.last()
    val isSingleStop = this.size == 1
    val isLastStopFinished = latestStop.isFinished()

    return if (isSingleStop) {
        if (isLastStopFinished) {
            MergeScenarioStrategy.SINGLE_STOP_FINISHED
        } else {
            MergeScenarioStrategy.SINGLE_STOP_ONGOING
        }
    } else {
        if (isLastStopFinished) {
            MergeScenarioStrategy.MULTIPLE_STOPS_ALL_FINISHED
        } else {
            MergeScenarioStrategy.MULTIPLE_STOPS_LATEST_ONGOING
        }
    }
}

/**
 * Merge the provided [UniqueBerthEvent] with the given [Stop].
 */
private fun Stop.mergeWithBerthEvent(
    berthEvent: UniqueBerthEvent,
    isStartEvent: Boolean
): Stop {
    return if (isStartEvent) {
        this.mergeWithBerthStartEvent(berthEvent)
    } else {
        this.mergeWithBerthEndEvent(berthEvent)
    }
}

/**
 * Find the first matching [Stop] which is different depending on the selected [mergeStrategy].
 *
 * @return The matching stop and a boolean indicating if the event should be dropped.
 */
private fun List<Stop>.findFirstMatchingStop(
    berthEvent: UniqueBerthEvent,
    isStartEvent: Boolean,
    mergeStrategy: MergeScenarioStrategy,
    berth: Berth?
): Pair<Stop?, Boolean> {
    val latestStop = this.last()
    when (mergeStrategy) {
        MergeScenarioStrategy.SINGLE_STOP_ONGOING -> {
            if (latestStop.isStopTimeBeforeEventOrClose(berthEvent, isStartEvent) ||
                latestStop.isStopInsideBerth(berth, isStartEvent)
            ) {
                return latestStop to false
            }
        }
        MergeScenarioStrategy.SINGLE_STOP_FINISHED -> {
            if (latestStop.isStopTimeBeforeEventOrClose(berthEvent, isStartEvent) &&
                latestStop.isLocationCloseEnough(berthEvent, isStartEvent)
            ) {
                return latestStop to false
            }
        }
        MergeScenarioStrategy.MULTIPLE_STOPS_LATEST_ONGOING -> {
            val matchingFinishedStop = getLastFinishedStopsMatchingBerthEvent(this, berth)

            // Only return the matching finished stop if the time between the event and stop is close enough
            if (matchingFinishedStop != null && matchingFinishedStop.isStopTimeCloseEnough(berthEvent, isStartEvent)) {
                if (shouldDropEvent(isStartEvent, matchingFinishedStop)) {
                    return null to true
                }

                return matchingFinishedStop to false
            }

            // We don't have a matching finished stop, check if the ongoing stop is relevant for our case
            // Skip any other ongoing stop if there is any
            if ((!isStartEvent || latestStop.isStopStartBerthMatchingEventBerth(berth)) &&
                latestStop.isStopTimeBeforeEventOrClose(berthEvent, isStartEvent)
            ) {
                return latestStop to false
            }
        }
        MergeScenarioStrategy.MULTIPLE_STOPS_ALL_FINISHED -> {
            val matchingFinishedStop = getLastFinishedStopsMatchingBerthEvent(this, berth)

            // Because all stops are finished, just check if the time of the stop and berth event are close enough
            if (matchingFinishedStop != null && matchingFinishedStop.isStopTimeCloseEnough(berthEvent, isStartEvent)) {
                if (shouldDropEvent(isStartEvent, matchingFinishedStop)) {
                    return null to true
                }

                return matchingFinishedStop to false
            }
        }
    }

    // Provided stops aren't matching
    return null to false
}

/**
 * Check if we should drop the berth event.
 * If the berth related fields are already filled, then we don't have to merge anything, but can just drop the event.
 */
private fun shouldDropEvent(isStartEvent: Boolean, stop: Stop): Boolean {
    // Drop the event if we already set the berth related field
    return if (isStartEvent) {
        stop.berthStart != null
    } else {
        stop.berthEnd != null
    }
}

/**
 * Get the stops split into a map having the key indicate if the stops are finished.
 */
private fun getSplitStops(stops: List<Stop>): Map<Boolean, List<Stop>> {
    return stops.groupBy { it.isFinished() }
}

private fun getLastFinishedStopsMatchingBerthEvent(
    stops: List<Stop>,
    berth: Berth?
): Stop? {
    val splitStops = getSplitStops(stops)
    val finishedStops = splitStops[true] ?: emptyList()

    val lastMatchingFinished = finishedStops.lastOrNull { finishedStop ->
        finishedStop.pomaId != null && finishedStop.pomaId == berth?._id
    }

    return lastMatchingFinished
}

/**
 * Check if the stop berth start is the same berth where we got an event for.
 */
private fun Stop.isStopStartBerthMatchingEventBerth(eventBerth: Berth?): Boolean {
    // No need to check when we don't have a berth start
    val stopBerth = this.berthStart ?: return true

    // Check if the berth from the stop and the event are the same
    return stopBerth.id == eventBerth?._id ||
        stopBerth.id == eventBerth?.authorityId
}

private fun Stop.isStopTimeCloseEnough(berthEvent: UniqueBerthEvent, isStartEvent: Boolean): Boolean {
    val relevantStopTime = this.getRelevantStopTime(isStartEvent)
    val durationBetween = Duration.between(relevantStopTime, berthEvent.actualTime.atZone(ZoneOffset.UTC)).abs()

    return durationBetween <= MERGE_FINISHED_TIME_STOP_AND_BERTH_EVENT
}

/**
 * Check if the provided [Stop] time happened before the event time of the [UniqueBerthEvent] to be merged together.
 */
private fun Stop.isStopTimeBeforeEventOrClose(berthEvent: UniqueBerthEvent, isStartEvent: Boolean): Boolean {
    val relevantStopTime = this.getRelevantStopTime(isStartEvent)

    return relevantStopTime.isBefore(berthEvent.actualTime.atZone(ZoneOffset.UTC)) ||
        relevantStopTime.isBefore(berthEvent.actualTime.atZone(ZoneOffset.UTC).plusMinutes(15))
}

private fun Stop.isStopInsideBerth(berth: Berth?, isStartEvent: Boolean): Boolean {
    // We can't check this if the provided berth is null
    if (berth == null) {
        return false
    }

    val stopLocation = this.getRelevantStopLocation(isStartEvent)
    val berthPolygon = berth.area.map { it.toVesselVoyageLocation() }

    return pointInPolygon(berthPolygon, stopLocation)
}

/**
 * Check if the provided [Stop] location is close enough to the location of the [UniqueBerthEvent] to be merged together.
 */
private fun Stop.isLocationCloseEnough(berthEvent: UniqueBerthEvent, isStartEvent: Boolean): Boolean {
    val relevantStopLocation = this.getRelevantStopLocation(isStartEvent)
    val distanceBetweenStopAndBerthEvent = haversineDistance(berthEvent.location.toVesselVoyageLocation(), relevantStopLocation)

    return distanceBetweenStopAndBerthEvent <= MERGE_DISTANCE_STOP_AND_BERTH_EVENT
}

/**
 * Get the most relevant time of the [Stop] used when comparing the stop with the berth event.
 */
private fun Stop.getRelevantStopTime(isStartEvent: Boolean): ZonedDateTime {
    return if (isStartEvent) {
        this.startTime
    } else {
        this.endTime ?: this.startTime
    }
}

/**
 * Get the most relevant location of the [Stop] used when comparing the stop with the berth event.
 */
private fun Stop.getRelevantStopLocation(isStartEvent: Boolean): Location {
    return if (isStartEvent) {
        this.actualLocation ?: this.startLocation
    } else {
        this.actualLocation ?: this.endLocation ?: this.startLocation
    }
}

private fun Stop.mergeWithBerthStartEvent(berthEvent: UniqueBerthEvent): Stop {
    val stopBerthEvent = berthEvent.toStopStartDetectionInfo()

    return copy(
        type = StopType.BERTH,
        startEventId = stopBerthEvent.id,
        startTime = stopBerthEvent.time,
        startLocation = stopBerthEvent.location,
        detectionVersion = StopDetectionVersion.BERTH_EVENT,
        berthStart = stopBerthEvent
    )
}

private fun Stop.mergeWithBerthEndEvent(berthEvent: UniqueBerthEvent): Stop {
    val stopBerthEvent = berthEvent.toStopEndDetectionInfo()

    return copy(
        type = StopType.BERTH,
        endEventId = stopBerthEvent.id,
        endTime = stopBerthEvent.time,
        endLocation = stopBerthEvent.location,
        detectionVersion = StopDetectionVersion.BERTH_EVENT,
        berthEnd = stopBerthEvent
    )
}

private fun UniqueBerthEvent.toStopStartDetectionInfo(): StopStartDetectionInfo {
    return StopStartDetectionInfo(
        id = this._id,
        location = this.location.toVesselVoyageLocation(),
        time = this.actualTime.atZone(ZoneOffset.UTC)
    )
}

private fun UniqueBerthEvent.toStopEndDetectionInfo(): StopEndDetectionInfo {
    return StopEndDetectionInfo(
        id = this._id,
        location = this.location.toVesselVoyageLocation(),
        time = this.actualTime.atZone(ZoneOffset.UTC)
    )
}

private fun List<Stop>.replaceStop(oldStop: Stop, newStop: Stop): List<Stop> {
    val currentIndex = this.indexOf(oldStop)

    val newStops = this.toMutableList()
    newStops[currentIndex] = newStop

    return newStops
}

/**
 * The selected strategy when merging a [Stop] with a [UniqueBerthEvent].
 */
private enum class MergeScenarioStrategy {
    /**
     * The strategy when the current esof has only one stop and it is still ongoing
     */
    SINGLE_STOP_ONGOING,

    /**
     * The strategy when the current esof has only one stop and it is finished
     */
    SINGLE_STOP_FINISHED,

    /**
     * The strategy when the current esof has multiple stops and the latest is still ongoing
     */
    MULTIPLE_STOPS_LATEST_ONGOING,

    /**
     * The strategy when the current esof has multiple stops and the latest is finished
     */
    MULTIPLE_STOPS_ALL_FINISHED
}
