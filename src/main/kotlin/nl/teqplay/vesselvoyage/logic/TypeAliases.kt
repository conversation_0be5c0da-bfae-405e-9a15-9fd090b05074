package nl.teqplay.vesselvoyage.logic

import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.vesselvoyage.model.Location
import nl.teqplay.vesselvoyage.model.Stop
import nl.teqplay.vesselvoyage.model.StopDetectionLocation
import nl.teqplay.vesselvoyage.model.lightweight.poma.Anchorage
import nl.teqplay.vesselvoyage.model.lightweight.poma.Berth
import nl.teqplay.vesselvoyage.model.lightweight.poma.Port
import java.time.ZonedDateTime

typealias FindPortById = (portId: String) -> Port?
typealias FindBerthById = (berthId: String) -> Berth?
typealias FindAnchorage = (name: String, location: Location) -> Anchorage?
typealias FindShipTraceForStopDetection = (imo: String, stop: Stop) -> List<StopDetectionLocation>
typealias FindShipTrace = (imo: String, startTime: ZonedDateTime, endTime: ZonedDateTime) -> List<AisHistoricMessage>
typealias FindBerthsByLocation = (location: Location) -> List<Berth>
typealias FindAnchoragesByLocation = (location: Location) -> List<Anchorage>
