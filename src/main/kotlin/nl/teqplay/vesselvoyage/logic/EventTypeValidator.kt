package nl.teqplay.vesselvoyage.logic

import nl.teqplay.aisengine.event.interfaces.AnchoredEvent
import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.interfaces.ShipMovingEvent
import nl.teqplay.aisengine.event.interfaces.StopEvent
import nl.teqplay.aisengine.event.interfaces.UniqueBerthEvent
import nl.teqplay.aisengine.event.model.AisStatusChangedEvent
import nl.teqplay.aisengine.event.model.LockEtaEvent
import nl.teqplay.aisengine.event.model.PortcallPilotBoardingEtaEvent
import nl.teqplay.aisengine.event.model.TrueDestinationChangedEvent
import nl.teqplay.aisengine.event.model.hamis.HamisEtaEvent
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType

object EventTypeValidator {

    fun isValid(event: Event): Bo<PERSON>an {
        return when (event) {
            is AnchoredEvent,
            is TrueDestinationChangedEvent,
            is PortcallPilotBoardingEtaEvent,
            is HamisEtaEvent,
            is LockEtaEvent,
            is ShipMovingEvent,
            is AisStatusChangedEvent,
            is UniqueBerthEvent,
            is StopEvent,
            is EncounterEvent -> {
                val encounter = event as? EncounterEvent
                val encounterType = encounter?.encounterType
                encounterType != EncounterEvent.EncounterType.TUG_WAITING_DEPARTURE // filter out
            }

            is AreaEvent -> {
                when (event.area.type) {
                    AreaType.PORT,
                    AreaType.END_OF_SEA_PASSAGE,
                    AreaType.PILOT_BOARDING_PLACE,
                    AreaType.ANCHOR,
                    AreaType.TERMINAL_MOORING_AREA,
                    AreaType.LOCK,
                    AreaType.APPROACH_POINT -> true

                    else -> false
                }
            }

            else -> false
        }
    }
}
