package nl.teqplay.vesselvoyage.logic

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.csi.model.ship.info.component.ShipDimensions
import nl.teqplay.platform.util.LocationUtils
import nl.teqplay.vesselvoyage.model.ESOF_STOP_MAX_COMBINE_DISTANCE
import nl.teqplay.vesselvoyage.model.ESOF_STOP_MAX_COMBINE_DISTANCE_SHORT_DURATION
import nl.teqplay.vesselvoyage.model.ESOF_STOP_MAX_COMBINE_DURATION
import nl.teqplay.vesselvoyage.model.ESOF_STOP_MAX_COMBINE_HEADING_DIFFERENCE
import nl.teqplay.vesselvoyage.model.ESOF_STOP_MAX_COMBINE_HEADING_DISTANCE
import nl.teqplay.vesselvoyage.model.ESOF_STOP_MAX_COMBINE_VISIT_FINISHED_DISTANCE
import nl.teqplay.vesselvoyage.model.ESOF_STOP_MAX_COMBINE_VISIT_FINISHED_DURATION
import nl.teqplay.vesselvoyage.model.ESOF_STOP_MAX_COMBINE_VISIT_FINISHED_SHORT_DURATION
import nl.teqplay.vesselvoyage.model.ESOF_STOP_MAX_COMBINE_VISIT_FINISHED_SMALL_DISTANCE
import nl.teqplay.vesselvoyage.model.ESOF_STOP_MAX_DISTANCE
import nl.teqplay.vesselvoyage.model.ESOF_STOP_MAX_INITIAL_SPEED
import nl.teqplay.vesselvoyage.model.ESOF_STOP_MAX_ONGOING_SPEED
import nl.teqplay.vesselvoyage.model.ESOF_STOP_MAX_OUTLIER_TRACKING
import nl.teqplay.vesselvoyage.model.ESOF_STOP_MIN_DURATION
import nl.teqplay.vesselvoyage.model.ESof
import nl.teqplay.vesselvoyage.model.Location
import nl.teqplay.vesselvoyage.model.LocationTime
import nl.teqplay.vesselvoyage.model.Stop
import nl.teqplay.vesselvoyage.model.StopDetectionLocation
import nl.teqplay.vesselvoyage.model.StopDetectionVersion
import nl.teqplay.vesselvoyage.model.StopEndDetectionInfo
import nl.teqplay.vesselvoyage.model.StopStartDetectionInfo
import nl.teqplay.vesselvoyage.model.StopType
import nl.teqplay.vesselvoyage.model.internal.DetectedStop
import nl.teqplay.vesselvoyage.model.lightweight.poma.Anchorage
import nl.teqplay.vesselvoyage.model.lightweight.poma.Berth
import nl.teqplay.vesselvoyage.model.lightweight.poma.PomaModel
import nl.teqplay.vesselvoyage.service.InfraCacheService.Companion.ANCHORAGE_SCALE
import nl.teqplay.vesselvoyage.service.InfraCacheService.Companion.BERTH_SCALE
import nl.teqplay.vesselvoyage.util.duration
import nl.teqplay.vesselvoyage.util.haversineDistance
import nl.teqplay.vesselvoyage.util.isFinished
import nl.teqplay.vesselvoyage.util.isNotFinished
import nl.teqplay.vesselvoyage.util.toPlatformLocation
import nl.teqplay.vesselvoyage.util.toScaled
import java.time.Duration
import java.time.ZonedDateTime
import kotlin.math.abs
import kotlin.math.max

private val log = KotlinLogging.logger {}
private typealias DetectedStopsList = MutableList<DetectedStop>

/**
 * This class contains all the logic to determine the stops of a visit, doing multiple steps to eventually add the result to the [ESof].
 *
 * Old mechanism:
 * 1. A movement start event is processed.
 * 2. The latest stop is finished using the location and time of the movement start event.
 * 3. The actual location is set based on the trace that happened while this stop was going on.
 *
 * New mechanism:
 * 1. A movement start event is processed.
 * 2. Traces between the start time of the latest stop and the movement start event are used to detect any stops.
 * 3. Iterate over the trace, creating stops when the vessel stayed below the [ESOF_STOP_MAX_INITIAL_SPEED]
 *  and didn't move any big distances from the stop starting point which is specified with [ESOF_STOP_MAX_DISTANCE].
 * 4. Once all stops are detected, combine all stops that are close to each other.
 * 5. Prepare all combined detected stops, filtering out too short stops and unfinished ones.
 * 6. Replace the old stop in the [ESof] that has been created with the movement end event with the newly detected stops.
 *
 * If the new mechanism isn't able to find anything (e.g. the trace isn't complete enough) than fallback on the old mechanism.
 */
class CalculateStops {
    companion object {
        fun tryToCombineStopsOnVisitComplete(stops: List<Stop>?, shipDimensions: ShipDimensions?): List<Stop> {
            // Nothing to combine if we only have 1 stop or no stops in our visit
            if (stops == null || stops.size <= 1) {
                return stops ?: emptyList()
            }

            val resultStops = mutableListOf<Stop>()
            val combinableStops = mutableListOf(stops.first())
            val lastStop = stops.last()

            for ((previousStop, stop) in stops.zipWithNext()) {
                val isLastStop = lastStop == stop
                val previousEndTime = previousStop.endTime ?: previousStop.startTime
                val previousLocation = previousStop.actualLocation ?: previousStop.startLocation
                val currentStartTime = stop.startTime
                val currentLocation = stop.actualLocation ?: stop.startLocation
                val timeBetween = Duration.between(previousEndTime, currentStartTime)

                if (timeBetween < ESOF_STOP_MAX_COMBINE_VISIT_FINISHED_SHORT_DURATION) {
                    tryToCombineStops(stop, combinableStops, resultStops, isLastStop)

                    // We can combine this stop with the previous one, check the next one
                    continue
                }

                val distanceBetween = haversineDistance(previousLocation, currentLocation)

                val shipLength = shipDimensions?.length
                val allowedDurationBetween = getAllowedDurationBetweenWithShipLength(shipLength)

                val isCloseDistance = distanceBetween <= ESOF_STOP_MAX_COMBINE_VISIT_FINISHED_SMALL_DISTANCE
                val isBigVessel = (shipLength ?: 0.0) >= 150
                val isCombinableDistance = distanceBetween <= ESOF_STOP_MAX_COMBINE_VISIT_FINISHED_DISTANCE
                val isAllowedDuration = timeBetween <= allowedDurationBetween

                if ((isCloseDistance && isBigVessel) || (isCombinableDistance && isAllowedDuration)) {
                    tryToCombineStops(stop, combinableStops, resultStops, isLastStop)

                    // We can combine this stop with the previous one, check the next one
                    continue
                } else {
                    // Combine the previous combined stops
                    if (combinableStops.isNotEmpty()) {
                        combineStops(combinableStops, resultStops)
                    }

                    // We could not combine the current stop with anything
                    combinableStops.add(stop)

                    // We are at the last stop, always combine to the result
                    if (isLastStop) {
                        combineStops(combinableStops, resultStops)
                    }
                }
            }

            return resultStops
        }

        private fun getAllowedDurationBetweenWithShipLength(shipLength: Double?): Duration {
            val defaultDuration = ESOF_STOP_MAX_COMBINE_VISIT_FINISHED_DURATION.values.last()

            if (shipLength == null) {
                return defaultDuration
            }

            return ESOF_STOP_MAX_COMBINE_VISIT_FINISHED_DURATION.entries.find { (length, _) ->
                shipLength >= length
            }?.value ?: defaultDuration
        }

        private fun tryToCombineStops(currentStop: Stop, combinableStops: MutableList<Stop>, resultStops: MutableList<Stop>, isLastStop: Boolean) {
            if (isLastStop) {
                combinableStops.add(currentStop)

                combineStops(combinableStops, resultStops)
            }
        }

        private fun combineStops(combinableStops: MutableList<Stop>, resultStops: MutableList<Stop>) {
            // Instantly add a singular stop without trying to combine to avoid needing to set any of the derived fields
            if (combinableStops.size == 1) {
                resultStops.add(combinableStops.first())
                combinableStops.clear()
                return
            }

            val lastStopTime = combinableStops.last().endTime

            // There is no way to combine when we don't have a stop time for the last stop, just add them to the result stops
            if (lastStopTime == null) {
                resultStops.addAll(combinableStops)
                combinableStops.clear()
                return
            }

            val newStop = combineStops(combinableStops)
            resultStops.add(newStop)
            combinableStops.clear()
        }

        private fun duplicateActualLocationBasedOnStopDuration(stop: Stop): List<Location> {
            // Return the location of the stop when we don't have an end time, meaning we have an ongoing stop
            if (stop.endTime == null) {
                return listOf(stop.actualLocation ?: stop.startLocation)
            }

            val duration = Duration.between(stop.startTime, stop.endTime)
            // Using hours seem to work for all cases so far.
            // We could make this into minutes if a more accurate duration weighted actual stop location is needed.
            val stopDuplicateAmount = duration.toHours().toInt()
            val duplicateAmount = max(stopDuplicateAmount, 1)

            return List(duplicateAmount) { stop.actualLocation ?: stop.startLocation }
        }

        private fun combineStops(stops: List<Stop>): Stop {
            val firstStop = stops.first()
            val lastStop = stops.last()

            val startTime = firstStop.startTime
            val endTime = lastStop.endTime!!

            val timeBetween = Duration.between(startTime, endTime)
            val actualTime = startTime + timeBetween.dividedBy(2)

            val stopLocations = stops.map { duplicateActualLocationBasedOnStopDuration(it) }.flatten()
            val currentLatitudes = stopLocations.map { it.latitude }
            val currentLongitudes = stopLocations.map { it.longitude }
            val actualLocation = Location(
                latitude = currentLatitudes.average(),
                longitude = currentLongitudes.average()
            )

            val type = stops.firstOrNull { it.type != StopType.UNCLASSIFIED }?.type
            val aisType = stops.firstOrNull { it.aisType != StopType.UNCLASSIFIED }?.aisType
            val pomaType = stops.firstOrNull { it.pomaType != StopType.UNCLASSIFIED }?.pomaType

            val startInfo = StopStartDetectionInfo(
                id = firstStop.startEventId,
                time = startTime,
                location = firstStop.startLocation
            )

            val endInfo = StopEndDetectionInfo(
                id = lastStop.endEventId,
                time = endTime,
                location = lastStop.endLocation
            )

            return Stop(
                type = type ?: StopType.UNCLASSIFIED,
                aisType = aisType ?: StopType.UNCLASSIFIED,
                pomaType = pomaType ?: StopType.UNCLASSIFIED,
                startEventId = firstStop.startEventId,
                startTime = startTime,
                startLocation = firstStop.startLocation,
                endEventId = lastStop.endEventId,
                endTime = endTime,
                endLocation = lastStop.endLocation,
                actualLocation = actualLocation,
                actualTime = actualTime,
                detectionVersion = StopDetectionVersion.COMBINED_STOPS_VISIT_FINISHED,
                accuracy = null,
                berthStart = firstStop.berthStart,
                berthEnd = lastStop.berthEnd,
                aisStart = startInfo,
                aisEnd = endInfo
            )
        }

        /**
         * Prepare the [detectedStops] before we can use it to put on the [ESof].
         *
         * @param detectedStops The stops that have been found by the detection mechanism.
         * @param eventId The id of the event that triggered the finding of stops.
         * @param eventTime The time of the event that triggered the finding of stops.
         * @param eventLocation The location of the event that triggered the finding of stops.
         * @return A list of prepared stops, containing the needed [Stop.actualLocation]
         */
        fun prepareDetectedStops(
            detectedStops: List<DetectedStop>,
            eventId: String,
            eventTime: ZonedDateTime,
            eventLocation: Location,
            findBerthsByLocation: FindBerthsByLocation,
            findAnchoragesByLocation: FindAnchoragesByLocation
        ): List<Stop> {
            // When only 1 stop is found instantly start preparing it
            if (detectedStops.size == 1) {
                return prepareSingleStop(detectedStops.first(), eventId, eventTime, eventLocation)
                    .fillPomaStop(findBerthsByLocation, findAnchoragesByLocation)
            }

            // No stops were found, so nothing to prepare
            if (detectedStops.isEmpty()) {
                return emptyList()
            }

            val preparedDetectedStops = detectedStops.map { detectedStop ->
                val correctedStop = detectedStop.stop.copy(
                    actualLocation = determineActualStopLocation(detectedStop),
                    actualTime = determineActualStopTime(detectedStop)
                ).fillPomaStop(findBerthsByLocation, findAnchoragesByLocation)

                log.debug { "Prepared stop actual location and time (traces: ${detectedStop.trace.size} location: ${correctedStop.actualLocation}, time: ${correctedStop.actualTime})" }
                detectedStop.copy(stop = correctedStop)
            }

            // We have multiple stops, try to combine them
            val combinedStops = combineStopsIfPossible(preparedDetectedStops, eventId)

            // If we only have 1 combined stop we can just prepare that one
            if (combinedStops.size == 1) {
                return prepareSingleStop(combinedStops.first(), eventId, eventTime, eventLocation)
                    .fillPomaStop(findBerthsByLocation, findAnchoragesByLocation)
            }

            // When we have multiple combined stops, filter out all unfinished stops and ensure their length is long enough
            val preparedStops = combinedStops
                .filter { detectedStop -> detectedStop.stop.isFinished() && detectedStop.stop.duration() >= ESOF_STOP_MIN_DURATION }
                .map { detectedStop ->
                    detectedStop.stop.copy(
                        actualLocation = determineActualStopLocation(detectedStop),
                        actualTime = determineActualStopTime(detectedStop)
                    )
                }

            log.debug { "Ended up with ${preparedStops.size} stops after filtering out not finished ones and too short ones" }

            return preparedStops
        }

        /**
         * Helper function to determine the actual location of a stop based on the provided [detectedStop].
         * Use the trace of said detected stop by removing any outliers for latitude and longitude separately.
         *
         * @param detectedStop the detected stop including a trace of AIS points
         * @return The location where the vessel most likely stopped.
         */
        private fun determineActualStopLocation(detectedStop: DetectedStop): Location {
            val trace = detectedStop.trace

            // We can't calculate the actual location when we don't have a trace long enough
            if (trace.size < 4) {
                return detectedStop.stop.startLocation
            }

            return determineActualStopLocation(trace)
        }

        private fun determineActualStopLocation(trace: List<StopDetectionLocation>): Location {
            val latitudes = trace.map { it.latitude }.sorted()
            val longitudes = trace.map { it.longitude }.sorted()

            // Only take a selected amount of items to filter out outliers
            val (fromIndex, toIndex) = trace.getIndexesToFilterOutliers()

            val latitude = latitudes.subList(fromIndex, toIndex).average()
            val longitude = longitudes.subList(fromIndex, toIndex).average()

            return Location(
                latitude = latitude,
                longitude = longitude
            )
        }

        /**
         * Helper function to determine the actual time of the stop.
         * This is used later to generate the simplified stop trace.
         *
         * @param detectedStop The detected stop containing the start and end time of the stop.
         * @return The time when the vessel most likely stopped.
         */
        private fun determineActualStopTime(detectedStop: DetectedStop): ZonedDateTime {
            val startTime = detectedStop.stop.startTime
            val endTime = detectedStop.stop.endTime ?: return startTime

            return determineActualStopTime(startTime, endTime)
        }

        private fun determineActualStopTime(startTime: ZonedDateTime, endTime: ZonedDateTime): ZonedDateTime {
            val timeBetween = Duration.between(startTime, endTime)
            return startTime + timeBetween.dividedBy(2)
        }

        /**
         * Prepare a stop if the provided start and end time is long enough to be an actual stop.
         * Otherwise, replace the end time and location with the information from the [MovementEvent]
         *
         * @param detectedStop The single stop that needs to be prepared.
         * @param eventId The movement start event that triggerd the detection of stops.
         * @return A list with only the single prepared stop.
         */
        private fun prepareSingleStop(
            detectedStop: DetectedStop,
            eventId: String,
            eventTime: ZonedDateTime,
            eventLocation: Location
        ): List<Stop> {
            val stop = detectedStop.stop

            val preparedStop = if (stop.endTime != null) {
                stop.copy(
                    actualLocation = determineActualStopLocation(detectedStop),
                    actualTime = determineActualStopTime(detectedStop)
                )
            } else {
                val aisEnd = StopEndDetectionInfo(
                    id = eventId,
                    location = eventLocation,
                    time = eventTime
                )

                stop.copy(
                    endEventId = eventId,
                    endTime = eventTime,
                    endLocation = eventLocation,
                    actualLocation = stop.startLocation,
                    actualTime = stop.startTime,
                    // Don't set the AisEnd if it was detected by a berth event
                    aisEnd = aisEnd.takeIf { stop.detectionVersion != StopDetectionVersion.BERTH_EVENT }
                )
            }

            if (preparedStop.duration() >= ESOF_STOP_MIN_DURATION) {
                log.debug { "Ended up with 1 stop after preparing" }
                return listOf(preparedStop)
            }

            log.debug { "Ended up with 0 stops after preparing" }
            return emptyList()
        }

        /**
         * Check if the current stop should be merged with the stops we are currently trying to combine.
         *
         * @param startDetectedStop The stop we started combining.
         * @param previousDetectedStop The previous stop we combined with our start stop.
         * @param currentDetectedStop The current stop we are trying to combine.
         * @return True when we can combine the stop.
         */
        private fun shouldMergeStops(
            startDetectedStop: DetectedStop,
            previousDetectedStop: DetectedStop?,
            currentDetectedStop: DetectedStop
        ): Boolean {
            val startStop = startDetectedStop.stop
            val previousStop = previousDetectedStop?.stop
            val currentStop = currentDetectedStop.stop

            val currentStopLocation = currentStop.actualLocation ?: currentStop.startLocation
            val startStopLocation = startStop.actualLocation ?: startStop.startLocation

            val distanceBetweenStartStop = haversineDistance(currentStopLocation, startStopLocation)

            if (hasBigHeadingChangeHappened(startDetectedStop, currentDetectedStop)) {
                return if (distanceBetweenStartStop < ESOF_STOP_MAX_COMBINE_HEADING_DISTANCE) {
                    log.debug { "Merging stop with turn (distance between start: $distanceBetweenStartStop)" }
                    true
                } else {
                    // Don't attempt to merge this stop if such a big heading change happened
                    false
                }
            }

            if (distanceBetweenStartStop < ESOF_STOP_MAX_COMBINE_DISTANCE) {
                log.debug { "Merging stop (distance between start: $distanceBetweenStartStop)" }
                return true
            }

            val previousStopLocation = previousStop?.actualLocation ?: previousStop?.startLocation
            val distanceBetweenPreviousStop = previousStopLocation?.let { haversineDistance(currentStopLocation, it) }

            // Do more aggressive checks when we know the current stop is not a berth
            if (currentStop.type != StopType.BERTH) {
                val isSmallEnoughDistanceWithPrevious = distanceBetweenPreviousStop != null && distanceBetweenPreviousStop < ESOF_STOP_MAX_COMBINE_DISTANCE

                // No need to do any checks using the time when we already know distance between the previous one was small enough to be merged together
                if (isSmallEnoughDistanceWithPrevious) {
                    log.debug { "Merging non berth stop small distance (distance between previous: $distanceBetweenPreviousStop)" }
                    return true
                }

                val timeBetweenPrevious = previousStop?.let { Duration.between((it.endTime ?: it.startTime), currentStop.startTime) }
                val isShortTimeBetweenPrevious = timeBetweenPrevious != null && timeBetweenPrevious < ESOF_STOP_MAX_COMBINE_DURATION
                val isSmallEnoughBigDistanceWithStart = distanceBetweenStartStop < ESOF_STOP_MAX_COMBINE_DISTANCE_SHORT_DURATION

                if (isShortTimeBetweenPrevious && isSmallEnoughBigDistanceWithStart) {
                    log.debug { "Merging non berth stop short time (distance between start: $distanceBetweenStartStop, time between: $timeBetweenPrevious)" }
                    return true
                }
            }

            return false
        }

        /**
         * Check if the heading difference between the [startDetectedStop] and [currentDetectedStop] are significant.
         *
         * @param startDetectedStop The stop we started combining.
         * @param currentDetectedStop The current stop we are trying to combine.
         * @return True when the difference between the two stops heading is greater than [ESOF_STOP_MAX_COMBINE_HEADING_DIFFERENCE]
         */
        private fun hasBigHeadingChangeHappened(startDetectedStop: DetectedStop, currentDetectedStop: DetectedStop): Boolean {
            val startStopHeading = getAverageHeading(startDetectedStop.trace)
            val currentStopHeading = getAverageHeading(currentDetectedStop.trace)

            return abs(startStopHeading - currentStopHeading) > ESOF_STOP_MAX_COMBINE_HEADING_DIFFERENCE
        }

        /**
         * Calculate the average heading, filtering out outliers.
         */
        private fun getAverageHeading(trace: List<StopDetectionLocation>): Double {
            val headings = trace.mapNotNull { it.heading }

            if (headings.isEmpty()) {
                return 0.0
            }

            if (headings.size < 4) {
                return headings.average()
            }

            // Filter out any outliers as the true heading can sometimes flip to unreasonable values
            val (fromIndex, toIndex) = headings.getIndexesToFilterOutliers()

            return headings.sorted()
                .subList(fromIndex, toIndex)
                .average()
        }

        /**
         * Attempt to combine the given list of detected stops when they overlap.
         *
         * @param detectedStops The stops we initially found using the new detection mechanism.
         * @param startEvent The movement start event that triggerd the detection of stops.
         * @return A list of combined stops.
         */
        private fun combineStopsIfPossible(detectedStops: List<DetectedStop>, eventId: String): List<DetectedStop> {
            var startStop = detectedStops.first()
            var endStop: DetectedStop? = null
            val combinedStops = mutableListOf<DetectedStop>()
            val combinableStops = mutableListOf(startStop)

            for ((i, detectedStop) in detectedStops.withIndex()) {
                // No need to check if the same stop can be combined with each other
                if (detectedStop == startStop) {
                    continue
                }

                if (shouldMergeStops(
                        startDetectedStop = startStop,
                        previousDetectedStop = endStop,
                        currentDetectedStop = detectedStop
                    )
                ) {
                    // Stop can be combined, set it as the potential endStop for now
                    endStop = detectedStop
                    combinableStops.add(detectedStop)
                } else {
                    // Combine the previous stop if possible
                    if (endStop != null) {
                        val combinedStop = combineStop(combinableStops, eventId)

                        // Set the endStop to null again as we can get a single stop where no combining is needed after this one
                        endStop = null
                        combinedStops.add(combinedStop)
                    } else {
                        // We couldn't combine any of the stops, adding to the results
                        combinedStops.add(startStop)
                    }

                    // Set the startStop to the current, so we can check if we need to combine the stop on the next execution
                    startStop = detectedStop
                    combinableStops.clear()
                    combinableStops.add(detectedStop)
                }

                // We are currently iterating through the last stop, add it to the combinedStops list
                if (i == detectedStops.lastIndex) {
                    // We can combine stops here, do so before adding this stop
                    if (endStop != null) {
                        val combinedStop = combineStop(combinableStops, eventId)
                        combinedStops.add(combinedStop)
                    } else {
                        // Always add the last stop to the combinedStops when we have no stop to combine it with
                        combinedStops.add(detectedStop)
                    }
                }
            }

            log.debug { "Combined ${detectedStops.size} stops to ${combinedStops.size} stops" }

            return combinedStops
        }

        private fun combineStop(stops: List<DetectedStop>, eventId: String): DetectedStop {
            // Just return the first stop if we only have one to combine
            if (stops.size == 1) {
                return stops.first()
            }

            val allTraces = stops.map { it.trace }.flatten()

            val startStop = stops.first().stop
            val endStop = stops.last().stop
            val endTime = endStop.endTime ?: endStop.startTime

            // Recalculate the actual location and time based on all the traces and the and new end time of the stop
            val actualLocation = determineActualStopLocation(allTraces)
            val actualTime = determineActualStopTime(startStop.startTime, endTime)

            val endEventId = endStop.endEventId ?: eventId
            val endLocation = endStop.endLocation ?: endStop.startLocation
            val aisEnd = StopEndDetectionInfo(
                id = endEventId,
                location = endLocation,
                time = endTime
            )

            return DetectedStop(
                stop = startStop.copy(
                    endEventId = endEventId,
                    endTime = endTime,
                    endLocation = endLocation,
                    actualLocation = actualLocation,
                    actualTime = actualTime,
                    // Don't set the AisEnd if the end stop we are merging with was detected by a berth event
                    aisEnd = aisEnd.takeIf { endStop.detectionVersion != StopDetectionVersion.BERTH_EVENT }
                ),
                trace = allTraces
            )
        }

        /**
         * Function to find all stops in the given [stopTraces].
         *
         * @param finishedStopViaMovementEvent The original stop created by the movement end event
         *  which has been finished by the movement start event.
         * @param stopTraces All the trace that happened in the duration of the [finishedStopViaMovementEvent].
         * @param eventId The id of the event that triggered the finding of stops.
         * @return A list of stops detected via the [stopTraces].
         */
        fun findActualStops(
            finishedStopViaMovementEvent: Stop,
            stopTraces: List<StopDetectionLocation>,
            eventId: String
        ): List<DetectedStop> {
            if (stopTraces.isEmpty()) {
                return emptyList()
            }

            val trackedTraces = mutableListOf<StopDetectionLocation>()
            val detectedStops = mutableListOf<DetectedStop>()

            val lastTrace = stopTraces.last()

            for ((previousTrace, trace) in stopTraces.zipWithNext()) {
                val isLastTrace = lastTrace == trace

                detectedStops.detectStopInTrace(
                    stopTrace = trace,
                    lastStopTrace = previousTrace,
                    isLastTrace = isLastTrace,
                    finishedStopViaMovementEvent = finishedStopViaMovementEvent,
                    eventId = eventId,
                    trackedTraces = trackedTraces,
                    fullStopTrace = stopTraces
                )
            }

            log.debug { "Detected ${detectedStops.size} potential stops in the duration of the original stop" }
            return detectedStops
        }

        /**
         * Detect a stop in the trace, adding it to the list when a new one is created
         *  or attempting to finish one if tracking an ongoing stop.
         *
         * @param stopTrace The stop trace we are currently checking.
         * @param lastStopTrace The stop trace of the previous time we were checking.
         * @param isLastTrace True when this is the last trace of the list.
         * @param finishedStopViaMovementEvent The original stop created by the movement end event
         *  which has been finished by the movement start event.
         * @param eventId The id of the event that triggered the finding of stops.
         * @param trackedTraces A list of traces that are currently tracked to potentially mark the end of the stop.
         *  This is done, so we can do jitter detection in the trace to avoid unneeded creation of new stops.
         */
        private fun DetectedStopsList.detectStopInTrace(
            stopTrace: StopDetectionLocation,
            lastStopTrace: StopDetectionLocation,
            isLastTrace: Boolean,
            finishedStopViaMovementEvent: Stop,
            eventId: String,
            trackedTraces: MutableList<StopDetectionLocation>,
            fullStopTrace: List<StopDetectionLocation>
        ) {
            val lastStop = this.lastOrNull()
            val distanceBetweenPoints = haversineDistance(lastStopTrace.toLocation(), stopTrace.toLocation())
            val timeBetween = Duration.between(lastStopTrace.time, stopTrace.time).toMillis()
            val speedBetweenPoints = LocationUtils.calculateSpeedBetweenTwoPoints(distanceBetweenPoints, timeBetween.toFloat())

            // Check the distance of an ongoing stop
            if (lastStop?.stop?.isNotFinished() == true) {
                val distanceBetweenStartAndCurrent = haversineDistance(lastStop.stop.startLocation, stopTrace.toLocation())

                if (distanceBetweenStartAndCurrent >= ESOF_STOP_MAX_DISTANCE) {
                    log.debug { "Distance between start of stop is too big, attempting to finish (distance: $distanceBetweenStartAndCurrent, stop: $lastStop)" }
                    // Moved significantly away from the initial start of the stop, could potentially end the stop
                    this.tryToFinishLastStop(lastStopTrace, isLastTrace, eventId, trackedTraces, fullStopTrace)

                    return
                }
            }

            // If there is no stop ongoing, be strict with the speed check and do a distance check
            if (lastStop == null || lastStop.stop.isFinished()) {
                // We are not moving and the distance between points is small enough to create a new stop
                if (speedBetweenPoints < ESOF_STOP_MAX_INITIAL_SPEED && distanceBetweenPoints < ESOF_STOP_MAX_DISTANCE) {
                    log.debug { "Creating new stop" }
                    this.add(
                        DetectedStop(
                            stop = createNewStop(finishedStopViaMovementEvent, lastStopTrace),
                            trace = emptyList()
                        )
                    )

                    // We added a new stop, so we can clear the old tracked traces
                    trackedTraces.clear()
                }
            } else if (speedBetweenPoints < ESOF_STOP_MAX_ONGOING_SPEED) {
                log.trace { "Stop ongoing, continuing" }
                // We can clear the tracked traces, if something was in here than the previous traces were just jitters and can be ignored.
                trackedTraces.clear()
                return
            } else {
                // When the stop is already finished, no need to track if the movement point is an outlier
                if (lastStop.stop.isFinished()) {
                    log.debug { "Found big movement but stop already finished, continuing" }
                    return
                }

                log.debug { "Found big movement, trying to finish stop" }
                // Too much movement happened after the stop, we can end the stop here
                this.tryToFinishLastStop(lastStopTrace, isLastTrace, eventId, trackedTraces, fullStopTrace)
            }
        }

        /**
         * Try to finish the last stop we have.
         * End the stop when the amount of tracked traces is above a certain threshold defined in [ESOF_STOP_MAX_OUTLIER_TRACKING].
         *
         * @param stopTrace The stop trace we are currently checking.
         * @param isLastTrace True when this is the last trace of the list.
         * @param eventId The id of the event that triggered the finding of stops.
         * @param trackedTraces A list of traces that are currently tracked to potentially mark the end of the stop.
         *  This is done, so we can do jitter detection in the trace to avoid unneeded creation of new stops.
         */
        private fun DetectedStopsList.tryToFinishLastStop(
            stopTrace: StopDetectionLocation,
            isLastTrace: Boolean,
            eventId: String,
            trackedTraces: MutableList<StopDetectionLocation>,
            fullStopTrace: List<StopDetectionLocation>
        ) {
            trackedTraces.add(stopTrace)

            // Only finish the latest stop if trackedTraces has more points than set in ESOF_STOP_MAX_OUTLIER_TRACKING.
            // We can instantly finish the stop if the current trace we are trying to finish is the last one.
            if (trackedTraces.size > ESOF_STOP_MAX_OUTLIER_TRACKING || isLastTrace) {
                val lastStop = this.last()
                val finishTrace = if (trackedTraces.size == 1) {
                    // When this happens isLastTrace was true and only the current trace we are checking is being tracked
                    trackedTraces.first()
                } else {
                    // We take the 2nd movement in our tracked traces as the first one is always the ship still at the stop
                    trackedTraces[1]
                }

                log.debug { "Stop finished, setting end of the stop" }
                this.replaceLast {
                    finishStop(lastStop.stop, finishTrace, eventId, fullStopTrace)
                }
                trackedTraces.clear()
            }
        }

        /**
         * Create a new stop using the provided [lastStopTrace]
         *  and using the [finishedStopViaMovementEvent] to set the startEventId and type of the stop.
         *
         * @param finishedStopViaMovementEvent The original stop created by the movement end event
         *  which has been finished by the movement start event.
         * @param lastStopTrace The stop trace we last checked where a new stop has been detected.
         * @return The newly created stop detected via the traces.
         */
        private fun createNewStop(finishedStopViaMovementEvent: Stop, lastStopTrace: StopDetectionLocation): Stop {
            return Stop(
                type = finishedStopViaMovementEvent.type,
                aisType = finishedStopViaMovementEvent.aisType,
                pomaType = finishedStopViaMovementEvent.pomaType,
                startEventId = finishedStopViaMovementEvent.startEventId,
                startTime = lastStopTrace.time,
                startLocation = lastStopTrace.toLocation(),
                endEventId = null,
                endTime = null,
                endLocation = null,
                actualLocation = null,
                actualTime = null,
                detectionVersion = StopDetectionVersion.TRACE_BETWEEN_MOVEMENT_EVENT,
                accuracy = null,
                berthStart = finishedStopViaMovementEvent.berthStart,
                berthEnd = finishedStopViaMovementEvent.berthEnd,
                aisStart = StopStartDetectionInfo(
                    id = finishedStopViaMovementEvent.startEventId,
                    location = lastStopTrace.toLocation(),
                    time = lastStopTrace.time
                ),
                aisEnd = null
            )
        }

        /**
         * When not stop can be calculated using the new mechanism.
         * Use the old stop that we already determined and together with trace of the stop location
         *  the actual location like we used to.
         *
         * @param oldStop The old stop that has been determined via the [MovementEvent].
         * @param stopTraces All the traces we have during the timespan of this old stop.
         * @return A list containing the [oldStop], provided with the calculated [Stop.actualLocation].
         */
        fun fallbackOnOldStopHandling(
            oldStop: Stop,
            stopTraces: List<StopDetectionLocation>,
            findBerthsByLocation: FindBerthsByLocation,
            findAnchoragesByLocation: FindAnchoragesByLocation,
        ): List<Stop> {
            val actualStopLocation = calculateStopLocation(oldStop, stopTraces.map { it.toLocationTime() })
            val correctedStop = if (actualStopLocation != null) {
                oldStop.copy(
                    actualLocation = Location(actualStopLocation.latitude, actualStopLocation.longitude),
                    actualTime = actualStopLocation.time
                )
            } else {
                oldStop
            }

            return listOf(correctedStop).fillPomaStop(findBerthsByLocation, findAnchoragesByLocation)
        }

        private fun finishStop(
            stop: Stop,
            finishTrace: StopDetectionLocation,
            eventId: String,
            fullStopTrace: List<StopDetectionLocation>,
        ): DetectedStop {
            val finishedStop = stop.copy(
                endEventId = eventId,
                endTime = finishTrace.time,
                endLocation = finishTrace.toLocation(),
                aisEnd = StopEndDetectionInfo(
                    id = eventId,
                    location = finishTrace.toLocation(),
                    time = finishTrace.time
                )
            )

            val startTraceIndex = fullStopTrace.indexOfFirst { it.time == finishedStop.startTime }
                .coerceAtLeast(0)

            val finishedTraceIndex = fullStopTrace.indexOf(finishTrace)

            log.debug { "Stop trace determined (fromIndex: $startTraceIndex, toIndex: $finishedTraceIndex)" }

            return DetectedStop(
                stop = finishedStop,
                trace = fullStopTrace.subList(startTraceIndex, finishedTraceIndex)
            )
        }

        private fun <T> MutableList<T>.replaceLast(replaceStop: () -> T) {
            val updatedStop = replaceStop()
            this[this.lastIndex] = updatedStop
        }

        private fun List<*>.getIndexesToFilterOutliers(): Pair<Int, Int> {
            val itemsToSkipEachSide = this.size * 0.90
            val fromIndex = (this.size - itemsToSkipEachSide).toInt().coerceAtLeast(0)
            val toIndex = this.lastIndex - fromIndex

            return fromIndex to toIndex
        }

        private fun StopDetectionLocation.toLocation(): Location {
            return Location(
                latitude = this.latitude,
                longitude = this.longitude
            )
        }

        private fun StopDetectionLocation.toLocationTime(): LocationTime {
            return LocationTime(
                latitude = this.latitude,
                longitude = this.longitude,
                time = this.time
            )
        }

        /**
         * Given a list of stops, iterate over them and fill all of them with their known poma type if
         */
        fun List<Stop>.fillPomaStop(
            findBerthsByLocation: FindBerthsByLocation,
            findAnchoragesByLocation: FindAnchoragesByLocation
        ): List<Stop> {
            return this.map { stop ->
                stop.fillPomaStop(findBerthsByLocation, findAnchoragesByLocation)
            }
        }

        fun Stop.fillPomaStop(
            findBerthsByLocation: FindBerthsByLocation,
            findAnchoragesByLocation: FindAnchoragesByLocation
        ): Stop {
            val pomaEntity = this.determinePomaStop(findBerthsByLocation, findAnchoragesByLocation)

            return when (pomaEntity) {
                is Berth -> this.copy(
                    pomaId = pomaEntity._id,
                    name = pomaEntity.nameLong?.takeIf(String::isNotBlank) ?: pomaEntity.name,
                    type = StopType.BERTH,
                    pomaType = StopType.BERTH
                )

                is Anchorage -> this.copy(
                    pomaId = pomaEntity._id,
                    name = pomaEntity.name,
                    type = StopType.ANCHOR_AREA,
                    pomaType = StopType.ANCHOR_AREA
                )

                else -> this.copy(pomaType = StopType.UNCLASSIFIED)
            }
        }

        /**
         * Determine the poma stop type using the actual location of the stop.
         */
        private fun Stop.determinePomaStop(
            findBerthsByLocation: FindBerthsByLocation,
            findAnchoragesByLocation: FindAnchoragesByLocation
        ): PomaModel? {
            val stopLocation = this.actualLocation ?: this.startLocation
            val stopPlatformLocation = stopLocation.toPlatformLocation()
            val berthStopLocationKey = stopLocation.toScaled(BERTH_SCALE)
            val berths = findBerthsByLocation(berthStopLocationKey)

            val berth = berths.firstOrNull { berth ->
                val berthPolygon = berth.area.map { it.toPlatformLocation() }.toTypedArray()
                LocationUtils.pointInPolygon(berthPolygon, stopPlatformLocation)
            }

            if (berth != null) {
                return berth
            }

            val anchorageStopLocationKey = stopLocation.toScaled(ANCHORAGE_SCALE)
            val anchorages = findAnchoragesByLocation(anchorageStopLocationKey)
            val anchorage = anchorages.firstOrNull { anchorage ->
                val anchoragePolygon = anchorage.area.map { it.toPlatformLocation() }.toTypedArray()
                LocationUtils.pointInPolygon(anchoragePolygon, stopPlatformLocation)
            }

            if (anchorage != null) {
                return anchorage
            }

            // We could not find any berth or anchor area at this location, mark it as unclassified, so the AIS status is used instead.
            return null
        }
    }
}
