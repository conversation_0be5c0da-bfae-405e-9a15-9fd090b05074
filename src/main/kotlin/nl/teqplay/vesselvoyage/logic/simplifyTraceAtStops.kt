package nl.teqplay.vesselvoyage.logic

import nl.teqplay.vesselvoyage.model.LocationTime
import nl.teqplay.vesselvoyage.model.SIMPLIFY_TRACE_LOCATIONS_HALFWAY_COUNT
import nl.teqplay.vesselvoyage.model.SIMPLIFY_TRACE_STOP_TIME_MARGIN
import nl.teqplay.vesselvoyage.model.Stop
import nl.teqplay.vesselvoyage.model.StopDetectionVersion
import nl.teqplay.vesselvoyage.model.StopType
import java.time.Duration
import java.time.ZonedDateTime

fun simplifyTraceAtStops(trace: List<LocationTime>, stops: List<Stop>, from: ZonedDateTime, to: ZonedDateTime?): List<LocationTime> {
    val simplifiedLocations = mutableListOf<LocationTime>()

    var lastEndIndex = 0

    for (stop in stops) {
        val (startTime, endTime) = getPreparedStopTimes(stop)

        // Ignore any stops when they will create a weird trace
        if (startTime.isBefore(from) && endTime != null && to != null && endTime.isBefore(to)) {
            continue
        }

        // find all locations until the start of the stop
        var startIndex = lastEndIndex
        while (
            startIndex < trace.size &&
            trace[startIndex].time.isBefore(startTime)
        ) {
            startIndex += 1
        }

        // copy the locations before the stop, leaving them as is
        val locationsBeforeStop = trace.subList(lastEndIndex, startIndex)
        simplifiedLocations += locationsBeforeStop

        // find all locations until the end of the stop
        var endIndex = startIndex
        while (
            endIndex < trace.size &&
            (endTime == null || trace[endIndex].time.isBefore(endTime))
        ) {
            endIndex += 1
        }

        // simplify the locations during the stop
        val locationsDuringStop = trace.subList(startIndex, endIndex)
        val locationAtStop = calculateStopLocation(stop, locationsDuringStop)
        if (locationAtStop != null) {
            simplifiedLocations += locationAtStop
        }

        lastEndIndex = endIndex
    }

    // append all locations after the last stop
    val locationsAfterLastStop = trace.subList(lastEndIndex, trace.size)
    simplifiedLocations += locationsAfterLastStop

    return simplifiedLocations
}

fun getPreparedStopTimes(stop: Stop): Pair<ZonedDateTime, ZonedDateTime?> {
    // for berths, we apply a time margin to make sure we don't remove locations close to the beginning or
    // end of the stop: in that period the ship is typically still very slowly moving to/from the berth.
    // Only do this for the old detection mechanism as the new one is accurate enough
    return if (stop.type == StopType.BERTH && stop.detectionVersion == StopDetectionVersion.MOVEMENT_EVENT) {
        val startTime = stop.startTime.plus(SIMPLIFY_TRACE_STOP_TIME_MARGIN)
        val endTime = stop.endTime?.minus(SIMPLIFY_TRACE_STOP_TIME_MARGIN)

        startTime to endTime
    } else {
        stop.startTime to stop.endTime
    }
}

fun calculateStopLocation(stop: Stop, locationsDuringStop: List<LocationTime>): LocationTime? {
    val endTime = stop.endTime

    // finished stop
    if (endTime != null) {
        val actualLocation = stop.actualLocation
        val actualTime = stop.actualTime

        // Use the actual location and time when we know any, no need to do any hacky way of determining a location
        if (actualLocation != null && actualTime != null) {
            return LocationTime(
                latitude = actualLocation.latitude,
                longitude = actualLocation.longitude,
                time = actualTime
            )
        }

        val stopDuration = Duration.between(stop.startTime, endTime)
        val timeHalfway = stop.startTime.plus(stopDuration.dividedBy(2))

        val locationsHalfway = locationsDuringStop
            .sortedBy { Duration.between(it.time, timeHalfway).abs() }
            .take(SIMPLIFY_TRACE_LOCATIONS_HALFWAY_COUNT)

        return if (locationsHalfway.isNotEmpty()) {
            LocationTime(
                latitude = locationsHalfway.map { it.latitude }.average(),
                longitude = locationsHalfway.map { it.longitude }.average(),
                time = timeHalfway
            )
        } else {
            null
        }
    } else {
        // ongoing stop
        return locationsDuringStop.maxByOrNull { it.time }
    }
}
