package nl.teqplay.vesselvoyage.controller.processing

import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2
import nl.teqplay.skeleton.common.exception.BadRequestException
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.auth.Operation
import nl.teqplay.vesselvoyage.auth.Resource
import nl.teqplay.vesselvoyage.model.internal.LimitedShip
import nl.teqplay.vesselvoyage.service.LimitedShipService
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@ProfileProcessing
@RestController
@RequestMapping(
    path = ["/v2/ships/limited"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ProcessingLimitedShipController(
    private val limitedShipService: LimitedShipService
) {
    @GetMapping("/visits")
    @PreAuthorize("hasPermission(null,'${Resource.VALIDATE}:${Operation.READ}')")
    fun getLimitedShipsByVisit(
        @RequestParam page: Int,
        @RequestParam categories: Set<ShipCategoryV2> = emptySet()
    ): List<LimitedShip> {
        if (page < 1) {
            throw BadRequestException("Page number must be greater than or equal to 1")
        }

        return limitedShipService.getLimitedShipsByVisits(page, categories)
    }

    @GetMapping("/visits/count")
    @PreAuthorize("hasPermission(null,'${Resource.VALIDATE}:${Operation.READ}')")
    fun getLimitedShipsByVisitCount(
        @RequestParam categories: Set<ShipCategoryV2> = emptySet()
    ): Long {
        return limitedShipService.countLimitedShipsByVisits(categories)
    }

    @GetMapping("/voyages")
    @PreAuthorize("hasPermission(null,'${Resource.VALIDATE}:${Operation.READ}')")
    fun getLimitedShipsByVoyage(
        @RequestParam page: Int,
        @RequestParam categories: Set<ShipCategoryV2> = emptySet()
    ): List<LimitedShip> {
        if (page < 1) {
            throw BadRequestException("Page number must be greater than or equal to 1")
        }

        return limitedShipService.getLimitedShipsByVoyages(page, categories)
    }

    @GetMapping("/voyages/count")
    @PreAuthorize("hasPermission(null,'${Resource.VALIDATE}:${Operation.READ}')")
    fun getLimitedShipsByVoyageCount(
        @RequestParam categories: Set<ShipCategoryV2> = emptySet()
    ): Long {
        return limitedShipService.countLimitedShipsByVoyages(categories)
    }
}
