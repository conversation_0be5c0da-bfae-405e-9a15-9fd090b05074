package nl.teqplay.vesselvoyage.controller.processing

import nl.teqplay.skeleton.common.exception.NotFoundException
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.auth.Operation
import nl.teqplay.vesselvoyage.auth.Resource
import nl.teqplay.vesselvoyage.model.lightweight.poma.Berth
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.StaticShipInfoService
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@ProfileProcessing
@RestController
@RequestMapping(
    path = ["/v1/infra"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ProcessingInfraController(
    private val infraService: InfraService,
    private val staticShipInfoService: StaticShipInfoService
) {
    @GetMapping("/ports")
    @PreAuthorize("hasPermission(null,'${Resource.PORT}:${Operation.READ}')")
    fun allPorts() = infraService.getAllPorts()

    @GetMapping("/anchorages")
    @PreAuthorize("hasPermission(null,'${Resource.ANCHORAGE}:${Operation.READ}')")
    fun allAnchorages() = infraService.getAllAnchorages()

    @GetMapping("/berths")
    @PreAuthorize("hasPermission(null,'${Resource.BERTH}:${Operation.READ}')")
    fun getBerth(
        @RequestParam("berthId") berthId: String
    ): Berth {
        return infraService.getBerth(berthId)
            ?: throw NotFoundException("Berth with id $berthId not found")
    }

    // TODO: technically, fetching ship information here is not "infra",
    //  move this method to a different controller and rename it?
    @GetMapping("/refresh")
    @PreAuthorize("hasPermission(null,'${Resource.PORT}:${Operation.UPDATE}')")
    fun refresh() {
        infraService.refresh()
        staticShipInfoService.refresh()
    }
}
