package nl.teqplay.vesselvoyage.controller.processing

import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.auth.Operation
import nl.teqplay.vesselvoyage.auth.Resource
import nl.teqplay.vesselvoyage.model.internal.ShipStory
import nl.teqplay.vesselvoyage.service.StoryService
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.Instant

@ProfileProcessing
@RestController
@RequestMapping(
    path = ["/v2/story"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ProcessingStoryController(
    private val storyService: StoryService
) {
    @GetMapping("/{imo}")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun story(
        @PathVariable imo: Int,
        @RequestParam start: Instant,
        @RequestParam end: Instant?
    ): ShipStory {
        return storyService.getStory(imo, start, end)
    }

    @PostMapping("/{imo}/dryRun")
    @PreAuthorize("hasPermission(null,'${Resource.VALIDATE}:${Operation.READ}')")
    fun storyDryRun(
        @PathVariable imo: Int,
        @RequestParam start: Instant,
        @RequestParam end: Instant
    ): ShipStory {
        return storyService.storyDryRun(imo, start, end)
    }
}
