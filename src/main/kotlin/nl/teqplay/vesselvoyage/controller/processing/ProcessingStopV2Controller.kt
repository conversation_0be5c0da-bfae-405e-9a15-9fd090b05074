package nl.teqplay.vesselvoyage.controller.processing

import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.auth.Operation
import nl.teqplay.vesselvoyage.auth.Resource
import nl.teqplay.vesselvoyage.service.ProcessingStopService
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import kotlin.concurrent.thread

@ProfileProcessing
@RestController
@RequestMapping(
    path = ["/v2/entry/stop"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ProcessingStopV2Controller(
    private val stopService: ProcessingStopService
) {
    @PostMapping("/fix-broken")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.UPDATE}')")
    fun fixBrokenStops() {
        thread(name = "StopFixerThread") {
            stopService.fixBrokenStops()
        }
    }
}
