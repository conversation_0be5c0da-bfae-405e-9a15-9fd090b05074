package nl.teqplay.vesselvoyage.controller.processing

import io.github.oshai.kotlinlogging.KotlinLogging
import io.swagger.v3.oas.annotations.Parameter
import jakarta.validation.constraints.NotEmpty
import nl.teqplay.skeleton.common.exception.NotFoundException
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.auth.Operation
import nl.teqplay.vesselvoyage.auth.Resource
import nl.teqplay.vesselvoyage.controller.annotation.IsoDate
import nl.teqplay.vesselvoyage.model.RecalculationPortResult
import nl.teqplay.vesselvoyage.model.RecalculationResult
import nl.teqplay.vesselvoyage.model.RecalculationShipResult
import nl.teqplay.vesselvoyage.model.ReventsRecalculationStatus.Guarantee
import nl.teqplay.vesselvoyage.model.internal.AutomaticRecalculationStatus
import nl.teqplay.vesselvoyage.service.recalculation.AutomaticRecalculationService
import nl.teqplay.vesselvoyage.service.recalculation.ManualRecalculationService
import nl.teqplay.vesselvoyage.service.recalculation.ReventsRecalculationService
import nl.teqplay.vesselvoyage.util.asUser
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.Authentication
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.ZonedDateTime
import kotlin.concurrent.thread

@Validated
@ProfileProcessing
@RestController
@RequestMapping(
    path = ["/v2/recalculate"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ProcessingRecalculateV2Controller(
    private val reventsRecalculationService: ReventsRecalculationService,
    private val manualRecalculationService: ManualRecalculationService,
    private val automaticRecalculationService: AutomaticRecalculationService?
) {
    private val log = KotlinLogging.logger {}

    @GetMapping
    @PreAuthorize("hasPermission(null,'${Resource.RECALCULATION}:${Operation.READ}')")
    fun findAllRecalculations(): List<RecalculationResult> {
        return reventsRecalculationService.findAll()
    }

    @GetMapping("/ship/{imo}")
    @PreAuthorize("hasPermission(null,'${Resource.RECALCULATION}:${Operation.READ}')")
    fun findShipRecalculationsByImo(
        @PathVariable imo: Int
    ): List<RecalculationResult> {
        return reventsRecalculationService.findAllByImo(imo)
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasPermission(null,'${Resource.RECALCULATION}:${Operation.READ}')")
    fun findRecalculationById(
        @PathVariable id: String
    ): RecalculationResult {
        return reventsRecalculationService.findById(id)
            ?: throw NotFoundException("Recalculation object not found by id: $id")
    }

    @PostMapping("/ship/{imo}")
    @PreAuthorize("hasPermission(null,'${Resource.RECALCULATION}:${Operation.CREATE}')")
    fun recalculateShip(
        @PathVariable imo: Int,
        @RequestParam(required = true) @IsoDate start: ZonedDateTime,
        @RequestParam(required = true) @IsoDate end: ZonedDateTime,
        @RequestParam(required = false) guarantee: Guarantee?,
        @RequestParam(required = false, defaultValue = "false") includeHistoricData: Boolean,
        @Parameter(hidden = true) authentication: Authentication
    ): RecalculationShipResult {
        log.debug { "Recalculate story of ship with IMO $imo" }
        return reventsRecalculationService.recalculateByShip(
            imo = imo,
            start = start.toInstant(),
            end = end.toInstant(),
            guarantee = guarantee ?: Guarantee.V2,
            username = authentication.asUser().username,
            includeHistoricData = includeHistoricData
        )
    }

    @PostMapping("/ship/batch")
    @PreAuthorize("hasPermission(null,'${Resource.RECALCULATION}:${Operation.CREATE}')")
    fun recalculateShips(
        @RequestBody @NotEmpty imos: Set<Int>,
        @RequestParam(required = true) @IsoDate start: ZonedDateTime,
        @RequestParam(required = true) @IsoDate end: ZonedDateTime,
        @RequestParam(required = false) guarantee: Guarantee?,
        @RequestParam(required = false, defaultValue = "false") includeHistoricData: Boolean,
        @Parameter(hidden = true) authentication: Authentication
    ): RecalculationResult {
        // Handle as a single recalc when we only provide 1 imo
        if (imos.size == 1) {
            return reventsRecalculationService.recalculateByShip(
                imo = imos.first(),
                start = start.toInstant(),
                end = end.toInstant(),
                guarantee = guarantee ?: Guarantee.V2,
                username = authentication.asUser().username,
                includeHistoricData = includeHistoricData
            )
        }

        return reventsRecalculationService.recalculateByShips(
            imos = imos,
            start = start.toInstant(),
            end = end.toInstant(),
            guarantee = guarantee ?: Guarantee.V2,
            username = authentication.asUser().username
        )
    }

    @PostMapping("/automatic/batch")
    @PreAuthorize("hasPermission(null,'${Resource.RECALCULATION}:${Operation.CREATE}')")
    fun triggerAutomaticRecalculationBatch() {
        thread {
            automaticRecalculationService?.recalculateBatch()
        }
    }

    @GetMapping("/automatic/status")
    @PreAuthorize("hasPermission(null,'${Resource.RECALCULATION}:${Operation.CREATE}')")
    fun getAutomaticRecalculationStatus(): AutomaticRecalculationStatus? {
        return automaticRecalculationService?.getStatus()
    }

    @PostMapping("/ship/{imo}/fullStory")
    @PreAuthorize("hasPermission(null,'${Resource.RECALCULATION}:${Operation.CREATE}')")
    fun recalculateShipFullStory(
        @PathVariable imo: Int
    ) {
        manualRecalculationService.recreateFullShipStory(imo)
    }

    @PostMapping("/ship/fullStory/batch")
    @PreAuthorize("hasPermission(null,'${Resource.RECALCULATION}:${Operation.CREATE}')")
    fun recalculateShipsFullStory(
        @RequestBody @NotEmpty imos: Set<Int>,
    ) {
        log.info { "Starting full story recalc for ${imos.size} imos" }
        imos.toSortedSet()
            .parallelStream()
            .forEach { imo ->
                try {
                    manualRecalculationService.recreateFullShipStory(imo)
                } catch (e: Exception) {
                    log.warn { "Something went wrong when recalculating ship story imo = $imo" }
                }
            }
        log.info { "Finished full story recalc for ${imos.size} imos" }
    }

    @PostMapping("/port/{unlocode}")
    @PreAuthorize("hasPermission(null,'${Resource.RECALCULATION}:${Operation.CREATE}')")
    fun recalculatePort(
        @PathVariable unlocode: String,
        @RequestParam(required = true) @IsoDate start: ZonedDateTime,
        @RequestParam(required = true) @IsoDate end: ZonedDateTime,
        @RequestParam(required = false) guarantee: Guarantee?,
        @RequestParam(required = false, defaultValue = "false") includeHistoricData: Boolean,
        @Parameter(hidden = true) authentication: Authentication
    ): RecalculationPortResult {
        log.debug { "Recalculate story of ships inside port (unlocode = $unlocode)" }
        return reventsRecalculationService.recalculateByPort(
            unlocode = unlocode,
            start = start.toInstant(),
            end = end.toInstant(),
            guarantee = guarantee ?: Guarantee.V2,
            username = authentication.asUser().username,
            includeHistoricData = includeHistoricData
        )
    }

    @PostMapping("/scenario/{scenarioId}")
    @PreAuthorize("hasPermission(null,'${Resource.RECALCULATION}:${Operation.CREATE}')")
    fun trackScenario(
        @PathVariable scenarioId: String,
    ): RecalculationResult {
        log.debug { "Track recalculation scenario with id $scenarioId" }
        return reventsRecalculationService.trackScenario(scenarioId)
    }

    /**
     * Merge back the recalculation result from revents for a ship.
     *
     * @param scenarioId the scenario where we want to merge back ships from, if null the latest revents scenario
     * containing data for the specific IMO will be used.
     */
    @PostMapping("/ship/{imo}/merge")
    @PreAuthorize("hasPermission(null,'${Resource.RECALCULATION}:${Operation.CREATE}')")
    fun mergeShipRecalculation(
        @PathVariable imo: Int,
        @RequestParam(required = false) scenarioId: String? = null,
        @Parameter(hidden = true) authentication: Authentication,
    ): Boolean {
        log.debug { "Merging ship recalculation for IMO: $imo, scenarioId: ${scenarioId ?: "latest"}" }
        return reventsRecalculationService.mergeShipRecalculation(
            imo = imo,
            scenario = scenarioId,
            username = authentication.asUser().username
        )
    }

    /**
     * Merge back the recalculation result from revents for a list of ships.
     *
     * @param scenarioId the scenario where we want to merge back ships from, if null the latest revents scenario
     * containing data for the specific IMO will be used.
     */
    @PostMapping("/ship/imo/batch/merge")
    @PreAuthorize("hasPermission(null,'${Resource.RECALCULATION}:${Operation.CREATE}')")
    fun mergeShipRecalculationBatch(
        @RequestBody imos: List<Int>,
        @RequestParam(required = false) scenarioId: String? = null,
        @Parameter(hidden = true) authentication: Authentication,
    ): Boolean {
        log.debug { "Merging batch ship recalculation for ${imos.size} ships, scenarioId: ${scenarioId ?: "latest"}" }
        return reventsRecalculationService.mergeShipRecalculation(
            imos = imos,
            scenario = scenarioId,
            username = authentication.asUser().username
        )
    }

    /**
     * Merge back the recalculation result from revents for a given [scenarioId].
     *
     * @param scenarioId the scenario where we want to merge back data from
     */
    @PostMapping("/scenario/{scenarioId}/merge")
    @PreAuthorize("hasPermission(null,'${Resource.RECALCULATION}:${Operation.CREATE}')")
    fun mergeScenario(
        @PathVariable scenarioId: String,
        @Parameter(hidden = true) authentication: Authentication,
    ): Boolean {
        log.debug { "Merging recalculation for scenario: $scenarioId" }
        return reventsRecalculationService.mergeShipRecalculation(
            scenario = scenarioId,
            username = authentication.asUser().username
        )
    }
}
