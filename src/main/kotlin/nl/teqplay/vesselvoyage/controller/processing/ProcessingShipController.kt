package nl.teqplay.vesselvoyage.controller.processing

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2
import nl.teqplay.skeleton.common.exception.NotFoundException
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.auth.Operation
import nl.teqplay.vesselvoyage.auth.Resource
import nl.teqplay.vesselvoyage.model.Entry
import nl.teqplay.vesselvoyage.model.ShipDetails
import nl.teqplay.vesselvoyage.service.EntryProcessingService
import nl.teqplay.vesselvoyage.service.RecalculationService
import nl.teqplay.vesselvoyage.service.StaticShipInfoService
import nl.teqplay.vesselvoyage.util.joinToStringTruncated
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.Duration

@ProfileProcessing
@RestController
@RequestMapping(
    path = ["/v1/ships"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ProcessingShipController(
    private val entryProcessingService: EntryProcessingService,
    private val recalculationService: RecalculationService,
    private val staticShipInfoService: StaticShipInfoService
) {
    private val log = KotlinLogging.logger {}

    @GetMapping("/{imo}/current")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findCurrentStatusByIMO(
        @PathVariable imo: String
    ): Entry {
        log.debug { "Find current status for ship with IMO $imo" }

        return entryProcessingService.getCurrentStatus(imo)
            ?: throw NotFoundException("Ship with IMO $imo not found")
    }

    @GetMapping("/static/{imo}")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun getShipDetailsByIMO(
        @PathVariable imo: String
    ): ShipDetails {
        log.debug { "Find static ship info for IMO $imo" }

        return staticShipInfoService.getShipDetailsByIMO(imo)
            ?: throw NotFoundException("Ship with IMO $imo not found")
    }

    @PostMapping("/static/imos")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun getShipDetailsByIMOs(
        @RequestBody imos: List<String>
    ): List<ShipDetails> {
        log.debug { "Find static ship info for IMO's ${imos.joinToStringTruncated(10)}" }

        return imos.mapNotNull { imo ->
            staticShipInfoService.getShipDetailsByIMO(imo)
        }
    }

    @PostMapping("/static/mmsis")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun getShipDetailsByMMSIs(
        @RequestBody mmsis: List<String>
    ): List<ShipDetails> {
        log.debug { "Find static ship info for MMSI's ${mmsis.joinToStringTruncated(10)}" }

        return mmsis.mapNotNull { mmsi ->
            staticShipInfoService.getShipDetailsByMMSI(mmsi)
        }
    }

    @GetMapping("/static/search")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun searchShipDetails(
        @RequestParam("query") query: String,
        @RequestParam("limit") limit: Int?
    ): List<ShipDetails> {
        log.debug { "Find static ships info, query=$query, limit=$limit" }

        return staticShipInfoService.searchShipDetails(query, limit)
    }

    @PostMapping("/current")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findCurrentStatusByIMOs(
        @RequestBody imos: List<String>
    ): List<Entry> {
        log.debug { "Find current status for ${imos.size} ships with IMOs ${imos.joinToStringTruncated(limit = 10)}" }

        return imos.mapNotNull { entryProcessingService.getCurrentStatus(it) }
    }

    @GetMapping("/{imo}/recalculate")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.UPDATE}')")
    fun recalculate(
        @PathVariable imo: String,
        @RequestParam(required = false, defaultValue = "false") forceRegenerateTraces: Boolean
    ): Int {
        log.debug { "Recalculate story of ship with IMO $imo" }

        return recalculationService.recalculate(imo, forceRegenerateTraces)
    }

    @GetMapping("/{imo}/recalculate/partially")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.UPDATE}')")
    fun recalculatePartially(
        @PathVariable imo: String,
        @RequestParam(required = true) duration: Duration,
    ): Int {
        log.debug { "Recalculate story of ship with IMO $imo" }

        return recalculationService.recalculateEvents(setOf(imo), duration)[imo] ?: 0
    }

    // TODO move to dedicated recalculation controller
    @GetMapping("/resetRecalculationService")
    @PreAuthorize("hasPermission(null,'${Resource.RECALCULATION}:${Operation.DELETE}')")
    fun resetRecalculationService() {
        log.debug { "Reset calculation information of all ships" }

        recalculationService.reset()
    }

    @GetMapping("/categories")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun getShipCategories(): List<ShipCategoryV2> {
        log.debug { "Get all ship categories" }

        return ShipCategoryV2.values().toList()
    }
}
