package nl.teqplay.vesselvoyage.controller.processing

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.auth.Operation
import nl.teqplay.vesselvoyage.auth.Resource
import nl.teqplay.vesselvoyage.model.ExtendedVisit
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.model.VisitQuery
import nl.teqplay.vesselvoyage.properties.ControllerLimitProperties
import nl.teqplay.vesselvoyage.service.StaticShipInfoService
import nl.teqplay.vesselvoyage.service.VisitService
import nl.teqplay.vesselvoyage.util.VisitCsvHeader
import nl.teqplay.vesselvoyage.util.generateCSV
import nl.teqplay.vesselvoyage.util.throwWhenThresholdReached
import nl.teqplay.vesselvoyage.util.toCsvRow
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.time.ZonedDateTime

@ProfileProcessing
@RestController
@RequestMapping(
    path = ["/v1/visits"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ProcessingVisitController(
    properties: ControllerLimitProperties,
    private val visitService: VisitService,
    private val staticShipInfoService: StaticShipInfoService
) {
    private val log = KotlinLogging.logger {}

    private val maxEntries = properties.maxEntries

    @PostMapping("/query/extended")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun queryExtendedVisits(
        @RequestBody query: VisitQuery,
    ): List<ExtendedVisit> {
        log.debug { "Query visits with ship info, query=$query" }

        return findExtendedVisits(query)
    }

    @PostMapping("/query/download/csv")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun queryDownloadCsvVisits(
        @RequestBody query: VisitQuery,
    ): ResponseEntity<String> {
        log.debug { "Download visits as CSV, query=$query" }

        val extendedVisits = findExtendedVisits(query)

        val filename = "visits_${ZonedDateTime.now()}"
        val outputStream = generateCSV(
            header = VisitCsvHeader.map { it.name },
            rows = extendedVisits.map { it.toCsvRow() }
        )

        return ResponseEntity.ok()
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=$filename")
            .contentType(MediaType.parseMediaType("application/csv"))
            .body(outputStream.toString())
    }

    private fun findExtendedVisits(query: VisitQuery): List<ExtendedVisit> {
        return findLimited(query)
            .map { visit ->
                ExtendedVisit(
                    visit = visit,
                    ship = staticShipInfoService.getShipDetailsByIMO(visit.imo)
                )
            }
            .toList()
    }

    private fun findLimited(query: VisitQuery): List<Visit> {
        // limit the limit to protect against a too large query that would result in the server running out of memory
        val limitedQuery = query.copy(
            limit = query.limit.coerceAtMost(maxEntries)
        )

        val visits = visitService.find(limitedQuery).toList()

        throwWhenThresholdReached(visits.size, maxEntries)

        return visits
    }
}
