package nl.teqplay.vesselvoyage.controller.processing

import io.swagger.v3.oas.annotations.Parameter
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.auth.Operation.READ
import nl.teqplay.vesselvoyage.auth.Resource.USER_PROFILE
import nl.teqplay.vesselvoyage.model.UserProfile
import nl.teqplay.vesselvoyage.util.asUser
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.Authentication
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * Endpoint for fetching the current user.
 */
@ProfileProcessing
@RestController
@RequestMapping("/v1/users", produces = [MediaType.APPLICATION_JSON_VALUE])
class ProcessingUserController {
    /**
     * Retrieve the currently-logged in user.
     */
    @PreAuthorize("hasPermission(null, '$USER_PROFILE:$READ')")
    @GetMapping("/current")
    fun getCurrentUser(@Parameter(hidden = true) authentication: Authentication): UserProfile {
        val user = authentication.asUser()

        // currently UserProfile is the same as User,
        // in the future we will probably extend the user profile
        // with more fields.
        return UserProfile(user.username, user.roles)
    }
}
