package nl.teqplay.vesselvoyage.controller.processing

import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.auth.Operation
import nl.teqplay.vesselvoyage.auth.Resource
import nl.teqplay.vesselvoyage.model.EventRecalculation
import nl.teqplay.vesselvoyage.service.RecalculationService
import nl.teqplay.vesselvoyage.service.SnapshotRecalculationService
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@ProfileProcessing
@RestController
@RequestMapping(
    path = ["/v1/recalculate"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ProcessingRecalculateController(
    private val service: RecalculationService,
    private val snapshotService: SnapshotRecalculationService
) {
    @PostMapping("/events")
    @PreAuthorize("hasPermission(null,'${Resource.RECALCULATION}:${Operation.CREATE}')")
    fun recalculateEvents(@RequestBody body: EventRecalculation): ResponseEntity<Map<String, Int>> {
        if (body.duration.toDays() < 1 && body.imos.isEmpty()) {
            return ResponseEntity.badRequest()
                .body(null)
        }

        val result = service.recalculateEvents(body.imos, body.duration)

        return ResponseEntity.ok(result)
    }

    @PostMapping("/imos", produces = [MediaType.TEXT_PLAIN_VALUE])
    @PreAuthorize("hasPermission(null,'${Resource.RECALCULATION}:${Operation.CREATE}')")
    fun recalculateImos(@RequestBody body: Set<String>): ResponseEntity<String> {
        if (body.isEmpty()) {
            return ResponseEntity.badRequest()
                .body("Provided list of imo numbers is empty, can't set recalculable")
        }

        val (isSuccess, message) = service.setRecalculable(body)

        return if (isSuccess) {
            ResponseEntity.ok(message)
        } else {
            ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                .body(message)
        }
    }

    @PostMapping("/createSnapshots")
    @PreAuthorize("hasPermission(null,'${Resource.RECALCULATION}:${Operation.CREATE}')")
    fun createSnapshots() {
        snapshotService.createAndSaveSnapshots()
    }
}
