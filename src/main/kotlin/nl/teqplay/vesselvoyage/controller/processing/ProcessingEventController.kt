package nl.teqplay.vesselvoyage.controller.processing

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.auth.Operation
import nl.teqplay.vesselvoyage.auth.Resource
import nl.teqplay.vesselvoyage.controller.annotation.IsoDate
import nl.teqplay.vesselvoyage.model.Entry
import nl.teqplay.vesselvoyage.model.internal.ProcessedEvent
import nl.teqplay.vesselvoyage.service.EntryProcessingService
import nl.teqplay.vesselvoyage.service.EventFetchingService
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.ZonedDateTime

private val log = KotlinLogging.logger {}

@ProfileProcessing
@RestController
@RequestMapping(
    path = ["/v1/events"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ProcessingEventController(
    private val eventFetchingService: EventFetchingService,
    private val entryProcessingService: EntryProcessingService
) {
    @GetMapping("/{imo}")
    @PreAuthorize("hasPermission(null,'${Resource.EVENT}:${Operation.READ}')")
    fun find(
        @PathVariable imo: String,
        @RequestParam(required = true) @IsoDate start: ZonedDateTime,
        @RequestParam(required = true) @IsoDate end: ZonedDateTime
    ): List<Event> {
        log.debug { "Find events for ship with IMO $imo, start=$start, end=$end" }

        return eventFetchingService.fetchEventsByIMO(imo, start, end)
            .toList()
    }

    @GetMapping("/{imo}/processed")
    @PreAuthorize("hasPermission(null,'${Resource.EVENT}:${Operation.READ}')")
    fun findAndProcess(
        @PathVariable imo: String,
        @RequestParam(required = true) @IsoDate start: ZonedDateTime,
        @RequestParam(required = true) @IsoDate end: ZonedDateTime
    ): List<ProcessedEvent> {
        log.debug { "Find and process events for ship with IMO $imo, start=$start, end=$end" }

        val teqplayEvents = eventFetchingService.fetchAisEngineEventsByIMO(imo.toInt(), start.toInstant(), end.toInstant())
            .toList()

        return entryProcessingService.processTeqplayEventsDryRun(teqplayEvents)
            .toList()
    }

    @PostMapping("/processed/aisengine")
    @PreAuthorize("hasPermission(null,'${Resource.EVENT}:${Operation.READ}')")
    fun process(
        @RequestBody aisEngineEvents: List<Event>
    ): List<Entry> {
        return entryProcessingService.processAisEngineEventsDryRunForStory(aisEngineEvents.asSequence())
            .toList()
    }
}
