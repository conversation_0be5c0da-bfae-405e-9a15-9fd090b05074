package nl.teqplay.vesselvoyage.controller.processing

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.auth.Operation
import nl.teqplay.vesselvoyage.auth.Resource
import nl.teqplay.vesselvoyage.model.lightweight.poma.Port
import nl.teqplay.vesselvoyage.service.InfraService
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@ProfileProcessing
@RestController
@RequestMapping(
    path = ["/v1/ports"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ProcessingPortController(
    private val infraService: InfraService
) {
    private val log = KotlinLogging.logger {}

    @PostMapping("/static/unlocodes")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun getPortsByUnlocodes(
        @RequestBody unlocodes: List<String>,
    ): List<Port> {
        log.debug { "Find ports with unlocodes $unlocodes" }

        return unlocodes.mapNotNull { unlocode ->
            infraService.getPortByUnlocode(unlocode)
        }
    }

    @GetMapping("/static/search")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun searchPorts(
        @RequestParam("query") query: String,
        @RequestParam("limit") limit: Int?
    ): List<Port> {
        log.debug { "Find ports, query=$query, limit=$limit" }

        return infraService.searchPorts(query, limit)
    }
}
