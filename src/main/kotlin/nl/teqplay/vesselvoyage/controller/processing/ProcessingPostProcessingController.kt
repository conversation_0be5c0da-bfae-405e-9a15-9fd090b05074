package nl.teqplay.vesselvoyage.controller.processing

import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.auth.Operation
import nl.teqplay.vesselvoyage.auth.Resource
import nl.teqplay.vesselvoyage.model.v2.EntryId
import nl.teqplay.vesselvoyage.service.PostProcessingService
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@ProfileProcessing
@ConditionalOnProperty("post-processing.enabled", havingValue = "true")
@RestController
@RequestMapping(
    path = ["/v2/post-processing"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ProcessingPostProcessingController(
    val postProcessingService: PostProcessingService
) {

    @PostMapping("/schedule/missed")
    @PreAuthorize("hasPermission(null,'${Resource.RECALCULATION}:${Operation.CREATE}')")
    fun scheduleForMissed(): List<EntryId> {
        return postProcessingService.schedulePostProcessingForMissed()
    }
}
