package nl.teqplay.vesselvoyage.controller.processing

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.auth.Operation
import nl.teqplay.vesselvoyage.auth.Resource
import nl.teqplay.vesselvoyage.controller.annotation.IsoDate
import nl.teqplay.vesselvoyage.model.internal.ProcessingDryRunResult
import nl.teqplay.vesselvoyage.model.v2.EntryESoFWrapper
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.service.EntryProcessingService
import nl.teqplay.vesselvoyage.service.EventFetchingService
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.ZonedDateTime

@ProfileProcessing
@RestController
@RequestMapping(
    path = ["/v2/events"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ProcessingV2EventController(
    private val eventFetchingService: EventFetchingService,
    private val entryProcessingService: EntryProcessingService
) {
    private val log = KotlinLogging.logger {}

    @GetMapping("/{imo}/processed")
    @PreAuthorize("hasPermission(null,'${Resource.EVENT}:${Operation.READ}')")
    fun findAndProcess(
        @PathVariable imo: Int,
        @RequestParam(required = true) @IsoDate start: ZonedDateTime,
        @RequestParam(required = true) @IsoDate end: ZonedDateTime
    ): List<ProcessingDryRunResult> {
        log.debug { "Find and process events for ship with IMO $imo, start=$start, end=$end" }

        val aisEngineEvents = eventFetchingService.fetchAisEngineEventsByIMO(
            imo = imo,
            start = start.toInstant(),
            end = end.toInstant()
        )

        return entryProcessingService.processDryRun(aisEngineEvents)
            .toList()
    }

    @PostMapping("/processed/aisengine")
    @PreAuthorize("hasPermission(null,'${Resource.EVENT}:${Operation.READ}')")
    fun process(
        @RequestBody aisEngineEvents: List<Event>
    ): List<EntryESoFWrapper<out NewEntry>> {
        return entryProcessingService.processAisEngineEventsDryRunForStoryV2(aisEngineEvents.asSequence())
            .toList()
    }
}
