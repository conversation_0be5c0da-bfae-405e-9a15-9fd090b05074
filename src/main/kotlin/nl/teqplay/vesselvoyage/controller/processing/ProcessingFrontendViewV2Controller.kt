package nl.teqplay.vesselvoyage.controller.processing

import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2
import nl.teqplay.skeleton.common.exception.BadRequestException
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.apiv2.model.Visit
import nl.teqplay.vesselvoyage.apiv2.model.Voyage
import nl.teqplay.vesselvoyage.model.ShipDetails
import nl.teqplay.vesselvoyage.service.ProcessingFrontendViewV2Service
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.CrossOrigin
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * This controller is intended for the VesselVoyage front-end, and therefore only enabled with profile 'processing'.
 * It's not meant for other applications or consumers!
 */
@CrossOrigin
@ProfileProcessing
@RestController
@RequestMapping(
    path = ["/v1/frontend"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ProcessingFrontendViewV2Controller(
    private val service: ProcessingFrontendViewV2Service
) {
    /**
     * Endpoint collecting all information for the front-end 'Ports' page. Supply an unlocode and optional ship
     * categories.
     * Note that this is intended for the VesselVoyage front-end, not for API users!
     */
    @PostMapping("/ports")
    fun portsPage(@RequestBody request: PortsPageRequest): PortsResponse {
        if (request.unlocode.isBlank()) {
            throw BadRequestException("Missing unlocode")
        }
        return service.createPortsPageResponse(request)
    }

    data class PortsPageRequest(
        val unlocode: String = "",
        val categories: Set<ShipCategoryV2> = emptySet()
    )

    data class PortsResponse(
        val sailingTowards: List<Voyage>,
        val currentlyVisiting: List<Visit>,
        val justLeft: List<Voyage>,
        val shipByImo: Map<Int, ShipDetails>
    )
}
