package nl.teqplay.vesselvoyage.controller.processing

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.auth.Operation
import nl.teqplay.vesselvoyage.auth.Resource
import nl.teqplay.vesselvoyage.model.ExtendedVoyage
import nl.teqplay.vesselvoyage.model.Voyage
import nl.teqplay.vesselvoyage.model.VoyageQuery
import nl.teqplay.vesselvoyage.properties.ControllerLimitProperties
import nl.teqplay.vesselvoyage.service.StaticShipInfoService
import nl.teqplay.vesselvoyage.service.VoyageService
import nl.teqplay.vesselvoyage.util.VoyageCsvHeader
import nl.teqplay.vesselvoyage.util.generateCSV
import nl.teqplay.vesselvoyage.util.throwWhenThresholdReached
import nl.teqplay.vesselvoyage.util.toCsvRow
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.time.ZonedDateTime

@ProfileProcessing
@RestController
@RequestMapping(
    path = ["/v1/voyages"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ProcessingVoyageController(
    properties: ControllerLimitProperties,
    private val voyageService: VoyageService,
    private val staticShipInfoService: StaticShipInfoService
) {
    private val log = KotlinLogging.logger {}

    private val maxEntries = properties.maxEntries

    @PostMapping("/query/download/csv")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun queryDownloadCsvVoyages(
        @RequestBody query: VoyageQuery,
    ): ResponseEntity<String> {
        log.debug { "Download voyages as CSV, query=$query" }

        val extendedVoyages = findExtendedVoyages(query)

        val filename = "voyages_${ZonedDateTime.now()}"
        val outputStream = generateCSV(
            header = VoyageCsvHeader.map { it.name },
            rows = extendedVoyages.map { it.toCsvRow() }
        )

        return ResponseEntity.ok()
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=$filename")
            .contentType(MediaType.parseMediaType("application/csv"))
            .body(outputStream.toString())
    }

    private fun findExtendedVoyages(query: VoyageQuery): List<ExtendedVoyage> {
        return findLimited(query)
            .map { voyage ->
                ExtendedVoyage(
                    voyage = voyage,
                    ship = staticShipInfoService.getShipDetailsByIMO(voyage.imo)
                )
            }
            .toList()
    }

    private fun findLimited(query: VoyageQuery): List<Voyage> {
        // limit the limit to protect against a too large query that would result in the server running out of memory
        val limitedQuery = query.copy(
            limit = query.limit.coerceAtMost(maxEntries)
        )

        val visits = voyageService.find(limitedQuery).toList()

        throwWhenThresholdReached(visits.size, maxEntries)

        return visits
    }
}
