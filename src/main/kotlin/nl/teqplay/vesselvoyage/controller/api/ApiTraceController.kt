package nl.teqplay.vesselvoyage.controller.api

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.auth.Operation
import nl.teqplay.vesselvoyage.auth.Resource
import nl.teqplay.vesselvoyage.controller.annotation.IsoDate
import nl.teqplay.vesselvoyage.model.HistoricTrace
import nl.teqplay.vesselvoyage.model.LocationTime
import nl.teqplay.vesselvoyage.properties.ControllerLimitProperties
import nl.teqplay.vesselvoyage.service.EntryService
import nl.teqplay.vesselvoyage.service.V1TraceService
import nl.teqplay.vesselvoyage.util.joinToStringTruncated
import nl.teqplay.vesselvoyage.util.throwWhenThresholdReached
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.ZonedDateTime

@ProfileApi
@RestController
@RequestMapping(
    path = ["/v1/traces"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ApiTraceController(
    properties: ControllerLimitProperties,
    private val entryService: EntryService,
    private val v1TraceService: V1TraceService
) {
    private val log = KotlinLogging.logger {}

    private val maxTraces = properties.maxTraces

    @GetMapping("/entry/{entryId}")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findTraceById(
        @PathVariable entryId: String,
        @RequestParam(required = false) tolerance: Double?
    ): HistoricTrace? {
        log.debug { "Find trace for entry $entryId, tolerance $tolerance" }

        return entryService.findEntry(entryId)
            ?.let { v1TraceService.getByEntry(it, tolerance) }
    }

    @PostMapping("/entry")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findTracesByIds(
        @RequestBody entryIds: List<String>,
        @RequestParam(required = false) tolerance: Double?
    ): List<HistoricTrace> {
        log.debug {
            "Find trace for ${entryIds.size} entries ${entryIds.joinToStringTruncated(limit = 10)}, " +
                "tolerance $tolerance"
        }

        throwWhenThresholdReached(entryIds.size, maxTraces)

        val entries = entryService.findEntries(entryIds)
        return v1TraceService.getByEntries(entries, tolerance)
    }

    @GetMapping("/ship/{imo}")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findTraceByImo(
        @PathVariable imo: String,
        @RequestParam(required = true) @IsoDate start: ZonedDateTime,
        @RequestParam(required = true) @IsoDate end: ZonedDateTime,
        @RequestParam(required = false) tolerance: Double?
    ): List<LocationTime> {
        log.debug { "Find trace for ship with IMO $imo, start=$start, end=$end, tolerance $tolerance" }

        val entries = entryService.findEntriesByIMO(imo, start, end, maxTraces).toList()

        throwWhenThresholdReached(entries.size, maxTraces)

        val traces = v1TraceService.getByEntries(entries, tolerance)
        val locations = traces
            .flatMap { it.locations }
            .filter { it.time >= start && it.time < end }

        log.debug {
            "Created a merged trace from ${entries.size} traces " +
                "with ${locations.size} locations for IMO $imo"
        }

        return locations
    }
}
