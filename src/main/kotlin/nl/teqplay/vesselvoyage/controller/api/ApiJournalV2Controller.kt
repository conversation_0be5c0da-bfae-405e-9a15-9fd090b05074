package nl.teqplay.vesselvoyage.controller.api

import nl.teqplay.skeleton.common.exception.BadRequestException
import nl.teqplay.skeleton.common.exception.NotFoundException
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.JournalItemByImoRequest
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.JournalItemByImoResponse
import nl.teqplay.vesselvoyage.auth.Operation
import nl.teqplay.vesselvoyage.auth.Resource
import nl.teqplay.vesselvoyage.mapper.EntryV2Mapper
import nl.teqplay.vesselvoyage.model.v2.JournalItem
import nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter
import nl.teqplay.vesselvoyage.service.api.JournalV2Service
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.Instant

@ProfileApi
@RestController
@RequestMapping(
    path = ["/v2/journal"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ApiJournalV2Controller(
    private val service: JournalV2Service,
    private val mapper: EntryV2Mapper
) {

    @GetMapping("/{visitId}")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findById(@PathVariable visitId: String) = service
        .findByVisitId(visitId)
        ?.mapToApi()
        ?: throw NotFoundException("A visit with the given id does not exist")

    @PostMapping("/ids")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findByIds(@RequestBody visitIds: List<String>) = service
        .findByVisitIds(visitIds.toSet())
        .mapToApi()

    @GetMapping("/byImo/{imo}")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findByImo(
        @PathVariable imo: Int,
        @RequestParam("start", required = false) start: Instant?,
        @RequestParam("end", required = false) end: Instant?,
        @RequestParam("last", required = false) last: Int?,
        @RequestParam("finished") finished: Boolean?,
        @RequestParam("confirmed") confirmed: Boolean?
    ): JournalItemByImoResponse {
        return findByImo(JournalItemByImoRequest(imo, start, end, last, finished, confirmed))
    }

    @PostMapping("/byImo")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findByImoBatch(
        @RequestBody requests: List<JournalItemByImoRequest>
    ): List<JournalItemByImoResponse> {
        return requests.map { request -> findByImo(request) }
    }

    private fun findByImo(request: JournalItemByImoRequest): JournalItemByImoResponse {
        val (imo: Int, start: Instant?, end: Instant?, last: Int?) = request
        val finishedState = NewEntryFinishedFilter.ofBoolean(request.finished)
        if (start != null && last != null) {
            throw BadRequestException("Too many request params. Either provided a 'time-range' (start + optional end) or 'last'")
        }

        val data = if (start != null) {
            if (end != null && start > end) {
                throw BadRequestException("Expected value 'start' is bigger than 'end'")
            }

            if (end != null) {
                service.findByImoAndTimeRange(imo, start, end, finishedState)
            } else {
                service.findByImoStartingAtOrAfter(imo, start, finishedState)
            }
        } else if (last != null) {
            if (last < 1) {
                throw BadRequestException("Expected value >= 1 for parameter 'last'")
            }
            service.findLastByImo(imo, last, finishedState)
        } else {
            throw BadRequestException("Missing required parameters. Choose 'start'+'end' or 'last'")
        }
        return JournalItemByImoResponse(request, data.mapToApi())
    }

    private fun JournalItem.mapToApi() = mapper.toApi(this)
    private fun List<JournalItem>.mapToApi() = map { it.mapToApi() }
}
