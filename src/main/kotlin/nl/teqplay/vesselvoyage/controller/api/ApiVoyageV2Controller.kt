package nl.teqplay.vesselvoyage.controller.api

import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2
import nl.teqplay.skeleton.common.exception.BadRequestException
import nl.teqplay.skeleton.common.exception.NotFoundException
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.apiv2.model.Voyage
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.VoyageByImoRequest
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.VoyageByImoResponse
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.VoyagesByAisDestination
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.VoyagesByPortRequest
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.VoyagesByPortRequestUsingAreaId
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.VoyagesByPortRequestUsingUnlocode
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.VoyagesByPortResponse
import nl.teqplay.vesselvoyage.auth.Operation
import nl.teqplay.vesselvoyage.auth.Resource
import nl.teqplay.vesselvoyage.datasource.NewVoyageDataSource
import nl.teqplay.vesselvoyage.mapper.EntryV2Mapper
import nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import nl.teqplay.vesselvoyage.service.StaticShipInfoService
import nl.teqplay.vesselvoyage.service.api.VoyageV2Service
import nl.teqplay.vesselvoyage.util.validateDwtTeu
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.CrossOrigin
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.Instant

@CrossOrigin
@ProfileApi
@RestController
@RequestMapping(
    path = ["/v2/voyage"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ApiVoyageV2Controller(
    private val service: VoyageV2Service,
    private val mapper: EntryV2Mapper,
    private val shipService: StaticShipInfoService
) : BaseApiV2Controller<Voyage, NewVoyage, NewVoyageDataSource, VoyageV2Service>(service, shipService) {

    @GetMapping("/{id}")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findById(@PathVariable id: String) = service
        .findById(id)
        ?.mapToApi()
        ?: throw NotFoundException("A voyage with the given id does not exist")

    @PostMapping("/ids")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findByIds(@RequestBody ids: List<String>) = service
        .findByIds(ids.toSet())
        .mapToApi()

    @GetMapping("/byImo/{imo}")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findByImo(
        @PathVariable imo: Int,
        @RequestParam("start") start: Instant?,
        @RequestParam("end") end: Instant?,
        @RequestParam("last") last: Int?,
        @RequestParam("finished") finished: Boolean?,
    ): VoyageByImoResponse {
        val request = VoyageByImoRequest(
            imo = imo,
            start = start,
            end = end,
            last = last,
            finished = finished,
            // Voyages can't be confirmed, so we can just provide null
            confirmed = null
        )
        return findByImo(request)
    }

    @PostMapping("/byImo")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findByImoBatch(
        @RequestBody requests: List<VoyageByImoRequest>
    ): List<VoyageByImoResponse> {
        return requests.map { request -> findByImo(request) }
    }

    @GetMapping("/byPort")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findByPort(
        @RequestParam("originPortUnlocodes") originPortUnlocodes: List<String>?,
        @RequestParam("destinationPortUnlocodes") destinationPortUnlocodes: List<String>?,
        @RequestParam("originAreaIds") originPortAreaIds: List<String>?,
        @RequestParam("destinationAreaIds") destinationPortAreaIds: List<String>?,
        @RequestParam("aisDestinationUnlocodes") aisDestinationUnlocodes: List<String>?,
        @RequestParam("start") start: Instant?,
        @RequestParam("end") end: Instant?,
        @RequestParam("limit") limit: Int?,
        @RequestParam("categories") categories: Set<ShipCategoryV2>?,
        @RequestParam("finished") finished: Boolean?,
        @RequestParam("minDwt") minDwt: Int?,
        @RequestParam("maxDwt") maxDwt: Int?,
        @RequestParam("minTeu") minTeu: Int?,
        @RequestParam("maxTeu") maxTeu: Int?,
    ): VoyagesByPortResponse {

        val hasUnlocodes = originPortUnlocodes?.isNotEmpty() == true || destinationPortUnlocodes?.isNotEmpty() == true
        val hasAreaIds = originPortAreaIds?.isNotEmpty() == true || destinationPortAreaIds?.isNotEmpty() == true
        val hasAisDestinations = aisDestinationUnlocodes?.isNotEmpty() == true
        val request = when {
            hasUnlocodes && hasAreaIds -> throw BadRequestException("Provide unlocodes OR areaIds, not both")
            hasUnlocodes -> VoyagesByPortRequestUsingUnlocode(
                originPortUnlocodes = originPortUnlocodes ?: emptyList(),
                destinationPortUnlocodes = destinationPortUnlocodes ?: emptyList(),
                start = start,
                end = end,
                limit = limit,
                categories = categories,
                finished = finished,
                minDwt = minDwt,
                maxDwt = maxDwt,
                minTeu = minTeu,
                maxTeu = maxTeu,
            )
            hasAreaIds -> VoyagesByPortRequestUsingAreaId(
                originPortAreaIds = originPortAreaIds ?: emptyList(),
                destinationPortAreaIds = destinationPortAreaIds ?: emptyList(),
                start = start,
                end = end,
                limit = limit,
                categories = categories,
                finished = finished,
                minDwt = minDwt,
                maxDwt = maxDwt,
                minTeu = minTeu,
                maxTeu = maxTeu,
            )
            hasAisDestinations -> VoyagesByAisDestination(
                aisDestinationUnlocodes = aisDestinationUnlocodes ?: emptyList(),
                start = start,
                end = end,
                limit = limit,
                categories = categories,
                finished = finished,
                minDwt = minDwt,
                maxDwt = maxDwt,
                minTeu = minTeu,
                maxTeu = maxTeu,
            )
            else -> throw BadRequestException("Expected unlocodes OR areaIds port identifiers, but none given")
        }
        validateDwtTeu(request)
        return findByPort(request)
    }

    @PostMapping("/byPort")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findByPortBatch(@RequestBody requests: List<VoyagesByPortRequest>): List<VoyagesByPortResponse> {
        return requests.map { request -> findByPort(request) }
    }

    private fun findByImo(request: VoyageByImoRequest): VoyageByImoResponse {
        val data = with(request) {
            findByImoBase(
                imo = imo,
                start = start,
                end = end,
                last = last,
                finished = finished,
                confirmed = confirmed
            )
        }
        return VoyageByImoResponse(
            request = request,
            data = data.mapToApi()
        )
    }

    private fun findByPort(request: VoyagesByPortRequest): VoyagesByPortResponse {
        val finishedState = NewEntryFinishedFilter.ofBoolean(request.finished)

        // when ship filters are given (e.g. ship category, dwt/teu), find the ships that match the given filters
        // might be rather large, but for now the most optimal way to do it
        val qualifyingImos = selectImosOrEmpty(request)

        val voyages = when (request) {
            is VoyagesByPortRequestUsingUnlocode -> service.findByPortUnlocode(
                originPortUnlocodes = request.originPortUnlocodes,
                destinationPortUnlocodes = request.destinationPortUnlocodes,
                start = request.start,
                end = request.end,
                limit = request.limit,
                finishedState = finishedState,
                qualifyingImos = qualifyingImos
            )

            is VoyagesByPortRequestUsingAreaId -> service.findByPortAreaId(
                originPortAreaIds = request.originPortAreaIds,
                destinationPortAreaIds = request.destinationPortAreaIds,
                start = request.start,
                end = request.end,
                limit = request.limit,
                finishedState = finishedState,
                qualifyingImos = qualifyingImos
            )

            is VoyagesByAisDestination -> service.findByAisDestination(
                aisDestinationUnlocodes = request.aisDestinationUnlocodes,
                start = request.start,
                end = request.end,
                limit = request.limit,
                finishedState = finishedState,
                qualifyingImos = qualifyingImos
            )

            // when is not exhaustive, as it's from another module
            else -> throw BadRequestException("Unknown request object")
        }

        return VoyagesByPortResponse(
            request = request,
            data = voyages.mapToApi()
        )
    }

    private fun NewVoyage.mapToApi() = mapper.toApi(this)
    private fun List<NewVoyage>.mapToApi() = map { voyage -> mapper.toApi(voyage) }
}
