package nl.teqplay.vesselvoyage.controller.api

import nl.teqplay.skeleton.common.exception.NotFoundException
import nl.teqplay.skeleton.model.Polyline
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.apiv2.model.Trace
import nl.teqplay.vesselvoyage.auth.Operation
import nl.teqplay.vesselvoyage.auth.Resource
import nl.teqplay.vesselvoyage.mapper.TraceMapper
import nl.teqplay.vesselvoyage.model.v2.EntryId
import nl.teqplay.vesselvoyage.service.trace.TraceService
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.CrossOrigin
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * Allows to retrieve traces.
 */
@CrossOrigin
@ProfileApi
@ProfileProcessing
@RestController
@RequestMapping(
    path = ["/v2/traces"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ApiTraceV2Controller(
    private val traceService: TraceService,
    private val mapper: TraceMapper
) {

    @GetMapping("/{entryId}")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findEntryId(
        @PathVariable entryId: EntryId,
        @RequestParam(defaultValue = "false") blocking: Boolean
    ): Trace {
        return traceService.getTraceById(
            entryId = entryId,
            scheduleTraceGeneratingIfMissing = true,
            blocking = blocking
        )?.let { trace -> mapper.toApi(trace) } ?: throw NotFoundException("Trace not found")
    }

    @GetMapping("/{entryId}/polyline")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findEntryPolyline(
        @PathVariable entryId: EntryId,
        @RequestParam(defaultValue = "false") blocking: Boolean
    ): Polyline {
        return this.findEntryId(entryId, blocking).polyline
    }

    @PostMapping("/ids")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findByEntryIds(
        @RequestBody entryIds: List<EntryId>,
        @RequestParam(defaultValue = "false") blocking: Boolean
    ): List<Trace> {
        val traces = traceService.getTracesByIds(
            entryIds = entryIds,
            scheduleTraceGeneratingIfMissing = true,
            blocking = blocking
        )

        return traces.map { trace -> mapper.toApi(trace) }
    }
}
