package nl.teqplay.vesselvoyage.controller.api

import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2
import nl.teqplay.skeleton.common.exception.BadRequestException
import nl.teqplay.skeleton.common.exception.NotFoundException
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.StatementOfFactsViewByImoLookAroundRequest
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.StatementOfFactsViewByImoLookAroundResponse
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.StatementOfFactsViewByImoRequest
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.StatementOfFactsViewByImoResponse
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.StatementOfFactsViewByPortRequest
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.StatementOfFactsViewByPortResponse
import nl.teqplay.vesselvoyage.apiv2.model.sof.StatementOfFactsView
import nl.teqplay.vesselvoyage.apiv2.model.sof.StatementOfFactsViewName
import nl.teqplay.vesselvoyage.auth.Operation
import nl.teqplay.vesselvoyage.auth.Resource
import nl.teqplay.vesselvoyage.datasource.NewVisitDataSource
import nl.teqplay.vesselvoyage.model.isVisitId
import nl.teqplay.vesselvoyage.model.v2.EntryId
import nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.service.StaticShipInfoService
import nl.teqplay.vesselvoyage.service.api.EsofV2Service
import nl.teqplay.vesselvoyage.service.api.VisitV2Service
import nl.teqplay.vesselvoyage.util.validateDwtTeu
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.CrossOrigin
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.Instant

@CrossOrigin
@ProfileApi
@RestController
@RequestMapping(
    path = ["/v2/sof"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ApiStatementOfFactsV2Controller(
    private val shipService: StaticShipInfoService,
    private val visitV2Service: VisitV2Service,
    private val esofV2Service: EsofV2Service
) : BaseApiV2Controller<StatementOfFactsView, NewVisit, NewVisitDataSource, VisitV2Service>(
    visitV2Service,
    shipService
) {

    @GetMapping("/byVisit/{visitId}")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun byVisit(
        @PathVariable visitId: EntryId,
        @RequestParam("view") view: StatementOfFactsViewName
    ): StatementOfFactsView {
        isVisitId(visitId) ||
            throw BadRequestException("Provided ID is not a Visit ID")

        return esofV2Service.produce(view, visitId)
            ?: throw NotFoundException("Could not generate SOF, visit not found")
    }

    @GetMapping("/byImo/{imo}")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findByImo(
        @PathVariable imo: Int,
        @RequestParam("start") start: Instant?,
        @RequestParam("end") end: Instant?,
        @RequestParam("last") last: Int?,
        @RequestParam("view") view: StatementOfFactsViewName,
        @RequestParam("finished") finished: Boolean?,
        @RequestParam("confirmed") confirmed: Boolean?
    ): StatementOfFactsViewByImoResponse<*> {
        val request = StatementOfFactsViewByImoRequest(
            view = view,
            imo = imo,
            start = start,
            end = end,
            last = last,
            finished = finished,
            confirmed = confirmed
        )
        return findByImo(request)
    }

    @PostMapping("/byImo")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findByImoBatch(
        @RequestBody requests: List<StatementOfFactsViewByImoRequest>
    ): List<StatementOfFactsViewByImoResponse<*>> {
        return requests.map { request -> findByImo(request) }
    }

    @GetMapping("/byImo/{imo}/lookaround")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findByImoLookAround(
        @PathVariable imo: Int,
        @RequestParam("timestamp") timestamp: Instant,
        @RequestParam("limit") limit: Int,
        @RequestParam("view") view: StatementOfFactsViewName,
        @RequestParam("finished") finished: Boolean?,
        @RequestParam("confirmed") confirmed: Boolean?
    ): StatementOfFactsViewByImoLookAroundResponse<*> {
        val request = StatementOfFactsViewByImoLookAroundRequest(view, imo, timestamp, limit, finished, confirmed)
        val data = visitV2Service.findByImoLookAround(
            imo = imo,
            limit = limit,
            timestamp = timestamp,
            finishedState = NewEntryFinishedFilter.ofBoolean(finished),
            confirmed = confirmed
        ).map { visit ->
            esofV2Service.produce(request.view, visit)
        }
        return StatementOfFactsViewByImoLookAroundResponse(request, data)
    }

    @GetMapping("/byPort")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun byPort(
        @RequestParam("unlocode") unlocode: String?,
        @RequestParam("areaId") areaId: String?,
        @RequestParam("aisTrueDestination") aisTrueDestination: String?,
        @RequestParam("start") start: Instant?,
        @RequestParam("end") end: Instant?,
        @RequestParam("categories") categories: Set<ShipCategoryV2>?,
        @RequestParam("view") view: StatementOfFactsViewName,
        @RequestParam("finished") finished: Boolean?,
        @RequestParam("confirmed") confirmed: Boolean?,
        @RequestParam("minDwt") minDwt: Int?,
        @RequestParam("maxDwt") maxDwt: Int?,
        @RequestParam("minTeu") minTeu: Int?,
        @RequestParam("maxTeu") maxTeu: Int?,
    ): StatementOfFactsViewByPortResponse<*> {
        val request = StatementOfFactsViewByPortRequest(
            view = view,
            start = start,
            end = end,
            unlocode = unlocode,
            areaId = areaId,
            categories = categories,
            finished = finished,
            confirmed = confirmed,
            minDwt = minDwt,
            maxDwt = maxDwt,
            minTeu = minTeu,
            maxTeu = maxTeu,
            aisTrueDestination = aisTrueDestination
        )
        validateDwtTeu(request)
        return StatementOfFactsViewByPortResponse(
            request = request,
            data = findByPort(request)
        )
    }

    @PostMapping("/byPort")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun byPort(
        @RequestBody requests: List<StatementOfFactsViewByPortRequest>
    ): List<StatementOfFactsViewByPortResponse<*>> {
        return requests.map { request ->
            StatementOfFactsViewByPortResponse(
                request = request,
                data = findByPort(request)
            )
        }
    }

    private fun findByImo(request: StatementOfFactsViewByImoRequest): StatementOfFactsViewByImoResponse<*> {
        val visits = with(request) {
            findByImoBase(
                imo = imo,
                start = start,
                end = end,
                last = last,
                finished = finished,
                confirmed = confirmed
            )
        }
        val sofs = esofV2Service.produceBulk(request.view, visits)
        return StatementOfFactsViewByImoResponse(
            request = request,
            data = sofs
        )
    }

    private fun findByPort(request: StatementOfFactsViewByPortRequest): List<StatementOfFactsView> {
        val qualifyingImos = selectImosOrEmpty(request)
        val unlocode = request.unlocode
        val areaId = request.areaId
        val finishedState = NewEntryFinishedFilter.ofBoolean(request.finished)
        val visits = when {
            unlocode != null && areaId != null -> throw BadRequestException("Provide unlocode or areaId, not both")
            unlocode != null -> visitV2Service.findByPortUnlocode(
                unlocode = unlocode,
                start = request.start,
                end = request.end,
                finishedState = finishedState,
                confirmed = request.confirmed,
                aisTrueDestination = request.aisTrueDestination,
                qualifyingImos = qualifyingImos
            )
            areaId != null -> visitV2Service.findByPortAreaId(
                pomaId = areaId,
                start = request.start,
                end = request.end,
                finishedState = finishedState,
                confirmed = request.confirmed,
                aisTrueDestination = request.aisTrueDestination,
                qualifyingImos = qualifyingImos
            )
            else -> throw BadRequestException("Provide unlocode or areaId")
        }
        return esofV2Service.produceBulk(request.view, visits)
    }
}
