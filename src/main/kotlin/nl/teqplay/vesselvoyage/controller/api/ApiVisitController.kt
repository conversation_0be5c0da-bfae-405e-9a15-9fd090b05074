package nl.teqplay.vesselvoyage.controller.api

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.platform.util.LocationUtils
import nl.teqplay.skeleton.common.exception.NotFoundException
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.auth.Operation
import nl.teqplay.vesselvoyage.auth.Resource
import nl.teqplay.vesselvoyage.controller.annotation.IsoDate
import nl.teqplay.vesselvoyage.model.Filter
import nl.teqplay.vesselvoyage.model.Stop
import nl.teqplay.vesselvoyage.model.StopType
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.model.VisitQuery
import nl.teqplay.vesselvoyage.properties.ControllerLimitProperties
import nl.teqplay.vesselvoyage.service.InfraCacheService.Companion.BERTH_SCALE
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.StaticShipInfoService
import nl.teqplay.vesselvoyage.service.VisitService
import nl.teqplay.vesselvoyage.util.joinToStringTruncated
import nl.teqplay.vesselvoyage.util.throwWhenThresholdReached
import nl.teqplay.vesselvoyage.util.toPlatformLocation
import nl.teqplay.vesselvoyage.util.toScaled
import nl.teqplay.vesselvoyage.util.toVesselVoyageLocation
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.ZonedDateTime
import java.util.concurrent.ConcurrentHashMap

@ProfileApi
@RestController
@RequestMapping(
    path = ["/v1/visits"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ApiVisitController(
    properties: ControllerLimitProperties,
    private val visitService: VisitService,
    private val infraService: InfraService,
    private val staticShipInfoService: StaticShipInfoService
) {
    private val log = KotlinLogging.logger {}

    private val maxEntries = properties.maxEntries

    @GetMapping("/ports/{portId}")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findByPortId(
        @PathVariable portId: String,
        @RequestParam(required = true) @IsoDate start: ZonedDateTime,
        @RequestParam(required = true) @IsoDate end: ZonedDateTime
    ): List<Visit> {
        log.debug { "Find visits port $portId, start=$start, end=$end" }

        val visits = visitService.findByPortId(portId, start, end, maxEntries)

        throwWhenThresholdReached(visits.size, maxEntries)

        return visits
    }

    @PostMapping("/ports/{portId}")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findByPortId(
        @PathVariable portId: String,
        @RequestParam(required = true) @IsoDate start: ZonedDateTime,
        @RequestParam(required = true) @IsoDate end: ZonedDateTime,
        @RequestBody filter: Filter
    ): List<Visit> {
        val visits = visitService.findByPortId(portId, start, end, maxEntries, filter)

        throwWhenThresholdReached(visits.size, maxEntries)

        return visits
    }

    @GetMapping("/destination/{destination}")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findByDestination(
        @PathVariable destination: String
    ): List<Visit> {
        return visitService.findByDestination(destination)
    }

    @PostMapping("/destination/{destination}")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findByDestination(
        @PathVariable destination: String,
        @RequestBody filter: Filter
    ): List<Visit> {
        return visitService.findByDestination(destination, filter)
    }

    @GetMapping("/previous/{imo}")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findPreviousLocationByIMO(
        @PathVariable imo: String,
        @RequestParam(required = false) @IsoDate before: ZonedDateTime?,
    ): Visit {
        log.debug { "Find previous location for ship with IMO $imo, before=$before" }

        val beforeOrNow = before ?: ZonedDateTime.now()

        return visitService.findPreviousLocationByIMO(imo, beforeOrNow)
            ?: throw NotFoundException("No previous visit found for IMO $imo before $beforeOrNow")
    }

    @PostMapping("/previous")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findPreviousLocationsByIMO(
        @RequestBody imos: List<String>,
        @RequestParam(required = false) @IsoDate before: ZonedDateTime?,
    ): List<Visit> {
        log.debug { "Find previous location for list with IMO's ${imos.joinToStringTruncated(limit = 10)}, before=$before" }

        return visitService.findPreviousLocationsByIMO(imos, before ?: ZonedDateTime.now())
    }

    @PostMapping("/query")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun queryVisits(
        @RequestBody query: VisitQuery,
    ): List<Visit> {
        log.debug { "Query visits, query=$query" }

        return findLimited(query)
    }

    @GetMapping("/{imo}/ids")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun getVisitsIdByImo(
        @PathVariable imo: String,
    ): List<String> {
        return visitService.getVisitIdsByImo(imo)
    }

    @GetMapping("/{imo}/count")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun getVisitCountByImo(
        @PathVariable imo: String,
    ): Long {
        return visitService.countByImo(imo)
    }

    private fun findLimited(query: VisitQuery): List<Visit> {
        // limit the limit to protect against a too large query that would result in the server running out of memory
        val limitedQuery = query.copy(
            limit = query.limit.coerceAtMost(maxEntries)
        )

        val visits = visitService.find(limitedQuery).toList()

        throwWhenThresholdReached(visits.size, maxEntries)

        return if (query.fallbackStopEnd) {
            visits.parallelStream()
                .map(::prepareVisit)
                .toList()
        } else {
            visits
        }
    }

    private fun prepareVisit(visit: Visit): Visit {
        // Ongoing visits can't be fixed
        if (visit.endTime == null) {
            return visit
        }

        val stops = visit.esof?.stops
            // No need to do any fallback logic when there are no stops to fix
            ?: return visit

        val preparedStops = ConcurrentHashMap<Int, Stop>()

        stops.parallelStream().forEach { stop ->
            val index = stops.indexOf(stop)
            if (stop.endTime != null) {
                preparedStops[index] = stop
                return@forEach
            }

            val nextStop = stops.getOrNull(index + 1)
            val fallbackEndEventId = nextStop?.startEventId ?: stop.startEventId
            val fallbackEndTime = nextStop?.startTime ?: visit.endTime
            val fallbackEndLocation = if (stop.type != StopType.ANCHOR_AREA) {
                val stopPlatformLocation = stop.startLocation.toPlatformLocation()
                val berthStopLocationKey = stop.startLocation.toScaled(BERTH_SCALE)

                val ship = staticShipInfoService.getShipRegisterInfoCacheByIMO(imo = visit.imo)
                val shipLength = ship?.dimensions?.length

                // Use the berth closest to our start location
                val berthDistancePair = infraService.getBerthsByLocation(berthStopLocationKey)
                    .filter { berth ->
                        // Only berths with a cargo category are trustable to check
                        // Next to that to do this check we need a ship length, ships without are not interested
                        if (berth.cargoCategoryType.isNullOrEmpty() || shipLength == null) {
                            return@filter false
                        }

                        val berthLength = berth.length
                            // We can't check if the length is not set on the berth
                            ?: return@filter false

                        // Only check the length if we know it, otherwise only allow berths bigger than the ship
                        berthLength >= shipLength
                    }
                    .mapNotNull { berth ->
                        val distance = LocationUtils.haversineDistance(berth.location.toPlatformLocation(), stopPlatformLocation)
                        // Only take into account berths that are not far away from the ship
                        if (distance <= 400) {
                            berth to distance
                        } else {
                            null
                        }
                    }
                    .minByOrNull { (_, distance) -> distance }

                berthDistancePair?.first?.location?.toVesselVoyageLocation()
            } else {
                null
            } ?: nextStop?.startLocation ?: stop.startLocation

            val fallbackStop = stop.copy(
                endEventId = "$fallbackEndEventId.FALLBACK",
                endTime = fallbackEndTime,
                endLocation = fallbackEndLocation
            )
            preparedStops[index] = fallbackStop
        }

        val sortedStops = preparedStops.toSortedMap()
            .values
            .toList()
        val updatedEsof = visit.esof?.copy(stops = sortedStops)
        return visit.copy(esof = updatedEsof)
    }
}
