package nl.teqplay.vesselvoyage.controller.api

import nl.teqplay.skeleton.common.exception.BadRequestException
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.apiv2.model.Visit
import nl.teqplay.vesselvoyage.apiv2.model.sof.StatementOfFactsView
import nl.teqplay.vesselvoyage.apiv2.model.sof.StatementOfFactsViewName
import nl.teqplay.vesselvoyage.auth.Operation
import nl.teqplay.vesselvoyage.auth.Resource
import nl.teqplay.vesselvoyage.datasource.NewVisitDataSource
import nl.teqplay.vesselvoyage.mapper.EntryV2Mapper
import nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.service.StaticShipInfoService
import nl.teqplay.vesselvoyage.service.api.EsofV2Service
import nl.teqplay.vesselvoyage.service.api.VisitV2Service
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * Controller providing paginated endpoints for retrieving data from the database.
 * Supports pagination for PTOStatementOfFactsView and Visit objects.
 *
 * Note: This is a basic implementation that uses the existing findByLimited method.
 * For production use, consider implementing dedicated pagination methods in the service layer
 * that support filtering by finished/confirmed status at the database level for better performance.
 */
@ProfileApi
@RestController
@RequestMapping(
    path = ["/v2/paginated"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ApiPaginatedDataController(
    private val visitV2Service: VisitV2Service,
    private val esofV2Service: EsofV2Service,
    private val mapper: EntryV2Mapper,
    private val shipService: StaticShipInfoService
) : BaseApiV2Controller<Any, NewVisit, NewVisitDataSource, VisitV2Service>(visitV2Service, shipService) {

    companion object {
        private const val DEFAULT_PAGE_SIZE = 20
        private const val MAX_PAGE_SIZE = 100
        private const val MIN_PAGE_NUMBER = 1
    }

    /**
     * Retrieve all PTOStatementOfFactsView objects with pagination support.
     * 
     * @param page Page number (1-based, default: 1)
     * @param size Page size (default: 20, max: 100)
     * @param view Statement of Facts view type (required)
     * @param finished Filter by finished status (true/false/null for all)
     * @param confirmed Filter by confirmed status (true/false/null for all)
     * @return Paginated response containing PTOStatementOfFactsView objects
     */
    @GetMapping("/statement-of-facts")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun getPaginatedStatementOfFacts(
        @RequestParam(defaultValue = "1") page: Int,
        @RequestParam(defaultValue = "$DEFAULT_PAGE_SIZE") size: Int,
        @RequestParam view: StatementOfFactsViewName,
        @RequestParam(required = false) finished: Boolean?,
        @RequestParam(required = false) confirmed: Boolean?
    ): PaginatedResponse<StatementOfFactsView> {
        validatePaginationParameters(page, size)
        
        val qualifyingImos = emptySet<Int>() // No ship filtering for general pagination

        // Get visits with pagination using the existing service method
        // Note: The findByLimited method doesn't support finished/confirmed filtering,
        // so we'll need to implement a more sophisticated approach for production use
        val visits = visitV2Service.findByLimited(
            limited = false, // Get all entries, not just limited ones
            imos = qualifyingImos,
            page = page,
            pageSize = size
        )
        
        // Generate Statement of Facts for the visits
        val statementOfFacts = esofV2Service.produceBulk(view, visits)
        
        // Get total count for pagination metadata
        val totalCount = visitV2Service.countByLimited(false, qualifyingImos)
        
        return PaginatedResponse(
            data = statementOfFacts,
            pagination = PaginationMetadata(
                page = page,
                size = size,
                totalElements = totalCount,
                totalPages = calculateTotalPages(totalCount, size)
            )
        )
    }

    /**
     * Retrieve all Visit objects with pagination support.
     * 
     * @param page Page number (1-based, default: 1)
     * @param size Page size (default: 20, max: 100)
     * @param finished Filter by finished status (true/false/null for all)
     * @param confirmed Filter by confirmed status (true/false/null for all)
     * @return Paginated response containing Visit objects
     */
    @GetMapping("/visits")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun getPaginatedVisits(
        @RequestParam(defaultValue = "1") page: Int,
        @RequestParam(defaultValue = "$DEFAULT_PAGE_SIZE") size: Int,
        @RequestParam(required = false) finished: Boolean?,
        @RequestParam(required = false) confirmed: Boolean?
    ): PaginatedResponse<Visit> {
        validatePaginationParameters(page, size)
        
        val qualifyingImos = emptySet<Int>() // No ship filtering for general pagination

        // Get visits with pagination using the existing service method
        // Note: The findByLimited method doesn't support finished/confirmed filtering,
        // so we'll need to implement a more sophisticated approach for production use
        val newVisits = visitV2Service.findByLimited(
            limited = false, // Get all entries, not just limited ones
            imos = qualifyingImos,
            page = page,
            pageSize = size
        )
        
        // Map to API Visit objects
        val visits = newVisits.map { mapper.toApi(it) }
        
        // Get total count for pagination metadata
        val totalCount = visitV2Service.countByLimited(false, qualifyingImos)
        
        return PaginatedResponse(
            data = visits,
            pagination = PaginationMetadata(
                page = page,
                size = size,
                totalElements = totalCount,
                totalPages = calculateTotalPages(totalCount, size)
            )
        )
    }

    /**
     * Validates pagination parameters and throws BadRequestException if invalid.
     */
    private fun validatePaginationParameters(page: Int, size: Int) {
        if (page < MIN_PAGE_NUMBER) {
            throw BadRequestException("Page number must be greater than or equal to $MIN_PAGE_NUMBER")
        }
        if (size < 1) {
            throw BadRequestException("Page size must be greater than 0")
        }
        if (size > MAX_PAGE_SIZE) {
            throw BadRequestException("Page size must not exceed $MAX_PAGE_SIZE")
        }
    }

    /**
     * Calculates the total number of pages based on total elements and page size.
     */
    private fun calculateTotalPages(totalElements: Long, pageSize: Int): Long {
        return if (totalElements == 0L) 0L else (totalElements + pageSize - 1) / pageSize
    }
}

/**
 * Generic paginated response wrapper.
 */
data class PaginatedResponse<T>(
    val data: List<T>,
    val pagination: PaginationMetadata
)

/**
 * Pagination metadata containing information about the current page and total elements.
 */
data class PaginationMetadata(
    val page: Int,
    val size: Int,
    val totalElements: Long,
    val totalPages: Long
)
