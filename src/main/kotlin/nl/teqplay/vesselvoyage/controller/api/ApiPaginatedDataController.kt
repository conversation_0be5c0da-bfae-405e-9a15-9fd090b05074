package nl.teqplay.vesselvoyage.controller.api

import nl.teqplay.skeleton.common.exception.BadRequestException
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.apiv2.model.Visit
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.PaginatedResponse
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.PaginationMetadata
import nl.teqplay.vesselvoyage.apiv2.model.sof.StatementOfFactsView
import nl.teqplay.vesselvoyage.apiv2.model.sof.StatementOfFactsViewName
import nl.teqplay.vesselvoyage.auth.Operation
import nl.teqplay.vesselvoyage.auth.Resource
import nl.teqplay.vesselvoyage.datasource.NewVisitDataSource
import nl.teqplay.vesselvoyage.mapper.EntryV2Mapper
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.service.StaticShipInfoService
import nl.teqplay.vesselvoyage.service.api.EsofV2Service
import nl.teqplay.vesselvoyage.service.api.VisitV2Service
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * Controller providing paginated endpoints for retrieving ALL data from the database.
 * Supports pagination for PTOStatementOfFactsView and Visit objects.
 *
 * This implementation retrieves ALL entries from the database without any filtering
 * (no limited status filtering, no finished/confirmed filtering, etc.).
 * The finished and confirmed parameters are included for API compatibility but are currently ignored.
 */
@ProfileApi
@RestController
@RequestMapping(
    path = ["/v2/paginated"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ApiPaginatedDataController(
    private val visitV2Service: VisitV2Service,
    private val esofV2Service: EsofV2Service,
    private val mapper: EntryV2Mapper,
    shipService: StaticShipInfoService
) : BaseApiV2Controller<Any, NewVisit, NewVisitDataSource, VisitV2Service>(visitV2Service, shipService) {

    companion object {
        private const val DEFAULT_PAGE_SIZE = 500
        private const val MAX_PAGE_SIZE = 1000
        private const val MIN_PAGE_NUMBER = 1
    }

    /**
     * Retrieve ALL PTOStatementOfFactsView objects with pagination support.
     * This endpoint returns ALL entries from the database without any filtering.
     *
     * @param page Page number (1-based, default: 1)
     * @param size Page size (default: [DEFAULT_PAGE_SIZE], max: [MAX_PAGE_SIZE])
     * @param view Statement of Facts view type (required)
     * @return Paginated response containing ALL PTOStatementOfFactsView objects
     */
    @GetMapping("/pto-statement-of-facts")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun getPaginatedPTOStatementOfFacts(
        @RequestParam(defaultValue = "1") page: Int,
        @RequestParam(defaultValue = "$DEFAULT_PAGE_SIZE") size: Int,
        @RequestParam view: StatementOfFactsViewName,
    ): PaginatedResponse<StatementOfFactsView> {
        validatePaginationParameters(page, size)

        // Get ALL visits with pagination (no filtering whatsoever)
        val visits = visitV2Service.findAllWithPagination(
            page = page,
            pageSize = size
        )

        // Generate Statement of Facts for the visits
        val statementOfFacts = esofV2Service.produceBulk(view, visits)

        // Get total count for pagination metadata
        val totalCount = visitV2Service.countAllEntries()

        return PaginatedResponse(
            data = statementOfFacts,
            pagination = PaginationMetadata(
                page = page,
                size = size,
                totalElements = totalCount,
                totalPages = calculateTotalPages(totalCount, size)
            )
        )
    }

    /**
     * Retrieve ALL Visit objects with pagination support.
     * This endpoint returns ALL entries from the database without any filtering.
     *
     * @param page Page number (1-based, default: 1)
     * @param size Page size (default: [DEFAULT_PAGE_SIZE], max: [MAX_PAGE_SIZE])
     * @return Paginated response containing ALL Visit objects
     */
    @GetMapping("/visits")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun getPaginatedVisits(
        @RequestParam(defaultValue = "1") page: Int,
        @RequestParam(defaultValue = "$DEFAULT_PAGE_SIZE") size: Int,
    ): PaginatedResponse<Visit> {
        validatePaginationParameters(page, size)

        // Get ALL visits with pagination (no filtering whatsoever)
        val newVisits = visitV2Service.findAllWithPagination(
            page = page,
            pageSize = size
        )

        // Map to API Visit objects
        val visits = newVisits.map { mapper.toApi(it) }

        // Get total count for pagination metadata
        val totalCount = visitV2Service.countAllEntries()

        return PaginatedResponse(
            data = visits,
            pagination = PaginationMetadata(
                page = page,
                size = size,
                totalElements = totalCount,
                totalPages = calculateTotalPages(totalCount, size)
            )
        )
    }

    /**
     * Validates pagination parameters and throws BadRequestException if invalid.
     */
    private fun validatePaginationParameters(page: Int, size: Int) {
        if (page < MIN_PAGE_NUMBER) {
            throw BadRequestException("Page number must be greater than or equal to $MIN_PAGE_NUMBER")
        }
        if (size < 1) {
            throw BadRequestException("Page size must be greater than 0")
        }
        if (size > MAX_PAGE_SIZE) {
            throw BadRequestException("Page size must not exceed $MAX_PAGE_SIZE")
        }
    }

    /**
     * Calculates the total number of pages based on total elements and page size.
     */
    private fun calculateTotalPages(totalElements: Long, pageSize: Int): Long {
        return if (totalElements == 0L) 0L else (totalElements + pageSize - 1) / pageSize
    }
}
