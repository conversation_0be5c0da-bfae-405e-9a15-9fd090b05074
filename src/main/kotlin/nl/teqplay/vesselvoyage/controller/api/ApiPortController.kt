package nl.teqplay.vesselvoyage.controller.api

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.auth.Operation
import nl.teqplay.vesselvoyage.auth.Resource
import nl.teqplay.vesselvoyage.model.Filter
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.model.Voyage
import nl.teqplay.vesselvoyage.service.VisitService
import nl.teqplay.vesselvoyage.service.VoyageService
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@ProfileApi
@RestController
@RequestMapping(
    path = ["/v1/ports"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ApiPortController(
    private val visitService: VisitService,
    private val voyageService: VoyageService
) {
    private val log = KotlinLogging.logger {}

    @GetMapping("/{portId}/visits")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findCurrentVisitsByPortId(
        @PathVariable portId: String
    ): List<Visit> {
        log.debug { "Find current visits for port $portId" }

        return visitService.findCurrentVisitsByPortId(portId).toList()
    }

    @PostMapping("/{portId}/visits")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findCurrentVisitsByPortId(
        @PathVariable portId: String,
        @RequestBody filter: Filter
    ): List<Visit> = visitService.findCurrentVisitsByPortId(portId, filter)

    @GetMapping("/{portId}/anchorages")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findCurrentAnchorageByPortId(
        @PathVariable portId: String
    ): List<Visit> {
        log.debug { "Find current anchorages for port $portId" }

        return visitService.findCurrentAnchorageByPortId(portId).toList()
    }

    @PostMapping("/{portId}/anchorages")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findCurrentAnchorageByPortId(
        @PathVariable portId: String,
        @RequestBody filter: Filter
    ): List<Visit> = visitService.findCurrentAnchorageByPortId(portId, filter)

    @GetMapping("/{portId}/next")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findByNextPort(
        @PathVariable portId: String
    ): List<Voyage> {
        log.debug { "Find active voyages with destination $portId" }

        return voyageService.findByDestination(portId)
    }

    @PostMapping("/{portId}/next")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findByNextPort(
        @PathVariable portId: String,
        @RequestBody filter: Filter
    ): List<Voyage> {
        return voyageService.findByDestination(portId, filter)
    }

    @GetMapping("/{portId}/previous")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findByPreviousPort(
        @PathVariable portId: String
    ): List<Voyage> {
        log.debug { "Find active voyages of ships that have left $portId" }

        return voyageService.findByOrigin(portId)
    }

    @PostMapping("/{portId}/previous")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findByPreviousPort(
        @PathVariable portId: String,
        @RequestBody filter: Filter
    ): List<Voyage> {
        return voyageService.findByOrigin(portId, filter)
    }
}
