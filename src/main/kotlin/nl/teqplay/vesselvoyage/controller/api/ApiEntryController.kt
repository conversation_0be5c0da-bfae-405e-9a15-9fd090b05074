package nl.teqplay.vesselvoyage.controller.api

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.auth.Operation
import nl.teqplay.vesselvoyage.auth.Resource
import nl.teqplay.vesselvoyage.model.ChangeMetadata
import nl.teqplay.vesselvoyage.model.Entry
import nl.teqplay.vesselvoyage.model.VisitVoyage
import nl.teqplay.vesselvoyage.properties.ControllerLimitProperties
import nl.teqplay.vesselvoyage.service.EntryService
import nl.teqplay.vesselvoyage.util.joinToStringTruncated
import nl.teqplay.vesselvoyage.util.throwWhenThresholdReached
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import kotlin.math.floor

@ProfileApi
@RestController
@RequestMapping(
    path = ["/v1/entries"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ApiEntryController(
    properties: ControllerLimitProperties,
    private val entryService: EntryService
) {
    private val log = KotlinLogging.logger {}

    private val maxEntries = properties.maxEntries

    @GetMapping("{entryId}")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findEntryById(@PathVariable entryId: String): ResponseEntity<Entry> {
        log.debug { "Find entry $entryId" }

        val result = entryService.findEntry(entryId)

        return if (result == null) {
            ResponseEntity.notFound()
                .build()
        } else {
            ResponseEntity.ok(result)
        }
    }

    @PostMapping
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findEntriesByIds(@RequestBody entryIds: List<String>): List<Entry> {
        log.debug { "Find ${entryIds.size} entries ${entryIds.joinToStringTruncated(limit = 10)}" }

        throwWhenThresholdReached(entryIds.size, maxEntries)

        return entryService.findEntries(entryIds)
    }

    @PostMapping("/metadata/imos")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun getChangeMetadataByIMOs(
        @RequestBody imos: Set<String>
    ): Map<String, ChangeMetadata> = imos.associateWith { imo -> entryService.getChangeMetadata(imo) }

    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    @GetMapping("/visits/{imo}")
    fun getVisitsAround(
        @PathVariable imo: String,
        @RequestParam(required = false) visitId: String? = null,
        @RequestParam radius: UInt = 2u, // so it will receive, in the most common case 2*2 + 1 (-> 2 past + 2 future + 1 centerPivot)
    ): List<VisitVoyage> {
        /*
           Range needs to be at Most (maxEntries-1) / (2 * 2) because:
             -> [maxEntries -1] because the pivot visit (the centered visit) counts as one result, so we want to exclude it from the capped range.
             -> [divided by 4] because we're about to request past and future visits (so divided by 2) and also their corresponding previous voyages (so again divided by 2).
        */
        val cappedRadius = radius.coerceAtMost(floor((maxEntries - 1).toDouble() / (4)).toUInt())
        val combinedVisitVoyages = entryService.getVisitsAroundVisit(imo, visitId, cappedRadius)
        throwWhenThresholdReached(combinedVisitVoyages.size, maxEntries)
        return combinedVisitVoyages
    }
}
