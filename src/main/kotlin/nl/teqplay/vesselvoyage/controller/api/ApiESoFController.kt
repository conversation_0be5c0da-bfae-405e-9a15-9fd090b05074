package nl.teqplay.vesselvoyage.controller.api

import nl.teqplay.skeleton.common.exception.NotFoundException
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.apiv2.model.ESoF
import nl.teqplay.vesselvoyage.auth.Operation
import nl.teqplay.vesselvoyage.auth.Resource
import nl.teqplay.vesselvoyage.mapper.EntryV2Mapper
import nl.teqplay.vesselvoyage.model.v2.NewESoF
import nl.teqplay.vesselvoyage.service.api.EsofV2Service
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.CrossOrigin
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@CrossOrigin
@ProfileApi
@RestController
@RequestMapping(
    path = ["/v2/esof"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ApiESoFController(
    private val service: EsofV2Service,
    private val mapper: EntryV2Mapper
) {
    @GetMapping("/{id}")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findById(@PathVariable id: String) = service
        .findById(id)
        ?.mapToApi()
        ?: throw NotFoundException("A visit with the given id does not exist")

    @PostMapping("/ids")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findByIds(@RequestBody ids: List<String>) = service
        .findAllById(ids)
        .mapToApi()

    fun NewESoF.mapToApi(): ESoF = mapper.toApi(this)
    fun List<NewESoF>.mapToApi(): List<ESoF> = map { it.mapToApi() }
}
