package nl.teqplay.vesselvoyage.controller.api

import nl.teqplay.skeleton.common.exception.BadRequestException
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.ShipPropertyFilterRequest
import nl.teqplay.vesselvoyage.datasource.BaseApiDataSource
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter
import nl.teqplay.vesselvoyage.service.StaticShipInfoService
import nl.teqplay.vesselvoyage.service.api.BaseApiV2Service
import nl.teqplay.vesselvoyage.util.shipMatchesCategoryAndOptionalRange
import java.time.Instant

abstract class BaseApiV2Controller<
    T : Any,
    I : NewEntry,
    D : BaseApiDataSource<I>,
    S : BaseApiV2Service<I, D>
    >(
    private val service: S,
    private val shipService: StaticShipInfoService
) {
    protected fun findByImoBase(
        imo: Int,
        start: Instant?,
        end: Instant?,
        last: Int?,
        finished: Boolean?,
        confirmed: Boolean?,
    ): List<I> {
        val finishedState = NewEntryFinishedFilter.ofBoolean(finished)
        if (start != null && last != null) {
            throw BadRequestException("Too many request params. Either provided a 'time-range' (start + optional end) or 'last'")
        }

        return if (start != null) {
            if (end != null && start > end) {
                throw BadRequestException("'start' is bigger than 'end'")
            }

            if (end != null) {
                service.findByImoAndTimeRange(
                    imo = imo,
                    start = start,
                    end = end,
                    finishedState = finishedState,
                    confirmed = confirmed
                )
            } else {
                service.findByImoStartingAtOrAfter(
                    imo = imo,
                    start = start,
                    finishedState = finishedState,
                    confirmed = confirmed
                )
            }
        } else if (last != null) {
            if (last < 1) {
                throw BadRequestException("Expected value >= 1 for parameter 'last'")
            }
            service.findLastByImo(
                imo = imo,
                limit = last,
                finishedState = finishedState,
                confirmed = confirmed
            )
        } else {
            throw BadRequestException("Missing required parameters. Choose 'start'+'end' or 'last'")
        }
    }

    protected fun selectImosOrEmpty(request: ShipPropertyFilterRequest): Set<Int> {
        return request.categories
            ?.flatMap { category ->
                shipService
                    .getShipsByCategory(category)
                    .parallelStream().filter { imo ->
                        val ship = shipService.getShipRegisterInfoCacheByIMO(imo)
                        ship != null && shipMatchesCategoryAndOptionalRange(ship, request)
                    }
                    .map { imo -> imo.toInt() }
                    .toList()
            }
            ?.toSet()
            ?: emptySet()
    }
}
