package nl.teqplay.vesselvoyage.controller.api

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.auth.Operation
import nl.teqplay.vesselvoyage.auth.Resource
import nl.teqplay.vesselvoyage.model.ExtendedVoyage
import nl.teqplay.vesselvoyage.model.Voyage
import nl.teqplay.vesselvoyage.model.VoyageQuery
import nl.teqplay.vesselvoyage.properties.ControllerLimitProperties
import nl.teqplay.vesselvoyage.service.StaticShipInfoService
import nl.teqplay.vesselvoyage.service.VoyageService
import nl.teqplay.vesselvoyage.util.throwWhenThresholdReached
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@ProfileApi
@RestController
@RequestMapping(
    path = ["/v1/voyages"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ApiVoyageController(
    properties: ControllerLimitProperties,
    private val voyageService: VoyageService,
    private val staticShipInfoService: StaticShipInfoService
) {
    private val log = KotlinLogging.logger {}

    private val maxEntries = properties.maxEntries

    @PostMapping("/query")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun queryVoyages(
        @RequestBody query: VoyageQuery,
    ): List<Voyage> {
        log.debug { "Query voyages, query=$query" }

        return findLimited(query)
    }

    @PostMapping("/query/extended")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun queryVoyagesDetailed(
        @RequestBody query: VoyageQuery,
    ): List<ExtendedVoyage> {
        log.debug { "Query voyages, query=$query" }

        return findExtendedVoyages(query)
    }

    private fun findExtendedVoyages(query: VoyageQuery): List<ExtendedVoyage> {
        return findLimited(query)
            .map { voyage ->
                ExtendedVoyage(
                    voyage = voyage,
                    ship = staticShipInfoService.getShipDetailsByIMO(voyage.imo)
                )
            }
            .toList()
    }

    private fun findLimited(query: VoyageQuery): List<Voyage> {
        // limit the limit to protect against a too large query that would result in the server running out of memory
        val limitedQuery = query.copy(
            limit = query.limit.coerceAtMost(maxEntries)
        )

        val visits = voyageService.find(limitedQuery).toList()

        throwWhenThresholdReached(visits.size, maxEntries)

        return visits
    }
}
