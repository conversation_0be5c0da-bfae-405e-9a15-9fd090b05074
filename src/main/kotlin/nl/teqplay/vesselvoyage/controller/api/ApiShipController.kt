package nl.teqplay.vesselvoyage.controller.api

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.auth.Operation
import nl.teqplay.vesselvoyage.auth.Resource
import nl.teqplay.vesselvoyage.controller.annotation.IsoDate
import nl.teqplay.vesselvoyage.datasource.VisitDataSource
import nl.teqplay.vesselvoyage.datasource.VoyageDataSource
import nl.teqplay.vesselvoyage.model.Entry
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.model.Voyage
import nl.teqplay.vesselvoyage.properties.ControllerLimitProperties
import nl.teqplay.vesselvoyage.util.isEven
import nl.teqplay.vesselvoyage.util.joinToStringTruncated
import nl.teqplay.vesselvoyage.util.throwWhenThresholdReached
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.ZonedDateTime

@ProfileApi
@RestController
@RequestMapping(
    path = ["/v1/ships"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ApiShipController(
    properties: ControllerLimitProperties,
    private val visitDataSource: VisitDataSource,
    private val voyageDataSource: VoyageDataSource
) {
    private val log = KotlinLogging.logger {}

    private val maxEntries = properties.maxEntries

    private fun List<Entry>.sortedByStartAndEndTime(descending: Boolean = false): List<Entry> {
        val now = ZonedDateTime.now()
        val comparator = if (descending) {
            compareByDescending<Entry> { it.startTime }.thenByDescending { it.endTime ?: now }
        } else {
            compareBy<Entry> { it.startTime }.thenBy { it.endTime ?: now }
        }

        return this.sortedWith(comparator)
    }

    @GetMapping("/{imo}/recent")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findRecentByIMO(
        @PathVariable imo: String,
        @RequestParam limit: Int,
    ): List<Entry> {
        log.debug { "Find the most recent $limit visits and voyages for ship with IMO $imo" }

        val halfLimit = if (isEven(limit)) {
            limit / 2
        } else {
            limit / 2 + 1
        }

        // visits and voyages alternate, so we only have to fetch half of the requested limit
        val recentVisits = visitDataSource.findRecentByIMO(imo, halfLimit)
        val recentVoyages = voyageDataSource.findRecentByIMO(imo, halfLimit)

        return (recentVisits.toList() + recentVoyages.toList())
            .sortedByStartAndEndTime(descending = true)
            .take(limit)
            .sortedByStartAndEndTime()
    }

    @PostMapping("/recent")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findRecentByIMOs(
        @RequestBody imos: List<String>,
        @RequestParam limit: Int,
    ): List<Entry> {
        log.debug {
            "Find the most recent $limit visits and voyages for ships " +
                "with IMO's ${imos.joinToStringTruncated(limit = 10)}"
        }

        // TODO: do we need to optimize this by doing a single request to mongo?
        return imos.flatMap { findRecentByIMO(it, limit) }
    }

    @GetMapping("/{imo}/history")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findHistoryByIMO(
        @PathVariable imo: String,
        @RequestParam(required = true) @IsoDate start: ZonedDateTime,
        @RequestParam(required = true) @IsoDate end: ZonedDateTime
    ) = findHistoryByIMOs(listOf(imo), start, end)

    @PostMapping("/history")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findHistoryByIMOs(
        @RequestBody imos: List<String>,
        @RequestParam(required = true) @IsoDate start: ZonedDateTime,
        @RequestParam(required = true) @IsoDate end: ZonedDateTime
    ): List<Entry> {
        log.debug {
            "Find history for ${imos.size} ships " +
                "with IMO's ${imos.joinToStringTruncated(limit = 10)}, start=$start, end=$end"
        }

        val visits = visitDataSource.findByIMOs(imos, start, end, maxEntries).toList()
        val voyages = voyageDataSource.findByIMOs(imos, start, end, maxEntries).toList()

        throwWhenThresholdReached(visits.size + voyages.size, maxEntries)

        // First add voyages to ensure a 0-second voyage is placed before a visit with the same timestamp.
        return (voyages + visits).sortedByStartAndEndTime()
    }

    @GetMapping("/{imo}/history/visits")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findVisitsByIMO(
        @PathVariable imo: String,
        @RequestParam(required = true) @IsoDate start: ZonedDateTime,
        @RequestParam(required = true) @IsoDate end: ZonedDateTime
    ) = findVisitsByIMOs(listOf(imo), start, end)

    @GetMapping("/{imo}/history/voyages")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findVoyagesByIMO(
        @PathVariable imo: String,
        @RequestParam(required = true) @IsoDate start: ZonedDateTime,
        @RequestParam(required = true) @IsoDate end: ZonedDateTime
    ) = findVoyagesByIMOs(listOf(imo), start, end)

    @PostMapping("/history/visits")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findVisitsByIMOs(
        @RequestBody imos: List<String>,
        @RequestParam(required = true) @IsoDate start: ZonedDateTime,
        @RequestParam(required = true) @IsoDate end: ZonedDateTime
    ): List<Visit> {
        log.debug {
            "Find visits for ${imos.size} ships " +
                "with IMO's ${imos.joinToStringTruncated(limit = 10)}, start=$start, end=$end"
        }

        val visits = visitDataSource.findByIMOs(imos, start, end, maxEntries).toList()

        throwWhenThresholdReached(visits.size, maxEntries)

        return visits
    }

    @PostMapping("/history/voyages")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findVoyagesByIMOs(
        @RequestBody imos: List<String>,
        @RequestParam(required = true) @IsoDate start: ZonedDateTime,
        @RequestParam(required = true) @IsoDate end: ZonedDateTime
    ): List<Voyage> {
        log.debug {
            "Find voyages for ${imos.size} ships " +
                "with IMO's ${imos.joinToStringTruncated(limit = 10)}, start=$start, end=$end"
        }

        val voyages = voyageDataSource.findByIMOs(imos, start, end, maxEntries).toList()

        throwWhenThresholdReached(voyages.size, maxEntries)

        return voyages
    }
}
