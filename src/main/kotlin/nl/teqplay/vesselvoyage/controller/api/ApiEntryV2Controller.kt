package nl.teqplay.vesselvoyage.controller.api

import nl.teqplay.skeleton.common.exception.BadRequestException
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.EntryByImoRequest
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.EntryByImoResponse
import nl.teqplay.vesselvoyage.auth.Operation
import nl.teqplay.vesselvoyage.auth.Resource
import nl.teqplay.vesselvoyage.mapper.EntryV2Mapper
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter
import nl.teqplay.vesselvoyage.service.api.EntryV2Service
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.CrossOrigin
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.Instant

@CrossOrigin
@ProfileApi
@RestController
@RequestMapping(
    path = ["/v2/entry"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ApiEntryV2Controller(
    private val entryV2Service: EntryV2Service,
    private val mapper: EntryV2Mapper
) {

    @GetMapping("/{entryId}")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findById(@PathVariable entryId: String) = entryV2Service
        .findEntry(entryId)
        ?.mapToApi()

    @PostMapping("/ids")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findByIds(@RequestBody entryIds: List<String>) = entryV2Service
        .findEntries(entryIds.toSet())
        .mapToApi()

    @GetMapping("/byImo/{imo}")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findByImo(
        @PathVariable imo: Int,
        @RequestParam("start") start: Instant?,
        @RequestParam("end") end: Instant?,
        @RequestParam("last") last: Int?,
        @RequestParam("finished") finished: Boolean?,
        @RequestParam("confirmed") confirmed: Boolean?
    ): EntryByImoResponse {
        return findByImo(EntryByImoRequest(imo, start, end, last, finished, confirmed))
    }

    @PostMapping("/byImo")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findByImoBatch(
        @RequestBody requests: List<EntryByImoRequest>
    ): List<EntryByImoResponse> {
        return requests.map { request -> findByImo(request) }
    }

    private fun findByImo(request: EntryByImoRequest): EntryByImoResponse {
        val (imo: Int, start: Instant?, end: Instant?, last: Int?) = request
        val finishedState = NewEntryFinishedFilter.ofBoolean(request.finished)
        val data = if (start != null && end != null) {
            if (start > end) {
                throw BadRequestException("Expected value 'start' is bigger than 'end'")
            }
            entryV2Service.findByImoAndTimeRange(imo, start, end, finishedState, request.confirmed)
        } else if (last != null) {
            if (last < 1) {
                throw BadRequestException("Expected value >= 1 for parameter 'last'")
            }
            entryV2Service.findLastByImo(imo, last, finishedState, request.confirmed)
        } else {
            throw BadRequestException("Missing required parameters. Choose 'start'+'end' or 'last'")
        }
        return EntryByImoResponse(request, data.mapToApi())
    }

    private fun NewEntry.mapToApi() = mapper.toApi(this)
    private fun List<NewEntry>.mapToApi() = map { mapper.toApi(it) }
}
