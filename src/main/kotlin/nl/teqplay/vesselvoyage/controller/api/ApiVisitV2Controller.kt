package nl.teqplay.vesselvoyage.controller.api

import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2
import nl.teqplay.skeleton.common.exception.BadRequestException
import nl.teqplay.skeleton.common.exception.NotFoundException
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.apiv2.model.Visit
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.VisitByImoRequest
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.VisitByImoResponse
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.VisitByPortRequest
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.VisitByPortResponse
import nl.teqplay.vesselvoyage.auth.Operation
import nl.teqplay.vesselvoyage.auth.Resource
import nl.teqplay.vesselvoyage.datasource.NewVisitDataSource
import nl.teqplay.vesselvoyage.mapper.EntryV2Mapper
import nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.service.StaticShipInfoService
import nl.teqplay.vesselvoyage.service.api.VisitV2Service
import nl.teqplay.vesselvoyage.util.validateDwtTeu
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.CrossOrigin
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.Instant

@CrossOrigin
@ProfileApi
@RestController
@RequestMapping(
    path = ["/v2/visit"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class ApiVisitV2Controller(
    private val service: VisitV2Service,
    private val mapper: EntryV2Mapper,
    private val shipService: StaticShipInfoService
) : BaseApiV2Controller<Visit, NewVisit, NewVisitDataSource, VisitV2Service>(service, shipService) {

    @GetMapping("/{id}")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findById(@PathVariable id: String) = service
        .findById(id)
        ?.mapToApi()
        ?: throw NotFoundException("A visit with the given id does not exist")

    @PostMapping("/ids")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findByIds(@RequestBody ids: List<String>) = service
        .findByIds(ids.toSet())
        .mapToApi()

    @GetMapping("/byImo/{imo}")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findByImo(
        @PathVariable imo: Int,
        @RequestParam("start") start: Instant?,
        @RequestParam("end") end: Instant?,
        @RequestParam("last") last: Int?,
        @RequestParam("finished") finished: Boolean?,
        @RequestParam("confirmed") confirmed: Boolean?
    ): VisitByImoResponse {
        val request = VisitByImoRequest(
            imo = imo,
            start = start,
            end = end,
            last = last,
            finished = finished,
            confirmed = confirmed
        )
        return findByImo(request)
    }

    @PostMapping("/byImo")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findByImoBatch(
        @RequestBody requests: List<VisitByImoRequest>
    ): List<VisitByImoResponse> {
        return requests.map { request -> findByImo(request) }
    }

    @GetMapping("/byPort")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findByPort(
        @RequestParam("unlocode") unlocode: String?,
        @RequestParam("areaId") areaId: String?,
        @RequestParam("aisTrueDestination") aisTrueDestination: String?,
        @RequestParam("start") start: Instant?,
        @RequestParam("end") end: Instant?,
        @RequestParam("limit") limit: Int?,
        @RequestParam("categories") categories: Set<ShipCategoryV2>?,
        @RequestParam("finished") finished: Boolean?,
        @RequestParam("confirmed") confirmed: Boolean?,
        @RequestParam("minDwt") minDwt: Int?,
        @RequestParam("maxDwt") maxDwt: Int?,
        @RequestParam("minTeu") minTeu: Int?,
        @RequestParam("maxTeu") maxTeu: Int?,
    ): VisitByPortResponse {
        val request = VisitByPortRequest(
            start = start,
            end = end,
            unlocode = unlocode,
            areaId = areaId,
            aisTrueDestination = aisTrueDestination,
            categories = categories,
            finished = finished,
            confirmed = confirmed,
            minDwt = minDwt,
            maxDwt = maxDwt,
            minTeu = minTeu,
            maxTeu = maxTeu,
        )
        return findByPort(request)
    }

    @PostMapping("/byPort")
    @PreAuthorize("hasPermission(null,'${Resource.SHIP}:${Operation.READ}')")
    fun findByPortBatch(
        @RequestBody requests: List<VisitByPortRequest>
    ): List<VisitByPortResponse> {
        return requests.map { request -> findByPort(request) }
    }

    private fun findByImo(request: VisitByImoRequest): VisitByImoResponse {
        val data = with(request) {
            findByImoBase(
                imo = imo,
                start = start,
                end = end,
                last = last,
                finished = finished,
                confirmed = confirmed
            )
        }
        return VisitByImoResponse(
            request = request,
            data = data.mapToApi()
        )
    }

    private fun findByPort(request: VisitByPortRequest): VisitByPortResponse {
        validateDwtTeu(request)
        val qualifyingImos = selectImosOrEmpty(request)
        val unlocode = request.unlocode
        val areaId = request.areaId
        val finishedState = NewEntryFinishedFilter.ofBoolean(request.finished)
        val visits = when {
            unlocode != null && areaId != null -> throw BadRequestException("Provide unlocode or areaId, not both")
            unlocode != null -> service.findByPortUnlocode(
                unlocode = unlocode,
                start = request.start,
                end = request.end,
                finishedState = finishedState,
                confirmed = request.confirmed,
                aisTrueDestination = request.aisTrueDestination,
                qualifyingImos = qualifyingImos
            )
            areaId != null -> service.findByPortAreaId(
                pomaId = areaId,
                start = request.start,
                end = request.end,
                finishedState = finishedState,
                confirmed = request.confirmed,
                aisTrueDestination = request.aisTrueDestination,
                qualifyingImos = qualifyingImos
            )
            else -> throw BadRequestException("Provide unlocode or areaId")
        }
        return VisitByPortResponse(
            request = request,
            data = visits.mapToApi()
        )
    }

    private fun NewVisit.mapToApi(): Visit = mapper.toApi(this)
    private fun List<NewVisit>.mapToApi(): List<Visit> = map { visit -> mapper.toApi(visit) }
}
