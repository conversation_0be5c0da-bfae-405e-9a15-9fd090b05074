package nl.teqplay.vesselvoyage.auth

import nl.teqplay.skeleton.auth.credentials.permissions.PermissionMap.Companion.permissions
import nl.teqplay.vesselvoyage.auth.Resource.ANCHORAGE
import nl.teqplay.vesselvoyage.auth.Resource.BERTH
import nl.teqplay.vesselvoyage.auth.Resource.EVENT
import nl.teqplay.vesselvoyage.auth.Resource.PORT
import nl.teqplay.vesselvoyage.auth.Resource.RECALCULATION
import nl.teqplay.vesselvoyage.auth.Resource.SHIP
import nl.teqplay.vesselvoyage.auth.Resource.USER_PROFILE
import nl.teqplay.vesselvoyage.auth.Resource.VALIDATE

object Resource {
    const val EVENT = "event"
    const val SHIP = "ship"
    const val PORT = "port"
    const val BERTH = "berth"
    const val ANCHORAGE = "anchorage"
    const val USER_PROFILE = "user-profile"
    const val RECALCULATION = "recalculation"
    const val VALIDATE = "validate"
}

object Operation {
    const val READ = "read"
    const val CREATE = "create"
    const val UPDATE = "update"
    const val DELETE = "delete"
}

object Role {
    const val USER = "ROLE_USER"
    const val DEVELOPER = "ROLE_DEVELOPER"
    const val REVENTS = "ROLE_REVENTS"

    val permissionMap by lazy {
        permissions {
            resource(EVENT) {
                create += DEVELOPER
                read += DEVELOPER or REVENTS
                update += DEVELOPER
                delete += DEVELOPER
            }

            resource(SHIP) {
                read += DEVELOPER or USER
                update += DEVELOPER
            }

            resource(PORT) {
                read += DEVELOPER or USER
                update += DEVELOPER
            }

            resource(ANCHORAGE) {
                read += DEVELOPER or USER
            }

            resource(BERTH) {
                read += DEVELOPER or USER
            }

            resource(USER_PROFILE) {
                read += DEVELOPER or USER
            }

            resource(RECALCULATION) {
                read += DEVELOPER or USER or REVENTS
                create += DEVELOPER or REVENTS
                delete += DEVELOPER
            }

            resource(VALIDATE) {
                read += DEVELOPER
            }
        }
    }
}
