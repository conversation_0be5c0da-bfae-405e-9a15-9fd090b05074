package nl.teqplay.vesselvoyage.properties

import org.springframework.boot.context.properties.ConfigurationProperties
import java.time.Duration

@ConfigurationProperties(prefix = "automatic-recalculation")
data class AutomaticRecalculationProperties(
    val enabled: Boolean,

    /**
     * Indicate if we have to run a pre-recalculation step using event history to ensure the data is mergable.
     */
    val eventHistoryPreRecalculationEnabled: Boolean,

    /**
     * The amount of ships that can be recalculated at the same time.
     */
    val batchSize: Int,

    /**
     * When the automatic recalculation triggers.
     */
    val cron: String,

    /**
     * How long ago voyage needs to happen to mark a ship as recalculatable.
     */
    val readyOffset: Duration,

    /**
     * How often in a fixed rate we have to refresh all ships that need to be recalculated.
     */
    val shipRefreshInterval: Duration
)
