package nl.teqplay.vesselvoyage.properties

import org.springframework.boot.context.properties.ConfigurationProperties
import java.time.Duration

@ConfigurationProperties(prefix = "recalculation")
data class RecalculationProperties(
    val enabled: Boolean,
    val recalculateShipInterval: Duration,
    val snapshotEnabled: <PERSON><PERSON>an,
    val snapshotAge: Duration,
    val refreshRecalculableShipsCron: String,
    val threadPoolSize: Int,
    val maxMmsiCount: Int,
    val reventsRetryEnabled: Boolean
)
