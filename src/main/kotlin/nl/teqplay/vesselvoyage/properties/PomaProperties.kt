package nl.teqplay.vesselvoyage.properties

import nl.teqplay.skeleton.common.config.Auth0S2SClientProperties
import org.springframework.boot.context.properties.ConfigurationProperties

@Deprecated("Use the Keycloak variant instead of Auth0")
@ConfigurationProperties(prefix = "poma")
data class PomaProperties(
    override val url: String,
    override val domain: String?,
    override val clientId: String,
    override val clientSecret: String,
    override val audience: String,
) : Auth0S2SClientProperties(url, domain, clientId, clientSecret, audience)
