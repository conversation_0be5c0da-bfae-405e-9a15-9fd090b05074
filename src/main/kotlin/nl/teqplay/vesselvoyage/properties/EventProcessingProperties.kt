package nl.teqplay.vesselvoyage.properties

import org.springframework.boot.context.properties.ConfigurationProperties
import java.time.Duration

@ConfigurationProperties(prefix = "event-processing")
data class EventProcessingProperties(
    val maxSpeedMps: Double,
    val minDuration: Duration,
    val enableTraceCalculations: <PERSON><PERSON>an,
    val enableSlowMovingPeriods: <PERSON><PERSON>an,
    val newStopDetection: <PERSON><PERSON><PERSON>,

    /**
     * Indicate that the V1 definition processing is enabled.
     * Note that disabling this will result in projects that rely on V1 to not get any more data.
     */
    val enableOldDefinition: <PERSON>olean,

    /**
     * Indicate that the V2 definition processing is enabled.
     */
    val enableNewDefinition: Boolean,

    /**
     * Total amount of threads used when consuming events.
     */
    val totalThreads: Int,

    /**
     * Whether to log the event and information on decisions for modifying entries
     */
    val logResults: Boolean,

    val activenessMonitoring: ActivenessMonitoring
) {
    /**
     * Configuration for monitoring the activeness of the event processing.
     */
    data class ActivenessMonitoring(
        /**
         * Whether the activeness monitoring is enabled.
         */
        val enabled: <PERSON><PERSON><PERSON>,

        /**
         * Interval in ms how often the throughput activeness is checked.
         */
        val interval: Int,

        /**
         * Maximum time without any event received before the consumer is recreated.
         */
        val maxIdleTime: Duration
    )
}
