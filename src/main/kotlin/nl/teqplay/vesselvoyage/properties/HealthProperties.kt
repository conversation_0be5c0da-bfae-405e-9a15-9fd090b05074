package nl.teqplay.vesselvoyage.properties

import org.springframework.boot.context.properties.ConfigurationProperties
import java.time.Duration

@ConfigurationProperties(prefix = "health")
data class HealthProperties(
    val slack: Slack,
    val teqplayEventsQueue: EventsQueue,
    val aisStreamingQueue: AisQueue
) {
    data class Slack(
        val webhook: String
    )

    data class EventsQueue(
        val maxTimeBetweenEvents: Duration
    )

    data class AisQueue(
        val maxTimeBetweenEvents: Duration
    )
}
