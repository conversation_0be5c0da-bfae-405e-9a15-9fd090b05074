package nl.teqplay.vesselvoyage.properties

import org.springframework.boot.context.properties.ConfigurationProperties
import java.time.Duration

@ConfigurationProperties(prefix = "trace")
data class TraceProperties(
    val tolerance: Double,
    val ongoing: Ongoing,
    val historic: Historic,

    /**
     * Indicate that the V1 definition processing is enabled.
     * Note that disabling this will result in old traces to not be generated.
     */
    val enableOldDefinition: Boolean,

    /**
     * Indicate that the V2 definition processing is enabled.
     */
    val enableNewDefinition: Boolean,

    val totalThreads: Int,

    /**
     * The maximum length in days for a trace request. This also affects chunks of requests, where the full time
     * window should not exceed this duration.
     */
    val maxRequestLengthDays: Int,

    val activenessMonitoring: ActivenessMonitoring
) {
    /**
     * Configuration for monitoring the activeness of the AIS processing.
     */
    data class ActivenessMonitoring(
        /**
         * Whether the activeness monitoring is enabled.
         */
        val enabled: Boolean,

        /**
         * Interval in ms how often the throughput activeness is checked.
         */
        val interval: Int,

        /**
         * Maximum time without any AIS received before the consumer is recreated.
         */
        val maxIdleTime: Duration
    )

    data class Ongoing(
        val maxAge: Duration,
        val maxNonSimplifiedSize: Int,
        val maxSize: Int,
        val persistInterval: String
    )

    data class Historic(
        val generateWhenFinished: Boolean,
        val finished: Finished,
        val unfinished: Unfinished
    ) {
        data class Finished(
            val createFromAis: Boolean
        )

        data class Unfinished(
            val createFromAis: Boolean
        )
    }
}
