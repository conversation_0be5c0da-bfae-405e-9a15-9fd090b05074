package nl.teqplay.vesselvoyage.properties

import nl.teqplay.skeleton.common.config.NatsClientConfiguration
import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = "event-publishing")
data class EventPublishingProperties(
    val enabled: <PERSON>olean,
    val nats: Nats,
    val rabbitMq: RabbitMQ,
    val enabledPublishers: Set<Publishers>
) {
    data class Nats(
        override val enabled: Boolean,
        override val url: String,
        override val username: String,
        override val password: String,
    ) : NatsClientConfiguration

    data class RabbitMQ(
        val enabled: Boolean,
        val uri: String,
        val exchange: String,

        @Deprecated("Should not publish anything of V1 in the future")
        val v1Exchange: String
    )

    enum class Publishers {
        ENTRIES,
        STATEMENT_OF_FACTS
    }
}
