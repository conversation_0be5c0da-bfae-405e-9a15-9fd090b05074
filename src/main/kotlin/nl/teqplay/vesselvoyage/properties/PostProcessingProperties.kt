package nl.teqplay.vesselvoyage.properties

import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = "post-processing")
class PostProcessingProperties(
    val enabled: Boolean,

    /**
     * Total amount of threads used when doing post-processing.
     */
    val totalThreads: Int,

    /**
     * Interval in ms how much we want to delay each time after we finished running the post-processing logic for each batch.
     */
    val interval: Int,

    /**
     * When enabled only run post-processing on the Visits and skip Voyages.
     */
    val onlyVisits: <PERSON><PERSON>an
)
