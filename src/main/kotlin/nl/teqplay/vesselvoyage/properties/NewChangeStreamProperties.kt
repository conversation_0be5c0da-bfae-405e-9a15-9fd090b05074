package nl.teqplay.vesselvoyage.properties

import nl.teqplay.skeleton.common.config.NatsClientConfiguration
import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = "nats.change-stream")
data class NewChangeStreamProperties(
    override val enabled: <PERSON><PERSON><PERSON>,
    override val url: String,
    override val username: String,
    override val password: String
) : NatsClientConfiguration
