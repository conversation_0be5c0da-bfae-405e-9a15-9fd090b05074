package nl.teqplay.vesselvoyage.config

import com.github.benmanes.caffeine.cache.Caffeine
import com.github.benmanes.caffeine.cache.Ticker
import nl.teqplay.vesselvoyage.config.CacheNames.POMA_BERTHS
import nl.teqplay.vesselvoyage.properties.CacheTtlProperties
import org.springframework.cache.CacheManager
import org.springframework.cache.caffeine.CaffeineCache
import org.springframework.cache.support.SimpleCacheManager
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.Duration

object CacheNames {
    const val POMA_BERTHS = "POMA_BERTHS"
}

@Configuration
class CacheConfiguration {
    @Bean
    fun cacheManager(properties: CacheTtlProperties, ticker: Ticker): CacheManager =
        SimpleCacheManager().apply {
            setCaches(
                listOf(
                    buildCache(POMA_BERTHS, ticker, properties.pomaBerths)
                )
            )
        }

    private fun buildCache(name: String, ticker: Ticker, duration: Duration) =
        CaffeineCache(
            name,
            Caffeine.newBuilder()
                .expireAfterWrite(duration)
                .expireAfterAccess(duration)
                .ticker(ticker)
                .build()
        )

    @Bean
    fun ticker(): Ticker = Ticker.systemTicker()
}
