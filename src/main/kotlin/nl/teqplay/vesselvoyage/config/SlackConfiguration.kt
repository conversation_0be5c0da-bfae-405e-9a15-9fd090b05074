package nl.teqplay.vesselvoyage.config

import com.github.seratch.jslack.Slack
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.properties.HealthProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@ProfileProcessing
@Configuration
class SlackConfiguration(
    private val properties: HealthProperties
) {
    @Bean
    fun slackClient(): Slack? {
        return if (properties.slack.webhook.isNotBlank()) {
            Slack.getInstance()
        } else {
            null
        }
    }
}
