package nl.teqplay.vesselvoyage.config

import com.fasterxml.jackson.databind.ObjectMapper
import io.nats.client.api.RetentionPolicy
import nl.teqplay.skeleton.nats.NatsClientBuilder
import nl.teqplay.skeleton.nats.NatsProducerStream
import nl.teqplay.vesselvoyage.annotation.ProfileRevents
import nl.teqplay.vesselvoyage.model.v2.NewChange
import nl.teqplay.vesselvoyage.properties.NewChangeStreamProperties
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.Duration

@Configuration
@ProfileRevents
@ConditionalOnProperty(prefix = "nats.change-stream", name = ["enabled"], havingValue = "true")
class ReventsConfiguration(
    private val objectMapper: ObjectMapper,
    private val newChangeStreamProperties: NewChangeStreamProperties,
    private val natsClientBuilder: NatsClientBuilder,
) {
    @Bean
    fun changeProducer(): NatsProducerStream<NewChange<*>>? {
        return natsClientBuilder.producerStream(
            config = newChangeStreamProperties,
            stream = "vesselvoyage:change",
            subjects = listOf("vesselvoyage.change.>"),
            serializer = objectMapper::writeValueAsBytes,
            maxAge = Duration.ofHours(1),
            storeOnDisk = false,
            retentionPolicy = RetentionPolicy.Interest
        )
    }
}
