package nl.teqplay.vesselvoyage.config

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.annotation.ProfileRevents
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@ProfileApi
@ProfileProcessing
@ProfileRevents
@Configuration
class CoroutineConfiguration {
    @Bean
    fun applicationScope(): CoroutineScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
}
