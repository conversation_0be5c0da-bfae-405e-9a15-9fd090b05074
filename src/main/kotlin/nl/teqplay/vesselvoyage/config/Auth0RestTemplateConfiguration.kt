package nl.teqplay.vesselvoyage.config

import nl.teqplay.skeleton.common.config.AuthCredentialsAuth0Properties
import nl.teqplay.skeleton.common.logging.OutgoingRequestLogger
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.properties.Auth0Properties
import org.springframework.beans.factory.ObjectProvider
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.web.client.RestTemplate
import java.lang.annotation.Inherited

@ProfileProcessing
@Configuration
class Auth0RestTemplateConfiguration {

    /** Set up the template and how it handles requests */
    @Bean(AUTH0_REST_TEMPLATE)
    fun auth0RestTemplate(
        restTemplateBuilder: RestTemplateBuilder,
        properties: Auth0Properties,
        test: AuthCredentialsAuth0Properties,
        outgoingRequestLoggerProvider: ObjectProvider<OutgoingRequestLogger>
    ):
        RestTemplate = restTemplateBuilder.rootUri(properties.url)
        .build()
        .apply {
            outgoingRequestLoggerProvider.ifAvailable?.let { interceptors.add(it) }
        }
}

/** Standard name for the template */
const val AUTH0_REST_TEMPLATE = "auth0RestTemplate"

/** Allow the template to be used as an annotation */
@Target(AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
@MustBeDocumented
@Inherited
@Qualifier(AUTH0_REST_TEMPLATE)
annotation class Auth0RestTemplate
