package nl.teqplay.vesselvoyage.config

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.skeleton.auth.credentials.AUTH_PATHS
import nl.teqplay.skeleton.auth.credentials.AuthorizationConfigurer
import nl.teqplay.skeleton.auth.credentials.SWAGGER_PATHS
import nl.teqplay.skeleton.common.exception.UnauthorizedException
import nl.teqplay.vesselvoyage.auth.Role
import nl.teqplay.vesselvoyage.util.asUser
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.security.access.PermissionEvaluator
import org.springframework.security.core.Authentication
import java.io.Serializable

@Configuration
class AuthorizationConfiguration {
    @Bean
    fun authorizationConfigurer(): AuthorizationConfigurer =
        AuthorizationConfigurer(
            AUTH_PATHS + SWAGGER_PATHS + listOf(
                "/actuator/health",
                "/actuator/prometheus",
                "/actuator/atlas",
            )
        )

    @Bean
    fun permissionEvaluator(): PermissionEvaluator = CustomPermissionEvaluator()
}

/** Used by Spring to evaluate `hasPermission(..)` in SPEL constructs. */
class CustomPermissionEvaluator : PermissionEvaluator {
    private val logger = KotlinLogging.logger { }

    override fun hasPermission(authentication: Authentication?, targetDomainObject: Any?, permission: Any?): Boolean {
        val user = authentication?.asUser() ?: throw UnauthorizedException("action requires login")
        if (permission == null) return false

        return user.roles
            .any { role ->
                Role.permissionMap.hasPermission(role, permission.toString())
            } // check permissions for the role
            .also { granted ->
                if (granted) {
                    logger.trace { "hasPermission($permission) for $user >> $granted" }
                } else {
                    logger.info { "hasPermission($permission) for $user >> $granted" }
                }
            }
    }

    override fun hasPermission(
        authentication: Authentication?,
        targetId: Serializable?,
        targetType: String?,
        permission: Any?
    ): Boolean = false
}
