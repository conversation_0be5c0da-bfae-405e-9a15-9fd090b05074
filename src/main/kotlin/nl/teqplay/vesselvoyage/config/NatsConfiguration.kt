package nl.teqplay.vesselvoyage.config

import com.fasterxml.jackson.databind.ObjectMapper
import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.skeleton.nats.NatsClientBuilder
import nl.teqplay.skeleton.nats.NatsClientHealthBuilder
import nl.teqplay.skeleton.nats.NatsProducerStream
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.apiv2.model.OutgoingChange
import nl.teqplay.vesselvoyage.properties.EventPublishingProperties
import org.springframework.boot.actuate.health.HealthIndicator
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.Duration

@ProfileProcessing
@ConditionalOnProperty("event-publishing.enabled", "event-publishing.nats.enabled", havingValue = "true")
@Configuration
class NatsConfiguration(
    private val eventPublishingProperties: EventPublishingProperties,
    private val natsClientBuilder: <PERSON>sClientBuilder,
    private val natsClientHealthBuilder: NatsClientHealthBuilder,
    private val objectMapper: ObjectMapper
) {
    companion object {
        private const val STREAM_BASE = "vessel-voyage"
        const val OUTGOING_CHANGES_STREAM = STREAM_BASE
        const val OUTGOING_CHANGES_SUBJECT = "$STREAM_BASE.>"
    }

    private val log = KotlinLogging.logger {}

    @Bean
    fun natsChangeProducerStream(): NatsProducerStream<OutgoingChange<*>>? {
        log.info { "Creating outgoing changes producer" }
        if (eventPublishingProperties.enabledPublishers.isEmpty()) {
            // Nothing is published so there is no need to make a Nats producer stream
            return null
        }

        return natsClientBuilder.producerStream(
            config = eventPublishingProperties.nats,
            stream = OUTGOING_CHANGES_STREAM,
            subjects = listOf(OUTGOING_CHANGES_SUBJECT),
            serializer = objectMapper::writeValueAsBytes,
            maxAge = Duration.ofHours(1),
            storeOnDisk = true
        )
    }

    @Bean
    fun natsChangeProducerStreamHealthIndicator(
        stream: NatsProducerStream<OutgoingChange<*>>?
    ): HealthIndicator? {
        return stream?.let { natsClientHealthBuilder.natsClientHealthIndicator(stream) }
    }
}
