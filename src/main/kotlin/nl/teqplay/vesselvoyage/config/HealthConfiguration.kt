package nl.teqplay.vesselvoyage.config

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.skeleton.actuator.DEGRADED
import nl.teqplay.skeleton.actuator.degraded
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.config.BeanNames.RABBIT_SENDER_HEALTH_INDICATOR
import org.springframework.boot.actuate.autoconfigure.health.HealthEndpointProperties
import org.springframework.boot.actuate.endpoint.ApiVersion
import org.springframework.boot.actuate.endpoint.SecurityContext
import org.springframework.boot.actuate.endpoint.web.WebEndpointResponse
import org.springframework.boot.actuate.endpoint.web.WebServerNamespace
import org.springframework.boot.actuate.health.AbstractHealthIndicator
import org.springframework.boot.actuate.health.CompositeHealth
import org.springframework.boot.actuate.health.Health
import org.springframework.boot.actuate.health.HealthComponent
import org.springframework.boot.actuate.health.HealthContributorRegistry
import org.springframework.boot.actuate.health.HealthEndpointGroups
import org.springframework.boot.actuate.health.HealthEndpointWebExtension
import org.springframework.boot.actuate.health.HealthIndicator
import org.springframework.boot.actuate.health.Status
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.Duration

// TODO: the HealthConfiguration is a bit messy, reorganize
@Configuration
class HealthConfiguration {
    companion object {
        val STATUS_DEGRADED = Status(DEGRADED)
    }

    @Bean
    fun healthEndpointWebExtension(
        registry: HealthContributorRegistry,
        groups: HealthEndpointGroups,
        managementProperties: HealthEndpointProperties
    ): HealthEndpointWebExtension {
        return CustomHealthEndpointWebExtension(
            registry = registry,
            groups = groups,
            // Provide the slow indicator property from the spring itself so we have 1 place to manage all
            slowIndicatorThreshold = managementProperties.logging.slowIndicatorThreshold
        )
    }

    @ProfileProcessing
    @ConditionalOnProperty("event-publishing.enabled", "event-publishing.rabbit-mq.enabled", havingValue = "true")
    @Bean(RABBIT_SENDER_HEALTH_INDICATOR)
    fun rabbitHealth(eventHandler: RabbitMqEventSender): HealthIndicator = object : AbstractHealthIndicator() {
        override fun doHealthCheck(builder: Health.Builder) {
            builder.withDetail("host", eventHandler.host)
            when (eventHandler.healthy) {
                true -> builder.up()
                false -> builder.degraded()
                else -> builder.unknown()
            }
        }
    }

    /** Logs health checks when the status is [Status.DOWN] or [STATUS_DEGRADED]. */
    class CustomHealthEndpointWebExtension(
        registry: HealthContributorRegistry,
        groups: HealthEndpointGroups,
        slowIndicatorThreshold: Duration
    ) : HealthEndpointWebExtension(registry, groups, slowIndicatorThreshold) {
        private val logger = KotlinLogging.logger { }
        private var previousStatus = Status.UNKNOWN

        override fun health(
            apiVersion: ApiVersion?,
            serverNamespace: WebServerNamespace?,
            securityContext: SecurityContext?,
            showAll: Boolean,
            vararg path: String?
        ): WebEndpointResponse<HealthComponent> =
            super.health(apiVersion, serverNamespace, securityContext, showAll, *path)
                .also { response ->
                    val health = response.body
                    if ((health.status == STATUS_DEGRADED || health.status == Status.DOWN) && previousStatus != health.status) {
                        logger.warn {
                            "Health status is ${health.status}" +
                                (if (health is CompositeHealth) " ${health.components}" else "") +
                                " (path: $path)"
                        }
                    }
                    previousStatus = health.status
                }
    }
}

object BeanNames {
    const val RABBIT_SENDER_HEALTH_INDICATOR = "rabbitSenderHealthIndicator"
}
