package nl.teqplay.vesselvoyage.config

import nl.teqplay.skeleton.common.logging.OutgoingRequestLogger
import nl.teqplay.vesselvoyage.properties.DriftPredictorProperties
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.web.client.RestTemplate
import java.lang.annotation.Inherited
@Configuration
class DriftPredictorConfiguration {
    @Bean(DRIFT_PREDICTOR_REST_TEMPLATE)
    fun driftPredictorRestTemplate(
        restTemplateBuilder: RestTemplateBuilder,
        driftPredictorProperties: DriftPredictorProperties,
        outgoingRequestLoggerProvider: OutgoingRequestLogger?
    ): RestTemplate {
        return restTemplateBuilder
            .rootUri(driftPredictorProperties.url)
            .build()
            .also { restTemplate ->
                if (outgoingRequestLoggerProvider != null) {
                    restTemplate.interceptors.add(outgoingRequestLoggerProvider)
                }
            }
    }
}

/** Standard name for the template */
const val DRIFT_PREDICTOR_REST_TEMPLATE = "driftPredictorRestTemplate"

/** Allow the template to be used as an annotation */
@Target(AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
@MustBeDocumented
@Inherited
@Qualifier(DRIFT_PREDICTOR_REST_TEMPLATE)
annotation class DriftPredictorRestTemplate
