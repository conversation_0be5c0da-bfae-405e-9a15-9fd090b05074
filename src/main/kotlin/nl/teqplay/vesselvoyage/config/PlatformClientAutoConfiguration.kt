package nl.teqplay.vesselvoyage.config

import nl.teqplay.aisengine.client.annotations.InternalApiRestTemplate
import nl.teqplay.vesselvoyage.client.AisHistoryClient
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.web.client.RestTemplate

@Configuration
class PlatformClientAutoConfiguration {

    @Bean
    fun aisHistoryClient(@InternalApiRestTemplate restTemplate: RestTemplate): AisHistoryClient =
        AisHistoryClient(restTemplate)
}
