package nl.teqplay.vesselvoyage.config

import nl.teqplay.skeleton.auth.credentials.auth0.s2s.client.Auth0S2SClientWrapper
import nl.teqplay.skeleton.auth.credentials.keycloak.s2s.client.KeycloakS2SClientWrapper
import nl.teqplay.skeleton.common.logging.OutgoingRequestLogger
import nl.teqplay.vesselvoyage.properties.KeycloakProperties
import nl.teqplay.vesselvoyage.properties.PomaProperties
import org.springframework.beans.factory.ObjectProvider
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.web.client.RestTemplate
import java.lang.annotation.Inherited

@Configuration
class PomaConfiguration {
    /** Set up the template and how it handles requests */
    @Bean(POMA_REST_TEMPLATE)
    fun pomaRestTemplate(
        keycloakProperties: KeycloakProperties,
        auth0Properties: PomaProperties,
        restTemplateBuilder: RestTemplateBuilder,
        outgoingRequestLoggerProvider: ObjectProvider<OutgoingRequestLogger>
    ): RestTemplate {
        return if (keycloakProperties.enabled) {
            KeycloakS2SClientWrapper.create(
                restTemplateBuilder,
                outgoingRequestLoggerProvider,
                keycloakProperties.poma
            )
        } else {
            Auth0S2SClientWrapper.create(
                restTemplateBuilder,
                outgoingRequestLoggerProvider,
                auth0Properties
            )
        }
    }
}

/** Standard name for the template */
const val POMA_REST_TEMPLATE = "pomaRestTemplate"

/** Allow the template to be used as an annotation */
@Target(AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
@MustBeDocumented
@Inherited
@Qualifier(POMA_REST_TEMPLATE)
annotation class PomaRestTemplate
