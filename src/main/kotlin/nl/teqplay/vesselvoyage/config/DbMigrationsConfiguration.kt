package nl.teqplay.vesselvoyage.config

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.github.cloudyrock.mongock.driver.mongodb.sync.v4.driver.MongoSync4Driver
import com.github.cloudyrock.spring.v5.MongockSpring5
import com.mongodb.ConnectionString
import com.mongodb.MongoClientSettings
import com.mongodb.client.MongoClients
import nl.teqplay.skeleton.common.config.MongoDbConfiguration
import nl.teqplay.skeleton.datasource.ExtendedDateModule
import nl.teqplay.skeleton.datasource.MongoDbBuilder
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.datasource.migrations.Migrations
import org.bson.UuidRepresentation
import org.mongojack.JacksonCodecRegistry
import org.mongojack.internal.MongoJackModule
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression
import org.springframework.context.ApplicationContext
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@ProfileProcessing
@Configuration
class DbMigrationsConfiguration {
    @Bean
    @ConditionalOnExpression("\${spring.mongock.enabled:true}")
    fun mongockRunner(
        context: ApplicationContext,
        objectMapper: ObjectMapper,
        mongoDb: MongoDbConfiguration,
        @Value("\${mongodb.db}") db: String
    ): MongockSpring5.MongockInitializingBeanRunner {
        // Create the Mongock runner by hand. Using the annotation forces the use of Spring Data.
        val mongoJackObjectMapper = objectMapper
            .copy()
            .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .registerModule(ExtendedDateModule())
            .registerModule(JavaTimeModule())
            .registerKotlinModule()
        MongoJackModule.configure(mongoJackObjectMapper)

        val uri = MongoDbBuilder.createMongoUri(mongoDb)
        val settings = MongoClientSettings.builder()
            .codecRegistry(JacksonCodecRegistry(mongoJackObjectMapper, MongoClientSettings.getDefaultCodecRegistry(), UuidRepresentation.STANDARD))
            .applyConnectionString(ConnectionString(uri))
            .build()

        val client = MongoClients.create(settings)

        val driver = MongoSync4Driver.withDefaultLock(client, db).apply {
            disableTransaction() // our mongodb instance does not support transactions
        }
        return MongockSpring5.builder()
            .setDriver(driver)
            .addChangeLogsScanPackage(Migrations::class.java.packageName)
            .setSpringContext(context)
            .buildInitializingBeanRunner()
    }
}
