package nl.teqplay.vesselvoyage.config

import nl.teqplay.skeleton.auth.credentials.auth0.s2s.client.Auth0S2SClientWrapper
import nl.teqplay.skeleton.auth.credentials.keycloak.s2s.client.KeycloakS2SClientWrapper
import nl.teqplay.skeleton.common.logging.OutgoingRequestLogger
import nl.teqplay.vesselvoyage.properties.CsiProperties
import nl.teqplay.vesselvoyage.properties.KeycloakProperties
import org.springframework.beans.factory.ObjectProvider
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.web.client.RestTemplate
import java.lang.annotation.Inherited

@Configuration
class CSIConfiguration {
    /** Set up the template and how it handles requests */
    @Bean(CSI_REST_TEMPLATE)
    fun csiRestTemplate(
        keycloakProperties: KeycloakProperties,
        auth0Properties: CsiProperties,
        restTemplateBuilder: RestTemplateBuilder,
        outgoingRequestLoggerProvider: ObjectProvider<OutgoingRequestLogger>
    ): RestTemplate {
        return if (keycloakProperties.enabled) {
            KeycloakS2SClientWrapper.create(
                restTemplateBuilder,
                outgoingRequestLoggerProvider,
                keycloakProperties.csi
            )
        } else {
            Auth0S2SClientWrapper.create(
                restTemplateBuilder,
                outgoingRequestLoggerProvider,
                auth0Properties
            )
        }
    }
}

/** Standard name for the template */
const val CSI_REST_TEMPLATE = "csiRestTemplate"

/** Allow the template to be used as an annotation */
@Target(AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
@MustBeDocumented
@Inherited
@Qualifier(CSI_REST_TEMPLATE)
annotation class CSIRestTemplate
