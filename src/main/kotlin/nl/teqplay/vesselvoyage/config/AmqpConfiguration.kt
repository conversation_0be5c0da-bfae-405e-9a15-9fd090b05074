package nl.teqplay.vesselvoyage.config

import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.properties.EventPublishingProperties
import org.springframework.amqp.rabbit.annotation.EnableRabbit
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

/**
 * Register beans for connecting to the AMQP queue.
 * This queue is used to receive port events from the platform
 */
@ProfileProcessing
@ConditionalOnProperty("event-publishing.enabled", "event-publishing.rabbit-mq.enabled", havingValue = "true")
@Configuration
@EnableRabbit
class AmqpConfiguration {
    @Bean
    fun rabbitMqEventSender(properties: EventPublishingProperties) = RabbitMqEventSender(properties.rabbitMq.uri)
        .also { it.ensureChannelOpened() }
}
