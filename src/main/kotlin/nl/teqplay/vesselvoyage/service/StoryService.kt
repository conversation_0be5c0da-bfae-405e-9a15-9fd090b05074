package nl.teqplay.vesselvoyage.service

import nl.teqplay.skeleton.model.Location
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.model.internal.InfraAreaType
import nl.teqplay.vesselvoyage.model.internal.ShipStory
import nl.teqplay.vesselvoyage.model.lightweight.poma.PomaModel
import nl.teqplay.vesselvoyage.model.v2.AreaActivity
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter
import nl.teqplay.vesselvoyage.model.v2.NewStop
import nl.teqplay.vesselvoyage.model.v2.NewStopType
import nl.teqplay.vesselvoyage.model.v2.NewTrace
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import nl.teqplay.vesselvoyage.service.api.EsofV2Service
import nl.teqplay.vesselvoyage.service.api.VisitV2Service
import nl.teqplay.vesselvoyage.service.api.VoyageV2Service
import nl.teqplay.vesselvoyage.service.trace.ProcessingTraceService
import nl.teqplay.vesselvoyage.util.sortedByStartAndEndTime
import org.springframework.stereotype.Service
import java.time.Instant
import java.time.ZoneOffset

@ProfileProcessing
@Service
class StoryService(
    private val eventService: EventFetchingService,
    private val entryProcessingService: EntryProcessingService,
    private val infraService: InfraService,
    private val processingTraceService: ProcessingTraceService,
    private val visitService: VisitV2Service,
    private val voyageService: VoyageV2Service,
    private val esofService: EsofV2Service,
    private val staticShipInfoService: StaticShipInfoService
) {
    fun getStory(
        imo: Int,
        start: Instant,
        end: Instant?
    ): ShipStory {
        val visits = if (end != null) {
            visitService.findByImoAndTimeRange(
                imo = imo,
                start = start,
                end = end,
                finishedState = NewEntryFinishedFilter.ANY,
                confirmed = null
            )
        } else {
            visitService.findByImoStartingAtOrAfter(
                imo = imo,
                start = start,
                finishedState = NewEntryFinishedFilter.ANY,
                confirmed = null
            )
        }
        val voyages = if (end != null) {
            voyageService.findByImoAndTimeRange(
                imo = imo,
                start = start,
                end = end,
                finishedState = NewEntryFinishedFilter.ANY,
                confirmed = null
            )
        } else {
            voyageService.findByImoStartingAtOrAfter(
                imo = imo,
                start = start,
                finishedState = NewEntryFinishedFilter.ANY,
                confirmed = null
            )
        }

        val allEntries = (visits + voyages).sortedByStartAndEndTime()
        val pomaEntityIds = mutableSetOf<Pair<String, InfraAreaType>>()

        // Go parallel over all entries as it can take quite a while when having to calculate traces
        val storyItems = allEntries.parallelStream()
            .map { entry ->
                pomaEntityIds.addAll(entry.getAllPomaEntitiesIds())
                getStoryItem(entry = entry)
            }
            .toList()

        return ShipStory(
            imo = imo,
            items = storyItems,
            pomaEntities = getPomaEntitiesForShipStory(pomaEntityIds)
        )
    }

    private fun getStoryItem(
        entry: NewEntry
    ): ShipStory.ShipStoryItem {
        val esof = esofService.findById(entry._id)
        val trace = processingTraceService.getTraceById(
            entryId = entry._id,
            scheduleTraceGeneratingIfMissing = true
        )

        return ShipStory.ShipStoryItem(
            mmsis = getAllMmsisForEntry(entry),
            entry = entry,
            esof = esof,
            trace = trace.toStoryTrace()
        )
    }

    fun storyDryRun(
        imo: Int,
        start: Instant,
        end: Instant
    ): ShipStory {
        val events = eventService.fetchAisEngineEventsByIMO(
            imo = imo,
            start = start,
            end = end
        )

        val pomaEntityIds = mutableSetOf<Pair<String, InfraAreaType>>()

        var previousTraceLastLocation: Location? = null
        var previousTraceLastSpeedOverGround: Float? = null
        var previousTraceLastDraught: Float? = null
        val storyItems = entryProcessingService.processAisEngineEventsDryRunForStoryV2(events)
            .map { (entry, esof) ->
                pomaEntityIds.addAll(entry.getAllPomaEntitiesIds())
                val dryRunTrace = processingTraceService.generateTraceWithShipHistory(
                    entry = entry,
                    imo = imo,
                    start = entry.start.time,
                    end = entry.end?.time ?: end,
                    previousTraceLastLocation = previousTraceLastLocation,
                    previousTraceLastSpeedOverGround = previousTraceLastSpeedOverGround,
                    previousTraceLastDraught = previousTraceLastDraught
                )
                previousTraceLastLocation = dryRunTrace?.polyline?.lastLocation
                previousTraceLastSpeedOverGround = dryRunTrace?.speed?.lastSpeedOverGround
                previousTraceLastDraught = dryRunTrace?.draught?.last

                ShipStory.ShipStoryItem(
                    mmsis = getAllMmsisForEntry(entry),
                    entry = entry,
                    esof = esof,
                    trace = dryRunTrace.toStoryTrace()
                )
            }

        return ShipStory(
            imo = imo,
            items = storyItems,
            pomaEntities = getPomaEntitiesForShipStory(pomaEntityIds)
        )
    }

    private fun getPomaEntitiesForShipStory(
        pomaEntityIds: Set<Pair<String, InfraAreaType>>
    ): Map<InfraAreaType, List<PomaModel>> {
        return pomaEntityIds.mapNotNull { (infraId, infraType) ->
            val pomaEntity = infraService.getById(infraId, infraType)

            if (pomaEntity != null) {
                infraType to pomaEntity
            } else {
                null
            }
        }.groupBy({ it.first }, { it.second })
    }

    private fun NewEntry.getAllPomaEntitiesIds(): Set<Pair<String, InfraAreaType>> {
        return when (this) {
            is NewVisit -> {
                listOfNotNull(this.eospAreaActivity.getPomaEntityId(InfraAreaType.PORT, ".eosp")) +
                    this.otherOngoingEospAreaActivities.getPomaEntitiesIds(InfraAreaType.PORT, ".eosp") +
                    this.portAreaActivities.getPomaEntitiesIds(InfraAreaType.PORT) +
                    this.anchorAreaActivities.getPomaEntitiesIds(InfraAreaType.ANCHOR) +
                    this.anchorAreaAreaActivities.getPomaEntitiesIds(InfraAreaType.ANCHOR) +
                    this.berthAreaActivities.getPomaEntitiesIds(InfraAreaType.BERTH) +
                    this.passThroughEosp.getPomaEntitiesIds(InfraAreaType.PORT, ".eosp") +
                    this.passThroughPort.getPomaEntitiesIds(InfraAreaType.PORT) +
                    this.pilotAreaActivities.getPomaEntitiesIds(InfraAreaType.PILOT_BOARDING_PLACE) +
                    this.terminalMooringAreaActivities.getPomaEntitiesIds(InfraAreaType.TERMINAL, ".mooringarea") +
                    this.lockAreaActivities.getPomaEntitiesIds(InfraAreaType.LOCK) +
                    this.approachAreaActivities.getPomaEntitiesIds(InfraAreaType.APPROACH_AREA) +
                    this.stops.getPomaEntitiesIds()
            }

            is NewVoyage -> {
                this.passThroughEosp.getPomaEntitiesIds(InfraAreaType.PORT, ".eosp") +
                    this.passThroughPort.getPomaEntitiesIds(InfraAreaType.PORT)
            }
        }.toSet()
    }

    private fun AreaActivity.getPomaEntityId(areaType: InfraAreaType, suffix: String? = null): Pair<String, InfraAreaType> {
        val areaId = suffix?.let { this.areaId.removeSuffix(suffix) } ?: this.areaId

        return areaId to areaType
    }

    private fun List<AreaActivity>.getPomaEntitiesIds(areaType: InfraAreaType, suffix: String? = null): List<Pair<String, InfraAreaType>> {
        return this.map { activity ->
            activity.getPomaEntityId(areaType, suffix)
        }
    }

    private fun List<NewStop>.getPomaEntitiesIds(): List<Pair<String, InfraAreaType>> {
        return this.mapNotNull { stop ->
            val areaId = stop.areaId
            val stopAreaType = when (stop.type) {
                NewStopType.ANCHOR_AREA -> InfraAreaType.ANCHOR
                NewStopType.BERTH -> InfraAreaType.BERTH
                NewStopType.LOCK -> InfraAreaType.LOCK
                NewStopType.UNCLASSIFIED -> null
            }

            if (areaId != null && stopAreaType != null) {
                areaId to stopAreaType
            } else {
                null
            }
        }
    }

    private fun NewTrace?.toStoryTrace(): NewTrace? {
        if (this == null) {
            return null
        }

        return this.copy(
            polyline = processingTraceService.getCombinedPolyline(this)
        )
    }

    private fun getAllMmsisForEntry(entry: NewEntry): List<Int> {
        // Get all mmsis used for this entry
        val imoMmsiMapping = staticShipInfoService.getMmsiMappingFromImoAndDateRange(
            imo = entry.imo.toString(),
            from = entry.start.time.atZone(ZoneOffset.UTC),
            until = entry.end?.time?.atZone(ZoneOffset.UTC)
        )

        return imoMmsiMapping
            ?.mapNotNull { it.mmsi.toIntOrNull() }
            ?: emptyList()
    }
}
