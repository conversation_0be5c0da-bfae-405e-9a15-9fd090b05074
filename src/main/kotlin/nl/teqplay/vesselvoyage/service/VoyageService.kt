package nl.teqplay.vesselvoyage.service

import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.datasource.VoyageDataSource
import nl.teqplay.vesselvoyage.model.Filter
import nl.teqplay.vesselvoyage.model.Voyage
import nl.teqplay.vesselvoyage.model.VoyageQuery
import org.springframework.stereotype.Service
import java.time.ZonedDateTime

@ProfileProcessing
@ProfileApi
@Service
class VoyageService(
    private val voyageDataSource: VoyageDataSource,
    private val staticShipInfoService: StaticShipInfoService
) {
    fun findByIMO(imo: String, start: ZonedDateTime, end: ZonedDateTime, limit: Int) =
        voyageDataSource.findByIMO(imo, start, end, limit)

    fun findByDestination(
        destination: String,
        filter: Filter? = null
    ): List<Voyage> {
        require(destination.isNotBlank()) { "Destination shouldn't be blank" }

        val imoSet = staticShipInfoService.applyFilter(filter)
        return voyageDataSource.findByDestination(destination, imoSet)
            .toList()
    }

    fun findByOrigin(
        origin: String,
        filter: Filter? = null
    ): List<Voyage> {
        require(origin.isNotBlank()) { "Origin shouldn't be blank" }

        val imoSet = staticShipInfoService.applyFilter(filter)
        return voyageDataSource.findByOrigin(origin, imoSet)
            .toList()
    }

    fun find(query: VoyageQuery) = voyageDataSource.find(query, staticShipInfoService::getShipsByCategory)

    /**
     * Finds a [Voyage] that starts either before or at the specified [before] time.
     */
    fun findNewestStartedBeforeByImo(imo: String, before: ZonedDateTime): Voyage? =
        voyageDataSource.findNewestStartedBeforeByImo(imo, before)

    /**
     * Finds a [Voyage] that ends either after or at the specified [after] time.
     * The [Voyage] should [start] after the given time as well, as the [Voyage.endTime] alone might
     * be incorrect (i.e. end too far in the future).
     */
    fun findOldestEndedAfterByImo(imo: String, start: ZonedDateTime, after: ZonedDateTime): Voyage? =
        voyageDataSource.findOldestEndedAfterByImo(imo, start, after)
}
