package nl.teqplay.vesselvoyage.service.api

import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.apiv2.model.sof.StatementOfFactsView
import nl.teqplay.vesselvoyage.apiv2.model.sof.StatementOfFactsViewName
import nl.teqplay.vesselvoyage.apiv2.model.sof.StatementOfFactsViewName.PORTREPORTER
import nl.teqplay.vesselvoyage.apiv2.model.sof.StatementOfFactsViewName.PTO
import nl.teqplay.vesselvoyage.datasource.NewESoFDataSource
import nl.teqplay.vesselvoyage.model.v2.EntryId
import nl.teqplay.vesselvoyage.model.v2.NewESoF
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import org.springframework.stereotype.Service

@ProfileProcessing
@ProfileApi
@Service
class EsofV2Service(
    private val dataSource: NewESoFDataSource,
    private val ptoGenerator: PtoStatementOfFactsViewGenerator,
    private val portReporterGenerator: PortReporterStatementOfFactsViewGenerator,
    private val visitV2Service: VisitV2Service,
    private val voyageV2Service: VoyageV2Service,
) {
    fun findById(entryId: EntryId): NewESoF? {
        return dataSource.findById(entryId)
    }

    fun findAllById(entryIds: List<EntryId>): List<NewESoF> {
        return dataSource.findByIds(entryIds)
    }

    fun findAllNonPostProcessed(): List<NewESoF> {
        return dataSource.findAllNonPostProcessed()
    }

    /**
     * Builds the PTO view of a Statement of Facts. Returns null when the visit is not found.
     *
     * Note that the returned model is ready to be returned from the API! Information is already mapped to API specific
     * models and may not be suitable for further other internal purposes, as certain information is stripped away.
     */
    fun produce(view: StatementOfFactsViewName, entryId: EntryId): StatementOfFactsView? {
        val visit = visitV2Service.findById(entryId) ?: return null
        return produce(view, visit)
    }

    fun produce(view: StatementOfFactsViewName, visit: NewVisit): StatementOfFactsView {
        val esof = findById(visit._id)
        return produce(view, visit, esof)
    }

    fun produce(view: StatementOfFactsViewName, visit: NewVisit, esof: NewESoF?): StatementOfFactsView {
        return when (view) {
            PORTREPORTER -> portReporterGenerator.generate(
                visit = visit,
                esof = esof
            )
            PTO -> ptoGenerator.generate(
                visit = visit,
                esof = esof,
                previousPortAreaId = findPreviousPortAreaId(visit.previous)
            )
        }
    }

    fun produceBulk(view: StatementOfFactsViewName, visits: List<NewVisit>): List<StatementOfFactsView> {
        val visitIds = mutableListOf<String>()
        val previousVoyageIds = mutableListOf<String>()
        visits.forEach { visit ->
            visitIds.add(visit._id)
            visit.previous?.let { previousVoyageIds.add(it) }
        }

        // Search all esofs in 1 go to speed up the time to create a SOF
        val allEsofs = findAllById(visitIds).associateBy { it._id }
        return visits.map { visit ->
            val visitEsof = allEsofs[visit._id]
            produce(
                view = view,
                visit = visit,
                esof = visitEsof
            )
        }
    }

    private fun findPreviousPortAreaId(voyageId: EntryId?): String? {
        return if (voyageId != null) {
            voyageV2Service.findById(voyageId)?.originPort?.removeSuffix(".eosp")
        } else {
            null
        }
    }
}
