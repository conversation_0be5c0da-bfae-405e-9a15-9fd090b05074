package nl.teqplay.vesselvoyage.service.api

import nl.teqplay.vesselvoyage.datasource.BaseApiDataSource
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter
import java.time.Instant

abstract class BaseApiV2Service<T : NewEntry, D : BaseApiDataSource<T>>(
    private val dataSource: D
) {
    fun findById(id: String): T? {
        return dataSource.findById(id)
    }

    fun findByIds(ids: Set<String>): List<T> {
        return dataSource.findByIds(ids)
    }

    fun findByImoStartingAtOrAfter(
        imo: Int,
        start: Instant,
        finishedState: NewEntryFinishedFilter,
        confirmed: Boolean?
    ): List<T> = dataSource.findByImoStartedAfter(
        imo = imo,
        start = start,
        finished = finishedState,
        confirmed = confirmed
    )

    fun findByImoAndTimeRange(
        imo: Int,
        start: Instant,
        end: Instant,
        finishedState: NewEntryFinishedFilter,
        confirmed: Boolean?
    ) = dataSource.findByImoAndTimeRange(
        imo = imo,
        start = start,
        end = end,
        finished = finishedState,
        confirmed = confirmed
    )

    fun findLastByImo(
        imo: Int,
        limit: Int,
        finishedState: NewEntryFinishedFilter,
        confirmed: Boolean?
    ) = dataSource.findLastByImo(
        imo = imo,
        limit = limit,
        finished = finishedState,
        confirmed = confirmed
    )

    fun countByLimited(limited: Boolean, imos: Set<Int>): Long =
        dataSource.countLimitedEntries(
            limited = limited,
            imos = imos
        )

    fun findByLimited(limited: Boolean, imos: Set<Int>, page: Int, pageSize: Int): List<T> {
        val skip = if (page == 1) {
            0
        } else {
            (page - 1) * pageSize
        }

        return dataSource.getLimitedEntries(
            limited = limited,
            imos = imos,
            limit = pageSize,
            skip = skip
        )
    }
}
