package nl.teqplay.vesselvoyage.service.api

import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.datasource.NewVisitDataSource
import nl.teqplay.vesselvoyage.datasource.NewVoyageDataSource
import nl.teqplay.vesselvoyage.model.isVisitId
import nl.teqplay.vesselvoyage.model.isVoyageId
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter
import nl.teqplay.vesselvoyage.util.ceilDiv
import nl.teqplay.vesselvoyage.util.sortedByStartAndEndTime
import org.springframework.stereotype.Service
import java.time.Instant

@ProfileProcessing
@ProfileApi
@Service
class EntryV2Service(
    private val visitDataSource: NewVisitDataSource,
    private val voyageDataSource: NewVoyageDataSource
) {

    fun findEntry(id: String) = when {
        isVisitId(id) -> visitDataSource.findById(id)
        isVoyageId(id) -> voyageDataSource.findById(id)
        else -> null
    }

    fun findEntries(ids: Set<String>): List<NewEntry> {
        val visitIds = ids.filter(::isVisitId).toSet()
        val voyageIds = ids.filter(::isVoyageId).toSet()
        val visits = visitDataSource.findByIds(visitIds)
        val voyages = voyageDataSource.findByIds(voyageIds)
        return (visits + voyages).sortedByStartAndEndTime()
    }

    fun findByImoAndTimeRange(
        imo: Int,
        start: Instant,
        end: Instant,
        finishedState: NewEntryFinishedFilter,
        confirmed: Boolean?
    ): List<NewEntry> {
        val visits = visitDataSource.findByImoAndTimeRange(imo, start, end, finishedState, confirmed)
        val voyages = voyageDataSource.findByImoAndTimeRange(imo, start, end, finishedState, confirmed)
        return (visits + voyages).sortedByStartAndEndTime()
    }

    fun findLastByImo(
        imo: Int,
        limit: Int,
        finishedState: NewEntryFinishedFilter,
        confirmed: Boolean?
    ): List<NewEntry> {
        // We don't know in advance if the latest entry is a visit or voyage, so always retrieve an equal amount of
        // both visits and voyages. The limit is divided by 2, then Math.ceil'ed to achieve this.
        val halfLimitCeiled = limit.ceilDiv(2)
        val visits = visitDataSource.findLastByImo(imo, halfLimitCeiled, finishedState, confirmed)
        val voyages = voyageDataSource.findLastByImo(imo, halfLimitCeiled, finishedState, confirmed)

        // in case when limit is an odd number, we have 1 entry too much, therefore take the last (most recent)
        // entries to drop the item that exceeds the limit
        // For example with this result [0,1,2,3] and limit=3, the returned array is [1,2,3]
        return (visits + voyages).sortedByStartAndEndTime().takeLast(limit)
    }
}
