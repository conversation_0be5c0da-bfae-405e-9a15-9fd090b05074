package nl.teqplay.vesselvoyage.service.api

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType.TUG_WAITING_DEPARTURE
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.AreaMeta
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.CategorizedPeriods
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.PtoStatementOfFactsView
import nl.teqplay.vesselvoyage.mapper.EntryV2Mapper
import nl.teqplay.vesselvoyage.mapper.PtoStatementOfFactsMapper
import nl.teqplay.vesselvoyage.model.MAX_DURATION_DEPART_AND_TUG
import nl.teqplay.vesselvoyage.model.ShipDetails
import nl.teqplay.vesselvoyage.model.esof.ptoview.AnchorStopInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.ApproachAreaVisitInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.BerthVisitInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.EncounterInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.LockStopInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.PilotInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.PortAreaInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.ShipToShipTransferInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.TerminalVisitInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.TugInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.UnclassifiedStopInfo
import nl.teqplay.vesselvoyage.model.internal.InfraAreaType
import nl.teqplay.vesselvoyage.model.lightweight.poma.Anchorage
import nl.teqplay.vesselvoyage.model.lightweight.poma.ApproachArea
import nl.teqplay.vesselvoyage.model.lightweight.poma.Berth
import nl.teqplay.vesselvoyage.model.lightweight.poma.Lock
import nl.teqplay.vesselvoyage.model.lightweight.poma.PilotBoardingPlace
import nl.teqplay.vesselvoyage.model.lightweight.poma.PomaModel
import nl.teqplay.vesselvoyage.model.lightweight.poma.Port
import nl.teqplay.vesselvoyage.model.lightweight.poma.ShipToShipArea
import nl.teqplay.vesselvoyage.model.lightweight.poma.Terminal
import nl.teqplay.vesselvoyage.model.v2.AreaActivity
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewESoF
import nl.teqplay.vesselvoyage.model.v2.NewEncounter
import nl.teqplay.vesselvoyage.model.v2.NewSlowMovingPeriod
import nl.teqplay.vesselvoyage.model.v2.NewStop
import nl.teqplay.vesselvoyage.model.v2.NewStopType.ANCHOR_AREA
import nl.teqplay.vesselvoyage.model.v2.NewStopType.BERTH
import nl.teqplay.vesselvoyage.model.v2.NewStopType.LOCK
import nl.teqplay.vesselvoyage.model.v2.NewStopType.UNCLASSIFIED
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.ShipToShipTransfer
import nl.teqplay.vesselvoyage.model.v2.StartEnd
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.ShipCacheService
import nl.teqplay.vesselvoyage.util.contains
import nl.teqplay.vesselvoyage.util.endsIn
import nl.teqplay.vesselvoyage.util.isFinished
import nl.teqplay.vesselvoyage.util.isOngoing
import nl.teqplay.vesselvoyage.util.mapWithSurrounding
import nl.teqplay.vesselvoyage.util.overlaps
import nl.teqplay.vesselvoyage.util.ports
import nl.teqplay.vesselvoyage.util.startsIn
import nl.teqplay.vesselvoyage.util.toShipDetails
import org.springframework.stereotype.Service
import java.time.Duration
import java.time.Instant
import java.util.UUID

@ProfileProcessing
@ProfileApi
@Service
class PtoStatementOfFactsViewGenerator(
    private val infraService: InfraService,
    private val mapper: PtoStatementOfFactsMapper,
    private val entryMapper: EntryV2Mapper,
    private val shipCacheService: ShipCacheService
) {

    private val log = KotlinLogging.logger {}

    companion object {
        const val version = 1
        /** Maximum allowed time before actual berth visit (in minutes) to consider a tug as assisting with a ship's arrival at a specific berth */
        const val TUG_ARRIVAL_TOLERANCE = 15L
        /** Maximum allowed time after actual berth visit (in minutes) to consider a tug as assisting with a ship's departure at a specific berth */
        const val TUG_DEPARTURE_TOLERANCE = 15L
    }

    fun generate(
        visit: NewVisit,
        esof: NewESoF?,
        previousPortAreaId: String?
    ): PtoStatementOfFactsView {
        val ship = shipDetailsOrDefaultUnknown(imo = visit.imo, mmsi = null)
        val area = generateEospAreaMeta(areaId = visit.eospAreaActivity.areaId)

        val portAreas = generatePortAreas(visit.portAreaActivities, visit.stops)
        val previousPortArea = previousPortAreaId?.let { areaId ->
            infraService.getById(areaId, InfraAreaType.PORT) as Port?
        }

        val berthStops = visit.stops.filter { it.type == BERTH }

        val encounters = esof?.encounters?.filterValidTugWaitingEvents()

        val tugEncounters = encounters?.filter { it.type == EncounterEvent.EncounterType.TUG }
            ?: emptyList()
        val waitingDepartureEncounters = encounters?.filter { it.type == TUG_WAITING_DEPARTURE }
            ?: emptyList()

        val allTugs = generateTugs(visit._id, tugEncounters, waitingDepartureEncounters)
        val berthVisitsWithoutTerminalRef = generateBerthVisits(visit._id, berthStops, portAreas)
            .attachTugs(allTugs)
            .alignWithBerthActivities(visit.berthAreaActivities)

        val terminalVisits = generateTerminalVisits(
            groupBerthVisitsByTerminal(berthVisitsWithoutTerminalRef),
            visit.terminalMooringAreaActivities
        )

        // set the berthVisit.terminalVisit where applicable, so the mapper can resolve the berthVisit.terminalVisitRef
        val berthVisits = populateBerthVisitsWithTerminalVisit(berthVisitsWithoutTerminalRef, terminalVisits)

        val anchorStops = generateAnchorStops(visit._id, visit.stops, portAreas)
        val unclassifiedStops = generateUnclassifiedStops(
            visit._id,
            stops = visit.stops,
            portAreas = portAreas,
            eospAreaId = visit.eospAreaActivity.areaId.removeSuffix(".eosp")
        )

        val encounterInfo = encounters?.let {
            generateEncounters(visit._id, it, portAreas, berthVisits, terminalVisits)
        }

        val pilotEncounters = encounters?.filter { encounter -> encounter.type == EncounterEvent.EncounterType.PILOT }
        val pilotInbound = pilotEncounters?.let { encounter -> findInboundPilot(visit._id, encounter, berthVisits, anchorStops, visit.pilotAreaActivities) }
            ?: findInboundPilotByAreaFallback(visit._id, visit.pilotAreaActivities, berthVisits.firstOrNull())
        val pilotOutbound = pilotEncounters?.let { encounter -> findOutboundPilot(visit._id, encounter, berthVisits, anchorStops, visit.pilotAreaActivities) }
            ?: findOutboundPilotByAreaFallback(visit._id, visit.pilotAreaActivities, berthVisits.lastOrNull())

        val lockStops = generateLockStops(visit._id, visit.stops, portAreas)
        val approachAreaVisits = generateApproachAreaVisits(visit.approachAreaActivities, portAreas)

        val shipToShipTransfers = esof?.shipToShipTransfers?.let { generateShipToShipTransfers(visit._id, it) }
        val categorizedSlowMovingPeriods = determineSlowMovingPeriods(
            initialSlowMovingPeriods = esof?.slowMovingPeriods,
            berthVisits = berthVisits,
            anchorStops = anchorStops,
            lockStops = lockStops,
            pilotInbound = pilotInbound,
            pilotOutbound = pilotOutbound,
            portAreaActivities = visit.portAreaActivities
        )

        val categorizedAnchorStops = categorizeAnchorStops(
            anchorStops = anchorStops,
            pilotInbound = pilotInbound,
            pilotOutbound = pilotOutbound,
            portAreaActivities = visit.portAreaActivities
        )

        return PtoStatementOfFactsView(
            version = version,
            generationId = UUID.randomUUID().toString(),
            entryId = visit._id,
            start = visit.start.toApi(),
            end = visit.end?.toApi(),
            area = area,
            previousPort = previousPortArea?.let(mapper::toAreaMeta),
            ship = ship,
            portAreas = portAreas.map(mapper::toPortArea),
            berthVisits = berthVisits.map(mapper::toBerthVisit),
            terminalVisits = terminalVisits.map(mapper::toTerminalVisit),
            anchorStops = anchorStops.map(mapper::toAnchorStop),
            unclassifiedStops = unclassifiedStops.map(mapper::toUnclassifiedStop),
            encounters = encounterInfo?.map(mapper::toEncounter) ?: emptyList(),
            pilotInbound = pilotInbound?.let(mapper::toPilot),
            pilotOutbound = pilotOutbound?.let(mapper::toPilot),
            lockStops = lockStops.map(mapper::toLockStop),
            approachAreas = approachAreaVisits.map(mapper::toApproachAreaVisit),
            shipToShipTransferTransfers = shipToShipTransfers?.map(mapper::toShipToShip) ?: emptyList(),
            slowMovingPeriods = categorizedSlowMovingPeriods?.let(mapper::mapCategorizedSlowMovingPeriods),
            categorizedAnchorStops = categorizedAnchorStops.let(mapper::mapCategorizedAnchorStopInfo)
        )
    }

    private fun generateEospAreaMeta(areaId: String): AreaMeta? {
        val portAreaId = areaId.removeSuffix(".eosp")
        val port = infraService.getById(portAreaId, InfraAreaType.PORT) as? Port
            // It can be possible that the port doesn't exist anymore for more older data
            ?: return null
        val portAreaMeta = mapper.toAreaMeta(port = port)

        // Keep the areaId from poma as that is what is used to find the port back in poma
        return portAreaMeta.copy(
            type = "eosp"
        )
    }

    fun generatePortAreas(
        portAreaActivities: List<AreaActivity>,
        stops: List<NewStop>
    ): List<PortAreaInfo> = portAreaActivities.mapIndexedNotNull { index, portActivity ->
        val port = infraService.getById(portActivity.areaId, InfraAreaType.PORT) as? Port
        if (port != null) {
            PortAreaInfo(
                ref = index,
                activity = portActivity,
                isPassThrough = isPassingThroughPortActivity(portActivity, stops),
                area = port
            )
        } else {
            null
        }
    }

    /** Whether the ship did not stop in the given port area, in other words: just passing through */
    fun isPassingThroughPortActivity(
        portActivity: AreaActivity,
        stops: List<NewStop>
    ): Boolean {
        // it doesn't matter here whether the full visit is finished,
        // we're only interested in stops inside the given port area
        return if (portActivity.isFinished()) {
            // the port activity is finished, therefore all stops inside the port area should be finished
            stops.none { stop -> stop in portActivity }
        } else {
            // the port activity is ongoing, if a stop has started inside this ongoing port activity, it is safe
            // to assume the ship will not just pass through
            stops.none { stop -> stop.startsIn(portActivity) }
        }
    }

    private fun generateBerthVisits(
        visitId: String,
        berthStops: List<NewStop>,
        portAreas: List<PortAreaInfo>
    ): List<BerthVisitInfo> {
        return berthStops.mapIndexed { index, berthStop ->
            val berth = berthStop.areaId?.let { infraService.getBerth(it) }
            val berthActivity = AreaActivity(id = index.toString(), berthStop.start, berthStop.end, berthStop.areaId!!)
            BerthVisitInfo(
                id = "$visitId.${berthStop.startEventId}",
                ref = index,
                activity = berthActivity,
                area = berth,
                portArea = berth?.let { matchBerthVisitToPortArea(berthActivity, portAreas, it) },
                // properties below can only be derived when all berth visits are calculated
                arrivalTugs = emptyList(),
                departureTugs = emptyList(),
                firstLineSecured = null,
                allFast = null,
                lastLineReleased = null,
                terminalVisit = null
            )
        }
    }

    /**
     * Returns berth visits grouped by what would be considered a terminal visit. The system does not have a register
     * of terminal area stops, therefore it derives them from visits to berths belonging to the same terminal.
     *
     * Note that visits to berths that are not part of a terminal, are not returned!
     *
     * A terminal visit is derived from berth visits that: (1) are adjacent and (2) have the same terminal id.
     * Note that the system needs to respect the timeline of berth visits: if the system would just use groupBy,
     * visits to the same terminal may be concatenated while there was a different terminal in between.
     *
     * Examples:
     * --[berth 1]--[berth 2]--[berth 3]--[berth 1]--
     * --[    terminal A    ]-------------[term. A]-- -> should result in 2 terminal visits! A - A
     * --[    terminal A    ]--[term. B]--[term. A]-- -> should result in 3 terminal visits: A - B - A
     */
    fun groupBerthVisitsByTerminal(berthVisits: List<BerthVisitInfo>): List<BerthVisitGroup> {
        // use an invalid value instead of null, to prevent nullability in BerthVisitGroup terminalId
        val invalidTerminalId = ""
        return berthVisits.fold(mutableListOf<BerthVisitGroup>()) { groupedVisits, berthVisit ->
            val terminalId = berthVisit.area?.terminalId ?: invalidTerminalId

            // create a new group when the terminalId changes
            // otherwise add it to the last created group
            val newSubList = groupedVisits.isEmpty() || groupedVisits.last().terminalId != terminalId
            if (newSubList) {
                groupedVisits.add(BerthVisitGroup(terminalId, mutableListOf(berthVisit)))
            } else {
                groupedVisits.last().visits += berthVisit
            }
            groupedVisits
        }.filter { group ->
            // only return visit groups that belong to a terminal
            group.terminalId != invalidTerminalId
        }
    }

    fun generateTerminalVisits(
        berthVisitGroups: List<BerthVisitGroup>,
        terminalMoorings: List<AreaActivity>
    ): List<TerminalVisitInfo> {
        // Create terminal visits from consecutive berth visits having the same terminal id AND same port area id.
        // In other words, fold berth visits with those criteria into terminal visits
        // Note that a simple berthVisits.groupBy() would not suffice, as a berth may be visited multiple times, and
        // those visits are likely not consecutive. In other words, groupBy() would not honor the order of events.
        // For example, assume terminal 1 with berth A and B, and terminal 2 with berth C, and berth D without terminal
        // berth A -> berth A = 1 terminal visit
        // berth A -> berth B = 1 terminal visit
        // berth A -> anchorage inside port area -> berth B = 1 terminal visit
        // berth A -> anchorage outside port area -> berth B = 2 terminal visits
        // berth A -> berth C -> berth A = 3 terminal visits. Visits to A are not combined, as interrupted by C

        // ---[berth1]--[berth2]--[berth3]--
        // ---[   terminal A   ]--|--------- -> 1 terminal visit for A
        // ---[term.A]------------[term.B]-- ->

        return berthVisitGroups.mapIndexedNotNull { index, groupedVisits ->
            val (terminalId: String, visits: List<BerthVisitInfo>) = groupedVisits
            if (visits.isEmpty()) {
                return@mapIndexedNotNull null
            }

            val firstBerth = visits.first()

            val terminalMooring = terminalMoorings
                .filter { activity -> "$terminalId.mooringarea" == activity.areaId }
                // pick closest to first berth visit of the group, multiple terminal visits cause multiple mooring times
                .minByOrNull { mooring -> Duration.between(mooring.start.time, firstBerth.start.time).abs() }
            TerminalVisitInfo(
                ref = index,
                activity = AreaActivity(
                    id = terminalMooring?.id ?: firstBerth.id,
                    start = firstBerth.start,
                    end = visits.last().end,
                    areaId = terminalId
                ),
                mooringActivity = terminalMooring,
                area = infraService.getById(terminalId, InfraAreaType.TERMINAL) as? Terminal,
                portArea = firstBerth.portArea,
                berthVisits = visits
            )
        }
    }

    fun matchBerthVisitToPortArea(
        berthActivity: AreaActivity,
        portAreas: List<PortAreaInfo>,
        berth: Berth
    ): PortAreaInfo? = portAreas.find { (_, _, activity: AreaActivity, port: Port) ->
        berthActivity.startsIn(activity) &&
            port._id != null &&
            (port._id == berth.mainPort || port._id in berth.ports || port.unlocode in berth.ports)
    }

    fun generateTugs(
        visitId: String,
        tugEncounters: List<NewEncounter>,
        waitingDepartureEncounter: List<NewEncounter>
    ) = tugEncounters.mapIndexed { index, encounter ->
        TugInfo(
            id = "$visitId.${encounter.startEventId}",
            ref = index,
            encounter = encounter,
            waitingForDepartureEncounter = retrieveMatchingWaitingForDepartureEncounter(
                tug = encounter,
                waitingEncounters = waitingDepartureEncounter
            )
        )
    }

    fun findArrivalTugs(
        previousStop: StartEnd?,
        currentStop: StartEnd,
        allTugs: List<TugInfo>
    ): List<TugInfo> {

        // Arrival tugs for berth 2:
        // ------[  berth 1  ]--------------[    berth 2   ]------
        // ------[      candidate window                   ]------
        // ---------------[         tug          ]-------------- <-- arrival tug for berth 2, in window,
        // -----------------------------[   tug  ]-------------- <-- arrival tug for berth 2
        // --------------------------------------[tug]---------- <-- NOT arrival tug, tug.start not before berth.start
        // ----------------[tug]-------------------------------- <-- NOT arrival tug, tug.end not in berth2
        // ---[   tug  ]---------------------------------------- <-- NOT arrival tug, tug not overlapping berth2

        return allTugs.filter { tug ->
            // Tug must start before current berth.start, otherwise there is no reason for the tug to be involved in
            // berth arrival
            // In case there is a previous stop known, expect the tug to start in or after previous stop. Longer tug
            // encounters are usually not tug operations (from experience in pto)
            val startsBeforeCurrentBerth = if (previousStop != null) {
                tug.start < currentStop.start && tug.start >= previousStop.start
            } else {
                tug.start < currentStop.start
            }

            // The tug.end should end somewhere after arrival (berth.start).
            // A tug ending before or after the currentStop would not make sense for tugging the ship for arrival
            val endsOrOngoingInsideCurrentBerth = if (currentStop.isFinished()) {
                // Add a tolerance check to include encounters that end just before the berth visit starts.
                // This ensures that tugs with low AIS reporting frequency are still considered.
                tug.endsIn(currentStop, startTolerance = TUG_ARRIVAL_TOLERANCE)
            } else {
                // the berth visit is still ongoing:
                // if the tug encounter is finished, then we need the encounter.end after the
                // else the tug encounter is ongoing, then we assume the tug is still tugging the ship
                tug.isOngoing() || tug.end!! > currentStop.start
            }

            log.debug {
                "tug ${tug.imo ?: tug.mmsi} ${tug.start.time} / ${tug.end?.time}, " +
                    "starts before current berth: $startsBeforeCurrentBerth, " +
                    "ends or ongoing inside current berth: $startsBeforeCurrentBerth"
            }

            startsBeforeCurrentBerth && endsOrOngoingInsideCurrentBerth
        }
    }

    fun findDepartureTugs(
        currentStop: StartEnd,
        nextStop: StartEnd?,
        allTugs: List<TugInfo>
    ): List<TugInfo> {

        // Departure tugs for berth 1:
        // ------[  berth 1  ]--------------[    berth 2   ]------
        // ------[      candidate window                   ]------
        // ---[   tug  ]---------------------------------------- <-- NOT departure tug, tug.start not in berth 1
        // --------[tug]---------------------------------------- <-- NOT departure tug, tug.end not after berth 1
        // ---------------[         tug          ]-------------- <-- departure tug, start in berth 1, ends in berth 2
        // ---------------[   tug   ]--------------------------- <-- departure tug, starts in berth1, ends after berth1
        // ---------------[   tug                             ]- <-- NOT departure tug, ends after berth 2 (nextstop)
        // -----------------------------[   tug  ]-------------- <-- NOT departure tug, does not start in berth 1
        // --------------------[ tug ]-------------------------- <-- departure tug, starts after berth1 in first half of in-between time
        // -------------------------[ tug ]--------------------- <-- NOT departure tug, starts after berth1 in second half of in-between time

        return allTugs.filter { tug ->

            // Tug must start before current berth.start, otherwise there is no reason for the tug to be there
            // Add a tolerance check to include encounters that start just after the berth visit ends.
            // This ensures that tugs with low AIS reporting frequency are still considered.
            val startsInCurrentBerth = tug.startsIn(other = currentStop, endTolerance = TUG_DEPARTURE_TOLERANCE)

            if (!startsInCurrentBerth) {
                // Tug was not during the berth visit so check if it started before the next stop
                if (currentStop.isFinished()) {
                    val currentEndTime = currentStop.end!!.time
                    val nextStartTime = nextStop?.start?.time
                    val tugStartTime = tug.start.time

                    // Skip tugs that either started before our current stop or after
                    if (currentEndTime > tugStartTime || (nextStartTime != null && nextStartTime < tugStartTime)) {
                        return@filter false
                    }

                    val halfTimeBetweenStops = nextStartTime?.let { Duration.between(currentEndTime, nextStartTime) }
                        ?.dividedBy(2) // Take half of the duration

                    if (halfTimeBetweenStops != null) {
                        // This tug is a departure tug when it started in the first half of the time between the current and next stop
                        return@filter tugStartTime.isBefore(currentEndTime + halfTimeBetweenStops)
                    }

                    // Tug happened after current stop end and no next stop happened
                    return@filter true
                }

                return@filter false
            }

            // and check the tug.end to see whether the tug qualifies as departure tug
            // current is ongoing && no next -> tug start in current && ongoing
            // current is finished && no next -> tug start in current && (tug ongoing || ends after current)
            // current is finished && next ongoing -> tug start in current && (tug ongoing || end after current)
            // current is finished && next finished -> tug finished && (start in current && end < next.end

            if (currentStop.isOngoing() && nextStop == null) {
                tug.isOngoing()
            } else if (currentStop.isFinished() && nextStop == null) {
                // departure is still ongoing or tug should have stopped after current stop
                tug.isOngoing() || tug.end!! > currentStop.end!!
            } else if (currentStop.isFinished() && nextStop?.isOngoing() == true) {
                // if tug is ongoing, it finished the departure, but is also tugging for arrival at next berth
                // or, tug only tugged with departure from currentStop
                tug.isOngoing() || tug.end!! > currentStop.end!!
            } else if (currentStop.isFinished() && nextStop?.isFinished() == true) {
                // tug must have ended after current stop AND (before next stop OR in next stop)
                tug.isFinished() && tug.end!! >= currentStop.end!! && (tug.end < nextStop.start || tug.endsIn(nextStop))
            } else {
                // other cases are not possible for departure tugs
                false
            }
        }
    }

    fun setTugs(allTugs: List<TugInfo>, berthVisits: List<BerthVisitInfo>): List<BerthVisitInfo> {
        return berthVisits.mapWithSurrounding { previous, current, next ->

            log.debug {
                "Finding tugs for berth visit ${current.id} (${current.area?.name}): " +
                    "${current.start.time} / ${current.end?.time}"
            }

            val arrivalTugs = findArrivalTugs(previous?.activity, current, allTugs)
            val departureTugs = findDepartureTugs(current, next?.activity, allTugs)
                // Set the start times of tugs to the waiting depature encounter start time if available
                .alignDepartureStartTimes()

            // first tug leaving the ship in the arrival process
            val firstLineSecured: LocationTime?
            // last tug leaving the ship in the arrival process
            val allFast: LocationTime?
            // last tug arriving at the ship in the departure process
            val lastLineReleased: LocationTime?

            if (arrivalTugs.isNotEmpty() && arrivalTugs.allFinished()) {
                firstLineSecured = arrivalTugs.minByOrNull { it.end!! }?.end
                allFast = arrivalTugs.maxByOrNull { it.end!! }?.end
            } else {
                firstLineSecured = null
                allFast = null
            }

            if (departureTugs.isNotEmpty() && departureTugs.allFinished()) {
                lastLineReleased = departureTugs.minByOrNull { it.start }?.start
            } else {
                lastLineReleased = null
            }

            current.copy(
                arrivalTugs = arrivalTugs,
                departureTugs = departureTugs,
                firstLineSecured = firstLineSecured,
                allFast = allFast,
                lastLineReleased = lastLineReleased
            )
        }
    }

    fun populateBerthVisitsWithTerminalVisit(
        berthVisits: List<BerthVisitInfo>,
        terminalVisits: List<TerminalVisitInfo>
    ): List<BerthVisitInfo> {
        // this map only contains berthVisits that have a terminal visit
        val berthTerminalVisitMapping = terminalVisits.flatMap { terminalVisit ->
            terminalVisit.berthVisits.map { berthVisit -> berthVisit to terminalVisit }
        }.toMap()

        // attach the terminal visit to the berth visits
        // or if not available, just return the existing berth visit
        return berthVisits.map { berthVisit ->
            berthTerminalVisitMapping[berthVisit]
                ?.let { terminalVisit -> berthVisit.copy(terminalVisit = terminalVisit) }
                ?: berthVisit
        }
    }

    fun generateAnchorStops(
        visitId: String,
        stops: List<NewStop>,
        portAreas: List<PortAreaInfo>
    ) = stops
        .filter { it.type == ANCHOR_AREA }
        .map { stop ->
            val anchorage = stop.areaId?.let { infraService.getById(it, InfraAreaType.ANCHOR) } as? Anchorage
            val portAreaInfo = anchorage?.let { findPortArea(it, portAreas) }
            AnchorStopInfo(
                id = "$visitId.${stop.startEventId}",
                stop = stop,
                area = anchorage,
                portArea = portAreaInfo
            )
        }

    fun findPortArea(area: PomaModel, portAreas: List<PortAreaInfo>) = portAreas.find { portArea ->
        val portId = portArea.area._id
        portId != null && portId in area.ports()
    }

    /**
     * Returns the unclassified stops and tries to match the port inner area based on location (instead of area id).
     * The system has no area classification here, it's therefore not possible to retrieve an area by id. The area ids
     * are not unique across area types. The system falls back on checking whether the stop location is in the port
     * inner area polygon. Note that the [mainPortArea] may be null if the visit is ongoing and the ship is not yet
     * inside the port inner area.
     *
     * Also note that the system only looks at the main port. In case of sub ports, a port may appear multiple times
     * and have a unique portAreaRef in the resulting API model. As the system only refers to the main port, which
     * should only appear once in the portAreas list, the portAreaRef naturally points to the correct portArea item.
     */
    fun generateUnclassifiedStops(
        visitId: String,
        stops: List<NewStop>,
        portAreas: List<PortAreaInfo>,
        eospAreaId: String
    ): List<UnclassifiedStopInfo> = stops.filter { it.type == UNCLASSIFIED }.map { stop ->
        // only pick the main port, sub ports should always overlap the main port area
        // if only a sub port is returned, then just ignore
        // on multiple main port results, make sure to pick the one that contains the stop (looking at start/end)
        val portArea = portAreas.firstOrNull { portArea ->
            stop.startsIn(portArea.activity) && portArea.activity.areaId == eospAreaId
        }
        UnclassifiedStopInfo(
            id = "$visitId.${stop.startEventId}",
            stop = stop,
            portArea = portArea
        )
    }

    fun generateEncounters(
        visitId: String,
        encounters: List<NewEncounter>,
        portAreas: List<PortAreaInfo>,
        berthVisits: List<BerthVisitInfo>,
        terminalVisits: List<TerminalVisitInfo>
    ): List<EncounterInfo> {
        return encounters.map { encounter ->
            // the encounter may overlap multiple berth visits, but we're picking the first here
            // overlapping multiple berth visits does not happen often
            val berthVisit = berthVisits.firstOrNull { berthVisit -> encounter.overlaps(berthVisit) }
            EncounterInfo(
                id = "$visitId.${encounter.startEventId}",
                encounter = encounter,
                portAreaInfo = berthVisit?.portArea ?: portAreas.firstOrNull(),
                berthVisitInfo = berthVisit,
                terminalVisitInfo = berthVisit?.terminalVisit,
            )
        }
    }

    /**
     * Finds the last pilot before the first berth.
     * There may be multiple inbound pilots, see [PtoStatementOfFactsView.encounters] for all pilot encounters.
     */
    fun findInboundPilot(
        visitId: String,
        encounters: List<NewEncounter>,
        berthVisits: List<BerthVisitInfo>,
        anchorVisits: List<AnchorStopInfo>,
        pilotAreaActivities: List<AreaActivity>
    ): PilotInfo? {
        // pilot can only be labeled as 'inbound' when it happened before the first berth visit
        val firstActivityTime = berthVisits.firstOrNull()?.start
            // If there is no berth visit then it could be that the ship had a pilot for only going to the anchorage (e.g. SGSIN cases)
            ?: anchorVisits.firstOrNull()?.start
            ?: return null

        // Take the last pilot before we went to the first berth as that is the pilot we want to use for steaming in time
        val pilotEncounter = encounters.lastOrNull { encounter ->
            encounter.start < firstActivityTime
        } ?: return null

        val pilotArea = findSingleOverlappingPilotAreaOrNull(pilotEncounter, pilotAreaActivities)
        return PilotInfo.fromPilotShipEncounter(
            visitId = visitId,
            encounter = pilotEncounter,
            pilotArea = pilotArea
        )
    }

    fun findInboundPilotByAreaFallback(
        visitId: String,
        pilotAreaActivities: List<AreaActivity>,
        firstBerthVisit: BerthVisitInfo?,
    ): PilotInfo? {
        if (firstBerthVisit == null) {
            return null
        }

        // the last pilot area activity before the first berth visit
        val pilotAreaActivity = pilotAreaActivities.lastOrNull { encounter ->
            encounter.isFinished() && encounter.end!! < firstBerthVisit.start
        } ?: return null

        return if (pilotAreaActivity.isFinished()) {
            val pilotArea = infraService.getById(pilotAreaActivity.areaId, InfraAreaType.PILOT_BOARDING_PLACE)
                as? PilotBoardingPlace
            PilotInfo.fromPilotAreaActivity(
                visitId = visitId,
                pilotAreaActivity = pilotAreaActivity,
                pilotArea = pilotArea
            )
        } else {
            null
        }
    }

    /**
     * Finds the first pilot after the last berth.
     * There may be multiple outbound pilots, see [PtoStatementOfFactsView.encounters] for all pilot encounters.
     */
    fun findOutboundPilot(
        visitId: String,
        encounters: List<NewEncounter>,
        berthVisits: List<BerthVisitInfo>,
        anchorVisits: List<AnchorStopInfo>,
        pilotAreaActivities: List<AreaActivity>
    ): PilotInfo? {
        // pilot can only be labeled as 'outbound' when it happened after the last berth visit
        val lastActivityTime = berthVisits.lastOrNull()?.end
            // If there is no berth visit then it could be that the ship had a pilot for only leaving the anchorage (e.g. SGSIN cases)
            ?: anchorVisits.lastOrNull()?.start
            ?: return null

        // Always use the end time of our last berth.
        // Otherwise, we potentially select a pilot encounter when still at berth,
        //  which is the encounter when the pilot goes on-board instead of off-board that we want to detect
        val outboundPilotEncounter = encounters.firstOrNull { encounter ->
            // By taking the first we don't take into account river pilots, but align with the decision made functionally
            encounter.start > lastActivityTime
        } ?: return null

        val pilotArea = findSingleOverlappingPilotAreaOrNull(outboundPilotEncounter, pilotAreaActivities)
        return PilotInfo.fromPilotShipEncounter(
            visitId = visitId,
            encounter = outboundPilotEncounter,
            pilotArea = pilotArea
        )
    }

    fun findOutboundPilotByAreaFallback(
        visitId: String,
        pilotAreaActivities: List<AreaActivity>,
        lastBerthVisit: BerthVisitInfo?,
    ): PilotInfo? {
        // determining pilot only makes sense when berths are involved. The last berth must be finished, it is not
        // possible for pilot area activity to take place when the ship is in a berth
        if (lastBerthVisit == null || lastBerthVisit.isOngoing()) {
            return null
        }

        // the first pilot area activity after the last berth visit
        val pilotAreaActivity = pilotAreaActivities.lastOrNull { activity ->
            activity.start > lastBerthVisit.end!!
        } ?: return null

        return if (pilotAreaActivity.isFinished()) {
            val pilotArea = infraService.getById(pilotAreaActivity.areaId, InfraAreaType.PILOT_BOARDING_PLACE)
                as? PilotBoardingPlace
            PilotInfo.fromPilotAreaActivity(
                visitId = visitId,
                pilotAreaActivity = pilotAreaActivity,
                pilotArea = pilotArea
            )
        } else {
            null
        }
    }

    fun generateLockStops(
        visitId: String,
        stops: List<NewStop>,
        portAreas: List<PortAreaInfo>
    ) = stops.filter { it.type == LOCK }.map { stop ->
        val lock = stop.areaId?.let { areaId -> infraService.getById(areaId, InfraAreaType.LOCK) as? Lock }
        val portArea = lock?.let { findPortArea(it, portAreas) }
        LockStopInfo(
            id = "$visitId.${stop.startEventId}",
            stop = stop,
            area = lock,
            portArea = portArea
        )
    }

    fun generateApproachAreaVisits(
        approachAreaActivities: List<AreaActivity>,
        portAreas: List<PortAreaInfo>
    ): List<ApproachAreaVisitInfo> {
        return approachAreaActivities.map { activity ->
            val area = infraService.getById(activity.areaId, InfraAreaType.APPROACH_AREA) as? ApproachArea
            val portArea = area?.let { findPortArea(it, portAreas) }
            ApproachAreaVisitInfo(activity, portArea, area)
        }
    }

    fun generateShipToShipTransfers(
        visitId: String,
        transfers: List<ShipToShipTransfer>
    ): List<ShipToShipTransferInfo> {
        return transfers.map { transfer ->
            val area = transfer.areaId?.let { infraService.getById(it, InfraAreaType.SHIP_TO_SHIP) } as? ShipToShipArea
            val otherShip = shipDetailsOrDefaultUnknown(imo = transfer.otherImo, mmsi = transfer.otherMmsi)
            ShipToShipTransferInfo(
                id = "$visitId.${transfer.startEventId}",
                transfer = transfer,
                area = area,
                otherShip = otherShip
            )
        }
    }

    private fun determinePortBoundaries(
        pilotInbound: StartEnd?,
        pilotOutbound: StartEnd?,
        portAreaActivities: List<AreaActivity>
    ): Pair<Instant?, Instant?> {
        val inPortStartBoundary = (pilotInbound?.start ?: portAreaActivities.firstOrNull()?.start)?.time
        val inPortEndBoundary = (pilotOutbound?.start ?: portAreaActivities.lastOrNull()?.end)?.time
        return Pair(inPortStartBoundary, inPortEndBoundary)
    }

    /**
     * Categorizes anchor stops in the [CategorizedPeriods] periods.
     */
    fun categorizeAnchorStops(
        anchorStops: List<AnchorStopInfo>,
        pilotInbound: StartEnd?,
        pilotOutbound: StartEnd?,
        portAreaActivities: List<AreaActivity>,
    ): CategorizedPeriods<AnchorStopInfo> {
        if (anchorStops.isEmpty()) {
            return CategorizedPeriods()
        }

        val (inPortStart, inPortEnd) = determinePortBoundaries(pilotInbound, pilotOutbound, portAreaActivities)

        return categorizePeriods(anchorStops, inPortStart, inPortEnd)
    }

    fun determineSlowMovingPeriods(
        initialSlowMovingPeriods: List<NewSlowMovingPeriod>?,
        berthVisits: List<BerthVisitInfo>,
        anchorStops: List<AnchorStopInfo>,
        lockStops: List<LockStopInfo>,
        pilotInbound: StartEnd?,
        pilotOutbound: StartEnd?,
        portAreaActivities: List<AreaActivity>,
    ): CategorizedPeriods<NewSlowMovingPeriod>? {
        // When the provided value is null we still have to calculate the slow moving periods
        if (initialSlowMovingPeriods == null) {
            return null
        }

        // We already calculated slow moving but there was none found
        if (initialSlowMovingPeriods.isEmpty()) {
            return CategorizedPeriods()
        }

        val slowMovingPeriods = removeOverlappingActivities(initialSlowMovingPeriods, berthVisits, anchorStops, lockStops)
        val (inPortStart, inPortEnd) = determinePortBoundaries(
            pilotInbound = pilotInbound,
            pilotOutbound = pilotOutbound,
            portAreaActivities = portAreaActivities
        )

        return categorizePeriods(slowMovingPeriods, inPortStart, inPortEnd)
    }

    /**
     * Categorizes the [StartEnd] periods in 3 buckets: arrival, in-port and departure. Keep in mind that the system may not be able to determine
     * one or both boundaries, especially in case of realtime processing. The system tries to place the item in the
     * buckets from end to start (departure > in-port > arrival), if a boundary could not be determined, the system
     * tries the next bucket towards the start.
     */
    fun <T : StartEnd> categorizePeriods(
        periods: List<T>,
        inPortStart: Instant?,
        inPortEnd: Instant?
    ): CategorizedPeriods<T> {
        val arrival = mutableListOf<T>()
        val inPort = mutableListOf<T>()
        val departure = mutableListOf<T>()

        periods.forEach { segment ->
            val time = segment.start.time
            if (inPortEnd != null && time >= inPortEnd) {
                departure.add(segment)
            } else if (inPortStart != null && time >= inPortStart) {
                inPort.add(segment)
            } else {
                arrival.add(segment)
            }
        }
        return CategorizedPeriods(arrival, inPort, departure)
    }

    fun removeOverlappingActivities(
        smp: List<NewSlowMovingPeriod>,
        berthVisits: List<BerthVisitInfo>,
        anchorStops: List<AnchorStopInfo>,
        lockStops: List<LockStopInfo>
    ): List<NewSlowMovingPeriod> {
        val all: List<StartEnd> = berthVisits + lockStops
        // return all periods that overlap none of the berth visits and anchor stop
        val filtered = smp.filter { period -> all.none { period.overlaps(it) } }

        var slowPeriods = filtered.toMutableList()
        anchorStops.forEach { anchor ->
            val newPeriods = mutableListOf<NewSlowMovingPeriod>()
            slowPeriods.forEach { period ->
                // get the part of slow moving period that does not overlap the anchor
                // if there's no overlap, then it is just the original slow moving period
                newPeriods.addAll(period.removeOverlapWithAnchorStop(anchor))
            }
            slowPeriods = newPeriods
        }

        return slowPeriods
    }

    /**
     * Returns the parts of the slow moving period (SMP) before and after the anchor stop. Or an empty list when the
     * SMP does not overlap the anchor stop.
     * Meaning:
     * - if the SMP starts before the anchor stop, copy the SMP and cut the end to the anchor start
     * - if the SMP ends after the anchor stop, copy the SMP and cut the start to the anchor end.
     */
    fun NewSlowMovingPeriod.removeOverlapWithAnchorStop(anchor: AnchorStopInfo): List<NewSlowMovingPeriod> {
        return if (this.overlaps(anchor)) {
            val before = if (start < anchor.start) {
                copy(
                    id = "${id}b",
                    start = start,
                    end = anchor.start
                )
            } else null
            val after = if (anchor.isFinished() && end > anchor.end!!) {
                copy(
                    id = "${id}a",
                    start = anchor.end,
                    end = end
                )
            } else null
            listOfNotNull(before, after)
        } else {
            listOf(this)
        }
    }

    /**
     * Align the berth visit by comparing the area activities from the:
     *
     * 1. AreaActivity from the [BerthVisitInfo] which is derived from a StopEvent
     * 2. AreaActivity from [activities] which is derived from a BerthEvent
     *
     * By intersecting the start & end times from these activities, the parts of the stop which are not in the berth
     * are excluded from the resulting [BerthVisitInfo]
     *
     * When no berthActivity is present, we use the original start and end times
     */
    private fun List<BerthVisitInfo>.alignWithBerthActivities(activities: List<AreaActivity>): List<BerthVisitInfo> {
        return this.map { visit ->
            // Retrieve all berth activities for the current visit
            val berthActivities = activities.filter { berthActivity ->
                // Should have the same area id and should overlap with the visit
                berthActivity.areaId == visit.area?._id && berthActivity.overlaps(visit)
            }

            if (berthActivities.isEmpty()) {
                log.debug { "No berth activities found for berth visit: ${visit.id} when creating SOF" }
            }

            val firstStart = berthActivities.minOfOrNull { it.start }
            val lastEnd = berthActivities.mapNotNull { it.end }.maxOrNull()

            // Retrieve the intersection between the time ranges or the original start when no berthActivity is present
            val start = firstStart?.let { maxOf(visit.start, it) } ?: visit.start
            val end = visit.end?.let { minOf(it, lastEnd ?: it) }

            visit.copy(
                activity = visit.activity.copy(start = start, end = end)
            )
        }
    }

    /**
     * Filter out the [TUG_WAITING_FOR_DEPARTURE] events that are not valid.
     * The system should only keep the encounters that are followed up with an actual [TUG] encounter.
     */
    private fun List<NewEncounter>.filterValidTugWaitingEvents(): List<NewEncounter> {
        val tugs = this.filter { it.type == EncounterEvent.EncounterType.TUG }
        val waitingForDepartureEncounters = this.filter { it.type == TUG_WAITING_DEPARTURE }

        val validWaitingForDepartureEncounters = tugs.map { tug ->
            retrieveMatchingWaitingForDepartureEncounter(tug, waitingForDepartureEncounters)
        }

        // Keep only the valid tug waiting encounters
        return this.filter { encounter ->
            encounter.type != TUG_WAITING_DEPARTURE || encounter in validWaitingForDepartureEncounters
        }
    }

    /**
     * Find the waiting for departure encounter that matches the tug encounter.
     */
    private fun retrieveMatchingWaitingForDepartureEncounter(tug: NewEncounter, waitingEncounters: List<NewEncounter>) =
        waitingEncounters.filter { waiting ->
            // Match encounters with the same service MMSI
            waiting.otherMmsi == tug.otherMmsi &&
                // Waiting departure should have ended
                waiting.end?.time != null &&
                // Ensure the waiting event ended before the tug started
                waiting.end?.time!! <= tug.start.time &&
                // Ensure the time gap is within the max duration
                Duration.between(waiting.end?.time, tug.start.time) < MAX_DURATION_DEPART_AND_TUG
            // Get the encounter closest to the tug encounter if multiple are present
        }.maxByOrNull { it.end?.time ?: Instant.MAX }

    /**
     * Find the pilot area based on overlapping with the [pilotEncounter], or null if multiple pilot area activities
     * match. The pilot encounter has no area id, the system can only try to find an overlapping pilot area activity,
     * matching event times.
     * For multiple matches, there is no way to determine the right pilot area, and therefore the system returns null.
     */
    private fun findSingleOverlappingPilotAreaOrNull(
        pilotActivity: StartEnd,
        pilotAreaActivities: List<AreaActivity>
    ): PilotBoardingPlace? = pilotAreaActivities
        .singleOrNull { activity -> pilotActivity.overlaps(activity) }
        ?.areaId
        ?.let { areaId -> infraService.getById(areaId, InfraAreaType.PILOT_BOARDING_PLACE) as? PilotBoardingPlace }

    private fun List<BerthVisitInfo>.attachTugs(allTugs: List<TugInfo>) = setTugs(allTugs, this)

    private fun LocationTime.toApi() = entryMapper.toApi(this)

    private fun List<StartEnd>.allFinished() = all { it.isFinished() }

    private fun shipDetailsOrDefaultUnknown(imo: Int?, mmsi: Int?): ShipDetails {
        return imo?.let { shipCacheService.getCacheByImo(it.toString())?.csi?.register?.toShipDetails() }
            ?: mmsi?.let { shipCacheService.getCachyByMmsi(it.toString())?.csi?.register?.toShipDetails() }
            ?: ShipDetails(
                mmsi = mmsi?.toString() ?: "",
                imo = imo.toString(),
                name = null,
                type = null,
                categories = null,
                length = null,
                beam = null,
                maxDraught = null,
                dwt = null
            )
    }

    /** Set the start time of a tug to the start of the waiting departure encounter */
    private fun List<TugInfo>.alignDepartureStartTimes() =
        this.map {
            it.copy(
                encounter = it.encounter.copy(start = it.waitingForDepartureEncounter?.start ?: it.encounter.start)
            )
        }

    /** Helper class to collect berth visits for a terminal visit in a fold operation */
    data class BerthVisitGroup(
        val terminalId: String,
        val visits: MutableList<BerthVisitInfo>
    )
}
