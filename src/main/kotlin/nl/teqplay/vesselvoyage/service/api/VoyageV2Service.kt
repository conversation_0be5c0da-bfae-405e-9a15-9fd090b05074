package nl.teqplay.vesselvoyage.service.api

import nl.teqplay.skeleton.common.exception.BadRequestException
import nl.teqplay.skeleton.common.exception.NotFoundException
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.datasource.NewVoyageDataSource
import nl.teqplay.vesselvoyage.model.internal.MinimalNewVoyage
import nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter
import nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter.ANY
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import nl.teqplay.vesselvoyage.service.InfraService
import org.springframework.stereotype.Service
import java.time.Instant

@ProfileProcessing
@ProfileApi
@Service
class VoyageV2Service(
    private val voyageDataSource: NewVoyageDataSource,
    private val infraService: InfraService
) : BaseApiV2Service<NewVoyage, NewVoyageDataSource>(voyageDataSource) {
    fun findByPortUnlocode(
        originPortUnlocodes: List<String>,
        destinationPortUnlocodes: List<String>,
        start: Instant?,
        end: Instant?,
        limit: Int?,
        finishedState: NewEntryFinishedFilter = ANY,
        qualifyingImos: Set<Int>
    ): List<NewVoyage> {
        val originAreaIds = originPortUnlocodes.map { unlocode ->
            infraService.getPortByUnlocode(unlocode)?._id
                ?: throw NotFoundException("An origin port with unlocode $unlocode does not exist in the system")
        }
        val destinationAreaIds = destinationPortUnlocodes.map { unlocode ->
            infraService.getPortByUnlocode(unlocode)?._id
                ?: throw NotFoundException("A destination port with unlocode $unlocode does not exist in the system")
        }
        return findByPortAreaId(originAreaIds, destinationAreaIds, start, end, limit, finishedState, qualifyingImos)
    }

    fun findByPortAreaId(
        originPortAreaIds: List<String>,
        destinationPortAreaIds: List<String>,
        start: Instant?,
        end: Instant?,
        limit: Int?,
        finishedState: NewEntryFinishedFilter = ANY,
        qualifyingImos: Set<Int>
    ): List<NewVoyage> {
        if (originPortAreaIds.isEmpty() && destinationPortAreaIds.isEmpty()) {
            throw BadRequestException("Origin or destination not set. Expected at least one or both")
        }
        return voyageDataSource.findByPortAreaIds(
            originPortAreaIds,
            destinationPortAreaIds,
            start,
            end,
            limit,
            finishedState,
            qualifyingImos
        )
    }

    fun findByAisDestination(
        aisDestinationUnlocodes: List<String>,
        start: Instant?,
        end: Instant?,
        limit: Int?,
        finishedState: NewEntryFinishedFilter = ANY,
        qualifyingImos: Set<Int>
    ) = voyageDataSource.findByAisDestination(
        aisDestinationUnlocodes,
        start,
        end,
        limit,
        finishedState,
        qualifyingImos
    )

    fun findByOriginPortUnlocodeAndImos(
        originPortUnlocode: String,
        imos: Set<Int>,
        finished: NewEntryFinishedFilter = ANY
    ): List<MinimalNewVoyage> {
        val resolvedPortId = resolvePomaIdFromUnlocode(originPortUnlocode)

        return voyageDataSource.findMinimalByPortAreaIdAndImos(
            originPortAreaId = resolvedPortId,
            imos = imos,
            finished = finished
        )
    }

    fun findByAisDestinationAndImos(
        imos: Set<Int>,
        aisDestinationUnlocode: String,
        finished: NewEntryFinishedFilter
    ): List<MinimalNewVoyage> {
        return voyageDataSource.findMinimalByAisDestinationAndImos(
            aisDestinationUnlocode = aisDestinationUnlocode,
            imos = imos,
            finished = finished
        )
    }

    private fun resolvePomaIdFromUnlocode(unlocode: String): String {
        val pomaId = infraService.getPortByUnlocode(unlocode)?._id
            ?: throw NotFoundException("A port with the given unlocode does not exist in the system")

        return pomaId
    }
}
