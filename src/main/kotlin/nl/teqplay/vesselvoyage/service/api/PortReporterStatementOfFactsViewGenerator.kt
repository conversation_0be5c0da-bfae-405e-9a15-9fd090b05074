package nl.teqplay.vesselvoyage.service.api

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.apiv2.model.sof.portreporter.AreaMeta
import nl.teqplay.vesselvoyage.apiv2.model.sof.portreporter.PortReporterStatementOfFactsView
import nl.teqplay.vesselvoyage.apiv2.model.sof.portreporter.Ship
import nl.teqplay.vesselvoyage.mapper.EntryV2Mapper
import nl.teqplay.vesselvoyage.mapper.PortReporterStatementOfFactsMapper
import nl.teqplay.vesselvoyage.model.MAX_DURATION_DEPART_AND_TUG
import nl.teqplay.vesselvoyage.model.esof.portreporterview.AnchorStopInfo
import nl.teqplay.vesselvoyage.model.esof.portreporterview.BerthVisitInfo
import nl.teqplay.vesselvoyage.model.esof.portreporterview.EncounterInfo
import nl.teqplay.vesselvoyage.model.esof.portreporterview.PilotInfo
import nl.teqplay.vesselvoyage.model.esof.portreporterview.PortAreaInfo
import nl.teqplay.vesselvoyage.model.esof.portreporterview.TerminalVisitInfo
import nl.teqplay.vesselvoyage.model.esof.portreporterview.TugInfo
import nl.teqplay.vesselvoyage.model.esof.portreporterview.UnclassifiedStopInfo
import nl.teqplay.vesselvoyage.model.internal.InfraAreaType
import nl.teqplay.vesselvoyage.model.lightweight.poma.Anchorage
import nl.teqplay.vesselvoyage.model.lightweight.poma.Berth
import nl.teqplay.vesselvoyage.model.lightweight.poma.PilotBoardingPlace
import nl.teqplay.vesselvoyage.model.lightweight.poma.PomaModel
import nl.teqplay.vesselvoyage.model.lightweight.poma.Port
import nl.teqplay.vesselvoyage.model.lightweight.poma.Terminal
import nl.teqplay.vesselvoyage.model.v2.AreaActivity
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewESoF
import nl.teqplay.vesselvoyage.model.v2.NewEncounter
import nl.teqplay.vesselvoyage.model.v2.NewStop
import nl.teqplay.vesselvoyage.model.v2.NewStopType.ANCHOR_AREA
import nl.teqplay.vesselvoyage.model.v2.NewStopType.BERTH
import nl.teqplay.vesselvoyage.model.v2.NewStopType.UNCLASSIFIED
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.StartEnd
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.ShipCacheService
import nl.teqplay.vesselvoyage.util.contains
import nl.teqplay.vesselvoyage.util.endsIn
import nl.teqplay.vesselvoyage.util.isFinished
import nl.teqplay.vesselvoyage.util.isOngoing
import nl.teqplay.vesselvoyage.util.mapWithSurrounding
import nl.teqplay.vesselvoyage.util.overlaps
import nl.teqplay.vesselvoyage.util.ports
import nl.teqplay.vesselvoyage.util.startsIn
import nl.teqplay.vesselvoyage.util.toShipDetails
import org.springframework.stereotype.Service
import java.time.Duration
import java.time.Instant
import java.util.UUID

@ProfileProcessing
@ProfileApi
@Service
class PortReporterStatementOfFactsViewGenerator(
    private val infraService: InfraService,
    private val mapper: PortReporterStatementOfFactsMapper,
    private val entryMapper: EntryV2Mapper,
    private val shipCacheService: ShipCacheService
) {

    private val log = KotlinLogging.logger {}

    companion object {
        const val version = 1

        /** Maximum allowed time before actual berth visit (in minutes) to consider a tug as assisting with a ship's arrival at a specific berth */
        const val TUG_ARRIVAL_TOLERANCE = 15L

        /** Maximum allowed time after actual berth visit (in minutes) to consider a tug as assisting with a ship's departure at a specific berth */
        const val TUG_DEPARTURE_TOLERANCE = 15L
    }

    fun generate(
        visit: NewVisit,
        esof: NewESoF?,
    ): PortReporterStatementOfFactsView {
        val ship = shipDetailsOrDefaultUnknown(visit.imo)
        val area = generateEospAreaMeta(areaId = visit.eospAreaActivity.areaId)

        val portAreas = generatePortAreas(visit.portAreaActivities, visit.stops)
        val berthStops = visit.stops.filter { it.type == BERTH }

        val encounters = esof?.encounters?.filterValidTugWaitingEvents()
        val tugEncounters = encounters?.filter { it.type == EncounterType.TUG }
            ?: emptyList()
        val waitingDepartureEncounters = encounters?.filter { it.type == EncounterType.TUG_WAITING_DEPARTURE }
            ?: emptyList()

        val allTugs = generateTugs(tugEncounters, waitingDepartureEncounters)
        val berthVisitsWithoutTerminalRef = generateBerthVisits(berthStops, portAreas)
            .attachTugs(allTugs)
            .alignWithBerthActivities(visit.berthAreaActivities)

        val terminalVisits = generateTerminalVisits(
            groupBerthVisitsByTerminal(berthVisitsWithoutTerminalRef),
            visit.terminalMooringAreaActivities
        )

        // set the berthVisit.terminalVisit where applicable, so the mapper can resolve the berthVisit.terminalVisitRef
        val berthVisits = populateBerthVisitsWithTerminalVisit(berthVisitsWithoutTerminalRef, terminalVisits)

        val anchorStops = generateAnchorStops(visit.stops, portAreas)
        val unclassifiedStops = generateUnclassifiedStops(
            stops = visit.stops,
            portAreas = portAreas,
            eospAreaId = visit.eospAreaActivity.areaId.removeSuffix(".eosp")
        )

        val encounterInfo = encounters?.let {
            generateEncounters(it, portAreas, berthVisits)
        }

        val pilotEncounters = encounters?.filter { it.type == EncounterType.PILOT }
        val pilotInbound =
            pilotEncounters?.let { findInboundPilot(it, berthVisits, anchorStops, visit.pilotAreaActivities) }
                ?: findInboundPilotByAreaFallback(visit.pilotAreaActivities, berthVisits.firstOrNull())
        val pilotOutbound =
            pilotEncounters?.let { findOutboundPilot(it, berthVisits, anchorStops, visit.pilotAreaActivities) }
                ?: findOutboundPilotByAreaFallback(visit.pilotAreaActivities, berthVisits.lastOrNull())

        return PortReporterStatementOfFactsView(
            version = version,
            generationId = UUID.randomUUID().toString(),
            entryId = visit._id,
            previousVoyageId = visit.previous,
            nextVoyageId = visit.next,
            start = visit.start.toApi(),
            end = visit.end?.toApi(),
            area = area,
            ship = ship,
            portAreas = portAreas.map(mapper::toPortArea),
            berthVisits = berthVisits.map(mapper::toBerthVisit),
            terminalVisits = terminalVisits.map(mapper::toTerminalVisit),
            anchorStops = anchorStops.map(mapper::toAnchorStop),
            unclassifiedStops = unclassifiedStops.map(mapper::toUnclassifiedStop),
            encounters = encounterInfo?.map(mapper::toEncounter) ?: emptyList(),
            pilotInbound = pilotInbound?.let(mapper::toPilot),
            pilotOutbound = pilotOutbound?.let(mapper::toPilot),
        )
    }

    private fun generateEospAreaMeta(areaId: String): AreaMeta? {
        val portAreaId = areaId.removeSuffix(".eosp")
        val port = infraService.getById(portAreaId, InfraAreaType.PORT) as? Port
            // It can be possible that the port doesn't exist anymore for more older data
            ?: return null
        val portAreaMeta = mapper.toAreaMeta(port = port)

        // Keep the areaId from poma as that is what is used to find the port back in poma
        return portAreaMeta.copy(
            type = "eosp"
        )
    }

    fun generatePortAreas(
        portAreaActivities: List<AreaActivity>,
        stops: List<NewStop>
    ): List<PortAreaInfo> = portAreaActivities.mapIndexedNotNull { index, portActivity ->
        val port = infraService.getById(portActivity.areaId, InfraAreaType.PORT) as? Port
        if (port != null) {
            PortAreaInfo(
                ref = index,
                activity = portActivity,
                isPassThrough = isPassingThroughPortActivity(portActivity, stops),
                area = port
            )
        } else {
            null
        }
    }

    /** Whether the ship did not stop in the given port area, in other words: just passing through */
    fun isPassingThroughPortActivity(
        portActivity: AreaActivity,
        stops: List<NewStop>
    ): Boolean {
        // it doesn't matter here whether the full visit is finished,
        // we're only interested in stops inside the given port area
        return if (portActivity.isFinished()) {
            // the port activity is finished, therefore all stops inside the port area should be finished
            stops.none { stop -> stop in portActivity }
        } else {
            // the port activity is ongoing, if a stop has started inside this ongoing port activity, it is safe
            // to assume the ship will not just pass through
            stops.none { stop -> stop.startsIn(portActivity) }
        }
    }

    private fun generateBerthVisits(berthStops: List<NewStop>, portAreas: List<PortAreaInfo>): List<BerthVisitInfo> {
        return berthStops.mapIndexed { index, berthStop ->
            val berth = berthStop.areaId?.let { infraService.getBerth(it) }
            val berthActivity = AreaActivity(id = index.toString(), berthStop.start, berthStop.end, berthStop.areaId!!)
            BerthVisitInfo(
                ref = index,
                activity = berthActivity,
                area = berth,
                portArea = berth?.let { matchBerthVisitToPortArea(berthActivity, portAreas, it) },
                // properties below can only be derived when all berth visits are calculated
                arrivalTugs = emptyList(),
                departureTugs = emptyList(),
                firstLineSecured = null,
                allFast = null,
                lastLineReleased = null,
                terminalVisit = null
            )
        }
    }

    /**
     * Returns berth visits grouped by what would be considered a terminal visit. The system does not have a register
     * of terminal area stops, therefore it derives them from visits to berths belonging to the same terminal.
     *
     * Note that visits to berths that are not part of a terminal, are not returned!
     *
     * A terminal visit is derived from berth visits that: (1) are adjacent and (2) have the same terminal id.
     * Note that the system needs to respect the timeline of berth visits: if the system would just use groupBy,
     * visits to the same terminal may be concatenated while there was a different terminal in between.
     *
     * Examples:
     * --[berth 1]--[berth 2]--[berth 3]--[berth 1]--
     * --[    terminal A    ]-------------[term. A]-- -> should result in 2 terminal visits! A - A
     * --[    terminal A    ]--[term. B]--[term. A]-- -> should result in 3 terminal visits: A - B - A
     */
    fun groupBerthVisitsByTerminal(berthVisits: List<BerthVisitInfo>): List<BerthVisitGroup> {
        // use an invalid value instead of null, to prevent nullability in BerthVisitGroup terminalId
        val invalidTerminalId = ""
        return berthVisits.fold(mutableListOf<BerthVisitGroup>()) { groupedVisits, berthVisit ->
            val terminalId = berthVisit.area?.terminalId ?: invalidTerminalId

            // create a new group when the terminalId changes
            // otherwise add it to the last created group
            val newSubList = groupedVisits.isEmpty() || groupedVisits.last().terminalId != terminalId
            if (newSubList) {
                groupedVisits.add(BerthVisitGroup(terminalId, mutableListOf(berthVisit)))
            } else {
                groupedVisits.last().visits += berthVisit
            }
            groupedVisits
        }.filter { group ->
            // only return visit groups that belong to a terminal
            group.terminalId != invalidTerminalId
        }
    }

    fun generateTerminalVisits(
        berthVisitGroups: List<BerthVisitGroup>,
        terminalMoorings: List<AreaActivity>
    ): List<TerminalVisitInfo> {
        // Create terminal visits from consecutive berth visits having the same terminal id AND same port area id.
        // In other words, fold berth visits with those criteria into terminal visits
        // Note that a simple berthVisits.groupBy() would not suffice, as a berth may be visited multiple times, and
        // those visits are likely not consecutive. In other words, groupBy() would not honor the order of events.
        // For example, assume terminal 1 with berth A and B, and terminal 2 with berth C, and berth D without terminal
        // berth A -> berth A = 1 terminal visit
        // berth A -> berth B = 1 terminal visit
        // berth A -> anchorage inside port area -> berth B = 1 terminal visit
        // berth A -> anchorage outside port area -> berth B = 2 terminal visits
        // berth A -> berth C -> berth A = 3 terminal visits. Visits to A are not combined, as interrupted by C

        // ---[berth1]--[berth2]--[berth3]--
        // ---[   terminal A   ]--|--------- -> 1 terminal visit for A
        // ---[term.A]------------[term.B]-- ->

        return berthVisitGroups.mapIndexedNotNull { index, groupedVisits ->
            val (terminalId: String, visits: List<BerthVisitInfo>) = groupedVisits
            if (visits.isEmpty()) {
                return@mapIndexedNotNull null
            }

            val firstBerth = visits.first()
            val terminalMooring = terminalMoorings
                .filter { activity -> "$terminalId.mooringarea" == activity.areaId }
                // pick closest to first berth visit of the group, multiple terminal visits cause multiple mooring times
                .minByOrNull { mooring -> Duration.between(mooring.start.time, firstBerth.start.time) }
            TerminalVisitInfo(
                ref = index,
                activity = AreaActivity(
                    id = "derived-terminal-visit-ref-$index",
                    start = firstBerth.start,
                    end = visits.last().end,
                    areaId = terminalId
                ),
                mooringActivity = terminalMooring,
                area = infraService.getById(terminalId, InfraAreaType.TERMINAL) as? Terminal,
                portArea = firstBerth.portArea,
                berthVisits = visits
            )
        }
    }

    fun matchBerthVisitToPortArea(
        berthActivity: AreaActivity,
        portAreas: List<PortAreaInfo>,
        berth: Berth
    ): PortAreaInfo? = portAreas.find { (_, _, activity: AreaActivity, port: Port) ->
        berthActivity.startsIn(activity) &&
            port._id != null &&
            (port._id == berth.mainPort || port._id in berth.ports || port.unlocode in berth.ports)
    }

    fun generateTugs(
        tugEncounters: List<NewEncounter>,
        waitingDepartureEncounter: List<NewEncounter>
    ) = tugEncounters.mapIndexed { index, encounter ->
        TugInfo(
            ref = index,
            encounter,
            waitingForDepartureEncounter = retrieveMatchingWaitingForDepartureEncounter(
                tug = encounter,
                waitingEncounters = waitingDepartureEncounter
            )
        )
    }

    fun findArrivalTugs(
        previousStop: StartEnd?,
        currentStop: StartEnd,
        allTugs: List<TugInfo>
    ): List<TugInfo> {

        // Arrival tugs for berth 2:
        // ------[  berth 1  ]--------------[    berth 2   ]------
        // ------[      candidate window                   ]------
        // ---------------[         tug          ]-------------- <-- arrival tug for berth 2, in window,
        // -----------------------------[   tug  ]-------------- <-- arrival tug for berth 2
        // --------------------------------------[tug]---------- <-- NOT arrival tug, tug.start not before berth.start
        // ----------------[tug]-------------------------------- <-- NOT arrival tug, tug.end not in berth2
        // ---[   tug  ]---------------------------------------- <-- NOT arrival tug, tug not overlapping berth2

        return allTugs.filter { tug ->
            // Tug must start before current berth.start, otherwise there is no reason for the tug to be involved in
            // berth arrival
            // In case there is a previous stop known, expect the tug to start in or after previous stop. Longer tug
            // encounters are usually not tug operations (from experience in pto)
            val startsBeforeCurrentBerth = if (previousStop != null) {
                tug.start < currentStop.start && tug.start >= previousStop.start
            } else {
                tug.start < currentStop.start
            }

            // The tug.end should end somewhere after arrival (berth.start).
            // A tug ending before or after the currentStop would not make sense for tugging the ship for arrival
            val endsOrOngoingInsideCurrentBerth = if (currentStop.isFinished()) {
                // Add a tolerance check to include encounters that end just before the berth visit starts.
                // This ensures that tugs with low AIS reporting frequency are still considered.
                tug.endsIn(currentStop, startTolerance = TUG_ARRIVAL_TOLERANCE)
            } else {
                // the berth visit is still ongoing:
                // if the tug encounter is finished, then we need the encounter.end after the
                // else the tug encounter is ongoing, then we assume the tug is still tugging the ship
                tug.isOngoing() || tug.end!! > currentStop.start
            }

            log.debug {
                "tug ${tug.imo ?: tug.mmsi} ${tug.start.time} / ${tug.end?.time}, " +
                    "starts before current berth: $startsBeforeCurrentBerth, " +
                    "ends or ongoing inside current berth: $startsBeforeCurrentBerth"
            }

            startsBeforeCurrentBerth && endsOrOngoingInsideCurrentBerth
        }
    }

    fun findDepartureTugs(
        currentStop: StartEnd,
        nextStop: StartEnd?,
        allTugs: List<TugInfo>
    ): List<TugInfo> {

        // Departure tugs for berth 1:
        // ------[  berth 1  ]--------------[    berth 2   ]------
        // ------[      candidate window                   ]------
        // ---[   tug  ]---------------------------------------- <-- NOT departure tug, tug.start not in berth 1
        // --------[tug]---------------------------------------- <-- NOT departure tug, tug.end not after berth 1
        // ---------------[         tug          ]-------------- <-- departure tug, start in berth 1, ends in berth 2
        // ---------------[   tug   ]--------------------------- <-- departure tug, starts in berth1, ends after berth1
        // ---------------[   tug                             ]- <-- NOT departure tug, ends after berth 2 (nextstop)
        // -----------------------------[   tug  ]-------------- <-- NOT departure tug, does not start in berth 1
        // --------------------[ tug ]-------------------------- <-- departure tug, starts after berth1 in first half of in-between time
        // -------------------------[ tug ]--------------------- <-- NOT departure tug, starts after berth1 in second half of in-between time

        return allTugs.filter { tug ->

            // Tug must start before current berth.start, otherwise there is no reason for the tug to be there
            // Add a tolerance check to include encounters that start just after the berth visit ends.
            // This ensures that tugs with low AIS reporting frequency are still considered.
            val startsInCurrentBerth = tug.startsIn(other = currentStop, endTolerance = TUG_DEPARTURE_TOLERANCE)

            if (!startsInCurrentBerth) {
                // Tug was not during the berth visit so check if it started before the next stop
                if (currentStop.isFinished()) {
                    val currentEndTime = currentStop.end!!.time
                    val nextStartTime = nextStop?.start?.time
                    val tugStartTime = tug.start.time

                    // Skip tugs that either started before our current stop or after
                    if (currentEndTime > tugStartTime || (nextStartTime != null && nextStartTime < tugStartTime)) {
                        return@filter false
                    }

                    val halfTimeBetweenStops = nextStartTime?.let { Duration.between(currentEndTime, nextStartTime) }
                        ?.dividedBy(2) // Take half of the duration

                    if (halfTimeBetweenStops != null) {
                        // This tug is a departure tug when it started in the first half of the time between the current and next stop
                        return@filter tugStartTime.isBefore(currentEndTime + halfTimeBetweenStops)
                    }

                    // Tug happened after current stop end and no next stop happened
                    return@filter true
                }

                return@filter false
            }

            // and check the tug.end to see whether the tug qualifies as departure tug
            // current is ongoing && no next -> tug start in current && ongoing
            // current is finished && no next -> tug start in current && (tug ongoing || ends after current)
            // current is finished && next ongoing -> tug start in current && (tug ongoing || end after current)
            // current is finished && next finished -> tug finished && (start in current && end < next.end

            if (currentStop.isOngoing() && nextStop == null) {
                tug.isOngoing()
            } else if (currentStop.isFinished() && nextStop == null) {
                // departure is still ongoing or tug should have stopped after current stop
                tug.isOngoing() || tug.end!! > currentStop.end!!
            } else if (currentStop.isFinished() && nextStop?.isOngoing() == true) {
                // if tug is ongoing, it finished the departure, but is also tugging for arrival at next berth
                // or, tug only tugged with departure from currentStop
                tug.isOngoing() || tug.end!! > currentStop.end!!
            } else if (currentStop.isFinished() && nextStop?.isFinished() == true) {
                // tug must have ended after current stop AND (before next stop OR in next stop)
                tug.isFinished() && tug.end!! >= currentStop.end!! && (tug.end < nextStop.start || tug.endsIn(nextStop))
            } else {
                // other cases are not possible for departure tugs
                false
            }
        }
    }

    fun setTugs(allTugs: List<TugInfo>, berthVisits: List<BerthVisitInfo>): List<BerthVisitInfo> {
        return berthVisits.mapWithSurrounding { previous, current, next ->

            log.debug {
                "Finding tugs for berth visit ${current.ref} (${current.area?.name}): " +
                    "${current.start.time} / ${current.end?.time}"
            }

            val arrivalTugs = findArrivalTugs(previous?.activity, current, allTugs)
            val departureTugs = findDepartureTugs(current, next?.activity, allTugs)
                // Set the start times of tugs to the waiting depature encounter start time if available
                .alignDepartureStartTimes()

            // first tug leaving the ship in the arrival process
            val firstLineSecured: LocationTime?
            // last tug leaving the ship in the arrival process
            val allFast: LocationTime?
            // last tug arriving at the ship in the departure process
            val lastLineReleased: LocationTime?

            if (arrivalTugs.isNotEmpty() && arrivalTugs.allFinished()) {
                firstLineSecured = arrivalTugs.minByOrNull { it.end!! }?.end
                allFast = arrivalTugs.maxByOrNull { it.end!! }?.end
            } else {
                firstLineSecured = null
                allFast = null
            }

            if (departureTugs.isNotEmpty() && departureTugs.allFinished()) {
                lastLineReleased = departureTugs.minByOrNull { it.start }?.start
            } else {
                lastLineReleased = null
            }

            current.copy(
                arrivalTugs = arrivalTugs,
                departureTugs = departureTugs,
                firstLineSecured = firstLineSecured,
                allFast = allFast,
                lastLineReleased = lastLineReleased
            )
        }
    }

    fun populateBerthVisitsWithTerminalVisit(
        berthVisits: List<BerthVisitInfo>,
        terminalVisits: List<TerminalVisitInfo>
    ): List<BerthVisitInfo> {
        // this map only contains berthVisits that have a terminal visit
        val berthTerminalVisitMapping = terminalVisits.flatMap { terminalVisit ->
            terminalVisit.berthVisits.map { berthVisit -> berthVisit to terminalVisit }
        }.toMap()

        // attach the terminal visit to the berth visits
        // or if not available, just return the existing berth visit
        return berthVisits.map { berthVisit ->
            berthTerminalVisitMapping[berthVisit]
                ?.let { terminalVisit -> berthVisit.copy(terminalVisit = terminalVisit) }
                ?: berthVisit
        }
    }

    fun generateAnchorStops(stops: List<NewStop>, portAreas: List<PortAreaInfo>): List<AnchorStopInfo> {
        return stops.filter { it.type == ANCHOR_AREA }.map { stop ->
            val anchorage = stop.areaId?.let { infraService.getById(it, InfraAreaType.ANCHOR) } as? Anchorage
            val portAreaInfo = anchorage?.let { findPortArea(it, portAreas) }
            AnchorStopInfo(stop, anchorage, portAreaInfo)
        }
    }

    fun findPortArea(area: PomaModel, portAreas: List<PortAreaInfo>) = portAreas.find { portArea ->
        val portId = portArea.area._id
        portId != null && portId in area.ports()
    }

    fun generateUnclassifiedStops(
        stops: List<NewStop>,
        portAreas: List<PortAreaInfo>,
        eospAreaId: String
    ): List<UnclassifiedStopInfo> {
        return stops.filter { it.type == UNCLASSIFIED }.map { stop ->
            val portArea = portAreas.firstOrNull { portArea ->
                stop.startsIn(portArea.activity) && portArea.activity.areaId == eospAreaId
            }
            UnclassifiedStopInfo(stop, portArea)
        }
    }

    fun generateEncounters(
        encounters: List<NewEncounter>,
        portAreas: List<PortAreaInfo>,
        berthVisits: List<BerthVisitInfo>,
    ): List<EncounterInfo> {
        return encounters.map { encounter ->
            // the encounter may overlap multiple berth visits, but we're picking the first here
            // overlapping multiple berth visits does not happen often
            val berthVisit = berthVisits.firstOrNull { berthVisit -> encounter.overlaps(berthVisit) }
            EncounterInfo(
                encounter = encounter,
                portAreaInfo = berthVisit?.portArea ?: portAreas.firstOrNull(),
                berthVisitInfo = berthVisit,
                terminalVisitInfo = berthVisit?.terminalVisit,
            )
        }
    }

    /**
     * Finds the last pilot before the first berth.
     * There may be multiple inbound pilots, see [PtoStatementOfFactsView.encounters] for all pilot encounters.
     */
    fun findInboundPilot(
        encounters: List<NewEncounter>,
        berthVisits: List<BerthVisitInfo>,
        anchorVisits: List<AnchorStopInfo>,
        pilotAreaActivities: List<AreaActivity>
    ): PilotInfo? {
        // pilot can only be labeled as 'inbound' when it happened before the first berth visit
        val firstActivityTime = berthVisits.firstOrNull()?.start
            // If there is no berth visit then it could be that the ship had a pilot for only going to the anchorage (e.g. SGSIN cases)
            ?: anchorVisits.firstOrNull()?.start
            ?: return null

        // Take the first pilot before we went to the first berth as that is when the pilot goes on board
        // If we take the last one we would potentially select the moment the pilot went off board
        val pilotEncounter = encounters.firstOrNull { encounter ->
            encounter.start < firstActivityTime
        } ?: return null

        val pilotArea = findSingleOverlappingPilotAreaOrNull(pilotEncounter, pilotAreaActivities)
        return PilotInfo.fromPilotShipEncounter(pilotEncounter, pilotArea)
    }

    fun findInboundPilotByAreaFallback(
        pilotAreaActivities: List<AreaActivity>,
        firstBerthVisit: BerthVisitInfo?,
    ): PilotInfo? {
        if (firstBerthVisit == null) {
            return null
        }

        // the last pilot area activity before the first berth visit
        val pilotAreaActivity = pilotAreaActivities.lastOrNull { encounter ->
            encounter.isFinished() && encounter.end!! < firstBerthVisit.start
        } ?: return null

        return if (pilotAreaActivity.isFinished()) {
            val pilotArea = infraService.getById(pilotAreaActivity.areaId, InfraAreaType.PILOT_BOARDING_PLACE)
                as? PilotBoardingPlace
            PilotInfo.fromPilotAreaActivity(pilotAreaActivity, pilotArea)
        } else {
            null
        }
    }

    /**
     * Finds the first pilot after the last berth.
     * There may be multiple outbound pilots, see [PtoStatementOfFactsView.encounters] for all pilot encounters.
     */
    fun findOutboundPilot(
        encounters: List<NewEncounter>,
        berthVisits: List<BerthVisitInfo>,
        anchorVisits: List<AnchorStopInfo>,
        pilotAreaActivities: List<AreaActivity>
    ): PilotInfo? {
        // pilot can only be labeled as 'outbound' when it happened after the last berth visit
        val lastActivityTime = berthVisits.lastOrNull()?.end
            // If there is no berth visit then it could be that the ship had a pilot for only leaving the anchorage (e.g. SGSIN cases)
            ?: anchorVisits.lastOrNull()?.start
            ?: return null

        // Always use the end time of our last berth.
        // Otherwise, we potentially select a pilot encounter when still at berth,
        //  which is the encounter when the pilot goes on-board instead of off-board that we want to detect
        val outboundPilotEncounter = encounters.firstOrNull { encounter ->
            // By taking the first we don't take into account river pilots, but align with the decision made functionally
            encounter.start > lastActivityTime
        } ?: return null

        val pilotArea = findSingleOverlappingPilotAreaOrNull(outboundPilotEncounter, pilotAreaActivities)
        return PilotInfo.fromPilotShipEncounter(outboundPilotEncounter, pilotArea)
    }

    fun findOutboundPilotByAreaFallback(
        pilotAreaActivities: List<AreaActivity>,
        lastBerthVisit: BerthVisitInfo?,
    ): PilotInfo? {
        // determining pilot only makes sense when berths are involved. The last berth must be finished, it is not
        // possible for pilot area activity to take place when the ship is in a berth
        if (lastBerthVisit == null || lastBerthVisit.isOngoing()) {
            return null
        }

        // the first pilot area activity after the last berth visit
        val pilotAreaActivity = pilotAreaActivities.lastOrNull { activity ->
            activity.start > lastBerthVisit.end!!
        } ?: return null

        return if (pilotAreaActivity.isFinished()) {
            val pilotArea = infraService.getById(pilotAreaActivity.areaId, InfraAreaType.PILOT_BOARDING_PLACE)
                as? PilotBoardingPlace
            PilotInfo.fromPilotAreaActivity(pilotAreaActivity, pilotArea)
        } else {
            null
        }
    }

    /**
     * Align the berth visit by comparing the area activities from the:
     *
     * 1. AreaActivity from the [BerthVisitInfo] which is derived from a StopEvent
     * 2. AreaActivity from [activities] which is derived from a BerthEvent
     *
     * By intersecting the start & end times from these activities, the parts of the stop which are not in the berth
     * are excluded from the resulting [BerthVisitInfo]
     *
     * When no berthActivity is present, we use the original start and end times
     */
    private fun List<BerthVisitInfo>.alignWithBerthActivities(activities: List<AreaActivity>): List<BerthVisitInfo> {
        return this.map { visit ->
            // Retrieve all berth activities for the current visit
            val berthActivities = activities.filter { berthActivity ->
                // Should have the same area id and should overlap with the visit
                berthActivity.areaId == visit.area?._id && berthActivity.overlaps(visit)
            }

            if (berthActivities.isEmpty()) {
                log.debug { "No berth activities found for berth visit: ${visit.ref} when creating SOF" }
            }

            val firstStart = berthActivities.minOfOrNull { it.start }
            val lastEnd = berthActivities.mapNotNull { it.end }.maxOrNull()

            // Retrieve the intersection between the time ranges or the original start when no berthActivity is present
            val start = firstStart?.let { maxOf(visit.start, it) } ?: visit.start
            val end = visit.end?.let { minOf(it, lastEnd ?: it) }

            visit.copy(
                activity = visit.activity.copy(start = start, end = end)
            )
        }
    }

    /**
     * Filter out the [TUG_WAITING_DEPARTURE] events that are not valid.
     * The system should only keep the encounters that are followed up with an actual [TUG] encounter.
     */
    private fun List<NewEncounter>.filterValidTugWaitingEvents(): List<NewEncounter> {
        val tugs = this.filter { it.type == EncounterType.TUG }
        val waitingForDepartureEncounters = this.filter { it.type == EncounterType.TUG_WAITING_DEPARTURE }

        val validWaitingForDepartureEncounters = tugs.map { tug ->
            retrieveMatchingWaitingForDepartureEncounter(tug, waitingForDepartureEncounters)
        }

        // Keep only the valid tug waiting encounters
        return this.filter { encounter ->
            encounter.type != EncounterType.TUG_WAITING_DEPARTURE || encounter in validWaitingForDepartureEncounters
        }
    }

    /**
     * Find the waiting for departure encounter that matches the tug encounter.
     */
    private fun retrieveMatchingWaitingForDepartureEncounter(tug: NewEncounter, waitingEncounters: List<NewEncounter>) =
        waitingEncounters.filter { waiting ->
            // Match encounters with the same service MMSI
            waiting.otherMmsi == tug.otherMmsi &&
                // Waiting departure should have ended
                waiting.end?.time != null &&
                // Ensure the waiting event ended before the tug started
                waiting.end?.time!! <= tug.start.time &&
                // Ensure the time gap is within the max duration
                Duration.between(waiting.end?.time, tug.start.time) < MAX_DURATION_DEPART_AND_TUG
            // Get the encounter closest to the tug encounter if multiple are present
        }.maxByOrNull { it.end?.time ?: Instant.MAX }

    /**
     * Find the pilot area based on overlapping with the [pilotEncounter], or null if multiple pilot area activities
     * match. The pilot encounter has no area id, the system can only try to find an overlapping pilot area activity,
     * matching event times.
     * For multiple matches, there is no way to determine the right pilot area, and therefore the system returns null.
     */
    private fun findSingleOverlappingPilotAreaOrNull(
        pilotActivity: StartEnd,
        pilotAreaActivities: List<AreaActivity>
    ): PilotBoardingPlace? = pilotAreaActivities
        .singleOrNull { activity -> pilotActivity.overlaps(activity) }
        ?.areaId
        ?.let { areaId -> infraService.getById(areaId, InfraAreaType.PILOT_BOARDING_PLACE) as? PilotBoardingPlace }

    private fun List<BerthVisitInfo>.attachTugs(allTugs: List<TugInfo>) = setTugs(allTugs, this)

    private fun LocationTime.toApi() = entryMapper.toApi(this)

    private fun List<StartEnd>.allFinished() = all { it.isFinished() }

    private fun shipDetailsOrDefaultUnknown(imo: Int): Ship {
        val details = shipCacheService.getCacheByImo(imo.toString())?.csi?.register?.toShipDetails()
        return Ship(
            imo = imo,
            name = details?.name,
            type = details?.categories?.v2
        )
    }

    /** Set the start time of a tug to the start of the waiting departure encounter */
    private fun List<TugInfo>.alignDepartureStartTimes() =
        this.map {
            it.copy(
                encounter = it.encounter.copy(start = it.waitingForDepartureEncounter?.start ?: it.encounter.start)
            )
        }

    /** Helper class to collect berth visits for a terminal visit in a fold operation */
    data class BerthVisitGroup(
        val terminalId: String,
        val visits: MutableList<BerthVisitInfo>
    )
}
