package nl.teqplay.vesselvoyage.service.api

import nl.teqplay.skeleton.common.exception.NotFoundException
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.datasource.NewVisitDataSource
import nl.teqplay.vesselvoyage.model.internal.MinimalNewVisit
import nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.service.InfraService
import org.springframework.stereotype.Service
import java.time.Instant

@ProfileProcessing
@ProfileApi
@Service
class VisitV2Service(
    private val visitDataSource: NewVisitDataSource,
    private val infraService: InfraService
) : BaseApiV2Service<NewVisit, NewVisitDataSource>(visitDataSource) {

    fun findByPortUnlocode(
        unlocode: String,
        start: Instant?,
        end: Instant?,
        finishedState: NewEntryFinishedFilter,
        confirmed: Boolean?,
        aisTrueDestination: String?,
        qualifyingImos: Set<Int>
    ): List<NewVisit> {
        val pomaId = resolvePomaIdFromUnlocode(unlocode)
        return findByPortAreaId(
            pomaId = pomaId,
            start = start,
            end = end,
            finishedState = finishedState,
            confirmed = confirmed,
            aisTrueDestination = aisTrueDestination,
            qualifyingImos = qualifyingImos
        )
    }

    fun findByPortAreaId(
        pomaId: String,
        start: Instant?,
        end: Instant?,
        finishedState: NewEntryFinishedFilter,
        confirmed: Boolean?,
        aisTrueDestination: String?,
        qualifyingImos: Set<Int>,
    ) = visitDataSource.findByPortAreaId(
        areaId = pomaId,
        start = start,
        end = end,
        finished = finishedState,
        confirmed = confirmed,
        aisTrueDestination = aisTrueDestination,
        qualifyingImos = qualifyingImos
    )

    fun findByPortUnlocodeAndImos(
        imos: Set<Int>,
        unlocode: String,
        finishedState: NewEntryFinishedFilter
    ): List<MinimalNewVisit> {
        val pomaId = resolvePomaIdFromUnlocode(unlocode)

        return visitDataSource.findByPortAreaIdAndImos(
            areaId = pomaId,
            imos = imos,
            finished = finishedState
        )
    }

    fun findByImoLookAround(
        imo: Int,
        limit: Int,
        timestamp: Instant,
        finishedState: NewEntryFinishedFilter,
        confirmed: Boolean?
    ): List<NewVisit> = visitDataSource.findByImoLookAround(
        imo = imo,
        limit = limit,
        timestamp = timestamp,
        finished = finishedState,
        confirmed = confirmed,
    )

    private fun resolvePomaIdFromUnlocode(unlocode: String): String {
        val pomaId = infraService.getPortByUnlocode(unlocode)?._id
            ?: throw NotFoundException("A port with the given unlocode does not exist in the system")

        return pomaId
    }
}
