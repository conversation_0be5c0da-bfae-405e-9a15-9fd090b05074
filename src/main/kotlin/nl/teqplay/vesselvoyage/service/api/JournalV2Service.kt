package nl.teqplay.vesselvoyage.service.api

import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.model.v2.EntryId
import nl.teqplay.vesselvoyage.model.v2.JournalItem
import nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter
import nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter.ANY
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import org.springframework.stereotype.Service
import java.time.Instant
import kotlin.reflect.KProperty1

@ProfileProcessing
@ProfileApi
@Service
class JournalV2Service(
    private val visitV2Service: VisitV2Service,
    private val voyageV2Service: VoyageV2Service
) {

    fun findByVisitId(visitId: String): JournalItem? {
        val visit = visitV2Service.findById(visitId)
            ?: return null
        val precedingVoyage = visit.previous?.let(voyageV2Service::findById)

        return JournalItem(
            visit = visit,
            precedingVoyage = precedingVoyage
        )
    }

    fun findByVisitIds(visitIds: Collection<String>): List<JournalItem> =
        visitV2Service.findByIds(visitIds.toSet()).associateWithVoyage(NewVisit::previous)

    fun findByImoAndTimeRange(
        imo: Int,
        start: Instant,
        end: Instant,
        finishedState: NewEntryFinishedFilter = ANY
    ) = visitV2Service.findByImoAndTimeRange(
        imo = imo,
        start = start,
        end = end,
        finishedState = finishedState,
        confirmed = null
    ).associateWithVoyage(NewVisit::previous)

    fun findByImoStartingAtOrAfter(
        imo: Int,
        start: Instant,
        finishedState: NewEntryFinishedFilter = ANY
    ): List<JournalItem> =
        visitV2Service.findByImoStartingAtOrAfter(
            imo = imo,
            start = start,
            finishedState = finishedState,
            confirmed = null
        ).associateWithVoyage(NewVisit::previous)

    fun findLastByImo(
        imo: Int,
        limit: Int,
        finishedState: NewEntryFinishedFilter = ANY
    ): List<JournalItem> =
        visitV2Service.findLastByImo(
            imo = imo,
            limit = limit,
            finishedState = finishedState,
            confirmed = null
        ).associateWithVoyage(NewVisit::previous)

    /**
     * Maps the visits to [JournalItem]s and looks up the associated visit.
     * The voyage is looked up from the database using the id provided by the [voyageIdSelector].
     * When the voyage is not found, the returned [JournalItem.precedingVoyage] is null.
     */
    private fun List<NewVisit>.associateWithVoyage(
        voyageIdSelector: KProperty1<NewVisit, EntryId?>
    ): List<JournalItem> {
        val voyageIds = this.mapNotNull { visit -> voyageIdSelector.get(visit) }.toSet()
        val voyagesById = voyageV2Service.findByIds(voyageIds).associateBy { voyage -> voyage._id }
        return this.map { visit ->
            val voyageId = voyageIdSelector.get(visit)
            JournalItem(
                visit = visit,
                precedingVoyage = voyagesById[voyageId]
            )
        }
    }
}
