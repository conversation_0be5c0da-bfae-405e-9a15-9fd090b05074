package nl.teqplay.vesselvoyage.service

import io.github.oshai.kotlinlogging.KotlinLogging
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service
import kotlin.system.measureTimeMillis

@Profile("api & !processing")
@Service
class ApiStartUpService(
    staticShipInfoService: StaticShipInfoService,
    infraService: InfraService,
    applicationScope: CoroutineScope
) {

    private val log = KotlinLogging.logger {}

    init {
        log.info { "Running API startup (blocking from init{})" }
        val shipsAndInfraLoadTime = measureTimeMillis {
            runBlocking(Dispatchers.Default) {
                applicationScope.launch {
                    val shipsToLoadIn = async { staticShipInfoService.onStartup() }
                    val infraToLoadIn = async { infraService.onStartup() }

                    shipsToLoadIn.await()
                    infraToLoadIn.await()
                }.join()
            }
        }
        log.info { "Finished API startup (blocking from init{}) ($shipsAndInfraLoadTime ms)" }
    }
}
