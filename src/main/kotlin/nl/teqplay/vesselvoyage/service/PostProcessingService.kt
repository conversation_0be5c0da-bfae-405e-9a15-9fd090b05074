package nl.teqplay.vesselvoyage.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.datasource.PostProcessableDataSource
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.internal.PostProcessable
import nl.teqplay.vesselvoyage.model.v2.ESoFChange
import nl.teqplay.vesselvoyage.model.v2.EntryESoFWrapper
import nl.teqplay.vesselvoyage.model.v2.EntryId
import nl.teqplay.vesselvoyage.model.v2.NewESoF
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewSlowMovingPeriod
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import nl.teqplay.vesselvoyage.properties.PostProcessingProperties
import nl.teqplay.vesselvoyage.service.api.EntryV2Service
import nl.teqplay.vesselvoyage.service.api.EsofV2Service
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import org.springframework.stereotype.Service
import java.time.Instant
import java.time.ZonedDateTime

/**
 * Service that processes Visits and Voyages, doing heavy operations in the background after finishing Visit or Voyage.
 * All post-processing steps can only mutate the [NewESoF] as don't mutate the Visit or Voyage data.
 */
@ProfileProcessing
@ConditionalOnProperty("post-processing.enabled", havingValue = "true")
@Service
class PostProcessingService(
    private val properties: PostProcessingProperties,
    private val dataSource: PostProcessableDataSource,
    private val imoLockService: ImoLockService,
    private val slowMovingService: SlowMovingService,
    private val processingShipStatusService: ProcessingShipStatusService,
    private val persistChangesService: PersistChangesService,
    private val entryV2Service: EntryV2Service,
    private val esofV2Service: EsofV2Service,
    private val slackMessageService: SlackMessageService?,
) {
    private val log = KotlinLogging.logger {}
    private val executor = ThreadPoolTaskExecutor().also { pool ->
        pool.corePoolSize = properties.totalThreads
        pool.setThreadNamePrefix("post-processing-")
        pool.initialize()
    }

    /**
     * Schedule post-processing for one entry.
     * @param entryId The Visit or Voyage ID.
     */
    fun schedulePostProcessing(entryId: EntryId) {
        val postProcessable = PostProcessable(
            entryId = entryId
        )
        dataSource.save(postProcessable)
    }

    fun schedulePostProcessingForMissed(): List<EntryId> {
        val missedPostProcessingEntryIds = esofV2Service.findAllNonPostProcessed()
            .map { it._id }

        missedPostProcessingEntryIds.forEach { entryId ->
            schedulePostProcessing(entryId = entryId)
        }

        return missedPostProcessingEntryIds
    }

    @Scheduled(fixedDelayString = "\${post-processing.interval}")
    fun executePostProcessing() {
        val startTime = ZonedDateTime.now()
        log.debug { "Start post-processing cycle (only visits = ${properties.onlyVisits})" }
        try {
            val allPostProcessable = dataSource.getAll()
            val allPostProcessableVisits = allPostProcessable.filter { postProcessable ->
                postProcessable.entryId.endsWith("VISIT")
            }
            val allPostProcessableVoyages = allPostProcessable - allPostProcessableVisits.toSet()

            log.info { "Found ${allPostProcessableVisits.size} visits to be post-processed" }
            for (postProcessable in allPostProcessableVisits) {
                runPostProcessingForEntry(postProcessable)
            }

            if (!properties.onlyVisits) {
                log.info { "Found ${allPostProcessableVoyages.size} voyages to be post-processed" }
                for (postProcessable in allPostProcessableVoyages) {
                    runPostProcessingForEntry(postProcessable)
                }
            }

            // Wait for all post-processing to finish before we start a new batch
            waitUntilThreadPoolIsEmpty(executor)

            log.info { "Finished post-processing cycle for ${allPostProcessableVisits.size} visits and ${allPostProcessableVoyages.size} voyages (only visits = ${properties.onlyVisits})" }
        } catch (e: Throwable) {
            val failTime = ZonedDateTime.now()
            log.error(e) { "Error in post-processing cycle: ${e.message}" }
            slackMessageService?.sendMessage(
                title = "Scheduled task failed: PostProcessingService.executePostProcessing",
                text = "Error in post-processing cycle: ${e.message}\nStarted at: $startTime\nFailed at: $failTime",
                barColor = SLACK_COLOR_RED
            )
        }
    }

    private fun waitUntilThreadPoolIsEmpty(executor: ThreadPoolTaskExecutor) {
        val threadPool = executor.threadPoolExecutor

        while (true) {
            // Check if we are done
            val active = threadPool.activeCount
            val queueSize = threadPool.queue.size

            log.trace { "Still post processing (active threads = $active, queue size = $queueSize)" }

            if (active == 0 && queueSize == 0) {
                break
            }

            // Not done yet, waiting a bit
            Thread.sleep(1000)
        }
    }

    private fun runPostProcessingForEntry(postProcessable: PostProcessable) {
        executor.execute {
            try {
                val entry = entryV2Service.findEntry(postProcessable.entryId)

                if (entry == null) {
                    log.warn { "Could not post process because entry is not finished (id = ${postProcessable.entryId})" }
                    // Log entry got deleted. This means it got canceled, so we can just finalize and do nothing.
                    dataSource.remove(postProcessable.entryId)
                    return@execute
                }

                val endTime = entry.end?.time

                if (endTime == null) {
                    log.warn { "Post processing scheduled on unfinished entry (id = ${entry._id} imo = ${entry.imo})" }
                    // We have no end time, meaning the entry got resumed so we cannot do any post-processing.
                    dataSource.remove(postProcessable.entryId)
                    return@execute
                }

                log.debug { "Doing post processing for entry (id = ${entry._id} imo = ${entry.imo})" }
                val esof = esofV2Service.findById(postProcessable.entryId)
                val wrapper = EntryESoFWrapper(entry = entry, esof = esof)

                // Run post-processing
                runPostProcessingTasks(
                    imo = entry.imo,
                    wrapper = wrapper,
                    entryStartTime = entry.start.time,
                    entryEndTime = endTime
                )
            } catch (ex: Exception) {
                log.error(ex) { "Error while post-processing entry (id = ${postProcessable.entryId})" }
            }
        }
    }

    private fun runPostProcessingTasks(
        imo: Int,
        wrapper: EntryESoFWrapper<*>,
        entryStartTime: Instant,
        entryEndTime: Instant
    ) {
        try {
            imoLockService.executeBlocking(imo) {
                val (currentEntry, currentEsof) = wrapper

                // Run all tasks while we are locked and in a different thread
                val slowMovingPeriods = processSlowMovingPeriods(
                    imo = imo,
                    startTime = entryStartTime,
                    endTime = entryEndTime
                )

                // Prepare the changes, so we can persist them to the database
                val esofChange = if (currentEsof == null) {
                    ESoFChange(
                        action = Action.CREATE,
                        value = NewESoF(
                            _id = currentEntry._id,
                            encounters = emptyList(),
                            slowMovingPeriods = slowMovingPeriods,
                            postProcessed = true,
                            shipToShipTransfers = emptyList()
                        )
                    )
                } else {
                    ESoFChange(
                        action = Action.UPDATE,
                        value = currentEsof.copy(
                            slowMovingPeriods = slowMovingPeriods,
                            postProcessed = true
                        )
                    )
                }

                persistChangesService.persistChanges(listOf(esofChange))

                // As last step check if we need to update the current status if we are mutating the esof of a recent visit or voyage
                val currentStatus = processingShipStatusService.getStatus(imo)
                val updatedStatus = updateMatchingRecentVisitOrVoyageWrapper(imo, currentEntry, currentStatus, esofChange.value)

                // When not null it means we updated our status, meaning we should persist that as well
                if (updatedStatus != null) {
                    processingShipStatusService.updateStatus(imo, updatedStatus)
                }

                log.debug { "Done post processing for entry (id = ${currentEntry._id} imo = ${currentEntry.imo})" }
                // Done running, remove the post-processing entry
                dataSource.remove(currentEntry._id)
            }
        } catch (ex: Exception) {
            log.error(ex) { "Could not post process, trying again in next cycle (id = ${wrapper.entry._id} imo = $imo)" }
        }
    }

    private fun processSlowMovingPeriods(
        imo: Int,
        startTime: Instant,
        endTime: Instant,
    ): List<NewSlowMovingPeriod> {
        log.debug { "Scheduling to calculate slow moving periods (imo = $imo)" }
        return slowMovingService.determineSlowMovingSegments(imo, startTime, endTime)
    }

    /**
     * Update a matching wrapper given a provided [entry]. This checks if the id of the provided [entry]
     *  matches any previousVisit and previousVoyage and updated the current esof with [updatedEsof].
     *
     * @param imo Imo of the ship.
     * @param entry The entry we are currently post-processing to be used to find a matching wrapper.
     * @param currentStatus The current ship status from real-time processing.
     * @param updatedEsof The new esof of the matching entry.
     * @return The updated status or null when non-matched.
     */
    private fun updateMatchingRecentVisitOrVoyageWrapper(
        imo: Int,
        entry: NewEntry,
        currentStatus: NewShipStatus,
        updatedEsof: NewESoF
    ): NewShipStatus? {
        return when (currentStatus) {
            is NewInitialShipStatus -> {
                log.error { "We are inside an initial state but post-processing for an entry? This should not be possible ... (entry = ${entry._id}, imo = $imo)" }
                null
            }
            is NewVisitShipStatus -> {
                val previousVoyage = currentStatus.previousVoyage
                val previousVisit = currentStatus.previousVisit

                // Check Voyage first because that is the first finished when we enter a Visit
                if (previousVoyage?.entry?._id == entry._id) {
                    currentStatus.copy(
                        previousVoyage = previousVoyage.copy(esof = updatedEsof)
                    )
                } else if (previousVisit?.entry?._id == entry._id) {
                    currentStatus.copy(
                        previousVisit = previousVisit.copy(esof = updatedEsof)
                    )
                } else {
                    null
                }
            }
            is NewVoyageShipStatus -> {
                val previousVisit = currentStatus.previousVisit
                val previousVoyage = currentStatus.previousVoyage

                // Check Visit first because that is the first finished when we enter a Voyage
                if (previousVisit?.entry?._id == entry._id) {
                    currentStatus.copy(
                        previousVisit = previousVisit.copy(esof = updatedEsof)
                    )
                } else if (previousVoyage?.entry?._id == entry._id) {
                    currentStatus.copy(
                        previousVoyage = previousVoyage.copy(esof = updatedEsof)
                    )
                } else {
                    null
                }
            }
        }
    }
}
