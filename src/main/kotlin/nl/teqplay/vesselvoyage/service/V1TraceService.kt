package nl.teqplay.vesselvoyage.service

import io.github.oshai.kotlinlogging.KotlinLogging
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.datasource.HistoricTraceDataSource
import nl.teqplay.vesselvoyage.datasource.OngoingTraceDataSource
import nl.teqplay.vesselvoyage.datasource.VisitDataSource
import nl.teqplay.vesselvoyage.datasource.VoyageDataSource
import nl.teqplay.vesselvoyage.logic.simplifyOngoingTrace
import nl.teqplay.vesselvoyage.logic.simplifyTrace
import nl.teqplay.vesselvoyage.logic.simplifyTraceAtStops
import nl.teqplay.vesselvoyage.logic.toLocationTime
import nl.teqplay.vesselvoyage.model.Entry
import nl.teqplay.vesselvoyage.model.HistoricTrace
import nl.teqplay.vesselvoyage.model.LocationTime
import nl.teqplay.vesselvoyage.model.OngoingTrace
import nl.teqplay.vesselvoyage.model.Stop
import nl.teqplay.vesselvoyage.model.TRACE_ALGORITHM_VERSION
import nl.teqplay.vesselvoyage.model.isVisitId
import nl.teqplay.vesselvoyage.model.isVoyageId
import nl.teqplay.vesselvoyage.properties.AisFetchingProperties
import nl.teqplay.vesselvoyage.properties.TraceProperties
import nl.teqplay.vesselvoyage.util.MMSINotFoundException
import nl.teqplay.vesselvoyage.util.groupOverlapping
import nl.teqplay.vesselvoyage.util.joinToStringTruncated
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.time.Duration
import java.time.ZonedDateTime
import java.util.UUID
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong
import kotlin.system.measureTimeMillis

private val log = KotlinLogging.logger {}

@Deprecated("V1 trace service")
@ProfileProcessing
@ProfileApi
@Service
class V1TraceService(
    private val traceProperties: TraceProperties,
    private val aisFetchingProperties: AisFetchingProperties,
    private val historicTraceDataSource: HistoricTraceDataSource,
    private val ongoingTraceDataSource: OngoingTraceDataSource,
    private val shipHistoryService: ShipHistoryService,
    private val staticShipInfoService: StaticShipInfoService,
    private val visitDataSource: VisitDataSource,
    private val voyageDataSource: VoyageDataSource,
    private val applicationScope: CoroutineScope,
    private val aisFetchingService: AisFetchingService,
    private val slackMessageService: SlackMessageService?
) {
    // TODO: find better, shorter names for these long variable names
    private val defaultTolerance = traceProperties.tolerance
    private val ongoingTracesMaxAge = traceProperties.ongoing.maxAge
    private val ongoingTracesMaxSize = traceProperties.ongoing.maxSize
    private val maxNonSimplifiedSize = traceProperties.ongoing.maxNonSimplifiedSize
    private val createFinishedHistoricTraceFromAis = traceProperties.historic.finished.createFromAis
    private val createUnfinishedHistoricTraceFromAis = traceProperties.historic.unfinished.createFromAis
    private val cacheMaxAge = traceProperties.ongoing.maxAge

    /**
     * A [Channel] containing all the entry ids of visits and voyages without a historic trace
     *  which we will retrieve in the background
     */
    private val scheduledEntries = Channel<String>(capacity = Channel.UNLIMITED)

    init {
        log.info {
            "Configuration: " +
                "tolerance: $defaultTolerance, " +
                "ongoingTracesMaxAge: $ongoingTracesMaxAge, " +
                "ongoingTracesMaxSize: $ongoingTracesMaxSize, " +
                "createFinishedHistoricTraceFromAis: $createFinishedHistoricTraceFromAis, " +
                "createUnfinishedHistoricTraceFromAis: $createUnfinishedHistoricTraceFromAis"
        }
    }

    /**
     * Function that will spin up a coroutine which is used to read all entry ids from the [scheduledEntries] where the
     *  [HistoricTrace] needs to be calculated for.
     */
    @EventListener(ApplicationReadyEvent::class)
    fun startHistoricTraceCalculation() {
        applicationScope.launch {
            log.info { "Started historic trace calculation mechanism" }
            while (true) {
                scheduledEntries.tryReceive()
                    .getOrNull()
                    ?.let(::onCalculateTrace)

                delay(100)
            }
        }
    }

    private var ongoingTracesByIMO = ConcurrentHashMap<String, OngoingTrace>()

    final fun insertLocation(imo: String, mmsi: String, location: LocationTime): Boolean {
        val ongoingTrace = getOrCreateOngoingTrace(imo = imo, mmsi = mmsi)

        // add the location (will be ordered chronologically)
        ongoingTrace.insert(location)

        // once in a while, simplify the locations
        val nonSimplifiedCount = (ongoingTrace.locations.size - ongoingTrace.simplifiedCount)
        val updatedOngoingTrace = if (nonSimplifiedCount >= maxNonSimplifiedSize) {
            simplifyOngoingTrace(ongoingTrace, defaultTolerance).also {
                log.trace {
                    "Simplified ongoing trace for IMO ${ongoingTrace.imo} " +
                        "(old size: ${ongoingTrace.locations.size}, new size: ${it.locations.size})"
                }
            }
        } else {
            // no simplification needed
            ongoingTrace
        }

        // if the ongoingTrace is getting too large, persist it and remove it from cache
        // (next inserted location will be added to a new ongoing trace)
        if (updatedOngoingTrace.locations.size > ongoingTracesMaxSize) {
            ongoingTracesByIMO.remove(imo)

            ongoingTraceDataSource.upsert(updatedOngoingTrace)
        } else {
            ongoingTracesByIMO[imo] = updatedOngoingTrace
        }

        return true
    }

    fun getByEntry(entry: Entry, tolerance: Double? = null): HistoricTrace? {
        val toleranceOrDefault = tolerance ?: defaultTolerance

        return when (entry.finished) {
            true -> {
                historicTraceDataSource.findByEntryId(entry._id, toleranceOrDefault)
                    .also { historicTrace ->
                        if (historicTrace == null) {
                            scheduleHistoricTraceRetrievingForEntry(entry)
                        }
                    }
            }
            false -> {
                val startOfOngoingTraces = ZonedDateTime.now().minus(ongoingTracesMaxAge)

                if (createUnfinishedHistoricTraceFromAis || entry.startTime.isBefore(startOfOngoingTraces)) {
                    scheduleHistoricTraceRetrievingForEntry(entry)
                    null
                } else {
                    // cheap: fetch from the ongoing trace. But the ongoing trace must be populated
                    getHistoricTraceFromOngoingTrace(entry)
                }
            }
        }
    }

    fun getByEntries(entries: List<Entry>, tolerance: Double? = null): List<HistoricTrace> {
        val toleranceOrDefault = tolerance ?: defaultTolerance

        val finishedEntryIds = entries.filter { it.finished }.map { it._id }
        val historicTracesByEntryId = historicTraceDataSource
            .findByEntryIds(finishedEntryIds, toleranceOrDefault)
            .associateBy { it._id }

        return entries
            .mapNotNull { entry ->
                historicTracesByEntryId[entry._id]
                    ?: getByEntry(entry, toleranceOrDefault) // for ongoing trace and missing historic traces
            }
            .sortedBy { it.startTime }
    }

    fun getByImo(imo: String): List<HistoricTrace> {
        return historicTraceDataSource.findByImo(imo)
    }

    fun createHistoricTrace(entry: Entry): HistoricTrace? {
        log.trace { "Create historic trace (IMO: ${entry.imo}, entryId: ${entry._id})" }

        if (!entry.finished) {
            log.warn { "Could not create historic trace: entry is not finished (IMO: ${entry.imo})" }
            return null
        }

        if (createFinishedHistoricTraceFromAis) {
            return createHistoricTraceFromAIS(entry, defaultTolerance)
        }

        // when the entry.startTime is older than the trace.ongoing.maxAge fallback on generating
        // the trace from AIS instead of the ongoing trace (less efficient)
        val startOfOngoingTraces = ZonedDateTime.now().minus(ongoingTracesMaxAge)
        if (entry.startTime.isBefore(startOfOngoingTraces)) {
            log.warn {
                "Cannot create historic trace from ongoing trace: " +
                    "start time is before the start of the ongoing traces." +
                    "If this happens a lot, please configure a larger maxAge for the ongoing traces " +
                    "(IMO: ${entry.imo}, entryId: ${entry._id})"
            }

            return createHistoricTraceFromAIS(entry, defaultTolerance)
        }

        val trace = getHistoricTraceFromOngoingTrace(entry)
        if (trace == null) {
            log.warn { "Could not create historic trace: no ongoing trace found (IMO: ${entry.imo})" }

            return null
        }

        historicTraceDataSource.upsert(trace)

        return trace
    }

    fun createHistoricTraceFromAIS(entry: Entry, tolerance: Double = defaultTolerance): HistoricTrace? {
        log.trace { "Creating historic trace for IMO: ${entry.imo}, start: ${entry.startTime}, end: ${entry.endTime}" }

        if (!entry.finished) {
            log.warn { "Cannot create trace from AIS: entry is not finished (IMO: ${entry.imo})" }
            return null
        }

        val newTrace = getHistoricTraceFromAIS(entry, tolerance)

        historicTraceDataSource.upsert(newTrace)

        return newTrace
    }

    private fun getHistoricTraceFromAIS(entry: Entry, tolerance: Double = defaultTolerance): HistoricTrace {
        val from = entry.startTime
        val to = entry.endTime ?: ZonedDateTime.now()

        val totalAisPoints = AtomicLong(0)
        val simplifiedLocations = if (Duration.between(from, to).toDays() <= traceProperties.maxRequestLengthDays) {
            try {
                aisFetchingService.getTraceFromAIS(entry.imo, from, to) { ais ->
                    totalAisPoints.addAndGet(ais.size.toLong())

                    val stops = entry.esof?.stops
                    val locations = ais.map { it.toLocationTime() }
                    simplifyTraceWithStops(locations, stops, tolerance)
                }
            } catch (ex: MMSINotFoundException) {
                log.debug { "Could not find any mmsis when trying to retrieve for the vessel with imo: ${entry.imo}" }
                emptyList()
            }
        } else {
            log.debug {
                "Trace request length for imo ${entry.imo} (${Duration.between(from, to).toDays()} days) " +
                    "is bigger than max length (${traceProperties.maxRequestLengthDays} days), returning empty list"
            }
            emptyList()
        }

        val newTrace = HistoricTrace(
            _id = UUID.randomUUID().toString(),
            entryId = entry._id,
            mmsi = entry.mmsi,
            imo = entry.imo,
            tolerance = tolerance,
            startTime = entry.startTime,
            endTime = entry.endTime
                ?: simplifiedLocations.lastOrNull()?.time
                ?: entry.startTime,
            locations = simplifiedLocations,
            finished = entry.finished,
            version = TRACE_ALGORITHM_VERSION
        )

        log.trace {
            "Generated trace containing ${simplifiedLocations.size} data points " +
                "from a total of $totalAisPoints data points " +
                "(IMO ${entry.imo}, tolerance $defaultTolerance)"
        }

        return newTrace
    }

    fun rebuildOngoingTrace(imo: String) {
        val end = ZonedDateTime.now()
        val start = end.minus(ongoingTracesMaxAge)
        log.info { "Start rebuilding ongoing trace for IMO: $imo, start: $start, end: $end" }

        val mmsiMapping = staticShipInfoService.getMmsiMappingFromImoAndDateRange(imo, start, end)

        if (mmsiMapping == null || mmsiMapping.isEmpty()) {
            log.info { "Cannot rebuild ongoing trace for IMO $imo: no MMSI's found" }
            return
        }

        log.info { "Rebuilding ongoing trace for IMO: $imo, MMSI's: ${mmsiMapping.map { it.mmsi }}, start: $start, end: $end" }

        val totalAisPoints = AtomicLong(0)
        val simplifiedLocations = mmsiMapping.groupOverlapping(start, end).map { (groupedMmsis, groupedStartTime, groupedEndTime) ->
            if (groupedMmsis.isNotEmpty()) {
                shipHistoryService.queryAisHistoryInChunks(
                    mmsis = groupedMmsis,
                    from = groupedStartTime,
                    to = groupedEndTime ?: end,
                    maxDays = aisFetchingProperties.maxDays,
                    transformChunk = { ais ->
                        // TODO replace this with being able to filter in the query endpoint instead
                        val relevantAis = ais.filterNot { it.historic }

                        totalAisPoints.addAndGet(relevantAis.size.toLong())

                        relevantAis.map { it.toLocationTime() }
                            .simplifyTrace(defaultTolerance)
                    },
                    accumulateChunks = { it.flatten() }
                )
            } else {
                log.warn { "Could not get trace for IMO: $imo it had no MMSI's from $groupedStartTime until $groupedEndTime" }
                emptyList()
            }
        }.flatten()

        log.trace {
            "Generated ongoing trace containing ${simplifiedLocations.size} data points " +
                "from a total of $totalAisPoints data points " +
                "(IMO $imo, tolerance $defaultTolerance)"
        }

        val ongoingTrace = OngoingTrace(
            _id = UUID.randomUUID().toString(),
            imo = imo,
            mmsi = mmsiMapping.first().mmsi, // TODO: mmsi is a bit meaningless/unreliable. Change this into mmsis? Or remove it at all?
            locations = simplifiedLocations.toMutableList(),
            simplifiedCount = simplifiedLocations.size,
            minTime = simplifiedLocations.firstOrNull()?.time,
            maxTime = simplifiedLocations.lastOrNull()?.time
        )

        ongoingTracesByIMO.remove(imo)
        ongoingTraceDataSource.deleteAllByIMO(imo)
        ongoingTraceDataSource.upsert(ongoingTrace)

        log.info { "Finished rebuilding ongoing trace for IMO $imo" }
    }

    fun deleteHistoricTrace(entry: Entry) {
        deleteHistoricTrace(imo = entry.imo, entryId = entry._id)
    }

    fun deleteHistoricTrace(entryId: String, imo: String) {
        log.trace { "Delete historic trace for IMO $imo (entryId: $entryId)" }

        historicTraceDataSource.deleteByEntryId(entryId)
    }

    fun deleteHistoricTraces(entryIds: List<String>, imo: String) {
        log.trace { "Delete historic traces for IMO $imo (entryIds: ${entryIds.joinToStringTruncated(10)})" }
        historicTraceDataSource.deleteByEntryIds(entryIds)
    }

    // FIXME Copy from the entryService to avoid circular dependencies
    private fun findEntry(entryId: String): Entry? = when {
        isVisitId(entryId) -> visitDataSource.findById(entryId)
        isVoyageId(entryId) -> voyageDataSource.findById(entryId)
        else -> null
    }

    /**
     * Recalculate the historic trace when a [Entry] is found and no [HistoricTrace] exist yet.
     *
     * @param entryId the id of the [Entry] we want to recalculate the [HistoricTrace] for.
     */
    fun onCalculateTrace(entryId: String) {
        val entry = findEntry(entryId)

        if (entry != null) {
            val historicTrace = historicTraceDataSource.findByEntryId(entryId, defaultTolerance)

            // Only try to create the historic trace when we don't have any yet.
            if (historicTrace == null) {
                log.trace { "Calculating trace of entry (id: $entryId)" }
                createHistoricTrace(entry)
            } else {
                log.trace { "Trace already recalculated, ignoring entry (id: $entryId)" }
            }
        }
    }

    /**
     * Schedule the provided id of the given [entry] to the [scheduledEntries] Channel.
     */
    fun scheduleHistoricTraceRetrievingForEntry(entry: Entry) {
        applicationScope.launch {
            scheduledEntries.send(entry._id)
            log.info { "Trace recalculation scheduled (id: ${entry._id})" }
        }
    }

    private fun getOrCreateOngoingTrace(imo: String, mmsi: String): OngoingTrace {
        // get from cache
        val ongoingTrace = ongoingTracesByIMO.computeIfAbsent(imo) {
            OngoingTrace(
                _id = UUID.randomUUID().toString(),
                imo = imo,
                mmsi = mmsi,
                locations = mutableListOf(),
                minTime = null,
                maxTime = null,
                simplifiedCount = 0
            )
        }

        return ongoingTrace
    }

    private fun getHistoricTraceFromOngoingTrace(entry: Entry): HistoricTrace? {
        // TODO: see how performance heavy this is in practice. If needed, optimize
        val start = System.currentTimeMillis()

        val ongoingLocations = (
            ongoingTraceDataSource.findByIMO(entry.imo, entry.startTime).toList() +
                listOfNotNull(ongoingTracesByIMO[entry.imo])
            )
            .flatMap { it.locations }
            .filter { it.time >= entry.startTime && (entry.endTime == null || it.time < entry.endTime) }
            .sortedBy { it.time }

        val simplifiedLocations = simplifyTraceWithStops(ongoingLocations, entry.esof?.stops, defaultTolerance)

        val end = System.currentTimeMillis()
        log.trace {
            "Generated historic trace with ${simplifiedLocations.size} locations on the fly " +
                "(IMO: ${entry.imo}, duration: ${end - start} ms)"
        }

        if (ongoingLocations.isNotEmpty()) {
            return HistoricTrace(
                _id = UUID.randomUUID().toString(),
                imo = entry.imo,
                mmsi = entry.mmsi,
                entryId = entry._id,
                startTime = ongoingLocations.first().time,
                endTime = null,
                locations = simplifiedLocations,
                tolerance = defaultTolerance,
                finished = entry.finished,
                version = TRACE_ALGORITHM_VERSION
            )
        }

        return null
    }

    private fun deleteAgedOngoingTracesFromCache() {
        val start = System.currentTimeMillis()
        val cacheAgedTime = ZonedDateTime.now().minus(cacheMaxAge)
        log.debug { "Deleting all aged ongoing traces older than $cacheAgedTime..." }

        fun isAged(ongoingTrace: OngoingTrace): Boolean {
            val maxTime = ongoingTrace.maxTime

            return maxTime == null || maxTime.isBefore(cacheAgedTime)
        }

        val staleTraces = ongoingTracesByIMO.values.filter { isAged(it) }
        var deleteCountCache = 0
        staleTraces.forEach { staleTrace ->
            if (isAged(staleTrace)) {
                ongoingTracesByIMO.remove(staleTrace.imo)
                deleteCountCache++
            }
        }

        val end = System.currentTimeMillis()
        log.debug { "Deleted $deleteCountCache aged ongoing traces from cache in ${end - start} ms" }
    }

    private fun deleteAgedOngoingTracesFromDatabase() {
        val dbAgedTime = ZonedDateTime.now().minus(ongoingTracesMaxAge)

        log.debug { "Deleting all aged ongoing traces older than $dbAgedTime..." }

        val start = System.currentTimeMillis()
        val deleteCountDatabase = ongoingTraceDataSource.deleteAllOlderThan(dbAgedTime)
        val end = System.currentTimeMillis()
        log.debug { "Deleted $deleteCountDatabase aged ongoing traces from database in ${end - start} ms" }
    }

    @Scheduled(fixedDelayString = "\${trace.ongoing.persist-interval}")
    fun persistOngoingTraces() {
        val startTime = ZonedDateTime.now()
        try {
            // remove aged ongoing traces from cache and database
            deleteAgedOngoingTracesFromCache()
            deleteAgedOngoingTracesFromDatabase()

            // note that the only reason for regularly persisting the ongoing
            // traces is to not lose any new AIS data points when the server crashes
            val traces = ongoingTracesByIMO.values.toList()
            log.info { "Start persisting ${traces.size} ongoing traces..." }
            val duration = measureTimeMillis {
                ongoingTraceDataSource.upsertAll(traces)
            }
            log.info { "Finished persisting ${traces.size} ongoing traces in $duration ms..." }
        } catch (e: Throwable) {
            val failTime = ZonedDateTime.now()
            log.error(e) { "Error persisting ongoing traces: ${e.message}" }
            slackMessageService?.sendMessage(
                title = "Scheduled task failed: TraceService.persistOngoingTraces",
                text = "Error persisting ongoing traces: ${e.message}\nStarted at: $startTime\nFailed at: $failTime",
                barColor = SLACK_COLOR_RED
            )
        }
    }

    fun shutdown() {
        log.info { "Persisting ongoing traces on shutdown..." }

        persistOngoingTraces()
    }
}

private fun simplifyTraceWithStops(
    locations: List<LocationTime>,
    stops: List<Stop>?,
    tolerance: Double
): List<LocationTime> {
    if (locations.isEmpty()) {
        return emptyList()
    }

    val (from, to) = locations.first().time to locations.last().time

    return if (stops != null) {
        simplifyTraceAtStops(locations, stops, from, to)
    } else {
        locations
    }.simplifyTrace(tolerance)
}
