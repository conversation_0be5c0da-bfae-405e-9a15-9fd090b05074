package nl.teqplay.vesselvoyage.service

import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.datasource.VisitDataSource
import nl.teqplay.vesselvoyage.model.Filter
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.model.VisitQuery
import org.springframework.stereotype.Service
import java.time.ZonedDateTime
import java.util.stream.Collectors

@ProfileProcessing
@ProfileApi
@Service
class VisitService(
    private val visitDataSource: VisitDataSource,
    private val staticShipInfoService: StaticShipInfoService,
    private val infraService: InfraService
) {
    fun findByPortId(portId: String, start: ZonedDateTime, end: ZonedDateTime, limit: Int, filter: Filter? = null): List<Visit> {
        require(portId.isNotBlank()) { "Provided portId shouldn't be blank" }

        val imoSet = staticShipInfoService.applyFilter(filter)
        return visitDataSource.findByPortId(portId, start, end, limit, imoSet)
            .toList()
    }

    fun findByIMO(imo: String, start: ZonedDateTime, end: ZonedDateTime, limit: Int) =
        visitDataSource.findByIMO(imo, start, end, limit)

    fun findByDestination(
        destination: String,
        filter: Filter? = null
    ): List<Visit> {
        require(destination.isNotBlank()) { "Destination shouldn't be blank" }

        val imoSet = staticShipInfoService.applyFilter(filter)
        return visitDataSource.findByDestination(destination, imoSet)
            .toList()
    }

    fun getVisitIdsByImo(imo: String) = visitDataSource.getVisitIdsByImo(imo).toList()

    fun countByImo(imo: String) = visitDataSource.countByImo(imo)

    // TODO: idea: if these requests turn out to be slow, we can create a cache keeping the latest visit
    fun findPreviousLocationByIMO(imo: String, before: ZonedDateTime): Visit? =
        visitDataSource.findPreviousLocationByIMO(imo, before)

    fun findPreviousLocationsByIMO(imos: List<String>, before: ZonedDateTime): List<Visit> = imos
        .parallelStream()
        .map { imo ->
            findPreviousLocationByIMO(imo, before)
        }
        .collect(Collectors.toList())
        .filterNotNull()

    fun find(query: VisitQuery): List<Visit> {
        val ports = if (query.includeSubPorts == true && query.portIds?.isNotEmpty() == true) {
            infraService.addAllSubPorts(query.portIds!!)
        } else {
            query.portIds
        }
        val enrichedQuery = query.copy(
            portIds = ports
        )
        return visitDataSource.find(enrichedQuery, staticShipInfoService::getShipsByCategory)
    }

    fun findCurrentVisitsByPortId(
        portId: String,
        filter: Filter? = null
    ): List<Visit> {
        require(portId.isNotBlank()) { "Provided portId shouldn't be blank" }

        val imoSet = staticShipInfoService.applyFilter(filter)
        return visitDataSource.findCurrentVisitsByPortId(portId, imoSet)
            .toList()
    }

    fun findCurrentAnchorageByPortId(
        portId: String,
        filter: Filter? = null
    ): List<Visit> {
        require(portId.isNotBlank()) { "Provided portId shouldn't be blank" }

        val imoSet = staticShipInfoService.applyFilter(filter)
        return visitDataSource.findCurrentAnchorageByPortId(portId, imoSet)
            .toList()
    }
}
