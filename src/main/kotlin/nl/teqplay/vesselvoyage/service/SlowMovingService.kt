package nl.teqplay.vesselvoyage.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.client.driftpredictor.DriftPredictorClient
import nl.teqplay.vesselvoyage.model.internal.TraceItem
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewSlowMovingPeriod
import nl.teqplay.vesselvoyage.util.createSpeedOfTraceItemsOrNull
import org.springframework.stereotype.Service
import java.time.Instant
import java.time.ZoneOffset
import java.util.UUID

@ProfileProcessing
@Service
class SlowMovingService(
    private val driftPredictorClient: DriftPredictorClient,
    private val aisFetchingService: AisFetchingService
) {
    private val log = KotlinLogging.logger {}

    fun determineSlowMovingSegments(imo: Int, start: Instant, end: Instant): List<NewSlowMovingPeriod> {
        log.debug { "Determining slow moving segments (imo = $imo, start = $start, end = $end)" }
        val aisHistory = try {
            aisFetchingService.getShipTrace(
                imo = imo.toString(),
                startTime = start.atZone(ZoneOffset.UTC),
                endTime = end.atZone(ZoneOffset.UTC)
            )
        } catch (ex: Exception) {
            // We can't retrieve any slow moving periods when getting the ship trace fails in any way
            log.warn(ex) { "Could not retrieve slow moving periods because getting the ship trace for $imo failed" }
            emptyList()
        }

        // No need to determine slow moving when there is no AIS in the time periods we provided
        if (aisHistory.isEmpty()) {
            return emptyList()
        }

        val driftSegments = driftPredictorClient.getSegmentPredictions(aisHistory)

        log.debug { "Found ${driftSegments.size} drift segments grouping them to NewSlowMovingPeriod (imo = $imo, start = $start, end = $end)" }
        val slowMovingPeriods = driftSegments.mapNotNull { segment ->
            if (segment.drifting) {
                val startLocationTime = aisHistory.find { it.messageTime == segment.startTime }?.toLocationTime()
                val endLocationTime = aisHistory.find { it.messageTime == segment.endTime }?.toLocationTime()
                val firstSpeedOverGround = aisHistory.firstOrNull { it.speedOverGround != null }?.speedOverGround

                if (startLocationTime == null || endLocationTime == null || firstSpeedOverGround == null) {
                    return@mapNotNull null
                }

                val speedTrace = aisHistory.mapNotNull { aisHistoricMessage ->
                    // Ensures only the drifting segment is used to calculate speed
                    val messageTime = aisHistoricMessage.messageTime
                    if (messageTime >= startLocationTime.time && messageTime <= endLocationTime.time) {
                        TraceItem(
                            location = aisHistoricMessage.location,
                            speedOverGround = aisHistoricMessage.speedOverGround,
                            draught = aisHistoricMessage.draught,
                            timestamp = messageTime
                        )
                    } else {
                        null
                    }
                }

                val speed = createSpeedOfTraceItemsOrNull(
                    trace = speedTrace,
                    speedOverGroundWhenMissingFirst = firstSpeedOverGround
                ) ?: return@mapNotNull null

                return@mapNotNull NewSlowMovingPeriod(
                    id = UUID.randomUUID().toString(),
                    start = startLocationTime,
                    end = endLocationTime,
                    speed = speed
                )
            }
            return@mapNotNull null
        }

        log.debug { "Done grouping, resulted in ${slowMovingPeriods.size} slow moving periods out of the ${driftSegments.size} segments (imo = $imo, start = $start, end = $end)" }
        return slowMovingPeriods
    }

    private fun AisHistoricMessage.toLocationTime() = LocationTime(
        location = location,
        time = messageTime
    )
}
