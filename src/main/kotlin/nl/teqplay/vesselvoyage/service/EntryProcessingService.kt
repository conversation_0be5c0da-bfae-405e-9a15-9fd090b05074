package nl.teqplay.vesselvoyage.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.datasource.VisitDataSource
import nl.teqplay.vesselvoyage.datasource.VoyageDataSource
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.Change
import nl.teqplay.vesselvoyage.model.Entry
import nl.teqplay.vesselvoyage.model.HistoricTrace
import nl.teqplay.vesselvoyage.model.InitialShipStatus
import nl.teqplay.vesselvoyage.model.OutgoingChange
import nl.teqplay.vesselvoyage.model.ShipStatus
import nl.teqplay.vesselvoyage.model.TRACE_ALGORITHM_VERSION
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.Voyage
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.model.internal.ProcessedAisEngineEvent
import nl.teqplay.vesselvoyage.model.internal.ProcessedEvent
import nl.teqplay.vesselvoyage.model.internal.ProcessingDryRunResult
import nl.teqplay.vesselvoyage.model.internal.SnapshotShipStatus
import nl.teqplay.vesselvoyage.model.v2.ESoFChange
import nl.teqplay.vesselvoyage.model.v2.EntryESoFWrapper
import nl.teqplay.vesselvoyage.model.v2.EntryId
import nl.teqplay.vesselvoyage.model.v2.NewChange
import nl.teqplay.vesselvoyage.model.v2.NewESoF
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewShipStatus
import nl.teqplay.vesselvoyage.model.v2.VisitChange
import nl.teqplay.vesselvoyage.model.v2.VoyageChange
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.properties.TraceProperties
import nl.teqplay.vesselvoyage.service.processing.EventProcessingService
import nl.teqplay.vesselvoyage.service.publisher.ChangesPublisherService
import nl.teqplay.vesselvoyage.service.publisher.OutgoingEventsSender
import nl.teqplay.vesselvoyage.service.trace.ProcessingTraceService
import nl.teqplay.vesselvoyage.util.MMSINotFoundException
import org.springframework.stereotype.Service
import java.time.Instant
import java.time.ZonedDateTime

@ProfileProcessing
@Service
class EntryProcessingService(
    private val entryService: EntryService,
    private val eventProcessingProperties: EventProcessingProperties,
    private val traceProperties: TraceProperties,
    private val eventFetchingService: EventFetchingService,
    private val outgoingEventsSender: OutgoingEventsSender?,
    private val visitDataSource: VisitDataSource,
    private val voyageDataSource: VoyageDataSource,
    private val v1TraceService: V1TraceService,
    private val processingTraceService: ProcessingTraceService,
    private val shipStatuses: ProcessingShipStatusService,
    private val eventProcessingService: EventProcessingService,
    private val slowMovingService: SlowMovingService,
    private val postProcessingService: PostProcessingService?,
    private val persistChangesService: PersistChangesService,
    private val changesPublisherService: ChangesPublisherService?
) {
    companion object {
        private val THE_BEGINNING_OF_TEQPLAY = ZonedDateTime.parse("2015-01-01T00:00:00Z") // Teqplay started in 2015
    }

    private val log = KotlinLogging.logger {}

    init {
        log.info { "Configuration: $eventProcessingProperties" }
    }

    fun getCurrentShipStatus(imo: String): ShipStatus {
        return entryService.getCurrentShipStatus(imo)
    }

    fun getCurrentStatus(imo: String): Entry? {
        return shipStatuses.getEntry(imo)
    }

    fun insertNew(
        event: Event,
        imo: Int
    ): List<NewChange<*>> {
        val shipStatus = shipStatuses.getStatus(imo)

        val processTime = Instant.now()
        val result = eventProcessingService.onEventAndBuffer(
            shipStatus = shipStatus,
            event = event,
            processTime = processTime
        )

        result.issues.forEach {
            log.debug { "EventProcessingIssue: " + it.description }
        }

        shipStatuses.updateStatus(imo, result.status)

        var lastUpdateEntryId: EntryId? = null
        persistChangesService.persistChanges(result.changes)
        result.changes.forEach { change ->
            lastUpdateEntryId = handleEntryTraceChanges(change, imo, lastUpdateEntryId)
        }

        // Publish all changes to Nats if there are any
        if (result.changes.isNotEmpty()) {
            changesPublisherService?.tryPublishNewChanges(
                oldStatus = shipStatus,
                updatedStatus = result.status,
                changes = result.changes
            )
        }

        // Only when post-processing is enabled schedule it
        if (postProcessingService != null) {
            for (postProcessable in result.readyForPostProcessing) {
                postProcessingService.schedulePostProcessing(entryId = postProcessable)
            }
        }

        return result.changes
    }

    @Deprecated("Should be using the V2 flow instead")
    fun insert(
        event: Event,
        sendToQueue: Boolean = true,
        logIssues: Boolean = true,
        generateTraceWhenFinished: Boolean? = null,
        imo: String
    ): List<Change> {
        val shipStatus = shipStatuses[imo]

        val (updatedStatus, changes, issues) = eventProcessingService.onEvent(
            shipStatus = shipStatus,
            event = event
        )

        if (logIssues) {
            issues.forEach {
                log.debug { "EventProcessingIssue: " + it.description }
            }
        }

        shipStatuses[imo] = updatedStatus

        changes.forEach {
            persistEntry(it)

            if (generateTraceWhenFinished ?: traceProperties.historic.generateWhenFinished) {
                updateTrace(it)
            }

            if (sendToQueue) {
                val metadata = entryService.getChangeMetadata(imo)
                outgoingEventsSender?.send(OutgoingChange(it, metadata))
            }
        }

        return changes
    }

    fun regenerateBySnapshot(snapshot: SnapshotShipStatus): Int {
        log.info { "Start regenerating by snapshot (imo: ${snapshot.imo})" }
        var eventsCount = 0
        val end: ZonedDateTime = ZonedDateTime.now()

        // Delete all visits and voyages that happened
        visitDataSource.delete(snapshot.imo, snapshot.createdAt, end)
        voyageDataSource.delete(snapshot.imo, snapshot.createdAt, end)

        // Change the current ship status to the one of the snapshot
        shipStatuses[snapshot.imo] = snapshot.status

        try {
            // Process all events from where we have to regenerate
            eventFetchingService.fetchEventsByIMO(snapshot.imo, snapshot.createdAt, end).forEach { event ->
                insert(event, sendToQueue = false, logIssues = false, generateTraceWhenFinished = false, snapshot.imo)
                eventsCount += 1
            }
        } catch (_: MMSINotFoundException) {
            // Ignore when no MMSI is matching the IMO number we are regenerating
        }

        log.info { "Finished regenerating by snapshot (imo: ${snapshot.imo}, eventsCount: $eventsCount)" }

        return eventsCount
    }

    fun regeneratePartially(imo: String, from: ZonedDateTime): Int {
        var eventsCount = 0
        val to = ZonedDateTime.now()
        log.info { "Start regenerating entries partially (imo: $imo, from: $from, to: $to)" }

        val firstVisit = visitDataSource.findFirst(imo, from, to)
        val firstVoyage = voyageDataSource.findFirst(imo, from, to)

        // Delete all visits and voyages that happened in the given time range
        visitDataSource.delete(imo, from, to)
        voyageDataSource.delete(imo, from, to)

        val firstEntry = if (firstVisit != null && firstVoyage != null && firstVisit.startTime.isBefore(firstVoyage.startTime)) {
            firstVisit
        } else {
            firstVoyage
        }

        // Set the actual time window to use to recalculate
        val actualFrom = firstEntry?.startTime ?: from

        val historicalShipState = createHistoricalShipStatus(firstEntry)
        shipStatuses[imo] = historicalShipState

        try {
            // Process all events from where we have to regenerate
            eventFetchingService.fetchEventsByIMO(imo, actualFrom, to).forEach { event ->
                insert(event, sendToQueue = false, logIssues = false, generateTraceWhenFinished = false, imo)
                eventsCount += 1
            }
        } catch (_: MMSINotFoundException) {
            // Ignore when no MMSI is matching the IMO number we are regenerating
        }

        log.info { "Finished regenerating entries partially (imo: $imo, from: $from, to: $to, eventsCount: $eventsCount)" }

        return eventsCount
    }

    fun regenerateAllEventsAndTracesByIMO(imo: String, forceRegenerateTraces: Boolean): Int {
        val result = regenerateAllEntriesByIMO(imo)

        regenerateAllTracesByIMO(imo, forceRegenerateTraces, result.entrySummariesById)

        return result.eventsCount
    }

    private data class RegenerateEntriesResult(
        val eventsCount: Int,
        val changeCount: Int,
        val entrySummariesById: Map<String, EntrySummary>
    )

    /**
     * Create a historical ship status based on the provided [currentEntry].
     *
     * @return the ship status before the [currentEntry]
     */
    fun createHistoricalShipStatus(currentEntry: Entry?): ShipStatus {
        if (currentEntry == null) {
            // When can't create any historical ship state when no entry is provided
            // This can mean that this vessel is new, so we need to start from scratch
            return InitialShipStatus
        }

        // Get the entry we had before the provided entry
        val previousEntry = when (currentEntry) {
            is Visit -> voyageDataSource.findByPreviousId(currentEntry)
            is Voyage -> visitDataSource.findByPreviousId(currentEntry)
        }

        if (previousEntry == null) {
            log.warn { "Could not create historical ship status because entry wasn't found in the database, using initial status instead" }

            // When no previous entry is found it means that the currentEntry is our only entry we know in VesselVoyage
            // This will result in all existing visits and voyages being dropped, and we have no state for this vessel
            return InitialShipStatus
        }

        return when (previousEntry) {
            is Visit -> {
                val previousVoyage = voyageDataSource.findByPreviousId(previousEntry)
                val previousVisit = visitDataSource.findByPreviousId(previousVoyage)

                VisitShipStatus(
                    visit = previousEntry,
                    previousVoyage = previousVoyage,
                    previousVisit = previousVisit
                )
            }
            is Voyage -> {
                val previousVisit = visitDataSource.findByPreviousId(previousEntry)
                val previousVoyage = voyageDataSource.findByPreviousId(previousVisit)

                VoyageShipStatus(
                    voyage = previousEntry,
                    previousVisit = previousVisit,
                    previousVoyage = previousVoyage
                )
            }
        }
    }

    private fun regenerateAllEntriesByIMO(imo: String): RegenerateEntriesResult {
        // get current status (may load from disk if not in memory)
        val oldStatusEntry = shipStatuses.getEntry(imo)

        log.debug { "Deleting all visits for ship with IMO $imo..." }
        visitDataSource.deleteAllByIMO(imo)
        log.debug { "Deleting all voyages for ship with IMO $imo..." }
        voyageDataSource.deleteAllByIMO(imo)

        // clear current status from memory, start clean
        shipStatuses.remove(imo)

        // process all events, don't send all changes over the queue,
        // and don't generate traces: we'll do that afterwards
        val entrySummariesById = mutableMapOf<String, EntrySummary>()
        var eventsCount = 0
        var changeCount = 0
        try {
            log.info { "Fetching all events for ship with IMO $imo..." }

            val start = THE_BEGINNING_OF_TEQPLAY
            val end = ZonedDateTime.now()

            eventFetchingService.fetchEventsByIMO(imo, start, end).forEach { event ->
                val changes = insert(event, sendToQueue = false, logIssues = false, generateTraceWhenFinished = false, imo)

                // keep a summary of the last version of all entries in a map, we need that for the traces later on
                // we keep only a summary and not the full entry to save memory (an entry can grow large when it
                changes.map { change ->
                    entrySummariesById[change.entry._id] = change.entry.toEntrySummary()
                }

                eventsCount += 1
                changeCount += changes.size
            }
        } catch (e: MMSINotFoundException) {
            log.error(e) { "Failed to fetch all events for ship with imo $imo" }
        }

        // only send the latest status over the queue
        val newStatusEntry = shipStatuses.getEntry(imo)
        if (newStatusEntry != null) {
            // the new status may be *completely* different from the previous status,
            // therefore we sent a REVISE message instead of an UPDATE
            val change = Change(Action.REVISE, newStatusEntry)
            val metadata = entryService.getChangeMetadata(imo)
            outgoingEventsSender?.send(OutgoingChange(change, metadata))
        } else if (oldStatusEntry != null) {
            // there was a status before, and now there isn't anymore: the ship is discontinued
            val change = Change(Action.DISCONTINUE, oldStatusEntry)
            val metadata = entryService.getChangeMetadata(imo)
            outgoingEventsSender?.send(OutgoingChange(change, metadata))
        } else {
            // no old and no new status: nothing to report on the queue
        }

        log.info {
            "Processed $eventsCount events for ship with IMO $imo, " +
                "resulting in $changeCount changes and ${entrySummariesById.size} entries"
        }

        return RegenerateEntriesResult(
            eventsCount = eventsCount,
            changeCount = changeCount,
            entrySummariesById = entrySummariesById
        )
    }

    private fun regenerateAllTracesByIMO(
        imo: String,
        forceRegenerateTraces: Boolean,
        entrySummariesById: Map<String, EntrySummary>
    ) {
        // determine which historic traces for this ship we can keep and which needs to be deleted
        // to save memory, we only keep a trace summary in memory
        val existingTraceSummaries = v1TraceService.getByImo(imo)
            .map { trace -> trace.toTraceSummary() }

        val (validTraces, invalidTraces) = existingTraceSummaries
            .partition { traceSummary ->
                val entrySummary = entrySummariesById[traceSummary.entryId]

                return@partition entrySummary != null &&
                    traceSummary.startTime == entrySummary.startTime &&
                    traceSummary.endTime == entrySummary.endTime &&
                    traceSummary.version == TRACE_ALGORITHM_VERSION &&
                    !forceRegenerateTraces
            }

        // delete the invalid traces
        v1TraceService.deleteHistoricTraces(invalidTraces.map { it.entryId }, imo)
        log.debug { "Deleted ${invalidTraces.size} traces for ship with IMO $imo" }
        log.debug { "Keeping ${validTraces.size} traces for ship with IMO $imo" }

        // regenerate missing traces if configured
        if (traceProperties.historic.generateWhenFinished && entrySummariesById.isNotEmpty()) {
            try {
                val entryIdsWithTrace = validTraces.map { trace -> trace.entryId }.toSet()
                val entryIdsWithoutTrace = entrySummariesById
                    .values
                    .filter { entry -> !entryIdsWithTrace.contains(entry._id) }
                    .filter { entry -> entry.finished }
                    .map { entry -> entry._id }

                entryIdsWithoutTrace.forEach { entryId ->
                    entryService.findEntry(entryId)?.let {
                        v1TraceService.createHistoricTraceFromAIS(it)
                    }
                }

                log.info {
                    "Re-generated ${entryIdsWithoutTrace.size} traces for ship with IMO $imo " +
                        "(deleted ${invalidTraces.size} old traces, kept ${validTraces.size} existing traces)"
                }
            } catch (e: MMSINotFoundException) {
                log.warn { "Cannot rebuild historic traces for IMO $imo: no MMSI's found" }
            }
        }

        // re-generate the ongoing trace for this ship
        v1TraceService.rebuildOngoingTrace(imo)
    }

    private fun persistEntry(change: Change) {
        when (val entry = change.entry) {
            is Visit -> {
                log.debug { "${change.action.toString().lowercase()} visit ${entry._id}" }

                when (change.action) {
                    Action.CREATE,
                    Action.UPDATE,
                    Action.REVISE -> {
                        visitDataSource.createOrReplace(entry)
                    }

                    Action.DELETE,
                    Action.DISCONTINUE -> {
                        visitDataSource.deleteById(entry._id)
                    }
                }
            }

            is Voyage -> {
                log.debug { "${change.action.toString().lowercase()} voyage ${entry._id}" }

                when (change.action) {
                    Action.CREATE,
                    Action.UPDATE,
                    Action.REVISE -> {
                        voyageDataSource.createOrReplace(entry)
                    }

                    Action.DELETE,
                    Action.DISCONTINUE -> {
                        voyageDataSource.deleteById(entry._id)
                    }
                }
            }
        }
    }

    private fun handleEntryTraceChanges(change: NewChange<*>, imo: Int, lastUpdateEntryId: EntryId?): EntryId? {
        val entryValueChange = change.value as? NewEntry

        when (change.action) {
            Action.UPDATE -> {
                if (entryValueChange != null) {
                    return entryValueChange._id
                }
            }
            Action.DELETE -> {
                if (lastUpdateEntryId != null && entryValueChange != null) {
                    processingTraceService.mergeCanceledVisitTraceWithResumedVoyage(
                        imo = imo,
                        canceledVisitId = lastUpdateEntryId,
                        resumedVoyageId = entryValueChange._id
                    )
                }
            }

            // We can only do trace mering on update/delete right now
            else -> {}
        }

        // No update entry id to return
        return null
    }

    private fun updateTrace(change: Change) {
        when (change.action) {
            Action.CREATE,
            Action.UPDATE,
            Action.REVISE -> {
                // a newly created entry *may* be finished directly
                if (change.entry.finished) {
                    v1TraceService.createHistoricTrace(change.entry)
                }
            }

            Action.DELETE,
            Action.DISCONTINUE -> {
                v1TraceService.deleteHistoricTrace(change.entry)

                // Remove the historic trace of the previous entry too
                // (normally a voyage which has become unfinished again after a pass-through visit)
                change.entry.previousEntryId?.let {
                    v1TraceService.deleteHistoricTrace(entryId = it, imo = change.entry.imo)
                }
            }
        }
    }

    fun processDryRun(events: Sequence<Event>): List<ProcessingDryRunResult> {
        var shipStatus: NewShipStatus = NewInitialShipStatus()

        return events.mapNotNull { event ->
            val processTime = Instant.now()

            val result = eventProcessingService.onEventAndBuffer(
                shipStatus = shipStatus,
                event = event,
                processTime = processTime
            )

            // Update the status using the provided result
            shipStatus = result.status

            ProcessingDryRunResult(
                event = event,
                changes = result.changes,
                issues = result.issues
            )
        }.toList()
    }

    fun processTeqplayEventsDryRun(
        events: List<Event>,
        initialStatus: ShipStatus = InitialShipStatus
    ): List<ProcessedEvent> {
        var shipStatus = initialStatus

        return events.map { event ->
            val result = eventProcessingService.onEvent(
                shipStatus = shipStatus,
                event = event
            )

            shipStatus = result.status

            ProcessedEvent(
                teqplayEvent = null,
                event = event,
                changes = result.changes,
                issues = result.issues
            )
        }
    }

    fun processAisEngineEventsDryRunForStoryV2(
        aisEngineEvents: Sequence<Event>
    ): List<EntryESoFWrapper<*>> {
        val processedEvents = processDryRun(aisEngineEvents)
        val entries = mutableListOf<EntryESoFWrapper<*>>()
        processedEvents.forEach { processedEvent ->
            processedEvent.changes.forEach { change ->
                val entryId = when (change) {
                    is VisitChange -> change.value._id
                    is VoyageChange -> change.value._id
                    is ESoFChange -> change.value._id
                }

                when (change.action) {
                    Action.CREATE -> {
                        if (change is ESoFChange) {
                            val matchingEntryId = entries.indexOfLast { it.entry._id == entryId }

                            // When we create a new ESoF we should always update the matching wrapper
                            if (matchingEntryId != -1) {
                                val matchingEntry = entries[matchingEntryId]
                                entries[matchingEntryId] = matchingEntry.copy(esof = change.value)
                            } else {
                                throw Exception("Missing entry, can't create esof (id = $entryId)")
                            }
                        } else {
                            val newWrapper = EntryESoFWrapper(
                                entry = change.value as NewEntry,
                                esof = null
                            )
                            entries.add(newWrapper)
                        }
                    }
                    Action.UPDATE -> {
                        if (change is ESoFChange) {
                            val matchingEntryId = entries.indexOfLast { it.entry._id == entryId }

                            // When we create a new ESoF we should always update the matching wrapper
                            if (matchingEntryId != -1) {
                                val matchingEntry = entries[matchingEntryId]
                                entries[matchingEntryId] = matchingEntry.copy(esof = change.value)
                            } else {
                                throw Exception("Missing entry, can't update esof (id = $entryId)")
                            }
                        } else {
                            val previousEntryIndex = entries.indexOfLast { it.entry._id == entryId }

                            if (previousEntryIndex != -1) {
                                val previousEntry = entries[previousEntryIndex]
                                entries[previousEntryIndex] = EntryESoFWrapper(
                                    entry = change.value as NewEntry,
                                    esof = previousEntry.esof
                                )
                            } else {
                                throw Exception("Missing create action, can't update entry (id = $entryId)")
                            }
                        }
                    }
                    Action.DELETE -> {
                        if (change is ESoFChange) {
                            val matchingEntryId = entries.indexOfLast { it.entry._id == entryId }

                            // Reset the esof of our entry when we found any
                            if (matchingEntryId != -1) {
                                val matchingEntry = entries[matchingEntryId]
                                entries[matchingEntryId] = matchingEntry.copy(esof = null)
                            }
                        } else {
                            entries.removeIf { it.entry._id == entryId }
                        }
                    }

                    Action.REVISE,
                    Action.DISCONTINUE
                    -> {
                        // ignore
                    }
                }
            }
        }

        val newEntries = entries.map { entryEsof ->
            val (entry, esof) = entryEsof

            val entryEnd = entryEsof.entry.end

            // We can only calculate slow moving periods for entries that are finished
            if (entryEnd == null) {
                entryEsof
            } else {
                val slowMovingPeriods = slowMovingService.determineSlowMovingSegments(
                    entry.imo,
                    entryEsof.entry.start.time,
                    entryEnd.time
                )

                val updatedEsof = esof?.copy(slowMovingPeriods = slowMovingPeriods)
                    ?: NewESoF(
                        _id = entry._id,
                        encounters = emptyList(),
                        slowMovingPeriods = slowMovingPeriods,
                        shipToShipTransfers = emptyList()
                    )

                entryEsof.copy(esof = updatedEsof)
            }
        }
        return newEntries
    }

    fun processAisEngineEventsDryRunForStory(
        aisEngineEvents: Sequence<Event>
    ): List<Entry> {
        val processedEvents = processAisEngineEventsDryRun(aisEngineEvents, InitialShipStatus)
        val entries = mutableListOf<Entry>()
        processedEvents.forEach { processedEvent ->
            processedEvent.changes.forEach { change ->
                when (change.action) {
                    Action.CREATE -> {
                        entries.add(change.entry)
                    }
                    Action.UPDATE -> {
                        val previousEntryIndex = entries.indexOfLast { it._id == change.entry._id }
                        if (previousEntryIndex != -1) {
                            entries[previousEntryIndex] = change.entry
                        } else {
                            entries.add(change.entry)
                        }
                    }
                    Action.DELETE -> {
                        entries.removeIf { it._id == change.entry._id }
                    }

                    Action.REVISE,
                    Action.DISCONTINUE -> {
                        // ignore
                    }
                }
            }
        }

        return entries
    }

    private fun processAisEngineEventsDryRun(
        aisEngineEvents: Sequence<Event>,
        initialStatus: ShipStatus
    ): Sequence<ProcessedAisEngineEvent> {
        var shipStatus = initialStatus

        return aisEngineEvents.map { event ->
            val result = eventProcessingService.onEvent(
                shipStatus = shipStatus,
                event = event
            )

            shipStatus = result.status

            ProcessedAisEngineEvent(
                aisEngineEvent = event,
                changes = result.changes,
                issues = result.issues
            )
        }
    }

    private data class EntrySummary(
        val _id: String,
        val startTime: ZonedDateTime,
        val endTime: ZonedDateTime?,
        val finished: Boolean
    )

    private fun Entry.toEntrySummary() = EntrySummary(
        _id = this._id,
        startTime = this.startTime,
        endTime = this.endTime,
        finished = this.finished
    )

    private data class TraceSummary(
        val entryId: String,
        val startTime: ZonedDateTime,
        val endTime: ZonedDateTime?,
        val version: Int
    )

    private fun HistoricTrace.toTraceSummary() = TraceSummary(
        entryId = this.entryId,
        startTime = this.startTime,
        endTime = this.endTime,
        version = this.version
    )
}
