package nl.teqplay.vesselvoyage.service

import com.github.seratch.jslack.Slack
import com.github.seratch.jslack.api.model.Attachment
import com.github.seratch.jslack.api.webhook.Payload
import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.properties.HealthProperties
import nl.teqplay.vesselvoyage.util.truncate
import org.springframework.stereotype.Service

const val SLACK_COLOR_RED = "#FF5722"
const val SLACK_COLOR_GREEN = "#4CAF50"

@ProfileProcessing
@Service
class SlackMessageService(
    private val properties: HealthProperties,
    private val slackClient: Slack?
) {
    private val log = KotlinLogging.logger {}

    fun sendMessage(title: String, text: String, barColor: String) {
        if (slackClient == null) {
            // Slack messages are not enabled
            return
        }

        val attachment = Attachment.builder()
            .title(title)
            .fallback(title)
            .text(text)
            .color(barColor)
            .build()
        val payload = Payload.builder()
            .attachments(listOf(attachment))
            .build()

        sendMessage(payload, slackClient)
    }

    private fun sendMessage(payload: Payload, slackClient: Slack) {
        try {
            log.info { "sending slack message ${payload.text.truncate(100)}" }

            val response = slackClient.send(properties.slack.webhook, payload)
            if (response.code != 200) {
                log.warn { "sendMessage: Something went wrong while invoking Slack webhook: ${response.code} : ${response.body}" }
            }
        } catch (e: Exception) {
            log.warn(e) { "Unexpected error while sending message to Slack Webhook" }
        }
    }
}
