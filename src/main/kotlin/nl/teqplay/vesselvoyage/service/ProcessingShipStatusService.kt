package nl.teqplay.vesselvoyage.service

import io.github.oshai.kotlinlogging.KotlinLogging
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.skeleton.metrics.Metric
import nl.teqplay.skeleton.metrics.MetricRegistry
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.datasource.NewESoFDataSource
import nl.teqplay.vesselvoyage.datasource.NewShipStatusStateDataSource
import nl.teqplay.vesselvoyage.datasource.NewVisitDataSource
import nl.teqplay.vesselvoyage.datasource.NewVoyageDataSource
import nl.teqplay.vesselvoyage.datasource.VisitDataSource
import nl.teqplay.vesselvoyage.datasource.VoyageDataSource
import nl.teqplay.vesselvoyage.model.Entry
import nl.teqplay.vesselvoyage.model.ShipStatus
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import org.springframework.context.annotation.Primary
import org.springframework.stereotype.Service
import java.time.Duration
import java.time.Instant
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong

@ProfileProcessing
@Primary
@Service
class ProcessingShipStatusService(
    visitDataSource: VisitDataSource,
    voyageDataSource: VoyageDataSource,
    newVisitDataSource: NewVisitDataSource,
    newVoyageDataSource: NewVoyageDataSource,
    newESoFDataSource: NewESoFDataSource,
    private val newShipStatusStateDataSource: NewShipStatusStateDataSource,
    meterRegistry: MeterRegistry
) : ShipStatusService(
    visitDataSource,
    voyageDataSource,
    newVisitDataSource,
    newVoyageDataSource,
    newESoFDataSource,
    newShipStatusStateDataSource
) {
    companion object {
        private const val TAG_STATE_KEY = "state_type"
        private const val TAG_STATE_VALUE_VISITS = "ongoing-visits"
        private const val TAG_STATE_VALUE_VOYAGES = "ongoing-voyages"
        private const val TAG_STATE_VALUE_NO_STATE = "no-state"
    }
    private val log = KotlinLogging.logger {}

    private val metricRegistry = MetricRegistry(this::class, meterRegistry, listOf(TAG_STATE_KEY))
    private val totalOngoingVisits = metricRegistry.createGauge(Metric.STATE_SIZE, AtomicLong(0), TAG_STATE_VALUE_VISITS)
    private val totalOngoingVoyages = metricRegistry.createGauge(Metric.STATE_SIZE, AtomicLong(0), TAG_STATE_VALUE_VOYAGES)
    private val totalNoState = metricRegistry.createGauge(Metric.STATE_SIZE, AtomicLong(0), TAG_STATE_VALUE_NO_STATE)

    private val shipStatusesByIMO = ConcurrentHashMap<String, ShipStatus>()
    private val newShipStatusesByImo = ConcurrentHashMap<Int, NewShipStatus>()

    init {
        log.info { "Preparing to load in ships" }
        val now = Instant.now()
        val yesterday = now.minus(Duration.ofDays(1))

        val visitImos = newVisitDataSource.distinctImos(yesterday)
        val voyageImos = newVoyageDataSource.distinctImos(yesterday)

        val imosToLoad = visitImos + voyageImos
        val totalLoaded = AtomicLong(0)
        log.info { "Start loading in ${imosToLoad.size} ships with an IMO" }

        imosToLoad.parallelStream().forEach { imo ->
            // Load V1
            get(imo.toString())

            // Load V2
            getStatus(imo)

            // Log every 2000 loaded in ships
            val currentlyLoadedIn = totalLoaded.incrementAndGet()
            if (currentlyLoadedIn % 2000 == 0L) {
                log.info { "Loaded in $currentlyLoadedIn ships out of ${imosToLoad.size}" }
            }
        }
        log.info { "Done loading in ${imosToLoad.size} ships with an IMO" }
    }

    fun updateStatus(imo: Int, updatedStatus: NewShipStatus) {
        val currentShipStatus = getStatus(imo)

        // Only count if we updated the status to something different
        if (updatedStatus != currentShipStatus) {
            // Count 1 down for the old status
            decrementCountWithShipStatus(currentShipStatus)

            // Count up again for the updated status
            incrementCountWithShipStatus(updatedStatus)
        }

        newShipStatusesByImo[imo] = updatedStatus
        newShipStatusStateDataSource.save(imo, updatedStatus)
    }

    @Deprecated("V1")
    override operator fun get(imo: String): ShipStatus {
        return shipStatusesByIMO.computeIfAbsent(imo) {
            getCurrentStatus(it)
        }
    }

    override fun getStatus(imo: Int): NewShipStatus {
        return newShipStatusesByImo.computeIfAbsent(imo) {
            val currentStatus = getCurrentNewStatus(it)
            incrementCountWithShipStatus(currentStatus)
            currentStatus
        }
    }

    @Deprecated("V1")
    override fun getEntry(imo: String): Entry? {
        val status = get(imo)

        return when (status) {
            is VisitShipStatus -> status.visit
            is VoyageShipStatus -> status.voyage
            else -> null
        }
    }

    @Deprecated("V1")
    override operator fun set(imo: String, status: ShipStatus) {
        shipStatusesByIMO[imo] = status
    }

    @Deprecated("V1")
    override fun remove(imo: String) {
        shipStatusesByIMO.remove(imo)
    }

    /**
     * Returns the latest ongoing entry for trace processing
     */
    fun getLatestOngoingEntry(imo: Int): NewEntry? {
        val status = getStatus(imo)

        return when (status) {
            is NewVisitShipStatus -> { status.visit.entry }
            is NewVoyageShipStatus -> { status.voyage.entry }
            is NewInitialShipStatus -> null
        }
    }

    override fun removeStatus(imo: Int) {
        newShipStatusesByImo.remove(imo)
    }

    private fun incrementCountWithShipStatus(shipStatus: NewShipStatus) {
        when (shipStatus) {
            is NewInitialShipStatus -> totalNoState.incrementAndGet()
            is NewVisitShipStatus -> totalOngoingVisits.incrementAndGet()
            is NewVoyageShipStatus -> totalOngoingVoyages.incrementAndGet()
        }
    }

    private fun decrementCountWithShipStatus(shipStatus: NewShipStatus) {
        when (shipStatus) {
            is NewInitialShipStatus -> totalNoState.decrementAndGet()
            is NewVisitShipStatus -> totalOngoingVisits.decrementAndGet()
            is NewVoyageShipStatus -> totalOngoingVoyages.decrementAndGet()
        }
    }
}
