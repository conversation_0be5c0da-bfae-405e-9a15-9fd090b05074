package nl.teqplay.vesselvoyage.service

import com.fasterxml.jackson.core.type.TypeReference
import io.github.oshai.kotlinlogging.KotlinLogging
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import nl.teqplay.csi.model.ship.info.ShipRegisterInfoCache
import nl.teqplay.csi.model.ship.info.component.ShipCategories
import nl.teqplay.csi.model.ship.info.component.ShipCategoryV1
import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2
import nl.teqplay.csi.model.ship.mapping.ImoMmsiMapping
import nl.teqplay.csi.model.ship.mapping.ShipRegisterMapping
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.annotation.ProfileRevents
import nl.teqplay.vesselvoyage.client.csi.CSIClient
import nl.teqplay.vesselvoyage.datasource.TempFileDataSource
import nl.teqplay.vesselvoyage.model.Filter
import nl.teqplay.vesselvoyage.model.SEARCH_SHIPS_DEFAULT_LIMIT
import nl.teqplay.vesselvoyage.model.ShipDetails
import nl.teqplay.vesselvoyage.properties.ApplicationProperties
import nl.teqplay.vesselvoyage.properties.DiskCacheProperties
import nl.teqplay.vesselvoyage.util.DiskCache
import nl.teqplay.vesselvoyage.util.intersect
import nl.teqplay.vesselvoyage.util.toEpochMillisecond
import nl.teqplay.vesselvoyage.util.toShipDetails
import nl.teqplay.vesselvoyage.util.toV2Categories
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.time.ZonedDateTime
import kotlin.system.measureTimeMillis

private val log = KotlinLogging.logger {}

class ShipRegisterInfoCacheListRef : TypeReference<List<ShipRegisterInfoCache>>()
class ShipRegisterMappingListRef : TypeReference<List<ShipRegisterMapping>>()

@ProfileApi
@ProfileProcessing
@ProfileRevents
@OptIn(DelicateCoroutinesApi::class)
@Service
class StaticShipInfoService(
    diskCacheProperties: DiskCacheProperties,
    private val appProperties: ApplicationProperties,
    private val csiClient: CSIClient,
    private val slackMessageService: SlackMessageService?,
    private val shipCacheService: ShipCacheService,
    tempFileDataSource: TempFileDataSource
) {
    private val csiShipsDiskCache = DiskCache(
        enabled = diskCacheProperties.enabled,
        filename = diskCacheProperties.csiShipsFile,
        fetch = {
            csiClient.listShipRegisterInfoCache()
        },
        tempFileDataSource = tempFileDataSource,
        typeReference = ShipRegisterInfoCacheListRef()
    )

    private val csiShipRegistryDiskCache = DiskCache(
        enabled = diskCacheProperties.enabled,
        filename = diskCacheProperties.csiShipRegistryFile,
        fetch = {
            csiClient.listShipRegisterMapping()
        },
        tempFileDataSource = tempFileDataSource,
        typeReference = ShipRegisterMappingListRef()
    )

    private suspend fun loadMapping() = coroutineScope {
        val csiMapping = launch { csiShipsDiskCache.load(::loadCSIShipMappings) }
        val csiShipRegister = launch { csiShipRegistryDiskCache.load(::loadCSIShipRegisterMappings) }

        csiMapping.join()
        csiShipRegister.join()
    }

    @Scheduled(cron = "\${ship-info.refresh.cron}", zone = "UTC")
    fun onShipRefresh() {
        val startTime = ZonedDateTime.now()
        try {
            log.info { "Start refreshing ships" }
            refresh()
            log.info { "Finished refreshing ships" }
        } catch (e: Throwable) {
            val failTime = ZonedDateTime.now()
            log.error(e) { "Error refreshing ships: ${e.message}" }
            slackMessageService?.sendMessage(
                title = "Scheduled task failed: StaticShipInfoService.onShipRefresh",
                text = "Error refreshing ships: ${e.message}\nStarted at: $startTime\nFailed at: $failTime",
                barColor = SLACK_COLOR_RED
            )
        }
    }

    suspend fun onStartup() = loadMapping()

    fun isShipCacheValid(): Boolean = shipCacheService.isShipCacheValid()

    fun refresh() = runBlocking { onRefresh() }

    private suspend fun onRefresh() {
        GlobalScope.launch(Dispatchers.Default) {
            launch { refreshCsiShips() }
            launch { refreshCsiShipRegister() }
        }.join()
    }

    private suspend fun refreshCsiShips() {
        try {
            val duration = measureTimeMillis {
                updateCSIShipMappings(csiShipsDiskCache.refresh())
            }
            log.info("Refreshed CSI ships in $duration ms")
        } catch (err: Throwable) {
            log.error(err) { "Failed to refresh ships from CSI" }
            slackMessageService?.sendMessage(
                title = "VesselVoyage ${appProperties.environment} fetching of ships from CSI failed",
                text = "Fetching will be tried again later. Error message: ${err.message}",
                barColor = SLACK_COLOR_RED
            )
        }
    }

    private suspend fun refreshCsiShipRegister() {
        try {
            val duration = measureTimeMillis {
                updateCSIShipRegisterMappings(csiShipRegistryDiskCache.refresh())
            }
            log.info("Refreshed CSI ship register mapping in $duration ms")
        } catch (err: Throwable) {
            log.error(err) { "Failed to refresh ship register mapping from CSI" }
            slackMessageService?.sendMessage(
                title = "VesselVoyage ${appProperties.environment} fetching of ship register mapping from CSI failed",
                text = "Fetching will be tried again later. Error message: ${err.message}",
                barColor = SLACK_COLOR_RED
            )
        }
    }

    private fun loadCSIShipMappings(allShipsWithImo: List<ShipRegisterInfoCache>) {
        val totalLoaded = shipCacheService.loadCsiCache(allShipsWithImo)
        log.info("Loaded $totalLoaded out of the ${allShipsWithImo.size} CSI ships")
    }

    private fun updateCSIShipMappings(allShipsWithImo: List<ShipRegisterInfoCache>) {
        val totalUpdated = shipCacheService.updateCsiCache(allShipsWithImo)
        log.info("Updated $totalUpdated CSI ships")
    }

    private fun loadCSIShipRegisterMappings(allShipRegisterMapping: List<ShipRegisterMapping>) {
        shipCacheService.loadCsiImoMmsiMappingCache(allShipRegisterMapping)
        log.info("Loaded ${allShipRegisterMapping.size} CSI ship register mapping")
    }

    private fun updateCSIShipRegisterMappings(allShipRegisterMapping: List<ShipRegisterMapping>) {
        shipCacheService.updateCsiImoMmsiMappingCache(allShipRegisterMapping)
        log.info("Updated ${allShipRegisterMapping.size} CSI ship register mapping")
    }

    fun getImoFromMmsi(mmsi: String): String? {
        return shipCacheService.getImoByMmsi(mmsi)
    }

    fun getCurrentMmsiFromImo(imo: String): String? {
        return shipCacheService.getCurrentMmsiByImo(imo)
    }

    fun getMmsiMappingFromImo(imo: String) = shipCacheService.getCacheByImo(imo)?.csiImoMmsiCache?.mapping

    fun getMmsiMappingFromImoAndDateRange(imo: String, from: ZonedDateTime, until: ZonedDateTime? = null): List<ImoMmsiMapping>? {
        return shipCacheService.getCacheByImo(imo)?.csiImoMmsiCache?.mapping
            ?.filter { mapping -> mapping.isActive(from, until) }
    }

    fun getShipRegisterInfoCacheByIMO(imo: String): ShipRegisterInfoCache? {
        return shipCacheService.getCacheByImo(imo)?.csi?.register
    }

    fun getShipRegisterInfoCacheByMMSI(mmsi: String): ShipRegisterInfoCache? {
        return shipCacheService.getCachyByMmsi(mmsi)?.csi?.register
    }

    fun getShipCategoriesByIMO(imo: String): ShipCategories? {
        return getShipRegisterInfoCacheByIMO(imo)?.categories
    }

    fun getShipCategoryByIMO(imo: String): ShipCategoryV2? {
        return getShipCategoriesByIMO(imo)?.v2
    }

    fun getImosByCategory(category: ShipCategoryV2): Set<Int> {
        return shipCacheService.getShipImosByCategory(category)
            .map { it.toInt() }
            .toSet()
    }

    fun getImosByCategories(categories: Set<ShipCategoryV2>): Set<Int> {
        return categories.map { category -> getImosByCategory(category) }
            .flatten()
            .toSet()
    }

    fun getShipsByCategory(categoryV2: ShipCategoryV2): Set<String> {
        return shipCacheService.getShipImosByCategory(categoryV2)
    }

    fun getShipsByCategories(categories: Set<ShipCategoryV2>?, backupV1Categories: Set<ShipCategoryV1>?): Set<String>? {
        val v2Categories = if (categories.isNullOrEmpty()) {
            backupV1Categories?.toV2Categories()
        } else {
            categories
        }

        return v2Categories?.flatMap { getShipsByCategory(it) }?.toSet()
    }

    fun getShipsByDeadWeightTonnages(deadWeightTonnages: List<Filter.Range<Double>>): Set<String> {
        if (deadWeightTonnages.isEmpty()) return emptySet()

        return shipCacheService.getFullCache().filterValues { shipCache ->
            val register = shipCache.csi?.register

            if (register != null) {
                val dwt = register.specification.deadWeightTonnage
                dwt != null && deadWeightTonnages.any { it.contains(dwt) }
            } else {
                false
            }
        }.keys
    }

    fun applyFilter(filter: Filter?): Set<String>? {
        if (filter == null) return null

        val perCategory = filter.perCategory
        return if (perCategory != null) {
            perCategory.flatMap { getShipsPerCategory(it) }.toSet()
        } else {
            val categoryImos = getShipsByCategories(filter.categoriesV2, filter.categoriesV1)
            val deadWeightTonnageImos = filter.deadWeightTonnages?.let { getShipsByDeadWeightTonnages(it) }
            intersect(categoryImos, deadWeightTonnageImos)
        }
    }

    private fun getShipsPerCategory(perCategory: Filter.PerCategory): Set<String> {
        val imos = getShipsByCategory(perCategory.v2)
        val deadWeightTonnages = perCategory.deadWeightTonnages
        val twentyFootEquivalentUnits = perCategory.twentyFootEquivalentUnits
        if (deadWeightTonnages == null && twentyFootEquivalentUnits == null) {
            return imos
        } else {
            return imos.filterTo(hashSetOf()) { imo ->
                val register = getShipRegisterInfoCacheByIMO(imo)
                val dwt = register?.specification?.deadWeightTonnage
                val teu = register?.calculated?.twentyFootEquivalentUnit

                val dwtMatches = deadWeightTonnages == null ||
                    (dwt != null && deadWeightTonnages.any { it.contains(dwt) })
                val teuMatches = twentyFootEquivalentUnits == null ||
                    (teu != null && twentyFootEquivalentUnits.any { it.contains(teu) })

                dwtMatches && teuMatches
            }
        }
    }

    fun getAllImos(): Set<String> = shipCacheService.getAllImos()

    fun getShipDetailsByIMO(imo: String): ShipDetails? = getShipRegisterInfoCacheByIMO(imo)?.toShipDetails()

    fun getShipDetailsByMMSI(mmsi: String): ShipDetails? {
        val imo = getImoFromMmsi(mmsi)

        val cacheRegister = if (imo != null) {
            getShipRegisterInfoCacheByIMO(imo)
        } else {
            getShipRegisterInfoCacheByMMSI(mmsi)
        }

        // Always make use of the cache register known from CSI when available
        if (cacheRegister != null) {
            return cacheRegister.toShipDetails()
        }

        return null
    }

    fun searchShipDetails(query: String, limit: Int?): List<ShipDetails> {
        // TODO: Right now we use the in memory map with ships to search for consistency,
        //  to only find ships that VesselVoyage itself has in memory and processes.
        //  In the future we probably want to proxy the query to CSI.
        val queryLowerCase = query.lowercase()

        val allShipDetails = shipCacheService.getFullCache().values
            .mapNotNull { it.csi?.register?.toShipDetails() }

        return allShipDetails
            .filter { ship ->
                (ship.name?.lowercase()?.contains(queryLowerCase) ?: false) ||
                    (ship.imo?.contains(queryLowerCase) ?: false) ||
                    ship.mmsi.contains(queryLowerCase)
            }
            .sortedBy { it.name }
            .take(limit ?: SEARCH_SHIPS_DEFAULT_LIMIT)
    }

    /**
     * Helper function which calls the [ImoMmsiMapping.isActive] for both the given parameters.
     * This also handles an edge case where the start and end time of a mapping wouldn't be found if the mapping was in a smaller date range.
     */
    private fun ImoMmsiMapping.isActive(from: ZonedDateTime, until: ZonedDateTime?): Boolean {
        val epochMilliFrom = from.toEpochMillisecond()
        val epochMilliUntil = until?.toEpochMillisecond()

        val mappingFrom = this.from
        val mappingTo = this.to

        val isActiveInFrom = mappingFrom == null || epochMilliUntil == null || mappingFrom < epochMilliUntil
        val isActiveInTo = mappingTo == null || mappingTo > epochMilliFrom

        if (isActiveInFrom && isActiveInTo) {
            return true
        }

        return this.isActive(epochMilliFrom) && if (epochMilliUntil != null) {
            this.isActive(epochMilliUntil)
        } else {
            true
        }
    }
}
