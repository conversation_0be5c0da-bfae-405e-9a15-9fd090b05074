package nl.teqplay.vesselvoyage.service.recalculation

import io.github.oshai.kotlinlogging.KotlinLogging
import jakarta.annotation.PostConstruct
import nl.teqplay.aisengine.revents.client.ReventsClient
import nl.teqplay.aisengine.revents.client.ReventsVesselVoyageMergingClient
import nl.teqplay.aisengine.revents.client.ReventsVesselVoyageMergingV2Client
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.InterestShip
import nl.teqplay.aisengine.reventsengine.model.scenario.Scenario
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioCreateRequest
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioEvent
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioGuarantee.VESSEL_VOYAGE_MERGING
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioGuarantee.VESSEL_VOYAGE_MERGING_V2
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioResponse
import nl.teqplay.aisengine.reventsengine.model.vesselvoyage.MergeEntry
import nl.teqplay.aisengine.reventsengine.model.vesselvoyage.MergeEntryV2
import nl.teqplay.skeleton.common.exception.BadRequestException
import nl.teqplay.skeleton.common.exception.NotFoundException
import nl.teqplay.skeleton.model.TimeWindow
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.datasource.AutomaticRecalculationShipDataSource
import nl.teqplay.vesselvoyage.datasource.NewESoFDataSource
import nl.teqplay.vesselvoyage.datasource.NewVisitDataSource
import nl.teqplay.vesselvoyage.datasource.NewVoyageDataSource
import nl.teqplay.vesselvoyage.datasource.ReventsRecalculationsDataSource
import nl.teqplay.vesselvoyage.datasource.VisitDataSource
import nl.teqplay.vesselvoyage.datasource.VoyageDataSource
import nl.teqplay.vesselvoyage.model.Entry
import nl.teqplay.vesselvoyage.model.MERGE_SUFFIX
import nl.teqplay.vesselvoyage.model.RecalculationMergeResult
import nl.teqplay.vesselvoyage.model.RecalculationPortResult
import nl.teqplay.vesselvoyage.model.RecalculationResult
import nl.teqplay.vesselvoyage.model.RecalculationShipResult
import nl.teqplay.vesselvoyage.model.RecalculationShipsResult
import nl.teqplay.vesselvoyage.model.RecalculationTrackResult
import nl.teqplay.vesselvoyage.model.ReventsRecalculationStatus
import nl.teqplay.vesselvoyage.model.ReventsRecalculationStatus.Error
import nl.teqplay.vesselvoyage.model.ReventsRecalculationStatus.ErrorLevel.ERROR
import nl.teqplay.vesselvoyage.model.ReventsRecalculationStatus.ErrorLevel.WARNING
import nl.teqplay.vesselvoyage.model.ReventsRecalculationStatus.ErrorType.ILLEGAL_MERGE
import nl.teqplay.vesselvoyage.model.ReventsRecalculationStatus.ErrorType.INVALID_WINDOW
import nl.teqplay.vesselvoyage.model.ReventsRecalculationStatus.ErrorType.MERGING_NOT_ALLOWED
import nl.teqplay.vesselvoyage.model.ReventsRecalculationStatus.ErrorType.REQUEST_FOR_ENTRIES_FAILED
import nl.teqplay.vesselvoyage.model.ReventsRecalculationStatus.ErrorType.REQUEST_FOR_INTERESTS_FAILED
import nl.teqplay.vesselvoyage.model.ReventsRecalculationStatus.ErrorType.STOPPED_EXCEPTION
import nl.teqplay.vesselvoyage.model.ReventsRecalculationStatus.Phase.FINISHED
import nl.teqplay.vesselvoyage.model.ReventsRecalculationStatus.Phase.PARTIALLY_FAILED
import nl.teqplay.vesselvoyage.model.ReventsRecalculationStatus.Phase.PROGRESSING
import nl.teqplay.vesselvoyage.model.ReventsRecalculationStatus.Phase.STOPPED
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.model.Voyage
import nl.teqplay.vesselvoyage.model.internal.AutomaticRecalculationShip.RecalculationState
import nl.teqplay.vesselvoyage.model.v2.EntryESoFWrapper
import nl.teqplay.vesselvoyage.model.v2.EntryId
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import nl.teqplay.vesselvoyage.properties.RecalculationProperties
import nl.teqplay.vesselvoyage.service.ImoLockService
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.PostProcessingService
import nl.teqplay.vesselvoyage.service.SLACK_COLOR_RED
import nl.teqplay.vesselvoyage.service.ShipStatusService
import nl.teqplay.vesselvoyage.service.SlackMessageService
import nl.teqplay.vesselvoyage.service.V1TraceService
import nl.teqplay.vesselvoyage.service.merge.EntriesMergeService
import nl.teqplay.vesselvoyage.service.merge.EntriesMergeV1Service
import nl.teqplay.vesselvoyage.service.merge.EntriesMergeV2Service
import nl.teqplay.vesselvoyage.service.merge.NoEntriesInWindowException
import nl.teqplay.vesselvoyage.service.trace.ProcessingTraceService
import nl.teqplay.vesselvoyage.util.Constants
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import org.springframework.web.client.HttpClientErrorException
import java.time.Instant
import java.time.ZonedDateTime
import java.util.UUID
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.TimeUnit

/**
 * Service that schedules running (r)events scenarios, as well as merge data based on these scenarios.
 */
@ProfileProcessing
@Service
class ReventsRecalculationService(
    private val properties: RecalculationProperties,
    private val imoLockService: ImoLockService,
    private val reventsRecalculationsDataSource: ReventsRecalculationsDataSource,

    private val reventsConversionService: ReventsConversionService,
    private val reventsClient: ReventsClient,
    private val shipStatusService: ShipStatusService,

    private val reventsMergingClient: ReventsVesselVoyageMergingClient,
    private val entriesMergeV1Service: EntriesMergeV1Service,
    private val visitDataSource: VisitDataSource,
    private val voyageDataSource: VoyageDataSource,
    private val v1TraceService: V1TraceService,

    private val reventsMergingV2Client: ReventsVesselVoyageMergingV2Client,
    private val entriesMergeV2Service: EntriesMergeV2Service,
    private val newVisitDataSource: NewVisitDataSource,
    private val newVoyageDataSource: NewVoyageDataSource,
    private val newESoFDataSource: NewESoFDataSource,
    private val processingTraceService: ProcessingTraceService,
    private val automaticRecalculationShipDataSource: AutomaticRecalculationShipDataSource,
    private val recalculationEnlargeTimeWindowService: RecalculationEnlargeTimeWindowService,
    private val infraService: InfraService,
    private val postProcessingService: PostProcessingService?,
    private val slackMessageService: SlackMessageService?,
) {

    private val log = KotlinLogging.logger {}

    /**
     * Contains an in-memory list of which scenarios are currently running.
     */
    private val runningScenarios = ConcurrentLinkedQueue<String>()

    @PostConstruct
    fun init() {
        reventsRecalculationsDataSource.findAllRunning().forEach { recalculation ->
            runningScenarios.add(recalculation.revents.scenarioId)
        }
    }

    /**
     * Find all recalculations.
     */
    fun findAll(): List<RecalculationResult> {
        return reventsRecalculationsDataSource.findAll()
    }

    /**
     * Find all recalculations by [imo].
     */
    fun findAllByImo(
        imo: Int
    ): List<RecalculationResult> {
        return reventsRecalculationsDataSource.findAllByImo(imo)
    }

    /**
     * Find recalculation by [reventsScenarioId], if it exists.
     */
    fun findById(reventsScenarioId: String): RecalculationResult? {
        return reventsRecalculationsDataSource.findById(reventsScenarioId)
    }

    /**
     * Start tracking a scenario by [scenarioId].
     */
    fun trackScenario(
        scenarioId: String
    ): RecalculationResult {
        val response = getScenario(scenarioId)
            ?: throw NotFoundException("Scenario with given id: $scenarioId cannot be found")
        val revents = reventsConversionService.convertToStatus(response)
        val result = when (val databaseResult = reventsRecalculationsDataSource.findById(revents.scenarioId)) {
            is RecalculationShipsResult -> databaseResult.copy(revents = revents)
            is RecalculationPortResult -> databaseResult.copy(revents = revents)
            is RecalculationShipResult -> databaseResult.copy(revents = revents)
            else -> RecalculationTrackResult(
                revents = revents
            )
        }

        reventsRecalculationsDataSource.save(result)
        addScenario(revents)
        return result
    }

    fun recalculateShipForAutomaticRecalculation(
        imos: Set<Int>,
        start: Instant,
        end: Instant
    ): RecalculationShipsResult {
        val timeWindow = TimeWindow(from = start, to = end)
        val scenarioCreateRequest = ScenarioCreateRequest(
            window = timeWindow,
            events = setOf(
                ScenarioEvent.ANCHOR,
                ScenarioEvent.AREA,
                ScenarioEvent.BERTH,
                ScenarioEvent.STOP,
                ScenarioEvent.ENCOUNTER
            ),
            guarantees = setOf(VESSEL_VOYAGE_MERGING_V2),
            settings = Scenario.Settings(
                persistEvents = false,
                resolveScenarioEventsByPostProcessing = false
            ),
            interests = imos.map { imo ->
                InterestShip(ship = InterestShip.Identifier(imo = imo))
            }
        )

        val reventsStatus = runScenario(scenarioCreateRequest)
        val result = RecalculationShipsResult(
            imos = imos.toList(),
            revents = reventsStatus,
            automated = true
        )
        reventsRecalculationsDataSource.save(result)

        return result
    }

    /**
     * Recalculate a port by [unlocode] between the [start] and [end] times.
     */
    fun recalculateByPort(
        unlocode: String,
        start: Instant,
        end: Instant,
        guarantee: ReventsRecalculationStatus.Guarantee,
        username: String,
        includeHistoricData: Boolean = false,
    ): RecalculationPortResult {
        if (!start.isBefore(end)) {
            throw BadRequestException("Start time should be before end time.")
        }

        val pomaPortId = infraService.getPortByUnlocode(unlocode)?._id
            ?: throw BadRequestException("Port with unlocode $unlocode not found.")

        val window = TimeWindow(start, end)
        val scenario = reventsConversionService.createScenarioRequestForPort(pomaPortId, window, guarantee, username, includeHistoricData)
        val revents = runScenario(scenario)
        val result = RecalculationPortResult(
            unlocode = unlocode,
            revents = revents,
            automated = false
        )
        reventsRecalculationsDataSource.save(result)
        return result
    }

    /**
     * Recalculate a ship by [imo] between the [start] and [end] times.
     */
    fun recalculateByShip(
        imo: Int,
        start: Instant,
        end: Instant,
        guarantee: ReventsRecalculationStatus.Guarantee,
        username: String,
        includeHistoricData: Boolean = false,
    ): RecalculationShipResult {
        if (!start.isBefore(end)) {
            throw BadRequestException("Start time should be before end time.")
        }

        val window = when (guarantee) {
            ReventsRecalculationStatus.Guarantee.V1 -> recalculationEnlargeTimeWindowService.enlargeTimeWindowBasedOnVoyagesV1(imo, start, end)
            ReventsRecalculationStatus.Guarantee.V2 -> recalculationEnlargeTimeWindowService.enlargeTimeWindowBasedOnVoyagesV2(imo, start, end)
        }
        val scenario = reventsConversionService.createScenarioRequestForShip(imo, window, guarantee, username, includeHistoricData)
        val revents = runScenario(scenario)
        val result = RecalculationShipResult(
            imo = imo,
            revents = revents,
            automated = false
        )
        reventsRecalculationsDataSource.save(result)
        return result
    }

    /**
     * Recalculate a ships by [imos] between the [start] and [end] times.
     */
    fun recalculateByShips(
        imos: Set<Int>,
        start: Instant,
        end: Instant,
        guarantee: ReventsRecalculationStatus.Guarantee,
        username: String,
        isRetryAttempt: Boolean = false,
        includeHistoricData: Boolean = false,
    ): RecalculationShipsResult {
        if (!start.isBefore(end)) {
            throw BadRequestException("Start time should be before end time.")
        }

        val windows = imos.map { imo ->
            when (guarantee) {
                ReventsRecalculationStatus.Guarantee.V1 -> recalculationEnlargeTimeWindowService.enlargeTimeWindowBasedOnVoyagesV1(imo, start, end)
                ReventsRecalculationStatus.Guarantee.V2 -> recalculationEnlargeTimeWindowService.enlargeTimeWindowBasedOnVoyagesV2(imo, start, end)
            }
        }

        val minStart = windows.minOf(TimeWindow::from)
        val maxEnd = windows.maxOf(TimeWindow::to)
        val window = TimeWindow(from = minStart, to = maxEnd)

        val scenario = reventsConversionService.createScenarioRequestForShips(imos, window, guarantee, username, isRetryAttempt, includeHistoricData)
        val revents = runScenario(scenario)
        val result = RecalculationShipsResult(
            imos = imos.toList(),
            revents = revents,
            automated = false
        )
        reventsRecalculationsDataSource.save(result)
        return result
    }

    /**
     * Run the specified [scenario], creating it by sending it off to (r)events.
     * As well as queueing it up to check for its progress.
     */
    fun runScenario(scenario: ScenarioCreateRequest): ReventsRecalculationStatus {
        val response = reventsClient.createScenario(scenario)
        runningScenarios.add(response.id)
        return reventsConversionService.convertToStatus(response)
    }

    /**
     * Add a scenario based on the [status], only adding it to
     * [runningScenarios] if it's not already added.
     */
    fun addScenario(status: ReventsRecalculationStatus): Boolean {
        val id = status.scenarioId
        if (id in runningScenarios) {
            return false
        }
        runningScenarios.add(id)
        return true
    }

    /**
     * Gets the scenario by [scenarioId]
     *
     * If the scenario is not available at the revents side, we stop the scenario
     * If the scenario is not available at the vesselvoyage and revents side we ignore.
     */
    fun getScenario(scenarioId: String): ScenarioResponse? {
        try {
            return reventsClient.getScenario(scenarioId)
        } catch (exception: HttpClientErrorException.NotFound) {
            log.warn { "Scenario not found on revents side, stopping $scenarioId" }

            // When scenario is not available on vesselvoyage side, ignore
            val status = reventsRecalculationsDataSource.findById(scenarioId) ?: return null
            // Set the recalculation state to "STOPPED"
            val updated = status.revents.stop(exception)

            finishAutomaticRecalculation(updated, updated.errors)
            reventsRecalculationsDataSource.updateStatus(
                id = scenarioId,
                revents = updated
            )

            return null
        }
    }

    /**
     * Stop a recalculation status by settings its phase and adding the exception that caused the recalculation to stop.
     */
    private fun ReventsRecalculationStatus.stop(exception: HttpClientErrorException) = this.copy(
        phase = STOPPED,
        errors = listOf(Error(type = STOPPED_EXCEPTION, version = 2, message = exception.message))
    )

    /**
     * Checks the progress for the first currently running scenario.
     * If it's finished, data for merging back into VesselVoyage is requested, and the merge itself is performed.
     *
     * @return whether calling this method again would progress the scenario further.
     */
    @Scheduled(fixedDelay = 1, timeUnit = TimeUnit.MINUTES)
    fun checkScenarioProgress(): Boolean {
        val startTime = ZonedDateTime.now()
        try {
            val allScenarios = runningScenarios.toList()

            // TODO: for now this is sequentially requesting scenarios, instead implement
            //  a way for (r)events to push a "ready"-event to a stream
            for (scenarioId in allScenarios) {
                if (scenarioId.isMergeScenario()) {
                    handleMergeScenario(scenarioId)
                    continue
                }

                val response = try {
                    log.info { "Trying to get revents scenario with id $scenarioId" }
                    getScenario(scenarioId)
                } catch (e: Throwable) {
                    log.error(e) { "Something went wrong while getting the revents scenario" }
                    continue
                }

                if (response == null) {
                    runningScenarios.remove(scenarioId)
                    continue
                }

                val reventsRecalculationStatus = reventsConversionService.convertToStatus(response)

                // If for some reason we don't have the merging guarantee, abort, we can't trust the data then.
                val canMergeV1 = VESSEL_VOYAGE_MERGING in response.guarantees
                val canMergeV2 = VESSEL_VOYAGE_MERGING_V2 in response.guarantees

                if (!canMergeV1 && !canMergeV2) {
                    runningScenarios.remove(scenarioId)
                    reventsRecalculationsDataSource.updateStatus(
                        id = scenarioId,
                        revents = reventsRecalculationStatus.copy(
                            errors = listOf(Error(type = MERGING_NOT_ALLOWED))
                        )
                    )
                    continue
                }

                // Wait for the scenario to go in a final phase, otherwise it's in a 'moving' phase like PROGRESSING,
                // and we have to wait for the data to become available.
                if (!response.phase.final) {
                    reventsRecalculationsDataSource.updateStatus(scenarioId, reventsRecalculationStatus)
                    continue
                }

                // The scenario is in a final phase, but if it's not FINISHED it probably crashed, we can't trust the data.
                if (response.phase != ScenarioResponse.Phase.FINISHED) {
                    runningScenarios.remove(scenarioId)
                    reventsRecalculationsDataSource.updateStatus(scenarioId, reventsRecalculationStatus)
                    // Crashed scenario doesn't contain any errors
                    finishAutomaticRecalculation(reventsRecalculationStatus)
                    continue
                }

                log.info { "Trying revents merge" }

                val errors = mutableListOf<Error>()
                try {
                    log.info { "Merging the following: V1: $canMergeV1 V2: $canMergeV2" }
                    if (canMergeV1) {
                        fetchAndMergeDataV1(response.id, errors)
                    }
                    if (canMergeV2) {
                        log.info { "Starting revents V2 merge" }
                        fetchAndMergeDataV2(response, errors)
                    }
                } catch (e: Exception) {
                    log.error(e) { "Error has occurred while merging" }
                    errors.add(Error(type = STOPPED_EXCEPTION, message = e.message))
                }
                val updatedReventsStatus = reventsRecalculationStatus.copy(
                    phase = when {
                        errors.any { it.level == ERROR } -> STOPPED
                        errors.any { it.level == WARNING } -> PARTIALLY_FAILED
                        else -> reventsRecalculationStatus.phase
                    },
                    errors = errors
                )

                finishAutomaticRecalculation(updatedReventsStatus, updatedReventsStatus.errors)
                reventsRecalculationsDataSource.updateStatus(
                    id = scenarioId,
                    revents = updatedReventsStatus
                )
                runningScenarios.remove(scenarioId)
                continue
            }
            return true
        } catch (e: Exception) {
            val failTime = ZonedDateTime.now()
            log.error(e) { "Error checking scenario progress: ${e.message}" }
            slackMessageService?.sendMessage(
                title = "Scheduled task failed: ReventsRecalculationService.checkScenarioProgress",
                text = "Error checking scenario progress: ${e.message}\nStarted at: $startTime\nFailed at: $failTime",
                barColor = SLACK_COLOR_RED
            )
            throw e
        }
    }

    /**
     * Handles processing of merge scenarios.
     */
    private fun handleMergeScenario(scenarioId: String) {
        val result = reventsRecalculationsDataSource.findById(scenarioId) as? RecalculationMergeResult

        if (result == null) {
            log.error { "Recalculation result with $scenarioId is not of type RecalculationMergeResult, removing scenario" }
        } else {
            runMergeScenario(scenario = result)
        }

        runningScenarios.remove(scenarioId)
    }

    /**
     * Fetches all IMOs eligible for merging, fetching all mergeable data for those IMOs, and
     * then performing the merge of that data.
     *
     * [errors] is a [MutableList] to be dynamically appended to, as well as
     * allowing exceptions to be passed to the caller without losing the errors.
     *
     * @return whether the merge was performed.
     */
    private fun fetchAndMergeDataV1(
        scenarioId: String,
        errors: MutableList<Error>
    ): Boolean {
        val interestImos: Array<String>
        try {
            interestImos = reventsMergingClient.getInterestsForMerging(scenarioId)
        } catch (e: Throwable) {
            log.error(e) { "Could not fetch interests for merging" }
            errors.add(Error(type = REQUEST_FOR_INTERESTS_FAILED, version = 1, message = e.message))
            // Unrecoverable error, report that back.
            return false
        }

        log.info { "Fetched ${interestImos.size} IMOs eligible for merging" }

        interestImos.forEach { imo ->
            var mergeEntries = emptyArray<MergeEntry>()
            try {
                mergeEntries = reventsMergingClient.fetchEntriesForMerging(scenarioId, imo)
                log.info { "Fetched ${mergeEntries.size} merge entries for imo $imo" }
            } catch (e: Throwable) {
                log.error(e) { "Could not fetch entries for merging" }
                errors.add(Error(type = REQUEST_FOR_ENTRIES_FAILED, version = 1, imo = imo, message = e.message))
            }

            mergeEntries.forEach { mergeEntry ->
                try {
                    val mergeResult = entriesMergeV1Service.merge(imo, mergeEntry.window, mergeEntry.entries)
                    persistMergeResultV1(imo, mergeResult)
                } catch (e: IllegalArgumentException) {
                    // Catching illegal arguments, due to the use of the require() calls.
                    // Other errors should be passed to our caller, since they will not be recoverable from here.
                    errors.add(Error(type = ILLEGAL_MERGE, version = 1, imo = imo, message = e.message))
                }
            }
        }

        // Merge was successfully performed.
        return true
    }

    /**
     * Persists the result of the merge, by upserting the entries and deleting others.
     */
    private fun persistMergeResultV1(
        imo: String,
        mergeResult: EntriesMergeService.MergeResult<Entry>
    ) {
        // Safeguard us from not deleting entries after upserting. This would mean there is a bug in the merging logic.
        val newEntryIds = mergeResult.entries.map { it._id }.toSet()
        require(mergeResult.deleteVisitIds.none { it in newEntryIds }) { "Visit is marked for both upsert and delete." }
        require(mergeResult.deleteVoyageIds.none { it in newEntryIds }) { "Voyage is marked for both upsert and delete." }

        // Create or update entries.
        mergeResult.entries.forEach { entry ->
            when (entry) {
                is Visit -> visitDataSource.createOrReplace(entry)
                is Voyage -> voyageDataSource.createOrReplace(entry)
            }
        }

        // Delete old entries.
        mergeResult.deleteVisitIds.forEach { visitId -> visitDataSource.deleteById(visitId) }
        mergeResult.deleteVoyageIds.forEach { voyageId -> voyageDataSource.deleteById(voyageId) }

        // Delete old traces, to let them be regenerated.
        val updatedEntryIds = mergeResult.deleteVisitIds + mergeResult.deleteVoyageIds + newEntryIds
        v1TraceService.deleteHistoricTraces(updatedEntryIds, imo)

        // Remove ship status, to let it be refreshed automatically.
        shipStatusService.remove(imo)
    }

    /**
     * Fetches all IMOs eligible for merging, fetching all mergeable data for those IMOs, and
     * then performing the merge of that data.
     *
     * [errors] is a [MutableList] to be dynamically appended to, as well as
     * allowing exceptions to be passed to the caller without losing the errors.
     *
     * @return whether the merge was performed.
     */
    private fun fetchAndMergeDataV2(
        scenario: ScenarioResponse,
        errors: MutableList<Error>
    ): Boolean {
        val interestImos = try {
            reventsMergingV2Client.getInterestsForMerging(scenario.id)
        } catch (e: Throwable) {
            errors.add(Error(type = REQUEST_FOR_INTERESTS_FAILED, version = 2, message = e.message))
            log.error(e) { "Could not fetch interests for merging" }
            // Unrecoverable error, report that back.
            return false
        }

        val retryImos = mutableSetOf<Int>()

        log.info { "Fetched ${interestImos.size} IMOs eligible for merging" }

        interestImos.forEach { imo ->
            processImoForMergingV2(imo, scenario.id, errors)?.let { possibleRetryImo ->
                retryImos.add(possibleRetryImo)
            }
        }

        if (retryImos.isNotEmpty() && properties.reventsRetryEnabled) {
            // Do not retry a scenario that is already a retry-attempt to prevent infinite loops
            if (scenario.metaData?.isRetryAttempt == false) {
                retryForImos(retryImos)
            }
        }

        // Merge was successfully performed.
        return true
    }

    /**
     * Process a single IMO for merging with V2 data.
     * Returns the IMO if it needs to be retried, null otherwise.
     */
    private fun processImoForMergingV2(
        imo: Int,
        scenarioId: String,
        errors: MutableList<Error>
    ): Int? {
        val mergeEntries: Array<MergeEntryV2>

        try {
            log.info { "Fetching entries to be merged (imo = $imo, id = $scenarioId)" }
            mergeEntries = reventsMergingV2Client.fetchEntriesForMerging(scenarioId, imo)
            log.info { "Fetched ${mergeEntries.size} merge entries (imo = $imo, id = $scenarioId)" }
        } catch (e: Throwable) {
            log.error(e) { "Could not fetch entries for merging" }
            errors.add(Error(type = REQUEST_FOR_ENTRIES_FAILED, version = 2, imo = imo.toString(), message = e.message, level = WARNING))
            return null
        }

        var needsRetry = false

        imoLockService.executeBlocking(imo) {
            mergeEntries.forEach { mergeEntry ->
                try {
                    val mergeResult = entriesMergeV2Service.merge(imo.toString(), mergeEntry.window, mergeEntry.entries)
                    persistMergeResultV2(imo, mergeResult)
                } catch (e: IllegalArgumentException) {
                    errors.add(Error(type = ILLEGAL_MERGE, version = 2, imo = imo.toString(), message = e.message, level = WARNING))
                } catch (e: NoEntriesInWindowException) {
                    errors.add(Error(type = INVALID_WINDOW, version = 2, imo = imo.toString(), message = e.message, level = WARNING))
                    needsRetry = true
                }
            }
        }

        return if (needsRetry) imo else null
    }

    /**
     * Merges V2 data for a single IMO from a specific scenario.
     *
     * This method processes the merging of V2 data for a given IMO and scenario ID.
     * It delegates the merging logic to `processImoForMergingV2` and updates the provided
     * `errors` list with any issues encountered during the process.
     *
     * @param imo The IMO number of the vessel to process.
     * @param scenarioId The ID of the scenario associated with the merging process.
     * @param errors A mutable list to collect errors encountered during the merging process.
     */
    private fun mergeV2Data(imo: Int, scenarioId: String, errors: MutableList<Error>) {
        processImoForMergingV2(imo, scenarioId, errors)
    }

    private fun runMergeScenario(scenario: RecalculationMergeResult) {
        val ships = scenario.ships

        val errors = scenario.revents.errors.toMutableList()

        ships.forEach { ship ->
            mergeV2Data(ship.imo, ship.scenarioId, errors)
        }

        val updatedReventsStatus = scenario.revents.copy(
            phase = when {
                errors.any { it.level == ERROR } -> STOPPED
                errors.any { it.level == WARNING } -> PARTIALLY_FAILED
                else -> FINISHED
            },
            errors = errors
        )

        reventsRecalculationsDataSource.updateStatus(
            id = scenario._id,
            revents = updatedReventsStatus
        )
    }

    /**
     * Creates a scenario for given [imos] recalculating their full history to solve merging errors.
     */
    private fun retryForImos(imos: Set<Int>) {
        recalculateByShips(
            imos = imos,
            start = Constants.recalculationStartDate,
            end = Instant.now(),
            guarantee = ReventsRecalculationStatus.Guarantee.V2,
            username = "Automated Retry",
            isRetryAttempt = true
        )
    }

    /**
     * Persists the result of the merge, by upserting the entries and deleting others.
     */
    private fun persistMergeResultV2(
        imo: Int,
        mergeResult: EntriesMergeService.MergeResult<EntryESoFWrapper<*>>
    ) {
        // Safeguard us from not deleting entries after upserting. This would mean there is a bug in the merging logic.
        val sortedNewEntryIds = mergeResult.entries.map { it.entry._id }
        val newEntryIds = sortedNewEntryIds.toSet()
        require(mergeResult.deleteVisitIds.none { it in newEntryIds }) { "Visit is marked for both upsert and delete." }
        require(mergeResult.deleteVoyageIds.none { it in newEntryIds }) { "Voyage is marked for both upsert and delete." }

        // Create or update entries.
        mergeResult.entries.forEach { wrapper ->
            when (val entry = wrapper.entry) {
                is NewVisit -> newVisitDataSource.createOrReplace(entry)
                is NewVoyage -> newVoyageDataSource.createOrReplace(entry)
            }

            val esof = wrapper.esof

            if (esof != null) {
                newESoFDataSource.createOrReplace(esof)
            } else {
                // Replaced entry doesn't contain a esof, so we can delete the old one
                newESoFDataSource.deleteById(wrapper.entry._id)
            }
        }

        // Delete old entries.
        mergeResult.deleteVisitIds.forEach { visitId -> newVisitDataSource.deleteById(visitId) }
        mergeResult.deleteVoyageIds.forEach { voyageId -> newVoyageDataSource.deleteById(voyageId) }

        val deleteESoFIds = mergeResult.deleteVisitIds + mergeResult.deleteVoyageIds

        // Also delete all esofs related to our just deleted visits and voyages
        deleteESoFIds.forEach { entryId -> newESoFDataSource.deleteById(entryId) }

        // Delete old traces, to let them be regenerated.
        val updatedEntryIds = mergeResult.deleteVisitIds + mergeResult.deleteVoyageIds + newEntryIds
        if (updatedEntryIds.isNotEmpty()) {
            processingTraceService.deleteHistoricTraces(updatedEntryIds, imo)
        }

        // Schedule for the trace to be calculated again for our new entries
        processingTraceService.scheduleTraceCalculation(entryIds = sortedNewEntryIds, force = true)

        // Schedule for the new entries to be post-processed
        schedulePostProcessing(sortedNewEntryIds)

        // Remove ship status, to let it be refreshed automatically.
        shipStatusService.removeStatus(imo)
    }

    private val finishedRecalculationStatus = setOf(ReventsRecalculationStatus.Phase.FINISHED, STOPPED, PARTIALLY_FAILED)

    private fun finishAutomaticRecalculation(status: ReventsRecalculationStatus, crashedErrors: List<Error> = emptyList()) {
        val scenario = reventsRecalculationsDataSource.findById(status.scenarioId) ?: return
        return finishAutomaticRecalculation(scenario, status, crashedErrors)
    }

    fun finishAutomaticRecalculation(scenario: RecalculationResult, status: ReventsRecalculationStatus, crashedErrors: List<Error>) {
        // Manually created scenarios should be ignored
        if (!scenario.automated) {
            return
        }

        // The scenario is finished, so we have to update our automatic recalculation status
        if (scenario is RecalculationShipsResult && status.phase in finishedRecalculationStatus) {
            val didGeneralErrorHappen = crashedErrors.any { crashedError -> crashedError.imo == null }
            scenario.imos.forEach { imo ->
                val phaseStopped = status.phase == STOPPED
                val didImoCrash = didGeneralErrorHappen || crashedErrors.any {
                    crashedError ->
                    crashedError.imo?.toIntOrNull() == imo
                }
                val finishedState = if (didImoCrash || phaseStopped) {
                    RecalculationState.ERROR
                } else {
                    RecalculationState.FINISHED
                }

                automaticRecalculationShipDataSource.updateState(imo, finishedState)
            }
        }
    }

    private fun schedulePostProcessing(entries: List<EntryId>) =
        entries.forEach { entry ->
            postProcessingService?.schedulePostProcessing(entry)
        }

    fun mergeShipRecalculation(
        imo: Int,
        scenario: String?,
        username: String
    ): Boolean {
        val errors = mutableListOf<Error>()

        val scenarioId = if (scenario != null) {
            // Check if scenario contains the right IMO
            val recalculationResult = reventsRecalculationsDataSource.findById(scenario)
            recalculationResult?.containsImo(imo) ?: throw BadRequestException("Scenario not found for scenarioId $scenario")

            recalculationResult._id
        } else {
            reventsRecalculationsDataSource.firstScenarioIdByImo(imo)
                ?: throw BadRequestException("Scenario not found for IMO $imo")
        }

        val scenarioShip = listOf(
            RecalculationMergeResult.ScenarioShip(
                imo = imo,
                scenarioId = scenarioId
            )
        )

        val mergeResult = createMergeResult(scenarioShip, username, errors)

        reventsRecalculationsDataSource.save(mergeResult)
        runningScenarios.add(mergeResult._id)

        return true
    }

    fun mergeShipRecalculation(
        imos: List<Int>,
        scenario: String?,
        username: String
    ): Boolean {
        val errors = mutableListOf<Error>()
        val interestShips = if (scenario == null) {
            val scenarioMap = reventsRecalculationsDataSource.findMostRecentResultsByImos(imos)
            scenarioMap.map {
                RecalculationMergeResult.ScenarioShip(
                    imo = it.key,
                    scenarioId = it.value._id
                )
            }
        } else {
            // Check if scenario contains the right IMO
            imos.forEach { imo ->
                val recalculationResult = reventsRecalculationsDataSource.findById(scenario)
                recalculationResult?.containsImo(imo) ?: throw BadRequestException("Scenario not found for scenarioId $scenario")

                recalculationResult._id
            }

            imos.map {
                RecalculationMergeResult.ScenarioShip(
                    imo = it,
                    scenarioId = scenario
                )
            }
        }

        if (interestShips.isEmpty()) {
            throw BadRequestException("No interests found for scenario $scenario")
        }

        val mergeResult = createMergeResult(interestShips, username, errors)

        reventsRecalculationsDataSource.save(mergeResult)
        runningScenarios.add(mergeResult._id)

        return true
    }

    /**
     * Checks if the given [RecalculationResult] contains the specified [imo].
     * Throws a [BadRequestException] if the IMO is not found in the scenario.
     */
    private fun RecalculationResult.containsImo(imo: Int) {
        when (this) {
            is RecalculationShipsResult -> {
                if (imo !in this.imos) { throw BadRequestException("IMO $imo not found in scenario ${this._id}") }
            }
            is RecalculationShipResult -> {
                if (imo != this.imo) { throw BadRequestException("IMO $imo not found in scenario ${this._id}") }
            }
            else -> throw BadRequestException("Invalid scenario type for merging: ${this._id}")
        }
    }

    fun mergeShipRecalculation(
        scenario: String,
        username: String
    ): Boolean {
        val errors = mutableListOf<Error>()
        val interestImos = try {
            reventsMergingV2Client.getInterestsForMerging(scenario)
        } catch (e: Throwable) {
            errors.add(Error(type = REQUEST_FOR_INTERESTS_FAILED, version = 2, message = e.message))
            log.error(e) { "Could not fetch interests for merging" }
            // Unrecoverable error, report that back.
            return false
        }

        val scenarioShips = interestImos.map {
            RecalculationMergeResult.ScenarioShip(
                imo = it,
                scenarioId = scenario
            )
        }

        if (interestImos.isEmpty()) {
            throw BadRequestException("No interests found for scenario $scenario")
        }

        val mergeResult = createMergeResult(scenarioShips, username, errors)

        reventsRecalculationsDataSource.save(mergeResult)
        runningScenarios.add(mergeResult._id)

        return true
    }

    private fun createMergeResult(
        scenarioShips: List<RecalculationMergeResult.ScenarioShip>,
        username: String,
        errors: MutableList<Error> = mutableListOf()
    ): RecalculationMergeResult {
        val id = createMergeId()

        return RecalculationMergeResult(
            id = id,
            revents = createMergeStatus(id, username, errors),
            username = username,
            imoScenarioMap = scenarioShips
        )
    }

    private fun createMergeStatus(scenarioId: String, username: String, errors: MutableList<Error>) =
        ReventsRecalculationStatus(
            queuedAt = ZonedDateTime.now(),
            scenarioId = scenarioId,
            phase = PROGRESSING,
            guarantees = emptySet(),
            username = username,
            from = null,
            to = null,
            errors = errors
        )

    private fun String.isMergeScenario(): Boolean {
        return this.endsWith(MERGE_SUFFIX)
    }

    private fun createMergeId() = "${UUID.randomUUID()}$MERGE_SUFFIX"
}
