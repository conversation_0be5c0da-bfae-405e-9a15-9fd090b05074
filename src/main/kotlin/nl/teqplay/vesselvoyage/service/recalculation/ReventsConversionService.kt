package nl.teqplay.vesselvoyage.service.recalculation

import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.InterestArea
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.InterestShip
import nl.teqplay.aisengine.reventsengine.model.scenario.Scenario
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioCreateRequest
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioGuarantee
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioGuarantee.VESSEL_VOYAGE_MERGING
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioGuarantee.VESSEL_VOYAGE_MERGING_V2
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioResponse
import nl.teqplay.skeleton.model.TimeWindow
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.model.ReventsRecalculationStatus
import nl.teqplay.vesselvoyage.model.ReventsRecalculationStatus.Guarantee
import org.springframework.stereotype.Service
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit

/**
 * Service that converts between requests from the "VesselVoyage-perspective" to (r)events definitions.
 */
@ProfileProcessing
@Service
class ReventsConversionService {

    companion object {
        const val VESSEL_VOYAGE = "VESSEL_VOYAGE"
    }

    /**
     * Converts a recalculation request for a specific ship by [imo] between
     * [window] times to a [ScenarioCreateRequest].
     */
    fun createScenarioRequestForShip(
        imo: Int,
        window: TimeWindow,
        guarantee: Guarantee,
        username: String,
        includeHistoricData: Boolean = false
    ): ScenarioCreateRequest = ScenarioCreateRequest(
        window = window,
        guarantees = setOf(guarantee.fromInternal()),
        interests = listOf(
            InterestShip(ship = InterestShip.Identifier(imo = imo))
        ),
        settings = Scenario.Settings(
            filterHistory = if (includeHistoricData) {
                Scenario.FilterHistory.USE_ALL_DATA
            } else {
                Scenario.FilterHistory.ONLY_REAL_TIME
            }
        ),
        metaData = Scenario.MetaData(
            createdBy = username,
            requestFromSystem = VESSEL_VOYAGE
        )
    )

    fun createScenarioRequestForShips(
        imos: Set<Int>,
        window: TimeWindow,
        guarantee: Guarantee,
        username: String,
        isRetryAttempt: Boolean,
        includeHistoricData: Boolean = false
    ): ScenarioCreateRequest = ScenarioCreateRequest(
        window = window,
        guarantees = setOf(guarantee.fromInternal()),
        interests = imos.map { imo ->
            InterestShip(ship = InterestShip.Identifier(imo = imo))
        },
        settings = Scenario.Settings(
            filterHistory = if (includeHistoricData) {
                Scenario.FilterHistory.USE_ALL_DATA
            } else {
                Scenario.FilterHistory.ONLY_REAL_TIME
            }
        ),
        metaData = Scenario.MetaData(
            createdBy = username,
            requestFromSystem = VESSEL_VOYAGE,
            isRetryAttempt = isRetryAttempt
        )
    )

    /**
     * Converts a recalculation request for a specific port by [pomaPortId] between
     * [window] times to a [ScenarioCreateRequest].
     */
    fun createScenarioRequestForPort(
        pomaPortId: String,
        window: TimeWindow,
        guarantee: Guarantee,
        username: String,
        includeHistoricData: Boolean = false,
    ): ScenarioCreateRequest = ScenarioCreateRequest(
        window = TimeWindow(
            from = window.from.minus(30, ChronoUnit.DAYS),
            to = window.to.plus(30, ChronoUnit.DAYS)
        ),
        guarantees = setOf(guarantee.fromInternal()),
        interests = listOf(
            InterestArea(
                area = InterestArea.Area(
                    type = AreaIdentifier.AreaType.PORT,
                    id = pomaPortId
                )
            )
        ),
        settings = Scenario.Settings(
            filterHistory = if (includeHistoricData) {
                Scenario.FilterHistory.USE_ALL_DATA
            } else {
                Scenario.FilterHistory.ONLY_REAL_TIME
            }
        ),
        metaData = Scenario.MetaData(
            createdBy = username,
            requestFromSystem = VESSEL_VOYAGE
        )
    )

    /**
     * Converts a (r)events [response] to our representation as [ReventsRecalculationStatus].
     */
    fun convertToStatus(
        response: ScenarioResponse
    ): ReventsRecalculationStatus {
        val phase = when (response.phase) {
            ScenarioResponse.Phase.QUEUED -> ReventsRecalculationStatus.Phase.QUEUED

            ScenarioResponse.Phase.INITIALIZING,
            ScenarioResponse.Phase.PROGRESSING,
            ScenarioResponse.Phase.POST_PROCESSING -> ReventsRecalculationStatus.Phase.PROGRESSING

            ScenarioResponse.Phase.FINISHED -> ReventsRecalculationStatus.Phase.FINISHED

            ScenarioResponse.Phase.CRASHED,
            ScenarioResponse.Phase.PRUNED,
            ScenarioResponse.Phase.CANCELLED -> ReventsRecalculationStatus.Phase.STOPPED
        }
        return ReventsRecalculationStatus(
            queuedAt = response.queued.atZone(ZoneOffset.UTC),
            scenarioId = response.id,
            phase = phase,
            guarantees = response.guarantees.mapNotNull { it.toInternal() }.toSet(),
            username = response.metaData?.createdBy,
            from = response.window.from.atZone(ZoneOffset.UTC),
            to = response.window.to.atZone(ZoneOffset.UTC)
        )
    }

    private fun ScenarioGuarantee.toInternal(): Guarantee? = when (this) {
        VESSEL_VOYAGE_MERGING -> Guarantee.V1
        VESSEL_VOYAGE_MERGING_V2 -> Guarantee.V2
        else -> null
    }

    private fun Guarantee.fromInternal(): ScenarioGuarantee = when (this) {
        Guarantee.V1 -> VESSEL_VOYAGE_MERGING
        Guarantee.V2 -> VESSEL_VOYAGE_MERGING_V2
    }
}
