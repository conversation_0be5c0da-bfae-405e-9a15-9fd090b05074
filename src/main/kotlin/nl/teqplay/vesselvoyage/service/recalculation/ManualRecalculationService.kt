package nl.teqplay.vesselvoyage.service.recalculation

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.datasource.NewESoFDataSource
import nl.teqplay.vesselvoyage.datasource.NewTraceDataSource
import nl.teqplay.vesselvoyage.datasource.NewVisitDataSource
import nl.teqplay.vesselvoyage.datasource.NewVoyageDataSource
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.v2.ESoFChange
import nl.teqplay.vesselvoyage.model.v2.EntryESoFWrapper
import nl.teqplay.vesselvoyage.model.v2.EntryId
import nl.teqplay.vesselvoyage.model.v2.NewChange
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import nl.teqplay.vesselvoyage.model.v2.VisitChange
import nl.teqplay.vesselvoyage.model.v2.VoyageChange
import nl.teqplay.vesselvoyage.service.EntryProcessingService
import nl.teqplay.vesselvoyage.service.EventFetchingService
import nl.teqplay.vesselvoyage.service.ImoLockService
import nl.teqplay.vesselvoyage.service.PersistChangesService
import nl.teqplay.vesselvoyage.service.ProcessingShipStatusService
import nl.teqplay.vesselvoyage.service.publisher.ChangesPublisherService
import nl.teqplay.vesselvoyage.util.Constants
import org.springframework.stereotype.Service
import java.time.Instant
import kotlin.system.measureTimeMillis

@ProfileProcessing
@Service
class ManualRecalculationService(
    private val lockService: ImoLockService,
    private val shipStatusService: ProcessingShipStatusService,
    private val visitDataSource: NewVisitDataSource,
    private val voyageDataSource: NewVoyageDataSource,
    private val esofDataSource: NewESoFDataSource,
    private val traceDataSource: NewTraceDataSource,
    private val changesPublisherService: ChangesPublisherService?,
    private val eventFetchingService: EventFetchingService,
    private val entryProcessingService: EntryProcessingService,
    private val persistChangesService: PersistChangesService
) {
    private val log = KotlinLogging.logger {}

    /**
     * Fully re-create the ship story. This is done using event history, so we can start "fresh".
     *
     * Potential use case for this is when the ship is fully stuck in some way and recalculating using revents isn't possible.
     */
    fun recreateFullShipStory(imo: Int) {
        log.info { "Start recreating full ship story with IMO $imo, locking ship" }

        val recalculationTime = measureTimeMillis {
            // Lock the IMO we want to recreate the story for, so we are sure no other processes are mutating this ship
            lockService.executeBlocking(imo) {
                val currentStatus = shipStatusService.getStatus(imo = imo)

                val events = eventFetchingService.fetchAisEngineEventsByIMO(
                    imo = imo,
                    start = Constants.recalculationStartDate,
                    end = Instant.now()
                )

                log.info { "Found ${events.count()} events for the full ship story (imo = $imo)" }
                val newEntries = entryProcessingService.processAisEngineEventsDryRunForStoryV2(aisEngineEvents = events)
                val newChanges = createChangesFromDryRunResult(newEntries = newEntries)

                // Only now try to delete the existing data
                // Otherwise if something goes wrong we end up deleting everything without generating anything back
                if (currentStatus !is NewInitialShipStatus) {
                    log.info { "We have data for the ship with IMO $imo, deleting existing visits and voyages" }

                    val (changesToPublish, currentEntryIds) = getChangesForDeleting(imo = imo)

                    log.info { "Publishing all ${changesToPublish.size} changes that are marked for deletion (imo = $imo)" }
                    changesPublisherService?.tryPublishNewChanges(
                        oldStatus = currentStatus,
                        updatedStatus = NewInitialShipStatus(),
                        changes = changesToPublish
                    )

                    log.info { "Deleting all current visits and voyages and resetting ship status (imo = $imo)" }
                    visitDataSource.deleteAllByImo(imo = imo)
                    voyageDataSource.deleteAllByImo(imo = imo)
                    esofDataSource.deleteAllByIds(entryIds = currentEntryIds)
                    traceDataSource.deleteByEntryIds(entryIds = currentEntryIds)
                }
                // Always delete the status from our state, so we can ensure the new status will be retrieved
                shipStatusService.removeStatus(imo = imo)

                log.info { "Done recreating story, persisting results of ${newChanges.size} changes (imo = $imo)" }
                persistChangesService.persistChanges(newChanges)

                // Re-fetch the status, as we just persisted the visits and voyages it will result in us recreating the state
                val newStatusAfterStoryRecreate = shipStatusService.getStatus(imo = imo)
                changesPublisherService?.tryPublishNewChanges(
                    oldStatus = NewInitialShipStatus(),
                    updatedStatus = newStatusAfterStoryRecreate,
                    changes = newChanges
                )
            }
        }

        log.info { "Finished recreating ship story with IMO $imo using event history ($recalculationTime ms)" }
    }

    private fun createChangesFromDryRunResult(
        newEntries: List<EntryESoFWrapper<*>>
    ): List<NewChange<*>> {
        val allChanges = mutableListOf<NewChange<*>>()

        newEntries.forEach { (entry, esof) ->
            when (entry) {
                is NewVisit -> {
                    val visitChange = VisitChange(
                        action = Action.CREATE,
                        value = entry
                    )
                    allChanges.add(visitChange)
                }
                is NewVoyage -> {
                    val voyageChange = VoyageChange(
                        action = Action.CREATE,
                        value = entry
                    )
                    allChanges.add(voyageChange)
                }
            }

            if (esof != null) {
                val esofChange = ESoFChange(
                    action = Action.CREATE,
                    value = esof
                )
                allChanges.add(esofChange)
            }
        }

        return allChanges
    }

    private fun getChangesForDeleting(
        imo: Int
    ): Pair<List<NewChange<*>>, List<EntryId>> {
        val currentVisits = visitDataSource.findAllByImo(imo)
        val currentVoyages = voyageDataSource.findAllByImo(imo)

        val currentEntryIds = currentVisits.map { it._id } + currentVoyages.map { it._id }
        val currentEsofs = esofDataSource.findByIds(currentEntryIds)

        return listOf(
            currentVisits.map { visit -> VisitChange(action = Action.DELETE, value = visit) },
            currentVoyages.map { voyage -> VoyageChange(action = Action.DELETE, value = voyage) },
            currentEsofs.map { esof -> ESoFChange(action = Action.DELETE, value = esof) },
        ).flatten() to currentEntryIds
    }
}
