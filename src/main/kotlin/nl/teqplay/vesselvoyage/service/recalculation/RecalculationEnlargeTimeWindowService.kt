package nl.teqplay.vesselvoyage.service.recalculation

import nl.teqplay.skeleton.model.TimeWindow
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.datasource.NewVoyageDataSource
import nl.teqplay.vesselvoyage.model.Voyage
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import nl.teqplay.vesselvoyage.service.VoyageService
import org.springframework.stereotype.Service
import java.time.Instant
import java.time.ZoneOffset

@ProfileProcessing
@Service
class RecalculationEnlargeTimeWindowService(
    private val voyageService: VoyageService,
    private val newVoyageDataSource: NewVoyageDataSource
) {

    /**
     * Enlarges the [TimeWindow] specified by [start] and [end] to align with
     * the [Voyage.startTime] and [Voyage.endTime] near the [start] and [end].
     */
    fun enlargeTimeWindowBasedOnVoyagesV1(
        imo: Int,
        start: Instant,
        end: Instant
    ): TimeWindow {
        val zonedStart = start.atZone(ZoneOffset.UTC)
        val zonedEnd = end.atZone(ZoneOffset.UTC)

        val voyageAtStart = voyageService.findNewestStartedBeforeByImo(imo.toString(), zonedStart)
        val voyageAtEnd = voyageService.findOldestEndedAfterByImo(imo.toString(), zonedStart, zonedEnd)
        return TimeWindow(
            from = min(start, voyageAtStart?.startTime?.toInstant()),
            to = max(end, voyageAtEnd?.endTime?.toInstant())
        )
    }

    /**
     * Enlarges the [TimeWindow] specified by [start] and [end] to align with
     * the [NewVoyage.start] and [NewVoyage.end] near the [start] and [end].
     */
    fun enlargeTimeWindowBasedOnVoyagesV2(
        imo: Int,
        start: Instant,
        end: Instant
    ): TimeWindow {
        val voyageAtStart = newVoyageDataSource.findNewestStartedBeforeByImo(imo, start)
        val voyageAtEnd = newVoyageDataSource.findOldestEndedAfterByImo(imo, start, end)
        return TimeWindow(
            from = min(start, voyageAtStart?.start?.time),
            to = max(end, voyageAtEnd?.end?.time)
        )
    }

    private fun min(d1: Instant, d2: Instant?): Instant {
        if (d2 == null) return d1
        return minOf(d1, d2)
    }

    private fun max(d1: Instant, d2: Instant?): Instant {
        if (d2 == null) return d1
        return maxOf(d1, d2)
    }
}
