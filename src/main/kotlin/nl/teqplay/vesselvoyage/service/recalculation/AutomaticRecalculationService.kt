package nl.teqplay.vesselvoyage.service.recalculation

import io.github.oshai.kotlinlogging.KotlinLogging
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.skeleton.metrics.Metric
import nl.teqplay.skeleton.metrics.MetricRegistry
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.datasource.AutomaticRecalculationShipDataSource
import nl.teqplay.vesselvoyage.datasource.NewVoyageDataSource
import nl.teqplay.vesselvoyage.datasource.ReventsRecalculationsDataSource
import nl.teqplay.vesselvoyage.model.internal.AutomaticRecalculationShip
import nl.teqplay.vesselvoyage.model.internal.AutomaticRecalculationShip.RecalculationState
import nl.teqplay.vesselvoyage.model.internal.AutomaticRecalculationStatus
import nl.teqplay.vesselvoyage.properties.AutomaticRecalculationProperties
import nl.teqplay.vesselvoyage.service.SLACK_COLOR_RED
import nl.teqplay.vesselvoyage.service.SlackMessageService
import nl.teqplay.vesselvoyage.service.StaticShipInfoService
import nl.teqplay.vesselvoyage.util.Constants
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.time.Instant
import java.time.ZonedDateTime
import java.util.concurrent.atomic.AtomicLong
import kotlin.system.measureTimeMillis

@ProfileProcessing
@ConditionalOnProperty("automatic-recalculation.enabled", havingValue = "true")
@Service
class AutomaticRecalculationService(
    private val properties: AutomaticRecalculationProperties,
    private val dataSource: AutomaticRecalculationShipDataSource,
    private val voyageDataSource: NewVoyageDataSource,
    private val staticShipInfoService: StaticShipInfoService,
    private val reventsRecalculationService: ReventsRecalculationService,
    private val reventsRecalculationsDataSource: ReventsRecalculationsDataSource,
    private val manualRecalculationService: ManualRecalculationService,
    meterRegistry: MeterRegistry,
    private val slackMessageService: SlackMessageService?,
) {
    private val log = KotlinLogging.logger {}
    private val metricRegistry = MetricRegistry.of<AutomaticRecalculationService>(meterRegistry, listOf("state"))
    private var totalReadyShips: AtomicLong = metricRegistry.createGauge(Metric.STATE_SIZE, AtomicLong(0), RecalculationState.READY.name)
    private var totalNotReadyShips: AtomicLong = metricRegistry.createGauge(Metric.STATE_SIZE, AtomicLong(0), RecalculationState.NOT_READY.name)
    private var totalOngoingRecalculatedShips: AtomicLong = metricRegistry.createGauge(Metric.STATE_SIZE, AtomicLong(0), RecalculationState.RUNNING.name)
    private var totalRecalculatedShips: AtomicLong = metricRegistry.createGauge(Metric.STATE_SIZE, AtomicLong(0), RecalculationState.FINISHED.name)
    private var totalErroredShips: AtomicLong = metricRegistry.createGauge(Metric.STATE_SIZE, AtomicLong(0), RecalculationState.ERROR.name)

    @Volatile
    private var running = false

    fun startup() {
        if (properties.enabled) {
            log.info { "Cleaning up left over automatic ship recalculations" }
            cleanUpOngoingRecalculationsIfPossible()

            log.info { "Starting automatic ship recalculation ($properties)" }
            running = true
        }
    }

    fun shutdown() {
        if (properties.enabled) {
            log.info { "Shutting down automatic ship recalculation" }
            running = false
        }
    }

    @Scheduled(cron = "\${automatic-recalculation.cron}")
    fun recalculateBatch() {
        val startTime = ZonedDateTime.now()
        try {
            // Only trigger recalculation when VesselVoyage is ready to do so
            if (!running) {
                return
            }
            log.debug { "Starting automatic ship recalculation scheduling cycle" }

            val shipsToRecalculate = dataSource.getAllByState(
                state = RecalculationState.READY,
                limit = properties.batchSize
            )

            if (shipsToRecalculate.isEmpty()) {
                log.info { "No ships are ready to be auto recalculated" }
                return
            }

            log.info { "Found ${shipsToRecalculate.size} ships that are eligible to be recalculated" }

            val allImos = shipsToRecalculate.map { ship -> ship.imo }.toSet()
            val end = Instant.now() - properties.readyOffset

            val result = reventsRecalculationService.recalculateShipForAutomaticRecalculation(
                allImos,
                Constants.recalculationStartDate,
                end
            )

            shipsToRecalculate.forEach { ship ->
                val scenarioId = result.revents.scenarioId
                val updatedShip = ship.copy(
                    scenarioId = scenarioId,
                    state = RecalculationState.RUNNING
                )
                dataSource.update(updatedShip)
            }
            log.info { "Finished scheduling ${shipsToRecalculate.size} ships to be regenerated using revents" }

            if (properties.eventHistoryPreRecalculationEnabled) {
                log.info { "Cleaning up story for the ${shipsToRecalculate.size} ships we are about to automatic recalculate" }
                val cleanUpDuration = measureTimeMillis {
                    allImos.forEach { imo ->
                        try {
                            manualRecalculationService.recreateFullShipStory(imo)
                        } catch (ex: Exception) {
                            log.error(ex) { "Could not clean up story for imo $imo" }
                        }
                    }
                }

                log.info { "Finished cleaning up all the ${shipsToRecalculate.size} ships ($cleanUpDuration ms)" }
            }
        } catch (e: Throwable) {
            val failTime = ZonedDateTime.now()
            log.error(e) { "Error persisting ongoing traces: ${e.message}" }
            slackMessageService?.sendMessage(
                title = "Scheduled task failed: persistOngoingTraces",
                text = "Error persisting ongoing traces: ${e.message}\nStarted at: $startTime\nFailed at: $failTime",
                barColor = SLACK_COLOR_RED
            )
        }
    }

    @Scheduled(fixedDelayString = "\${automatic-recalculation.ship-refresh-interval}")
    fun refreshTrackedShips() {
        // We are not running yet, ignore the refresh
        if (!running) {
            return
        }

        val startTime = ZonedDateTime.now()
        try {
            log.debug { "Refreshing tracked ships" }
            val allKnownShips = dataSource.getAll()
            val allKnownShipImos = allKnownShips.map { it.imo }

            val allImos = staticShipInfoService.getAllImos().mapNotNull { it.toIntOrNull() }
            val unknownShipImos = allImos.filterNot { imo -> imo in allKnownShipImos }

            val allShips = if (unknownShipImos.isNotEmpty()) {
                // Some IMOs seem to be new, so we have to track them
                val createdNewShips = unknownShipImos.map { newImo ->
                    val newShip = createEmptyShip(imo = newImo)
                    dataSource.insert(newShip)
                    newShip
                }

                refreshReadyState(allKnownShips) + createdNewShips
            } else {
                refreshReadyState(allKnownShips)
            }

            val groupedShips = allShips.groupBy { ship -> ship.state }

            totalReadyShips.updateTotalShipsGauge(groupedShips, RecalculationState.READY)
            totalNotReadyShips.updateTotalShipsGauge(groupedShips, RecalculationState.NOT_READY)
            totalOngoingRecalculatedShips.updateTotalShipsGauge(groupedShips, RecalculationState.RUNNING)
            totalRecalculatedShips.updateTotalShipsGauge(groupedShips, RecalculationState.FINISHED)
            totalErroredShips.updateTotalShipsGauge(groupedShips, RecalculationState.ERROR)
            log.debug { "Done refreshing tracked ships" }
        } catch (e: Throwable) {
            log.error(e) { "Could not refresh ships to be automatically recalculated" }
            val failTime = ZonedDateTime.now()
            slackMessageService?.sendMessage(
                title = "Scheduled task failed: AutomaticRecalculationService.refreshTrackedShips",
                text = "Error refreshing tracked ships: ${e.message}\nStarted at: $startTime\nFailed at: $failTime",
                barColor = SLACK_COLOR_RED
            )
        }
    }

    fun cleanUpOngoingRecalculationsIfPossible() {
        val shipsCurrentlyOngoing = dataSource.getAllByState(
            state = RecalculationState.RUNNING,
            limit = 0
        )

        val scenarios = shipsCurrentlyOngoing.groupBy { ship -> ship.scenarioId }
            .keys
            .filterNotNull()

        if (scenarios.isEmpty()) {
            log.info { "No scenarios to potentially clean up" }
            return
        }

        log.info { "Trying to clean up ${scenarios.size} scenarios containing ${shipsCurrentlyOngoing.size} ships" }
        scenarios.forEach { scenarioId ->
            val scenario = reventsRecalculationsDataSource.findById(scenarioId)

            // Try to finish any of our running scenarios
            if (scenario != null) {
                reventsRecalculationService.finishAutomaticRecalculation(scenario, scenario.revents, scenario.revents.errors)
            }
        }
        log.info { "Done cleaning up scenarios" }
    }

    fun getStatus(): AutomaticRecalculationStatus {
        val allShips = dataSource.getAll()
        val groupedByCategory = allShips.mapNotNull { ship ->
            val shipCategory = staticShipInfoService.getShipCategoryByIMO(ship.imo.toString()) ?: return@mapNotNull null
            shipCategory to ship
        }.groupBy({ (category, _) -> category }, { (_, state) -> state })

        return AutomaticRecalculationStatus(
            status = countShipsByState(allShips),
            byCategory = groupedByCategory.mapValues { (_, state) -> countShipsByState(state) }
        )
    }

    private fun countShipsByState(ships: List<AutomaticRecalculationShip>): Map<RecalculationState, Int> {
        return ships.groupBy { it.state }
            .mapValues { it.value.size }
    }

    private fun AtomicLong.updateTotalShipsGauge(
        groupedShips: Map<RecalculationState, List<AutomaticRecalculationShip>>,
        state: RecalculationState
    ) {
        val newAmount = groupedShips[state]?.size?.toLong() ?: 0L
        val currentAmount = this.get()
        log.debug { "Updating total amount of ship in state $state (currently = $currentAmount, new = $newAmount)" }
        this.set(newAmount)
    }

    /**
     * Refresh the ready state of all known ships.
     */
    private fun refreshReadyState(knownShips: List<AutomaticRecalculationShip>): List<AutomaticRecalculationShip> {
        return knownShips.map { ship ->
            // We only have to prepare ships that are not ready
            if (ship.state != RecalculationState.NOT_READY) {
                return@map ship
            }

            val newState = getShipReadyState(ship.imo)

            // Don't update the ship if the new state is still the same
            if (ship.state == newState) {
                return@map ship
            }

            // We can only do something with the ship if the ship is now ready to be recalculated
            if (newState == RecalculationState.READY) {
                val updatedShip = ship.copy(state = newState)
                dataSource.update(updatedShip)

                return@map updatedShip
            }

            // Ship is still not ready, we can't do anything with it
            ship
        }
    }

    private fun getShipReadyState(imo: Int): RecalculationState {
        val recalculatbleTime = Instant.now() - properties.readyOffset
        val shipHasVoyage = voyageDataSource.hasVoyageStartedBeforeForShip(imo, recalculatbleTime)

        // Only mark as ready when there is a voyage, and it is not a 0-second voyage
        return if (shipHasVoyage) {
            RecalculationState.READY
        } else {
            RecalculationState.NOT_READY
        }
    }

    private fun createEmptyShip(imo: Int): AutomaticRecalculationShip {
        return AutomaticRecalculationShip(
            imo = imo,
            state = RecalculationState.NOT_READY,
            scenarioId = null
        )
    }
}
