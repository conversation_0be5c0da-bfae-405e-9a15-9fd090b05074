package nl.teqplay.vesselvoyage.service.publisher

import com.fasterxml.jackson.databind.ObjectMapper
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.apiv2.model.OutgoingChange
import org.springframework.stereotype.Component

@ProfileProcessing
@Component
class RabbitMqOutgoingChangeSender(
    private val rabbitMqEventSender: RabbitMqEventSender?,
    private val objectMapper: ObjectMapper
) {
    fun send(
        exchange: String,
        change: OutgoingChange<*>,
        routingKey: String
    ) {
        rabbitMqEventSender?.send(
            exchange = exchange,
            message = objectMapper.writeValueAsBytes(change),
            routingKey = routingKey
        )
    }
}
