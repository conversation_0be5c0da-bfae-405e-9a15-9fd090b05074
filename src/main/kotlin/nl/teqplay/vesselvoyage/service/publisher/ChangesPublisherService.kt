package nl.teqplay.vesselvoyage.service.publisher

import nl.teqplay.skeleton.nats.NatsProducerStream
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.apiv2.model.OutgoingChange
import nl.teqplay.vesselvoyage.apiv2.model.OutgoingEntryChange
import nl.teqplay.vesselvoyage.apiv2.model.OutgoingSofChange
import nl.teqplay.vesselvoyage.apiv2.model.Visit
import nl.teqplay.vesselvoyage.apiv2.model.Voyage
import nl.teqplay.vesselvoyage.apiv2.model.sof.StatementOfFactsViewName.PORTREPORTER
import nl.teqplay.vesselvoyage.apiv2.model.sof.StatementOfFactsViewName.PTO
import nl.teqplay.vesselvoyage.apiv2.model.sof.portreporter.PortReporterStatementOfFactsView
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.PtoStatementOfFactsView
import nl.teqplay.vesselvoyage.mapper.EntryV2Mapper
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.v2.ESoFChange
import nl.teqplay.vesselvoyage.model.v2.EntryESoFWrapper
import nl.teqplay.vesselvoyage.model.v2.EntryId
import nl.teqplay.vesselvoyage.model.v2.NewChange
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.VisitChange
import nl.teqplay.vesselvoyage.model.v2.VoyageChange
import nl.teqplay.vesselvoyage.properties.EventPublishingProperties
import nl.teqplay.vesselvoyage.properties.EventPublishingProperties.Publishers
import nl.teqplay.vesselvoyage.service.api.EsofV2Service
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Service

@ProfileProcessing
@ConditionalOnProperty("event-publishing.enabled", havingValue = "true")
@Service
class ChangesPublisherService(
    private val eventPublishingProperties: EventPublishingProperties,
    private val natsProducerStream: NatsProducerStream<OutgoingChange<*>>?,
    private val rabbitMqEventSender: RabbitMqOutgoingChangeSender?,
    private val entryMapper: EntryV2Mapper,
    private val esofV2Service: EsofV2Service,
) {
    fun tryPublishNewChanges(oldStatus: NewShipStatus, updatedStatus: NewShipStatus, changes: List<NewChange<*>>) {
        // Nothing to publish because we have both publishers not configured
        if (natsProducerStream == null && rabbitMqEventSender == null) {
            return
        }

        val changesGroupedByEntryId: Map<EntryId, List<NewChange<*>>> = changes.groupBy { change ->
            when (change) {
                is ESoFChange -> change.value._id
                is VisitChange -> change.value._id
                is VoyageChange -> change.value._id
            }
        }

        for (entryChanges in changesGroupedByEntryId) {
            val entryChange = entryChanges.value.firstOrNull { change -> change is VisitChange || change is VoyageChange }
            val esofChange = entryChanges.value.firstOrNull { change -> change is ESoFChange } as? ESoFChange

            val matchingEntry = getMatchingEntry(entryChange, updatedStatus)
                ?: getMatchingEntry(entryChange, oldStatus)
                ?: getMatchingEntryFromEsof(esofChange, updatedStatus)
                // We can't create an outgoing change when we can't get the matching entry
                ?: continue

            val outgoingChanges = createOutgoingChangesFromInternalChanges(
                matchingEntry = matchingEntry,
                entryChange = entryChange as? NewChange<NewEntry>,
                esofChange = esofChange
            )

            publishOutgoingChanges(outgoingChanges)
        }
    }

    private fun publishOutgoingChanges(outgoingChanges: List<OutgoingChange<*>>) {
        for (outgoingChange in outgoingChanges) {
            val (natsSubject, rabbitMqRoutingKey) = createMessageSubjects(outgoingChange)

            // Only publish any changes to the publishers that are enabled
            if (eventPublishingProperties.nats.enabled) {
                natsProducerStream?.publish(natsSubject, outgoingChange)
            }

            if (eventPublishingProperties.rabbitMq.enabled) {
                rabbitMqEventSender?.send(
                    exchange = eventPublishingProperties.rabbitMq.exchange,
                    change = outgoingChange,
                    routingKey = rabbitMqRoutingKey
                )
            }
        }
    }

    /**
     * Helper function to create message subjects used by NATs and RabbitMQ.
     */
    private fun createMessageSubjects(outgoingChange: OutgoingChange<*>): Pair<String, String> {
        val subjectChangePart = when (outgoingChange) {
            is OutgoingEntryChange -> "entry"
            is OutgoingSofChange -> "sof" // TODO support portreporter sof
        }
        val natsSubject = "vessel-voyage.outgoing-changes.$subjectChangePart.${outgoingChange.value.entryId}"

        val action = outgoingChange.action
        val rabbitMqRoutingKey = when (outgoingChange) {
            is OutgoingEntryChange -> {
                val value = outgoingChange.value
                val entryPrefix = when (value) {
                    is Visit -> "VISIT"
                    is Voyage -> "VOYAGE"
                    else -> throw RuntimeException("Unsupported entry type, can't publish")
                }

                "$entryPrefix.${value.imo}.$action"
            }
            is OutgoingSofChange -> {
                val viewName: String
                val imo: String
                when (val value = outgoingChange.value) {
                    is PtoStatementOfFactsView -> {
                        viewName = "PTO"
                        imo = value.ship.imo ?: "UNKOWN_IMO"
                    }
                    is PortReporterStatementOfFactsView -> {
                        viewName = "PORTREPORTER"
                        imo = value.ship.imo.toString()
                    }
                    else -> throw RuntimeException("Unsupported sof view, can't publish")
                }

                "SOFVIEW.$viewName.$imo.$action"
            }
        }

        return natsSubject to rabbitMqRoutingKey
    }

    private fun getMatchingEntryFromEsof(esofChange: ESoFChange?, updatedStatus: NewShipStatus): NewEntry? {
        if (esofChange == null) {
            return null
        }

        val entryId = esofChange.value._id

        return when (updatedStatus) {
            is NewVisitShipStatus -> getMatchingEntryById(
                entryId = entryId,
                current = updatedStatus.visit.entry,
                previousInBetween = updatedStatus.previousVoyage?.entry,
                previous = updatedStatus.previousVisit?.entry
            )
            is NewVoyageShipStatus -> getMatchingEntryById(
                entryId = entryId,
                current = updatedStatus.voyage.entry,
                previousInBetween = updatedStatus.previousVisit?.entry,
                previous = updatedStatus.previousVoyage?.entry
            )
            else -> null
        }
    }

    private fun getMatchingEntryById(
        entryId: EntryId,
        current: NewEntry,
        previousInBetween: NewEntry?,
        previous: NewEntry?
    ): NewEntry? {
        if (current._id == entryId) {
            return current
        } else if (previousInBetween?._id == entryId) {
            return previousInBetween
        } else if (previous?._id == entryId) {
            return previous
        }

        // There was no entry matching our provided ID
        return null
    }

    private fun getMatchingEntry(entryChange: NewChange<*>?, updatedStatus: NewShipStatus): NewEntry? {
        val matchingEntryEsof = when (entryChange) {
            is VisitChange -> when (updatedStatus) {
                is NewVisitShipStatus -> updatedStatus.visit
                is NewVoyageShipStatus -> updatedStatus.previousVisit
                else -> null
            }
            is VoyageChange -> when (updatedStatus) {
                is NewVisitShipStatus -> updatedStatus.previousVoyage
                is NewVoyageShipStatus -> updatedStatus.voyage
                else -> null
            }
            else -> null
        }

        return matchingEntryEsof?.entry
    }

    private fun createOutgoingChangesFromInternalChanges(
        matchingEntry: NewEntry,
        entryChange: NewChange<NewEntry>?,
        esofChange: ESoFChange?
    ): List<OutgoingChange<*>> {
        if (entryChange == null && esofChange == null) {
            // We can't create any outgoing changes if we don't have a changed entry or changed esof
            return emptyList()
        }

        val (entryEsof, action) = getMergedChanges(
            matchingEntry = matchingEntry,
            entryChange = entryChange,
            esofChange = esofChange
        )
        return createOutgoingChangesForEntryEsof(
            entryEsof = entryEsof,
            action = action,
            onlyEsofChange = entryChange == null
        )
    }

    /**
     * Merge together the entry and esof change into an [EntryESoFWrapper] as that makes generating [OutgoingChange] easier.
     */
    private fun getMergedChanges(
        matchingEntry: NewEntry,
        entryChange: NewChange<NewEntry>?,
        esofChange: ESoFChange?
    ): Pair<EntryESoFWrapper<NewEntry>, Action> {
        return if (entryChange == null) {
            // Only the esof changed, meaning we are always having an UPDATE as the entry already existed
            EntryESoFWrapper(matchingEntry, esofChange?.value) to Action.UPDATE
        } else {
            // We either have changes for both or only update the entry
            // This means we can always take the action from the entry as that is the leading factor
            EntryESoFWrapper(entryChange.value, esofChange?.value) to entryChange.action
        }
    }

    private fun createOutgoingChangesForEntryEsof(entryEsof: EntryESoFWrapper<*>, action: Action, onlyEsofChange: Boolean): List<OutgoingChange<*>> {
        val entry = entryEsof.entry
        val esof = entryEsof.esof

        val outgoingChanges = mutableListOf<OutgoingChange<*>>()

        // We don't have to generate a new outgoing entry change when the esof only updated
        if (!onlyEsofChange && Publishers.ENTRIES in eventPublishingProperties.enabledPublishers) {
            val apiEntry = entryMapper.toApi(entry)
            outgoingChanges.add(OutgoingEntryChange(action, apiEntry))
        }

        if (entry is NewVisit && Publishers.STATEMENT_OF_FACTS in eventPublishingProperties.enabledPublishers) {
            outgoingChanges.add(OutgoingSofChange(action, esofV2Service.produce(PTO, entry, esof)))
            outgoingChanges.add(OutgoingSofChange(action, esofV2Service.produce(PORTREPORTER, entry, esof)))
        }

        return outgoingChanges
    }
}
