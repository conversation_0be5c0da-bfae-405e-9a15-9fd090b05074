package nl.teqplay.vesselvoyage.service.publisher

import com.fasterxml.jackson.databind.ObjectMapper
import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.model.OutgoingChange
import nl.teqplay.vesselvoyage.properties.EventPublishingProperties
import nl.teqplay.vesselvoyage.util.getRoutingKey
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Service
import java.io.IOException

/** Sends events to outgoing AMQP. */
@ProfileProcessing
@ConditionalOnProperty("event-publishing.enabled", "event-publishing.rabbit-mq.enabled", havingValue = "true")
@Service
class OutgoingEventsSender(
    private val properties: EventPublishingProperties,
    private val objectMapper: ObjectMapper,
    private val rabbitMqEventSender: RabbitMqEventSender
) {
    private val log = KotlinLogging.logger {}

    fun send(change: OutgoingChange) {
        if (properties.rabbitMq.enabled) {
            val routingKey = change.getRoutingKey()

            log.debug { "Send change $routingKey" }
            val changeInBytes = objectMapper.writeValueAsBytes(change)

            sendEvent(changeInBytes, routingKey)
        }
    }

    private fun sendEvent(change: ByteArray, routingKey: String) {
        try {
            rabbitMqEventSender.send(properties.rabbitMq.v1Exchange, change, routingKey)
        } catch (e: IOException) {
            // Prevent thread-lock from happening
            log.warn { "Outgoing event could not be sent to RabbitMQ" }
            return
        }
    }
}
