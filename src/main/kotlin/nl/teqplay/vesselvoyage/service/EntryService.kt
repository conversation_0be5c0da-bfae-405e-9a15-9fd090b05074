package nl.teqplay.vesselvoyage.service

import nl.teqplay.skeleton.common.exception.NotFoundException
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.datasource.VisitDataSource
import nl.teqplay.vesselvoyage.datasource.VoyageDataSource
import nl.teqplay.vesselvoyage.model.ChangeMetadata
import nl.teqplay.vesselvoyage.model.Entry
import nl.teqplay.vesselvoyage.model.ShipStatus
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.model.VisitVoyage
import nl.teqplay.vesselvoyage.model.Voyage
import nl.teqplay.vesselvoyage.model.isVisitId
import nl.teqplay.vesselvoyage.model.isVoyageId
import org.springframework.stereotype.Service
import java.time.ZonedDateTime

@ProfileApi
@ProfileProcessing
@Service
class EntryService(
    private val visitDataSource: VisitDataSource,
    private val voyageDataSource: VoyageDataSource,
    private val staticShipInfoService: StaticShipInfoService,
    private val shipStatuses: ShipStatusService
) {
    fun getCurrentShipStatus(imo: String): ShipStatus {
        return shipStatuses[imo]
    }

    fun findEntry(entryId: String): Entry? {
        if (isVisitId(entryId)) {
            return visitDataSource.findById(entryId)
        }

        if (isVoyageId(entryId)) {
            return voyageDataSource.findById(entryId)
        }

        // for backward compatibility and robustness
        return visitDataSource.findById(entryId)
            ?: voyageDataSource.findById(entryId)
    }

    fun findEntries(entryIds: List<String>): List<Entry> {
        // note that we need to reckon with legacy id's having no suffix
        val visitAndLegacyIds = entryIds.filter { !isVoyageId(it) }
        val voyageAndLegacyIds = entryIds.filter { !isVisitId(it) }

        return (visitDataSource.findByIds(visitAndLegacyIds) + voyageDataSource.findByIds(voyageAndLegacyIds))
            .sortedBy { it.startTime }
    }

    fun findEntriesByIMO(imo: String, start: ZonedDateTime, end: ZonedDateTime, limit: Int): List<Entry> {
        val visits = visitDataSource.findByIMO(imo, start, end, limit)
        val voyages = voyageDataSource.findByIMO(imo, start, end, limit)
        // First add voyages to ensure a 0-second voyage is placed before a visit with the same timestamp.
        return (voyages + visits)
            .sortedBy { it.startTime }
            .take(limit)
    }

    /**
     * Get a list [VisitVoyage] (visit+voyage pairs) centered in the given visitId, or if not provided, the most recent
     * visit by the given imo.
     * Consider that if the center visit is close the ends (starting or end of the visits) the expected length of the
     * list may be shorter.
     */
    fun getVisitsAroundVisit(imo: String, visitId: String?, radius: UInt): List<VisitVoyage> {
        val visitCenter = getVisitByIdOrMostRecent(visitId, imo) ?: throw NotFoundException("No visit found for the given imo.")
        val pastVisits = visitDataSource.findPastVisits(visitCenter, radius) // i.e. [9, 8, 7]
        val futureVisits = visitDataSource.findFutureVisits(visitCenter, radius) // i.e. [13, 12, 11]
        val visits = futureVisits + visitCenter + pastVisits // Resulting in [13, 12, 11, 10, 9, 8, 7]
        val voyageIds = visits.mapNotNull(Visit::previousEntryId)
        val voyages = voyageDataSource.findByIds(voyageIds)
        return pairRelatedVisitsAndVoyages(visits, voyages)
    }

    /**
     * Auxiliar method for [getVisitsAroundVisit] to get the starting point for [findPastVisits] and [findFutureVisits] to get visits around it.
     * It encapsulates the calculation of visitCenter for the [getVisitsAroundVisit] method given the visitId or imo arguments.
     */
    private fun getVisitByIdOrMostRecent(visitId: String?, imo: String): Visit? {
        return if (visitId != null) {
            visitDataSource.findByIdAndImo(visitId, imo)
        } else {
            visitDataSource.findRecentByIMO(imo, 1).firstOrNull()
        }
    }

    /**
     * Auxiliary method for [getVisitsAroundVisit] to combine visits with Voyages making sure that they relate one-to-one.
     * Otherwise, it'd rise an exception, allowing a missing (null) voyage just at the last pair.
     */
    private fun pairRelatedVisitsAndVoyages(visits: List<Visit>, voyages: List<Voyage>): List<VisitVoyage> {
        val voyagesById = voyages.associateBy { it._id }
        return visits.map { visit ->
            val voyage = voyagesById[visit.previousEntryId]
            VisitVoyage(visit, voyage)
        }
    }

    fun getChangeMetadata(imo: String): ChangeMetadata {
        val registerCache = staticShipInfoService.getShipRegisterInfoCacheByIMO(imo)

        return ChangeMetadata(
            categories = registerCache?.categories,
            dimensions = registerCache?.dimensions,
            specification = registerCache?.specification,
            calculated = registerCache?.calculated
        )
    }
}
