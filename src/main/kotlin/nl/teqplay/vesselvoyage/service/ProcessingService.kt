package nl.teqplay.vesselvoyage.service

import io.github.oshai.kotlinlogging.KotlinLogging
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.annotation.ProfileRevents
import nl.teqplay.vesselvoyage.properties.DiskCacheProperties
import nl.teqplay.vesselvoyage.service.queue.AisStreamingMessageHandler
import nl.teqplay.vesselvoyage.service.queue.EventsMessageHandler
import nl.teqplay.vesselvoyage.service.recalculation.AutomaticRecalculationService
import nl.teqplay.vesselvoyage.spring.ProcessingRefreshEvent
import org.springframework.context.event.ContextClosedEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Service
import java.time.Duration
import kotlin.system.measureTimeMillis

/**
 * Service that will keep track of the processing that is being done inside VesselVoyage.
 *
 * 1. It will only start consuming the queues and allow recalculations when the system is fully started.
 * 2. It will stop processing when a connection to platform isn't available.
 * 3. It will gracefully stop the event processing, persisting of traces and stop recalculating of vessels on shutdown.
 */
@ProfileProcessing
@ProfileRevents
@Service
class ProcessingService(
    private val diskCacheProperties: DiskCacheProperties,
    private val aisStreamingMessageHandler: AisStreamingMessageHandler?,
    private val eventMessageHandler: EventsMessageHandler?,
    private val staticShipInfoService: StaticShipInfoService,
    private val infraService: InfraService,
    private val recalculationService: RecalculationService?,
    private val automaticRecalculationService: AutomaticRecalculationService?,
    private val v1TraceService: V1TraceService?,
    private val applicationScope: CoroutineScope
) {
    private val log = KotlinLogging.logger {}

    /**
     * Boolean that indicates if we have started.
     */
    @Volatile
    private var running = false

    /**
     * Boolean that indicates if we are processing any events and recalculations.
     */
    @Volatile
    private var processing = false

    /**
     * Boolean indicating we are shutting down VesselVoyage.
     */
    @Volatile
    private var closing = false

    init {
        log.info { "Starting processing service" }
        val shipsAndInfraLoadTime = measureTimeMillis {
            runBlocking(Dispatchers.Default) {
                applicationScope.launch {
                    val shipsToLoadIn = async { staticShipInfoService.onStartup() }
                    val infraToLoadIn = async { infraService.onStartup() }

                    shipsToLoadIn.await()
                    infraToLoadIn.await()
                }.join()
            }
        }

        log.info { "Loaded in all ships and infra needed for processing ($shipsAndInfraLoadTime ms)" }

        if (shouldStartConsumers()) {
            running = true
            onStartProcessing()

            applicationScope.launch {
                async { optionalRefreshAfterStartup() }
            }
        } else {
            log.warn { "Could not load in the required data, attempting in the background while not consuming" }

            // Any of the cached data is incorrect, so we can't start processing.
            // Keep trying to refresh until our internal caches are valid.
            // We will do this in the background, so we can make the VesselVoyage API available.

            applicationScope.launch {
                async {
                    val delayMillis = Duration.ofSeconds(2).toMillis()
                    while (!staticShipInfoService.isShipCacheValid()) {
                        delay(delayMillis)
                        staticShipInfoService.refresh()
                    }

                    while (!infraService.isPomaDataValid()) {
                        delay(delayMillis)
                        infraService.refresh()
                    }

                    running = true
                    onStartProcessing()
                }
            }
        }
    }

    @EventListener(ProcessingRefreshEvent::class)
    fun onRefreshProcessing() {
        log.warn { "Something went wrong when processing, refreshing processing (processing = $processing)" }
        onStopProcessing()
        onStartProcessing()
        log.info { "Processing refreshed (processing = $processing)" }
    }

    /**
     * What to do when we want to start processing.
     */
    fun onStartProcessing() {
        processing = true
        log.info { "Start processing" }
        aisStreamingMessageHandler?.startup()
        eventMessageHandler?.startup()
        recalculationService?.start()
        automaticRecalculationService?.startup()
        log.info { "Started all processing" }
    }

    /**
     * What to do when we want to stop processing.
     */
    fun onStopProcessing() {
        processing = false
        log.info { "Stop processing" }
        aisStreamingMessageHandler?.shutdown()
        eventMessageHandler?.shutdown()
        recalculationService?.shutdown()
        automaticRecalculationService?.shutdown()
        log.info { "Stopped all processing" }
    }

    /**
     * What to do when the application is being shutdown.
     */
    @EventListener(ContextClosedEvent::class)
    fun onClose() {
        log.info { "Shutting down all processing and trace service" }
        closing = true
        onStopProcessing()
        v1TraceService?.shutdown()
    }

    /**
     * Helper function which checks if we are ready to start consume events.
     */
    private fun shouldStartConsumers(): Boolean {
        return staticShipInfoService.isShipCacheValid() &&
            infraService.isPomaDataValid()
    }

    private suspend fun optionalRefreshAfterStartup() {
        // Refresh the data after startup only when using the disk cache
        if (diskCacheProperties.enabled) {
            infraService.refresh()
            staticShipInfoService.refresh()
        }
    }
}
