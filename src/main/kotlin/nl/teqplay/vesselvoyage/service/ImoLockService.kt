package nl.teqplay.vesselvoyage.service

import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import org.springframework.stereotype.Service
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

@ProfileProcessing
@Service
class ImoLockService {
    /**
     * Map to track locks for each IMO.
     */
    private val imoLocks = ConcurrentHashMap<Int, ReentrantLock>()

    /**
     * Lock for real-time and add a possibility to execute logic if this function created the lock, or it was already locked.
     */
    fun lock(imo: Int, onLock: (createdLock: Boolean) -> Unit = {}): Boolean {
        val lock = imoLocks.computeIfAbsent(imo) { ReentrantLock() }
        val createdLock = lock.tryLock()
        onLock(createdLock)
        return createdLock
    }

    /**
     * Unlock the IMO and clean up the lock if no longer needed.
     */
    fun unlock(imo: Int) {
        val lock = imoLocks[imo]
        if (lock != null && lock.isHeldByCurrentThread) {
            lock.unlock()
        }
    }

    /**
     * Execute the following [body] and wait until we can take the lock.
     */
    fun executeBlocking(imo: Int, body: () -> Unit) {
        val lock = imoLocks.computeIfAbsent(imo) { ReentrantLock() }
        lock.withLock {
            body()
        }
    }
}
