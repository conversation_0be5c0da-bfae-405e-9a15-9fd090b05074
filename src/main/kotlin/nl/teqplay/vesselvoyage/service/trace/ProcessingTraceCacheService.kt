package nl.teqplay.vesselvoyage.service.trace

import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.datasource.NewTraceDataSource
import nl.teqplay.vesselvoyage.model.v2.EntryId
import nl.teqplay.vesselvoyage.model.v2.NewTrace
import org.springframework.stereotype.Service
import java.util.concurrent.ConcurrentHashMap

@ProfileProcessing
@Service
class ProcessingTraceCacheService(
    private val traceDataSource: NewTraceDataSource,
) {
    private val currentTraceById = ConcurrentHashMap<EntryId, NewTrace?>()

    fun getTraceDirectly(id: EntryId): NewTrace? {
        return traceDataSource.findById(id)
    }

    fun getCurrentById(id: EntryId): NewTrace? {
        return currentTraceById.computeIfAbsent(id) {
            traceDataSource.findById(id)
        }
    }

    fun updateTrace(trace: NewTrace) {
        currentTraceById[trace._id] = trace
    }

    fun onReplaceCurrentTrace(oldId: EntryId?, newTrace: NewTrace) {
        if (oldId != null) {
            currentTraceById.remove(oldId)
        }
        updateTrace(newTrace)
    }
}
