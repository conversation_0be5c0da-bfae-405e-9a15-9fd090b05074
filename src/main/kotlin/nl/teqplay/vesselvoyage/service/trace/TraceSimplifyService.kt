package nl.teqplay.vesselvoyage.service.trace

import com.goebl.simplify.PointExtractor
import com.goebl.simplify.Simplify
import nl.teqplay.skeleton.model.Location
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.properties.TraceProperties
import nl.teqplay.vesselvoyage.util.toScaled
import org.springframework.stereotype.Service

@ProfileProcessing
@ProfileApi
@Service
class TraceSimplifyService(
    private val traceProperties: TraceProperties
) {
    companion object {
        /**
         * The scale we calculate our polyline to.
         * Remove any locations that have exactly the same location at 5 digits after the decimal.
         */
        private const val ENCODING_PRECISION_SCALE = 5
    }

    fun simplifyTrace(locations: List<Location>): List<Location> {
        val traceSimplifier = TraceSimplifier(traceProperties)
        val dedupedLocations = removeSequentialDuplicateLocations(locations)

        return traceSimplifier.simplify(dedupedLocations)
    }

    private fun removeSequentialDuplicateLocations(locations: List<Location>): List<Location> {
        val deduplicatedLocations = mutableListOf<Location>()

        var lastLocation: Location? = null
        locations.forEach { location: Location ->
            // Scale the location to the value that would actually be used in a polyline
            val actualPolylineLocation = location.toScaled(ENCODING_PRECISION_SCALE)

            // Only add the location if it is not exactly the same
            if (actualPolylineLocation != lastLocation) {
                deduplicatedLocations.add(location)
            }

            lastLocation = actualPolylineLocation
        }

        return deduplicatedLocations
    }

    /**
     * Wrapper class to create an empty [Simplify] that always has an empty [sampleArray] to have consistent results
     *  and makes use of the [TracePointExtractor].
     */
    private class TraceSimplifier(
        private val traceProperties: TraceProperties,
    ) : Simplify<Location>(emptyArray(), TracePointExtractor()) {
        fun simplify(
            locations: List<Location>
        ): List<Location> {
            return simplify(locations.toTypedArray(), traceProperties.tolerance, true).toList()
        }
    }

    /**
     * Implementation of a [PointExtractor] that supports the [Location] model.
     * This [TracePointExtractor] is used to get the latitude as x and longitude as y when simplifying the trace.
     */
    private class TracePointExtractor : PointExtractor<Location> {
        override fun getX(point: Location): Double {
            return point.lat
        }

        override fun getY(point: Location): Double {
            return point.lon
        }
    }
}
