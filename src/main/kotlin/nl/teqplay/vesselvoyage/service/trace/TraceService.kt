package nl.teqplay.vesselvoyage.service.trace

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polyline
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.datasource.NewTraceDataSource
import nl.teqplay.vesselvoyage.model.internal.TraceItem
import nl.teqplay.vesselvoyage.model.v2.EntryId
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewTrace
import nl.teqplay.vesselvoyage.properties.TraceProperties
import nl.teqplay.vesselvoyage.service.AisFetchingService
import nl.teqplay.vesselvoyage.service.SLACK_COLOR_RED
import nl.teqplay.vesselvoyage.service.SlackMessageService
import nl.teqplay.vesselvoyage.service.api.EntryV2Service
import nl.teqplay.vesselvoyage.service.trace.ProcessingTraceService.Companion.MAX_NON_SIMPLIFIED_TRACE_ITEMS
import nl.teqplay.vesselvoyage.util.createSpeedOfTraceItemsOrNull
import nl.teqplay.vesselvoyage.util.createTraceDistanceFrom
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.time.Duration
import java.time.Instant
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit
import kotlin.system.measureTimeMillis

/**
 * Service containing the minimal functionality needed to serve simple API requests to retrieve traces and generate traces when needed.
 *
 * For the processing of traces with the AIS messages, see [ProcessingTraceService].
 */
@ProfileApi
@ConditionalOnMissingBean(ProcessingTraceService::class)
@Service
open class TraceService(
    private val properties: TraceProperties,
    private val dataSource: NewTraceDataSource,
    private val simplifyService: TraceSimplifyService,
    private val aisFetchingService: AisFetchingService,
    private val entryService: EntryV2Service,
    private val slackMessageService: SlackMessageService?
) {
    private val log = KotlinLogging.logger {}

    /**
     * Thread-safe Set of entry IDs that we have to generate new traces for somewhere in the background.
     * It doesn't matter if we lose the scheduled traces to be generated when we restart as it can always be re-scheduled if the consumer really wants the trace.
     */
    private val traceIdsToGenerate = ConcurrentHashMap.newKeySet<EntryId>()

    /**
     * Get the trace by ID and optionally generate it in the background so next time we need the trace it is generated.
     */
    fun getTraceById(
        entryId: EntryId,
        scheduleTraceGeneratingIfMissing: Boolean,
        blocking: Boolean = false
    ): NewTrace? {
        val result = dataSource.findById(entryId)

        if (scheduleTraceGeneratingIfMissing && result == null) {
            // When we need to trace blocking, don't schedule it but generate it immediately
            if (blocking) {
                log.info { "Trace is missing but requested blocking, generating now. (id = $entryId)" }
                return generateTraceForTraceId(entryId)
            }

            // Schedule the trace to be calculated
            scheduleSingleTraceCalculation(entryId, force = true)
            return null
        }

        return result
    }

    /**
     * Get the trace by ID and optionally generate it in the background so next time we need the trace it is generated.
     */
    fun getTracesByIds(
        entryIds: List<EntryId>,
        scheduleTraceGeneratingIfMissing: Boolean,
        blocking: Boolean = false
    ): List<NewTrace> {
        val result = dataSource.findByIds(entryIds)
        val missingIds = entryIds - result.map { it._id }

        if (scheduleTraceGeneratingIfMissing && missingIds.isNotEmpty()) {
            // When we need to trace blocking, don't schedule it but generate it immediately
            if (blocking) {
                log.info { "Trace is missing but requested blocking, generating now. (ids = ${entryIds.joinToString()})" }
                // If any of the IDs fail, don't include them in the result
                return result + entryIds.mapNotNull { entryId -> generateTraceForTraceId(entryId) }
            }

            // Schedule the trace to be calculated
            scheduleTraceCalculation(entryIds, force = true)
        }

        // Always return the result, even when it is only containing part of the requested IDs
        return result
    }

    fun getCombinedPolyline(entryId: EntryId): Polyline? {
        val entryTrace = getTraceById(
            entryId = entryId,
            scheduleTraceGeneratingIfMissing = true
        ) ?: return null

        return getCombinedPolyline(entryTrace)
    }

    fun getCombinedPolyline(trace: NewTrace): Polyline {
        val simplifiedPolyline = trace.simplifiedPolyline

        return simplifiedPolyline?.appendPolyline(trace.polyline)
            // We don't have any simplified traces
            ?: trace.polyline
    }

    /**
     * Schedule trace generation for multiple Visits and/or Voyages.
     * @see scheduleSingleTraceCalculation
     */
    fun scheduleTraceCalculation(entryIds: List<EntryId>, force: Boolean) {
        entryIds.forEach { entryId ->
            scheduleSingleTraceCalculation(entryId, force)
        }
    }

    /**
     * Schedule a trace to be calculated for a Visit or Voyage.
     *
     * @param entryId The ID of the Visit or Voyage we want to create a trace for.
     * @param force When true, always generate a trace even when it already exists.
     */
    private fun scheduleSingleTraceCalculation(entryId: EntryId, force: Boolean = false) {
        // Check if we already have a trace when not forcing a trace generation
        if (!force) {
            val databaseHasTrace = dataSource.exists(entryId)

            // No need to do anything when we already have a trace for this entry
            if (databaseHasTrace) {
                log.trace {
                    "We already have a trace but we attempted to schedule a trace calculation. (id = " +
                        "$entryId, force = $force)"
                }
                return
            }
        }

        log.trace { "New trace generation scheduled. (id = $entryId, force = $force)" }
        traceIdsToGenerate.add(entryId)
    }

    /**
     * Create a [NewTrace] from the provided [traceItems].
     *
     * @param traceItems The AIS points we want to create a trace for.
     * @param locations A list of all locations of each [traceItems].
     * @param currentEntryId The entry we are currently creating a [NewTrace] for.
     * @param previousTraceLastLocation The last location we had with the previous entry trace, this location is needed
     * so the locations are attached to each other.
     * @param previousTraceLastSpeedOverGround The last speed of the previous trace (when available), to determine
     * the duration of the first speed of this trace.
     *
     * @return The [NewTrace] created from the [traceItems].
     */
    fun computeNewTrace(
        traceItems: List<TraceItem>,
        locations: List<Location>,
        currentEntryId: String,
        previousEntryEndTime: Instant?,
        previousTraceLastLocation: Location?,
        previousTraceLastSpeedOverGround: Float?,
        previousTraceLastDraught: Float?
    ): NewTrace {
        // Append the previous trace location in front of the provided list of locations
        val mergedLocations = previousTraceLastLocation?.let { listOf(it) + locations } ?: locations
        val distance = createTraceDistanceFrom(mergedLocations)

        val traceStart = traceItems.firstOrNull()?.timestamp

        val draught = if (previousTraceLastDraught != null && traceStart != null && (previousEntryEndTime == null || previousEntryEndTime.isBefore(traceStart))) {
            // Attempt to use the previous entry end time as that is the time of our draught and not when we got any AIS point
            val actualTraceStart = previousEntryEndTime ?: traceStart
            val baseDraught = createTraceStatistic(previousTraceLastDraught, actualTraceStart)

            appendTraceStatisticFromTraceItems(baseDraught, traceItems, TraceItem::draught)
        } else {
            createTraceStatisticFromTraceItemsOrNull(traceItems, TraceItem::draught)
        }

        // Simplify the new trace if we have too many trace items
        return if (mergedLocations.size > MAX_NON_SIMPLIFIED_TRACE_ITEMS) {
            // Remove the last location as this will be the start of the new ongoing trace
            val locationsToSimplify = mergedLocations.toMutableList()
            val lastLocation = locationsToSimplify.removeLast()

            // Simplify all locations except for the last one
            val simplifiedLocations = simplifyService.simplifyTrace(locationsToSimplify)
            val ongoingPolyline = Polyline.of(listOf(lastLocation))
            val simplifiedPolyline = Polyline.of(simplifiedLocations)

            NewTrace(
                _id = currentEntryId,
                polyline = ongoingPolyline,
                totalOngoingTraceItems = 1,
                simplifiedPolyline = simplifiedPolyline,
                speed = createSpeedOfTraceItemsOrNull(traceItems, previousTraceLastSpeedOverGround),
                distance = distance,
                draught = draught
            )
        } else {
            // We only got a small amount of trace items, so we can just create a new one without simplifying
            NewTrace(
                _id = currentEntryId,
                polyline = Polyline.of(path = mergedLocations),
                totalOngoingTraceItems = mergedLocations.size,
                simplifiedPolyline = null,
                speed = createSpeedOfTraceItemsOrNull(traceItems, previousTraceLastSpeedOverGround),
                distance = distance,
                draught = draught
            )
        }
    }

    /**
     * Helper function to create a [NewTrace] from AIS fetched from ShipHistory.
     */
    fun generateTraceWithShipHistory(
        entry: NewEntry,
        imo: Int,
        start: Instant,
        end: Instant,
        previousTraceLastLocation: Location?,
        previousTraceLastSpeedOverGround: Float?,
        previousTraceLastDraught: Float?,
    ): NewTrace? {
        // Having a trace of a single point is rather useless to show and can be skipped
        //  as the surrounding entries have this time already covered
        if (start == end) {
            return null
        } else if (Duration.between(start, end).toDays() > properties.maxRequestLengthDays) {
            log.warn {
                "Trace request length for imo ${entry.imo} (${Duration.between(start, end).toDays()} days) " +
                    "is bigger than max length (${properties.maxRequestLengthDays} days), returning empty list"
            }
            return null
        }

        val traces = try {
            aisFetchingService.getShipTrace(
                imo = imo.toString(),
                startTime = start.atZone(ZoneOffset.UTC),
                endTime = end.atZone(ZoneOffset.UTC)
            ).map { aisHistoricMessage ->
                TraceItem(
                    location = aisHistoricMessage.location,
                    speedOverGround = aisHistoricMessage.speedOverGround,
                    draught = aisHistoricMessage.draught,
                    timestamp = aisHistoricMessage.messageTime
                )
            }
        } catch (ex: Exception) {
            // Don't make a trace object for now, it might be a hiccup in the connection
            log.warn(ex) { "Could not generate a trace using ship history, something went wrong when fetching the data from ShipHistory" }
            return null
        }

        if (traces.isEmpty()) {
            return null
        }

        val entryEnd = entry.end
        // The searching of a trace is end exclusive, meaning we have to add the end location of the entry instead
        // to get a trace that is attached to the next entry
        val correctedTraces = if (entryEnd != null) {
            traces + TraceItem(
                location = entryEnd.location,
                speedOverGround = null,
                draught = null,
                timestamp = entryEnd.time
            )
        } else {
            traces
        }

        return computeNewTrace(
            traceItems = correctedTraces,
            locations = correctedTraces.map { it.location },
            currentEntryId = entry._id,
            previousEntryEndTime = start,
            previousTraceLastLocation = previousTraceLastLocation,
            previousTraceLastSpeedOverGround = previousTraceLastSpeedOverGround,
            previousTraceLastDraught = previousTraceLastDraught,
        )
    }

    private fun generateTraceForTraceId(
        traceId: String
    ): NewTrace? {
        val entry = entryService.findEntry(traceId)

        if (entry == null) {
            log.warn { "We scheduled a trace generation but the entry has been removed, skipping. (id = $traceId)" }
            return null
        }

        val entryEnd = entry.end
        if (entryEnd == null) {
            log.warn { "Cannot generate trace when entry isn't finished yet, skipping. (id = $traceId)" }
            return null
        }

        val previousEntryId = entry.previous

        val createdTrace = if (previousEntryId == null) {
            generateTraceWithShipHistory(
                entry = entry,
                imo = entry.imo,
                start = entry.start.time,
                end = entryEnd.time,
                previousTraceLastLocation = null,
                previousTraceLastSpeedOverGround = null,
                previousTraceLastDraught = null
            )
        } else {
            // Take into account the previous entry trace last location to have a nice stitched together trace.
            val previousTrace = dataSource.findById(previousEntryId)
            generateTraceWithShipHistory(
                entry = entry,
                imo = entry.imo,
                start = entry.start.time,
                end = entryEnd.time,
                previousTraceLastLocation = previousTrace?.polyline?.lastLocation,
                previousTraceLastSpeedOverGround = previousTrace?.speed?.lastSpeedOverGround,
                previousTraceLastDraught = previousTrace?.draught?.last
            )
        }

        if (createdTrace != null) {
            dataSource.insertOrUpdate(createdTrace)
        }

        return createdTrace
    }

    /**
     * Check occasionally if we have traces to be generated
     */
    @Scheduled(fixedDelay = 1, timeUnit = TimeUnit.MINUTES)
    fun generateTraces() {
        val startTime = ZonedDateTime.now()
        try {
            val currentBatchToProcess = traceIdsToGenerate.toList()

            if (currentBatchToProcess.isEmpty()) {
                log.trace { "No traces to generate." }
                return
            }

            log.info { "Generating traces for ${currentBatchToProcess.size} entities." }
            var finishedGenerating = 0
            val processingDuration = measureTimeMillis {
                // We calculate the batch of trace 1 by 1 and not in parallel to not overload ship history
                for (traceId in currentBatchToProcess) {
                    val generatedTrace = generateTraceForTraceId(traceId)
                    // Always remove the traceId from the set, even if it failed to generate
                    traceIdsToGenerate.remove(traceId)

                    if (generatedTrace != null) {
                        finishedGenerating += 1
                    }
                }
            }

            log.info { "Done generating traces $finishedGenerating out of the ${currentBatchToProcess.size} successfully in $processingDuration ms." }
        } catch (e: Throwable) {
            val failTime = ZonedDateTime.now()
            log.error(e) { "Error generating traces: ${e.message}" }
            slackMessageService?.sendMessage(
                title = "Scheduled task failed: ProcessingTraceService.generateTraces",
                text = "Error generating traces: ${e.message}\nStarted at: $startTime\nFailed at: $failTime",
                barColor = SLACK_COLOR_RED
            )
        }
    }
}
