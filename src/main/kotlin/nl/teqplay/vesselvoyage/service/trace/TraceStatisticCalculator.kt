package nl.teqplay.vesselvoyage.service.trace

import nl.teqplay.vesselvoyage.model.internal.TraceItem
import nl.teqplay.vesselvoyage.model.v2.TraceStatistic
import nl.teqplay.vesselvoyage.util.weightedAverage
import java.time.Duration
import java.time.Instant
import kotlin.math.max
import kotlin.math.min
import kotlin.reflect.KProperty1

/**
 * Creates a trace statistic for the given [value] and [atTimestamp]. This is intended for the first point in the
 * statistic, and should be appended with more data to make it meaningful. As the created object has a duration of
 * zero seconds, there is no (weighted) average to calculate, so min/max/avg will have the same value.
 */
fun createTraceStatistic(value: Float, atTimestamp: Instant): TraceStatistic {
    return TraceStatistic(
        min = value,
        max = value,
        avg = value,
        last = value,
        duration = Duration.ZERO,
        lastTimestamp = atTimestamp
    )
}

/**
 * Loops through the list, discarding [items] until finding the first non-null value of [valueProperty], then
 * creates a [TraceStatistic] out of it. The remainder of the list is appended to the statistic using
 * [appendTraceStatisticFromTraceItems].
 *
 * Note that this discards trace items until a non-null value from [valueProperty] is found! Meaning that the
 * duration of the discarded trace items is not taken into account with the weighted average calculation. That
 * may skew the average, but sometimes it's the only way, such as when a ship emits its first AIS messages.
 *
 * Use [createTraceStatistic] if a previous trace with the value of [valueProperty] is available!
 */
fun createTraceStatisticFromTraceItemsOrNull(
    items: List<TraceItem>,
    valueProperty: KProperty1<TraceItem, Float?>,
    timestampProperty: KProperty1<TraceItem, Instant> = TraceItem::timestamp
): TraceStatistic? {
    val firstEncounterIndex = items.indexOfFirst { valueProperty(it) != null }
    val first = items.getOrNull(firstEncounterIndex)
        ?: return null

    val stat = createTraceStatistic(valueProperty(first)!!, timestampProperty(first))

    // append remainder if the list has items left
    // +1 for skipping 'first' item here above
    val remainder = items.drop(firstEncounterIndex + 1)
    return appendTraceStatisticFromTraceItems(
        existing = stat,
        newItems = remainder,
        valueProperty = valueProperty
    )
}

/**
 * Appends the [newItems] to the [existing] trace statistic. If a trace item [valueProperty] gives a null value, then
 * the duration is appended.
 */
fun appendTraceStatisticFromTraceItems(
    existing: TraceStatistic,
    newItems: List<TraceItem>,
    valueProperty: KProperty1<TraceItem, Float?>,
    timestampProperty: KProperty1<TraceItem, Instant> = TraceItem::timestamp
): TraceStatistic {

    if (newItems.isEmpty()) {
        return existing
    }

    var min = existing.min
    var max = existing.max
    var avg = existing.avg
    var durationSeconds = existing.duration.toSeconds()
    var last = existing.last
    var lastTimestamp = existing.lastTimestamp

    newItems.forEach { item ->
        // Append the duration to the last value
        // Then set the new value
        val newValue = valueProperty.get(item) ?: last
        val newTimestamp = timestampProperty.get(item)
        val durationToAddSec = Duration.between(lastTimestamp, newTimestamp).toSeconds()

        val newAvg = weightedAverage(
            valueA = avg,
            weightA = durationSeconds,
            valueB = last,
            weightB = durationToAddSec
        )

        min = min(min, newValue)
        max = max(max, newValue)
        avg = newAvg
        durationSeconds += durationToAddSec
        last = newValue
        lastTimestamp = newTimestamp
    }

    return existing.copy(min, max, avg, last, Duration.ofSeconds(durationSeconds), lastTimestamp)
}

/**
 * Merges [a] and [b]. Both must be adjacent and must not overlap.
 */
fun mergeAdjacentTraceStatistics(a: TraceStatistic?, b: TraceStatistic?): TraceStatistic? {
    if (a == null || b == null) {
        return a ?: b
    }
    val newest = if (a.lastTimestamp > b.lastTimestamp) {
        a
    } else {
        b
    }
    return TraceStatistic(
        min = min(a.min, b.min),
        max = max(a.max, b.max),
        avg = weightedAverage(
            valueA = a.avg,
            weightA = a.duration.toSeconds(),
            valueB = b.avg,
            weightB = b.duration.toSeconds()
        ),
        last = newest.last,
        lastTimestamp = newest.lastTimestamp,
        duration = a.duration + b.duration
    )
}
