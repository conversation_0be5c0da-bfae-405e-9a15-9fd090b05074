package nl.teqplay.vesselvoyage.service.trace

import com.mongodb.MongoWriteException
import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polyline
import nl.teqplay.skeleton.util.PolylineUtils
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.datasource.NewTraceDataSource
import nl.teqplay.vesselvoyage.model.internal.TraceItem
import nl.teqplay.vesselvoyage.model.v2.EntryId
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewTrace
import nl.teqplay.vesselvoyage.properties.TraceProperties
import nl.teqplay.vesselvoyage.service.AisFetchingService
import nl.teqplay.vesselvoyage.service.SlackMessageService
import nl.teqplay.vesselvoyage.service.api.EntryV2Service
import nl.teqplay.vesselvoyage.util.amendSpeedWithDuration
import nl.teqplay.vesselvoyage.util.appendToTraceDistance
import nl.teqplay.vesselvoyage.util.createSpeedOfTraceItemsOrNull
import nl.teqplay.vesselvoyage.util.createTraceDistanceFrom
import nl.teqplay.vesselvoyage.util.joinToStringTruncated
import nl.teqplay.vesselvoyage.util.mergeSpeeds
import org.springframework.stereotype.Service
import java.time.Instant

/**
 * Processes and calculates trace information of a [nl.teqplay.vesselvoyage.model.v2.NewEntry]: a polyline of the
 * traveled route and speed.
 * In a real time scenario, a trace builds up as new AIS data comes in.
 * In a recalculating scenario, the full AIS trace of the entry start-end can be given to create a full trace.
 *
 * A trace, especially its polyline, is simplified when the amount of data points becomes too much for MongoDB to
 * store it.
 */
@ProfileProcessing
@Service
class ProcessingTraceService(
    private val dataSource: NewTraceDataSource,
    private val simplifyService: TraceSimplifyService,
    aisFetchingService: AisFetchingService,
    private val entryService: EntryV2Service,
    private val traceCacheService: ProcessingTraceCacheService,
    traceProperties: TraceProperties,
    slackMessageService: SlackMessageService?
) : TraceService(
    properties = traceProperties,
    dataSource = dataSource,
    simplifyService = simplifyService,
    aisFetchingService = aisFetchingService,
    entryService = entryService,
    slackMessageService = slackMessageService
) {
    private val log = KotlinLogging.logger {}

    companion object {
        /**
         * The total amount of trace items we allow until they are simplified
         */
        const val MAX_NON_SIMPLIFIED_TRACE_ITEMS = 100
    }

    /**
     * Creates or updates the trace of an entry with the new [traceItems]. The entry is determined by the
     * [NewShipStatus.getEntryId].
     *
     * @param traceItems The new trace items. In a realtime scenario, this is just 1 or a couple of items. In a
     * recalculate scenario, it can be all items of a full AIS trace (`entry.start` up to `entry.end`).
     */
    fun insertIntoCurrentTrace(
        ongoingEntry: NewEntry?,
        traceItem: TraceItem,
    ): Boolean {
        val currentEntryId = ongoingEntry?._id
            // Ignore the locations if we don't have any ongoing entry for this vessel
            ?: return false

        val locations = listOf(traceItem.location)
        val currentTrace = traceCacheService.getCurrentById(currentEntryId)

        if (currentTrace == null) {
            val previousEntry = ongoingEntry.previous?.let { previousEntryId ->
                entryService.findEntry(previousEntryId)
            }
            val previousEndTime = previousEntry?.end?.time

            if (previousEndTime != null && previousEndTime.isAfter(traceItem.timestamp)) {
                // AIS point provided happened before we finished our entry
                // This can happen when the event processing is ahead of the AIS processing
                val previousTrace = traceCacheService.getTraceDirectly(previousEntry._id)

                if (previousTrace == null) {
                    // Ignoring AIS point because we don't have a trace for the previous entry to add it to
                    return false
                }

                // Update the previous trace
                onUpdateTrace(
                    newTraceItems = listOf(traceItem),
                    locations = locations,
                    currentTrace = previousTrace
                )
                return true
            }

            // We don't have a trace for the current entry, so we create a new one
            onCreateNewTrace(
                traceItems = listOf(traceItem),
                locations = locations,
                currentEntryId = currentEntryId,
                previousEntryId = ongoingEntry.previous,
                previousEntryEndTime = previousEntry?.end?.time
            )
        } else {
            onUpdateTrace(
                newTraceItems = listOf(traceItem),
                locations = locations,
                currentTrace = currentTrace
            )
        }
        return true
    }

    private fun onCreateNewTrace(
        traceItems: List<TraceItem>,
        locations: List<Location>,
        currentEntryId: EntryId,
        previousEntryId: EntryId?,
        previousEntryEndTime: Instant?
    ) {
        val previousTrace = previousEntryId?.let { traceCacheService.getCurrentById(it) }
        val newTrace = computeNewTrace(
            traceItems = traceItems,
            locations = locations,
            currentEntryId = currentEntryId,
            previousEntryEndTime = previousEntryEndTime,
            previousTraceLastLocation = previousTrace?.polyline?.lastLocation,
            previousTraceLastSpeedOverGround = previousTrace?.speed?.lastSpeedOverGround,
            previousTraceLastDraught = previousTrace?.draught?.last
        )
        traceCacheService.onReplaceCurrentTrace(previousEntryId, newTrace)
        try {
            dataSource.insertOrUpdate(newTrace)
        } catch (ex: MongoWriteException) {
            // 11_000 is duplicate key exception
            if (ex.code == 11_000) {
                val id = newTrace._id
                // While we tried to insert we created a trace with the same ID in a different process, this means we can just fully replace it
                log.warn { "Trace with this ID already exists! ID: $id. Replacing it..." }
                dataSource.replace(newTrace)
            }
        }
    }

    private fun onUpdateTrace(
        newTraceItems: List<TraceItem>,
        locations: List<Location>,
        currentTrace: NewTrace
    ) {
        // Amend the new trace items to the Speed object
        val updatedSpeed = currentTrace.speed?.amendSpeedWithDuration(newTraceItems)
            // If there's no speed object, then up to now there was no suitable information to build a Speed object, try
            // it again. We have no previous speed over ground info here (otherwise it would have been available
            // before), so try to make it from the 'newTraceItems'
            ?: createSpeedOfTraceItemsOrNull(newTraceItems, speedOverGroundWhenMissingFirst = null)

        // Append distance, or create when the system has not enough data to do so before
        val updatedDistance = currentTrace.distance?.let { appendToTraceDistance(it, locations) }
            ?: createTraceDistanceFrom(locations)

        val currentDraught = currentTrace.draught
        val updatedDraught = if (currentDraught != null) {
            appendTraceStatisticFromTraceItems(currentDraught, newTraceItems, TraceItem::draught)
        } else {
            createTraceStatisticFromTraceItemsOrNull(newTraceItems, TraceItem::draught)
        }

        val newTotalOngoingTraceItems = currentTrace.totalOngoingTraceItems + locations.size

        val updatedTrace = if (newTotalOngoingTraceItems > MAX_NON_SIMPLIFIED_TRACE_ITEMS) {
            // Remove the last location as this will be the start of the new ongoing trace
            val appendingNewLocations = locations.toMutableList()
            val lastLocation = appendingNewLocations.removeLast()

            val currentPolylineLocations = PolylineUtils.decode(currentTrace.polyline.encoded)
            val locationsToSimplify = currentPolylineLocations + appendingNewLocations

            // Simplify the locations so the trace keeps easy to display in the frontend and doesn't hit the mongo storage limits
            val simplifiedLocations = simplifyService.simplifyTrace(locationsToSimplify)
            val currentSimplifiedTrace = currentTrace.simplifiedPolyline
            val newSimplifiedPolyline = currentSimplifiedTrace?.appendLocations(simplifiedLocations)
                // We never had a simplified trace, so we should create a new polyline
                ?: Polyline.of(simplifiedLocations)

            val newOngoingPolyline = Polyline.of(listOf(lastLocation))

            currentTrace.copy(
                totalOngoingTraceItems = 1,
                polyline = newOngoingPolyline,
                simplifiedPolyline = newSimplifiedPolyline,
                speed = updatedSpeed,
                distance = updatedDistance,
                draught = updatedDraught
            )
        } else {
            // Nothing to simplify, we can just update the ongoing polyline
            val updatedPolyline = currentTrace.polyline.appendLocations(locations = locations)

            currentTrace.copy(
                totalOngoingTraceItems = newTotalOngoingTraceItems,
                polyline = updatedPolyline,
                speed = updatedSpeed,
                distance = updatedDistance,
                draught = updatedDraught
            )
        }

        dataSource.replace(updatedTrace)
        traceCacheService.updateTrace(updatedTrace)
    }

    fun mergeCanceledVisitTraceWithResumedVoyage(
        imo: Int,
        canceledVisitId: EntryId,
        resumedVoyageId: EntryId
    ) {
        val canceledTrace = traceCacheService.getCurrentById(canceledVisitId)
        val resumedTrace = dataSource.findById(entryId = resumedVoyageId)

        // We don't have to merge anything if the canceled entry has no trace
        if (canceledTrace == null) {
            return
        }

        if (resumedTrace == null) {
            log.trace {
                "Resumed entry has no Trace, using canceled entry one (imo = $imo, resumed = " +
                    "$resumedVoyageId, canceled = $canceledVisitId)"
            }
            val newResumedEntryTrace = canceledTrace.copy(_id = resumedVoyageId)

            // We use an insert or update as it is possible that we already received some AIS while canceling our visit
            // This is possible as the event stream and AIS stream are not exactly aligned with each other
            dataSource.insertOrUpdate(newResumedEntryTrace)
            dataSource.deleteById(canceledTrace._id)
            traceCacheService.onReplaceCurrentTrace(canceledTrace._id, newResumedEntryTrace)
        } else {
            // Resuming trace is always older, so we append the canceled polyline at the end
            val mergedPolyline = resumedTrace.polyline.appendPolyline(canceledTrace.polyline)
            val mergedSpeed = mergeSpeeds(resumedTrace.speed, canceledTrace.speed)
            val mergedDraught = mergeAdjacentTraceStatistics(resumedTrace.draught, canceledTrace.draught)
            val updatedResumedTrace = resumedTrace.copy(
                polyline = mergedPolyline,
                speed = mergedSpeed,
                draught = mergedDraught
            )

            dataSource.replace(updatedResumedTrace)
            dataSource.deleteById(canceledTrace._id)
            traceCacheService.onReplaceCurrentTrace(canceledVisitId, updatedResumedTrace)
        }
    }

    fun deleteHistoricTraces(entryIds: List<EntryId>, imo: Int) {
        log.trace { "Delete historic traces for IMO $imo (entryIds: ${entryIds.joinToStringTruncated(10)})" }
        dataSource.deleteByEntryIds(entryIds)
    }
}
