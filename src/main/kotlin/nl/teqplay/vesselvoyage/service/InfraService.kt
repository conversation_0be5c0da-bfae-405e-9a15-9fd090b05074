package nl.teqplay.vesselvoyage.service

import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polygon
import nl.teqplay.skeleton.util.pointInPolygon
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.annotation.ProfileRevents
import nl.teqplay.vesselvoyage.model.SEARCH_PORTS_DEFAULT_LIMIT
import nl.teqplay.vesselvoyage.model.StopType
import nl.teqplay.vesselvoyage.model.internal.ClassifiedStop
import nl.teqplay.vesselvoyage.model.internal.InfraAreaType
import nl.teqplay.vesselvoyage.model.lightweight.poma.Anchorage
import nl.teqplay.vesselvoyage.model.lightweight.poma.Berth
import nl.teqplay.vesselvoyage.model.lightweight.poma.Lock
import nl.teqplay.vesselvoyage.model.lightweight.poma.PomaModel
import nl.teqplay.vesselvoyage.model.lightweight.poma.PomaPorts
import nl.teqplay.vesselvoyage.model.lightweight.poma.Port
import nl.teqplay.vesselvoyage.model.v2.NewStopType
import nl.teqplay.vesselvoyage.service.InfraCacheService.Companion.ANCHORAGE_SCALE
import nl.teqplay.vesselvoyage.service.InfraCacheService.Companion.BERTH_SCALE
import nl.teqplay.vesselvoyage.service.InfraCacheService.Companion.LOCK_SCALE
import nl.teqplay.vesselvoyage.util.haversineDistance
import nl.teqplay.vesselvoyage.util.toScaled
import nl.teqplay.vesselvoyage.util.toSkeletonLocation
import nl.teqplay.vesselvoyage.util.toVesselVoyageLocation
import org.springframework.stereotype.Service
import nl.teqplay.vesselvoyage.model.Location as OldLocation

/**
 * Service to get any Poma infra via the [InfraCacheService].
 */
@ProfileProcessing
@ProfileApi
@ProfileRevents
@OptIn(DelicateCoroutinesApi::class)
@Service
class InfraService(private val infraCacheService: InfraCacheService) {
    suspend fun onStartup() = infraCacheService.loadInitialMappings()

    fun refresh() {
        GlobalScope.launch(Dispatchers.Default) {
            infraCacheService.refreshAll()
        }
    }

    fun isPomaDataValid(): Boolean {
        return infraCacheService.portCache?.portsByUnlocode?.isNotEmpty() == true &&
            infraCacheService.anchorCache?.anchoragesByName?.isNotEmpty() == true &&
            infraCacheService.berthCache?.berthsByLocation?.isNotEmpty() == true &&
            infraCacheService.lockCache?.locksByLocation?.isNotEmpty() == true
    }

    fun getAllPorts(): List<Port> = infraCacheService.portCache?.getAll() ?: emptyList()

    fun addAllSubPorts(ports: Set<String>): Set<String> = ports
        .flatMap { port ->
            infraCacheService.portCache?.portsByMainPort?.get(port)?.mapNotNull { it.unlocode } ?: listOf()
        }
        .toSet() + ports

    fun getAllAnchorages(): List<Anchorage> = infraCacheService.anchorCache?.getAll() ?: emptyList()

    fun getAnchoragesByLocation(oldLocation: OldLocation): List<Anchorage> {
        return getAnchoragesInLocationGrid(oldLocation.toSkeletonLocation())
    }

    fun getAnchoragesInLocationGrid(location: Location): List<Anchorage> {
        return infraCacheService.anchorCache?.anchoragesByLocation?.get(location)?.toList() ?: emptyList()
    }

    fun getBerth(berthId: String): Berth? {
        return infraCacheService.berthCache?.get(berthId)
    }

    fun getBerthsByLocation(oldLocation: OldLocation): List<Berth> {
        return getBerthsInLocationGrid(oldLocation.toSkeletonLocation())
    }

    fun getBerthsInLocationGrid(location: Location): List<Berth> {
        return infraCacheService.berthCache?.berthsByLocation?.get(location)?.toList() ?: emptyList()
    }

    fun getLocksInLocationGrid(location: Location): List<Lock> {
        return infraCacheService.lockCache?.locksByLocation?.get(location)?.toList() ?: emptyList()
    }

    fun getPortByUnlocode(unlocode: String): Port? {
        val uppercasedUnlocode = unlocode.uppercase()
        return infraCacheService.portCache?.portsByUnlocode?.get(uppercasedUnlocode)
    }

    fun getMainPortFromBerth(berth: Berth): Port? {
        val berthMainPort = berth.mainPort

        // There is a main port provided in the berth object, fully rely on that
        if (berthMainPort != null) {
            // sometimes the filled in main port is an unlocode, but most of the time the id
            return if (berthMainPort.length == 5 && berthMainPort.all(Char::isUpperCase)) {
                getPortByUnlocode(unlocode = berthMainPort)
            } else {
                getById(id = berthMainPort, areaType = InfraAreaType.PORT) as? Port
            }
        }

        val berthPorts = berth.ports

        if (berthPorts.isNotEmpty()) {
            for (berthPort in berthPorts) {
                val port = if (berthPort.length == 5 && berthPort.all(Char::isUpperCase)) {
                    getPortByUnlocode(unlocode = berthPort)
                } else {
                    getById(id = berthPort, areaType = InfraAreaType.PORT) as? Port
                }

                // Return the port when we found something and it is a main port
                if (port != null && port.mainPort == null) {
                    return port
                }
            }
        }

        // We couldn't find any main ports
        return null
    }

    /**
     * Find the Poma [Port] from the provided area ID, which handles of removing the .eosp suffix.
     */
    fun getPortByAreaId(id: String): Port? {
        val idWithoutSuffix = id.removeSuffix(".eosp")
        val port = getById(idWithoutSuffix, InfraAreaType.PORT)

        return port as? Port
    }

    /**
     * Get the provided infra by [id], given the infra is part of the provided port [unlocode]
     */
    fun <T : PomaPorts> getInfraPartOfPort(id: String, unlocode: String, areaType: InfraAreaType): T? {
        val infra = getById(id, areaType) as? T

        if (infra != null) {
            return infra.takeIf { unlocode in infra.ports }
        }

        return null
    }

    fun getById(id: String, areaType: InfraAreaType): PomaModel? {
        return infraCacheService[areaType]?.get(id)
    }

    fun isPortMainPortById(id: String): Boolean {
        val port = (getById(id, InfraAreaType.PORT) as? Port) ?: return false

        return port.mainPort == null
    }

    fun isPortMainPort(unlocode: String): Boolean {
        val port = getPortByUnlocode(unlocode) ?: return false

        return port.mainPort == null
    }

    fun searchPorts(query: String, limit: Int?): List<Port> {
        val queryLowerCase = query.lowercase()

        val allPortsByUnlocode = infraCacheService.portCache?.portsByUnlocode?.values ?: emptyList()

        return allPortsByUnlocode
            .filter { port ->
                port.name.lowercase().contains(queryLowerCase) ||
                    (port.unlocode?.lowercase()?.startsWith(queryLowerCase) ?: false)
            }
            .sortedBy { it.name }
            .take(limit ?: SEARCH_PORTS_DEFAULT_LIMIT)
    }

    /**
     * Search anchorage by name and location
     * Since anchorage names are not always unique, we search the anchorage
     * with matching name closest to provided location
     */
    fun getAnchorage(name: String, location: OldLocation): Anchorage? {
        return infraCacheService.anchorCache?.anchoragesByName?.get(name.uppercase())
            ?.minByOrNull { haversineDistance(it.location.toVesselVoyageLocation(), location) }
    }

    /**
     * Use the provided Stop [location] to find a poma Berth or Anchorage to classify the Stop.
     *
     * @param location The location of the Stop.
     * @return A wrapper model containing a [StopType] and the poma id of the matching Berth or Anchorage.
     */
    fun findStopClassification(location: Location): ClassifiedStop {
        // Try to classify the stop
        val foundStopType = getStopClassificationInLocationGrid(
            location = location,
            scale = BERTH_SCALE,
            stopType = NewStopType.BERTH,
            getEntitiesInLocationGrid = ::getBerthsInLocationGrid
        ) ?: getStopClassificationInLocationGrid(
            location = location,
            scale = ANCHORAGE_SCALE,
            stopType = NewStopType.ANCHOR_AREA,
            getEntitiesInLocationGrid = ::getAnchoragesInLocationGrid
        ) ?: getStopClassificationInLocationGrid(
            location = location,
            scale = LOCK_SCALE,
            stopType = NewStopType.LOCK,
            getEntitiesInLocationGrid = ::getLocksInLocationGrid
        )

        if (foundStopType == null) {
            return ClassifiedStop(
                type = NewStopType.UNCLASSIFIED,
                areaId = null
            )
        }

        return foundStopType
    }

    /**
     * Get a [ClassifiedStop] from the provided [location].
     *
     * @param T The poma entity we want to get when classifying our stop.
     * @param location The location that needs to be in the area of our [T].
     * @param scale The lookup scale of finding our area. This scale has been decided by the scale defined [InfraCacheService]. For example, [BERTH_SCALE].
     * @param stopType When an entity is found, use the following stop type when classifying the location.
     * @param getEntitiesInLocationGrid Function to get all entities by the created location key.
     * @return The stop classified with the provided [stopType] and areaId from the poma entity.
     *  When null, the provided [location] was never inside any of the inner areas of our entities.
     */
    private fun <T : PomaModel> getStopClassificationInLocationGrid(
        location: Location,
        scale: Int,
        stopType: NewStopType,
        getEntitiesInLocationGrid: (Location) -> List<T>
    ): ClassifiedStop? {
        val locationKey = location.toScaled(scale)
        val entitiesInLocationGrid = getEntitiesInLocationGrid(locationKey)

        val entity = entitiesInLocationGrid.firstOrNull { entity ->
            val polygon = Polygon(entity.area)
            pointInPolygon(polygon, location)
        }

        return if (entity != null) {
            ClassifiedStop(
                type = stopType,
                areaId = entity._id
            )
        } else {
            null
        }
    }
}
