package nl.teqplay.vesselvoyage.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.eventhistory.client.EventHistoryClient
import nl.teqplay.skeleton.model.TimeWindow
import nl.teqplay.vesselvoyage.properties.EventFetchingProperties
import nl.teqplay.vesselvoyage.util.generateTimeWindows
import org.springframework.stereotype.Service
import java.time.Instant
import java.time.ZoneOffset
import java.time.ZonedDateTime

@Service
class EventFetchingService(
    private val properties: EventFetchingProperties,
    private val eventHistoryClient: EventHistoryClient
) {
    private val log = KotlinLogging.logger {}
    private val v2SupportedEvents = setOf(
        "AnchoredStartEvent",
        "AnchoredEndEvent",
        "TrueDestinationChangedEvent",
        "UniqueBerthStartEvent",
        "UniqueBerthEndEvent",
        "AreaStartEvent",
        "AreaEndEvent",
        "EncounterStartEvent",
        "EncounterEndEvent",
        "StopStartEvent",
        "StopEndEvent",
    )

    // semi-streaming solution: fetch in chunks, and stream the results
    fun fetchEventsByIMO(imo: String, start: ZonedDateTime, end: ZonedDateTime): Sequence<Event> {
        return fetchAisEngineEventsByIMO(imo.toInt(), start.toInstant(), end.toInstant())
    }

    fun fetchAisEngineEventsByIMO(
        imo: Int,
        start: Instant,
        end: Instant
    ): Sequence<Event> {
        return generateTimeWindows(
            startTime = start.atZone(ZoneOffset.UTC),
            endTime = end.atZone(ZoneOffset.UTC),
            stepSize = properties.chunkDuration
        ).map { fetchChunkOfAisEngineEventsByIMO(imo, it.start, it.end).asSequence() }
            .asSequence()
            .flatten()
    }

    private fun fetchChunkOfAisEngineEventsByIMO(
        imo: Int,
        start: ZonedDateTime,
        end: ZonedDateTime
    ): List<Event> {
        val timeWindow = TimeWindow(
            from = start.toInstant(),
            to = end.toInstant()
        )

        log.debug { "Fetching events for IMO $imo from AisEngine EventHistory" }
        val events = eventHistoryClient.findHistoryByImo(
            imos = listOf(imo),
            window = timeWindow,
            maxDays = properties.maxDays,
            includeEvents = v2SupportedEvents
        )
        log.info { "Fetched ${events.size} events from AisEngine EventHistory (imo: $imo)" }
        return events
    }
}
