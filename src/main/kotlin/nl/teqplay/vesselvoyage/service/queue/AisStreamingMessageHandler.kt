package nl.teqplay.vesselvoyage.service.queue

import io.github.oshai.kotlinlogging.KotlinLogging
import io.nats.client.Message
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.nats.NatsConsumerStream
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.model.internal.InFlightMessage
import nl.teqplay.vesselvoyage.model.internal.ProcessingItem
import nl.teqplay.vesselvoyage.model.internal.TraceItem
import nl.teqplay.vesselvoyage.properties.TraceProperties
import nl.teqplay.vesselvoyage.service.ImoLockService
import nl.teqplay.vesselvoyage.service.ProcessingShipStatusService
import nl.teqplay.vesselvoyage.service.StaticShipInfoService
import nl.teqplay.vesselvoyage.service.V1TraceService
import nl.teqplay.vesselvoyage.service.trace.ProcessingTraceService
import nl.teqplay.vesselvoyage.spring.ProcessingRefreshEvent
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.ApplicationContext
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import org.springframework.stereotype.Component
import java.time.Duration
import java.time.Instant
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Executors
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.system.measureTimeMillis
import kotlin.time.DurationUnit
import kotlin.time.toDuration
import nl.teqplay.vesselvoyage.model.LocationTime as OldLocationTime

private val log = KotlinLogging.logger {}

/**
 * Service to deserialize and dispatch messages with AIS data coming in over the event bus
 */
@ProfileProcessing
@ConditionalOnProperty("processing.enable-real-time", havingValue = "true")
@Component
class AisStreamingMessageHandler(
    private val traceProperties: TraceProperties,
    private val v1TraceService: V1TraceService,
    private val shipStatusService: ProcessingShipStatusService,
    private val processingTraceService: ProcessingTraceService,
    private val staticShipInfoService: StaticShipInfoService,
    private val imoLockService: ImoLockService,
    private val consumerStream: NatsConsumerStream<AisDiffMessage>?,
    private val applicationContext: ApplicationContext
) {
    @Volatile
    private var receivedLastAisMessageAt: Instant? = null
    private val aisConsumerThreadPool = ThreadPoolTaskExecutor().also { threadPool ->
        val threadPoolSize = traceProperties.totalThreads
        threadPool.corePoolSize = threadPoolSize
        threadPool.setThreadNamePrefix("ais-processing-")
        threadPool.initialize()
    }

    private val inFlightAisMessages = ConcurrentHashMap<Int, LinkedBlockingQueue<InFlightMessage<Pair<TraceItem, ZonedDateTime>, Message>>>()
    private val backgroundInFlightEventsProcessor = Executors.newFixedThreadPool(1)

    fun startup() {
        if (consumerStream == null) {
            log.warn { "ais-stream:history consumer not started, since it's not enabled." }
            return
        }

        log.info { "Starting AIS Consumer (V1 enabled = ${traceProperties.enableOldDefinition}, V2 enabled = ${traceProperties.enableNewDefinition})" }
        consumerStream.consume { wrapper, message ->
            try {
                receivedLastAisMessageAt = Instant.now()
                consumeMessage(natsMessage = message, data = wrapper)
            } catch (e: InterruptedException) {
                log.info(e) { "Got interrupted during consumption" }
            } catch (e: Exception) {
                log.error(e) { "Something went wrong during consumption" }
            }
        }
    }

    /**
     * Monitor the throughput of the AIS messages and refresh the consumer if no messages have been received.
     */
    @ConditionalOnProperty("trace.activeness-monitoring.enabled", havingValue = "true")
    @Scheduled(
        initialDelayString = "\${trace.activeness-monitoring.interval}",
        fixedDelayString = "\${trace.activeness-monitoring.interval}"
    )
    fun monitorThroughput() {
        if (receivedLastAisMessageAt == null) {
            log.warn { "No AIS messages have been received yet. Skipping throughput check." }
            return
        }

        val now = Instant.now()
        val timeBetweenLast = Duration.between(receivedLastAisMessageAt, now)
        val maxIdleTime = traceProperties.activenessMonitoring.maxIdleTime

        log.info { "Last AIS message received at ${receivedLastAisMessageAt?.truncatedTo(ChronoUnit.SECONDS)} (age = ${timeBetweenLast.toSeconds()} seconds, max = ${maxIdleTime.toSeconds()})" }
        if (timeBetweenLast > maxIdleTime) {
            log.warn { "No AIS message received in the allowed idle time. Refreshing ais-stream consumer." }
            refreshConsumer()
        }
    }

    private fun refreshConsumer() {
        try {
            applicationContext.publishEvent(ProcessingRefreshEvent(applicationContext))
            log.info { "Triggered ProcessingRefreshEvent to refresh ais-stream consumer." }
        } catch (e: Exception) {
            log.error(e) { "Failed to refresh ais-stream consumer." }
        }
    }

    fun shutdown() {
        log.info { "Stopping AIS Consumer" }
        consumerStream?.drain()
    }

    fun consumeMessage(natsMessage: Message, data: AisDiffMessage) {
        val processingMessage = ProcessingItem(
            ongoing = AtomicBoolean(true),
            natsMessage = natsMessage,
            data = data
        )

        aisConsumerThreadPool.submit {
            try {
                processMessageTask(message = processingMessage)
            } catch (ex: Exception) {
                log.error(ex) { "Something went wrong during processing of the AIS diff message, dropping AIS message" }
                natsMessage.ack()
            }
        }
    }

    private fun processMessageTask(message: ProcessingItem<AisDiffMessage>) {
        val aisDiffMessage = message.data
        val newLocation = aisDiffMessage.location.changed?.new

        // Ignore diff messages that don't have a location which is updated
        if (newLocation == null) {
            ignoredMessageResult(message.natsMessage)
                .onAcknowledgeMessage()
            return
        }

        val (mmsi, currentImo) = determineShipIdentifiersFromMessage(aisDiffMessage)

        // Skip this message if we couldn't find an imo attached to the given mmsi
        if (currentImo == null) {
            ignoredMessageResult(message.natsMessage)
                .onAcknowledgeMessage()
            return
        }

        val traceItem = getTraceItemFromMessage(message = aisDiffMessage, location = newLocation)

        try {
            imoLockService.lock(currentImo) { createdLock ->
                if (createdLock) {
                    nonBlockedAisProcessing(
                        natsMessage = message.natsMessage,
                        traceItem = traceItem,
                        mmsi = mmsi,
                        imo = currentImo
                    )
                } else {
                    blockedAisProcessing(
                        natsMessage = message.natsMessage,
                        traceItem = traceItem,
                        mmsi = mmsi,
                        imo = currentImo
                    )
                }
            }
        } catch (ex: Exception) {
            log.error(ex) { "Failed to process AIS message, dropping message" }
            message.natsMessage.ack()
        } finally {
            imoLockService.unlock(currentImo)
        }
    }

    private fun nonBlockedAisProcessing(
        natsMessage: Message,
        traceItem: TraceItem,
        mmsi: Int,
        imo: Int
    ) {
        val result = processMessage(
            time = traceItem.timestamp.atZone(ZoneOffset.UTC),
            message = natsMessage,
            traceItem = traceItem,
            mmsi = mmsi,
            currentImo = imo
        )

        // Call the NATS acknowledge after we canceled our inProgress check
        result.onAcknowledgeMessage()
    }

    private fun blockedAisProcessing(
        natsMessage: Message,
        traceItem: TraceItem,
        mmsi: Int,
        imo: Int
    ) {
        var isNewQueue = false
        val queue = synchronized(inFlightAisMessages) {
            val messageTime = traceItem.timestamp.atZone(ZoneOffset.UTC)
            val inFlightAisMessage = InFlightMessage(
                message = traceItem to messageTime,
                natsMessage = natsMessage
            )

            inFlightAisMessages.computeIfAbsent(imo) {
                // We don't have a queue for this IMO, create a new one
                isNewQueue = true
                LinkedBlockingQueue()
            }.also { queue ->
                log.debug { "AIS processing blocked for $imo putting on queue (new = $isNewQueue)" }
                // Add the event to a queue, so we can process it when unblocked
                queue.put(inFlightAisMessage)
            }
        }

        // If the queue is new, start a background task to process the in flight when unblocked
        if (isNewQueue) {
            backgroundInFlightEventsProcessor.execute {
                synchronized(inFlightAisMessages) {
                    imoLockService.executeBlocking(imo) {
                        log.debug { "Processing backlog of ${queue.size} AIS for $imo" }
                        while (queue.isNotEmpty()) {
                            val aisToProcess = queue.take()
                            val (messageTraceItem, messageTime) = aisToProcess.message

                            val result = processMessage(
                                time = messageTime,
                                message = aisToProcess.natsMessage,
                                traceItem = messageTraceItem,
                                mmsi = mmsi,
                                currentImo = imo
                            )

                            // Call the NATS acknowledge after we canceled our inProgress check
                            result.onAcknowledgeMessage()
                        }

                        log.trace { "Done processing backlog now ${queue.size} AIS are left for $imo" }
                        inFlightAisMessages.remove(imo)
                    }
                }
            }
        }
    }

    data class ProcessingResult(
        val onAcknowledgeMessage: () -> Unit
    )

    /**
     * Process a single AIS message
     */
    private fun processMessage(
        time: ZonedDateTime,
        message: Message,
        traceItem: TraceItem,
        mmsi: Int,
        currentImo: Int
    ): ProcessingResult {
        try {
            if (traceProperties.enableOldDefinition) {
                val processingDurationInMillis = measureTimeMillis {
                    val oldLocationTime = OldLocationTime(
                        latitude = traceItem.location.lat,
                        longitude = traceItem.location.lon,
                        time = time
                    )

                    v1TraceService.insertLocation(
                        imo = currentImo.toString(),
                        mmsi = mmsi.toString(),
                        location = oldLocationTime
                    )
                }

                val processingDuration = processingDurationInMillis
                    .toDuration(DurationUnit.MILLISECONDS)

                log.debug { "[V1] Processing AIS took ${processingDuration.inWholeSeconds} seconds (imo = $currentImo)" }
            }
        } catch (ex: Exception) {
            log.error(ex) { "Could not process V1 trace" }
        }

        try {
            if (traceProperties.enableNewDefinition) {
                val processingDurationInMillis = measureTimeMillis {
                    val ongoingEntry = shipStatusService.getLatestOngoingEntry(imo = currentImo)
                    processingTraceService.insertIntoCurrentTrace(
                        ongoingEntry = ongoingEntry,
                        traceItem = traceItem
                    )
                }

                val processingDuration = processingDurationInMillis
                    .toDuration(DurationUnit.MILLISECONDS)

                log.debug { "[V2] Processing AIS took ${processingDuration.inWholeSeconds} seconds (imo = $currentImo)" }
            }
        } catch (ex: Exception) {
            log.error(ex) { "Could not process V2 trace" }
        }

        return ProcessingResult(onAcknowledgeMessage = { message.ack() })
    }

    private fun ignoredMessageResult(message: Message): ProcessingResult {
        return ProcessingResult(onAcknowledgeMessage = { message.term() })
    }

    private fun determineShipIdentifiersFromMessage(message: AisDiffMessage): Pair<Int, Int?> {
        val mmsi = message.mmsi

        val imo = staticShipInfoService.getImoFromMmsi(mmsi.toString())
            ?.toIntOrNull()

        return mmsi to imo
    }

    private fun getTraceItemFromMessage(message: AisDiffMessage, location: Location): TraceItem {
        val speedOverGround = message.speedOverGround.latest()
        val draught = message.draught.latest()

        return TraceItem(
            location = location,
            speedOverGround = speedOverGround,
            draught = draught,
            timestamp = message.messageTime // when it was sent from the AIS transponder
        )
    }
}
