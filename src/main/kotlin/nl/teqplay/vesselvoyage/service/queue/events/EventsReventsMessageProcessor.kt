package nl.teqplay.vesselvoyage.service.queue.events

import io.github.oshai.kotlinlogging.KotlinLogging
import jakarta.annotation.PostConstruct
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.aisengine.revents.publishPoisonPill
import nl.teqplay.skeleton.nats.NatsProducerStream
import nl.teqplay.vesselvoyage.annotation.ProfileRevents
import nl.teqplay.vesselvoyage.model.v2.NewChange
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewShipStatus
import nl.teqplay.vesselvoyage.service.processing.EventProcessingService
import org.springframework.stereotype.Component
import java.time.Duration
import java.time.Instant
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicLong
import kotlin.concurrent.thread

@ProfileRevents
@Component
class EventsReventsMessageProcessor(
    private val eventProcessingService: EventProcessingService,
    private val producer: NatsProducerStream<NewChange<*>>
) : EventsMessageProcessor {
    private val log = KotlinLogging.logger {}

    private val statuses = mutableMapOf<Int, NewShipStatus>()

    /**
     * Whether the poison pill has been received or not.
     */
    @Volatile
    private var poisonPillReceived = false

    private val events = LinkedBlockingQueue<ChangeWrapper>(100_000)

    data class ChangeWrapper(
        val imo: Int,
        val change: NewChange<*>
    )

    override fun processMessage(event: Event, message: EventStreamService.MessageContext) {
        val imo = event.ship.imo

        // Event imo should not be null
        if (imo == null) {
            message.term()
            return
        }

        processEvent(event, imo)
        message.ack()
    }

    override fun processEvent(event: Event, imo: Int) {
        val processTime = Instant.now()

        val status = statuses.getOrPut(imo) { NewInitialShipStatus() }
        val result = eventProcessingService.onEventAndBuffer(status, event, processTime)
        statuses[imo] = result.status

        result.changes.forEach { change ->
            events.put(ChangeWrapper(imo, change))
        }
    }

    override fun onPoisonPill() {
        poisonPillReceived = true
    }

    @PostConstruct
    fun init() {
        thread(start = true, name = "publish", block = ::publish)
    }

    private fun publish() {
        val queue = LinkedBlockingQueue<ChangeWrapper>(1000)
        val pending = AtomicLong()
        val published = AtomicLong()

        while (!poisonPillReceived || events.isNotEmpty()) {
            val maxTimeout = Duration.ofSeconds(5).toMillis()
            val startTime = System.currentTimeMillis()
            while (queue.remainingCapacity() > 0) {
                val timeout = startTime + maxTimeout - System.currentTimeMillis()
                val message = events.poll(timeout, TimeUnit.MILLISECONDS) ?: break

                queue.put(message)
            }

            while (queue.isNotEmpty()) {
                try {
                    producer.publishAsync(
                        timeout = null,
                        queue = queue,
                        pending = pending,
                        published = published,
                        subject = { wrapper, _ -> "vesselvoyage.change.${wrapper.imo}" },
                        convert = { it.change }
                    )
                } catch (e: Exception) {
                    // log error
                    log.error(e) { "Error publishing message" }
                }
            }
        }

        // publish poison pill to indicate this component has finished processing/publishing events
        publishPoisonPill(
            subject = "vesselvoyage.change.poison",
            properties = producer.getConfig(),
            stream = producer
        )
    }
}
