package nl.teqplay.vesselvoyage.service.queue.events

import io.github.oshai.kotlinlogging.KotlinLogging
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.skeleton.metrics.Metric
import nl.teqplay.skeleton.metrics.MetricRegistry
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.datasource.ShipBlacklistDatasource
import nl.teqplay.vesselvoyage.logic.EventTypeValidator
import nl.teqplay.vesselvoyage.model.internal.InFlightMessage
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.EntryProcessingService
import nl.teqplay.vesselvoyage.service.ImoLockService
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import org.springframework.stereotype.Component
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Executors
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.atomic.AtomicLong

private val log = KotlinLogging.logger {}

@ProfileProcessing
@Component
class EventsDefaultMessageProcessor(
    private val processingProperties: EventProcessingProperties,
    private val entryProcessingService: EntryProcessingService,
    private val imoLockService: ImoLockService,
    private val shipBlacklistDatasource: ShipBlacklistDatasource,
    meterRegistry: MeterRegistry
) : EventsMessageProcessor {
    companion object {
        private const val TAG_INPUT_TYPE_KEY = "input_type"
        private const val TAG_INPUT_TYPE_VALUE_EVENTS = "events"
        private const val TAG_INPUT_TYPE_VALUE_OLD_CHANGES = "old-changes"
        private const val TAG_INPUT_TYPE_VALUE_CHANGES = "changes"
    }

    private val metricRegistry = MetricRegistry(this::class, meterRegistry, listOf(TAG_INPUT_TYPE_KEY))

    val eventInputCount = metricRegistry.createGauge(Metric.MESSAGE_COUNT_INPUT, AtomicLong(0), TAG_INPUT_TYPE_VALUE_EVENTS)
    val eventProcessedCount = metricRegistry.createGauge(Metric.MESSAGE_COUNT_PROCESSED, AtomicLong(0), TAG_INPUT_TYPE_VALUE_EVENTS)
    val eventDroppedCount = metricRegistry.createGauge(Metric.MESSAGE_COUNT_DROPPED, AtomicLong(0), TAG_INPUT_TYPE_VALUE_EVENTS)
    val changesOutputCount = metricRegistry.createGauge(Metric.MESSAGE_COUNT_OUTPUT, AtomicLong(0), TAG_INPUT_TYPE_VALUE_CHANGES)

    @Deprecated("Remove once V1 support has been removed")
    val oldChangesOutputCount = metricRegistry.createGauge(Metric.MESSAGE_COUNT_OUTPUT, AtomicLong(0), TAG_INPUT_TYPE_VALUE_OLD_CHANGES)

    private val eventConsumerThreadPool = ThreadPoolTaskExecutor().also { threadPool ->
        val threadPoolSize = processingProperties.totalThreads
        threadPool.corePoolSize = threadPoolSize
        threadPool.setThreadNamePrefix("event-processing-")
        threadPool.initialize()
    }

    private val inFlightEvents = ConcurrentHashMap<Int, LinkedBlockingQueue<InFlightMessage<Event, EventStreamService.MessageContext>>>()
    private val backgroundInFlightEventsProcessor = Executors.newFixedThreadPool(1)
    private val blacklistedShips = shipBlacklistDatasource.getAll()

    override fun processMessage(event: Event, message: EventStreamService.MessageContext) {
        val shipImo = event.ship.imo

        if (shipImo == null || shipImo in blacklistedShips) {
            message.term()
            return
        }

        eventConsumerThreadPool.submit {
            try {
                imoLockService.lock(shipImo) { createdLock ->
                    if (createdLock) {
                        nonBlockedEventProcessing(
                            event = event,
                            message = message,
                            imo = shipImo
                        )
                    } else {
                        // we are blocked by revents
                        blockedEventProcessing(
                            imo = shipImo,
                            event = event,
                            message = message
                        )
                    }
                }
            } catch (ex: Exception) {
                log.error(ex) { "Failed to process event, dropping message $event" }
                message.ack()
            } finally {
                imoLockService.unlock(shipImo)
            }
        }
    }

    private fun nonBlockedEventProcessing(
        event: Event,
        message: EventStreamService.MessageContext,
        imo: Int
    ) {
        processEvent(event, imo)
        message.ack()
    }

    private fun blockedEventProcessing(
        imo: Int,
        event: Event,
        message: EventStreamService.MessageContext,
    ) {
        var isNewQueue = false
        val queue = synchronized(inFlightEvents) {
            val inFlightEvent = InFlightMessage(
                message = event,
                natsMessage = message
            )

            inFlightEvents.computeIfAbsent(imo) {
                // We don't have a queue for this IMO, create a new one
                isNewQueue = true
                LinkedBlockingQueue()
            }.also { queue ->
                log.debug { "Event processing blocked for $imo putting on queue (new = $isNewQueue)" }
                // Add the event to a queue, so we can process it when unblocked
                queue.put(inFlightEvent)
            }
        }

        // If the queue is new, start a background task to process the in flight when unblocked
        if (isNewQueue) {
            backgroundInFlightEventsProcessor.execute {
                synchronized(inFlightEvents) {
                    imoLockService.executeBlocking(imo) {
                        log.debug { "Processing backlog of ${queue.size} events for $imo" }
                        while (queue.isNotEmpty()) {
                            val eventToProcess = queue.take()
                            processEvent(eventToProcess.message, imo)
                            eventToProcess.natsMessage.ack()
                        }

                        log.trace { "Done processing backlog now ${queue.size} events are left for $imo" }
                        inFlightEvents.remove(imo)
                    }
                }
            }
        }
    }

    override fun processEvent(event: Event, imo: Int) {
        eventInputCount.incrementAndGet()

        if (!EventTypeValidator.isValid(event)) {
            log.trace { "Invalid event type received: ${event.getEventType()}. Dropping event" }
            eventDroppedCount.incrementAndGet()

            return
        }

        // V1 processing
        if (processingProperties.enableOldDefinition) {
            try {
                val changes = entryProcessingService.insert(event, imo = imo.toString(), logIssues = false)
                oldChangesOutputCount.addAndGet(changes.size.toLong())
            } catch (e: Exception) {
                // Ignore any exception if we couldn't process the old definition
                log.error(e) { "Error processing old definition for event $event" }
            }
        }

        // V2 processing
        if (processingProperties.enableNewDefinition) {
            val newChanges = entryProcessingService.insertNew(event, imo)
            changesOutputCount.addAndGet(newChanges.size.toLong())
        }

        eventProcessedCount.incrementAndGet()
    }
}
