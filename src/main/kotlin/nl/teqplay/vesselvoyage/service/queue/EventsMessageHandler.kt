package nl.teqplay.vesselvoyage.service.queue

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.aisengine.nats.stream.revents.model.ReventsEventStreamOptions
import nl.teqplay.skeleton.nats.NatsConsumerStream
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.annotation.ProfileRevents
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.queue.events.EventsMessageProcessor
import nl.teqplay.vesselvoyage.spring.ProcessingRefreshEvent
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.ApplicationContext
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit

private val log = KotlinLogging.logger {}

/**
 * Service to deserialize and dispatch messages with events coming in over the event bus
 */
@ProfileProcessing
@ProfileRevents
@ConditionalOnProperty("processing.enable-real-time", havingValue = "true")
@Component
class EventsMessageHandler(
    private val processingProperties: EventProcessingProperties,
    private val consumer: NatsConsumerStream<Event>?,
    private val eventStreamService: EventStreamService?,
    private val eventsMessageProcessor: EventsMessageProcessor,
    private val applicationContext: ApplicationContext
) {
    @Volatile
    private var receivedLastEventAt: Instant? = null

    @Volatile
    private var running = false

    fun startup() {
        log.info { "Starting Event Consumer (running = $running, V1 = ${processingProperties.enableOldDefinition}, V2 = ${processingProperties.enableNewDefinition})" }

        if (!running) {
            running = true
            startConsuming()
        }
    }

    fun shutdown() {
        log.info { "Stopping Event Consumer (running = $running)" }
        if (running) {
            consumer?.drain()
            running = false
        }
    }

    private fun startConsuming() {
        if (consumer == null || eventStreamService == null) {
            log.warn { "event-stream consumer not started, since it's not enabled." }
            return
        }
        try {
            val consumerName = consumer.natsClient.config.username
            log.info { "Starting event stream consumer (user = $consumerName)" }
            eventStreamService.consume(
                stream = consumer,
                revents = ReventsEventStreamOptions(
                    requireBatchSort = true,
                    onPoisonPill = eventsMessageProcessor::onPoisonPill
                ),
                handler = { event, messageContext ->
                    receivedLastEventAt = Instant.now()
                    eventsMessageProcessor.processMessage(event, messageContext)
                }
            )
            log.info { "Started event stream consumer (user = $consumerName)" }
        } catch (e: Exception) {
            log.error(e) { "Something went wrong during consumption" }
        }
    }

    @ConditionalOnProperty("event-processing.activeness-monitoring.enabled", havingValue = "true")
    @Scheduled(
        initialDelayString = "\${event-processing.activeness-monitoring.interval}",
        fixedDelayString = "\${event-processing.activeness-monitoring.interval}"
    )
    fun monitorThroughput() {
        if (receivedLastEventAt == null) {
            log.warn { "No events have been received yet. Skipping throughput check." }
            return
        }

        val now = Instant.now()
        val timeBetweenLast = Duration.between(receivedLastEventAt, now)
        val maxIdleTime = processingProperties.activenessMonitoring.maxIdleTime

        log.info { "Last event received at ${receivedLastEventAt?.truncatedTo(ChronoUnit.SECONDS)} (age = ${timeBetweenLast.toSeconds()} seconds, max = ${maxIdleTime.toSeconds()})" }
        if (timeBetweenLast > maxIdleTime) {
            log.warn { "No events received in the allowed idle time. Refreshing event-stream consumer." }
            refreshConsumer()
        }
    }

    private fun refreshConsumer() {
        try {
            applicationContext.publishEvent(ProcessingRefreshEvent(applicationContext))
            log.info { "Triggered ProcessingRefreshEvent to refresh event-stream consumer." }
        } catch (e: Exception) {
            log.error(e) { "Failed to refresh event-stream consumer." }
        }
    }
}
