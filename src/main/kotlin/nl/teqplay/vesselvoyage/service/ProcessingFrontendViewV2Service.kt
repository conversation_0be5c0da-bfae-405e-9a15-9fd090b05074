package nl.teqplay.vesselvoyage.service

import io.github.oshai.kotlinlogging.KotlinLogging
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.controller.processing.ProcessingFrontendViewV2Controller.PortsPageRequest
import nl.teqplay.vesselvoyage.controller.processing.ProcessingFrontendViewV2Controller.PortsResponse
import nl.teqplay.vesselvoyage.mapper.EntryV2Mapper
import nl.teqplay.vesselvoyage.model.ShipDetails
import nl.teqplay.vesselvoyage.model.internal.MinimalNewVisit
import nl.teqplay.vesselvoyage.model.internal.MinimalNewVoyage
import nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import nl.teqplay.vesselvoyage.service.api.VisitV2Service
import nl.teqplay.vesselvoyage.service.api.VoyageV2Service
import org.springframework.stereotype.Service

@ProfileProcessing
@Service
class ProcessingFrontendViewV2Service(
    private val visitService: VisitV2Service,
    private val voyageService: VoyageV2Service,
    private val shipInfoService: StaticShipInfoService,
    private val mapper: EntryV2Mapper
) {
    private val log = KotlinLogging.logger {}

    fun createPortsPageResponse(request: PortsPageRequest): PortsResponse = runBlocking(Dispatchers.Default) {
        val possibleImos = shipInfoService.getImosByCategories(request.categories)

        // Get all data in parallel as querying the data can take quite some time
        // We request the current visits first as that is most of the time the biggest collection
        val currentlyVisitingRequest = async { getShipsCurrentlyVisitingPort(request.unlocode, possibleImos) }
        val sailingTowardsRequest = async { getShipsSailingTowardsPort(request.unlocode, possibleImos) }
        val justLeftRequest = async { getRecentlyLeftShipFromPort(request.unlocode, possibleImos) }

        log.debug { "Awaiting response of all" }
        val sailingTowards = sailingTowardsRequest.await()
        val justLeft = justLeftRequest.await()
        val currentlyVisiting = currentlyVisitingRequest.await()
        log.debug { "Done getting data, building up frontend response" }

        val imos = buildSet {
            addAll(sailingTowards.map { it.imo })
            addAll(justLeft.map { it.imo })
            addAll(currentlyVisiting.map { it.imo })
        }

        // only include entries where the imo resolves to a ship
        val shipsByImo = mutableMapOf<Int, ShipDetails>()
        imos.forEach { imo ->
            val ship = shipInfoService.getShipDetailsByIMO(imo.toString())
            if (ship != null) {
                shipsByImo[imo] = ship
            }
        }

        PortsResponse(
            sailingTowards = sailingTowards.map(mapper::toApi),
            currentlyVisiting = currentlyVisiting.map(mapper::toApi),
            justLeft = justLeft.map(mapper::toApi),
            shipByImo = shipsByImo
        )
    }

    private fun getShipsSailingTowardsPort(unlocode: String, possibleImos: Set<Int>): List<NewVoyage> {
        log.debug { "Getting all current voyages going towards $unlocode" }
        val start = System.currentTimeMillis()
        val result = voyageService.findByAisDestinationAndImos(
            aisDestinationUnlocode = unlocode,
            imos = possibleImos,
            finished = NewEntryFinishedFilter.ONGOING
        )

        log.debug { "Finished getting all current voyages going towards $unlocode (${System.currentTimeMillis() - start}ms)" }
        return result.convertToFullVoyages()
    }

    private fun getRecentlyLeftShipFromPort(unlocode: String, possibleImos: Set<Int>): List<NewVoyage> {
        log.debug { "Getting all voyages that just left $unlocode" }
        val start = System.currentTimeMillis()
        val result = voyageService.findByOriginPortUnlocodeAndImos(
            originPortUnlocode = unlocode,
            imos = possibleImos,
            finished = NewEntryFinishedFilter.ONGOING
        )

        log.debug { "Finished getting all voyages that just left $unlocode (${System.currentTimeMillis() - start}ms)" }
        return result.convertToFullVoyages()
    }

    private fun getShipsCurrentlyVisitingPort(unlocode: String, possibleImos: Set<Int>): List<NewVisit> {
        log.debug { "Getting all ships visiting $unlocode" }
        val start = System.currentTimeMillis()
        val result = visitService.findByPortUnlocodeAndImos(
            imos = possibleImos,
            unlocode = unlocode,
            finishedState = NewEntryFinishedFilter.ONGOING
        )

        log.debug { "Finished getting all ships visiting $unlocode (${System.currentTimeMillis() - start}ms)" }
        return result.convertToFullVisits()
    }

    /**
     * Convert the light-weight [MinimalNewVisit] to a [NewVisit] with all the fields we don't use in the frontend
     *  to a default value.
     */
    private fun List<MinimalNewVisit>.convertToFullVisits(): List<NewVisit> {
        return this.map { minimalVisit ->
            NewVisit(
                _id = minimalVisit._id,
                imo = minimalVisit.imo,
                start = minimalVisit.start,
                end = null,
                stops = emptyList(),
                eospAreaActivity = minimalVisit.eospAreaActivity,
                portAreaActivities = minimalVisit.portAreaActivities.toMutableList(),
                destination = null,
                previous = null,
                next = null
            )
        }
    }

    /**
     * Convert the light-weight [MinimalNewVoyage] to a [NewVoyage] with all the fields we don't use in the frontend
     *  to a default value.
     */
    private fun List<MinimalNewVoyage>.convertToFullVoyages(): List<NewVoyage> {
        return this.map { minimalVisit ->
            NewVoyage(
                _id = minimalVisit._id,
                imo = minimalVisit.imo,
                start = minimalVisit.start,
                end = null,
                stops = emptyList(),
                destination = minimalVisit.destination,
                previous = minimalVisit.previous,
                next = null,
                actualStart = null,
                originPort = minimalVisit.originPort,
                destinationPort = minimalVisit.destinationPort
            )
        }
    }
}
