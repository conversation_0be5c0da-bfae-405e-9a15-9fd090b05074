package nl.teqplay.vesselvoyage.service

import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.datasource.NewESoFDataSource
import nl.teqplay.vesselvoyage.datasource.NewShipStatusStateDataSource
import nl.teqplay.vesselvoyage.datasource.NewVisitDataSource
import nl.teqplay.vesselvoyage.datasource.NewVoyageDataSource
import nl.teqplay.vesselvoyage.datasource.VisitDataSource
import nl.teqplay.vesselvoyage.datasource.VoyageDataSource
import nl.teqplay.vesselvoyage.model.Entry
import nl.teqplay.vesselvoyage.model.ShipStatus
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewShipStatus
import org.springframework.stereotype.Service

@ProfileApi
@Service
class ApiShipStatusService(
    visitDataSource: VisitDataSource,
    voyageDataSource: VoyageDataSource,
    newVisitDataSource: NewVisitDataSource,
    newVoyageDataSource: NewVoyageDataSource,
    newESoFDataSource: NewESoFDataSource,
    newShipStatusStateDataSource: NewShipStatusStateDataSource
) : ShipStatusService(
    visitDataSource,
    voyageDataSource,
    newVisitDataSource,
    newVoyageDataSource,
    newESoFDataSource,
    newShipStatusStateDataSource
) {
    @Deprecated("V1")
    override operator fun get(imo: String): ShipStatus {
        return getCurrentStatus(imo)
    }

    override fun getStatus(imo: Int): NewShipStatus {
        return getCurrentNewStatus(imo)
    }

    @Deprecated("V1")
    override fun getEntry(imo: String): Entry? {
        val status = get(imo)

        return when (status) {
            is VisitShipStatus -> status.visit
            is VoyageShipStatus -> status.voyage
            else -> null
        }
    }

    @Deprecated("V1")
    override operator fun set(imo: String, status: ShipStatus) {
        // We don't mutate the ship status in the api layer
    }

    @Deprecated("V1")
    override fun remove(imo: String) {
        // We don't mutate the ship status in the api layer
    }

    override fun removeStatus(imo: Int) {
        // We don't mutate the ship status in the api layer
    }
}
