package nl.teqplay.vesselvoyage.service

import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.model.internal.LimitedShip
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.service.api.VisitV2Service
import nl.teqplay.vesselvoyage.service.api.VoyageV2Service
import nl.teqplay.vesselvoyage.service.processing.EventProcessor.Companion.MAX_ALLOWED_ACTIVITIES
import org.springframework.stereotype.Service

@ProfileProcessing
@Service
class LimitedShipService(
    private val visitService: VisitV2Service,
    private val voyageService: VoyageV2Service,
    private val staticShipInfoService: StaticShipInfoService
) {
    fun getLimitedShipsByVoyages(page: Int, categories: Set<ShipCategoryV2>): List<LimitedShip> {
        val imos = staticShipInfoService.getImosByCategories(categories)
        val limitedVoyages = voyageService.findByLimited(limited = true, imos = imos, page = page, pageSize = 100)
        return getLimitedShips(limitedVoyages)
    }

    fun getLimitedShipsByVisits(page: Int, categories: Set<ShipCategoryV2>): List<LimitedShip> {
        val imos = staticShipInfoService.getImosByCategories(categories)
        val limitedVisits = visitService.findByLimited(limited = true, imos = imos, page = page, pageSize = 100)
        return getLimitedShips(limitedVisits)
    }

    private fun getLimitedShips(limitedEntries: List<NewEntry>): List<LimitedShip> {
        return limitedEntries.map { limitedEntry ->
            val imo = limitedEntry.imo
            val shipDetails = staticShipInfoService.getShipDetailsByIMO(imo.toString())

            LimitedShip(
                id = limitedEntry._id,
                imo = imo,
                name = shipDetails?.name,
                category = shipDetails?.categories?.v2,
                reason = determineLimitedReason(limitedEntry)
            )
        }
    }

    private fun <T : NewEntry> determineLimitedReason(entry: T): String? {
        with(entry) {
            return getActivityLimitedReason(stops, "stops")
                ?: getActivityLimitedReason(passThroughEosp, "pass-through EOSPs")
                ?: getActivityLimitedReason(passThroughPort, "pass-through ports")
                ?: if (this is NewVisit) {
                    getActivityLimitedReason(otherOngoingEospAreaActivities, "other ongoingEOSPs")
                        ?: getActivityLimitedReason(portAreaActivities, "ports")
                        ?: getActivityLimitedReason(anchorAreaActivities, "anchorages")
                        ?: getActivityLimitedReason(berthAreaActivities, "berths")
                        ?: getActivityLimitedReason(pilotAreaActivities, "pilot areas")
                        ?: getActivityLimitedReason(anchorAreaAreaActivities, "anchor areas")
                        ?: getActivityLimitedReason(terminalMooringAreaActivities, "terminal mooring areas")
                        ?: getActivityLimitedReason(lockAreaActivities, "lock areas")
                        ?: getActivityLimitedReason(approachAreaActivities, "approach areas")
                } else {
                    // Reason to limit is missing
                    null
                }
        }
    }

    private fun getActivityLimitedReason(activities: List<*>, name: String): String? {
        if (activities.size >= MAX_ALLOWED_ACTIVITIES) {
            return "Reached at least $MAX_ALLOWED_ACTIVITIES $name"
        }

        // Activity was fine
        return null
    }

    fun countLimitedShipsByVisits(categories: Set<ShipCategoryV2>): Long {
        val imos = staticShipInfoService.getImosByCategories(categories)
        return visitService.countByLimited(limited = true, imos = imos)
    }

    fun countLimitedShipsByVoyages(categories: Set<ShipCategoryV2>): Long {
        val imos = staticShipInfoService.getImosByCategories(categories)
        return voyageService.countByLimited(limited = true, imos = imos)
    }
}
