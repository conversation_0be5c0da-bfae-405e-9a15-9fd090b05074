package nl.teqplay.vesselvoyage.service

import com.fasterxml.jackson.core.type.TypeReference
import io.github.oshai.kotlinlogging.KotlinLogging
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.runBlocking
import nl.teqplay.skeleton.model.Location
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.annotation.ProfileRevents
import nl.teqplay.vesselvoyage.client.poma.PomaClient
import nl.teqplay.vesselvoyage.datasource.TempFileDataSource
import nl.teqplay.vesselvoyage.model.internal.InfraAreaType
import nl.teqplay.vesselvoyage.model.lightweight.poma.Anchorage
import nl.teqplay.vesselvoyage.model.lightweight.poma.ApproachArea
import nl.teqplay.vesselvoyage.model.lightweight.poma.Berth
import nl.teqplay.vesselvoyage.model.lightweight.poma.Lock
import nl.teqplay.vesselvoyage.model.lightweight.poma.PilotBoardingPlace
import nl.teqplay.vesselvoyage.model.lightweight.poma.PomaModel
import nl.teqplay.vesselvoyage.model.lightweight.poma.Port
import nl.teqplay.vesselvoyage.model.lightweight.poma.ShipToShipArea
import nl.teqplay.vesselvoyage.model.lightweight.poma.Terminal
import nl.teqplay.vesselvoyage.properties.DiskCacheProperties
import nl.teqplay.vesselvoyage.util.DiskCache
import nl.teqplay.vesselvoyage.util.toScaled
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.time.ZonedDateTime
import java.util.concurrent.ConcurrentHashMap
import kotlin.system.measureTimeMillis

private val log = KotlinLogging.logger {}

object PortListRef : TypeReference<List<Port>>()
object AnchorageListRef : TypeReference<List<Anchorage>>()
object BerthListRef : TypeReference<List<Berth>>()
object PilotBoardingPlaceListRef : TypeReference<List<PilotBoardingPlace>>()
object TerminalListRef : TypeReference<List<Terminal>>()
object LockListRef : TypeReference<List<Lock>>()
object ApproachAreaListRef : TypeReference<List<ApproachArea>>()
object ShipToShipListRef : TypeReference<List<ShipToShipArea>>()

/**
 * Service maintaining all Poma infra related caches
 */
@ProfileProcessing
@ProfileApi
@ProfileRevents
@Service
class InfraCacheService(
    private val diskCacheProperties: DiskCacheProperties,
    private val tempFileDataSource: TempFileDataSource,
    private val pomaClient: PomaClient,
    private val slackMessageService: SlackMessageService?
) {
    companion object {
        const val ANCHORAGE_SCALE = 0
        const val BERTH_SCALE = 1
        const val LOCK_SCALE = 1
    }

    private val infraCacheByAreaType = ConcurrentHashMap<InfraAreaType, InfraCache<*>>()

    operator fun get(areaType: InfraAreaType): InfraCache<*>? {
        return infraCacheByAreaType[areaType]
    }

    val portCache: PortInfraCache?
        get() = infraCacheByAreaType[InfraAreaType.PORT] as? PortInfraCache

    val anchorCache: AnchorInfraCache?
        get() = infraCacheByAreaType[InfraAreaType.ANCHOR] as? AnchorInfraCache

    val berthCache: BerthInfraCache?
        get() = infraCacheByAreaType[InfraAreaType.BERTH] as? BerthInfraCache

    val lockCache: LockInfraCache?
        get() = infraCacheByAreaType[InfraAreaType.LOCK] as? LockInfraCache

    suspend fun loadInitialMappings() {
        log.info { "Initializing empty infra caches" }
        val allCaches = listOf(
            PortInfraCache(diskCacheProperties, tempFileDataSource, pomaClient),
            AnchorInfraCache(diskCacheProperties, tempFileDataSource, pomaClient),
            BerthInfraCache(diskCacheProperties, tempFileDataSource, pomaClient),
            PilotBoardingPlaceInfraCache(diskCacheProperties, tempFileDataSource, pomaClient),
            TerminalInfraCache(diskCacheProperties, tempFileDataSource, pomaClient),
            LockInfraCache(diskCacheProperties, tempFileDataSource, pomaClient),
            ApproachAreaInfraCache(diskCacheProperties, tempFileDataSource, pomaClient),
            ShipToShipInfraCache(diskCacheProperties, tempFileDataSource, pomaClient),
        )

        coroutineScope {
            val cacheLoadJobs = allCaches.map { cache ->
                async { cache.onInitialLoadCache() }
            }

            cacheLoadJobs.awaitAll()
        }

        allCaches.forEach { cache ->
            // Put all created caches on the map
            infraCacheByAreaType[cache.areaType] = cache
        }
    }

    @Scheduled(cron = "\${infra.refresh.cron}", zone = "UTC")
    fun onCronInfraRefresh() = runBlocking {
        val startTime = ZonedDateTime.now()
        try {
            log.info { "Start refreshing infra" }
            refreshAll()
            log.info { "Finished refreshing infra" }
        } catch (e: Throwable) {
            val failTime = ZonedDateTime.now()
            log.error(e) { "Error refreshing infra: ${e.message}" }
            slackMessageService?.sendMessage(
                title = "Scheduled task failed: InfraCacheService.onCronInfraRefresh",
                text = "Error refreshing infra: ${e.message}\nStarted at: $startTime\nFailed at: $failTime",
                barColor = SLACK_COLOR_RED
            )
        }
    }

    suspend fun refreshAll() = coroutineScope {
        val refreshJobs = infraCacheByAreaType.map { (_, cache) ->
            async { cache.onRefreshCache() }
        }

        refreshJobs.awaitAll()
    }

    class PortInfraCache(
        diskCacheProperties: DiskCacheProperties,
        tempFileDataSource: TempFileDataSource,
        private val pomaClient: PomaClient
    ) : InfraCache<Port>(
        areaType = InfraAreaType.PORT,
        diskCacheEnabled = diskCacheProperties.enabled,
        tempFileDataSource = tempFileDataSource,
        listRef = PortListRef,
        getPomaEntries = pomaClient::getAllPorts
    ) {
        var portsByUnlocode: Map<String, Port> = emptyMap()
            private set
        var portsByMainPort: Map<String, List<Port>> = emptyMap()
            private set

        override fun onUpdateMapping(entities: List<Port>) {
            itemsById = entities
                .filter { it.unlocode != null && it._id != null }
                .associateBy { it._id!! }
            portsByUnlocode = entities
                .filter { it.unlocode != null }
                .associateBy { it.unlocode!! }
            portsByMainPort = entities
                .filter { it.mainPort != null }
                .groupBy { it.mainPort!! }
        }
    }

    class AnchorInfraCache(
        diskCacheProperties: DiskCacheProperties,
        tempFileDataSource: TempFileDataSource,
        private val pomaClient: PomaClient
    ) : InfraCache<Anchorage>(
        areaType = InfraAreaType.ANCHOR,
        diskCacheEnabled = diskCacheProperties.enabled,
        tempFileDataSource = tempFileDataSource,
        listRef = AnchorageListRef,
        getPomaEntries = pomaClient::getAllAnchorages
    ) {
        var anchoragesByName: Map<String, List<Anchorage>> = emptyMap()
            private set
        var anchoragesByLocation: Map<Location, Set<Anchorage>> = emptyMap()
            private set

        override fun onUpdateMapping(entities: List<Anchorage>) {
            super.onUpdateMapping(entities)

            anchoragesByName = entities
                .groupBy { it.name.uppercase() }

            anchoragesByLocation = groupAreaBasedPomaEntites(desiredScale = ANCHORAGE_SCALE, entities) { anchorage ->
                anchorage.area + anchorage.location
            }
        }
    }

    class BerthInfraCache(
        diskCacheProperties: DiskCacheProperties,
        tempFileDataSource: TempFileDataSource,
        private val pomaClient: PomaClient
    ) : InfraCache<Berth>(
        areaType = InfraAreaType.BERTH,
        diskCacheEnabled = diskCacheProperties.enabled,
        tempFileDataSource = tempFileDataSource,
        listRef = BerthListRef,
        getPomaEntries = pomaClient::getAllBerths
    ) {
        var berthsByLocation: Map<Location, Set<Berth>> = emptyMap()
            private set

        override fun onUpdateMapping(entities: List<Berth>) {
            super.onUpdateMapping(entities)

            berthsByLocation = groupAreaBasedPomaEntites(desiredScale = BERTH_SCALE, entities) { berth ->
                berth.area + berth.location
            }
        }
    }

    class PilotBoardingPlaceInfraCache(
        diskCacheProperties: DiskCacheProperties,
        tempFileDataSource: TempFileDataSource,
        private val pomaClient: PomaClient
    ) : InfraCache<PilotBoardingPlace>(
        areaType = InfraAreaType.PILOT_BOARDING_PLACE,
        diskCacheEnabled = diskCacheProperties.enabled,
        tempFileDataSource = tempFileDataSource,
        listRef = PilotBoardingPlaceListRef,
        getPomaEntries = pomaClient::getAllPilotBoardingPlaces
    )

    class TerminalInfraCache(
        diskCacheProperties: DiskCacheProperties,
        tempFileDataSource: TempFileDataSource,
        private val pomaClient: PomaClient
    ) : InfraCache<Terminal>(
        areaType = InfraAreaType.TERMINAL,
        diskCacheEnabled = diskCacheProperties.enabled,
        tempFileDataSource = tempFileDataSource,
        listRef = TerminalListRef,
        getPomaEntries = pomaClient::getAllTerminals
    )

    class LockInfraCache(
        diskCacheProperties: DiskCacheProperties,
        tempFileDataSource: TempFileDataSource,
        private val pomaClient: PomaClient
    ) : InfraCache<Lock>(
        areaType = InfraAreaType.LOCK,
        diskCacheEnabled = diskCacheProperties.enabled,
        tempFileDataSource = tempFileDataSource,
        listRef = LockListRef,
        getPomaEntries = pomaClient::getAllLocks
    ) {
        var locksByLocation: Map<Location, Set<Lock>> = emptyMap()
            private set

        override fun onUpdateMapping(entities: List<Lock>) {
            super.onUpdateMapping(entities)

            locksByLocation = groupAreaBasedPomaEntites(desiredScale = LOCK_SCALE, entities) { lock ->
                lock.area + lock.location
            }
        }
    }

    class ApproachAreaInfraCache(
        diskCacheProperties: DiskCacheProperties,
        tempFileDataSource: TempFileDataSource,
        private val pomaClient: PomaClient
    ) : InfraCache<ApproachArea>(
        areaType = InfraAreaType.APPROACH_AREA,
        diskCacheEnabled = diskCacheProperties.enabled,
        tempFileDataSource = tempFileDataSource,
        listRef = ApproachAreaListRef,
        getPomaEntries = pomaClient::getAllApproachAreas
    )

    class ShipToShipInfraCache(
        diskCacheProperties: DiskCacheProperties,
        tempFileDataSource: TempFileDataSource,
        private val pomaClient: PomaClient
    ) : InfraCache<ShipToShipArea>(
        areaType = InfraAreaType.SHIP_TO_SHIP,
        diskCacheEnabled = diskCacheProperties.enabled,
        tempFileDataSource = tempFileDataSource,
        listRef = ShipToShipListRef,
        getPomaEntries = pomaClient::getAllShipToShipAreas
    )

    abstract class InfraCache<T : PomaModel>(
        val areaType: InfraAreaType,
        val diskCacheEnabled: Boolean,
        tempFileDataSource: TempFileDataSource,
        listRef: TypeReference<List<T>>,
        val getPomaEntries: () -> List<T>
    ) {
        private val diskCache = DiskCache(
            enabled = diskCacheEnabled,
            filename = "vesselvoyage-infra-${areaType.name.lowercase()}.json",
            fetch = getPomaEntries,
            tempFileDataSource = tempFileDataSource,
            typeReference = listRef
        )

        protected var itemsById: Map<String, T> = emptyMap()

        operator fun get(id: String): T? = itemsById[id]
        fun getAll(): List<T> = itemsById.values.toList()

        fun onInitialLoadCache() {
            log.info { "Loading $areaType cache (disk cache = $diskCacheEnabled)" }

            try {
                val duration = measureTimeMillis {
                    diskCache.load(::onUpdateMapping)
                }
                log.info { "Finished loading $areaType cache ($duration ms)" }
            } catch (err: Throwable) {
                log.error(err) { "Failed to load in $areaType cache." }
            }
        }

        fun onRefreshCache() {
            try {
                val duration = measureTimeMillis {
                    log.info { "Refreshing Poma cache (area type = $areaType)" }
                    val entities = diskCache.refresh()
                    onUpdateMapping(entities)
                    log.info { "Finished updating ${entities.size} Poma $areaType entities" }
                }
                log.info { "Finished refreshing Poma $areaType entities ($duration ms)" }
            } catch (err: Throwable) {
                log.error(err) { "Failed to refresh $areaType from Poma" }
            }
        }

        open fun onUpdateMapping(entities: List<T>) {
            itemsById = entities.filter { it._id != null }
                .associateBy { it._id!! }
        }
    }
}

/**
 * NOTE: Keep in mind that this only groups the Entities based on the points of the area and location.
 * This means that if let's say we have a scale of 0, with a box that has 10.0 and 12.0 the location won't be put in the 11.0 bucket.
 */
fun <T> groupAreaBasedPomaEntites(desiredScale: Int, allEntities: List<T>, getLocations: (T) -> List<Location>): Map<Location, Set<T>> {
    val preparedByLocation = mutableMapOf<Location, MutableSet<T>>()

    allEntities.forEach { entity ->
        val locationKeys = getLocations(entity).map { it.toScaled(desiredScale) }.toSet()

        locationKeys.forEach { location ->
            preparedByLocation.getOrPut(location) { mutableSetOf() }
                .add(entity)
        }
    }

    return preparedByLocation
}
