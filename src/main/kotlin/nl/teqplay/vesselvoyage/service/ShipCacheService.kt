package nl.teqplay.vesselvoyage.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.csi.model.ship.info.ShipRegisterInfoCache
import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2
import nl.teqplay.csi.model.ship.mapping.ShipRegisterMapping
import nl.teqplay.vesselvoyage.model.internal.ShipCache
import nl.teqplay.vesselvoyage.util.toConcurrentHashMap
import org.springframework.stereotype.Service
import java.util.concurrent.ConcurrentHashMap

@Service
class ShipCacheService {
    private val log = KotlinLogging.logger {}

    /**
     * In-memory cache map containing the [ShipCache] objects
     */
    private val shipCacheImo = ConcurrentHashMap<String, ShipCache>()

    /**
     * In-memory cache map containing the [ShipCache] objects for ships that have no IMO but do have an MMSI
     */
    private val shipCacheMmsi = ConcurrentHashMap<String, ShipCache>()

    /**
     * In-memory cache map containing the pairs of MMSI and IMOs having MMSI as the key
     */
    private var shipImoByMmsi = ConcurrentHashMap<String, String>()

    /**
     * In-memory cache map containing sets of ship IMOs grouped by [ShipCategoryV2]
     */
    private var shipImosByCategory = ConcurrentHashMap<ShipCategoryV2, Set<String>>()

    fun isShipCacheValid(): Boolean {
        return shipCacheImo.isNotEmpty() && shipImoByMmsi.isNotEmpty() && shipImosByCategory.isNotEmpty()
    }

    /**
     * @return All the ship IMOs known by the [shipCacheImo]
     */
    fun getAllImos() = shipCacheImo.keys

    /**
     * Find a ship cache based on a given IMO number
     *
     * @param imo the IMO number of the vessel
     * @return The in-memory [ShipCache] or null if not found
     */
    fun getCacheByImo(imo: String): ShipCache? {
        return shipCacheImo[imo]
    }

    /**
     * Find a ship cache based on a given MMSI number
     *
     * @param mmsi the MMSI number of the vessel
     * @return The in-memory [ShipCache] or null if not found
     */
    fun getCachyByMmsi(mmsi: String): ShipCache? {
        return shipCacheMmsi[mmsi]
    }

    /**
     * @return An immutable version of the [shipCacheImo] map
     */
    fun getFullCache(): Map<String, ShipCache> {
        return shipCacheImo
    }

    /**
     * Find the IMO of a vessel based on the provided MMSI number
     *
     * @return The IMO of a ship or null when not found
     */
    fun getImoByMmsi(mmsi: String): String? {
        return shipImoByMmsi[mmsi]
    }

    /**
     * Find the MMSI currently used of a vessel based on the provided IMO number
     *
     * @return The MMSI of a ship or null when not found
     */
    fun getCurrentMmsiByImo(imo: String): String? {
        return getCacheByImo(imo)?.csi?.register?.identifiers?.mmsi
    }

    /**
     * Find all IMO numbers based on the provided [ShipCategoryV2]
     *
     * @return a set of IMO numbers or an empty set if the category wasn't found
     */
    fun getShipImosByCategory(category: ShipCategoryV2): Set<String> {
        return shipImosByCategory[category] ?: emptySet()
    }

    /**
     * Update the CSI cache given a list of [csiShipRegister].
     *
     * @return The total amount of objects added and updated while updating the CSI cache
     */
    fun updateCsiCache(csiShipRegister: List<ShipRegisterInfoCache>): Int {
        if (csiShipRegister.isEmpty()) {
            log.warn { "Could not update CSI ship cache because the provided list is empty" }
            return 0
        }

        return loadCsiCache(csiShipRegister)
    }

    /**
     * Populate the [ShipCache] with the information from the provided [ShipRegisterInfoCache] from CSI
     *
     * @return The total number of objects added and updated while loading the CSI cache
     */
    fun loadCsiCache(csiShipRegister: List<ShipRegisterInfoCache>): Int {
        log.info { "Loading in CSI ship info into ShipCache" }

        shipImoByMmsi = csiShipRegister
            .filter { info -> info.identifiers.mmsi != null && info.identifiers.imo != null }
            .associate { info -> info.identifiers.mmsi!! to info.identifiers.imo!! }
            .toConcurrentHashMap()

        shipImosByCategory = csiShipRegister
            .filter { it.categories.v2 != null && it.identifiers.imo != null }
            .groupBy({ requireNotNull(it.categories.v2) }, { requireNotNull(it.identifiers.imo) })
            .mapValues { it.value.toSet() }
            .toConcurrentHashMap()

        val (totalAdded, totalUpdated) = createOrUpdateShipCache(csiShipRegister) { shipRegister ->
            val imo = shipRegister.identifiers.imo

            if (imo != null) {
                updateShipCache(
                    key = imo,
                    onCreate = { ShipCache(identifier = imo, csi = shipRegister.createCsiShipCache()) },
                    onUpdate = { it.copy(csi = shipRegister.createCsiShipCache()) },
                    cache = shipCacheImo
                )
            } else {
                // If no IMO is present, add the ship to the MMSI cache
                val mmsi = shipRegister.identifiers.mmsi ?: return@createOrUpdateShipCache null

                updateShipCache(
                    key = mmsi,
                    onCreate = { ShipCache(identifier = mmsi, csi = shipRegister.createCsiShipCache()) },
                    onUpdate = { it.copy(csi = shipRegister.createCsiShipCache()) },
                    cache = shipCacheMmsi
                )
            }
        }

        log.info { "Completed loading in CSI ship info into ShipCache (added = $totalAdded, updated = $totalUpdated)" }
        return totalAdded + totalUpdated
    }

    fun updateCsiImoMmsiMappingCache(csiMappings: List<ShipRegisterMapping>) {
        if (csiMappings.isEmpty()) {
            log.warn { "Could not update CSI imo mmsi mapping because the provided list is empty" }
            return
        }

        loadCsiImoMmsiMappingCache(csiMappings)
    }

    /**
     * Populate the [ShipCache] with the known IMO-MMSI mapping provided by CSI
     */
    fun loadCsiImoMmsiMappingCache(csiMappings: List<ShipRegisterMapping>) {
        log.info { "Loading in CSI imo mmsi mapping into ShipCache" }

        val (totalAdded, totalUpdated) = createOrUpdateShipCache(csiMappings) { mapping ->
            updateShipCache(
                key = mapping.imo,
                onCreate = { ShipCache(identifier = mapping.imo, csiImoMmsiCache = mapping.createCsiImoMmsiCache()) },
                onUpdate = { it.copy(csiImoMmsiCache = mapping.createCsiImoMmsiCache()) },
                cache = shipCacheImo
            )
        }

        log.info { "Completed loading in CSI imo mmsi mapping into ShipCache (added = $totalAdded, updated = $totalUpdated)" }
    }

    /**
     * Change the state of the [ShipCache] given a list of [items]
     *
     * @param items The list of items that need to be processed
     * @param onProcess What to do when processing one item
     * @return The total amount of items that have been added and updated in the cache
     */
    private fun <T> createOrUpdateShipCache(items: List<T>, onProcess: (T) -> Boolean?): Pair<Int, Int> {
        var totalCreated = 0
        var totalUpdated = 0

        for (item in items) {
            when (onProcess(item)) {
                true -> totalUpdated += 1
                false -> totalCreated += 1
                else -> continue
            }
        }

        return Pair(totalCreated, totalUpdated)
    }

    /**
     * Change the state of the [ShipCache] using the IMO number of a vessel as its key
     *
     * @param key The IMO/MMSI number of the vessel
     * @param onCreate How to create the ShipCache when it doesn't exist yet
     * @param onUpdate How to update the ShipCache when it was found in the in-memory [shipCacheImo]
     * @return true when a [ShipCache] was updated or false when a new entry was added
     */
    private fun updateShipCache(
        key: String,
        onCreate: () -> ShipCache,
        onUpdate: (ShipCache) -> ShipCache,
        cache: ConcurrentHashMap<String, ShipCache>
    ): Boolean {
        var isUpdate = true

        cache.compute(key) { _, cache ->
            if (cache != null) {
                onUpdate(cache)
            } else {
                isUpdate = false
                onCreate()
            }
        }

        return isUpdate
    }

    private fun ShipRegisterInfoCache.createCsiShipCache(): ShipCache.CsiShipCache {
        return ShipCache.CsiShipCache(
            register = this
        )
    }

    private fun ShipRegisterMapping.createCsiImoMmsiCache(): ShipCache.CsiImoMmsiCache {
        return ShipCache.CsiImoMmsiCache(
            mapping = this.mapping
        )
    }
}
