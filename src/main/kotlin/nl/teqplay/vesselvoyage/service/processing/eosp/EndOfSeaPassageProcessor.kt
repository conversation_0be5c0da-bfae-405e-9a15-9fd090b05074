package nl.teqplay.vesselvoyage.service.processing.eosp

import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.annotation.ProfileRevents
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.processing.StartEndEventProcessor
import org.springframework.stereotype.Service

@ProfileProcessing
@ProfileRevents
@Service
class EndOfSeaPassageProcessor(
    config: EventProcessingProperties,
    infraService: InfraService
) : StartEndEventProcessor<AreaEvent, EndOfSeaPassageStartProcessor, EndOfSeaPassageEndProcessor>(
    startProcessor = EndOfSeaPassageStartProcessor(config, infraService),
    endProcessor = EndOfSeaPassageEndProcessor(config, infraService)
)
