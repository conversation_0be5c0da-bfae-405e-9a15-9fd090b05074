package nl.teqplay.vesselvoyage.service.processing.terminalmooring

import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.NewEventProcessingResult
import nl.teqplay.vesselvoyage.model.v2.NewStop
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.VisitChange
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.util.updateCurrentVisit
import java.time.Instant

class TerminalMooringAreaEndProcessor(config: EventProcessingProperties) : TerminalMooringAreaBaseProcessor(config) {
    override fun getResultOnVisitAtEndEvent(
        status: NewVisitShipStatus,
        event: AreaEvent,
        areaId: String,
        updateTime: Instant
    ): NewEventProcessingResult {
        val currentVisit = status.visit.entry
        val matchingOngoingAreaActivity = getFirstMatchingOngoingAreaActivity(currentVisit, areaId)

        // We can only handle the event when we have a matching ongoing area activity
        if (matchingOngoingAreaActivity == null) {
            return ignoredEventResult(
                status = status,
                event = event,
                description = "Currently known area activities don't match the area id or are already finished. (area id: $areaId)"
            )
        }

        val matchingStartTime = matchingOngoingAreaActivity.start.time
        val stoppedInsideTerminalMooring = currentVisit.stops
            // Reverse the list of stops, as one of the last stops should be inside our terminal mooring area
            .reversed()
            .any { stop -> didStopHappenDuringTerminalMooring(stop, matchingStartTime, event.actualTime) }

        // We didn't stop inside the terminal, meaning we just passed it
        val updatedVisit = if (!stoppedInsideTerminalMooring) {
            // We can just drop the area activity
            val remainingTerminalMooring = currentVisit.terminalMooringAreaActivities - matchingOngoingAreaActivity
            currentVisit.copy(
                terminalMooringAreaActivities = remainingTerminalMooring.toMutableList(),
                updatedAt = updateTime
            )
        } else {
            // We did stop inside the terminal, set the end time as we normally would
            updateEndOfMatchingActivity(event, currentVisit, matchingOngoingAreaActivity)
                .copy(updatedAt = updateTime)
        }

        return NewEventProcessingResult(
            status = status.updateCurrentVisit(updatedVisit),
            changes = listOf(
                VisitChange(Action.UPDATE, updatedVisit)
            )
        )
    }

    /**
     * Check if the [stop] happened inside our terminal mooring. It should match one of the following criteria to do so:
     * 1. Stopped inside the terminal mooring time.
     * 2. Stopped before terminal mooring and ended during terminal mooring.
     * 3. Stopped inside terminal mooring and ended after terminal mooring.
     * 4. Stopped before and ended after terminal mooring.
     *
     * @param stop The stop we want to check if it happened inside the terminal mooring.
     * @param terminalMooringStart The time we entered the terminal mooring area.
     * @param terminalMooringEnd The time we left the terminal mooring area.
     */
    private fun didStopHappenDuringTerminalMooring(stop: NewStop, terminalMooringStart: Instant, terminalMooringEnd: Instant): Boolean {
        val stopStart = stop.start.time
        val stopEnd = stop.end?.time

        return stopStart <= terminalMooringEnd && (stopEnd == null || stopEnd >= terminalMooringStart)
    }
}
