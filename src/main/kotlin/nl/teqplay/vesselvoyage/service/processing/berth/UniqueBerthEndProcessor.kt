package nl.teqplay.vesselvoyage.service.processing.berth

import nl.teqplay.aisengine.event.interfaces.UniqueBerthEvent
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.Change
import nl.teqplay.vesselvoyage.model.EventProcessingResult
import nl.teqplay.vesselvoyage.model.NewEventProcessingResult
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.AisFetchingService
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.util.joinToStringTruncated
import nl.teqplay.vesselvoyage.util.updateMatchingBerthAreaVisit
import java.time.Instant

class UniqueBerthEndProcessor(
    config: EventProcessingProperties,
    infraService: InfraService,
    aisFetchingService: AisFetchingService
) : UniqueBerthBaseProcessor(config, infraService, aisFetchingService) {
    companion object {
        const val END_EVENT_IGNORED_ISSUE = "Unique berth events end are only supported when a visit is ongoing."
        const val MISSING_BERTHID = "Unique berth event is missing berthId"
        const val MISSING_UNLOCODE = "Unique berth event is missing berthId"
    }

    override fun getResultOnVisit(status: VisitShipStatus, event: UniqueBerthEvent, config: EventProcessingProperties, imo: Int): EventProcessingResult {
        val berthId = event.area.id ?: return ignoredEventResult(status, event, MISSING_BERTHID)
        val unlocode = event.area.unlocode ?: return ignoredEventResult(status, event, MISSING_UNLOCODE)

        val currentVisit = status.visit
        val updatedVisit = currentVisit.copy(
            esof = updateEsof(currentVisit, event, config, berthId),
            berthAreas = updateMatchingBerthAreaVisit(currentVisit.berthAreas, event, unlocode, berthId)
        )

        if (updatedVisit.berthAreas == status.visit.berthAreas) {
            return ignoredEventResult(
                status, event,
                buildString {
                    append("Unique berth end event does not match any berth area visit of the current visit. Ignoring event ")
                    append("(imo: $imo, ")
                    append("open berth areas: ${status.visit.berthAreas.joinToStringTruncated(10) { it.berthId }}, ")
                    append("event berthId: $berthId)")
                }
            )
        }

        return EventProcessingResult(
            status = VisitShipStatus(updatedVisit, status.previousVoyage, status.previousVisit),
            changes = listOf(
                Change(Action.UPDATE, updatedVisit)
            )
        )
    }

    override fun getResultOnVisit(
        status: NewVisitShipStatus,
        event: UniqueBerthEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return getResultOnVisit(status, event, updateTime, imo)
    }

    override fun getResultOnVoyage(
        status: NewVoyageShipStatus,
        event: UniqueBerthEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return getResultForPreviousVisitOnVoyage(status, event, updateTime)
    }

    override fun getResultOnInitial(
        status: NewInitialShipStatus,
        event: UniqueBerthEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return ignoredEventResult(status, event, END_EVENT_IGNORED_ISSUE)
    }
}
