package nl.teqplay.vesselvoyage.service.processing.encounter

import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.aisengine.event.interfaces.EndEvent
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.NewEventProcessingResult
import nl.teqplay.vesselvoyage.model.v2.ESoFChange
import nl.teqplay.vesselvoyage.model.v2.EntryESoFWrapper
import nl.teqplay.vesselvoyage.model.v2.FallbackType
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewESoF
import nl.teqplay.vesselvoyage.model.v2.NewEncounter
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.processing.ESofEventProcessor
import nl.teqplay.vesselvoyage.service.processing.EventUtil.relatedEvent
import nl.teqplay.vesselvoyage.util.createEmptyEsof
import nl.teqplay.vesselvoyage.util.getShipImo
import nl.teqplay.vesselvoyage.util.replaceFirst
import java.time.Instant

abstract class EncounterBaseProcessor : ESofEventProcessor<EncounterEvent>() {
    companion object {
        const val UNSUPPORTED_STATUS = "Encounter events are only allowed when in Visit or Voyage status"
    }

    override fun isValidImo(status: NewShipStatus, event: EncounterEvent, imo: Int): EventProcessingIssue? {
        val statusImo = status.getShipImo()
        val eventImo = imo
        val eventOtherImo = event.otherShip.imo

        if (statusImo != null && statusImo != eventImo && statusImo != eventOtherImo) {
            return EventProcessingIssue(
                eventId = event._id,
                description = "Event has mismatching IMO: ship status has IMO $statusImo, event has IMO $imo. " +
                    "New event will be ignored (eventId: ${event._id})"
            )
        }

        return null
    }

    /**
     * Update the [NewESoF], creating a new encounter on a start event or updating the matching encounter on an end event.
     */
    abstract fun NewESoF.updateWithEncounterEvent(
        event: EncounterEvent,
        serviceVesselMmsi: Int,
        serviceVesselImo: Int?
    ): NewESoF

    /**
     * Validate the provided [event] to see if it is possible to add the [currentEncounters].
     */
    abstract fun validateEventWithCurrentEncounters(
        currentEncounters: List<NewEncounter>,
        event: EncounterEvent
    ): EventProcessingIssue?

    private fun getCurrentEsof(wrapper: EntryESoFWrapper<*>, updateTime: Instant): Pair<NewESoF, Boolean> {
        val currentEsof = wrapper.esof
            // Return an empty esof if we didn't have one before
            ?: return createEmptyEsof(wrapper.entry, updateTime) to true

        return currentEsof to false
    }

    private fun <T : NewEntry, U : NewShipStatus> getESoFChange(
        wrapper: EntryESoFWrapper<T>,
        event: EncounterEvent,
        status: U,
        updateTime: Instant,
        imo: Int,
        onUpdateStatus: (updatedEsof: NewESoF) -> U,
    ): NewEventProcessingResult {
        val (currentEsof, isNewEsof) = getCurrentEsof(wrapper, updateTime)

        // Check if the provided event makes sense with the currently known encounters
        val issue = validateEventWithCurrentEncounters(currentEsof.encounters, event)

        if (issue != null) {
            // We only set fallback on the visit so check the previous visit only on voyage status
            if (status is NewVoyageShipStatus && event is EndEvent) {
                val previousVisit = status.previousVisit

                if (previousVisit != null) {
                    val (previousVisitEsof, isPreviousVisitEsofNew) = getCurrentEsof(previousVisit, updateTime)

                    if (!isPreviousVisitEsofNew) {
                        // Replace relevant matching encounter that used a fallback for its end
                        val updatedEncounters = try {
                            previousVisitEsof.encounters.replaceFirst({ encounter ->
                                encounter.end?.fallback == FallbackType.ACTIVITY_END_BY_EOSP && encounter.startEventId == event.relatedEvent()
                            }) { encounter ->
                                val endLocationTime = LocationTime(
                                    location = event.location,
                                    time = event.actualTime
                                )

                                encounter.copy(
                                    end = endLocationTime
                                )
                            }
                        } catch (_: IndexOutOfBoundsException) {
                            // We couldn't find any matching encounter that had a fallback so instead return the issue we found before
                            return NewEventProcessingResult(
                                status = status,
                                changes = emptyList(),
                                issues = listOf(issue)
                            )
                        }

                        val updatedPreviousVisitEsof = previousVisitEsof.copy(encounters = updatedEncounters)
                        val updatedPreviousVisitStatus = status.copy(
                            previousVisit = previousVisit.copy(esof = updatedPreviousVisitEsof)
                        )

                        return NewEventProcessingResult(
                            status = updatedPreviousVisitStatus,
                            changes = listOf(
                                ESoFChange(
                                    action = Action.UPDATE,
                                    value = updatedPreviousVisitEsof
                                )
                            )
                        )
                    }
                }
            }

            return NewEventProcessingResult(
                status = status,
                changes = emptyList(),
                issues = listOf(issue)
            )
        }

        val (serviceVesselMmsi, serviceVesselImo) = if (status.getShipImo() == imo) {
            event.otherShip.mmsi to event.otherShip.imo
        } else {
            event.ship.mmsi to imo
        }
        val updatedEsof = currentEsof.updateWithEncounterEvent(event, serviceVesselMmsi, serviceVesselImo)
            .copy(updatedAt = updateTime)
        val updatedStatus = onUpdateStatus(updatedEsof)

        // Depending on if this is a new esof we create or otherwise updating the existing esof
        val changeAction = if (isNewEsof) {
            Action.CREATE
        } else {
            Action.UPDATE
        }

        return NewEventProcessingResult(
            status = updatedStatus,
            changes = listOf(
                ESoFChange(
                    action = changeAction,
                    value = updatedEsof
                )
            )
        )
    }

    override fun getResultOnVisit(
        status: NewVisitShipStatus,
        event: EncounterEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return getESoFChange(status.visit, event, status, updateTime, imo) { updatedEsof ->
            status.copy(status.visit.copy(esof = updatedEsof))
        }
    }

    override fun getResultOnVoyage(
        status: NewVoyageShipStatus,
        event: EncounterEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return getESoFChange(status.voyage, event, status, updateTime, imo) { updatedEsof ->
            status.copy(status.voyage.copy(esof = updatedEsof))
        }
    }

    override fun getResultOnInitial(
        status: NewInitialShipStatus,
        event: EncounterEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return ignoredEventResult(
            status = status,
            event = event,
            description = UNSUPPORTED_STATUS
        )
    }
}
