package nl.teqplay.vesselvoyage.service.processing.stop.merging

import nl.teqplay.vesselvoyage.model.v2.NewStop
import nl.teqplay.vesselvoyage.model.v2.NewStopType
import org.springframework.stereotype.Component
import java.time.Duration

@Component
class AnchorStopMergingTactic : StopMergingTactic {
    companion object {
        /**
         * Max distance in meters we merge anchorages together.
         */
        private const val MERGING_ANCHOR_MAX_DISTANCE = 1000

        /**
         * On more aggressive merging we have to take bigger distances as it can happen that ships drift for quite a bit when anchoring.
         */
        private const val MERGING_ANCHOR_MAX_DISTANCE_AGGRESSIVE = 2000

        /**
         * Max time we allow between stops to take them into account for merging.
         */
        private val MERGING_ANCHOR_MAX_TIME = Duration.ofHours(2)

        /**
         * When we can do more aggressive merging, use a bigger duration for merging anchorages.
         */
        private val MERGING_ANCHOR_MAX_TIME_AGGRESSIVE = Duration.ofHours(8)

        /**
         * Berth and lock stops are ignored as this merging tactic should only apply to anchorages and unclassified
         */
        private val INGORED_STOP_TYPES = listOf(NewStopType.BERTH, NewStopType.LOCK)
    }

    override fun shouldMerge(mergeableStop: NewStop, newStop: NewStop): Boolean {
        // ignore if any of our stops are of one of the ignored types
        if (mergeableStop.type in INGORED_STOP_TYPES || newStop.type in INGORED_STOP_TYPES) {
            return false
        }

        // One of the stops needs to be inside an anchor area to do this merging tactic
        if (mergeableStop.type != NewStopType.ANCHOR_AREA && newStop.type != NewStopType.ANCHOR_AREA) {
            return false
        }

        val timeBetween = getTimeBetweenStops(mergeableStop, newStop) ?: return false
        val distanceBetween = getDistanceBetweenStops(mergeableStop, newStop) ?: return false

        // Check if we can be more aggressive if both stops are the same anchorage
        if (mergeableStop.areaId == newStop.areaId) {
            return timeBetween < MERGING_ANCHOR_MAX_TIME_AGGRESSIVE &&
                distanceBetween < MERGING_ANCHOR_MAX_DISTANCE_AGGRESSIVE
        }

        return timeBetween < MERGING_ANCHOR_MAX_TIME &&
            distanceBetween < MERGING_ANCHOR_MAX_DISTANCE
    }
}
