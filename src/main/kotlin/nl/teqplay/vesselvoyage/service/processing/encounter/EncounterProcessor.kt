package nl.teqplay.vesselvoyage.service.processing.encounter

import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType
import nl.teqplay.aisengine.event.interfaces.EndEvent
import nl.teqplay.aisengine.event.interfaces.StartEvent
import nl.teqplay.skeleton.metrics.Metric
import nl.teqplay.skeleton.metrics.MetricRegistry
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.annotation.ProfileRevents
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.processing.StartEndEventProcessor
import org.springframework.stereotype.Service
import java.util.concurrent.atomic.AtomicLong

@ProfileProcessing
@ProfileRevents
@Service
class EncounterProcessor(
    config: EventProcessingProperties,
    meterRegistry: MeterRegistry
) : StartEndEventProcessor<EncounterEvent, EncounterStartProcessor, EncounterEndProcessor>(
    startProcessor = EncounterStartProcessor(config),
    endProcessor = EncounterEndProcessor(config),
    metricRegistry = MetricRegistry(EncounterProcessor::class, meterRegistry, listOf(TAG_ENCOUNTER_TYPE_KEY, TAG_EVENT_TYPE_KEY))
) {
    companion object {
        private const val TAG_ENCOUNTER_TYPE_KEY = "encounter_type"
    }

    private lateinit var startedEncountersByType: MutableMap<EncounterType, AtomicLong>
    private lateinit var endedEncountersByType: MutableMap<EncounterType, AtomicLong>

    override fun createGauges(metricRegistry: MetricRegistry<*>) {
        startedEncountersByType = mutableMapOf()
        endedEncountersByType = mutableMapOf()
        for (encounterType in EncounterType.values()) {
            startedEncountersByType[encounterType] = metricRegistry.createGauge(Metric.STATE_SIZE, AtomicLong(0), encounterType.name, "start")
            endedEncountersByType[encounterType] = metricRegistry.createGauge(Metric.STATE_SIZE, AtomicLong(0), encounterType.name, "end")
        }
    }

    override fun getGauge(event: EncounterEvent): AtomicLong? {
        return when (event) {
            is StartEvent -> startedEncountersByType.getValue(event.encounterType)
            is EndEvent -> endedEncountersByType.getValue(event.encounterType)
            else -> null
        }
    }
}
