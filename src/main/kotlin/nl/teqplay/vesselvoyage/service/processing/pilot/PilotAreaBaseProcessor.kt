package nl.teqplay.vesselvoyage.service.processing.pilot

import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.vesselvoyage.model.EventProcessingResult
import nl.teqplay.vesselvoyage.model.InitialShipStatus
import nl.teqplay.vesselvoyage.model.NewEventProcessingResult
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.processing.ActivityEventProcessor
import java.time.Instant

abstract class PilotAreaBaseProcessor(
    override val config: EventProcessingProperties
) : ActivityEventProcessor<AreaEvent>(
    activitiesField = NewVisit::pilotAreaActivities,
    areaIdField = AreaEvent::area
) {
    companion object {
        const val EVENT_IGNORED_ISSUE = "Pilot area events are only supported when a visit is ongoing."
    }

    override fun getResultOnVisit(
        status: NewVisitShipStatus,
        event: AreaEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return getResultOnVisit(status, event, updateTime, imo)
    }

    override fun getResultOnVoyage(
        status: NewVoyageShipStatus,
        event: AreaEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return getResultForPreviousVisitOnVoyage(status, event, updateTime)
    }

    override fun getResultOnInitial(
        status: NewInitialShipStatus,
        event: AreaEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return ignoredEventResult(
            status = status,
            event = event,
            description = EVENT_IGNORED_ISSUE
        )
    }

    override fun getResultOnVisit(
        status: VisitShipStatus,
        event: AreaEvent,
        config: EventProcessingProperties,
        imo: Int
    ): EventProcessingResult {
        return emptyEventResult(status)
    }

    override fun getResultOnVoyage(
        status: VoyageShipStatus,
        event: AreaEvent,
        config: EventProcessingProperties,
        imo: Int
    ): EventProcessingResult {
        return emptyEventResult(status)
    }

    override fun getResultOnInitial(
        status: InitialShipStatus,
        event: AreaEvent,
        config: EventProcessingProperties,
        imo: Int
    ): EventProcessingResult {
        return emptyEventResult(status)
    }
}
