package nl.teqplay.vesselvoyage.service.processing

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.vesselvoyage.model.ESof
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.EventProcessingResult
import nl.teqplay.vesselvoyage.model.ShipStatus
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.util.createChangeList
import nl.teqplay.vesselvoyage.util.regroupESof

/**
 * Extension of the [EventProcessor] which automatically resolves the visit and voyage processing results by updating the [ESof].
 * This will automatically deal with merging and restructuring of the [ESof],
 *  while also handling with updating the Visit or Voyage status.
 */
abstract class ESofEventProcessor<T : Event> : EventProcessor<T> {
    private val log = KotlinLogging.logger {}

    override fun getResultOnVisit(status: VisitShipStatus, event: T, config: EventProcessingProperties, imo: Int): EventProcessingResult {
        return getResultToUpdateESof(status, event, config, imo) { updatedESof ->
            VisitShipStatus(
                previousVisit = status.previousVisit?.copy(esof = null),
                previousVoyage = status.previousVoyage?.copy(esof = null),
                visit = status.visit.copy(esof = updatedESof)
            ).regroupESof()
        }
    }

    override fun getResultOnVoyage(status: VoyageShipStatus, event: T, config: EventProcessingProperties, imo: Int): EventProcessingResult {
        return getResultToUpdateESof(status, event, config, imo) { updatedESof ->
            VoyageShipStatus(
                previousVoyage = status.previousVoyage?.copy(esof = null),
                previousVisit = status.previousVisit?.copy(esof = null),
                voyage = status.voyage.copy(esof = updatedESof)
            ).regroupESof()
        }
    }

    private fun getResultToUpdateESof(
        status: ShipStatus,
        event: T,
        config: EventProcessingProperties,
        imo: Int,
        updateStatus: (updatedESof: ESof?) -> ShipStatus,
    ): EventProcessingResult {
        val mergedESof = getMergedEsof(status)

        val (updatedMergedESof, processingIssue) = getUpdatedEsofOnEvent(
            mergedESof = mergedESof,
            event = event,
            config = config,
            imo = imo
        )

        if (processingIssue != null) {
            log.debug { "Couldn't update e-sof of ship with IMO $imo with $event (issue = $processingIssue)" }
            return EventProcessingResult(
                status = status,
                changes = emptyList(),
                issues = listOf(processingIssue)
            )
        }

        log.debug { "Updated e-sof of ship with IMO $imo with $event" }
        val updatedStatus = updateStatus(updatedMergedESof)
        return EventProcessingResult(
            status = updatedStatus,
            changes = createChangeList(status, updatedStatus)
        )
    }

    /**
     * Update the [mergedESof] of the Visit or Voyage using the provided [event].
     *
     * @param mergedESof The current visit/voyage
     */
    abstract fun getUpdatedEsofOnEvent(mergedESof: ESof, event: T, config: EventProcessingProperties, imo: Int): Pair<ESof, EventProcessingIssue?>
}
