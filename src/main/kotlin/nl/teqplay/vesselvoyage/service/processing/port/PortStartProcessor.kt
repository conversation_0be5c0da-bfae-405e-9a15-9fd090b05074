package nl.teqplay.vesselvoyage.service.processing.port

import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.EventProcessingResult
import nl.teqplay.vesselvoyage.model.InitialShipStatus
import nl.teqplay.vesselvoyage.model.MAX_PORT_AREAS
import nl.teqplay.vesselvoyage.model.NewEventProcessingResult
import nl.teqplay.vesselvoyage.model.PortAreaVisit
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.AisFetchingService
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.util.createChangeList
import nl.teqplay.vesselvoyage.util.eventIsPartOfVisit
import nl.teqplay.vesselvoyage.util.getDestinationIfStillValid
import nl.teqplay.vesselvoyage.util.joinToStringTruncated
import nl.teqplay.vesselvoyage.util.startInitialVisit
import nl.teqplay.vesselvoyage.util.startVisit
import nl.teqplay.vesselvoyage.util.startVoyage
import nl.teqplay.vesselvoyage.util.toVesselVoyageLocation
import nl.teqplay.vesselvoyage.util.updatePreviousVoyageEndInformation
import java.time.Instant
import java.time.ZoneOffset
import java.time.ZonedDateTime

class PortStartProcessor(
    config: EventProcessingProperties,
    private val infraService: InfraService,
    private val aisFetchingService: AisFetchingService
) : PortBaseProcessor(config, infraService) {
    companion object {
        const val START_EVENT_IGNORED_ISSUE = "Port events start are only supported when a visit is ongoing."
        const val MISSING_UNLOCODE = "AreaEvent has no unlocode"
    }

    override fun getResultOnVisit(status: VisitShipStatus, event: AreaEvent, config: EventProcessingProperties, imo: Int): EventProcessingResult {
        val issues = mutableListOf<EventProcessingIssue>()
        val unlocode = event.area.unlocode ?: return ignoredEventResult(status, event, MISSING_UNLOCODE)

        if (status.visit.anchorAreas.any { it.endTime == null }) {
            val openAnchorages = status.visit.anchorAreas
                .filter { it.endTime == null }

            issues += EventProcessingIssue(
                eventId = event._id,
                description = "Unexpected start event: there are still ongoing anchor area visits " +
                    "(imo: ${status.visit.imo}, " +
                    "current open anchor area visits: ${
                    openAnchorages.joinToStringTruncated(10) { it.anchorAreaId }
                    }, " +
                    "new port: ${event.area.unlocode})"
            )
        }

        if (!eventIsPartOfVisit(status.visit, infraService::getPortByUnlocode, unlocode)) {
            issues += EventProcessingIssue(
                eventId = event._id,
                description = "Unexpected start event, not part of the current visit. " +
                    "Finishing current visit and starting a new visit (" +
                    "imo: ${status.visit.imo}, " +
                    "visit ports: ${status.visit.portAreas.joinToStringTruncated(10) {it.portId }}, " +
                    "event port: $unlocode" +
                    ")"
            )

            val newVoyageShipStatus = startVoyage(status, infraService::getAnchorage)
            val newVisitShipStatus = startVisit(event, newVoyageShipStatus, getShipTraceFunction(config), imo, unlocode)

            return EventProcessingResult(
                status = newVisitShipStatus,
                changes = createChangeList(status, listOf(newVoyageShipStatus, newVisitShipStatus)),
                issues = issues
            )
        }

        // protect against the visit growing infinitely large
        if (status.visit.portAreas.size >= MAX_PORT_AREAS) {
            issues += EventProcessingIssue(
                eventId = event._id,
                description = "Cannot process PortEvent: too many port areas in current visit. " +
                    "Will ignore the event (imo: $imo, portAreas: ${status.visit.portAreas.size})"
            )

            return EventProcessingResult(
                status = status,
                changes = listOf(),
                issues = issues
            )
        }

        val lastAreaWithSamePort = status.visit.portAreas.findLast { it.portId == unlocode }
        if (lastAreaWithSamePort != null && lastAreaWithSamePort.endTime == null) {
            issues += EventProcessingIssue(
                eventId = event._id,
                description = "Unexpected start event: " +
                    "there is already an ongoing start event in this same port $unlocode. " +
                    "Will ignore the event (imo: $imo)"
            )

            return EventProcessingResult(
                status = status,
                changes = listOf(),
                issues = issues
            )
        }

        // Call updatePreviousVoyage because the visits change
        val updatedShipStatus = updatePreviousVoyageEndInformation(
            status.copy(
                visit = status.visit.copy(
                    portAreas = status.visit.portAreas + PortAreaVisit(
                        portId = unlocode,
                        startEventId = event._id,
                        startTime = event.actualTime.atZone(ZoneOffset.UTC),
                        startLocation = event.location.toVesselVoyageLocation(),
                        startDraught = event.draught?.toDouble(),
                        endEventId = null,
                        endTime = null,
                        endLocation = null,
                        endDraught = null
                    ),
                    dest = getDestinationIfStillValid(status.visit, event)
                )
            )
        )

        return EventProcessingResult(
            status = updatedShipStatus,
            changes = createChangeList(status, updatedShipStatus)
        )
    }

    override fun getResultOnVoyage(status: VoyageShipStatus, event: AreaEvent, config: EventProcessingProperties, imo: Int): EventProcessingResult {
        val unlocode = event.area.unlocode ?: return ignoredEventResult(status, event, MISSING_UNLOCODE)

        val updatedShipStatus = startVisit(event, status, getShipTraceFunction(config), imo, unlocode)

        return EventProcessingResult(
            status = updatedShipStatus,
            changes = createChangeList(status, updatedShipStatus)
        )
    }

    override fun getResultOnInitial(status: InitialShipStatus, event: AreaEvent, config: EventProcessingProperties, imo: Int): EventProcessingResult {
        val unlocode = event.area.unlocode ?: return ignoredEventResult(status, event, MISSING_UNLOCODE)

        val shipStatus = startInitialVisit(event, imo, unlocode)

        return EventProcessingResult(
            status = shipStatus,
            changes = createChangeList(null, shipStatus)
        )
    }

    private fun getShipTraceFunction(config: EventProcessingProperties): (String, ZonedDateTime, ZonedDateTime) -> List<AisHistoricMessage> {
        return if (config.enableTraceCalculations && config.enableSlowMovingPeriods) {
            aisFetchingService::getShipTrace
        } else {
            { _, _, _ -> emptyList() }
        }
    }

    override fun getResultOnVisit(
        status: NewVisitShipStatus,
        event: AreaEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return getResultOnVisit(status, event, updateTime, imo)
    }

    override fun getResultOnVoyage(
        status: NewVoyageShipStatus,
        event: AreaEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return ignoredEventResult(status, event, START_EVENT_IGNORED_ISSUE)
    }

    override fun getResultOnInitial(
        status: NewInitialShipStatus,
        event: AreaEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return ignoredEventResult(status, event, START_EVENT_IGNORED_ISSUE)
    }
}
