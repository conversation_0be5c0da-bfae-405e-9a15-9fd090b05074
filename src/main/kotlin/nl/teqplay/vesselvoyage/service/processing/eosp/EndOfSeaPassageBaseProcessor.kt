package nl.teqplay.vesselvoyage.service.processing.eosp

import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.EventProcessingResult
import nl.teqplay.vesselvoyage.model.InitialShipStatus
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.AreaActivity
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.processing.EventProcessor
import nl.teqplay.vesselvoyage.service.processing.EventProcessor.Companion.MAX_ALLOWED_ACTIVITIES

abstract class EndOfSeaPassageBaseProcessor(
    private val infraService: InfraService
) : EventProcessor<AreaEvent> {

    companion object {
        const val EVENT_EOSP_NOT_MAIN_PORT_ISSUE = "Event received but EOSP is not of a main port"
        const val EVENT_NO_UNLOCODE = "Event has no unlocode"
    }

    /**
     * Limit the provided entry if the amount of pass-through areas are too high
     */
    private fun <T : NewEntry> T.limitIfNeeded(onLimit: (T) -> T): T {
        if (this.limited) {
            return onLimit(this)
        }

        if (this.passThroughEosp.size >= MAX_ALLOWED_ACTIVITIES || this.passThroughPort.size >= MAX_ALLOWED_ACTIVITIES) {
            return onLimit(this)
        }

        // Nothing to limit
        return this
    }

    /**
     * @see NewEntry.limitIfNeeded
     */
    protected fun NewVisit.limitIfNeeded(): NewVisit {
        return this.limitIfNeeded {
            it.copy(limited = true)
        }
    }

    /**
     * @see NewEntry.limitIfNeeded
     */
    protected fun NewVoyage.limitIfNeeded(): NewVoyage {
        return this.limitIfNeeded {
            it.copy(limited = true)
        }
    }

    override fun isValid(status: NewShipStatus, event: AreaEvent, imo: Int): EventProcessingIssue? {
        val unlocode = event.area.unlocode ?: return EventProcessingIssue(
            eventId = event._id,
            description = EVENT_NO_UNLOCODE
        )

        val issue = super.isValid(status, event, imo)
        if (issue != null) {
            return issue
        }

        val isMainPort = infraService.isPortMainPort(unlocode = unlocode)
        if (!isMainPort) {
            return EventProcessingIssue(
                eventId = event._id,
                EVENT_EOSP_NOT_MAIN_PORT_ISSUE
            )
        }

        return null
    }

    override fun getResultOnVisit(
        status: VisitShipStatus,
        event: AreaEvent,
        config: EventProcessingProperties,
        imo: Int
    ): EventProcessingResult {
        return emptyEventResult(status)
    }

    override fun getResultOnVoyage(
        status: VoyageShipStatus,
        event: AreaEvent,
        config: EventProcessingProperties,
        imo: Int
    ): EventProcessingResult {
        return emptyEventResult(status)
    }

    override fun getResultOnInitial(
        status: InitialShipStatus,
        event: AreaEvent,
        config: EventProcessingProperties,
        imo: Int
    ): EventProcessingResult {
        return emptyEventResult(status)
    }

    protected fun areaIdToUnlocode(areaId: String): String {
        return infraService.getPortByAreaId(areaId)?.unlocode ?: areaId
    }

    protected fun AreaActivity.unlocode(): String {
        return infraService.getPortByAreaId(this.areaId)?.unlocode ?: areaId
    }
}
