package nl.teqplay.vesselvoyage.service.processing.anchor

import nl.teqplay.aisengine.event.interfaces.AnchoredEvent
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.Voyage
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.processing.ActivityEventProcessor
import nl.teqplay.vesselvoyage.util.toVesselVoyageLocation

/**
 * Base for both start and end anchor event processors containing some utility functions.
 */
abstract class AnchorBaseProcessor(
    override val config: EventProcessingProperties,
    protected val infraService: InfraService
) : ActivityEventProcessor<AnchoredEvent>(
    activitiesField = NewVisit::anchorAreaActivities,
    areaIdField = AnchoredEvent::area
) {
    protected fun getDestinations(event: AnchoredEvent): DestinationsResult {
        val anchorage = (event.area.name ?: "").let { infraService.getAnchorage(it, event.location.toVesselVoyageLocation()) }

        return DestinationsResult(
            destinations = anchorage?.ports?.toSet() ?: emptySet(),
            issues = listOfNotNull(
                if (anchorage == null) {
                    EventProcessingIssue(
                        eventId = event._id,
                        description = "Anchorage area with id \"${event.area.name}\" not found, " +
                            "cannot resolve ports belonging to this anchorage"
                    )
                } else {
                    null
                }
            )
        )
    }

    protected fun getDestinations(voyage: Voyage, event: AnchoredEvent): DestinationsResult {
        val anchorAreaDestinations = getDestinations(event)

        val trueDestination = voyage.dest?.trueDestination
        return if (trueDestination != null) {
            DestinationsResult(
                destinations = setOf(trueDestination),
                issues = anchorAreaDestinations.issues + listOfNotNull(
                    if (!anchorAreaDestinations.destinations.contains(trueDestination)) {
                        EventProcessingIssue(
                            eventId = event._id,
                            description = "Ship arrived at anchorage ${event.area.name}, " +
                                "but non of the destinations of this anchorage match the voyage destination " +
                                "of the ship (imo: ${voyage.imo}, " +
                                "voyage destination: $trueDestination, " +
                                "anchorage: ${event.area.name}, " +
                                "anchorage destinations: ${anchorAreaDestinations.destinations}, " +
                                ")"
                        )
                    } else {
                        null
                    }
                )
            )
        } else {
            anchorAreaDestinations
        }
    }

    protected data class DestinationsResult(
        val destinations: Set<String>,
        val issues: List<EventProcessingIssue>
    )
}
