package nl.teqplay.vesselvoyage.service.processing.eosp

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.NewEventProcessingResult
import nl.teqplay.vesselvoyage.model.internal.InfraAreaType
import nl.teqplay.vesselvoyage.model.lightweight.poma.Anchorage
import nl.teqplay.vesselvoyage.model.v2.AreaActivity
import nl.teqplay.vesselvoyage.model.v2.ESoFChange
import nl.teqplay.vesselvoyage.model.v2.EntryESoFWrapper
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewStopType
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.VisitChange
import nl.teqplay.vesselvoyage.model.v2.VoyageChange
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.processing.EventProcessor.Companion.MAX_ALLOWED_ACTIVITIES
import nl.teqplay.vesselvoyage.util.correctESoFForZeroSecondVoyage
import nl.teqplay.vesselvoyage.util.correctFinishedVisitToZeroSecondVoyageStart
import nl.teqplay.vesselvoyage.util.createNewVisit
import nl.teqplay.vesselvoyage.util.createNewVoyage
import nl.teqplay.vesselvoyage.util.finishESoF
import nl.teqplay.vesselvoyage.util.finishVisit
import nl.teqplay.vesselvoyage.util.finishVoyage
import nl.teqplay.vesselvoyage.util.isOngoing
import nl.teqplay.vesselvoyage.util.isPassThrough
import nl.teqplay.vesselvoyage.util.matchingAreaId
import nl.teqplay.vesselvoyage.util.mergeESoF
import nl.teqplay.vesselvoyage.util.resumeVoyage
import java.time.Instant

class EndOfSeaPassageEndProcessor(
    override val config: EventProcessingProperties,
    private val infraService: InfraService
) : EndOfSeaPassageBaseProcessor(infraService) {
    private val log = KotlinLogging.logger {}

    companion object {
        const val END_EVENT_NEW_SHIP_ISSUE = "End event received but never started a visit for this ship."
        const val END_EVENT_VOYAGE_ALREADY_ONGOING_ISSUE = "End event received but we never started a visit, ignoring."
        const val EVENT_NO_AREA_ID = "End event received but no area id available"
    }

    override fun getResultOnVisit(
        status: NewVisitShipStatus,
        event: AreaEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        val areaId = event.area.id ?: return ignoredEventResult(status, event, EVENT_NO_AREA_ID)
        val currentVisit = status.visit.entry
        val visitEospAreaActivity = currentVisit.eospAreaActivity
        val eospAreaActivities = currentVisit.otherOngoingEospAreaActivities

        if (eospAreaActivities.any { activity -> activity.end != null }) {
            log.warn { "One of the other ongoing EOSP is already finished. This should not be possible (imo = ${currentVisit.imo} visit = ${currentVisit._id}) $eospAreaActivities" }
        }

        if (visitEospAreaActivity.areaId != areaId &&
            eospAreaActivities.all { activity -> activity.areaId != areaId }
        ) {
            val previousVisit = status.previousVisit?.entry
            val previousVisitEospAreaActivity = previousVisit?.eospAreaActivity

            // When we ended the previous visit with a fallback, check if the end event is relevant for the previous Visit
            if (previousVisitEospAreaActivity?.end?.fallback != null && previousVisitEospAreaActivity.areaId == areaId) {
                return handleAsPreviousVisitFallbackOverride(
                    status = status,
                    event = event,
                    updateTime = updateTime
                )
            }

            return ignoredEventResult(
                status = status,
                event = event,
                description = "End event received is not for any of the main ports we are currently visiting"
            )
        }

        // Close this Visit if the EOSP of our current leading main port is being ended
        if (visitEospAreaActivity.areaId == areaId) {
            val voyageStartLocationTime = LocationTime(
                location = event.location,
                time = event.actualTime
            )
            val previousVoyage = status.previousVoyage?.entry
            val firstOtherOngoingEosp = eospAreaActivities.firstOrNull()

            // Check if we need to process this EOSP event as a pass through, canceling our current visit
            return if (currentVisit.isPassThrough() || currentVisit.start.time == voyageStartLocationTime.time) {
                if (firstOtherOngoingEosp == null) {
                    if (previousVoyage != null) {
                        handleAsPassThroughResumePreviousVoyage(
                            currentVisit = currentVisit,
                            previousVoyage = previousVoyage,
                            voyageStartLocationTime = voyageStartLocationTime,
                            status = status,
                            updateTime = updateTime
                        )
                    } else {
                        // No other ongoing EOSP so we can cancel this visit
                        handleAsPassThroughCancelingOnlyVisit(
                            status = status,
                            updateTime = updateTime
                        )
                    }
                } else {
                    // Switch our current visit completely over to the other ongoing one
                    handleAsReplaceVisitWithOtherMainPort(
                        currentVisitEnd = voyageStartLocationTime,
                        currentVisit = currentVisit,
                        status = status,
                        otherMainPortEospActivity = firstOtherOngoingEosp,
                        updateTime = updateTime
                    )
                }
            } else {
                // Decide how we have to handle the ongoing visit as we might have overlapping EOSP areas
                // TODO cancel visit if not confirmed and after restructure of stops is empty

                if (firstOtherOngoingEosp != null) {
                    // We have an overlapping eosp, so check if our last stop was an anchor
                    val lastAnchorStop = currentVisit.stops.lastOrNull()?.takeIf { stop -> stop.type == NewStopType.ANCHOR_AREA }
                    val anchorageAreaId = lastAnchorStop?.areaId
                    val stopAnchoragePorts = anchorageAreaId
                        ?.let { (infraService.getById(anchorageAreaId, InfraAreaType.ANCHOR) as? Anchorage) }
                        ?.ports
                    val stopPotentialPorts = stopAnchoragePorts?.mapNotNull { unlocode -> infraService.getPortByUnlocode(unlocode)?._id }
                    val matchingAnchorOtherMainPortEospActivity = stopPotentialPorts?.let {
                        eospAreaActivities.firstOrNull { activity ->
                            activity.areaId.removeSuffix(".eosp") in stopPotentialPorts
                        }
                    }

                    if (!currentVisit.confirmed) {
                        handleAsReplaceVisitWithOtherMainPort(
                            currentVisitEnd = voyageStartLocationTime,
                            currentVisit = currentVisit,
                            status = status,
                            otherMainPortEospActivity = matchingAnchorOtherMainPortEospActivity ?: firstOtherOngoingEosp,
                            updateTime = updateTime
                        )
                    } else {
                        handleAsFinishVisitWithZeroSecondVoyage(
                            event = event,
                            currentVisit = currentVisit,
                            newVisitStart = voyageStartLocationTime,
                            status = status,
                            otherOngoingEospAreaActivities = eospAreaActivities,
                            firstOtherOngoingEosp = firstOtherOngoingEosp,
                            updateTime = updateTime,
                            imo = imo,
                            areaId = areaId
                        )
                    }
                } else {
                    // We exited our main port that was interested, meaning we can just close it
                    handleAsCreateFinishOngoingVisit(
                        event = event,
                        currentVisit = currentVisit,
                        voyageStartLocationTime = voyageStartLocationTime,
                        status = status,
                        updateTime = updateTime,
                        imo = imo,
                        areaId = areaId
                    )
                }
            }
        } else {
            return handleAsDroppingOtherOngoingEospActivity(
                event = event,
                eospAreaActivities = eospAreaActivities,
                areaId = areaId,
                currentVisit = currentVisit,
                status = status,
                updateTime = updateTime
            )
        }
    }

    private fun handleAsCreateFinishOngoingVisit(
        event: AreaEvent,
        currentVisit: NewVisit,
        voyageStartLocationTime: LocationTime,
        status: NewVisitShipStatus,
        updateTime: Instant,
        imo: Int,
        areaId: String
    ): NewEventProcessingResult {
        val newVoyage = createNewVoyage(
            event = event,
            currentVisit = currentVisit,
            voyageStart = voyageStartLocationTime,
            updateTime = updateTime,
            imo = imo,
            areaId = areaId
        )
        val finishedVisit = finishVisit(
            currentVisit = currentVisit,
            newVoyage = newVoyage,
            voyageStart = voyageStartLocationTime,
            updateTime = updateTime
        )
        val finishedVisitEsof = finishESoF(
            esof = status.visit.esof,
            voyageStart = voyageStartLocationTime,
            updateTime = updateTime
        )

        val updatedStatus = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(entry = newVoyage, esof = null),
            previousVisit = status.visit.copy(entry = finishedVisit, esof = finishedVisitEsof),
            previousVoyage = status.previousVoyage
        )

        val changes = listOfNotNull(
            VisitChange(Action.UPDATE, finishedVisit),
            finishedVisitEsof?.let { ESoFChange(Action.UPDATE, finishedVisitEsof) },
            VoyageChange(Action.CREATE, newVoyage)
        )

        return NewEventProcessingResult(
            status = updatedStatus,
            changes = changes,
            readyForPostProcessing = listOf(finishedVisit._id),
            decision = "Finishing visit, creating voyage"
        )
    }

    private fun handleAsReplaceVisitWithOtherMainPort(
        currentVisitEnd: LocationTime,
        currentVisit: NewVisit,
        status: NewVisitShipStatus,
        otherMainPortEospActivity: AreaActivity,
        updateTime: Instant
    ): NewEventProcessingResult {
        val currentVisitEosp = currentVisit.eospAreaActivity
        val finishedCurrentEosp = currentVisitEosp.copy(end = currentVisitEnd)
        val currentVisitEsof = status.visit.esof

        // Ensure the start time can only move forward not backward in time.
        val correctedStart = when {
            otherMainPortEospActivity.start.time > currentVisit.start.time -> otherMainPortEospActivity.start
            else -> currentVisit.start
        }

        val newVisitId = "${otherMainPortEospActivity.id}.VISIT"
        val newVisit = currentVisit.copy(
            _id = newVisitId,
            start = correctedStart,
            eospAreaActivity = otherMainPortEospActivity,
            otherOngoingEospAreaActivities = currentVisit.otherOngoingEospAreaActivities - otherMainPortEospActivity,
            passThroughEosp = (currentVisit.passThroughEosp + finishedCurrentEosp).take(MAX_ALLOWED_ACTIVITIES),
            updatedAt = updateTime
        ).limitIfNeeded()

        val newVisitEsof = currentVisitEsof?.copy(
            _id = newVisitId,
            updatedAt = updateTime
        )

        val updatedPreviousVoyage = status.previousVoyage?.entry?.copy(
            end = correctedStart,
            next = newVisit._id,
            updatedAt = updateTime
        )

        val newStatus = status.copy(
            visit = EntryESoFWrapper(newVisit, newVisitEsof),
            previousVoyage = if (updatedPreviousVoyage != null) {
                status.previousVoyage?.copy(entry = updatedPreviousVoyage)
            } else {
                status.previousVoyage
            }
        )

        val portA = currentVisit.eospAreaActivity.unlocode()
        val portB = otherMainPortEospActivity.unlocode()
        val otherMainPorts = newVisit.otherOngoingEospAreaActivities.joinToString { it.unlocode() }
        val changes = listOfNotNull(
            VisitChange(Action.DELETE, currentVisit.copy(updatedAt = updateTime)),
            currentVisitEsof?.let { ESoFChange(Action.DELETE, currentVisitEsof.copy(updatedAt = updateTime)) },
            VisitChange(Action.CREATE, newVisit),
            newVisitEsof?.let { ESoFChange(Action.CREATE, newVisitEsof) },
            updatedPreviousVoyage?.let { VoyageChange(Action.UPDATE, updatedPreviousVoyage) },
        )

        val decision = "Replacing visit because of main port switch $portA -> $portB. " +
            "Other mainports: $otherMainPorts.\n" +
            "Changes\n: ${changes.joinToString("\n")}\n" +
            "Status before update: $status\n" +
            "Status after update : $newStatus"

        return NewEventProcessingResult(
            status = newStatus,
            changes = changes,
            // If we had a previous Voyage, re-trigger post-processing as times are now changed
            readyForPostProcessing = updatedPreviousVoyage?.let { listOf(updatedPreviousVoyage._id) } ?: emptyList(),
            decision = decision
        )
    }

    private fun handleAsFinishVisitWithZeroSecondVoyage(
        event: AreaEvent,
        currentVisit: NewVisit,
        newVisitStart: LocationTime,
        status: NewVisitShipStatus,
        otherOngoingEospAreaActivities: List<AreaActivity>,
        firstOtherOngoingEosp: AreaActivity,
        updateTime: Instant,
        imo: Int,
        areaId: String
    ): NewEventProcessingResult {
        val newVoyage = createNewVoyage(
            event = event,
            currentVisit = currentVisit,
            voyageStart = newVisitStart,
            updateTime = updateTime,
            imo = imo,
            areaId = areaId
        )
        val ongoingOtherMainPortAreaActivities = currentVisit.portAreaActivities.filter { portAreaActivity ->
            portAreaActivity.isOngoing() && currentVisit.otherOngoingEospAreaActivities.any { eospAreaActivity ->
                eospAreaActivity.matchingAreaId(portAreaActivity, suffix = ".eosp")
            }
        }
        val finishedVisit = finishVisit(
            currentVisit = currentVisit,
            newVoyage = newVoyage,
            voyageStart = newVisitStart,
            portAreaActivitiesToRemove = ongoingOtherMainPortAreaActivities.toSet(),
            updateTime = updateTime
        )
        val finishedVisitEsof = finishESoF(
            esof = status.visit.esof,
            voyageStart = newVisitStart,
            updateTime = updateTime
        )
        val remainingOngoingEosp = otherOngoingEospAreaActivities - firstOtherOngoingEosp

        val correctedFinishedVisit = correctFinishedVisitToZeroSecondVoyageStart(
            finishedVisit = finishedVisit,
            zeroSecondVoyageStart = newVisitStart,
            updateTime = updateTime
        )
        val correctedFinishedVisitEsof = correctESoFForZeroSecondVoyage(
            finishedEsof = finishedVisitEsof,
            newVisitStartTime = newVisitStart,
            updateTime = updateTime
        )

        val correctedNewVoyage = newVoyage.copy(start = newVisitStart)

        val newVisit = createNewVisit(event, correctedNewVoyage, newVisitStart, updateTime, imo, areaId).copy(
            // Use the area id of the other ongoing EOSP instead of the event one
            eospAreaActivity = AreaActivity(
                id = event._id,
                start = firstOtherOngoingEosp.start,
                end = null,
                areaId = firstOtherOngoingEosp.areaId
            ),
            portAreaActivities = ongoingOtherMainPortAreaActivities.toMutableList(),
            otherOngoingEospAreaActivities = remainingOngoingEosp
        )
        val finishedZeroSecondVoyage = finishVoyage(
            currentVoyage = correctedNewVoyage,
            newVisit = newVisit,
            visitStart = newVisitStart,
            updateTime = updateTime
        )

        // restructure stops depending on start time of stops
        val (restructuredFinishedVisit, restructuredNewVisit) = restructureStops(
            correctedFinishedVisit = correctedFinishedVisit,
            newVisit = newVisit,
            splitTime = newVisitStart.time,
            updateTime = updateTime
        )

        val updatedStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(restructuredNewVisit, esof = null),
            previousVoyage = EntryESoFWrapper(entry = finishedZeroSecondVoyage, esof = null),
            previousVisit = status.visit.copy(entry = restructuredFinishedVisit, esof = correctedFinishedVisitEsof)
        )

        val changes = listOfNotNull(
            VisitChange(Action.UPDATE, restructuredFinishedVisit),
            correctedFinishedVisitEsof?.let { ESoFChange(Action.UPDATE, correctedFinishedVisitEsof) },
            VoyageChange(Action.CREATE, finishedZeroSecondVoyage),
            VisitChange(Action.CREATE, restructuredNewVisit)
        )

        val currentVisitUnlo = currentVisit.eospAreaActivity.unlocode()
        val firstOtherOngoingEospUnlo = firstOtherOngoingEosp.unlocode()
        val otherMainPorts = newVisit.otherOngoingEospAreaActivities.joinToString { it.unlocode() }
        val decision = "Ending current visit for $currentVisitUnlo, but there are still other ongoing main port " +
            "activities. Finishing current visit, adding 0-second voyage and creating a new visit for " +
            "$firstOtherOngoingEospUnlo. Other mainports after this update: $otherMainPorts.\n" +
            "Changes\n: ${changes.joinToString("\n")}\n" +
            "Status before update: $status\n" +
            "Status after update : $updatedStatus"

        return NewEventProcessingResult(
            status = updatedStatus,
            changes = changes,
            readyForPostProcessing = listOf(restructuredFinishedVisit._id),
            decision = decision
        )
    }

    private fun restructureStops(
        correctedFinishedVisit: NewVisit,
        newVisit: NewVisit,
        splitTime: Instant,
        updateTime: Instant
    ): Pair<NewVisit, NewVisit> {
        val stops = correctedFinishedVisit.stops
        val (finishedVisitStops, newVisitStops) = stops.partition { stop -> stop.start.time < splitTime }

        return correctedFinishedVisit.copy(stops = finishedVisitStops, updatedAt = updateTime) to newVisit.copy(stops = newVisitStops, updatedAt = updateTime)
    }

    private fun handleAsPassThroughCancelingOnlyVisit(
        status: NewVisitShipStatus,
        updateTime: Instant
    ): NewEventProcessingResult {
        // We are canceling our only visit, so we can go back to the initial ship status
        val newStatus = NewInitialShipStatus()

        return NewEventProcessingResult(
            status = newStatus,
            changes = listOfNotNull(
                // Delete any changes we might have from this visit
                VisitChange(Action.DELETE, status.visit.entry.copy(updatedAt = updateTime)),
                status.visit.esof?.let { esof -> ESoFChange(Action.DELETE, esof.copy(updatedAt = updateTime)) }
            ),
            decision = "Moving back to initial state, delete current visit as it was just a pass-through"
        )
    }

    private fun handleAsPassThroughResumePreviousVoyage(
        currentVisit: NewVisit,
        previousVoyage: NewVoyage,
        voyageStartLocationTime: LocationTime,
        status: NewVisitShipStatus,
        updateTime: Instant
    ): NewEventProcessingResult {
        val resumedVoyage = resumeVoyage(
            currentVisit = currentVisit,
            previousVoyage = previousVoyage,
            passThroughEndTime = voyageStartLocationTime,
            updateTime = updateTime
        )
        val (mergedESoF, mergedESoFIsNew) = mergeESoF(
            newEntryId = resumedVoyage._id,
            visit = status.visit,
            voyage = status.previousVoyage,
            updateTime = updateTime
        )
        val mergedESoFAction = if (mergedESoFIsNew) Action.CREATE else Action.UPDATE

        // Handle as pass-through as we are not in any other EOSP areas
        val actualPreviousVisitEnd = resumedVoyage.actualStart
        val previousVisit = status.previousVisit?.entry

        val currentVisitUnlo = currentVisit.eospAreaActivity.unlocode()
        val decision = "Resuming previous voyage, deleting current visit ($currentVisitUnlo) and marking current " +
            "visit as pass-through on resumed voyage."

        // If we had an actual start it means we had overlapping EOSP
        // Because we had a pass-through, this means the start of our voyage should be the original EOSP end time of our previous visit port
        if (actualPreviousVisitEnd != null && previousVisit != null) {
            val actualResumedVoyage = resumedVoyage.copy(
                start = actualPreviousVisitEnd,
                updatedAt = updateTime
            )
            val actualPreviousVisit = previousVisit.copy(
                end = actualPreviousVisitEnd,
                eospAreaActivity = previousVisit.eospAreaActivity.copy(
                    end = actualPreviousVisitEnd
                ),
                updatedAt = updateTime
            )
            val newStatus = NewVoyageShipStatus(
                voyage = EntryESoFWrapper(actualResumedVoyage, mergedESoF),
                previousVisit = status.previousVisit?.copy(entry = actualPreviousVisit),
                previousVoyage = null
            )

            return NewEventProcessingResult(
                status = newStatus,
                changes = listOfNotNull(
                    mergedESoF?.let { ESoFChange(mergedESoFAction, mergedESoF) },
                    VisitChange(Action.UPDATE, actualPreviousVisit),
                    VoyageChange(Action.UPDATE, actualResumedVoyage),
                    VisitChange(Action.DELETE, currentVisit.copy(updatedAt = updateTime)),
                    status.visit.esof?.let { esof -> ESoFChange(Action.DELETE, esof.copy(updatedAt = updateTime)) }
                ),
                // As we restructured the visit, this also means our visit end time is potentially different
                readyForPostProcessing = listOf(actualPreviousVisit._id),
                decision = "$decision. Additionally, setting the resumedVoyage.start to the previousVisit.end"
            )
        }

        val newStatus = NewVoyageShipStatus(
            voyage = EntryESoFWrapper(resumedVoyage, mergedESoF),
            previousVisit = status.previousVisit,
            previousVoyage = null
        )

        return NewEventProcessingResult(
            status = newStatus,
            changes = listOfNotNull(
                mergedESoF?.let { ESoFChange(mergedESoFAction, mergedESoF) },
                VoyageChange(Action.UPDATE, resumedVoyage),
                VisitChange(Action.DELETE, currentVisit.copy(updatedAt = updateTime)),
                status.visit.esof?.let { esof -> ESoFChange(Action.DELETE, esof.copy(updatedAt = updateTime)) }
            ),
            decision = decision
        )
    }

    private fun handleAsDroppingOtherOngoingEospActivity(
        event: AreaEvent,
        eospAreaActivities: List<AreaActivity>,
        areaId: String,
        currentVisit: NewVisit,
        status: NewVisitShipStatus,
        updateTime: Instant
    ): NewEventProcessingResult {
        val droppingActivity = eospAreaActivities.first { activity -> activity.areaId == areaId }
        val remainingEospAreaActivities = eospAreaActivities - droppingActivity

        val finishedPassThroughEosp = droppingActivity.copy(
            end = LocationTime(
                location = event.location,
                time = event.actualTime
            )
        )

        val newPassThroughEosp = currentVisit.passThroughEosp + finishedPassThroughEosp

        val updatedVisit = currentVisit.copy(
            otherOngoingEospAreaActivities = remainingEospAreaActivities,
            passThroughEosp = newPassThroughEosp.take(MAX_ALLOWED_ACTIVITIES),
            updatedAt = updateTime
        ).limitIfNeeded()

        return NewEventProcessingResult(
            status = status.copy(
                visit = status.visit.copy(entry = updatedVisit)
            ),
            changes = listOf(
                VisitChange(Action.UPDATE, updatedVisit)
            ),
            decision = "Marking ${event.area.unlocode} as pass-through, was marked as 'other ongoing eosp activity'."
        )
    }

    fun handleOtherOngoingEospOfPreviousVisitAsPassthrough(
        status: NewVoyageShipStatus,
        previousVisit: NewVisit,
        otherOngoingEospToMarkAsPassthrough: AreaActivity,
        event: AreaEvent,
        updateTime: Instant
    ): NewEventProcessingResult {
        val updatedOtherOngoingEosp = otherOngoingEospToMarkAsPassthrough.copy(
            end = LocationTime(
                location = event.location,
                time = event.actualTime
            )
        )
        // move the other ongoing eosp to the passthrough list
        val updatedPreviousVisit = previousVisit.copy(
            otherOngoingEospAreaActivities = previousVisit.otherOngoingEospAreaActivities - otherOngoingEospToMarkAsPassthrough,
            passThroughEosp = previousVisit.passThroughEosp + updatedOtherOngoingEosp,
            updatedAt = updateTime
        )
        return NewEventProcessingResult(
            status = status.copy(previousVisit = status.previousVisit?.copy(entry = updatedPreviousVisit)),
            changes = listOf(VisitChange(Action.UPDATE, updatedPreviousVisit)),
            decision = "Mark ${event.area.unlocode} as pass-through on previous visit, was in " +
                "'other ongoing main port EOSPs' list on previous visit."
        )
    }

    /**
     * Handle as by overriding the fallback end time of the visit with the end time of the event
     */
    private fun handleAsPreviousVisitFallbackOverride(
        status: NewVisitShipStatus,
        event: AreaEvent,
        updateTime: Instant
    ): NewEventProcessingResult {
        val previousVoyage = status.previousVoyage!!
        val previousVisit = status.previousVisit!!
        val previousVisitEntry = previousVisit.entry

        val correctedVisitEndTime = LocationTime(
            location = event.location,
            time = event.actualTime
        )
        val correctedPreviousVisit = previousVisitEntry.copy(
            end = correctedVisitEndTime,
            eospAreaActivity = previousVisitEntry.eospAreaActivity.copy(
                end = correctedVisitEndTime
            ),
            updatedAt = updateTime
        )

        // Also correct the voyage as it might have a new start time that is later/earlier
        val correctedInBetweenVoyage = previousVoyage.entry.copy(
            start = correctedVisitEndTime,
            updatedAt = updateTime
        )

        return NewEventProcessingResult(
            status = status.copy(
                previousVoyage = previousVoyage.copy(entry = correctedInBetweenVoyage),
                previousVisit = previousVisit.copy(entry = correctedPreviousVisit)
            ),
            changes = listOf(
                VisitChange(Action.UPDATE, correctedPreviousVisit),
                VoyageChange(Action.UPDATE, correctedInBetweenVoyage),
            ),
            decision = "Previous visit was ended by a fallback. Now received the actual end event, correcting the " +
                "previous visit.end and voyage.start"
        )
    }

    private fun handleAsPreviousVisitFallbackOverride(
        status: NewVoyageShipStatus,
        event: AreaEvent,
        updateTime: Instant,
    ): NewEventProcessingResult {
        val previousVisit = status.previousVisit!!
        val previousVisitEntry = previousVisit.entry

        val correctedVisitEndTime = LocationTime(
            location = event.location,
            time = event.actualTime
        )
        val correctedPreviousVisit = previousVisitEntry.copy(
            end = correctedVisitEndTime,
            eospAreaActivity = previousVisitEntry.eospAreaActivity.copy(
                end = correctedVisitEndTime
            ),
            updatedAt = updateTime
        )
        val correctedCurrentVoyage = status.voyage.entry.copy(
            start = correctedVisitEndTime
        )

        return NewEventProcessingResult(
            status = status.copy(
                voyage = status.voyage.copy(entry = correctedCurrentVoyage),
                previousVisit = previousVisit.copy(entry = correctedPreviousVisit),
            ),
            changes = listOf(
                VisitChange(Action.UPDATE, correctedPreviousVisit),
                VoyageChange(Action.UPDATE, correctedCurrentVoyage),
            ),
            decision = "Previous visit was finished by a fallback, but now received the actual end event. Updating " +
                "the previous visit with the actual end location/time"
        )
    }

    override fun getResultOnVoyage(
        status: NewVoyageShipStatus,
        event: AreaEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        val areaId = event.area.id ?: return ignoredEventResult(status, event, EVENT_NO_AREA_ID)

        val previousVisit = status.previousVisit?.entry
        val previousVisitEospAreaActivity = previousVisit?.eospAreaActivity

        // When we ended the previous visit with a fallback, check if the end event is relevant for the previous Visit
        if (previousVisitEospAreaActivity?.end?.fallback != null && previousVisitEospAreaActivity.areaId == areaId) {
            return handleAsPreviousVisitFallbackOverride(
                status = status,
                event = event,
                updateTime = updateTime
            )
        }

        // Allow previousVisit.otherOngoingMainPortEosps to end after the leading visit main port EOSP end has been
        // processed. This happens with a visit having *unfinished* other main port EOSPs at the same time (multiple
        // events on the same AIS point), but the visit leading main port EOSP end has already been processed. That
        // causes the ship status to switch to a voyage, which is why we end up in this function.
        val otherOngoingEospOfPreviousVisit = previousVisit
            ?.otherOngoingEospAreaActivities
            ?.find { otherOngoingEosp ->
                otherOngoingEosp.isOngoing() &&
                    otherOngoingEosp.areaId == areaId &&
                    previousVisit.end?.time == event.actualTime
            }

        return if (otherOngoingEospOfPreviousVisit != null) {
            handleOtherOngoingEospOfPreviousVisitAsPassthrough(
                status,
                previousVisit,
                otherOngoingEospOfPreviousVisit,
                event,
                updateTime
            )
        } else {
            ignoredEventResult(
                status = status,
                event = event,
                description = END_EVENT_VOYAGE_ALREADY_ONGOING_ISSUE
            )
        }
    }

    override fun getResultOnInitial(
        status: NewInitialShipStatus,
        event: AreaEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return ignoredEventResult(
            status = status,
            event = event,
            description = END_EVENT_NEW_SHIP_ISSUE
        )
    }
}
