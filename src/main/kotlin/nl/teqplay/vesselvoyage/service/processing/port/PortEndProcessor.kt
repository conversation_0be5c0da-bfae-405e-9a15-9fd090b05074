package nl.teqplay.vesselvoyage.service.processing.port

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.vesselvoyage.logic.CalculateStops
import nl.teqplay.vesselvoyage.logic.stop.calculateAndSetAccuracy
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.Change
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.EventProcessingResult
import nl.teqplay.vesselvoyage.model.InitialShipStatus
import nl.teqplay.vesselvoyage.model.NewEventProcessingResult
import nl.teqplay.vesselvoyage.model.Stop
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.VisitChange
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.StaticShipInfoService
import nl.teqplay.vesselvoyage.service.processing.EventProcessor.Companion.MAX_ALLOWED_ACTIVITIES
import nl.teqplay.vesselvoyage.util.appendPassThroughAreas
import nl.teqplay.vesselvoyage.util.createChangeList
import nl.teqplay.vesselvoyage.util.getDestinationIfStillValid
import nl.teqplay.vesselvoyage.util.isFinished
import nl.teqplay.vesselvoyage.util.isPassThrough
import nl.teqplay.vesselvoyage.util.joinToStringTruncated
import nl.teqplay.vesselvoyage.util.startInitialVoyage
import nl.teqplay.vesselvoyage.util.startVoyage
import nl.teqplay.vesselvoyage.util.unfinishVoyage
import nl.teqplay.vesselvoyage.util.updateCurrentVisit
import nl.teqplay.vesselvoyage.util.updateMatchingPortAreaVisit
import nl.teqplay.vesselvoyage.util.updatePreviousVoyageEndInformation
import java.time.Instant

class PortEndProcessor(
    config: EventProcessingProperties,
    private val infraService: InfraService,
    private val staticShipInfoService: StaticShipInfoService
) : PortBaseProcessor(config, infraService) {
    companion object {
        const val END_EVENT_IGNORED_ISSUE = "Port events end are only supported when a visit is ongoing."
        const val MISSING_UNLOCODE = "AreaEvent has no unlocode"
    }

    private val log = KotlinLogging.logger {}

    override fun getResultOnVisit(status: VisitShipStatus, event: AreaEvent, config: EventProcessingProperties, imo: Int): EventProcessingResult {
        val unlocode = event.area.unlocode ?: return ignoredEventResult(status, event, MISSING_UNLOCODE)

        val updatedVisit = status.visit.copy(
            portAreas = updateMatchingPortAreaVisit(status.visit.portAreas, event, unlocode)
        )

        if (updatedVisit.portAreas.all { isPassThrough(it, status.visit.esof?.stops, config) }) {
            // zero areas left over -> delete the visit and update the previous voyage (if any)
            val previousVoyage = status.previousVoyage
            if (previousVoyage != null) {
                log.debug {
                    "No areas left over in visit after filtering. " +
                        "Will delete visit ${status.visit._id}, " +
                        "and un-finish voyage ${previousVoyage._id} " +
                        "(imo: ${status.visit.imo})"
                }

                // change the previous, finished voyage into an unfinished voyage again
                val updatedPreviousVoyage = unfinishVoyage(previousVoyage, updatedVisit)

                // Note that we do not have the previousVoyage for VoyageShipStatus here, but this is not a problem:
                // the VoyageShipStatus.previousVoyage is only needed when finishing a visit and starting new voyage,
                // in that case we need to update the endPortIds of the previous voyage (see function startVoyage)
                return EventProcessingResult(
                    status = VoyageShipStatus(updatedPreviousVoyage, status.previousVisit, null),
                    changes = listOf(
                        Change(Action.DELETE, updatedVisit),
                        Change(Action.UPDATE, updatedPreviousVoyage)
                    )
                )
            } else {
                log.debug {
                    "No areas left over in visit after filtering. " +
                        "Will delete visit ${status.visit._id}, no previous voyage to un-finish"
                }

                return EventProcessingResult(
                    status = InitialShipStatus,
                    changes = listOf(
                        Change(Action.DELETE, status.visit)
                    )
                )
            }
        }

        if (updatedVisit == status.visit) {
            // event did not result in a change, that's odd...
            return EventProcessingResult(
                status = status,
                changes = listOf(),
                issues = listOf(
                    EventProcessingIssue(
                        eventId = event._id,
                        description = "End event does not match an area of the current visit. " +
                            "Ignoring event (" +
                            "imo: ${status.visit.imo}, " +
                            "visit ports: ${status.visit.portAreas.joinToStringTruncated(10) {it.portId }}, " +
                            "event port: $unlocode" +
                            ")"
                    )
                )
            )
        }

        // when one of the port areas is marked as a pass-through,
        // we need to move this area visit from portAreas to passThroughAreas in the visit,
        // and we need to update the end port(s) and end time of the previousVoyage according to the leftover port areas
        val (portAreas, passThroughAreas) = updatedVisit.portAreas.partition {
            !isPassThrough(it, updatedVisit.esof?.stops, config)
        }
        val updatedVisitShipStatus = updatePreviousVoyageEndInformation(
            status.copy(
                visit = status.visit
                    .copy(
                        portAreas = portAreas,
                        dest = getDestinationIfStillValid(status.visit, event)
                    )
                    .appendPassThroughAreas(passThroughAreas)
            )
        )

        val finished = portAreas.all { it.isFinished() }
        return if (finished) {
            // Do an extra combining of stops to fix potentially wrongly detected movement events
            val shipDimensions = staticShipInfoService.getShipRegisterInfoCacheByIMO(updatedVisit.imo)?.dimensions

            val actualStops = CalculateStops.tryToCombineStopsOnVisitComplete(updatedVisit.esof?.stops, shipDimensions)
                .map(Stop::calculateAndSetAccuracy)

            val actualUpdatedVisitShipStatus = updatedVisitShipStatus.copy(
                visit = updatedVisitShipStatus.visit.copy(esof = updatedVisitShipStatus.visit.esof?.copy(stops = actualStops))
            )

            // visit is finished, start a new voyage
            val newVoyageShipStatus = startVoyage(actualUpdatedVisitShipStatus, infraService::getAnchorage)

            EventProcessingResult(
                status = newVoyageShipStatus,
                changes = createChangeList(status, listOf(newVoyageShipStatus))
            )
        } else {
            // visit updated but not finished yet
            EventProcessingResult(
                status = updatedVisitShipStatus,
                changes = createChangeList(status, listOf(updatedVisitShipStatus))
            )
        }
    }

    override fun getResultOnVoyage(status: VoyageShipStatus, event: AreaEvent, config: EventProcessingProperties, imo: Int): EventProcessingResult {
        return ignoredEventResult(
            status = status,
            event = event,
            description = buildString {
                append("End event received but a voyage is already in progress. ")
                append("Ignoring event (imo: ${status.voyage.imo}, voyage ports: ${status.voyage.startPortIds}, event port: ${event.area.unlocode})")
            }
        )
    }

    override fun getResultOnInitial(status: InitialShipStatus, event: AreaEvent, config: EventProcessingProperties, imo: Int): EventProcessingResult {
        val unlocode = event.area.unlocode ?: return ignoredEventResult(status, event, MISSING_UNLOCODE)

        val shipStatus = startInitialVoyage(event, imo, unlocode)

        return EventProcessingResult(
            status = shipStatus,
            changes = createChangeList(null, shipStatus)
        )
    }

    override fun getResultOnVisit(
        status: NewVisitShipStatus,
        event: AreaEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return getResultOnVisit(status, event, updateTime, imo)
    }

    override fun getResultOnVisitAtEndEvent(
        status: NewVisitShipStatus,
        event: AreaEvent,
        areaId: String,
        updateTime: Instant,
    ): NewEventProcessingResult {
        val currentVisit = status.visit.entry
        val matchingOngoingAreaActivity = getFirstMatchingOngoingAreaActivity(currentVisit, areaId)

        // We can only handle the event when we have a matching ongoing area activity
        if (matchingOngoingAreaActivity == null) {
            return ignoredEventResult(
                status = status,
                event = event,
                description = "Currently known area activities don't match the area id or are already finished. (area id: $areaId)"
            )
        }

        val finishedPortActivity = updateEndOfAreaActivity(
            event = event,
            matchingAreaActivity = matchingOngoingAreaActivity
        )

        // To mark the port activity as a pass-through we need to check if the stop happened inside the port time range
        val stopTimes = currentVisit.stops.map { it.start.time to it.end?.time }

        val hasOverlappingStops = stopTimes.any { stopTime ->
            val (stopStartTime, stopEndTime) = stopTime

            val isOverlapping = isStopOverlappingPortTimes(
                portStartTime = finishedPortActivity.start.time,
                portEndTime = finishedPortActivity.end?.time!!,
                stopStartTime = stopStartTime,
                stopEndTime = stopEndTime
            )

            isOverlapping
        }

        val updatedVisit = if (!hasOverlappingStops) {
            val remainingPortActivities = currentVisit.portAreaActivities - matchingOngoingAreaActivity
            val updatedPassThroughPorts = currentVisit.passThroughPort + finishedPortActivity
            val shouldLimit = currentVisit.limited || updatedPassThroughPorts.size >= MAX_ALLOWED_ACTIVITIES

            currentVisit.copy(
                portAreaActivities = remainingPortActivities.toMutableList(),
                passThroughPort = updatedPassThroughPorts.take(MAX_ALLOWED_ACTIVITIES),
                limited = shouldLimit,
                updatedAt = updateTime
            )
        } else {
            // We are not a pass-through just handle this as we would with all other area activities
            updateEndOfMatchingActivity(event, currentVisit, matchingOngoingAreaActivity)
                .copy(updatedAt = updateTime)
        }

        return NewEventProcessingResult(
            status = status.updateCurrentVisit(updatedVisit),
            changes = listOf(
                VisitChange(Action.UPDATE, updatedVisit)
            )
        )
    }

    /**
     * Check if the provided stop times and port times are overlapping in any way
     */
    private fun isStopOverlappingPortTimes(
        portStartTime: Instant,
        portEndTime: Instant,
        stopStartTime: Instant,
        stopEndTime: Instant?
    ): Boolean {
        val stopStartedBeforePortVisit = stopStartTime <= portStartTime
        val stopStartedInsidePortVisit = !stopStartedBeforePortVisit && stopStartTime <= portEndTime

        // When the stop is still ongoing, we only need to check if it started inside the port visit
        if (stopEndTime == null) {
            return stopStartedInsidePortVisit
        }

        val stopEndedBeforeLeavingPort = stopEndTime <= portEndTime
        val stopEndedInsidePortVisit = stopEndedBeforeLeavingPort && stopEndTime > portStartTime

        return stopStartedInsidePortVisit ||
            stopEndedInsidePortVisit ||
            (stopStartedBeforePortVisit && !stopEndedBeforeLeavingPort)
    }

    override fun getResultOnVoyage(
        status: NewVoyageShipStatus,
        event: AreaEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return getResultForPreviousVisitOnVoyage(status, event, updateTime)
    }

    override fun getResultOnInitial(
        status: NewInitialShipStatus,
        event: AreaEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return ignoredEventResult(status, event, END_EVENT_IGNORED_ISSUE)
    }
}
