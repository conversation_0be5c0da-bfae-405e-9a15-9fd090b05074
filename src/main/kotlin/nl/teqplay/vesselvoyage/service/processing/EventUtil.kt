package nl.teqplay.vesselvoyage.service.processing

import nl.teqplay.aisengine.event.interfaces.ActualEvent
import nl.teqplay.aisengine.event.interfaces.EndEvent
import nl.teqplay.aisengine.event.interfaces.Event

object EventUtil {
    /**
     * Returns the actual time of the event.
     * If the event is an [ActualEvent], it returns the [ActualEvent.actualTime].
     * Otherwise, it returns the [Event.createdTime] because actualTime is not available.
     */
    fun <T : Event> T.actualTime() =
        when (this) {
            is ActualEvent -> this.actualTime
            else -> this.createdTime
        }

    fun Event.relatedEvent() = (this as? EndEvent)?.startEventId
}
