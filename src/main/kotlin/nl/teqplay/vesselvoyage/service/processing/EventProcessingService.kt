package nl.teqplay.vesselvoyage.service.processing

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.event.interfaces.AnchoredEvent
import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType.SHIP_TO_SHIP
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.interfaces.PredictedEvent
import nl.teqplay.aisengine.event.interfaces.ShipMovingEvent
import nl.teqplay.aisengine.event.interfaces.StopEvent
import nl.teqplay.aisengine.event.interfaces.UniqueBerthEvent
import nl.teqplay.aisengine.event.model.AisStatusChangedEvent
import nl.teqplay.aisengine.event.model.LockEtaEvent
import nl.teqplay.aisengine.event.model.PortcallPilotBoardingEtaEvent
import nl.teqplay.aisengine.event.model.TrueDestinationChangedEvent
import nl.teqplay.aisengine.event.model.hamis.HamisEtaEvent
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.annotation.ProfileRevents
import nl.teqplay.vesselvoyage.datasource.ProcessorLogDatasource
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.Action.CREATE
import nl.teqplay.vesselvoyage.model.Action.REVISE
import nl.teqplay.vesselvoyage.model.Action.UPDATE
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.EventProcessingResult
import nl.teqplay.vesselvoyage.model.NewEventProcessingResult
import nl.teqplay.vesselvoyage.model.ProcessorLog
import nl.teqplay.vesselvoyage.model.ShipStatus
import nl.teqplay.vesselvoyage.model.v2.ESoFChange
import nl.teqplay.vesselvoyage.model.v2.EntryId
import nl.teqplay.vesselvoyage.model.v2.NewChange
import nl.teqplay.vesselvoyage.model.v2.NewESoF
import nl.teqplay.vesselvoyage.model.v2.NewShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.VisitChange
import nl.teqplay.vesselvoyage.model.v2.VoyageChange
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.processing.EventUtil.actualTime
import nl.teqplay.vesselvoyage.service.processing.anchor.AnchorProcessor
import nl.teqplay.vesselvoyage.service.processing.anchorarea.AnchorAreaProcessor
import nl.teqplay.vesselvoyage.service.processing.approach.ApproachAreaProcessor
import nl.teqplay.vesselvoyage.service.processing.berth.UniqueBerthProcessor
import nl.teqplay.vesselvoyage.service.processing.destination.DestinationChangedProcessor
import nl.teqplay.vesselvoyage.service.processing.encounter.EncounterProcessor
import nl.teqplay.vesselvoyage.service.processing.eosp.EndOfSeaPassageProcessor
import nl.teqplay.vesselvoyage.service.processing.eta.EtaProcessor
import nl.teqplay.vesselvoyage.service.processing.lock.LockAreaProcessor
import nl.teqplay.vesselvoyage.service.processing.movement.MovementProcessor
import nl.teqplay.vesselvoyage.service.processing.pilot.PilotAreaProcessor
import nl.teqplay.vesselvoyage.service.processing.port.PortProcessor
import nl.teqplay.vesselvoyage.service.processing.shiptoship.ShipToShipTransferProcessor
import nl.teqplay.vesselvoyage.service.processing.status.StatusChangedProcessor
import nl.teqplay.vesselvoyage.service.processing.stop.StopProcessor
import nl.teqplay.vesselvoyage.service.processing.terminalmooring.TerminalMooringAreaProcessor
import org.springframework.stereotype.Service
import java.time.Instant
import kotlin.system.measureTimeMillis
import kotlin.time.DurationUnit
import kotlin.time.toDuration

@ProfileProcessing
@ProfileRevents
@Service
class EventProcessingService(
    private val anchorEventProcessor: AnchorProcessor,
    private val destinationChangedEventProcessor: DestinationChangedProcessor,
    private val encounterEventProcessor: EncounterProcessor,
    private val shipToShipTransferEventProcessor: ShipToShipTransferProcessor,
    private val etaEventProcessor: EtaProcessor,
    private val movementEventProcessor: MovementProcessor,
    private val portEventProcessor: PortProcessor,
    private val statusChangedEventProcessor: StatusChangedProcessor,
    private val uniqueBerthEventProcessor: UniqueBerthProcessor,
    private val endOfSeaPassageEventProcessor: EndOfSeaPassageProcessor,
    private val stopEventProcessor: StopProcessor,
    private val pilotAreaEventProcessor: PilotAreaProcessor,
    private val anchorAreaEventProcessor: AnchorAreaProcessor,
    private val terminalMooringAreaEventProcessor: TerminalMooringAreaProcessor,
    private val lockAreaEventProcessor: LockAreaProcessor,
    private val approachAreaEventProcessor: ApproachAreaProcessor,
    private val processorLogDatasource: ProcessorLogDatasource?,
    private val eventProcessingProperties: EventProcessingProperties
) {
    private val log = KotlinLogging.logger {}

    companion object {
        /**
         * The max duration a single event processing can take before we log a warning
         */
        private const val MAX_DURATION_EVENT_PROCESSING_IN_MILLI = 2_000
    }

    /**
     * Process an [Event] resulting in a [EventProcessingResult].
     *
     * @param shipStatus The current status of the ship at the time when the [event] was received.
     * @param event The [Event] we want to process.
     * @param enableTraceCalculations When true the visit and voyages will be enhanced by requesting ship traces.
     */
    fun onEvent(
        shipStatus: ShipStatus,
        event: Event,
        enableTraceCalculations: Boolean = true
    ): EventProcessingResult {
        val result: EventProcessingResult
        val processingDurationInMillis = measureTimeMillis {
            result = when (event) {
                is AnchoredEvent -> anchorEventProcessor.processEvent(shipStatus, event)
                is TrueDestinationChangedEvent -> destinationChangedEventProcessor.processEvent(shipStatus, event)
                is EncounterEvent ->
                    when (event.encounterType) {
                        SHIP_TO_SHIP -> ignoredEventResult(
                            status = shipStatus,
                            event = event,
                            description = "Ignoring unknown type of event $event"
                        )
                        else -> encounterEventProcessor.processEvent(shipStatus, event)
                    }
                is PredictedEvent -> etaEventProcessor.processEvent(shipStatus, event)
                is ShipMovingEvent -> movementEventProcessor.processEvent(shipStatus, event, enableTraceCalculations)
                is UniqueBerthEvent -> uniqueBerthEventProcessor.processEvent(shipStatus, event, enableTraceCalculations)
                is AreaEvent ->
                    when (event.area.type) {
                        AreaIdentifier.AreaType.PORT -> portEventProcessor.processEvent(shipStatus, event)
                        else -> ignoredEventResult(
                            status = shipStatus,
                            event = event,
                            description = "Ignoring unknown type of event $event"
                        )
                    }
                is AisStatusChangedEvent -> statusChangedEventProcessor.processEvent(
                    shipStatus,
                    event
                )
                // Event is not supported
                else -> ignoredEventResult(
                    status = shipStatus,
                    event = event,
                    description = "Ignoring unknown type of event $event"
                )
            }
        }

        if (processingDurationInMillis > MAX_DURATION_EVENT_PROCESSING_IN_MILLI) {
            val processingDuration = processingDurationInMillis
                .toDuration(DurationUnit.MILLISECONDS)

            log.warn { "[V1] Processing Event took ${processingDuration.inWholeSeconds} seconds, event = $event" }
        }

        return result
    }

    fun onEventAndBuffer(
        shipStatus: NewShipStatus,
        event: Event,
        processTime: Instant
    ): NewEventProcessingResult {
        // Remove any events that happened before the currently processed event, as the buffer is only used to make
        // sure events that happen at the same time can be processed out of order.
        val cleanedUpBuffer = shipStatus.eventBuffer.filterNot { bufferedEvent -> bufferedEvent.actualTime().isBefore(event.actualTime()) }
        shipStatus.eventBuffer = cleanedUpBuffer

        var result = onEvent(shipStatus, event, processTime)

        // If we ran into an issue, add to buffer to try again later.
        if (result.issues.isNotEmpty()) {
            result.status.eventBuffer = shipStatus.eventBuffer + event
        } else if (shipStatus.eventBuffer.isNotEmpty()) {
            // We could successfully process an event, now also process our buffer
            val changes = result.changes.toMutableList()
            val issues = mutableListOf<EventProcessingIssue>()
            shipStatus.eventBuffer.forEach { bufferedEvent ->
                result = onEvent(result.status, bufferedEvent, processTime)
                changes.addAll(result.changes)
                issues.addAll(result.issues)
            }
            result = result.copy(changes = changes, issues = issues)
        }

        val changes = squashChanges(result.changes)
        return result.copy(changes = changes)
    }

    fun onEvent(
        shipStatus: NewShipStatus,
        event: Event,
        processTime: Instant
    ): NewEventProcessingResult {
        var result: NewEventProcessingResult
        val processingDurationInMillis = measureTimeMillis {
            result = when (event) {
                is AnchoredEvent -> anchorEventProcessor.processEvent(shipStatus, event, processTime)
                is TrueDestinationChangedEvent -> destinationChangedEventProcessor.processEvent(
                    shipStatus,
                    event,
                    processTime
                )

                is EncounterEvent ->
                    when (event.encounterType) {
                        SHIP_TO_SHIP -> shipToShipTransferEventProcessor.processEvent(
                            shipStatus,
                            event,
                            processTime
                        )

                        else -> encounterEventProcessor.processEvent(shipStatus, event, processTime)
                    }
                is PredictedEvent ->
                    when (event) {
                        is PortcallPilotBoardingEtaEvent,
                        is HamisEtaEvent,
                        is LockEtaEvent -> etaEventProcessor.processEvent(shipStatus, event, processTime)
                        else -> ignoredEventResult(
                            status = shipStatus,
                            event = event,
                            description = "Ignoring unsupported type of PredictedEvent event $event"
                        )
                    }
                is ShipMovingEvent -> movementEventProcessor.processEvent(shipStatus, event, processTime)
                is UniqueBerthEvent -> uniqueBerthEventProcessor.processEvent(shipStatus, event, processTime)
                is AreaEvent -> when (event.area.type) {
                    AreaIdentifier.AreaType.PORT -> portEventProcessor.processEvent(shipStatus, event, processTime)
                    AreaIdentifier.AreaType.END_OF_SEA_PASSAGE -> endOfSeaPassageEventProcessor.processEvent(shipStatus, event, processTime)
                    AreaIdentifier.AreaType.PILOT_BOARDING_PLACE -> pilotAreaEventProcessor.processEvent(shipStatus, event, processTime)
                    AreaIdentifier.AreaType.ANCHOR -> anchorAreaEventProcessor.processEvent(shipStatus, event, processTime)
                    AreaIdentifier.AreaType.TERMINAL_MOORING_AREA -> terminalMooringAreaEventProcessor.processEvent(shipStatus, event, processTime)
                    AreaIdentifier.AreaType.LOCK -> lockAreaEventProcessor.processEvent(shipStatus, event, processTime)
                    AreaIdentifier.AreaType.APPROACH_POINT -> approachAreaEventProcessor.processEvent(shipStatus, event, processTime)
                    else -> ignoredEventResult(
                        status = shipStatus,
                        event = event,
                        description = "Ignoring unsupported type of AreaEvent $event"
                    )
                }
                is AisStatusChangedEvent -> statusChangedEventProcessor.processEvent(shipStatus, event, processTime)
                is StopEvent -> stopEventProcessor.processEvent(shipStatus, event, processTime)
                // Event is not supported
                else -> ignoredEventResult(
                    status = shipStatus,
                    event = event,
                    description = "Ignoring unknown type of event $event"
                )
            }
        }

        if (processingDurationInMillis > MAX_DURATION_EVENT_PROCESSING_IN_MILLI) {
            val processingDuration = processingDurationInMillis
                .toDuration(DurationUnit.MILLISECONDS)

            log.warn { "[V2] Processing Event took ${processingDuration.inWholeSeconds} seconds, event = $event" }
        }

        logResult(result, event, processTime, processingDurationInMillis)

        return result
    }

    /**
     * The supplied [changes] may have redundant [Action.UPDATE] calls where there exists
     * an [Action.CREATE] for the same resource, squashing the updates into the creation.
     */
    private fun squashChanges(changes: List<NewChange<*>>): List<NewChange<*>> {
        val changesOut = mutableListOf<NewChange<*>>()
        changes.forEach { change ->
            if (change.action != UPDATE) {
                changesOut.add(change)
            } else {
                val index = when (change) {
                    is ESoFChange -> changesOut.indexOfLast { it is ESoFChange && it.value._id == change.value._id }
                    is VisitChange -> changesOut.indexOfLast { it is VisitChange && it.value._id == change.value._id }
                    is VoyageChange -> changesOut.indexOfLast { it is VoyageChange && it.value._id == change.value._id }
                }
                if (index == -1) {
                    changesOut.add(change)
                } else {
                    val previousChange = changesOut[index]
                    changesOut[index] = when (previousChange) {
                        is ESoFChange -> previousChange.copy(value = change.value as NewESoF)
                        is VisitChange -> previousChange.copy(value = change.value as NewVisit)
                        is VoyageChange -> previousChange.copy(value = change.value as NewVoyage)
                    }
                }
            }
        }
        return changesOut
    }

    fun ignoredEventResult(status: ShipStatus, event: Event, description: String): EventProcessingResult {
        return EventProcessingResult(
            status = status,
            changes = emptyList(),
            issues = listOf(
                EventProcessingIssue(
                    eventId = event._id,
                    description = description
                )
            )
        )
    }

    fun ignoredEventResult(status: NewShipStatus, event: Event, description: String): NewEventProcessingResult {
        return NewEventProcessingResult(
            status = status,
            changes = emptyList(),
            issues = listOf(
                EventProcessingIssue(
                    eventId = event._id,
                    description = description
                )
            )
        )
    }

    private fun logResult(
        result: NewEventProcessingResult,
        event: Event,
        processingStartTime: Instant,
        processingDurationInMillis: Long
    ) {
        if (result.decision != null && processorLogDatasource != null && eventProcessingProperties.logResults) {
            processorLogDatasource.save(
                ProcessorLog(
                    imo = event.ship.imo ?: 0,
                    entryIds = result.changes.map { it.id() }.toSet(),
                    event = event,
                    origin = getVesselVoyageCallStack(),
                    decision = result.decision,
                    changes = result.changes.map { it.action.name + ":" + it.id() },
                    issues = result.issues.map { it.description },
                    durationMillis = processingDurationInMillis,
                    processingStart = processingStartTime
                )
            )
        }
    }

    // these are always in the call stack, so no use in logging them
    private val doNotLog = listOf("ImoLockService", "EventProcessingService", "EntryProcessingService")

    // in the call stack, search for classes with this partial qualifier
    private val vvQualifier = "nl.teqplay.vesselvoyage.service"

    /**
     * Return the VesselVoyage service class names in the current call stack.
     * This helps debugging where an event originated from.
     */
    private fun getVesselVoyageCallStack(): String {
        // wrap in another try-catch, we need to be really sure this doesn't throw an exception as it's only for
        // debugging
        return try {
            try {
                throw Exception()
            } catch (e: Exception) {
                // Return all class names from the service package to determine where this event originated from
                // Sorted from most recent to the least recent invocation
                e.stackTrace.mapNotNull { item ->
                    if (item.className.startsWith(vvQualifier) && doNotLog.none { item.className.contains(it) }) {
                        // Only return the class name, no need for the whole qualifier
                        item.className.substringAfterLast(".").substringBefore("$")
                    } else {
                        null
                    }
                }.toSet().joinToString("<-")
            }
        } catch (e: Throwable) {
            "(error while determining callstack)"
        }
    }

    private fun NewChange<*>.id() = when (this) {
        is ESoFChange -> this.value._id
        is VisitChange -> this.value._id
        is VoyageChange -> this.value._id
    }

    fun List<NewChange<*>>.findVisit(id: EntryId): VisitChange? =
        this.find { it is VisitChange && it.value._id == id } as? VisitChange

    fun List<NewChange<*>>.findVoyage(id: EntryId): VoyageChange? =
        this.find { it is VoyageChange && it.value._id == id } as? VoyageChange

    private fun logResultInequality(result: NewEventProcessingResult) {

        val squashedChanges = squashChanges(result.changes)
        when (val status = result.status) {
            is NewVisitShipStatus -> {
                // current visit
                val visit = status.visit.entry
                val squashedVisit = squashedChanges.findVisit(visit._id)?.value
                if (squashedVisit != null && visit != squashedVisit) {
                    log.debug { "Resulting ship status.visit does not equal last change! Full status: $status" }
                    log.debug { "existing: $visit" }
                    log.debug { "change  : $squashedVisit" }
                }

                // previous visit
                val previousVisit = status.previousVisit?.entry
                if (previousVisit != null) {
                    val squashedPreviousVisit = squashedChanges.findVisit(previousVisit._id)?.value
                    if (squashedPreviousVisit != null && previousVisit != squashedPreviousVisit) {
                        log.info {
                            "Resulting ship status.previousVisit does not equal last change! Full status: $status"
                        }
                        log.debug { "existing: $previousVisit" }
                        log.debug { "change  : $squashedPreviousVisit" }
                    }
                }
            }

            is NewVoyageShipStatus -> {
                // current voyage
                val voyage = status.voyage.entry
                val squashedVoyage = squashedChanges.findVoyage(voyage._id)?.value
                if (squashedVoyage != null && voyage != squashedVoyage) {
                    log.debug { "Resulting ship status.voyage does not equal last change! Full status: $status" }
                    log.debug { "existing: $voyage" }
                    log.debug { "change  : $squashedVoyage" }
                }

                // previous voyage
                val previousVoyage = status.previousVoyage?.entry
                val squashedPreviousVoyage = previousVoyage?.let { squashedChanges.findVoyage(it._id) }
                if (squashedPreviousVoyage != null && squashedPreviousVoyage.action in listOf(CREATE, UPDATE, REVISE)) {
                    if (previousVoyage != squashedPreviousVoyage.value) {
                        log.debug {
                            "Resulting ship status.previousVoyage does not equal last change! Full status: $status"
                        }
                        log.debug { "existing: $previousVoyage" }
                        log.debug { "change  : $squashedPreviousVoyage" }
                    }
                }
            }

            else -> {}
        }
    }
}
