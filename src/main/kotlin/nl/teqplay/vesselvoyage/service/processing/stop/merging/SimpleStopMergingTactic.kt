package nl.teqplay.vesselvoyage.service.processing.stop.merging

import nl.teqplay.vesselvoyage.model.v2.NewStop
import org.springframework.stereotype.Component
import java.time.Duration

@Component
class SimpleStopMergingTactic : StopMergingTactic {
    companion object {
        private val MERGING_MAX_TIME = Duration.ofMinutes(5)
    }

    override fun shouldMerge(mergeableStop: NewStop, newStop: NewStop): Boolean {
        val timeBetween = getTimeBetweenStops(mergeableStop, newStop) ?: return false
        return timeBetween < MERGING_MAX_TIME
    }
}
