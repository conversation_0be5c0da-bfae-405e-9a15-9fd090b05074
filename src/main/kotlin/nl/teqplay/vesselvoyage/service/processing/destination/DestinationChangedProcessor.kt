package nl.teqplay.vesselvoyage.service.processing.destination

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.event.model.TrueDestinationChangedEvent
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.annotation.ProfileRevents
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.Destination
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.EventProcessingResult
import nl.teqplay.vesselvoyage.model.InitialShipStatus
import nl.teqplay.vesselvoyage.model.NewEventProcessingResult
import nl.teqplay.vesselvoyage.model.ShipStatus
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.VisitChange
import nl.teqplay.vesselvoyage.model.v2.VoyageChange
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.processing.EventProcessor
import nl.teqplay.vesselvoyage.util.createChangeList
import nl.teqplay.vesselvoyage.util.getCurrentEntry
import nl.teqplay.vesselvoyage.util.updateEntryOrThrow
import org.springframework.stereotype.Service
import java.time.Instant
import java.time.ZoneOffset.UTC
import nl.teqplay.vesselvoyage.model.v2.Destination as DestinationV2

@ProfileProcessing
@ProfileRevents
@Service
class DestinationChangedProcessor(override val config: EventProcessingProperties) : EventProcessor<TrueDestinationChangedEvent> {
    private val log = KotlinLogging.logger {}

    override fun getResultOnVisit(
        status: VisitShipStatus,
        event: TrueDestinationChangedEvent,
        config: EventProcessingProperties,
        imo: Int
    ): EventProcessingResult =
        getResult(status, event, imo)

    override fun getResultOnVoyage(
        status: VoyageShipStatus,
        event: TrueDestinationChangedEvent,
        config: EventProcessingProperties,
        imo: Int
    ): EventProcessingResult =
        getResult(status, event, imo)

    override fun getResultOnInitial(
        status: InitialShipStatus,
        event: TrueDestinationChangedEvent,
        config: EventProcessingProperties,
        imo: Int
    ): EventProcessingResult =
        getResult(status, event, imo)

    private fun getResult(
        status: ShipStatus,
        event: TrueDestinationChangedEvent,
        imo: Int
    ): EventProcessingResult {
        val entry = status.getCurrentEntry()
        if (entry == null) {
            log.debug { "Ignoring DestinationChangedEvent for IMO $imo: no active visit or voyage" }
            return EventProcessingResult(
                status = status,
                changes = emptyList()
            )
        }

        val newTrueDestination = event.trueDestination
        val newDestination = event.newValue

        if (entry.dest?.trueDestination == newTrueDestination) {
            // no actual changes (can be that the AIS destination changed but not the trueDestination)
            return EventProcessingResult(
                status = status,
                changes = emptyList()
            )
        }

        if (newDestination == null) {
            // cannot process the event: missing essential information
            return EventProcessingResult(
                status = status,
                changes = emptyList(),
                issues = listOf(
                    EventProcessingIssue(
                        eventId = event._id,
                        description = "Cannot update destination: required field 'newDestination' is undefined"
                    )
                )
            )
        }

        val existingEta = entry.eta
        val updatedDest = Destination(
            updatedAt = event.actualTime.atZone(UTC),
            aisDestination = newDestination,
            trueDestination = newTrueDestination
        )

        // clear eta when destination doesn't match anymore
        val updatedEta = if (existingEta != null && existingEta.portId != newTrueDestination) {
            log.debug { "Clearing ETA because destination changed (IMO: ${entry.imo}, new destination: $newTrueDestination)" }
            null
        } else {
            // leave as is (Can be null or an ETA)
            existingEta
        }

        log.debug { "Updated destination of ship with IMO ${entry.imo}. New destination: \"${updatedDest.trueDestination}\"" }

        val updatedShipStatus = status.updateEntryOrThrow(
            updateVisit = { it.copy(dest = updatedDest, eta = updatedEta) },
            updateVoyage = { it.copy(dest = updatedDest, eta = updatedEta) }
        )

        return EventProcessingResult(
            status = updatedShipStatus,
            changes = createChangeList(status, updatedShipStatus)
        )
    }

    override fun getResultOnVisit(
        status: NewVisitShipStatus,
        event: TrueDestinationChangedEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        val updatedVisit = status.visit.entry.copy(
            destination = event.newValue?.let { newDestination ->
                DestinationV2(
                    time = event.actualTime,
                    ais = newDestination,
                    actual = event.trueDestination
                )
            },
            updatedAt = updateTime
        )
        return NewEventProcessingResult(
            status = status.copy(
                visit = status.visit.copy(entry = updatedVisit)
            ),
            changes = listOf(
                VisitChange(Action.UPDATE, updatedVisit)
            )
        )
    }

    override fun getResultOnVoyage(
        status: NewVoyageShipStatus,
        event: TrueDestinationChangedEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        val updatedVoyage = status.voyage.entry.copy(
            destination = event.newValue?.let { newDestination ->
                DestinationV2(
                    time = event.actualTime,
                    ais = newDestination,
                    actual = event.trueDestination
                )
            },
            updatedAt = updateTime
        )
        return NewEventProcessingResult(
            status = status.copy(
                voyage = status.voyage.copy(entry = updatedVoyage)
            ),
            changes = listOf(
                VoyageChange(Action.UPDATE, updatedVoyage)
            )
        )
    }

    override fun getResultOnInitial(
        status: NewInitialShipStatus,
        event: TrueDestinationChangedEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return notUsedForNewDefinition(status)
    }
}
