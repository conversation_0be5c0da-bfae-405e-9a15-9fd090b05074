package nl.teqplay.vesselvoyage.service.processing.berth

import nl.teqplay.aisengine.event.interfaces.UniqueBerthEvent
import nl.teqplay.vesselvoyage.logic.stop.tryMergeWithBerthEvent
import nl.teqplay.vesselvoyage.model.ESof
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.EventProcessingResult
import nl.teqplay.vesselvoyage.model.InitialShipStatus
import nl.teqplay.vesselvoyage.model.MAX_BERTH_VISITS
import nl.teqplay.vesselvoyage.model.ShipStatus
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.AisFetchingService
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.processing.ActivityEventProcessor

abstract class UniqueBerthBaseProcessor(
    override val config: EventProcessingProperties,
    private val infraService: InfraService,
    private val aisFetchingService: AisFetchingService
) : ActivityEventProcessor<UniqueBerthEvent>(
    activitiesField = NewVisit::berthAreaActivities,
    areaIdField = UniqueBerthEvent::area
) {

    companion object {
        const val MISSING_IMO = "Event has no IMO"
    }

    override fun isValid(status: ShipStatus, event: UniqueBerthEvent, imo: Int): EventProcessingIssue? {
        val issue = super.isValid(status, event, imo)

        // Do some extra validation if we have a VisitShipStatus
        if (issue == null && status is VisitShipStatus) {
            // protect against the visit growing infinitely large
            if (status.visit.berthAreas.size >= MAX_BERTH_VISITS) {
                return EventProcessingIssue(
                    eventId = event._id,
                    description = "Cannot process UniqueBerthEvent: too many berth areas in current visit. " +
                        "Will ignore the event (imo: $imo, berthAreas: ${status.visit.berthAreas.size})"
                )
            }
        }

        return issue
    }

    fun processEvent(status: ShipStatus, event: UniqueBerthEvent, enableTraceCalculations: Boolean): EventProcessingResult {
        val imo = event.ship.imo ?: return ignoredEventResult(status, event, MISSING_IMO)

        val validationIssue = validateEvent(status, event, imo)

        if (validationIssue != null) {
            return validationIssue
        }

        val actualConfig = config.copy(enableTraceCalculations = enableTraceCalculations)

        return getProcessingResult(status, event, actualConfig, imo)
    }

    override fun getResultOnVoyage(status: VoyageShipStatus, event: UniqueBerthEvent, config: EventProcessingProperties, imo: Int): EventProcessingResult {
        return noActiveVisitResult(status, event, imo)
    }

    override fun getResultOnInitial(
        status: InitialShipStatus,
        event: UniqueBerthEvent,
        config: EventProcessingProperties,
        imo: Int
    ): EventProcessingResult {
        return noActiveVisitResult(status, event, imo)
    }

    private fun noActiveVisitResult(status: ShipStatus, event: UniqueBerthEvent, imo: Int): EventProcessingResult {
        return ignoredEventResult(
            status, event,
            buildString {
                append("Cannot process UniqueBerthEvent: no visit active ")
                append("(imo: $imo, portId: ${event.area.unlocode}, berthId: ${event.area.id})")
            }
        )
    }

    /**
     * Update the [ESof] of our [currentVisit] with the provided [event] by merging the currently known stops
     *  with the berth event we are processing.
     */
    protected fun updateEsof(
        currentVisit: Visit,
        event: UniqueBerthEvent,
        config: EventProcessingProperties,
        berthId: String
    ): ESof? {
        val visitStops = currentVisit.esof?.stops ?: emptyList()

        val getStopTraceFunction = aisFetchingService::getTraceForStopDetection.takeIf { config.enableTraceCalculations }
            ?: { _, _ -> emptyList() }

        // Only merge the berth event with the stops if we have any
        return if (visitStops.isNotEmpty()) {
            val mergedStops = visitStops.tryMergeWithBerthEvent(
                event,
                infraService::getBerth,
                currentVisit,
                getStopTraceFunction,
                infraService::getBerthsByLocation,
                infraService::getAnchoragesByLocation,
                berthId
            )

            currentVisit.esof?.copy(stops = mergedStops)
        } else {
            currentVisit.esof
        }
    }
}
