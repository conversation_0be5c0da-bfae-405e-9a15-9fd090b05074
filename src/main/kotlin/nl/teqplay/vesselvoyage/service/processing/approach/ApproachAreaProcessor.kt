package nl.teqplay.vesselvoyage.service.processing.approach

import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.annotation.ProfileRevents
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.processing.StartEndEventProcessor
import org.springframework.stereotype.Service

@ProfileProcessing
@ProfileRevents
@Service
class ApproachAreaProcessor(
    config: EventProcessingProperties
) : StartEndEventProcessor<AreaEvent, ApproachAreaStartProcessor, ApproachAreaEndProcessor>(
    startProcessor = ApproachAreaStartProcessor(config),
    endProcessor = ApproachAreaEndProcessor(config)
)
