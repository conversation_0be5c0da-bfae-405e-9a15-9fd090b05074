package nl.teqplay.vesselvoyage.service.processing.berth

import nl.teqplay.aisengine.event.interfaces.EndEvent
import nl.teqplay.aisengine.event.interfaces.StartEvent
import nl.teqplay.aisengine.event.interfaces.UniqueBerthEvent
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.annotation.ProfileRevents
import nl.teqplay.vesselvoyage.model.EventProcessingResult
import nl.teqplay.vesselvoyage.model.ShipStatus
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.AisFetchingService
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.processing.StartEndEventProcessor
import org.springframework.stereotype.Service

@ProfileProcessing
@ProfileRevents
@Service
class UniqueBerthProcessor(
    config: EventProcessingProperties,
    infraService: InfraService,
    aisFetchingService: AisFetchingService
) : StartEndEventProcessor<UniqueBerthEvent, UniqueBerthStartProcessor, UniqueBerthEndProcessor>(
    startProcessor = UniqueBerthStartProcessor(config, infraService, aisFetchingService),
    endProcessor = UniqueBerthEndProcessor(config, infraService, aisFetchingService)
) {
    fun processEvent(
        status: ShipStatus,
        event: UniqueBerthEvent,
        enableTraceCalculations: Boolean
    ): EventProcessingResult {
        return when (event) {
            is StartEvent -> startProcessor.processEvent(status, event, enableTraceCalculations)
            is EndEvent -> endProcessor.processEvent(status, event, enableTraceCalculations)
            else -> ignoredEventResult(status, event, "Invalid event type")
        }
    }
}
