package nl.teqplay.vesselvoyage.service.processing.shiptoship

import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.skeleton.metrics.MetricRegistry
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.annotation.ProfileRevents
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.processing.StartEndEventProcessor
import org.springframework.stereotype.Service

@ProfileProcessing
@ProfileRevents
@Service
class ShipToShipTransferProcessor(
    config: EventProcessingProperties,
    meterRegistry: MeterRegistry
) : StartEndEventProcessor<EncounterEvent, ShipToShipTransferStartProcessor,
    ShipToShipTransferEndProcessor>(
    startProcessor = ShipToShipTransferStartProcessor(config),
    endProcessor = ShipToShipTransferEndProcessor(config),
    metricRegistry = MetricRegistry(ShipToShipTransferProcessor::class, meterRegistry, listOf(TAG_EVENT_TYPE_KEY))
)
