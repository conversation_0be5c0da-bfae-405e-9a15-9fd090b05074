package nl.teqplay.vesselvoyage.service.processing

import nl.teqplay.aisengine.event.interfaces.EndEvent
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.interfaces.StartEvent
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.ESof
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.EventProcessingResult
import nl.teqplay.vesselvoyage.model.InitialShipStatus
import nl.teqplay.vesselvoyage.model.NewEventProcessingResult
import nl.teqplay.vesselvoyage.model.ShipStatus
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.processing.EventUtil.actualTime
import nl.teqplay.vesselvoyage.util.getAllEntries
import nl.teqplay.vesselvoyage.util.getEndOrStart
import nl.teqplay.vesselvoyage.util.getShipImo
import nl.teqplay.vesselvoyage.util.mergeEsofs
import java.time.Instant
import java.time.ZoneOffset

interface EventProcessor<in T : Event> {
    val config: EventProcessingProperties

    companion object {
        /**
         * Entries with a lot of data can potentially hit mongo limits and nats max message size. Use 50 as a hard value to avoid this.
         */
        const val MAX_ALLOWED_ACTIVITIES = 50
    }

    /**
     * Validate if the provided event should be processed.
     *
     * @param event The event we want to process.
     * @return A processing issue if the [event] is not valid.
     */
    fun isValid(status: ShipStatus, event: T, imo: Int): EventProcessingIssue? {
        val shipImo = when (status) {
            is VisitShipStatus -> { status.visit.imo }
            is VoyageShipStatus -> { status.voyage.imo }
            is InitialShipStatus -> null
        }
        if (shipImo != null && shipImo != imo.toString()) {
            return EventProcessingIssue(
                eventId = event._id,
                description = "Event has mismatching IMO: ship status has IMO $shipImo, event has IMO $imo. " +
                    "New event will be ignored (eventId: ${event._id})"
            )
        }

        val latestEventTime = when (status) {
            is VisitShipStatus -> { status.visit.endTime ?: status.visit.startTime }
            is VoyageShipStatus -> { status.voyage.endTime ?: status.voyage.startTime }
            is InitialShipStatus -> null
        }

        val actualEventTime = event.actualTime().atZone(ZoneOffset.UTC)

        if (latestEventTime != null && actualEventTime < latestEventTime) {
            // It is important to allow event time equal to the previous time:
            // sometimes it happens that two start events occur at the same time
            // when there are overlapping port areas for example
            return EventProcessingIssue(
                eventId = event._id,
                description = "Event time is older than the latest event. " +
                    "New event will be ignored (eventId: ${event._id})"
            )
        }

        return null
    }

    /**
     * Validate if the provided event should be processed.
     *
     * @param event The event we want to process
     * @return A processing issue if the [event] is not valid.
     */
    fun isValid(status: NewShipStatus, event: T, imo: Int): EventProcessingIssue? {
        return isValidImo(status, event, imo)
            ?: isValidEventTime(status, event)
    }

    fun isValidImo(status: NewShipStatus, event: T, imo: Int): EventProcessingIssue? {
        val shipImo = status.getShipImo()
        if (shipImo != null && shipImo != imo) {
            return EventProcessingIssue(
                eventId = event._id,
                description = "Event has mismatching IMO: ship status has IMO $shipImo, event has IMO $imo. " +
                    "New event will be ignored (eventId: ${event._id})"
            )
        }

        return null
    }

    fun isValidEventTime(status: NewShipStatus, event: T): EventProcessingIssue? {
        val latestEventTime = status.getEndOrStart()?.time
        val actualEventTime = event.actualTime()

        if (latestEventTime != null && actualEventTime < latestEventTime) {
            // It is important to allow event time equal to the previous time:
            // sometimes it happens that two start events occur at the same time
            // when there are overlapping port areas for example
            return EventProcessingIssue(
                eventId = event._id,
                description = "Event time is older than the latest event. " +
                    "New event will be ignored (eventId: ${event._id})"
            )
        }

        return null
    }

    /**
     * Process an [event] potentially mutating the Visit ship status.
     *
     * @param status The current status of the ship.
     * @param event The event we want to process.
     * @param issues The issues that were created while processing [event].
     * @return All the changes that happened when processing [event].
     */
    fun getResultOnVisit(status: VisitShipStatus, event: T, config: EventProcessingProperties, imo: Int): EventProcessingResult

    fun getResultOnVisit(status: NewVisitShipStatus, event: T, config: EventProcessingProperties, updateTime: Instant, imo: Int): NewEventProcessingResult

    /**
     * Process an [event] potentially mutating the Voyage ship status.
     *
     * @param status The current status of the ship.
     * @param event The event we want to process.
     * @param issues The issues that were created while processing [event].
     * @return All the changes that happened when processing [event].
     */
    fun getResultOnVoyage(status: VoyageShipStatus, event: T, config: EventProcessingProperties, imo: Int): EventProcessingResult

    fun getResultOnVoyage(status: NewVoyageShipStatus, event: T, config: EventProcessingProperties, updateTime: Instant, imo: Int): NewEventProcessingResult

    /**
     * Process an [event] potentially mutating the Initial ship status.
     *
     * @param status The current status of the ship.
     * @param event The event we want to process.
     * @param issues The issues that were created while processing [event].
     * @return All the changes that happened when processing [event].
     */
    fun getResultOnInitial(status: InitialShipStatus, event: T, config: EventProcessingProperties, imo: Int): EventProcessingResult

    fun getResultOnInitial(status: NewInitialShipStatus, event: T, config: EventProcessingProperties, updateTime: Instant, imo: Int): NewEventProcessingResult

    fun getProcessingResult(
        status: ShipStatus,
        event: T,
        config: EventProcessingProperties,
        imo: Int
    ): EventProcessingResult {
        return when (status) {
            is VisitShipStatus -> getResultOnVisit(status, event, config, imo)
            is VoyageShipStatus -> getResultOnVoyage(status, event, config, imo)
            is InitialShipStatus -> getResultOnInitial(status, event, config, imo)
        }
    }

    fun getProcessingResult(
        status: NewShipStatus,
        event: T,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        val result = when (status) {
            is NewVisitShipStatus -> getResultOnVisit(status, event, config, updateTime, imo)
            is NewVoyageShipStatus -> getResultOnVoyage(status, event, config, updateTime, imo)
            is NewInitialShipStatus -> getResultOnInitial(status, event, config, updateTime, imo)
        }
        return if (result.decision != null) {
            result.copy(origin = buildOriginLog(event))
        } else {
            result
        }
    }

    fun validateEvent(status: ShipStatus, event: T, imo: Int): EventProcessingResult? {
        val validationIssue = isValid(status, event, imo)

        if (validationIssue != null) {
            return EventProcessingResult(
                status = status,
                changes = emptyList(),
                issues = listOf(validationIssue)
            )
        }

        return null
    }

    fun validateEvent(status: NewShipStatus, event: T, imo: Int): NewEventProcessingResult? {
        val validationIssue = isValid(status, event, imo)

        if (validationIssue != null) {
            return NewEventProcessingResult(
                status = status,
                changes = emptyList(),
                issues = listOf(validationIssue)
            )
        }

        return null
    }

    fun processEvent(status: ShipStatus, event: T): EventProcessingResult {
        val imo = event.ship.imo ?: return ignoredEventResult(status, event, "Event has no IMO")

        val validationIssue = validateEvent(status, event, imo)

        if (validationIssue != null) {
            return validationIssue
        }

        return getProcessingResult(status, event, config, imo)
    }

    fun processEvent(status: NewShipStatus, event: T, updateTime: Instant): NewEventProcessingResult {
        val imo = event.ship.imo ?: return ignoredEventResult(status, event, "Event has no IMO")

        val validationIssue = validateEvent(status, event, imo)

        if (validationIssue != null) {
            return validationIssue
        }

        return getProcessingResult(status, event, config, updateTime, imo)
    }

    /**
     * Do special handling when the current status is limited.
     */
    fun handleAsLimitedStatus(
        status: NewShipStatus,
        eventProcessingResult: NewEventProcessingResult,
        event: T
    ): NewEventProcessingResult? {
        val currentEntry = when (status) {
            is NewVisitShipStatus -> { status.visit.entry }
            is NewVoyageShipStatus -> { status.voyage.entry }
            is NewInitialShipStatus -> null
        }

        // If we're limited, we only allow if there were any creates/deletes/etc.
        // to allow us getting out of the limited state.
        val onlyHasUpdates = eventProcessingResult.changes.all { it.action == Action.UPDATE }
        if (currentEntry != null && currentEntry.limited && onlyHasUpdates) {
            return ignoredEventResult(status, event, "Could not create any new activities because the current entry is already limited.")
        }

        // We are not limited, just handle as normal
        return null
    }

    fun ignoredEventResult(status: ShipStatus, event: T, description: String): EventProcessingResult {
        return EventProcessingResult(
            status = status,
            changes = emptyList(),
            issues = listOf(
                EventProcessingIssue(
                    eventId = event._id,
                    description = description
                )
            )
        )
    }

    fun ignoredEventResult(status: NewShipStatus, event: T, description: String): NewEventProcessingResult {
        return NewEventProcessingResult(
            status = status,
            changes = emptyList(),
            issues = listOf(
                EventProcessingIssue(
                    eventId = event._id,
                    description = description
                )
            )
        )
    }

    fun emptyEventResult(status: ShipStatus): EventProcessingResult {
        return EventProcessingResult(
            status = status,
            changes = emptyList()
        )
    }

    /**
     * Indicate that the event is not used for the new definition, so it can be fully ignored.
     */
    fun notUsedForNewDefinition(status: NewShipStatus): NewEventProcessingResult {
        return NewEventProcessingResult(
            status = status,
            changes = emptyList()
        )
    }

    fun getMergedEsof(status: ShipStatus): ESof = status
        .getAllEntries()
        .mapNotNull { entry -> entry.esof }
        .mergeEsofs()

    private fun buildOriginLog(event: T): String {
        return buildString {
            append(event::class.simpleName)
            if (event is StartEvent) {
                append(".")
                append("START")
            } else if (event is EndEvent) {
                append(".")
                append("END")
            }
            append(" id ")
            append(event._id)
        }
    }
}
