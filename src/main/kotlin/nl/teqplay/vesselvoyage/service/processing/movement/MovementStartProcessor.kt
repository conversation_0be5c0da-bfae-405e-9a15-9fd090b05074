package nl.teqplay.vesselvoyage.service.processing.movement

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.event.interfaces.ShipMovingEvent
import nl.teqplay.vesselvoyage.logic.CalculateStops
import nl.teqplay.vesselvoyage.logic.CalculateStops.Companion.fillPomaStop
import nl.teqplay.vesselvoyage.logic.stop.calculateAndSetAccuracy
import nl.teqplay.vesselvoyage.model.ESof
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.EventProcessingResult
import nl.teqplay.vesselvoyage.model.InitialShipStatus
import nl.teqplay.vesselvoyage.model.NewEventProcessingResult
import nl.teqplay.vesselvoyage.model.ShipStatus
import nl.teqplay.vesselvoyage.model.Stop
import nl.teqplay.vesselvoyage.model.StopEndDetectionInfo
import nl.teqplay.vesselvoyage.model.StopType
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.AisFetchingService
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.processing.ESofEventProcessor
import nl.teqplay.vesselvoyage.service.processing.EventUtil.actualTime
import nl.teqplay.vesselvoyage.util.isFinished
import nl.teqplay.vesselvoyage.util.restructureStops
import nl.teqplay.vesselvoyage.util.toVesselVoyageLocation
import java.time.Instant
import java.time.ZoneOffset

class MovementStartProcessor(
    override val config: EventProcessingProperties,
    private val infraService: InfraService,
    private val aisFetchingService: AisFetchingService
) : ESofEventProcessor<ShipMovingEvent>() {
    private val log = KotlinLogging.logger {}

    fun processEvent(status: ShipStatus, event: ShipMovingEvent, enableTraceCalculations: Boolean): EventProcessingResult {
        val imo = event.ship.imo ?: return ignoredEventResult(status, event, "Event has no IMO")

        val validationIssue = validateEvent(status, event, imo)

        if (validationIssue != null) {
            return validationIssue
        }

        val actualConfig = config.copy(enableTraceCalculations = enableTraceCalculations)

        return getProcessingResult(status, event, actualConfig, imo)
    }

    override fun getResultOnInitial(status: InitialShipStatus, event: ShipMovingEvent, config: EventProcessingProperties, imo: Int): EventProcessingResult {
        log.debug { "Ignoring MovementEvent for IMO $imo: no active visit or voyage" }
        return EventProcessingResult(status, emptyList())
    }

    override fun getUpdatedEsofOnEvent(mergedESof: ESof, event: ShipMovingEvent, config: EventProcessingProperties, imo: Int): Pair<ESof, EventProcessingIssue?> {
        log.debug { "Processing movement event (eventTime: ${event.actualTime})" }
        val lastAreaVisit = mergedESof.stops.lastOrNull()

        if (lastAreaVisit == null || lastAreaVisit.isFinished()) {
            val issue = EventProcessingIssue(
                eventId = event._id,
                description = "Unexpected movement start event: ship does not have a stopped status right now. " +
                    "Event will be ignored (imo: $imo)"
            )

            return Pair(mergedESof, issue)
        }

        // Finish the existing stop, so we can use it to find the ship trace
        val finishedStopViaMovementEvent = lastAreaVisit.copy(
            endEventId = event._id,
            endTime = event.actualTime.atZone(ZoneOffset.UTC),
            endLocation = event.location.toVesselVoyageLocation(),
            aisEnd = StopEndDetectionInfo(
                id = event._id,
                time = event.actualTime.atZone(ZoneOffset.UTC),
                location = event.location.toVesselVoyageLocation()
            )
        )

        // Only request traces when enabled
        val stopTraces = if (config.enableTraceCalculations) {
            aisFetchingService.getTraceForStopDetection(imo.toString(), finishedStopViaMovementEvent)
        } else {
            emptyList()
        }
        val fallbackStop = CalculateStops.fallbackOnOldStopHandling(
            oldStop = finishedStopViaMovementEvent,
            stopTraces = stopTraces,
            findBerthsByLocation = infraService::getBerthsByLocation,
            findAnchoragesByLocation = infraService::getAnchoragesByLocation
        )

        // Only use the new stop detection when enabled
        val resultStops = if (config.newStopDetection) {
            val firstStopTrace = stopTraces.firstOrNull()

            // We can't detect any stops without a trace so instead fallback to the old way of handling
            if (firstStopTrace == null) {
                log.debug { "Could not determine stops because no ship trace was found, using the old way of detecting (imo: $imo, startEventId: ${event._id})" }

                return Pair(replaceEsofLatestStop(mergedESof, lastAreaVisit, fallbackStop), null)
            }

            val actualStops = CalculateStops.findActualStops(finishedStopViaMovementEvent, stopTraces, event._id)
            val preparedActualStops = CalculateStops.prepareDetectedStops(
                detectedStops = actualStops,
                eventId = event._id,
                eventTime = event.actualTime.atZone(ZoneOffset.UTC),
                eventLocation = event.location.toVesselVoyageLocation(),
                findBerthsByLocation = infraService::getBerthsByLocation,
                findAnchoragesByLocation = infraService::getAnchoragesByLocation
            )
            val restructuredActualStops = restructureStops(preparedStops = preparedActualStops)

            // When no stops are found with the new detection method, fallback on the old way how we handle stops
            val newStops = if (restructuredActualStops.isEmpty()) {
                log.debug { "Could not determine stops via the ship trace, using the old way of detecting (imo: $imo, startEventId: ${event._id})" }
                fallbackStop
            } else if (restructuredActualStops.size > 1 && restructuredActualStops.none { it.type == StopType.BERTH }) {
                // Most anchor areas are handled correctly now, if this happens we need to improve the mechanism
                log.debug { "Detected more than one stop and none of them are at a Berth. Using old stop handling. (imo: $imo, startEventId: ${event._id})" }
                fallbackStop
            } else {
                restructuredActualStops
            }

            log.debug { "Found ${newStops.size} stops in stop trace (imo: $imo, startEventId: ${event._id})" }

            newStops
        } else {
            fallbackStop
        }

        return Pair(
            replaceEsofLatestStop(
                esof = mergedESof,
                oldStop = lastAreaVisit,
                newStops = resultStops.fillPomaStop(infraService::getBerthsByLocation, infraService::getAnchoragesByLocation)
                    .map(Stop::calculateAndSetAccuracy)
            ),
            null
        )
    }

    override fun getResultOnInitial(
        status: NewInitialShipStatus,
        event: ShipMovingEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return notUsedForNewDefinition(status)
    }

    override fun getResultOnVoyage(
        status: NewVoyageShipStatus,
        event: ShipMovingEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return notUsedForNewDefinition(status)
    }

    override fun getResultOnVisit(
        status: NewVisitShipStatus,
        event: ShipMovingEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return notUsedForNewDefinition(status)
    }

    /**
     * Helper function to replace the [oldStop] of the [ESof] with one or more [newStops].
     */
    private fun replaceEsofLatestStop(esof: ESof, oldStop: Stop, newStops: List<Stop>): ESof {
        return esof.copy(
            stops = (esof.stops - oldStop) + newStops
        )
    }
}
