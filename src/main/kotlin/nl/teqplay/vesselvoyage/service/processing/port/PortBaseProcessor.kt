package nl.teqplay.vesselvoyage.service.processing.port

import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.ShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.processing.ActivityEventProcessor

abstract class PortBaseProcessor(
    override val config: EventProcessingProperties,
    private val infraService: InfraService,
) : ActivityEventProcessor<AreaEvent>(
    activitiesField = NewVisit::portAreaActivities,
    areaIdField = AreaEvent::area
) {
    override fun isValid(status: ShipStatus, event: AreaEvent, imo: Int): EventProcessingIssue? {
        val validationIssue = super.isValid(status, event, imo)

        if (validationIssue != null) {
            return validationIssue
        }

        // validate whether this is a known, valid port.
        // Filter out wrong events like DEBRVR (that should have been DEBRV)
        val pomaPort = event.area.unlocode?.let(infraService::getPortByUnlocode)

        if (pomaPort != null) {
            // We found a port so the event is valid
            return null
        }

        return EventProcessingIssue(
            eventId = event._id,
            description = buildString {
                append("Unknown port id \"${event.area.id}\" in PortEvent. ")
                append("Will ignore the event (imo: $imo)")
            }
        )
    }
}
