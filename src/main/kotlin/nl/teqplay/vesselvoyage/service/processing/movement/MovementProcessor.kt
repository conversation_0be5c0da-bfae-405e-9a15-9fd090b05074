package nl.teqplay.vesselvoyage.service.processing.movement

import nl.teqplay.aisengine.event.interfaces.EndEvent
import nl.teqplay.aisengine.event.interfaces.ShipMovingEvent
import nl.teqplay.aisengine.event.interfaces.StartEvent
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.annotation.ProfileRevents
import nl.teqplay.vesselvoyage.model.EventProcessingResult
import nl.teqplay.vesselvoyage.model.ShipStatus
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.AisFetchingService
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.processing.StartEndEventProcessor
import org.springframework.stereotype.Service

@ProfileProcessing
@ProfileRevents
@Service
class MovementProcessor(
    config: EventProcessingProperties,
    infraService: InfraService,
    aisFetchingService: AisFetchingService
) : StartEndEventProcessor<ShipMovingEvent, MovementStartProcessor, MovementEndProcessor>(
    startProcessor = MovementStartProcessor(config, infraService, aisFetchingService),
    endProcessor = MovementEndProcessor(config)
) {
    fun processEvent(
        status: ShipStatus,
        event: ShipMovingEvent,
        enableTraceCalculations: Boolean
    ): EventProcessingResult {
        return when (event) {
            is StartEvent -> startProcessor.processEvent(status, event, enableTraceCalculations)
            is EndEvent -> endProcessor.processEvent(status, event)
            else -> ignoredEventResult(status, event, "Unknown event type ${event.getType()}")
        }
    }
}
