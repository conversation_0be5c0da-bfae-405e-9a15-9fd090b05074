package nl.teqplay.vesselvoyage.service.processing.eta

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.event.interfaces.PredictedEvent
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.annotation.ProfileRevents
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.EventProcessingResult
import nl.teqplay.vesselvoyage.model.InitialShipStatus
import nl.teqplay.vesselvoyage.model.NewEventProcessingResult
import nl.teqplay.vesselvoyage.model.ShipStatus
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.processing.EventProcessor
import nl.teqplay.vesselvoyage.util.createChangeList
import nl.teqplay.vesselvoyage.util.getCurrentEntry
import nl.teqplay.vesselvoyage.util.toEta
import nl.teqplay.vesselvoyage.util.updateEntryOrThrow
import org.springframework.stereotype.Service
import java.time.Instant

@ProfileProcessing
@ProfileRevents
@Service
class EtaProcessor(
    override val config: EventProcessingProperties
) : EventProcessor<PredictedEvent> {
    val log = KotlinLogging.logger {}

    override fun getResultOnVisit(status: VisitShipStatus, event: PredictedEvent, config: EventProcessingProperties, imo: Int): EventProcessingResult {
        return updateStatusWithEta(status, event, imo)
    }

    override fun getResultOnVoyage(status: VoyageShipStatus, event: PredictedEvent, config: EventProcessingProperties, imo: Int): EventProcessingResult {
        return updateStatusWithEta(status, event, imo)
    }

    override fun getResultOnInitial(status: InitialShipStatus, event: PredictedEvent, config: EventProcessingProperties, imo: Int): EventProcessingResult {
        return emptyEventResult(status)
    }

    private fun updateStatusWithEta(shipStatus: ShipStatus, event: PredictedEvent, imo: Int): EventProcessingResult {
        val entry = shipStatus.getCurrentEntry()
        if (entry == null) {
            log.debug { "Ignoring PredictedEvent for IMO $imo: no active visit or voyage" }
            return EventProcessingResult(shipStatus, emptyList())
        }

        val eta = event.toEta()
        if (eta == null) {
            log.debug { "Ignoring PredictedEvent for IMO $imo: unsupported event type" }
            return EventProcessingResult(status = shipStatus, changes = emptyList())
        }

        if (eta == entry.eta) {
            // no actual change, ignore this
            log.debug { "Ignoring PredictedEvent of ship with IMO ${entry.imo}: nothing changed" }
            return EventProcessingResult(status = shipStatus, changes = emptyList())
        }

        if (eta.portId != entry.dest?.trueDestination) {
            return EventProcessingResult(
                status = shipStatus, changes = emptyList(),
                issues = listOf(
                    EventProcessingIssue(
                        eventId = event._id,
                        description = "Ignoring PredictedEvent of ship with IMO ${entry.imo}: " +
                            "the ETA's destination ${eta.portId} " +
                            "doesn't match the latest true destination ${entry.dest?.trueDestination}"
                    )
                )
            )
        }

        log.debug {
            "Updated eta of ship with IMO ${entry.imo}. " +
                "New eta: \"${eta.predictedTime}\""
        }

        val updatedShipStatus = shipStatus.updateEntryOrThrow(
            updateVisit = { it.copy(eta = eta) },
            updateVoyage = { it.copy(eta = eta) }
        )

        return EventProcessingResult(
            status = updatedShipStatus,
            changes = createChangeList(shipStatus, updatedShipStatus)
        )
    }

    override fun getResultOnVisit(
        status: NewVisitShipStatus,
        event: PredictedEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return notUsedForNewDefinition(status)
    }

    override fun getResultOnVoyage(
        status: NewVoyageShipStatus,
        event: PredictedEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return notUsedForNewDefinition(status)
    }

    override fun getResultOnInitial(
        status: NewInitialShipStatus,
        event: PredictedEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return notUsedForNewDefinition(status)
    }
}
