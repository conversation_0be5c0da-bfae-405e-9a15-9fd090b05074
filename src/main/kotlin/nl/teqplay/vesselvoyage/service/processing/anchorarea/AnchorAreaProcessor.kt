package nl.teqplay.vesselvoyage.service.processing.anchorarea

import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.annotation.ProfileRevents
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.processing.StartEndEventProcessor
import org.springframework.stereotype.Service

@ProfileProcessing
@ProfileRevents
@Service
class AnchorAreaProcessor(
    config: EventProcessingProperties
) : StartEndEventProcessor<AreaEvent, AnchorAreaStartProcessor, AnchorAreaEndProcessor>(
    startProcessor = AnchorAreaStartProcessor(config),
    endProcessor = AnchorAreaEndProcessor(config)
)
