package nl.teqplay.vesselvoyage.service.processing.eosp

import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polygon
import nl.teqplay.skeleton.util.pointInPolygon
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.NewEventProcessingResult
import nl.teqplay.vesselvoyage.model.internal.InfraAreaType
import nl.teqplay.vesselvoyage.model.lightweight.poma.Anchorage
import nl.teqplay.vesselvoyage.model.lightweight.poma.PilotBoardingPlace
import nl.teqplay.vesselvoyage.model.lightweight.poma.Port
import nl.teqplay.vesselvoyage.model.v2.AreaActivity
import nl.teqplay.vesselvoyage.model.v2.EntryESoFWrapper
import nl.teqplay.vesselvoyage.model.v2.FallbackType
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.VisitChange
import nl.teqplay.vesselvoyage.model.v2.VoyageChange
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.util.createNewVisit
import nl.teqplay.vesselvoyage.util.createNewVoyage
import nl.teqplay.vesselvoyage.util.finishVisit
import nl.teqplay.vesselvoyage.util.finishVoyage
import nl.teqplay.vesselvoyage.util.isOngoing
import nl.teqplay.vesselvoyage.util.mergeESoF
import nl.teqplay.vesselvoyage.util.resumeVisit
import java.time.Instant

class EndOfSeaPassageStartProcessor(
    override val config: EventProcessingProperties,
    private val infraService: InfraService
) : EndOfSeaPassageBaseProcessor(infraService) {

    companion object {
        const val START_EVENT_VISIT_ALREADY_INSIDE_EOSP_ISSUE = "Start event received but this visit is already inside this EOSP"
        const val EVENT_NO_AREA_ID = "Start event received but no area id available"
    }

    override fun getResultOnVisit(
        status: NewVisitShipStatus,
        event: AreaEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int,
    ): NewEventProcessingResult {
        val areaId = event.area.id ?: return ignoredEventResult(status, event, EVENT_NO_AREA_ID)

        val currentVisit = status.visit.entry

        // We can't process this EOSP event when we are already in this area
        if (currentVisit.eospAreaActivity.areaId == areaId ||
            currentVisit.otherOngoingEospAreaActivities.any { activity -> activity.areaId == areaId && activity.isOngoing() }
        ) {
            return ignoredEventResult(status, event, START_EVENT_VISIT_ALREADY_INSIDE_EOSP_ISSUE)
        }

        // check if we missed an end event of our previous visit
        val currentEospAreaId = currentVisit.eospAreaActivity.areaId
        val currentEospPort = infraService.getPortByAreaId(currentEospAreaId)
        val eospArea = currentEospPort?.eosArea

        // Check if we are still in our visit, we might have missed an EOSP event
        if (eospArea != null && !isShipStillInCurrentEosp(event.location, eospArea)) {
            return handleAsMissedEndEvent(status, event, currentVisit, currentEospPort, updateTime, imo, areaId)
        }

        return handleAsAddAdditionalOngoingEospToVisit(
            event = event,
            status = status,
            currentVisit = currentVisit,
            areaId = areaId
        )
    }

    private fun isShipStillInCurrentEosp(currentLocation: Location, eospArea: List<Location>): Boolean {
        val polygon = Polygon(eospArea)
        val isShipStillInEosp = pointInPolygon(polygon, currentLocation)

        return isShipStillInEosp
    }

    /**
     * In some cases we might have missed an end event when something went wrong on the AreaMonitor side, broker or VesselVoyage itself.
     *
     * - Finish all other ongoing that we also exited.
     * - Create a new Voyage of remaining EOSPs. Take the oldest already ongoing as our new main Visit.
     */
    private fun handleAsMissedEndEvent(
        status: NewVisitShipStatus,
        event: AreaEvent,
        currentVisit: NewVisit,
        currentEospPort: Port,
        updateTime: Instant,
        imo: Int,
        areaId: String
    ): NewEventProcessingResult {
        val newVisitStart = LocationTime(
            location = event.location,
            time = event.actualTime
        )

        val currentVisitFallbackEnd = determineFallbackEndOfEosp(
            missingEndPortId = currentVisit.eospAreaActivity.areaId.removeSuffix(".eosp"),
            missingEndPortUnlocode = currentEospPort.unlocode!!,
            newVisitStart = newVisitStart,
            currentVisit = currentVisit
        )

        if (currentVisit.otherOngoingEospAreaActivities.isNotEmpty()) {
            // We might have to close other EOSPs that we left or have to switch the visit to another port first
            val adjustedOtherOngoingEosps = currentVisit.otherOngoingEospAreaActivities.map { activity ->
                val otherOngoingEospPort = infraService.getPortByAreaId(activity.areaId)
                val eospArea = otherOngoingEospPort?.eosArea

                if (eospArea != null && !isShipStillInCurrentEosp(currentLocation = event.location, eospArea = eospArea)) {
                    val newEnd = determineFallbackEndOfEosp(
                        missingEndPortId = activity.areaId.removeSuffix(".eosp"),
                        missingEndPortUnlocode = otherOngoingEospPort.unlocode!!,
                        newVisitStart = newVisitStart,
                        currentVisit = currentVisit
                    )

                    activity.copy(end = newEnd)
                } else {
                    // EOSP is still ongoing, nothing to adjust
                    activity
                }
            }

            val (stillOngoingEosps, finishedEosps) = adjustedOtherOngoingEosps.partition { activity -> activity.end == null }

            val updatedCurrentVisit = currentVisit.copy(passThroughEosp = currentVisit.passThroughEosp + finishedEosps)

            if (stillOngoingEosps.isNotEmpty()) {
                // Create a visit for longest still ongoing EOSP as we didn't leave all EOSPs
                val longestOngoingEospActivity = stillOngoingEosps.minBy { activity -> activity.start.time }
                val leftoverOngoingEospActivities = stillOngoingEosps - longestOngoingEospActivity
                val newOverlappingVisitStart = longestOngoingEospActivity.start

                // As we had overlapping voyages it means that we always end up with a 0-second voyage
                return createResultOnMissedEndEvent(
                    status = status,
                    event = event,
                    currentVisit = updatedCurrentVisit,
                    currentVisitFallbackEnd = newOverlappingVisitStart,
                    newVisitStart = newOverlappingVisitStart,
                    updateTime = updateTime,
                    areaId = areaId,
                    imo = imo
                ) { inBetweenVoyage ->
                    // We don't want to use the id of the event because we are replacing the visit with an old already ongoing
                    val actualVoyageId = "${longestOngoingEospActivity.id}.VOYAGE"
                    val actualVisitId = "${longestOngoingEospActivity.id}.VISIT"
                    val correctedInBetweenVoyage = inBetweenVoyage.copy(
                        _id = actualVoyageId,
                        destinationPort = longestOngoingEospActivity.areaId
                    )
                    val newVisit = createNewVisit(
                        event = event,
                        currentVoyage = correctedInBetweenVoyage,
                        visitStart = newVisitStart,
                        updateTime = updateTime,
                        imo = imo,
                        areaId = areaId
                    )

                    // The EOSP activity we created by the event should be put on the other ongoing instead
                    val eventOtherOngoingEosp = newVisit.eospAreaActivity

                    val correctedNewVisit = newVisit.copy(
                        _id = actualVisitId,
                        start = longestOngoingEospActivity.start,
                        eospAreaActivity = longestOngoingEospActivity,
                        otherOngoingEospAreaActivities = leftoverOngoingEospActivities + eventOtherOngoingEosp
                    )

                    correctedNewVisit to correctedInBetweenVoyage
                }
            }
        }

        // We left all our EOSPs, so we can create our new visit without any other checks
        val currentVisitUnlo = currentVisit.eospAreaActivity.unlocode()
        return createResultOnMissedEndEvent(
            status = status,
            event = event,
            currentVisit = currentVisit,
            currentVisitFallbackEnd = currentVisitFallbackEnd,
            newVisitStart = newVisitStart,
            updateTime = updateTime,
            areaId = areaId,
            imo = imo
        ) { inBetweenVoyage ->
            // We just want to create a Visit here as the default behaviour via Event is sufficient
            createNewVisit(
                event = event,
                currentVoyage = inBetweenVoyage,
                visitStart = newVisitStart,
                updateTime = updateTime,
                imo = imo,
                areaId = areaId
            ) to inBetweenVoyage
        }.copy(
            decision = "Got EOSP start (${event.area.unlocode}), but missed EOSP end of current visit ($currentVisitUnlo)." +
                "Ending current visit with fallback end, adding voyage and starting new visit."
        )
    }

    private fun createResultOnMissedEndEvent(
        status: NewVisitShipStatus,
        event: AreaEvent,
        currentVisit: NewVisit,
        currentVisitFallbackEnd: LocationTime,
        newVisitStart: LocationTime,
        updateTime: Instant,
        areaId: String,
        imo: Int,
        onCreateNewVisit: (NewVoyage) -> Pair<NewVisit, NewVoyage>,
    ): NewEventProcessingResult {
        val inBetweenVoyage = createNewVoyage(
            event = event,
            currentVisit = currentVisit,
            voyageStart = currentVisitFallbackEnd,
            updateTime = updateTime,
            imo = imo,
            areaId = areaId
        ).copy(
            // Correct the origin port as we normally take the area id from the event
            originPort = currentVisit.eospAreaActivity.areaId
        )
        val (newVisit, adjustedInBetweenVoyage) = onCreateNewVisit(inBetweenVoyage)
        val finishedCurrentVisit = finishVisit(
            currentVisit = currentVisit,
            newVoyage = adjustedInBetweenVoyage,
            voyageStart = currentVisitFallbackEnd,
            updateTime = updateTime
        )
        val finishedInBetweenVoyage = finishVoyage(
            currentVoyage = adjustedInBetweenVoyage,
            newVisit = newVisit,
            visitStart = newVisitStart,
            updateTime = updateTime
        )

        return NewEventProcessingResult(
            status = status.copy(
                visit = EntryESoFWrapper(entry = newVisit, esof = null),
                previousVoyage = EntryESoFWrapper(entry = finishedInBetweenVoyage, esof = null),
                previousVisit = status.visit.copy(entry = finishedCurrentVisit)
            ),
            changes = listOf(
                VisitChange(Action.UPDATE, finishedCurrentVisit),
                VoyageChange(Action.CREATE, finishedInBetweenVoyage),
                VisitChange(Action.CREATE, newVisit)
            ),
            readyForPostProcessing = listOf(finishedCurrentVisit._id),
            decision = "Missed end event of current visit (${currentVisit._id}), " +
                "creating new voyage (${finishedInBetweenVoyage._id}) and new visit (${newVisit._id})"
        )
    }

    private fun determineFallbackEndOfEosp(
        missingEndPortId: String,
        missingEndPortUnlocode: String,
        newVisitStart: LocationTime,
        currentVisit: NewVisit
    ): LocationTime {
        val lastPilotAreaActivityOfPort = currentVisit.pilotAreaActivities.lastOrNull { activity ->
            infraService.getInfraPartOfPort<PilotBoardingPlace>(
                id = activity.areaId,
                unlocode = missingEndPortUnlocode,
                areaType = InfraAreaType.PILOT_BOARDING_PLACE
            ) != null
        }
        val lastAnchorAreaActivityOfPort = currentVisit.anchorAreaAreaActivities.lastOrNull { activity ->
            infraService.getInfraPartOfPort<Anchorage>(
                id = activity.areaId,
                unlocode = missingEndPortUnlocode,
                areaType = InfraAreaType.ANCHOR
            ) != null
        }

        val lastPortActivityOfPort = currentVisit.portAreaActivities
            .lastOrNull { activity -> activity.areaId == missingEndPortId }

        val latestEnd = maxOf(
            // Set the fallback already on all cases,
            // so we don't have to set it on them when we end up selecting one of the fields
            lastPilotAreaActivityOfPort?.end?.copy(fallback = FallbackType.ACTIVITY_END_BY_PILOT_AREA),
            lastAnchorAreaActivityOfPort?.end?.copy(fallback = FallbackType.ACTIVITY_END_BY_ANCHOR_AREA),
            lastPortActivityOfPort?.end?.copy(fallback = FallbackType.ACTIVITY_END_BY_PORT),
            compareBy { activityEnd -> activityEnd?.time }
        )

        // We could determine an end time via one of the area activities
        if (latestEnd != null) {
            return latestEnd
        }

        // We don't have any activity to fallback on, so we use the event time instead of our newly entered EOSP
        return newVisitStart.copy(fallback = FallbackType.ACTIVITY_END_BY_EOSP_START_EVENT)
    }

    private fun handleAsAddAdditionalOngoingEospToVisit(
        event: AreaEvent,
        status: NewVisitShipStatus,
        currentVisit: NewVisit,
        areaId: String
    ): NewEventProcessingResult {
        val newAreaActivity = AreaActivity(
            id = event._id,
            start = LocationTime(
                location = event.location,
                time = event.actualTime
            ),
            end = null,
            areaId = areaId
        )

        val updatedVisit = currentVisit.copy(
            otherOngoingEospAreaActivities = currentVisit.otherOngoingEospAreaActivities + newAreaActivity
        )
        val updatedShipStatus = status.copy(
            visit = status.visit.copy(entry = updatedVisit)
        )

        val decision = "Add ${event.area.unlocode} as other ongoing eosp (eventId=${event._id}). " +
            "Leading EOSP: ${updatedVisit.eospAreaActivity.unlocode()}"
        return NewEventProcessingResult(
            status = updatedShipStatus,
            changes = listOf(
                VisitChange(Action.UPDATE, updatedVisit)
            ),
            decision = decision
        )
    }

    override fun getResultOnVoyage(
        status: NewVoyageShipStatus,
        event: AreaEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        val areaId = event.area.id ?: return ignoredEventResult(status, event, EVENT_NO_AREA_ID)

        val currentVoyage = status.voyage.entry
        val visitStartLocationTime = LocationTime(
            location = event.location,
            time = event.actualTime
        )

        val previousVisit = status.previousVisit

        if (previousVisit != null) {
            // check if we only drifted outside the EOSP and we returned to the same port
            val isSameEosp = areaId == previousVisit.entry.eospAreaActivity.areaId

            // Check if we didn't enter the port yet, meaning we can ensure that we were drifting outside the port
            if (isSameEosp && previousVisit.entry.portAreaActivities.isEmpty()) {
                val resumedVisit = resumeVisit(currentVoyage, previousVisit.entry, updateTime)
                val resumedVisitWrapper = EntryESoFWrapper(entry = resumedVisit, esof = previousVisit.esof)
                val (mergedESoF, _) = mergeESoF(
                    newEntryId = resumedVisit._id,
                    visit = resumedVisitWrapper,
                    voyage = status.voyage,
                    updateTime = updateTime
                )
                return NewEventProcessingResult(
                    status = NewVisitShipStatus(
                        visit = EntryESoFWrapper(entry = resumedVisit, esof = mergedESoF),
                        previousVoyage = null,
                        previousVisit = null
                    ),
                    changes = listOf(
                        VisitChange(Action.UPDATE, resumedVisit),
                        VoyageChange(Action.DELETE, currentVoyage.copy(updatedAt = updateTime))
                    ),
                    decision = "Resuming last visit ${resumedVisit._id} (assume ship drifted outside EOSP), " +
                        "deleting current voyage ${currentVoyage._id}"
                )
            }
        }

        val newVisit = createNewVisit(
            event = event,
            currentVoyage = currentVoyage,
            visitStart = visitStartLocationTime,
            updateTime = updateTime,
            areaId = areaId,
            imo = imo
        )
        val updatedVoyage = finishVoyage(
            currentVoyage = currentVoyage,
            newVisit = newVisit,
            visitStart = visitStartLocationTime,
            updateTime = updateTime
        )

        val updateShipStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = newVisit, esof = null),
            previousVoyage = status.voyage.copy(entry = updatedVoyage),
            previousVisit = status.previousVisit
        )

        val voyageOriginUnlo = currentVoyage.originPort?.let(infraService::getPortByAreaId)?.unlocode
        return NewEventProcessingResult(
            status = updateShipStatus,
            changes = listOf(
                VoyageChange(Action.UPDATE, updatedVoyage),
                VisitChange(Action.CREATE, newVisit)
            ),
            readyForPostProcessing = listOf(updatedVoyage._id),
            decision = "Finish voyage (origin was $voyageOriginUnlo), start new visit (${event.area.unlocode})"
        )
    }

    override fun getResultOnInitial(
        status: NewInitialShipStatus,
        event: AreaEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int,
    ): NewEventProcessingResult {
        val areaId = event.area.id ?: return ignoredEventResult(status, event, EVENT_NO_AREA_ID)

        val visitStartLocationTime = LocationTime(
            location = event.location,
            time = event.actualTime
        )

        val newVisit = createNewVisit(
            event = event,
            currentVoyage = null,
            visitStart = visitStartLocationTime,
            updateTime = updateTime,
            imo = imo,
            areaId = areaId
        )
        val updateShipStatus = NewVisitShipStatus(
            visit = EntryESoFWrapper(entry = newVisit, esof = null),
            previousVoyage = null,
            previousVisit = null
        )

        return NewEventProcessingResult(
            status = updateShipStatus,
            changes = listOf(
                VisitChange(Action.CREATE, newVisit)
            ),
            decision = "Initial visit for this ship! Whoohoo!"
        )
    }
}
