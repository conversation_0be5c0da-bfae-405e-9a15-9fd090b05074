package nl.teqplay.vesselvoyage.service.processing.status

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.event.model.AisStatusChangedEvent
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.annotation.ProfileRevents
import nl.teqplay.vesselvoyage.logic.stop.calculateAndSetAccuracy
import nl.teqplay.vesselvoyage.model.ESof
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.EventProcessingResult
import nl.teqplay.vesselvoyage.model.InitialShipStatus
import nl.teqplay.vesselvoyage.model.NewEventProcessingResult
import nl.teqplay.vesselvoyage.model.ShipStatus
import nl.teqplay.vesselvoyage.model.Stop
import nl.teqplay.vesselvoyage.model.StopType
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.processing.ESofEventProcessor
import nl.teqplay.vesselvoyage.util.isEmpty
import nl.teqplay.vesselvoyage.util.isFinished
import org.springframework.stereotype.Service
import java.time.Instant

@ProfileProcessing
@ProfileRevents
@Service
class StatusChangedProcessor(
    override val config: EventProcessingProperties
) : ESofEventProcessor<AisStatusChangedEvent>() {
    private val log = KotlinLogging.logger {}

    override fun getResultOnVisit(status: VisitShipStatus, event: AisStatusChangedEvent, config: EventProcessingProperties, imo: Int): EventProcessingResult {
        return filterIrrelevantStatusEvents(status, event, imo)
            ?: super.getResultOnVisit(status, event, config, imo)
    }

    override fun getResultOnVoyage(
        status: VoyageShipStatus,
        event: AisStatusChangedEvent,
        config: EventProcessingProperties,
        imo: Int
    ): EventProcessingResult {
        return filterIrrelevantStatusEvents(status, event, imo)
            ?: super.getResultOnVoyage(status, event, config, imo)
    }

    private fun filterIrrelevantStatusEvents(status: ShipStatus, event: AisStatusChangedEvent, imo: Int): EventProcessingResult? {
        if (event.newValue != AisMessage.ShipStatus.MOORED && event.newValue != AisMessage.ShipStatus.AT_ANCHOR) {
            log.debug { "Ignoring MovementEvent for IMO $imo: no relevant status (newStatus: ${event.newValue})" }
            return emptyEventResult(status)
        }

        return null
    }

    override fun getResultOnInitial(
        status: InitialShipStatus,
        event: AisStatusChangedEvent,
        config: EventProcessingProperties,
        imo: Int
    ): EventProcessingResult {
        log.debug { "Ignoring MovementEvent for IMO $imo: no active visit or voyage" }
        return emptyEventResult(status)
    }

    override fun getUpdatedEsofOnEvent(mergedESof: ESof, event: AisStatusChangedEvent, config: EventProcessingProperties, imo: Int): Pair<ESof, EventProcessingIssue?> {
        val lastAreaVisit = mergedESof.stops.lastOrNull()
        if (mergedESof.isEmpty() || lastAreaVisit == null || lastAreaVisit.isFinished()) {
            val issue = EventProcessingIssue(
                eventId = event._id,
                description = "Unexpected status changed event: ship is currently not stopped. Event will be ignored " +
                    "(imo: $imo, newStatus: ${event.newValue})"
            )
            return Pair(mergedESof, issue)
        }

        val updatedMergedESof = mergedESof.copy(
            stops = mergedESof.stops
                .map(Stop::calculateAndSetAccuracy)
                .updateLastStop(event)
        )

        return Pair(updatedMergedESof, null)
    }

    private fun List<Stop>.updateLastStop(event: AisStatusChangedEvent): List<Stop> {
        return this.toMutableList().apply {
            val lastIndex = this.lastIndex
            val lastStop = this[lastIndex]
            val aisStatus = getStopTypeByAisStatus(event)

            // Only take the poma type if it was detected as a berth or anchor area stop
            val newType = lastStop.pomaType.takeUnless { it == StopType.UNCLASSIFIED } ?: aisStatus

            val updatedStop = lastStop.copy(
                type = newType,
                aisType = aisStatus,
            )

            this[lastIndex] = updatedStop
        }
    }

    private fun getStopTypeByAisStatus(event: AisStatusChangedEvent): StopType {
        return when (event.newValue) {
            AisMessage.ShipStatus.AT_ANCHOR -> StopType.ANCHOR_AREA
            AisMessage.ShipStatus.MOORED -> StopType.BERTH
            // This cannot happen in practice because we call this function after we've check if we are AT_ANCHOR or MOORED
            else -> StopType.UNCLASSIFIED
        }
    }
    override fun getResultOnInitial(
        status: NewInitialShipStatus,
        event: AisStatusChangedEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return notUsedForNewDefinition(status)
    }

    override fun getResultOnVoyage(
        status: NewVoyageShipStatus,
        event: AisStatusChangedEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return notUsedForNewDefinition(status)
    }

    override fun getResultOnVisit(
        status: NewVisitShipStatus,
        event: AisStatusChangedEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return notUsedForNewDefinition(status)
    }
}
