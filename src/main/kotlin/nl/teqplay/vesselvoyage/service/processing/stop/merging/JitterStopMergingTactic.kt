package nl.teqplay.vesselvoyage.service.processing.stop.merging

import nl.teqplay.vesselvoyage.model.v2.NewStop
import org.springframework.stereotype.Component
import java.time.Duration

@Component
class JitterStopMergingTactic : StopMergingTactic {
    companion object {
        private const val MERGING_JITTER_MAX_DISTANCE = 200
        private val MERGING_JITTER_MAX_TIME = Duration.ofHours(4)
    }

    override fun shouldMerge(mergeableStop: NewStop, newStop: NewStop): Boolean {
        val timeBetween = getTimeBetweenStops(mergeableStop, newStop) ?: return false
        val distanceBetween = getDistanceBetweenStops(mergeableStop, newStop) ?: return false

        return timeBetween < MERGING_JITTER_MAX_TIME && distanceBetween < MERGING_JITTER_MAX_DISTANCE
    }
}
