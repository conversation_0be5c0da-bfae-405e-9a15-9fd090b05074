package nl.teqplay.vesselvoyage.service.processing.anchor

import nl.teqplay.aisengine.event.interfaces.AnchoredEvent
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.Change
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.EventProcessingResult
import nl.teqplay.vesselvoyage.model.InitialShipStatus
import nl.teqplay.vesselvoyage.model.NewEventProcessingResult
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.util.getDestinationIfStillValid
import nl.teqplay.vesselvoyage.util.joinToStringTruncated
import nl.teqplay.vesselvoyage.util.updateMatchingAnchorAreaVisit
import java.time.Instant

class AnchorEndProcessor(config: EventProcessingProperties, infraService: InfraService) : AnchorBaseProcessor(config, infraService) {
    companion object {
        const val END_EVENT_IGNORED_ISSUE = "Anchor area events end are only supported when a visit is ongoing."
    }

    override fun getResultOnVisit(status: VisitShipStatus, event: AnchoredEvent, config: EventProcessingProperties, imo: Int): EventProcessingResult {
        val updatedVisit = status.visit.copy(
            anchorAreas = updateMatchingAnchorAreaVisit(status.visit.anchorAreas, event)
        )

        if (updatedVisit.anchorAreas == status.visit.anchorAreas) {
            return EventProcessingResult(
                status = status,
                changes = emptyList(),
                issues = listOf(
                    EventProcessingIssue(
                        eventId = event._id,
                        description = "Anchor end event does not match an anchor area of the current visit. " +
                            "Ignoring event (" +
                            "imo: ${status.visit.imo}, " +
                            "open anchor areas: ${
                            status.visit.anchorAreas.joinToStringTruncated(10) { it.anchorAreaId }
                            }, " +
                            "event area: ${event.area.name}" +
                            ")"
                    )
                )
            )
        }

        val (destinations, _) = getDestinations(event)

        val updatedVisitWithCorrectedDestination = updatedVisit.copy(
            dest = getDestinationIfStillValid(updatedVisit, destinations)
        )

        return EventProcessingResult(
            status = VisitShipStatus(updatedVisitWithCorrectedDestination, status.previousVoyage, status.previousVisit),
            changes = listOf(
                Change(Action.UPDATE, updatedVisitWithCorrectedDestination)
            )
        )
    }

    override fun getResultOnVoyage(status: VoyageShipStatus, event: AnchoredEvent, config: EventProcessingProperties, imo: Int): EventProcessingResult {
        return ignoredEventResult(
            status, event,
            buildString {
                append("Unexpected anchor end event: no corresponding anchor start event, ")
                append("and there is still an ongoing voyage. ")
                append("Will ignore this anchor event ")
                append("(imo: $imo, event area: ${event.area.name})")
            }
        )
    }

    override fun getResultOnInitial(status: InitialShipStatus, event: AnchoredEvent, config: EventProcessingProperties, imo: Int): EventProcessingResult {
        return ignoredEventResult(
            status, event,
            buildString {
                append("Unexpected anchor end event: no corresponding anchor start event, ")
                append("ship status is empty. ")
                append("Will ignore this anchor event ")
                append("(imo: $imo, event area: ${event.area.name})")
            }
        )
    }

    override fun getResultOnVisit(
        status: NewVisitShipStatus,
        event: AnchoredEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return getResultOnVisit(status, event, updateTime, imo)
    }

    override fun getResultOnVoyage(
        status: NewVoyageShipStatus,
        event: AnchoredEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return getResultForPreviousVisitOnVoyage(status, event, updateTime)
    }

    override fun getResultOnInitial(
        status: NewInitialShipStatus,
        event: AnchoredEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return ignoredEventResult(
            status = status,
            event = event,
            description = END_EVENT_IGNORED_ISSUE
        )
    }
}
