package nl.teqplay.vesselvoyage.service.processing.anchor

import nl.teqplay.aisengine.event.interfaces.AnchoredEvent
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.annotation.ProfileRevents
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.processing.StartEndEventProcessor
import org.springframework.stereotype.Service

@ProfileProcessing
@ProfileRevents
@Service
class AnchorProcessor(
    config: EventProcessingProperties,
    protected val infraService: InfraService
) : StartEndEventProcessor<AnchoredEvent, AnchorStartProcessor, AnchorEndProcessor>(
    startProcessor = AnchorStartProcessor(config, infraService),
    endProcessor = AnchorEndProcessor(config, infraService)
)
