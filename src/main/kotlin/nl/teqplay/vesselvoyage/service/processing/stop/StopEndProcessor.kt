package nl.teqplay.vesselvoyage.service.processing.stop

import nl.teqplay.aisengine.event.interfaces.StopEvent
import nl.teqplay.aisengine.event.model.StopEndEvent
import nl.teqplay.skeleton.model.Location
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.NewEventProcessingResult
import nl.teqplay.vesselvoyage.model.internal.ClassifiedStop
import nl.teqplay.vesselvoyage.model.v2.FallbackType
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewStop
import nl.teqplay.vesselvoyage.model.v2.NewStopType
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.VisitChange
import nl.teqplay.vesselvoyage.model.v2.VoyageChange
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.processing.EventUtil.actualTime
import nl.teqplay.vesselvoyage.service.processing.EventUtil.relatedEvent
import nl.teqplay.vesselvoyage.util.replaceFirst
import nl.teqplay.vesselvoyage.util.updateCurrentVisit
import nl.teqplay.vesselvoyage.util.updateCurrentVoyage
import java.time.Instant

class StopEndProcessor(
    override val config: EventProcessingProperties,
    private val infraService: InfraService
) : StopBaseProcessor(infraService = infraService) {

    companion object {
        const val MISSING_STOP_LOCATION = "StopEvent has no stop location"
    }

    /**
     * Get a [NewEventProcessingResult] for either a Visit or Voyage.
     *
     * @param status The current Visit/Voyage Ship Status.
     * @param currentEntry The currently ongoing Visit or Voyage.
     * @param previousEntry The entry that happened before the [currentEntry].
     * @param event The stop event we are currently processing.
     * @param onUpdateCurrent What to do when we only want to update the [status] of the current entry.
     * @param onUpdateCurrentAndPrevious What to do when we started a Stop in the [previousEntry] and want to update the status with both the previous and current entry.
     * @return The resulting processing result with either
     */
    private fun <T : NewEntry, U : NewEntry, V : NewShipStatus> getResultOnVisitOrVoyage(
        status: V,
        currentEntry: T,
        previousEntry: U?,
        event: StopEvent,
        onUpdateCurrent: (updatedStops: List<NewStop>) -> NewEventProcessingResult,
        onUpdateCurrentAndPrevious: (updatedPreviousStops: List<NewStop>, updatedCurrentStops: List<NewStop>) -> NewEventProcessingResult,
    ): NewEventProcessingResult {
        // At an end event we always have an actual location, meaning we can decide our final classification
        val stopEventActualLocation = (event as? StopEndEvent)?.stopLocation ?: return ignoredEventResult(status, event, MISSING_STOP_LOCATION)
        val stopClassification = infraService.findStopClassification(stopEventActualLocation)
            // Only use the classification if we found something
            .takeIf { classification -> classification.type != NewStopType.UNCLASSIFIED }

        val currentStops = currentEntry.stops
        val updatedStops = getUpdatedStops(currentStops, event, stopEventActualLocation, stopClassification)

        // Could not get updated stops so something went wrong when trying to replace
        if (updatedStops == null && previousEntry != null) {
            // Check if we have a matching stop in the previous entry
            val currentPreviousEntryStops = previousEntry.stops
            val matchingPreviousEntryStop = currentPreviousEntryStops.firstOrNull { isMatchingRelatedStop(it, event) }

            // Stop was never started or is already finished and we didn't use a fallback
            if (matchingPreviousEntryStop == null) {
                return ignoredEventResult(status, event, "Cannot process stop event, stop was never started or already finished in previous and current entry")
            }

            // We have a stop starting in the previous entry and starting in the current entry, meaning we have to split the stop in 2
            val finishedPreviousEntryStop = matchingPreviousEntryStop.copy(
                type = stopClassification?.type ?: matchingPreviousEntryStop.type,
                areaId = stopClassification?.areaId ?: matchingPreviousEntryStop.areaId,
                endEventId = event._id,
                end = currentEntry.start
            )

            // Create a new Stop for the part that is inside the current entry
            val newStop = matchingPreviousEntryStop.copy(
                type = stopClassification?.type ?: matchingPreviousEntryStop.type,
                areaId = stopClassification?.areaId ?: matchingPreviousEntryStop.areaId,
                start = currentEntry.start,
                endEventId = event._id,
                end = LocationTime(
                    location = event.location,
                    time = event.actualTime
                )
            )

            val updatedPreviousEntryStops = currentPreviousEntryStops.replaceFirst(matchingPreviousEntryStop, finishedPreviousEntryStop)
            val updatedCurrentEntryStops = currentStops + newStop

            return onUpdateCurrentAndPrevious(updatedPreviousEntryStops, updatedCurrentEntryStops)
        }

        if (updatedStops == null) {
            return ignoredEventResult(status, event, "Cannot process stop event, stop was never started or already finished in current entry")
        }

        // Stop started in the current entry, no need to do any special handling
        return onUpdateCurrent(updatedStops)
    }

    override fun getResultOnVisit(
        status: NewVisitShipStatus,
        event: StopEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        val (currentVisit, currentVisitEsof) = status.visit
        val previousVoyage = status.previousVoyage?.entry

        return getResultOnVisitOrVoyage(
            status = status,
            currentEntry = currentVisit,
            previousEntry = previousVoyage,
            event = event,
            onUpdateCurrent = { updatedStops ->
                // Visit isn't confirmed yet, so we have to see if we have to switch the main port
                if (!currentVisit.confirmed) {
                    val updatedStop = updatedStops.firstOrNull { it.startEventId == event.relatedEvent() }

                    if (updatedStop != null) {
                        val confirmedVisitResult = handleAsNonConfirmedVisit(
                            stop = updatedStop,
                            currentVisit = currentVisit,
                            currentVisitEsof = currentVisitEsof,
                            status = status,
                            previousVoyage = previousVoyage,
                            updateTime = updateTime
                        ) { newStop -> updatedStops }

                        // Only return the result if we confirmed the Visit, otherwise handle as we normally should
                        if (confirmedVisitResult != null) {
                            return@getResultOnVisitOrVoyage confirmedVisitResult
                        }
                    }
                }

                // Stop started in the Visit, no need to do any special handling
                val updatedVisit = currentVisit.copy(stops = updatedStops)
                val updatedStatus = status.updateCurrentVisit(updatedVisit = updatedVisit)
                val changes = listOf(
                    VisitChange(Action.UPDATE, updatedVisit)
                )

                NewEventProcessingResult(
                    status = updatedStatus,
                    changes = changes,
                    decision = "Finished the current stop"
                )
            },
            onUpdateCurrentAndPrevious = { updatedPreviousVoyageStops, updatedCurrentVisitStops ->
                // At this point the previous voyage is always filled as we otherwise can't update it
                val updatedPreviousVoyage = previousVoyage!!.copy(
                    stops = updatedPreviousVoyageStops,
                    updatedAt = updateTime
                )

                // Visit isn't confirmed yet, so we have to see if we have to switch the main port
                if (!currentVisit.confirmed) {
                    val updatedStop = previousVoyage.stops.firstOrNull { it.startEventId == event.relatedEvent() }

                    if (updatedStop != null) {
                        val confirmedVisitResult = handleAsNonConfirmedVisit(
                            stop = updatedStop,
                            currentVisit = currentVisit,
                            currentVisitEsof = currentVisitEsof,
                            status = status,
                            previousVoyage = updatedPreviousVoyage,
                            updateTime = updateTime
                        ) { newStop -> updatedCurrentVisitStops }

                        // Only return the result if we confirmed the Visit, otherwise handle as we normally should
                        if (confirmedVisitResult != null) {
                            return@getResultOnVisitOrVoyage confirmedVisitResult
                        }
                    }
                }

                val updatedCurrentVisit = currentVisit.copy(
                    stops = updatedCurrentVisitStops,
                    updatedAt = updateTime
                )
                val updatedStatus = status.copy(
                    visit = status.visit.copy(entry = updatedCurrentVisit),
                    previousVoyage = status.previousVoyage?.copy(entry = updatedPreviousVoyage)
                )
                val changes = listOf(
                    VoyageChange(Action.UPDATE, updatedPreviousVoyage),
                    VisitChange(Action.UPDATE, updatedCurrentVisit)
                )

                NewEventProcessingResult(
                    status = updatedStatus,
                    changes = changes,
                    decision = "Finished the stop, overlapping the current and previous entry"
                )
            }
        )
    }

    override fun getResultOnVoyage(
        status: NewVoyageShipStatus,
        event: StopEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        val currentVoyage = status.voyage.entry
        val previousVisit = status.previousVisit?.entry
        return getResultOnVisitOrVoyage(
            status = status,
            currentEntry = currentVoyage,
            previousEntry = previousVisit,
            event = event,
            onUpdateCurrent = { updatedStops ->
                // Stop started in the Voyage, no need to do any special handling
                val updatedVoyage = currentVoyage.copy(
                    stops = updatedStops,
                    updatedAt = updateTime
                )
                val updatedStatus = status.updateCurrentVoyage(updatedVoyage = updatedVoyage)
                val changes = listOf(
                    VoyageChange(Action.UPDATE, updatedVoyage)
                )

                NewEventProcessingResult(
                    status = updatedStatus,
                    changes = changes,
                    decision = "Finished the current stop"
                )
            },
            onUpdateCurrentAndPrevious = { updatedPreviousVisitStops, updatedCurrentVoyageStops ->
                // At this point the previous visit is always filled as we otherwise can't update it
                val updatedPreviousVisit = previousVisit!!.copy(
                    stops = updatedPreviousVisitStops,
                    updatedAt = updateTime
                )
                val updatedCurrentVoyage = currentVoyage.copy(
                    stops = updatedCurrentVoyageStops,
                    updatedAt = updateTime
                )
                val updatedStatus = status.copy(
                    voyage = status.voyage.copy(entry = updatedCurrentVoyage),
                    previousVisit = status.previousVisit?.copy(entry = updatedPreviousVisit)
                )
                val changes = listOf(
                    VisitChange(Action.UPDATE, updatedPreviousVisit),
                    VoyageChange(Action.UPDATE, updatedCurrentVoyage)
                )

                NewEventProcessingResult(
                    status = updatedStatus,
                    changes = changes,
                    decision = "Finished the stop, overlapping the current and previous entry"
                )
            }
        )
    }

    /**
     * Check if the provided [stop] related to the provided [event] and ongoing or we used a fallback.
     */
    private fun isMatchingRelatedStop(stop: NewStop, event: StopEvent): Boolean {
        val stopEnd = stop.end

        return stop.startEventId == event.relatedEvent() &&
            (stopEnd == null || stopEnd.fallback == FallbackType.ACTIVITY_END_BY_EOSP)
    }

    /**
     * Given the [currentStops] find a matching stop using the [event] and update the end location, time and id accordingly.
     *
     * @param currentStops A list of the current stop where we want to update the stop matching the [event].
     * @param event The stop we are currently processing.
     * @return The [currentStops] with the matching stop updated using the [event] or null when there was no matching stop.
     */
    private fun getUpdatedStops(
        currentStops: List<NewStop>,
        event: StopEvent,
        stopEventActualLocation: Location,
        stopClassification: ClassifiedStop?
    ): List<NewStop>? {
        return try {
            currentStops.replaceFirst({ stop -> isMatchingRelatedStop(stop, event) }) { matchingStop ->
                val updatedStop = matchingStop.copy(
                    type = stopClassification?.type ?: matchingStop.type,
                    areaId = stopClassification?.areaId ?: matchingStop.areaId,
                    location = stopEventActualLocation,
                    end = LocationTime(
                        location = event.location,
                        time = event.actualTime
                    ),
                    endEventId = event._id
                )

                updatedStop
            }
        } catch (ex: IndexOutOfBoundsException) {
            null
        }
    }
}
