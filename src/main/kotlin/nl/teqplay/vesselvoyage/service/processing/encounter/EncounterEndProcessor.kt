package nl.teqplay.vesselvoyage.service.processing.encounter

import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.vesselvoyage.model.ESof
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.EventProcessingResult
import nl.teqplay.vesselvoyage.model.InitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewESoF
import nl.teqplay.vesselvoyage.model.v2.NewEncounter
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.processing.EventUtil.relatedEvent
import nl.teqplay.vesselvoyage.util.replaceFirst
import nl.teqplay.vesselvoyage.util.toVesselVoyageLocation
import java.time.ZoneOffset

class EncounterEndProcessor(
    override val config: EventProcessingProperties
) : EncounterBaseProcessor() {

    override fun getResultOnInitial(status: InitialShipStatus, event: EncounterEvent, config: EventProcessingProperties, imo: Int): EventProcessingResult {
        return emptyEventResult(status)
    }

    override fun getUpdatedEsofOnEvent(esof: ESof, event: EncounterEvent, config: EventProcessingProperties, imo: Int): Pair<ESof, EventProcessingIssue?> {
        val matchingEncounter = event.relatedEvent()?.let { relatedEvent ->
            esof.encounters.find { it.startEventId == relatedEvent }
        } ?: esof.encounters.findLast { it.otherImo == event.otherShip.imo?.toString() }
            ?.takeUnless {
                // do not override when endEventId is already filled in
                // TODO: should we log a warning?
                it.endEventId != null
            }

        if (matchingEncounter == null) {
            val issue = EventProcessingIssue(
                eventId = event._id,
                description = "Unexpected encounter end event: no matching start event found " +
                    "(imo: $imo, otherImo: ${event.otherShip.imo}, type: ${event.encounterType})"
            )

            return Pair(esof, issue)
        }

        val updatedESof = esof.copy(
            encounters = esof.encounters.map { encounter ->
                if (encounter == matchingEncounter) {
                    matchingEncounter.copy(
                        endEventId = event._id,
                        endTime = event.actualTime.atZone(ZoneOffset.UTC),
                        endLocation = event.location.toVesselVoyageLocation()
                    )
                } else {
                    encounter
                }
            }
        )

        return Pair(updatedESof, null)
    }

    override fun NewESoF.updateWithEncounterEvent(
        event: EncounterEvent,
        serviceVesselMmsi: Int,
        serviceVesselImo: Int?
    ): NewESoF {
        val updatedEncounters = this.encounters.replaceFirst({ it.startEventId == event.relatedEvent() }) { encounter ->
            val endLocationTime = LocationTime(
                location = event.location,
                time = event.actualTime
            )

            // Remove the encounter if no time was spent.
            if (encounter.start.time != endLocationTime.time) {
                encounter.copy(end = endLocationTime)
            } else null
        }

        return this.copy(encounters = updatedEncounters)
    }

    override fun validateEventWithCurrentEncounters(
        currentEncounters: List<NewEncounter>,
        event: EncounterEvent,
    ): EventProcessingIssue? {
        val currentlyOngoingEncounter = currentEncounters.firstOrNull { encounter ->
            encounter.end == null && encounter.startEventId == event.relatedEvent()
        }

        if (currentlyOngoingEncounter == null) {
            return EventProcessingIssue(
                eventId = event._id,
                description = "Cannot process encounter event, we never received an encounter start"
            )
        }

        return null
    }
}
