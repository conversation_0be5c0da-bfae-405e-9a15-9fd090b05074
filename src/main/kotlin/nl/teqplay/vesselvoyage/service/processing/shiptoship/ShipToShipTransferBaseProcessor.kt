package nl.teqplay.vesselvoyage.service.processing.shiptoship

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.EventProcessingResult
import nl.teqplay.vesselvoyage.model.InitialShipStatus
import nl.teqplay.vesselvoyage.model.NewEventProcessingResult
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.ESoFChange
import nl.teqplay.vesselvoyage.model.v2.EntryESoFWrapper
import nl.teqplay.vesselvoyage.model.v2.NewESoF
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.ShipToShipTransfer
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.processing.EventProcessor
import nl.teqplay.vesselvoyage.util.createEmptyEsof
import java.time.Instant

private val log = KotlinLogging.logger {}

abstract class ShipToShipTransferBaseProcessor : EventProcessor<EncounterEvent> {

    companion object {
        const val UNSUPPORTED_STATUS = "ShipToShipTransfer events are only allowed when in Visit status"
    }

    /**
     * Update the [NewESoF], creating a new transfer on a start event or updating the matching transfer on an end
     * event.
     */
    abstract fun NewESoF.updateWithEvent(event: EncounterEvent, updateTime: Instant): NewESoF

    /**
     * Validate the provided [event] to see if it is possible to add the [currentShipToShipTransfers].
     */
    abstract fun validateEventWithCurrent(
        currentShipToShipTransfers: List<ShipToShipTransfer>,
        event: EncounterEvent
    ): EventProcessingIssue?

    private fun getCurrentEsof(wrapper: EntryESoFWrapper<*>, updateTime: Instant): Pair<NewESoF, Boolean> {
        val currentEsof = wrapper.esof
            // Return an empty esof if we didn't have one before
            ?: return createEmptyEsof(wrapper.entry, updateTime) to true

        return currentEsof to false
    }

    protected fun getESoFChangeOnVisit(
        status: NewVisitShipStatus,
        event: EncounterEvent,
        updateTime: Instant
    ): NewEventProcessingResult {
        val (currentEsof, isNewEsof) = getCurrentEsof(status.visit, updateTime)

        // Check if the provided event makes sense with the currently known transfers
        val issue = validateEventWithCurrent(currentEsof.shipToShipTransfers, event)
        if (issue != null) {
            return NewEventProcessingResult(
                status = status,
                changes = emptyList(),
                issues = listOf(issue)
            )
        }

        val updatedEsof = currentEsof.updateWithEvent(event, updateTime)
        val updatedStatus = status.copy(visit = status.visit.copy(esof = updatedEsof))

        // Depending on if this is a new esof we create or otherwise updating the existing esof
        val changeAction = if (isNewEsof) {
            Action.CREATE
        } else {
            Action.UPDATE
        }

        return NewEventProcessingResult(
            status = updatedStatus,
            changes = listOf(
                ESoFChange(
                    action = changeAction,
                    value = updatedEsof
                )
            )
        )
    }

    override fun getResultOnVisit(
        status: NewVisitShipStatus,
        event: EncounterEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return getESoFChangeOnVisit(status, event, updateTime)
    }

    override fun getResultOnInitial(
        status: NewInitialShipStatus,
        event: EncounterEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return ignoredEventResult(
            status = status,
            event = event,
            description = UNSUPPORTED_STATUS
        )
    }

    override fun getResultOnVisit(
        status: VisitShipStatus,
        event: EncounterEvent,
        config: EventProcessingProperties,
        imo: Int
    ): EventProcessingResult {
        // not implemented in V1
        return emptyEventResult(status)
    }

    override fun getResultOnVoyage(
        status: VoyageShipStatus,
        event: EncounterEvent,
        config: EventProcessingProperties,
        imo: Int
    ): EventProcessingResult {
        // not implemented in V1
        return emptyEventResult(status)
    }

    override fun getResultOnInitial(
        status: InitialShipStatus,
        event: EncounterEvent,
        config: EventProcessingProperties,
        imo: Int
    ): EventProcessingResult {
        // not implemented in V1
        return emptyEventResult(status)
    }
}
