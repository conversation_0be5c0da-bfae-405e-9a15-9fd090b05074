package nl.teqplay.vesselvoyage.service.processing.berth

import nl.teqplay.aisengine.event.interfaces.UniqueBerthEvent
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.BerthAreaVisit
import nl.teqplay.vesselvoyage.model.Change
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.EventProcessingResult
import nl.teqplay.vesselvoyage.model.NewEventProcessingResult
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.AisFetchingService
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.util.isFinished
import nl.teqplay.vesselvoyage.util.matchingBerth
import nl.teqplay.vesselvoyage.util.toVesselVoyageLocation
import java.time.Instant
import java.time.ZoneOffset

class UniqueBerthStartProcessor(
    config: EventProcessingProperties,
    infraService: InfraService,
    aisFetchingService: AisFetchingService
) : UniqueBerthBaseProcessor(config, infraService, aisFetchingService) {
    companion object {
        const val START_EVENT_IGNORED_ISSUE = "Unique berth events start are only supported when a visit is ongoing."
        const val MISSING_UNLOCODE = "Unique berth event has no unlocode"
        const val MISSING_BERTHID = "Unique berth event has no berth id"
    }

    override fun getResultOnVisit(status: VisitShipStatus, event: UniqueBerthEvent, config: EventProcessingProperties, imo: Int): EventProcessingResult {
        val unlocode = event.area.unlocode ?: return ignoredEventResult(status, event, MISSING_UNLOCODE)
        val berthId = event.area.id ?: return ignoredEventResult(status, event, MISSING_BERTHID)

        // check if the ship is not already in this very berth
        if (status.visit.berthAreas.findLast { matchingBerth(it, unlocode, berthId) }?.isFinished() == false) {
            return EventProcessingResult(
                status = status,
                changes = listOf(),
                issues = listOf(
                    EventProcessingIssue(
                        eventId = event._id,
                        description = "Cannot process UniqueBerthEvent start: ship is currently already in this berth. " +
                            "Will ignore the event " +
                            "(imo: $imo, berthId: $berthId)"
                    )
                )
            )
        }

        val currentVisit = status.visit

        val updatedVisit = currentVisit.copy(
            esof = updateEsof(currentVisit, event, config, berthId),
            berthAreas = currentVisit.berthAreas + createBerthAreaVisitFromStartEvent(event, unlocode, berthId)
        )

        return EventProcessingResult(
            status = VisitShipStatus(updatedVisit, status.previousVoyage, status.previousVisit),
            changes = listOf(Change(Action.UPDATE, updatedVisit))
        )
    }

    private fun createBerthAreaVisitFromStartEvent(event: UniqueBerthEvent, unlocode: String, berthId: String): BerthAreaVisit {
        return BerthAreaVisit(
            portId = unlocode,
            berthId = berthId,
            startEventId = event._id,
            startTime = event.actualTime.atZone(ZoneOffset.UTC),
            startLocation = event.location.toVesselVoyageLocation(),
            startDraught = event.draught?.toDouble(),
            endEventId = null,
            endTime = null,
            endLocation = null,
            endDraught = null
        )
    }

    override fun getResultOnVisit(
        status: NewVisitShipStatus,
        event: UniqueBerthEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return getResultOnVisit(status, event, updateTime, imo)
    }

    override fun getResultOnVoyage(
        status: NewVoyageShipStatus,
        event: UniqueBerthEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return ignoredEventResult(status, event, START_EVENT_IGNORED_ISSUE)
    }

    override fun getResultOnInitial(
        status: NewInitialShipStatus,
        event: UniqueBerthEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return ignoredEventResult(status, event, START_EVENT_IGNORED_ISSUE)
    }
}
