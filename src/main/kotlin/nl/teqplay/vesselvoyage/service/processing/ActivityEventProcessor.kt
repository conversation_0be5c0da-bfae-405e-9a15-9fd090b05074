package nl.teqplay.vesselvoyage.service.processing

import nl.teqplay.aisengine.event.interfaces.EndEvent
import nl.teqplay.aisengine.event.interfaces.LocationBasedEvent
import nl.teqplay.aisengine.event.interfaces.StartEvent
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.NewEventProcessingResult
import nl.teqplay.vesselvoyage.model.v2.AreaActivity
import nl.teqplay.vesselvoyage.model.v2.FallbackType
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.VisitChange
import nl.teqplay.vesselvoyage.service.processing.EventProcessor.Companion.MAX_ALLOWED_ACTIVITIES
import nl.teqplay.vesselvoyage.util.isOngoing
import nl.teqplay.vesselvoyage.util.replaceFirst
import nl.teqplay.vesselvoyage.util.updateCurrentVisit
import java.time.Duration
import java.time.Instant
import kotlin.reflect.KProperty1

abstract class ActivityEventProcessor<T : LocationBasedEvent>(
    private val activitiesField: KProperty1<NewVisit, MutableList<AreaActivity>>,
    private val areaIdField: KProperty1<T, AreaIdentifier?>,
    private val activityMergingEnabled: Boolean = false
) : EventProcessor<T> {
    companion object {
        const val MISSING_AREA_ID_VALUE_ISSUE = "Can't process event because the area id is null."
        const val NO_PREVIOUS_VISIT_STATUS = "Current Voyage doesn't have a previous Visit."
        const val NO_END_EVENT_ON_PREVIOUS_VISIT = "Can't process start events when dealing with a previous Visit."
        val MAX_DURATION_BETWEEN_TO_MERGE_ACTIVITIES: Duration = Duration.ofMinutes(60)
        const val UNKNOWN_EVENT_TYPE = "Can't process event because the type is unknown."
    }

    /**
     * Get the first [AreaActivity] that is ongoing and matching the provided [areaId].
     *
     * @param currentVisit The visit that is currently ongoing.
     * @param areaId The area id of the event we want to match to.
     * @return The matching ongoing [AreaActivity].
     */
    protected fun getFirstMatchingOngoingAreaActivity(
        currentVisit: NewVisit,
        areaId: String
    ): AreaActivity? {
        val activities = activitiesField.get(currentVisit)
        return activities.firstOrNull { activity ->
            activity.isOngoing() && activity.areaId == areaId
        }
    }

    /**
     * Get the first [AreaActivity] matching the provided [areaId]
     *  and where one of the fallbacks were used to fill in the [AreaActivity.end].
     *
     * @param currentVisit The visit that is currently ongoing.
     * @param areaId The area id of the event we want to match to.
     * @return The matching ongoing [AreaActivity].
     */
    private fun getFirstMatchingAreaActivityWithEndFallback(
        currentVisit: NewVisit,
        areaId: String
    ): AreaActivity? {
        val activities = activitiesField.get(currentVisit)
        return activities.firstOrNull { activity ->
            activity.areaId == areaId && activity.end?.fallback == FallbackType.ACTIVITY_END_BY_EOSP
        }
    }

    fun getResultForPreviousVisitOnVoyage(status: NewVoyageShipStatus, event: T, updateTime: Instant): NewEventProcessingResult {
        val areaId = areaIdField.get(event)?.id

        // We can only process the events if we have an ID.
        // The old platform event doesn't always include this id, so we filter them out.
        if (areaId == null) {
            return ignoredEventResult(
                status = status,
                event = event,
                description = MISSING_AREA_ID_VALUE_ISSUE
            )
        }

        // Make sure we have a previous visit, otherwise we have to drop the event.
        val previousVisitWrapper = status.previousVisit
        val previousVisit = previousVisitWrapper?.entry

        if (previousVisit == null) {
            return ignoredEventResult(
                status = status,
                event = event,
                description = NO_PREVIOUS_VISIT_STATUS
            )
        }

        // So far fallbacks are only set on end events, so we can ignore any start event.
        if (event !is EndEvent) {
            return ignoredEventResult(
                status = status,
                event = event,
                description = NO_END_EVENT_ON_PREVIOUS_VISIT
            )
        }

        val matchingAreaActivity = getFirstMatchingAreaActivityWithEndFallback(
            currentVisit = previousVisit,
            areaId = areaId
        )

        // We can only handle the event when we have a matching ongoing area activity
        if (matchingAreaActivity == null) {
            return ignoredEventResult(
                status = status,
                event = event,
                description = "Currently known area activities don't match the area id or didn't use a fallback. (area id: $areaId)"
            )
        }

        val updatedVisit = updateEndOfMatchingActivity(event, previousVisit, matchingAreaActivity)
            .copy(updatedAt = updateTime)
        val newPreviousVisitWrapper = previousVisitWrapper.copy(entry = updatedVisit)
        val newStatus = status.copy(previousVisit = newPreviousVisitWrapper)

        return NewEventProcessingResult(
            status = newStatus,
            changes = listOf(
                VisitChange(Action.UPDATE, updatedVisit)
            )
        )
    }

    protected open fun getResultOnVisit(status: NewVisitShipStatus, event: T, updateTime: Instant, imo: Int): NewEventProcessingResult {
        val areaId = areaIdField.get(event)?.id

        // We can only process the events if we have an ID.
        // The old platform event doesn't always include this id, so we filter them out.
        if (areaId == null) {
            return ignoredEventResult(
                status = status,
                event = event,
                description = MISSING_AREA_ID_VALUE_ISSUE
            )
        }

        return when (event) {
            is StartEvent -> getResultOnVisitAtStartEvent(status, event, areaId, updateTime, imo)
            is EndEvent -> getResultOnVisitAtEndEvent(status, event, areaId, updateTime)
            else -> return ignoredEventResult(
                status = status,
                event = event,
                description = UNKNOWN_EVENT_TYPE
            )
        }
    }

    private fun getResultOnVisitAtStartEvent(
        status: NewVisitShipStatus,
        event: T,
        areaId: String,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        val currentVisit = status.visit.entry
        val matchingOngoingAreaActivity = getFirstMatchingOngoingAreaActivity(currentVisit, areaId)

        // Check if we already have another area activity ongoing, as we can't start two activities
        //  when we missed the exit event of the first one.
        if (matchingOngoingAreaActivity != null) {
            return ignoredEventResult(
                status = status,
                event = event,
                description = buildString {
                    append("Visit already has an activity ongoing for this. ")
                    append("Will ignore the event (imo: $imo, area id: $areaId)")
                }
            )
        }

        val currentActivities = activitiesField.get(currentVisit)
        val updatedVisit = getUpdateVisitAtStartEvent(
            currentActivities = currentActivities,
            areaId = areaId,
            event = event,
            currentVisit = currentVisit,
            updateTime = updateTime
        )

        return NewEventProcessingResult(
            status = status.updateCurrentVisit(updatedVisit),
            changes = listOf(
                VisitChange(Action.UPDATE, updatedVisit)
            )
        )
    }

    private fun getUpdateVisitAtStartEvent(
        currentActivities: MutableList<AreaActivity>,
        areaId: String,
        event: T,
        currentVisit: NewVisit,
        updateTime: Instant
    ): NewVisit {
        // Only try to merge the activities if its enabled for this area activity
        if (activityMergingEnabled) {
            val currentLastActivityOfSameArea = currentActivities.lastOrNull { activity -> activity.areaId == areaId }
            val timeBetweenLastActivityAndEvent = currentLastActivityOfSameArea?.end?.time?.let { Duration.between(it, event.actualTime) }

            if (
                currentLastActivityOfSameArea != null &&
                timeBetweenLastActivityAndEvent != null &&
                timeBetweenLastActivityAndEvent <= MAX_DURATION_BETWEEN_TO_MERGE_ACTIVITIES
            ) {
                // The activity is abruptly ended, since we are back in the same area, resume
                val resumedActivity = currentLastActivityOfSameArea.copy(
                    id = event._id,
                    end = null
                )
                val updatedAreaActivities = currentActivities.replaceFirst(
                    currentActivity = currentLastActivityOfSameArea,
                    updatedActivity = resumedActivity
                )
                return updateVisitWithActivities(currentVisit, updatedAreaActivities)
                    .copy(
                        updatedAt = updateTime
                    )
            }
        }

        // Normal flow, we don't have any old activity to merge with
        val newAreaActivity = AreaActivity(
            id = event._id,
            start = createLocationTimeFromEvent(event),
            end = null,
            areaId = areaId
        )
        val updatedAreaActivities = currentActivities + newAreaActivity
        val shouldLimit = updatedAreaActivities.size >= MAX_ALLOWED_ACTIVITIES
        return updateVisitWithActivities(currentVisit, updatedAreaActivities)
            // Override the limited flag as we might have to limit this visit because of too many items
            .copy(
                limited = shouldLimit,
                updatedAt = updateTime
            )
    }

    protected open fun getResultOnVisitAtEndEvent(
        status: NewVisitShipStatus,
        event: T,
        areaId: String,
        updateTime: Instant
    ): NewEventProcessingResult {
        val currentVisit = status.visit.entry
        val matchingOngoingAreaActivity = getFirstMatchingOngoingAreaActivity(currentVisit, areaId)

        // We can only handle the event when we have a matching ongoing area activity
        if (matchingOngoingAreaActivity == null) {
            return ignoredEventResult(
                status = status,
                event = event,
                description = "Currently known area activities don't match the area id or are already finished. (area id: $areaId)"
            )
        }

        val updatedVisit = updateEndOfMatchingActivity(event, currentVisit, matchingOngoingAreaActivity)
            .copy(updatedAt = updateTime)

        return NewEventProcessingResult(
            status = status.updateCurrentVisit(updatedVisit),
            changes = listOf(
                VisitChange(Action.UPDATE, updatedVisit)
            )
        )
    }

    protected fun updateEndOfAreaActivity(
        event: T,
        matchingAreaActivity: AreaActivity
    ): AreaActivity {
        val endLocationTime = createLocationTimeFromEvent(event)
        return matchingAreaActivity.copy(end = endLocationTime)
    }

    protected fun updateEndOfMatchingActivity(
        event: T,
        visit: NewVisit,
        matchingAreaActivity: AreaActivity
    ): NewVisit {
        val updatedAreaActivity = updateEndOfAreaActivity(event, matchingAreaActivity)

        val currentActivities = activitiesField.get(visit)
        val updatedAreaActivities = currentActivities.replaceFirst(
            currentActivity = matchingAreaActivity,
            updatedActivity = updatedAreaActivity
        )

        return updateVisitWithActivities(visit, updatedAreaActivities)
    }

    /**
     * Update our activities field on the provided [visit] using reflection, as otherwise we need to implement an update
     *  function for all possible activity fields.
     */
    private fun updateVisitWithActivities(visit: NewVisit, updatedActivities: List<AreaActivity>): NewVisit {
        // Make a copy first, so we don't touch the existing visit
        val visitCopy = visit.copy()

        val activityList = activitiesField.get(visitCopy)

        activityList.clear()
        activityList.addAll(updatedActivities)

        return visitCopy
    }

    /**
     * Create an [LocationTime] entry from the provided [event].
     */
    protected open fun createLocationTimeFromEvent(event: T): LocationTime {
        return LocationTime(
            location = event.location,
            time = event.actualTime
        )
    }
}
