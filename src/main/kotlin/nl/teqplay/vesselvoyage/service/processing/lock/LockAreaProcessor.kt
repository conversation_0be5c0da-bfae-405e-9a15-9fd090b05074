package nl.teqplay.vesselvoyage.service.processing.lock

import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.annotation.ProfileRevents
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.processing.StartEndEventProcessor
import org.springframework.stereotype.Service

@ProfileProcessing
@ProfileRevents
@Service
class LockAreaProcessor(
    config: EventProcessingProperties
) : StartEndEventProcessor<AreaEvent, LockAreaStartProcessor, LockAreaEndProcessor>(
    startProcessor = LockAreaStartProcessor(config),
    endProcessor = LockAreaEndProcessor(config)
)
