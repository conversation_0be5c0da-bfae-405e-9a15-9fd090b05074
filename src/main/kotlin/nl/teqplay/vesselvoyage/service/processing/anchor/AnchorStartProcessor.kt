package nl.teqplay.vesselvoyage.service.processing.anchor

import nl.teqplay.aisengine.event.interfaces.AnchoredEvent
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.AnchorAreaVisit
import nl.teqplay.vesselvoyage.model.Change
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.EventProcessingResult
import nl.teqplay.vesselvoyage.model.InitialShipStatus
import nl.teqplay.vesselvoyage.model.MAX_ANCHOR_AREAS
import nl.teqplay.vesselvoyage.model.NewEventProcessingResult
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.util.createChangeList
import nl.teqplay.vesselvoyage.util.startInitialVisit
import nl.teqplay.vesselvoyage.util.startVisit
import java.time.Instant
import java.time.ZoneOffset

class AnchorStartProcessor(config: EventProcessingProperties, infraService: InfraService) : AnchorBaseProcessor(config, infraService) {
    companion object {
        const val START_EVENT_IGNORED_ISSUE = "Anchor area events start are only supported when a visit is ongoing."
        const val EVENT_MISSING_AREA = "Event is missing anchor area"
    }

    override fun getResultOnVisit(status: VisitShipStatus, event: AnchoredEvent, config: EventProcessingProperties, imo: Int): EventProcessingResult {
        val issues = mutableListOf<EventProcessingIssue>()

        val areaId = event.area.id ?: return ignoredEventResult(
            status = status,
            event = event,
            description = EVENT_MISSING_AREA
        )

        // protect against the visit growing infinitely large
        if (status.visit.anchorAreas.size >= MAX_ANCHOR_AREAS) {
            issues += EventProcessingIssue(
                eventId = event._id,
                description = "Cannot process AnchorEvent: too many anchor areas in current visit. " +
                    "Will ignore the event (imo: $imo, anchorAreas: ${status.visit.anchorAreas.size})"
            )

            return EventProcessingResult(
                status = status,
                changes = listOf(),
                issues = issues
            )
        }

        val (destinations, destinationIssues) = getDestinations(event)

        val updatedVisit = status.visit.copy(
            anchorAreas = status.visit.anchorAreas + AnchorAreaVisit(
                anchorAreaId = areaId,
                startEventId = event._id,
                startTime = event.actualTime.atZone(ZoneOffset.UTC),
                destinations = destinations,
                endEventId = null,
                endTime = null
            )
        )

        return EventProcessingResult(
            status = VisitShipStatus(updatedVisit, status.previousVoyage, status.previousVisit),
            changes = listOf(Change(Action.UPDATE, updatedVisit)),
            issues = issues + destinationIssues
        )
    }

    override fun getResultOnVoyage(status: VoyageShipStatus, event: AnchoredEvent, config: EventProcessingProperties, imo: Int): EventProcessingResult {
        val (destinations, destinationIssues) = getDestinations(status.voyage, event)

        val updatedShipStatus = startVisit(event, status, destinations, imo)

        return EventProcessingResult(
            status = updatedShipStatus,
            changes = createChangeList(status, updatedShipStatus),
            issues = destinationIssues
        )
    }

    override fun getResultOnInitial(status: InitialShipStatus, event: AnchoredEvent, config: EventProcessingProperties, imo: Int): EventProcessingResult {
        val (destinations, destinationIssues) = getDestinations(event)
        val newShipStatus = startInitialVisit(event, destinations, imo)

        return EventProcessingResult(
            status = newShipStatus,
            changes = createChangeList(null, newShipStatus),
            issues = destinationIssues
        )
    }

    override fun getResultOnVisit(
        status: NewVisitShipStatus,
        event: AnchoredEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return getResultOnVisit(status, event, updateTime, imo)
    }

    override fun getResultOnVoyage(
        status: NewVoyageShipStatus,
        event: AnchoredEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return ignoredEventResult(
            status = status,
            event = event,
            description = START_EVENT_IGNORED_ISSUE
        )
    }

    override fun getResultOnInitial(
        status: NewInitialShipStatus,
        event: AnchoredEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return ignoredEventResult(
            status = status,
            event = event,
            description = START_EVENT_IGNORED_ISSUE
        )
    }
}
