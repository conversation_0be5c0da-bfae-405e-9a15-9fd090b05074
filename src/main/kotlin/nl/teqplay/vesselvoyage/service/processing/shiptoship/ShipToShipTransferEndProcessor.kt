package nl.teqplay.vesselvoyage.service.processing.shiptoship

import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.aisengine.event.interfaces.StartEvent
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.NewEventProcessingResult
import nl.teqplay.vesselvoyage.model.v2.ESoFChange
import nl.teqplay.vesselvoyage.model.v2.FallbackType
import nl.teqplay.vesselvoyage.model.v2.NewESoF
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.ShipToShipTransfer
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.processing.ActivityEventProcessor
import nl.teqplay.vesselvoyage.service.processing.EventUtil.relatedEvent
import nl.teqplay.vesselvoyage.util.generateLocationTime
import nl.teqplay.vesselvoyage.util.replaceFirst
import java.time.Instant

class ShipToShipTransferEndProcessor(
    override val config: EventProcessingProperties
) : ShipToShipTransferBaseProcessor() {

    override fun getResultOnVoyage(
        status: NewVoyageShipStatus,
        event: EncounterEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return if (status.previousVisit != null) {
            getESoFChangeForPreviousVisit(status, event, updateTime)
        } else {
            ignoredEventResult(status, event, description = ActivityEventProcessor.NO_PREVIOUS_VISIT_STATUS)
        }
    }

    private fun getESoFChangeForPreviousVisit(
        status: NewVoyageShipStatus,
        event: EncounterEvent,
        updateTime: Instant,
    ): NewEventProcessingResult {

        // Ship is in a voyage, meaning we cannot process a start event
        if (event is StartEvent) {
            return ignoredEventResult(status, event, description = UNSUPPORTED_STATUS)
        }

        val wrapper = status.previousVisit
            ?: return ignoredEventResult(status, event, description = "No previous visit present")

        // No esof present? Then there's no start event as well
        val esof = wrapper.esof
            ?: return ignoredEventResult(status, event, description = "No esof present on previous visit")

        val matchingTransfer = esof.shipToShipTransfers.find { transfer ->
            transfer.end?.fallback == FallbackType.ACTIVITY_END_BY_EOSP && transfer.startEventId == event.relatedEvent()
        }
            ?: return ignoredEventResult(
                status,
                event,
                description = "No matching ship-to-ship transfer found. Either the start event was never recorded " +
                    "on the previous visit or the transfer.end was not set by a fallback mechanism"
            )

        // note that the transfer.end.fallback property will be overwritten to null, which is what we want: we're
        // replacing the fallback value with an actual value
        val updatedPreviousVisitEsof = esof.updateWithEvent(event, updateTime)
        val updatedPreviousVisitStatus = status.copy(
            previousVisit = wrapper.copy(esof = updatedPreviousVisitEsof)
        )

        return NewEventProcessingResult(
            status = updatedPreviousVisitStatus,
            changes = listOf(
                ESoFChange(
                    action = Action.UPDATE,
                    value = updatedPreviousVisitEsof
                )
            )
        )
    }

    override fun NewESoF.updateWithEvent(event: EncounterEvent, updateTime: Instant): NewESoF {
        val updatedTransfers = shipToShipTransfers.replaceFirst({ it.startEventId == event.relatedEvent() }) { transfer ->
            // note for transfer where .end was set with a fallback: this clears the fallback property!
            val endLocationTime = event.generateLocationTime()

            // Remove the transfer if no time was spent.
            if (transfer.start.time != endLocationTime.time) {
                transfer.copy(end = endLocationTime)
            } else null
        }

        return this.copy(shipToShipTransfers = updatedTransfers, updatedAt = updateTime)
    }

    override fun validateEventWithCurrent(
        currentShipToShipTransfers: List<ShipToShipTransfer>,
        event: EncounterEvent
    ): EventProcessingIssue? {
        val currentlyOngoingTransfer = currentShipToShipTransfers.firstOrNull { transfer ->
            transfer.end == null && transfer.startEventId == event.relatedEvent()
        }
            ?: return EventProcessingIssue(
                eventId = event._id,
                description = "Cannot process ShipToShipTransfer event, we never received a start"
            )

        return null
    }
}
