package nl.teqplay.vesselvoyage.service.processing.stop

import nl.teqplay.aisengine.event.interfaces.StopEvent
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.NewEventProcessingResult
import nl.teqplay.vesselvoyage.model.v2.FallbackType
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewStop
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.VisitChange
import nl.teqplay.vesselvoyage.model.v2.VoyageChange
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.processing.EventProcessor.Companion.MAX_ALLOWED_ACTIVITIES
import nl.teqplay.vesselvoyage.service.processing.stop.merging.StopMergingTactic
import nl.teqplay.vesselvoyage.util.createNewStop
import nl.teqplay.vesselvoyage.util.updateCurrentVisit
import nl.teqplay.vesselvoyage.util.updateCurrentVoyage
import java.time.Instant

class StopStartProcessor(
    override val config: EventProcessingProperties,
    private val infraService: InfraService,
    private val mergingTactics: List<StopMergingTactic>
) : StopBaseProcessor(infraService = infraService) {

    override fun getResultOnVisit(
        status: NewVisitShipStatus,
        event: StopEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        val (currentVisit, currentVisitEsof) = status.visit
        val currentStops = currentVisit.stops

        val currentlyOngoingStop = currentStops.firstOrNull { stop -> stop.end == null }

        val newStop = createNewStop(event, infraService::findStopClassification)

        // Visit isn't confirmed yet, so we have to see if we have to switch the main port
        if (!currentVisit.confirmed) {
            val confirmedVisitResult = handleAsNonConfirmedVisit(
                stop = newStop,
                currentVisit = currentVisit,
                currentVisitEsof = currentVisitEsof,
                status = status,
                previousVoyage = status.previousVoyage?.entry,
                updateTime = updateTime
            ) { newStop ->
                getUpdatedStops(
                    event = event,
                    currentlyOngoingStop = currentlyOngoingStop,
                    currentStops = currentStops,
                    newStop = newStop
                )
            }

            // Only return the result if we confirmed the Visit, otherwise handle as we normally should
            if (confirmedVisitResult != null) {
                return confirmedVisitResult
            }
        }

        val updatedStops = getUpdatedStops(
            event = event,
            currentlyOngoingStop = currentlyOngoingStop,
            currentStops = currentStops,
            newStop = newStop
        )
        val shouldLimit = updatedStops.size >= MAX_ALLOWED_ACTIVITIES
        val updatedVisit = currentVisit.copy(
            stops = updatedStops,
            limited = shouldLimit,
            updatedAt = updateTime
        )
        val updatedStatus = status.updateCurrentVisit(updatedVisit = updatedVisit)
        val changes = listOf(VisitChange(Action.UPDATE, updatedVisit))

        return NewEventProcessingResult(
            status = updatedStatus,
            changes = changes,
            decision = "Updated visit with stop(s). Should limit: $shouldLimit"
        )
    }

    override fun getResultOnVoyage(
        status: NewVoyageShipStatus,
        event: StopEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        val (currentVoyage, _) = status.voyage
        val currentStops = currentVoyage.stops

        val currentlyOngoingStop = currentStops.firstOrNull { stop -> stop.end == null }

        val newStop = createNewStop(event, infraService::findStopClassification)
        val updatedStops = getUpdatedStops(
            event = event,
            currentlyOngoingStop = currentlyOngoingStop,
            currentStops = currentStops,
            newStop = newStop
        )

        val shouldLimit = updatedStops.size >= MAX_ALLOWED_ACTIVITIES
        val updatedVoyage = currentVoyage.copy(
            stops = updatedStops,
            limited = shouldLimit
        )
        val updatedStatus = status.updateCurrentVoyage(updatedVoyage = updatedVoyage)
        val changes = listOf(VoyageChange(Action.UPDATE, updatedVoyage))

        return NewEventProcessingResult(
            status = updatedStatus,
            changes = changes,
            decision = "Updated voyage with stop(s). Should limit: $shouldLimit"
        )
    }

    private fun getUpdatedStops(
        event: StopEvent,
        currentlyOngoingStop: NewStop?,
        currentStops: List<NewStop>,
        newStop: NewStop
    ): List<NewStop> {
        return if (currentlyOngoingStop != null) {
            val fallbackEndedCurrentlyOngoingStop = currentlyOngoingStop.copy(
                location = currentlyOngoingStop.start.location,
                end = LocationTime(
                    location = currentlyOngoingStop.start.location,
                    time = event.actualTime,
                    fallback = FallbackType.ACTIVITY_END_BY_STOP_START
                )
            )
            currentStops - currentlyOngoingStop + fallbackEndedCurrentlyOngoingStop + newStop
        } else {
            for (mergingTactics in mergingTactics) {
                val mergeableStop = mergingTactics.getMergeableStop(currentStops)
                    ?: continue

                if (mergingTactics.shouldMerge(mergeableStop, newStop)) {
                    val correctedStop = mergeableStop.copy(
                        startEventId = newStop.startEventId,
                        end = null,
                        endEventId = null
                    )
                    return mergingTactics.onMerge(currentStops, mergeableStop, correctedStop)
                }
            }

            // There is no way of merging the new stop with the latest stop so just add a new stop
            return currentStops + newStop
        }
    }
}
