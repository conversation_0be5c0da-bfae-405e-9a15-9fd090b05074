package nl.teqplay.vesselvoyage.service.processing.shiptoship

import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.aisengine.event.model.encountermetadata.STSEncounterMetadata
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.NewEventProcessingResult
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewESoF
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.ShipToShipTransfer
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import java.time.Instant

class ShipToShipTransferStartProcessor(
    override val config: EventProcessingProperties
) : ShipToShipTransferBaseProcessor() {

    override fun getResultOnVoyage(
        status: NewVoyageShipStatus,
        event: EncounterEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return ignoredEventResult(status, event, description = UNSUPPORTED_STATUS)
    }

    override fun NewESoF.updateWithEvent(event: EncounterEvent, updateTime: Instant): NewESoF {
        val startLocationTime = LocationTime(
            location = event.location,
            time = event.actualTime
        )

        val updatedTransfers = this.shipToShipTransfers + ShipToShipTransfer(
            otherMmsi = event.otherShip.mmsi,
            otherImo = event.otherShip.imo,
            startEventId = event._id,
            start = startLocationTime,
            end = null,
            areaId = (event.metadata as? STSEncounterMetadata)?.areaId
        )

        return this.copy(shipToShipTransfers = updatedTransfers)
    }

    override fun validateEventWithCurrent(
        currentShipToShipTransfers: List<ShipToShipTransfer>,
        event: EncounterEvent,
    ): EventProcessingIssue? {
        val otherMmsi = event.otherShip.mmsi

        val currentlyOngoingEncounter = currentShipToShipTransfers.firstOrNull { encounter ->
            encounter.end == null && encounter.otherMmsi == otherMmsi
        }

        if (currentlyOngoingEncounter != null) {
            return EventProcessingIssue(
                eventId = event._id,
                description = "Cannot process ShipToShipTransfer event, same encounter already ongoing (other mmsi: " +
                    "$otherMmsi)"
            )
        }

        // No issues found
        return null
    }
}
