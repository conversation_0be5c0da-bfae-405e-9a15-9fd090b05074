package nl.teqplay.vesselvoyage.service.processing.stop

import nl.teqplay.aisengine.event.interfaces.StopEvent
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.EventProcessingResult
import nl.teqplay.vesselvoyage.model.InitialShipStatus
import nl.teqplay.vesselvoyage.model.NewEventProcessingResult
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.model.internal.InfraAreaType
import nl.teqplay.vesselvoyage.model.lightweight.poma.Berth
import nl.teqplay.vesselvoyage.model.v2.ESoFChange
import nl.teqplay.vesselvoyage.model.v2.EntryESoFWrapper
import nl.teqplay.vesselvoyage.model.v2.NewESoF
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewStop
import nl.teqplay.vesselvoyage.model.v2.NewStopType
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import nl.teqplay.vesselvoyage.model.v2.VisitChange
import nl.teqplay.vesselvoyage.model.v2.VoyageChange
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.processing.EventProcessor
import nl.teqplay.vesselvoyage.util.endsIn
import nl.teqplay.vesselvoyage.util.startsIn
import nl.teqplay.vesselvoyage.util.updateCurrentVisit
import java.time.Instant

abstract class StopBaseProcessor(
    private val infraService: InfraService
) : EventProcessor<StopEvent> {
    companion object {
        const val UNSUPPORTED_STATUS = "Stop events are only allowed when in Visit or Voyage status"
    }

    override fun getResultOnInitial(
        status: NewInitialShipStatus,
        event: StopEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return ignoredEventResult(
            status = status,
            event = event,
            description = UNSUPPORTED_STATUS
        )
    }

    protected fun handleAsNonConfirmedVisit(
        stop: NewStop,
        currentVisit: NewVisit,
        currentVisitEsof: NewESoF?,
        status: NewVisitShipStatus,
        previousVoyage: NewVoyage?,
        updateTime: Instant,
        getUpdatedStops: (NewStop) -> List<NewStop>
    ): NewEventProcessingResult? {
        // Lock stops can't be confirmed
        if (stop.type == NewStopType.LOCK) {
            return null
        }

        if (stop.type == NewStopType.BERTH) {
            // Area ID is always filled here as we classified it as a BERTH which is not possible without having a poma area
            val berthAreaId = stop.areaId!!
            val berthMainPortId = (infraService.getById(berthAreaId, InfraAreaType.BERTH) as? Berth)
                ?.let { berth -> infraService.getMainPortFromBerth(berth) }
                ?._id

            if (berthMainPortId != null) {
                return confirmVisitViaStop(
                    newStop = stop,
                    confirmedMainPortId = berthMainPortId,
                    currentVisit = currentVisit,
                    currentVisitEsof = currentVisitEsof,
                    status = status,
                    previousVoyage = previousVoyage,
                    getUpdatedStops = getUpdatedStops,
                    updateTime = updateTime
                )
            }
        }

        // We couldn't confirm the visit, try now based on if we stopped inside one of our ports.
        val currentEnteredPorts = currentVisit.portAreaActivities
        val stoppedMainPorts = currentEnteredPorts.filter { portActivity ->
            val stoppedInsidePort = stop.startsIn(portActivity) || stop.endsIn(portActivity)

            if (stoppedInsidePort) {
                infraService.isPortMainPortById(id = portActivity.areaId)
            } else {
                false
            }
        }

        // We can only confirm this way if there is only 1 port we are matching on, multiple main ports mean that they are
        // overlapping, and we are not sure in which port they wanted to go to.
        if (stoppedMainPorts.size == 1) {
            val mainPortId = stoppedMainPorts.first().areaId
            return confirmVisitViaStop(
                newStop = stop,
                confirmedMainPortId = mainPortId,
                currentVisit = currentVisit,
                currentVisitEsof = currentVisitEsof,
                status = status,
                previousVoyage = previousVoyage,
                getUpdatedStops = getUpdatedStops,
                updateTime = updateTime
            )
        }

        return null
    }

    private fun confirmVisitViaStop(
        newStop: NewStop,
        confirmedMainPortId: String,
        currentVisit: NewVisit,
        currentVisitEsof: NewESoF?,
        status: NewVisitShipStatus,
        previousVoyage: NewVoyage?,
        updateTime: Instant,
        getUpdatedStops: (NewStop) -> List<NewStop>
    ): NewEventProcessingResult? {

        val currentAreaId = currentVisit.eospAreaActivity.areaId.removeSuffix(".eosp")
        val updatedStops = getUpdatedStops(newStop)
        val decision: String
        val (newStatus, changes) = if (currentAreaId == confirmedMainPortId) {
            // Our leading port is already matching our confirmed main port id, we can just confirm it
            val confirmedVisit = currentVisit.copy(
                confirmed = true,
                stops = updatedStops
            )
            val updatedStatus = status.updateCurrentVisit(updatedVisit = confirmedVisit)
            val changes = listOf(VisitChange(Action.UPDATE, confirmedVisit))

            decision = "Confirming visit, stop matches the main port"
            updatedStatus to changes
        } else {
            // We need to switch our visit port as we stopped at a different port
            // Search if we ever entered the port of the berth
            val matchingPortEosp = currentVisit.otherOngoingEospAreaActivities.find { activity ->
                val portAreaId = activity.areaId.removeSuffix(".eosp")
                portAreaId == confirmedMainPortId
            }

            if (matchingPortEosp != null) {
                // Switch our visit to the new leading port, but keep the time if we'd move back in time.
                val newVisitStart = when {
                    matchingPortEosp.start.time > currentVisit.start.time -> matchingPortEosp.start
                    else -> currentVisit.start
                }

                // Move out currently ongoing eosp activity to the other ongoing as we can't finish it yet
                val newOtherOngoingEosp = currentVisit.eospAreaActivity

                val newVisitId = "${matchingPortEosp.id}.VISIT"
                // Create a new visit from our current visit but set all fields that define the "leading" port to our matching one
                val newConfirmedVisit = currentVisit.copy(
                    _id = newVisitId,
                    confirmed = true,
                    stops = updatedStops,
                    start = newVisitStart,
                    eospAreaActivity = matchingPortEosp,
                    otherOngoingEospAreaActivities = currentVisit.otherOngoingEospAreaActivities - matchingPortEosp + newOtherOngoingEosp,
                    updatedAt = updateTime
                )

                val updatedPreviousVoyage = previousVoyage?.copy(
                    next = newVisitId,
                    end = newVisitStart,
                    updatedAt = updateTime
                )

                val newVisitEsof = currentVisitEsof?.copy(
                    _id = newConfirmedVisit._id,
                    updatedAt = updateTime
                )
                val newShipStatus = status.copy(
                    visit = EntryESoFWrapper(entry = newConfirmedVisit, newVisitEsof),
                    previousVoyage = updatedPreviousVoyage?.let { status.previousVoyage?.copy(entry = updatedPreviousVoyage) }
                )

                val changes = listOfNotNull(
                    // We delete the existing one and create a new one as the Visit IDs change
                    VisitChange(Action.CREATE, newConfirmedVisit),
                    VisitChange(Action.DELETE, currentVisit.copy(updatedAt = updateTime)),
                    newVisitEsof?.let { ESoFChange(Action.CREATE, newVisitEsof) },
                    currentVisitEsof?.let { ESoFChange(Action.DELETE, currentVisitEsof.copy(updatedAt = updateTime)) },
                    updatedPreviousVoyage?.let { VoyageChange(Action.UPDATE, updatedPreviousVoyage) },
                )

                decision = "Replacing the visit and confirming the new visit, stop matches one of the " +
                    "currentVisit.otherOngoingEospActivities: " +
                    (infraService.getPortByAreaId(currentVisit.eospAreaActivity.areaId))?.unlocode + " -> " +
                    (infraService.getPortByAreaId(newConfirmedVisit.eospAreaActivity.areaId))?.unlocode + ". " +
                    "Also update the previous voyage to match the new areaId"
                newShipStatus to changes
            } else {
                decision = "Bleep bloop: error. We never entered the port of this stop!"
                // TODO We never entered the port of the berth, what should we do here?
                null to null
            }
        }

        if (newStatus != null && changes != null) {
            return NewEventProcessingResult(
                status = newStatus,
                changes = changes,
                decision = decision
            )
        }

        return null
    }

    override fun getResultOnVisit(
        status: VisitShipStatus,
        event: StopEvent,
        config: EventProcessingProperties,
        imo: Int
    ): EventProcessingResult {
        return emptyEventResult(status)
    }

    override fun getResultOnVoyage(
        status: VoyageShipStatus,
        event: StopEvent,
        config: EventProcessingProperties,
        imo: Int
    ): EventProcessingResult {
        return emptyEventResult(status)
    }

    override fun getResultOnInitial(
        status: InitialShipStatus,
        event: StopEvent,
        config: EventProcessingProperties,
        imo: Int
    ): EventProcessingResult {
        return emptyEventResult(status)
    }
}
