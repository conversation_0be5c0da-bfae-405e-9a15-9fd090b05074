package nl.teqplay.vesselvoyage.service.processing.encounter

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.vesselvoyage.model.ESof
import nl.teqplay.vesselvoyage.model.Encounter
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.EventProcessingResult
import nl.teqplay.vesselvoyage.model.InitialShipStatus
import nl.teqplay.vesselvoyage.model.MAX_ENCOUNTERS
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewESoF
import nl.teqplay.vesselvoyage.model.v2.NewEncounter
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.util.isFinished
import nl.teqplay.vesselvoyage.util.toVesselVoyageLocation
import java.time.ZoneOffset

class EncounterStartProcessor(
    override val config: EventProcessingProperties
) : EncounterBaseProcessor() {
    val log = KotlinLogging.logger {}

    override fun getResultOnInitial(status: InitialShipStatus, event: EncounterEvent, config: EventProcessingProperties, imo: Int): EventProcessingResult {
        log.debug { "Ignoring EncounterEvent for IMO $imo: no active visit or voyage" }
        return emptyEventResult(status)
    }

    override fun getUpdatedEsofOnEvent(esof: ESof, event: EncounterEvent, config: EventProcessingProperties, imo: Int): Pair<ESof, EventProcessingIssue?> {
        val newEncounter = Encounter(
            type = event.encounterType,
            otherMmsi = event.otherShip.mmsi.toString(),
            otherImo = event.otherShip.imo?.toString(),
            startEventId = event._id,
            startTime = event.actualTime.atZone(ZoneOffset.UTC),
            startLocation = event.location.toVesselVoyageLocation(),
            endEventId = null,
            endTime = null,
            endLocation = null
        )

        // protect against the esof growing infinitely large
        if (esof.encounters.size >= MAX_ENCOUNTERS) {
            val issue = EventProcessingIssue(
                eventId = event._id,
                description = "Cannot process EncounterEvent: too many encounters in current esof. " +
                    "Will ignore the event (imo: $imo, encounters: ${esof.encounters.size})"
            )

            return Pair(esof, issue)
        }

        if (esof.encounters.findLast { it.otherImo == event.otherShip.imo?.toString() && !it.isFinished() } != null) {
            val issue = EventProcessingIssue(
                eventId = event._id,
                description = "Cannot process EncounterEvent: an encounter with this ship is already ongoing. " +
                    "Will ignore the new event (imo: $imo, otherImo: ${event.otherShip.imo})"
            )

            return Pair(esof, issue)
        }

        val updatedESof = esof.copy(
            encounters = esof.encounters + newEncounter
        )

        return Pair(updatedESof, null)
    }

    override fun NewESoF.updateWithEncounterEvent(
        event: EncounterEvent,
        serviceVesselMmsi: Int,
        serviceVesselImo: Int?
    ): NewESoF {
        val startLocationTime = LocationTime(
            location = event.location,
            time = event.actualTime
        )

        val newEncounter = NewEncounter(
            type = event.encounterType,
            otherMmsi = serviceVesselMmsi,
            otherImo = serviceVesselImo,
            startEventId = event._id,
            start = startLocationTime,
            end = null
        )

        val updatedEncounters = this.encounters + newEncounter

        return this.copy(encounters = updatedEncounters)
    }

    override fun validateEventWithCurrentEncounters(
        currentEncounters: List<NewEncounter>,
        event: EncounterEvent,
    ): EventProcessingIssue? {
        // Check both as in some events the service vessel is provided as the main vessel.
        val mmsi = event.ship.mmsi
        val otherMmsi = event.otherShip.mmsi

        val currentlyOngoingEncounter = currentEncounters.firstOrNull { encounter ->
            encounter.end == null && (encounter.otherMmsi == otherMmsi || encounter.otherMmsi == mmsi)
        }

        if (currentlyOngoingEncounter != null) {
            return EventProcessingIssue(
                eventId = event._id,
                description = "Cannot process encounter event, same encounter already ongoing (other mmsi: $otherMmsi)"
            )
        }

        // No issues found
        return null
    }
}
