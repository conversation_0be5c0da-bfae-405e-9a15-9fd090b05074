package nl.teqplay.vesselvoyage.service.processing.port

import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.annotation.ProfileRevents
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.AisFetchingService
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.StaticShipInfoService
import nl.teqplay.vesselvoyage.service.processing.StartEndEventProcessor
import org.springframework.stereotype.Service

@ProfileProcessing
@ProfileRevents
@Service
class PortProcessor(
    config: EventProcessingProperties,
    infraService: InfraService,
    aisFetchingService: AisFetchingService,
    staticShipInfoService: StaticShipInfoService
) : StartEndEventProcessor<AreaEvent, PortStartProcessor, PortEndProcessor>(
    startProcessor = PortStartProcessor(config, infraService, aisFetchingService),
    endProcessor = PortEndProcessor(config, infraService, staticShipInfoService)
)
