package nl.teqplay.vesselvoyage.service.processing.movement

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.event.interfaces.ShipMovingEvent
import nl.teqplay.vesselvoyage.logic.stop.calculateAndSetAccuracy
import nl.teqplay.vesselvoyage.model.ESof
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.EventProcessingResult
import nl.teqplay.vesselvoyage.model.InitialShipStatus
import nl.teqplay.vesselvoyage.model.MAX_STOPS
import nl.teqplay.vesselvoyage.model.NewEventProcessingResult
import nl.teqplay.vesselvoyage.model.Stop
import nl.teqplay.vesselvoyage.model.StopDetectionVersion
import nl.teqplay.vesselvoyage.model.StopStartDetectionInfo
import nl.teqplay.vesselvoyage.model.StopType
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.processing.ESofEventProcessor
import nl.teqplay.vesselvoyage.util.isNotFinished
import nl.teqplay.vesselvoyage.util.toVesselVoyageLocation
import java.time.Instant
import java.time.ZoneOffset

class MovementEndProcessor(
    override val config: EventProcessingProperties
) : ESofEventProcessor<ShipMovingEvent>() {
    private val log = KotlinLogging.logger {}

    override fun getResultOnInitial(status: InitialShipStatus, event: ShipMovingEvent, config: EventProcessingProperties, imo: Int): EventProcessingResult {
        log.debug { "Ignoring MovementEvent for IMO $imo: no active visit or voyage" }
        return EventProcessingResult(status, emptyList())
    }

    override fun getUpdatedEsofOnEvent(esof: ESof, event: ShipMovingEvent, config: EventProcessingProperties, imo: Int): Pair<ESof, EventProcessingIssue?> {
        val lastStop = esof.stops.lastOrNull()

        if (lastStop != null && lastStop.isNotFinished()) {
            val issue = EventProcessingIssue(
                eventId = event._id,
                description = "Unexpected movement stop event: ship is currently already stopped. " +
                    "Event will be ignored (imo: $imo)"
            )

            return Pair(esof, issue)
        }

        // protect against the esof growing infinitely large
        if (esof.stops.size >= MAX_STOPS) {
            val issue = EventProcessingIssue(
                eventId = event._id,
                description = "Cannot process MovementEvent: too many stops in current esof. " +
                    "Will ignore the event " +
                    "(imo: $imo, stops: ${esof.stops.size})"
            )

            return Pair(esof, issue)
        }

        // Yeah, it's confusing: a movement *stop* event means that a Stop is *starting*
        val newStop = Stop(
            type = StopType.UNCLASSIFIED,
            aisType = StopType.UNCLASSIFIED,
            pomaType = StopType.UNCLASSIFIED,
            startEventId = event._id,
            startTime = event.actualTime.atZone(ZoneOffset.UTC),
            startLocation = event.location.toVesselVoyageLocation(),
            endEventId = null,
            endTime = null,
            endLocation = null,
            actualLocation = null,
            actualTime = null,
            detectionVersion = StopDetectionVersion.MOVEMENT_EVENT,
            accuracy = null,
            berthStart = null,
            berthEnd = null,
            aisStart = StopStartDetectionInfo(
                id = event._id,
                time = event.actualTime.atZone(ZoneOffset.UTC),
                location = event.location.toVesselVoyageLocation()
            ),
            aisEnd = null
        )
        val newStops = esof.stops + newStop

        val updatedESof = esof.copy(
            stops = newStops.map(Stop::calculateAndSetAccuracy)
        )

        return Pair(updatedESof, null)
    }

    override fun getResultOnInitial(
        status: NewInitialShipStatus,
        event: ShipMovingEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return notUsedForNewDefinition(status)
    }

    override fun getResultOnVoyage(
        status: NewVoyageShipStatus,
        event: ShipMovingEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return notUsedForNewDefinition(status)
    }

    override fun getResultOnVisit(
        status: NewVisitShipStatus,
        event: ShipMovingEvent,
        config: EventProcessingProperties,
        updateTime: Instant,
        imo: Int
    ): NewEventProcessingResult {
        return notUsedForNewDefinition(status)
    }
}
