package nl.teqplay.vesselvoyage.service.processing.stop

import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.event.interfaces.EndEvent
import nl.teqplay.aisengine.event.interfaces.StartEvent
import nl.teqplay.aisengine.event.interfaces.StopEvent
import nl.teqplay.aisengine.event.model.StopEndEvent
import nl.teqplay.skeleton.metrics.Metric
import nl.teqplay.skeleton.metrics.MetricRegistry
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.annotation.ProfileRevents
import nl.teqplay.vesselvoyage.model.v2.NewStopType
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.InfraService
import nl.teqplay.vesselvoyage.service.processing.StartEndEventProcessor
import nl.teqplay.vesselvoyage.service.processing.stop.merging.StopMergingTactic
import org.springframework.stereotype.Service
import java.util.concurrent.atomic.AtomicLong

@ProfileProcessing
@ProfileRevents
@Service
class StopProcessor(
    config: EventProcessingProperties,
    meterRegistry: MeterRegistry,
    private val infraService: InfraService,
    mergingTactics: List<StopMergingTactic>
) : StartEndEventProcessor<StopEvent, StopStartProcessor, StopEndProcessor>(
    startProcessor = StopStartProcessor(config, infraService, mergingTactics),
    endProcessor = StopEndProcessor(config, infraService),
    metricRegistry = MetricRegistry(StopProcessor::class, meterRegistry, listOf(TAG_STOP_TYPE_KEY, TAG_EVENT_TYPE_KEY))
) {
    companion object {
        private const val TAG_STOP_TYPE_KEY = "stop_type"
    }

    private lateinit var startedStopsByType: MutableMap<NewStopType, AtomicLong>
    private lateinit var endedStopsByType: MutableMap<NewStopType, AtomicLong>

    override fun createGauges(metricRegistry: MetricRegistry<*>) {
        startedStopsByType = mutableMapOf()
        endedStopsByType = mutableMapOf()
        for (stopType in NewStopType.values()) {
            startedStopsByType[stopType] = metricRegistry.createGauge(Metric.STATE_SIZE, AtomicLong(0), stopType.name, "start")
            endedStopsByType[stopType] = metricRegistry.createGauge(Metric.STATE_SIZE, AtomicLong(0), stopType.name, "end")
        }
    }

    override fun getGauge(event: StopEvent): AtomicLong? {
        val actualLocation = (event as? StopEndEvent)?.stopLocation
        val stopClassification = infraService.findStopClassification(actualLocation ?: event.location)

        return when (event) {
            is StartEvent -> startedStopsByType.getValue(stopClassification.type)
            is EndEvent -> endedStopsByType.getValue(stopClassification.type)
            else -> null
        }
    }
}
