package nl.teqplay.vesselvoyage.service.processing

import nl.teqplay.aisengine.event.interfaces.EndEvent
import nl.teqplay.aisengine.event.interfaces.StartEvent
import nl.teqplay.aisengine.event.interfaces.TeqplayEvent
import nl.teqplay.skeleton.metrics.Metric
import nl.teqplay.skeleton.metrics.MetricRegistry
import nl.teqplay.vesselvoyage.model.EventProcessingIssue
import nl.teqplay.vesselvoyage.model.EventProcessingResult
import nl.teqplay.vesselvoyage.model.NewEventProcessingResult
import nl.teqplay.vesselvoyage.model.ShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewShipStatus
import java.time.Instant
import java.util.concurrent.atomic.AtomicLong

/**
 * Wrapper class adding support for processing [StartEndEvent] where two different processor are needed.
 *
 * @param startProcessor The instance of the start event processor
 * @param endProcessor The instance of the end event processor
 * @param metricRegistry When provided the instance used to track start and end event processing
 * @param T The start/end event type
 * @param S The start event processor
 * @param E The end event processor
 */
abstract class StartEndEventProcessor<in T : TeqplayEvent, S : EventProcessor<T>, E : EventProcessor<T>>(
    protected val startProcessor: S,
    protected val endProcessor: E,
    protected val metricRegistry: MetricRegistry<*>? = null
) {
    companion object {
        const val TAG_EVENT_TYPE_KEY = "event_type"
        private const val TAG_EVENT_TYPE_VALUE_START = "start"
        private const val TAG_EVENT_TYPE_VALUE_END = "end"
    }

    private lateinit var startEventCount: AtomicLong
    private lateinit var endEventCount: AtomicLong

    init {
        metricRegistry?.let { this.createGauges(metricRegistry) }
    }

    protected open fun createGauges(metricRegistry: MetricRegistry<*>) {
        startEventCount = metricRegistry.createGauge(Metric.MESSAGE_COUNT_PROCESSED, AtomicLong(0), TAG_EVENT_TYPE_VALUE_START)
        endEventCount = metricRegistry.createGauge(Metric.MESSAGE_COUNT_PROCESSED, AtomicLong(0), TAG_EVENT_TYPE_VALUE_END)
    }

    protected open fun getGauge(event: T): AtomicLong? {
        return when (event) {
            is StartEvent -> startEventCount
            is EndEvent -> endEventCount
            else -> return null
        }
    }

    protected open fun countOnGauge(gauge: AtomicLong, event: T) {
        gauge.incrementAndGet()
    }

    fun processEvent(status: ShipStatus, event: T): EventProcessingResult {
        return when (event) {
            is StartEvent -> startProcessor.processEvent(status, event)
            is EndEvent -> endProcessor.processEvent(status, event)
            else -> ignoredEventResult(status, event, "Ignoring unknown type of event $event")
        }
    }

    fun processEvent(status: NewShipStatus, event: T, updateTime: Instant): NewEventProcessingResult {
        val result = when (event) {
            is StartEvent -> {
                // Always handle to inspect later if we're limited.
                val eventProcessingResult = startProcessor.processEvent(status, event, updateTime)

                // We don't want to start any new activities when we reached the threshold to limit this entry.
                startProcessor.handleAsLimitedStatus(status, eventProcessingResult, event)
                    ?: eventProcessingResult
            }
            is EndEvent -> endProcessor.processEvent(status, event, updateTime)
            else -> ignoredEventResult(status, event, "Ignoring unknown type of event $event")
        }

        // We can just return the result when we don't do anything with metrics for this processor
        if (metricRegistry == null) {
            return result
        }

        // Something got changed while processing and we didn't have any issues
        if (result.issues.isEmpty() && result.changes.isNotEmpty()) {
            val eventGauge = getGauge(event)

            // Event is neither start or end event, thus being an unknown type
            if (eventGauge == null) {
                ignoredEventResult(status, event, "Ignoring unknown type of event $event")
            } else {
                countOnGauge(eventGauge, event)
            }
        }

        return result
    }

    fun ignoredEventResult(status: ShipStatus, event: T, description: String): EventProcessingResult {
        return EventProcessingResult(
            status = status,
            changes = emptyList(),
            issues = listOf(
                EventProcessingIssue(
                    eventId = event._id,
                    description = description
                )
            )
        )
    }

    fun ignoredEventResult(status: NewShipStatus, event: T, description: String): NewEventProcessingResult {
        return NewEventProcessingResult(
            status = status,
            changes = emptyList(),
            issues = listOf(
                EventProcessingIssue(
                    eventId = event._id,
                    description = description
                )
            )
        )
    }
}
