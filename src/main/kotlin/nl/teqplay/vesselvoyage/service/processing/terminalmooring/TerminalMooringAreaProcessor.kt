package nl.teqplay.vesselvoyage.service.processing.terminalmooring

import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.annotation.ProfileRevents
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.processing.StartEndEventProcessor
import org.springframework.stereotype.Service

@ProfileProcessing
@ProfileRevents
@Service
class TerminalMooringAreaProcessor(
    config: EventProcessingProperties
) : StartEndEventProcessor<AreaEvent, TerminalMooringAreaStartProcessor, TerminalMooringAreaEndProcessor>(
    startProcessor = TerminalMooringAreaStartProcessor(config),
    endProcessor = TerminalMooringAreaEndProcessor(config)
)
