package nl.teqplay.vesselvoyage.service.processing.stop.merging

import nl.teqplay.skeleton.util.haversineDistance
import nl.teqplay.vesselvoyage.model.v2.NewStop
import nl.teqplay.vesselvoyage.util.isFinished
import nl.teqplay.vesselvoyage.util.replaceLast
import java.time.Duration

interface StopMergingTactic {
    fun shouldMerge(mergeableStop: NewStop, newStop: NewStop): Boolean

    fun getTimeBetweenStops(mergeableStop: NewStop, newStop: NewStop): Duration? {
        val stopEnd = mergeableStop.end ?: return null
        val newStopStart = newStop.start
        return Duration.between(stopEnd.time, newStopStart.time)
    }

    fun getDistanceBetweenStops(mergeableStop: NewStop, newStop: NewStop): Double? {
        val stopEnd = mergeableStop.end ?: return null

        // Get the smallest distance from either the start or end location
        return minOf(
            haversineDistance(mergeableStop.start.location, newStop.start.location),
            haversineDistance(stopEnd.location, newStop.start.location)
        )
    }

    fun getMergeableStop(currentStops: List<NewStop>): NewStop? {
        return currentStops.lastOrNull { stop -> stop.isFinished() }
    }

    fun onMerge(currentStops: List<NewStop>, mergeableStop: NewStop, correctedStop: NewStop): List<NewStop> {
        return currentStops.replaceLast(mergeableStop, correctedStop)
    }
}
