package nl.teqplay.vesselvoyage.service.revents

import nl.teqplay.aisengine.revents.ReventsDependenciesService
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioPostProcessing
import org.springframework.stereotype.Service

/**
 * This service specifies which applications we depend on.
 * Since VesselVoyage will receive all events out-of-order, this will also be used to know when we've received all
 * events from those components and are ready to sort the buffered events.
 */
@Service
class ReventsDependenciesServiceImpl : ReventsDependenciesService {
    override fun getDependentApplicationNames(): List<String> {
        return ScenarioPostProcessing.VESSEL_VOYAGE_V2.events.map { it.app }
    }

    override fun usesEventStreamProducerThread(): Boolean = false
}
