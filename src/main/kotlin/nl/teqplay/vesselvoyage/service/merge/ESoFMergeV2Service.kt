package nl.teqplay.vesselvoyage.service.merge

import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.v2.ESoFChange
import nl.teqplay.vesselvoyage.model.v2.EntryESoFWrapper
import nl.teqplay.vesselvoyage.model.v2.NewESoF
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import nl.teqplay.vesselvoyage.service.processing.encounter.EncounterProcessor
import nl.teqplay.vesselvoyage.util.createEmptyEsof
import org.springframework.stereotype.Service
import java.time.Instant

/**
 * Service that creates [NewESoF] with data (like encounters) that has been generated later.
 */
@ProfileProcessing
@Service
class ESoFMergeV2Service(
    private val encounterEventProcessor: EncounterProcessor,
) {

    /**
     * Create empty [NewESoF] for [entries] where [encounters] overlap.
     * Doesn't take current [NewESoF] state into account.
     */
    fun createEmptyESoFsWithEncounters(
        entries: List<NewEntry>,
        encounters: List<EncounterEvent>
    ): List<NewESoF> {
        if (entries.isEmpty() || encounters.isEmpty()) {
            return emptyList()
        }

        val entriesAreCorrectlyInterleaved = entries
            .zipWithNext { a, b -> a::class != b::class }
            .all { interleaved -> interleaved }
        require(entriesAreCorrectlyInterleaved) { "Visits and voyages must be interleaved." }

        val encountersAreOrdered = encounters
            .zipWithNext { a, b -> a.actualTime <= b.actualTime }
            .all { ordered -> ordered }
        require(encountersAreOrdered) { "Encounters must be ordered." }

        val mutableEntries = entries.toMutableList()
        val esofs = mutableMapOf<String, NewESoF>()

        var status: NewShipStatus = NewInitialShipStatus()
        val updateTime = Instant.now()
        encounters.forEach { encounter ->
            status = progressShipStatus(mutableEntries, encounter, status, esofs, updateTime)

            val result = encounterEventProcessor.processEvent(status, encounter, updateTime)
            status = result.status
            result.changes.filterIsInstance<ESoFChange>().forEach { change ->
                when (change.action) {
                    Action.CREATE,
                    Action.UPDATE -> {
                        if (change.value.encounters.isNotEmpty()) {
                            esofs[change.value._id] = change.value
                        } else {
                            esofs.remove(change.value._id)
                        }
                    }

                    Action.DELETE -> esofs.remove(change.value._id)
                    Action.REVISE, Action.DISCONTINUE -> {
                        // Ignore.
                    }
                }
            }
        }

        return esofs.values.toList()
    }

    private fun progressShipStatus(
        entries: MutableList<NewEntry>,
        encounter: EncounterEvent,
        status: NewShipStatus,
        esofs: MutableMap<String, NewESoF>,
        updateTime: Instant
    ): NewShipStatus {
        var progressedStatus = status
        do {
            val nextEntry = entries.firstOrNull()
            if (nextEntry == null || nextEntry.start.time > encounter.actualTime) {
                break
            }
            progressedStatus = when (val s = progressedStatus) {
                is NewInitialShipStatus -> when (nextEntry) {
                    is NewVisit -> NewVisitShipStatus(
                        visit = EntryESoFWrapper(nextEntry.copy(end = null), null),
                        previousVoyage = null,
                        previousVisit = null
                    )

                    is NewVoyage -> NewVoyageShipStatus(
                        voyage = EntryESoFWrapper(nextEntry.copy(end = null), null),
                        previousVisit = null,
                        previousVoyage = null
                    )
                }

                is NewVisitShipStatus -> {
                    val voyageEntry = (nextEntry as NewVoyage).copy(end = null)
                    val voyageESoF = patchContinuedEncounters(s.visit.esof, voyageEntry, esofs, updateTime)
                    NewVoyageShipStatus(
                        voyage = EntryESoFWrapper(voyageEntry, voyageESoF),
                        previousVisit = s.visit,
                        previousVoyage = s.previousVoyage,
                        eventBuffer = s.eventBuffer
                    )
                }

                is NewVoyageShipStatus -> {
                    val visitEntry = (nextEntry as NewVisit).copy(end = null)
                    val visitESoF = patchContinuedEncounters(s.voyage.esof, visitEntry, esofs, updateTime)
                    NewVisitShipStatus(
                        visit = EntryESoFWrapper(visitEntry, visitESoF),
                        previousVoyage = s.voyage,
                        previousVisit = s.previousVisit,
                        eventBuffer = s.eventBuffer
                    )
                }
            }
            entries.removeFirst()
        } while (true)
        return progressedStatus
    }

    private fun patchContinuedEncounters(
        previousESoF: NewESoF?,
        nextEntry: NewEntry,
        esofs: MutableMap<String, NewESoF>,
        updateTime: Instant
    ): NewESoF? {
        var currentESoF: NewESoF? = null

        val continuedEncounters = previousESoF?.encounters?.filter { it.end == null }
        if (continuedEncounters != null) {
            esofs[previousESoF._id] = previousESoF.copy(
                encounters = previousESoF.encounters.map {
                    if (it.end != null) it
                    else it.copy(end = nextEntry.start)
                }
            )
            currentESoF = createEmptyEsof(nextEntry, updateTime).copy(
                encounters = continuedEncounters.map { it.copy(start = nextEntry.start) }
            )
            esofs[currentESoF._id] = currentESoF
        }
        return currentESoF
    }
}
