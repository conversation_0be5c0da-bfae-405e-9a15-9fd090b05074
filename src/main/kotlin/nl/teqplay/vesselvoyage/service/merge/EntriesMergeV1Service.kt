package nl.teqplay.vesselvoyage.service.merge

import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.model.Entry
import nl.teqplay.vesselvoyage.model.EventPair
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.model.Voyage
import nl.teqplay.vesselvoyage.model.createVoyageId
import nl.teqplay.vesselvoyage.service.EntryService
import org.springframework.stereotype.Service
import java.time.Instant
import java.time.ZoneOffset
import java.util.UUID

/**
 * Service to merge old [Entry] with new ones.
 */
@ProfileProcessing
@Service
class EntriesMergeV1Service(
    private val entryService: EntryService,
) : EntriesMergeService<Visit, Voyage, Entry> {

    override fun Entry.getId(): String = _id
    override fun Entry.getPreviousEntryId(): String? = previousEntryId
    override fun Entry.getNextEntryId(): String? = nextEntryId

    override fun Entry.isFinished(): Boolean = finished
    override fun Entry.isRegenerated(): Boolean = regenerated
    override fun Entry.getStartTime(): Instant = startTime.toInstant()
    override fun Entry.getEndTime(): Instant? = endTime?.toInstant()
    override fun Entry.isVoyage(): Boolean = this is Voyage
    override fun Entry.asVoyage(): Voyage = this as Voyage
    override fun Entry.isVisit(): Boolean = this is Visit
    override fun Entry.asVisit(): Visit = this as Visit

    override fun createEmptyFirstVoyage(visit: Visit) = Voyage(
        _id = createVoyageId(UUID.randomUUID().toString()),
        mmsi = visit.mmsi,
        imo = visit.imo,
        startPortIds = emptyList(),
        startTime = visit.startTime,
        endTime = visit.startTime,
        eta = null,
        esof = null,
        nonMatchingAnchorAreas = null,
        passThroughAreas = null,
        endPortIds = null,
        previousEntryId = null,
        nextEntryId = visit._id,
        regenerated = true,
        finished = true
    ).alignVoyageEndWithVisitStart(visit)

    override fun hasEqualEntryType(e1: Entry, e2: Entry): Boolean = e1::class == e2::class

    override fun Voyage.alignVoyageEndWithVisitStart(visit: Visit): Voyage = this.copy(
        endTime = visit.startTime,
        endPortIds = visit.portAreas.map { it.portId },
        finished = true
    )

    override fun Voyage.alignVoyageStartWithVisitEnd(id: String, visit: Visit): Voyage = this.copy(
        _id = id,
        startTime = requireNotNull(visit.endTime),
        startPortIds = visit.portAreas.map { it.portId }
    )

    override fun Voyage.alignVoyageStart(voyage: Voyage): Voyage = this.copy(
        startTime = voyage.startTime,
        startPortIds = voyage.startPortIds,
        // Ensure we keep previous entry ID unset if what we align with also doesn't have it.
        previousEntryId = if (voyage.getPreviousEntryId() == null) null else voyage.getPreviousEntryId()
    )

    override fun Voyage.alignVoyageEnd(voyage: Voyage): Voyage = this.copy(
        endTime = voyage.endTime,
        endPortIds = voyage.endPortIds,
        finished = voyage.isFinished()
    )

    override fun Entry.copyValues(
        _id: String,
        previousEntryId: String?,
        nextEntryId: String?,
        regenerated: Boolean
    ): Entry = when (this) {
        is Visit -> this.copy(
            _id = _id,
            previousEntryId = previousEntryId,
            nextEntryId = nextEntryId,
            regenerated = regenerated
        )

        is Voyage -> this.copy(
            _id = _id,
            previousEntryId = previousEntryId,
            nextEntryId = nextEntryId,
            regenerated = regenerated
        )
    }

    /**
     * Corrects the [Voyage.esof] so items are still overlapping
     * with the [Voyage.startTime] and [Voyage.endTime].
     */
    override fun Voyage.correctESofAtEdge(): Voyage {
        val esof = this.esof ?: return this

        fun isOverlapping(eventPair: EventPair): Boolean {
            return (this.endTime == null || eventPair.startTime < this.endTime) &&
                (eventPair.endTime == null || requireNotNull(eventPair.endTime) > this.startTime)
        }

        return this.copy(
            esof = esof.copy(
                encounters = esof.encounters.filter(::isOverlapping),
                stops = esof.stops.filter(::isOverlapping)
            )
        )
    }

    override fun findEntriesByIMO(
        imo: String,
        start: Instant,
        end: Instant
    ): List<Entry> = entryService.findEntriesByIMO(
        imo = imo,
        start = start.atZone(ZoneOffset.UTC),
        end = end.atZone(ZoneOffset.UTC),
        limit = Int.MAX_VALUE
    )

    override fun findEntry(
        entryId: String
    ): Entry? = entryService.findEntry(entryId)
}
