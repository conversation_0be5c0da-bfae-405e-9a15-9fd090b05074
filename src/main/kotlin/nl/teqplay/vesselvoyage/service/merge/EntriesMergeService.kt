package nl.teqplay.vesselvoyage.service.merge

import nl.teqplay.skeleton.model.TimeWindow
import nl.teqplay.vesselvoyage.model.createVoyageId
import java.time.Instant
import java.util.UUID

/**
 * Service to merge old [Entry] with new ones.
 */
interface EntriesMergeService<
    Visit : Entry,
    Voyage : Entry,
    Entry : Any
    > {

    fun Entry.getId(): String
    fun Entry.getPreviousEntryId(): String?
    fun Entry.getNextEntryId(): String?

    fun Entry.isFinished(): Boolean
    fun Entry.isRegenerated(): Boolean
    fun Entry.getStartTime(): Instant
    fun Entry.getEndTime(): Instant?
    fun Entry.isVoyage(): Boolean
    fun Entry.asVoyage(): Voyage
    fun Entry.isVisit(): Boolean
    fun Entry.asVisit(): Visit
    fun createEmptyFirstVoyage(visit: Visit): Voyage

    fun hasEqualEntryType(e1: Entry, e2: Entry): Boolean

    fun Voyage.alignVoyageEndWithVisitStart(visit: Visit): Voyage
    fun Voyage.alignVoyageStartWithVisitEnd(id: String, visit: Visit): Voyage
    fun Voyage.alignVoyageStart(voyage: Voyage): Voyage
    fun Voyage.alignVoyageEnd(voyage: Voyage): Voyage
    fun Voyage.correctESofAtEdge(): Voyage

    fun Entry.copyValues(
        _id: String = this.getId(),
        previousEntryId: String? = this.getPreviousEntryId(),
        nextEntryId: String? = this.getNextEntryId(),
        regenerated: Boolean = this.isRegenerated(),
    ): Entry

    fun findEntriesByIMO(
        imo: String,
        start: Instant,
        end: Instant
    ): List<Entry>

    fun findEntry(
        entryId: String
    ): Entry?

    /**
     * Object that indicates what [entries] should be
     * created/updated and which visits/voyages should be deleted.
     */
    data class MergeResult<E : Any>(
        val entries: List<E> = emptyList(),
        val deleteVisitIds: List<String> = emptyList(),
        val deleteVoyageIds: List<String> = emptyList(),
    )

    /**
     * Merge new [entries] with the currently known (old) entries for the specified [imo].
     *
     * When generating the [entries] there is no guarantee that they match exactly
     * with the [window] that was used to generate them. Therefore, old entries are
     * looked up that overlap with this [window]. And only within this [window] the
     * merging takes place.
     *
     * This results into a [MergeResult] that specifies which [MergeResult.entries] should
     * be created/updated and which visits/voyages should be deleted.
     */
    fun merge(
        imo: String,
        window: TimeWindow,
        entries: List<Entry>,
    ): MergeResult<Entry> {
        // Nothing to merge.
        if (entries.isEmpty()) {
            return MergeResult()
        }

        require(entries.all { it.isFinished() }) { "All new entries should be finished." }

        val firstStartTime = entries.first().getStartTime()
        val lastEndTime = requireNotNull(entries.last().getEndTime())

        require(window.from <= firstStartTime && lastEndTime <= window.to) { "Entries must be contained within window." }

        val newEntries = entries.toMutableList()
        val oldEntries = findEntriesByIMO(imo, window.from, window.to).toMutableList()

        val (corruptedVisitIds, corruptedVoyageIds) = pruneCorruptEntries(oldEntries)
        if (oldEntries.isEmpty()) throw NoEntriesInWindowException()

        val missesStart = oldEntries.first().getStartTime() > window.from
        val (firstOldVoyage, lastOldVoyage) = determineFirstAndLastOldVoyages(
            oldEntries = oldEntries,
            firstStartTime = firstStartTime.takeUnless { missesStart }
        )

        // Prune the new entries based on the prior results.
        pruneNewEntries(
            newEntries = newEntries,
            firstOldVoyage = firstOldVoyage.takeUnless { missesStart },
            lastOldVoyage = lastOldVoyage
        )
        require(newEntries.isNotEmpty()) { "New entries are NOT eligible for merging." }

        // If the new entries start with a visit, we will end up needing the first old voyage.
        // Pre-emptively remove this entry to ensure we don't merge with it.
        if (!missesStart && newEntries.isNotEmpty() && newEntries.first().isVisit()) {
            oldEntries.remove(firstOldVoyage)
        }

        val mergePairs = generateMergePairs(oldEntries, newEntries)
        val mergedEntries = generateMergedEntries(mergePairs)

        stitchWithOldEntries(
            mergedEntries = mergedEntries,
            firstOldVoyage = firstOldVoyage.takeUnless { missesStart },
            lastOldVoyage = lastOldVoyage
        )
        generatePreviousAndNextEntryLinks(
            mergedEntries = mergedEntries,
            firstOldVoyage = firstOldVoyage.takeUnless { missesStart },
            lastOldVoyage = lastOldVoyage
        )

        // If we miss the start we have new entries at the beginning.
        // If the first entry is a visit, and we are not performing an inner-merge we can't
        // use the first visit without introducing a 0-second voyage.
        // Pre-emptively add 0-second voyage, maintaining consistency after merge to start and end with voyages.
        if (missesStart) {
            val newFirstEntry = mergedEntries.firstOrNull()
            if (newFirstEntry != null) {
                if (newFirstEntry.isVoyage()) {
                    val firstVoyage = newFirstEntry.asVoyage()
                    mergedEntries[0] = firstVoyage.copyValues(previousEntryId = null)
                } else {
                    val firstVisit = newFirstEntry.asVisit()
                    val emptyVoyage = createEmptyFirstVoyage(firstVisit)
                    mergedEntries[0] = firstVisit.copyValues(previousEntryId = emptyVoyage.getId())
                    mergedEntries.add(0, emptyVoyage)
                }
            }
        }

        val (deleteVisitIds, deleteVoyageIds) = determineEntriesToDelete(mergedEntries, oldEntries)
        val resultingEntries = mergedEntries.map { entry -> entry.copyValues(regenerated = true) }

        return MergeResult(
            entries = resultingEntries,
            deleteVisitIds = deleteVisitIds + corruptedVisitIds,
            deleteVoyageIds = deleteVoyageIds + corruptedVoyageIds,
        )
    }

    /**
     * Based on the [firstStartTime] the [oldEntries] are pruned.
     *
     * Returning the first and last old voyages from [oldEntries].
     */
    private fun determineFirstAndLastOldVoyages(
        oldEntries: MutableList<Entry>,
        firstStartTime: Instant?
    ): Pair<Voyage, Voyage> {
        pruneOldEntries(oldEntries, firstStartTime)
        require(oldEntries.isNotEmpty()) { "No old entries exist to merge with." }

        // After pruning we end up with voyages at both ends.
        // The time within the start of the first voyage and end of the last voyage
        // can be used for merging.
        val firstOldVoyage = oldEntries.first { it.isVoyage() }.asVoyage()
        val lastOldVoyage = oldEntries.last { it.isVoyage() }.asVoyage()

        return firstOldVoyage to lastOldVoyage
    }

    /**
     * Prunes [oldEntries] until there are only [Voyage] at both ends.
     * Merging can be flexible around voyages, but not visits.
     * These voyages determine in which time frame we can confidently merge data.
     */
    private fun pruneOldEntries(
        oldEntries: MutableList<Entry>,
        firstStartTime: Instant?
    ) {
        // First prune entries so it starts and ends with a voyage.
        while (firstStartTime != null && oldEntries.isNotEmpty() && !oldEntries.first().isVoyage()) {
            oldEntries.removeFirst()
        }
        while (oldEntries.isNotEmpty() && !oldEntries.last().isVoyage()) {
            oldEntries.removeLast()
        }
    }

    /**
     * Prunes [newEntries] by removing any entries that started too early or ended too late.
     * Merging can still occur, but without these entries.
     *
     * A larger, and more appropriate, [TimeWindow] should be used for these to be taken into account.
     */
    private fun pruneNewEntries(
        newEntries: MutableList<Entry>,
        firstOldVoyage: Voyage?,
        lastOldVoyage: Voyage
    ) {
        // Remove entries at the front that don't align with the first old voyage.
        if (firstOldVoyage != null) {
            while (newEntries.isNotEmpty()) {
                val firstEntry = newEntries.first()
                val startsBeforeOldVoyage = firstEntry.getStartTime() < firstOldVoyage.getStartTime()
                if (startsBeforeOldVoyage &&
                    // If it's a voyage, should not end before old voyage start.
                    (!firstEntry.isVoyage() || requireNotNull(firstEntry.getEndTime()) <= firstOldVoyage.getStartTime())
                ) {
                    newEntries.removeFirst()
                } else {
                    break
                }
            }
        }

        // Remove entries at the back that don't align with the last old voyage.
        if (lastOldVoyage.isFinished()) {
            while (newEntries.isNotEmpty()) {
                val lastEntry = newEntries.last()
                val startsAfterOldVoyage = lastEntry.getStartTime() >= requireNotNull(lastOldVoyage.getEndTime())
                if (startsAfterOldVoyage ||
                    // If it's not a voyage, should not end after old voyage end. Can't use that data.
                    (!lastEntry.isVoyage() && requireNotNull(lastEntry.getEndTime()) > lastOldVoyage.getEndTime())
                ) {
                    newEntries.removeLast()
                } else {
                    break
                }
            }
        }

        // Remove first voyage if it doesn't align well with the first old voyage.
        // (It should start earlier or at the same time)
        if (firstOldVoyage != null) {
            val firstEntry = newEntries.firstOrNull()
            if (firstEntry != null && firstEntry.isVoyage() && firstEntry.getStartTime() > firstOldVoyage.getStartTime()) {
                newEntries.removeFirst()
            }
        }

        // Remove last voyage if it doesn't align well with the last old voyage.
        // (It should end later or at the same time)
        if (lastOldVoyage.isFinished()) {
            val lastEntry = newEntries.lastOrNull()
            if (lastEntry != null && lastEntry.isVoyage() && requireNotNull(lastEntry.getEndTime()) < lastOldVoyage.getEndTime()) {
                newEntries.removeLast()
            }
        }
    }

    private data class MergePair<E : Any>(
        val new: E,
        val old: E?,
    )

    /**
     * Generates [MergePair] by looping over the [newEntries] and checking if any [oldEntries] overlap.
     * If an item from the [oldEntries] is overlapping, it's removed to not be merged with again.
     */
    private fun generateMergePairs(
        oldEntries: MutableList<Entry>,
        newEntries: MutableList<Entry>
    ): MutableList<MergePair<Entry>> {
        val mergePairs = mutableListOf<MergePair<Entry>>()
        newEntries.forEach { newEntry ->
            val newStartTime = newEntry.getStartTime()
            val newEndTime = requireNotNull(newEntry.getEndTime())
            val oldEntry = oldEntries.find { oldEntry ->
                // Can't merge if not the same type.
                if (!hasEqualEntryType(newEntry, oldEntry)) {
                    return@find false
                }

                val oldStartTime = oldEntry.getStartTime()
                val oldEndTime = oldEntry.getEndTime()

                // Either time windows overlap or they are both not finished.
                return@find (!newEntry.isFinished() && !oldEntry.isFinished()) ||
                    (oldEndTime != null && newStartTime < oldEndTime && newEndTime > oldStartTime)
            }

            // Remove old entry since it can only be merged once.
            if (oldEntry != null) {
                oldEntries.remove(oldEntry)
            }

            mergePairs.add(MergePair(newEntry, oldEntry))
        }
        return mergePairs
    }

    /**
     * Generates merged entries by looking at the [MergePair].
     *
     * If the [MergePair.new] is paired with an [MergePair.old], then the [getId] is kept.
     * Otherwise, the [MergePair.new] (including its own identifier) is used.
     */
    private fun generateMergedEntries(
        mergePairs: MutableList<MergePair<Entry>>
    ): MutableList<Entry> {
        val mergedEntries = mutableListOf<Entry>()
        mergePairs.forEach { pair ->
            if (pair.old == null) mergedEntries.add(pair.new)
            else {
                val entry = pair.new
                when {
                    entry.isVisit() -> mergedEntries.add(entry.copyValues(_id = pair.old.getId()))
                    entry.isVoyage() -> mergedEntries.add(entry.copyValues(_id = pair.old.getId()))
                }
            }
        }
        return mergedEntries
    }

    /**
     * Depending on if the new entries start/end with visits or voyages,
     * these need to be stitched to the old voyages.
     */
    private fun stitchWithOldEntries(
        mergedEntries: MutableList<Entry>,
        firstOldVoyage: Voyage?,
        lastOldVoyage: Voyage
    ) {
        if (mergedEntries.isNotEmpty()) {
            if (firstOldVoyage != null) {
                stitchWithFirstOldVoyage(mergedEntries, firstOldVoyage)
            }
            stitchWithLastOldVoyage(mergedEntries, lastOldVoyage)
        }
    }

    private fun stitchWithFirstOldVoyage(
        mergedEntries: MutableList<Entry>,
        firstOldVoyage: Voyage
    ) {
        val firstEntry = mergedEntries.first()
        when {
            // Add the old voyage, but correct for the new data.
            firstEntry.isVisit() -> mergedEntries.add(
                index = 0,
                element = firstOldVoyage.asVoyage()
                    .alignVoyageEndWithVisitStart(firstEntry.asVisit())
                    .correctESofAtEdge()
            )

            // Correct the new voyage to connect with the old data.
            firstEntry.isVoyage() -> {
                // New entries should be finished, so end time should be available.
                if (requireNotNull(firstEntry.getEndTime()) < firstOldVoyage.getStartTime()) {
                    mergedEntries[0] = firstEntry.asVoyage().correctESofAtEdge()
                } else {
                    mergedEntries[0] = firstEntry.asVoyage().alignVoyageStart(firstOldVoyage).correctESofAtEdge()
                }
            }
        }
    }

    private fun stitchWithLastOldVoyage(mergedEntries: MutableList<Entry>, lastOldVoyage: Voyage) {
        val lastEntry = mergedEntries.last()
        when {
            // Add the old voyage, but correct for the new data.
            lastEntry.isVisit() -> {
                // Ensure we don't use the same voyage id twice.
                var isNewVoyageCreated = false
                val id = if (mergedEntries.any { it.getId() == lastOldVoyage.getId() }) {
                    isNewVoyageCreated = true
                    createVoyageId(UUID.randomUUID().toString())
                } else lastOldVoyage.getId()

                mergedEntries.add(
                    lastOldVoyage.asVoyage()
                        .alignVoyageStartWithVisitEnd(id, lastEntry.asVisit()).correctESofAtEdge()
                )

                // If a new voyage is created we need to fetch the next entry and ensure
                // it links up with the new ID as previous entry ID.
                if (isNewVoyageCreated) {
                    val nextEntryId = lastOldVoyage.getNextEntryId()
                    if (nextEntryId != null) {
                        val oldEntry = findEntry(nextEntryId)
                        if (oldEntry != null) {
                            when {
                                oldEntry.isVisit() -> mergedEntries.add(oldEntry.copyValues(previousEntryId = id))
                                oldEntry.isVoyage() -> mergedEntries.add(oldEntry.copyValues(previousEntryId = id))
                            }
                        }
                    }
                }
            }

            // Correct the new voyage to connect with the old data.
            lastEntry.isVoyage() -> mergedEntries[mergedEntries.lastIndex] = lastEntry.asVoyage()
                .alignVoyageEnd(lastOldVoyage).correctESofAtEdge()
        }
    }

    /**
     * Generate previous and next links by walking over the merged entries and making corrections.
     */
    private fun generatePreviousAndNextEntryLinks(
        mergedEntries: MutableList<Entry>,
        firstOldVoyage: Voyage?,
        lastOldVoyage: Voyage,
    ) {
        mergedEntries.forEachIndexed { index, entry ->
            // First entry should link up with old data.
            val previous = when (index) {
                0 -> when (firstOldVoyage) {
                    // Preserve previous entry ID if first old voyage can't be used.
                    null -> entry.getPreviousEntryId()
                    else -> firstOldVoyage.getPreviousEntryId()
                }
                else -> mergedEntries.getOrNull(index - 1)?.getId()
            }

            // Last entry should link up with old data.
            val next = when {
                lastOldVoyage.getNextEntryId() == entry.getId() -> {
                    // If we've added an entry for fixing up after the last voyage, then we can't use its identifiers
                    // since we'd duplicate identifiers, we need to keep the original next entry id.
                    entry.getNextEntryId()
                }
                index == mergedEntries.lastIndex -> lastOldVoyage.getNextEntryId()
                else -> mergedEntries.getOrNull(index + 1)?.getId()
            }

            when {
                entry.isVisit() -> mergedEntries[index] = entry.copyValues(
                    previousEntryId = previous ?: entry.getPreviousEntryId(),
                    nextEntryId = next ?: entry.getNextEntryId(),
                )

                entry.isVoyage() -> mergedEntries[index] = entry.copyValues(
                    previousEntryId = previous ?: entry.getPreviousEntryId(),
                    nextEntryId = next ?: entry.getNextEntryId(),
                )
            }
        }
    }

    /**
     * Determine corrupted entries from the [oldEntries].
     * These can't possibly be merged with new data, so these are pre-emptively deleted.
     */
    private fun pruneCorruptEntries(
        oldEntries: MutableList<Entry>
    ): Pair<List<String>, List<String>> {
        val corruptedVisitIds = mutableListOf<String>()
        val corruptedVoyageIds = mutableListOf<String>()
        for (index in oldEntries.lastIndex - 1 downTo 0) {
            val oldEntry = oldEntries[index]
            if (!oldEntry.isFinished()) {
                when {
                    oldEntry.isVisit() -> corruptedVisitIds.add(oldEntry.getId())
                    oldEntry.isVoyage() -> corruptedVoyageIds.add(oldEntry.getId())
                }
                oldEntries.removeAt(index)
            }
        }
        return corruptedVisitIds to corruptedVoyageIds
    }

    /**
     * Determine to-be-deleted entries by looking at the [mergedEntries] and
     * which [oldEntries] are not merged.
     *
     * Any remaining [oldEntries] should be deleted. They are replaced by the [mergedEntries].
     */
    private fun determineEntriesToDelete(
        mergedEntries: MutableList<Entry>,
        oldEntries: MutableList<Entry>
    ): Pair<List<String>, List<String>> {
        val mergedEntryIds = mergedEntries.map { it.getId() }.toSet()
        val deleteVisitIds = mutableListOf<String>()
        val deleteVoyageIds = mutableListOf<String>()
        oldEntries.forEach { oldEntry ->
            if (oldEntry.getId() !in mergedEntryIds) {
                when {
                    oldEntry.isVisit() -> deleteVisitIds.add(oldEntry.getId())
                    oldEntry.isVoyage() -> deleteVoyageIds.add(oldEntry.getId())
                }
            }
        }

        return deleteVisitIds to deleteVoyageIds
    }
}

class NoEntriesInWindowException : Exception("No entries found within the specified window.")
