package nl.teqplay.vesselvoyage.service.merge

import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.datasource.NewESoFDataSource
import nl.teqplay.vesselvoyage.model.createVoyageId
import nl.teqplay.vesselvoyage.model.v2.EntryESoFWrapper
import nl.teqplay.vesselvoyage.model.v2.NewEncounter
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter
import nl.teqplay.vesselvoyage.model.v2.NewStop
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import nl.teqplay.vesselvoyage.service.api.EntryV2Service
import org.springframework.stereotype.Service
import java.time.Instant
import java.util.UUID

/**
 * Service to merge old [NewEntry] with new ones.
 */
@ProfileProcessing
@Service
class EntriesMergeV2Service(
    private val entryV2Service: EntryV2Service,
    private val eSoFDataSource: NewESoFDataSource,
) : EntriesMergeService<EntryESoFWrapper<NewVisit>, EntryESoFWrapper<NewVoyage>, EntryESoFWrapper<*>> {

    override fun EntryESoFWrapper<*>.getId(): String = entry._id
    override fun EntryESoFWrapper<*>.getPreviousEntryId(): String? = entry.previous
    override fun EntryESoFWrapper<*>.getNextEntryId(): String? = entry.next

    override fun EntryESoFWrapper<*>.isFinished(): Boolean = entry.end != null
    override fun EntryESoFWrapper<*>.isRegenerated(): Boolean = entry.regenerated
    override fun EntryESoFWrapper<*>.getStartTime(): Instant = entry.start.time
    override fun EntryESoFWrapper<*>.getEndTime(): Instant? = entry.end?.time
    override fun EntryESoFWrapper<*>.isVoyage(): Boolean = this.entry is NewVoyage
    override fun EntryESoFWrapper<*>.isVisit(): Boolean = this.entry is NewVisit

    override fun createEmptyFirstVoyage(
        visit: EntryESoFWrapper<NewVisit>
    ): EntryESoFWrapper<NewVoyage> = EntryESoFWrapper(
        entry = NewVoyage(
            _id = createVoyageId(UUID.randomUUID().toString()),
            imo = visit.entry.imo,
            start = visit.entry.start,
            end = visit.entry.start,
            stops = emptyList(),
            destination = null,
            previous = null,
            next = visit.entry._id,
            actualStart = null,
            originPort = null,
            destinationPort = null,
            regenerated = true
        ),
        esof = null
    ).alignVoyageEndWithVisitStart(visit)

    override fun hasEqualEntryType(e1: EntryESoFWrapper<*>, e2: EntryESoFWrapper<*>): Boolean =
        e1.entry::class == e2.entry::class

    @Suppress("UNCHECKED_CAST")
    override fun EntryESoFWrapper<*>.asVoyage(): EntryESoFWrapper<NewVoyage> = this as EntryESoFWrapper<NewVoyage>

    @Suppress("UNCHECKED_CAST")
    override fun EntryESoFWrapper<*>.asVisit(): EntryESoFWrapper<NewVisit> = this as EntryESoFWrapper<NewVisit>

    override fun EntryESoFWrapper<NewVoyage>.alignVoyageEndWithVisitStart(
        visit: EntryESoFWrapper<NewVisit>
    ): EntryESoFWrapper<NewVoyage> = this.copy(
        entry = this.entry.copy(
            end = visit.entry.start,
            destinationPort = visit.entry.eospAreaActivity.areaId
        )
    )

    override fun EntryESoFWrapper<NewVoyage>.alignVoyageStartWithVisitEnd(
        id: String,
        visit: EntryESoFWrapper<NewVisit>
    ): EntryESoFWrapper<NewVoyage> = this.copy(
        entry = this.entry.copy(
            _id = id,
            start = requireNotNull(visit.entry.end),
            originPort = visit.entry.eospAreaActivity.areaId
        ),
        esof = this.esof?.copy(
            _id = id
        )
    )

    override fun EntryESoFWrapper<NewVoyage>.alignVoyageStart(
        voyage: EntryESoFWrapper<NewVoyage>
    ): EntryESoFWrapper<NewVoyage> = this.copy(
        entry = this.entry.copy(
            start = voyage.entry.start,
            originPort = voyage.entry.originPort,
            // Ensure we keep previous entry ID unset if what we align with also doesn't have it.
            previous = if (voyage.getPreviousEntryId() == null) null else voyage.getPreviousEntryId()
        )
    )

    override fun EntryESoFWrapper<NewVoyage>.alignVoyageEnd(
        voyage: EntryESoFWrapper<NewVoyage>
    ): EntryESoFWrapper<NewVoyage> = this.copy(
        entry = this.entry.copy(
            end = voyage.entry.end,
            destinationPort = voyage.entry.destinationPort
        )
    )

    override fun EntryESoFWrapper<*>.copyValues(
        _id: String,
        previousEntryId: String?,
        nextEntryId: String?,
        regenerated: Boolean,
    ): EntryESoFWrapper<*> = when (val inner = this.entry) {
        is NewVisit -> EntryESoFWrapper(
            entry = inner.copy(
                _id = _id,
                previous = previousEntryId,
                next = nextEntryId,
                regenerated = regenerated,
            ),
            esof = this.esof?.copy(
                _id = _id
            )
        )

        is NewVoyage -> EntryESoFWrapper(
            entry = inner.copy(
                _id = _id,
                previous = previousEntryId,
                next = nextEntryId,
                regenerated = regenerated,
            ),
            esof = this.esof?.copy(
                _id = _id
            )
        )
    }

    /**
     * Corrects the [NewVoyage.stops] and [EntryESoFWrapper.esof] so items are still overlapping
     * with the [NewVoyage.start] and [NewVoyage.end].
     */
    override fun EntryESoFWrapper<NewVoyage>.correctESofAtEdge(): EntryESoFWrapper<NewVoyage> {
        fun isOverlapping(stop: NewStop): Boolean {
            return (this.entry.end == null || stop.start.time < requireNotNull(this.entry.end).time) &&
                (stop.end == null || requireNotNull(stop.end).time > this.entry.start.time)
        }

        fun isOverlapping(encounter: NewEncounter): Boolean {
            return (this.entry.end == null || encounter.start.time < requireNotNull(this.entry.end).time) &&
                (encounter.end == null || requireNotNull(encounter.end).time > this.entry.start.time)
        }

        return this.copy(
            entry = this.entry.copy(
                stops = this.entry.stops
                    .filter(::isOverlapping)
                    .map { stop ->
                        val entryStartTime = this.entry.start.time
                        val entryEndTime = this.entry.end?.time
                            ?: return@map stop
                        stop.copy(
                            start = stop.start.copy(
                                time = stop.start.time.coerceAtLeast(entryStartTime)
                            ),
                            end = stop.end?.let { end ->
                                end.copy(time = end.time.coerceAtMost(entryEndTime))
                            }
                        )
                    }
                    .filter { it.end == null || it.start.time < requireNotNull(it.end).time }
            ),
            esof = this.esof?.let { esof ->
                esof.copy(
                    encounters = esof.encounters.filter(::isOverlapping)
                )
            }
        )
    }

    override fun findEntriesByIMO(
        imo: String,
        start: Instant,
        end: Instant
    ): List<EntryESoFWrapper<*>> {
        val entries = entryV2Service.findByImoAndTimeRange(imo.toInt(), start, end, NewEntryFinishedFilter.ANY, null)
        val entryIds = entries.map { it._id }
        val esofs = eSoFDataSource.findByIds(entryIds).associateBy { it._id }
        return entries.map { entry -> EntryESoFWrapper(entry, esofs[entry._id]) }
    }

    override fun findEntry(
        entryId: String
    ): EntryESoFWrapper<*>? {
        val entry = entryV2Service.findEntry(entryId) ?: return null
        val esof = eSoFDataSource.findById(entryId)
        return EntryESoFWrapper(entry, esof)
    }
}
