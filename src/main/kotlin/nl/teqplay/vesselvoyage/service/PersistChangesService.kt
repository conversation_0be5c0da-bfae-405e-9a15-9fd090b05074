package nl.teqplay.vesselvoyage.service

import com.mongodb.MongoBulkWriteException
import com.mongodb.bulk.BulkWriteError
import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.datasource.NewESoFDataSource
import nl.teqplay.vesselvoyage.datasource.NewVisitDataSource
import nl.teqplay.vesselvoyage.datasource.NewVoyageDataSource
import nl.teqplay.vesselvoyage.model.v2.ESoFChange
import nl.teqplay.vesselvoyage.model.v2.NewChange
import nl.teqplay.vesselvoyage.model.v2.VisitChange
import nl.teqplay.vesselvoyage.model.v2.VoyageChange
import org.springframework.stereotype.Service

@ProfileProcessing
@Service
class PersistChangesService(
    private val newVisitDataSource: NewVisitDataSource,
    private val newVoyageDataSource: NewVoyageDataSource,
    private val newEsofDataSource: NewESoFDataSource,
) {
    private val log = KotlinLogging.logger {}

    fun persistChanges(changes: List<NewChange<*>>) {
        val visitChanges = changes.filterIsInstance<VisitChange>()
        if (visitChanges.isNotEmpty()) {
            log.trace { "Persisting ${visitChanges.size} VisitChange(s)" }
            try {
                newVisitDataSource.bulkWriteChanges(visitChanges)
            } catch (e: MongoBulkWriteException) {
                log.error(e) { "Failed to persist Visit changes" }
                logDebugBulkWriteIssues(e, visitChanges)
            } catch (e: Exception) {
                log.error(e) { "Unexpected error while persisting Visit changes" }
            }
        }

        val voyageChanges = changes.filterIsInstance<VoyageChange>()
        if (voyageChanges.isNotEmpty()) {
            log.trace { "Persisting ${voyageChanges.size} VoyageChange(s)" }
            try {
                newVoyageDataSource.bulkWriteChanges(voyageChanges)
            } catch (e: MongoBulkWriteException) {
                log.error(e) { "Failed to persist Voyage changes" }
                logDebugBulkWriteIssues(e, voyageChanges)
            } catch (e: Exception) {
                log.error(e) { "Unexpected error while persisting Voyage changes" }
            }
        }

        val esoFChanges = changes.filterIsInstance<ESoFChange>()
        if (esoFChanges.isNotEmpty()) {
            log.trace { "Persisting ${esoFChanges.size} ESoFChange(s)" }
            try {
                newEsofDataSource.bulkWriteChanges(esoFChanges)
            } catch (e: MongoBulkWriteException) {
                log.error(e) { "Failed to persist ESoF changes" }
                logDebugBulkWriteIssues(e, esoFChanges)
            } catch (e: Exception) {
                log.error(e) { "Unexpected error while persisting ESoF changes" }
            }
        }
    }

    /**
     * Logs the details of the bulk write issues for debugging purposes.
     */
    private fun logDebugBulkWriteIssues(e: MongoBulkWriteException, changes: List<NewChange<*>>) {
        // Only try to get additional debug information if debug logging is enabled
        if (!log.isDebugEnabled()) {
            return
        }
        val failedIds = mutableListOf<String>()
        e.writeErrors.forEach { writeError ->
            val id = extractFailedIdsFromExceptionMessage(writeError)
            if (id != null) {
                failedIds.add(id)
            } else {
                log.warn { "Failed write error without _id: $writeError" }
            }
        }

        val failedEntries = when (changes.firstOrNull()) {
            is VisitChange -> newVisitDataSource.findByIds(failedIds.toSet())
            is VoyageChange -> newVoyageDataSource.findByIds(failedIds.toSet())
            is ESoFChange -> newEsofDataSource.findByIds(failedIds)
            else -> {
                emptyList()
            }
        }
        log.debug { "Failed IDs: $failedIds" }
        log.debug { "Current in DB: $failedEntries" }
        log.debug { "Attempted to Write: $changes" }
    }

    /**
     * Extracts the ID from a [BulkWriteError].
     */
    private val idRegex = Regex("""dup key: \{ _id: "([^"]+)" \}""")

    /**
     * Extracts the ID from the BulkWriteError message.
     *
     * @param error The BulkWriteError containing the error message.
     * @return The extracted ID as a String, or null if not found.
     */
    private fun extractFailedIdsFromExceptionMessage(error: BulkWriteError): String? {
        return idRegex.find(error.message)?.groups?.get(1)?.value
    }
}
