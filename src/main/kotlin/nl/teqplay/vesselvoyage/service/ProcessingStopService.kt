package nl.teqplay.vesselvoyage.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.datasource.NewVisitDataSource
import nl.teqplay.vesselvoyage.datasource.NewVoyageDataSource
import org.springframework.stereotype.Service

@ProfileProcessing
@Service
class ProcessingStopService(
    private val visitDataSource: NewVisitDataSource,
    private val voyageDataSource: NewVoyageDataSource
) {
    private val log = KotlinLogging.logger {}

    fun fixBrokenStops() {
        log.info { "Fixing broken stops in visits and voyages" }
        visitDataSource.fixBrokenStopsInBatches { visit, mergedStops -> visit.copy(stops = mergedStops) }
        log.info { "Fixed broken stops of visits" }
        voyageDataSource.fixBrokenStopsInBatches { voyage, mergedStops -> voyage.copy(stops = mergedStops) }
        log.info { "Fixed broken stops of voyages" }
    }
}
