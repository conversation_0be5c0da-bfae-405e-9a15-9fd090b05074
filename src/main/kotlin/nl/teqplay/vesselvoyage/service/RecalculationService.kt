package nl.teqplay.vesselvoyage.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.datasource.CalculationInfoDataSource
import nl.teqplay.vesselvoyage.datasource.VisitDataSource
import nl.teqplay.vesselvoyage.model.VesselRecalculation
import nl.teqplay.vesselvoyage.properties.ApplicationProperties
import nl.teqplay.vesselvoyage.properties.RecalculationProperties
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import org.springframework.stereotype.Service
import java.time.Duration
import java.time.ZonedDateTime
import kotlin.system.measureTimeMillis

private val log = KotlinLogging.logger {}

@ProfileProcessing
@Service
class RecalculationService(
    private val properties: RecalculationProperties,
    private val appProperties: ApplicationProperties,
    private val calculationInfoDataSource: CalculationInfoDataSource,
    private val entryProcessingService: EntryProcessingService,
    private val staticShipInfoService: StaticShipInfoService,
    private val infraService: InfraService,
    private val visitDataSource: VisitDataSource,
    private val slackMessageService: SlackMessageService?
) {
    @Volatile
    private var running = false
    private val recalculationEnabled = properties.enabled
    private val recalculateShipInterval = properties.recalculateShipInterval

    private val lockRecalculableShips = Object()
    private var recalculableShips = mutableListOf<String>()

    private val recalculationThreadPool = createThreadPool()

    fun start() {
        val enabledText = if (recalculationEnabled) {
            "enabled"
        } else {
            "disabled"
        }
        log.info { "Automatic recalculation $enabledText" }

        refreshRecalculableShips()
        running = true
    }

    fun recalculateEvents(imos: Set<String>, recalculationDuration: Duration): Map<String, Int> {
        log.info { "Starting recalculation story for ${imos.size} ships" }
        val from = ZonedDateTime.now().minus(recalculationDuration)
        var result: Map<String, Int>

        val durationMs = measureTimeMillis {
            result = imos.associateWith { imo -> entryProcessingService.regeneratePartially(imo, from) }
        }

        log.info { "Finished recalculating story partially for ${imos.size} ships (${durationMs}ms)" }

        return result
    }

    fun recalculate(imo: String, forceRegenerateTraces: Boolean): Int {
        log.info { "Recalculating story for ship with IMO $imo" }

        val start = System.currentTimeMillis()

        val now = ZonedDateTime.now()
        val eventCount = entryProcessingService.regenerateAllEventsAndTracesByIMO(imo, forceRegenerateTraces)
        calculationInfoDataSource.createOrReplace(VesselRecalculation(imo, now))

        synchronized(lockRecalculableShips) {
            recalculableShips.remove(imo)
        }

        val end = System.currentTimeMillis()
        val duration = end - start

        log.info { "Recalculated story for ship with IMO $imo in $duration ms" }

        return eventCount
    }

    /**
     * Set the provided list of IMO numbers as recalculable and mark all other known IMO numbers as recalculated.
     *
     * @param imos All IMO numbers that need to be recalculated.
     * @return A boolean being true if everything went correct or false when something else occurred,
     *  and a message containing how the recalculable was set or the reason of failing.
     */
    fun setRecalculable(imos: Set<String>): Pair<Boolean, String> {
        if (!recalculationEnabled) {
            return false to "Recalculation disabled, ignoring recalculation set up".also { log.warn(it) }
        }

        if (!(staticShipInfoService.isShipCacheValid() && infraService.isPomaDataValid())) {
            return false to "Can't reset selected imos if not initialized properly".also { log.error(it) }
        }

        val start = System.currentTimeMillis()
        val allImos = staticShipInfoService.getAllImos()
        val recalculationInfos = allImos.map { VesselRecalculation(_id = it, calculatedAt = ZonedDateTime.now()) }

        // Reset all recalculation to contain all vessels, so we ignore all imo numbers that we don't want to recalculate
        calculationInfoDataSource.deleteAll()
        calculationInfoDataSource.insertMany(recalculationInfos)

        // Delete the ships we want to recalculate
        calculationInfoDataSource.deleteManyByImo(imos)
        recalculableShips = imos.toMutableList()

        val end = System.currentTimeMillis()
        val duration = end - start

        return true to logRefreshRecalculableShips(duration)
    }

    // the reset function will not delete any story or traces, but will mark
    // all ships to be recalculated again
    fun reset() {
        calculationInfoDataSource.deleteAll()

        refreshRecalculableShips()
    }

    @Scheduled(cron = "\${recalculation.refresh-recalculable-ships-cron}", zone = "UTC")
    protected final fun refreshRecalculableShips() {
        if (!recalculationEnabled) {
            return
        }

        val startTime = ZonedDateTime.now()
        try {
            log.info { "Start refreshing recalculatable ships for V1" }
            synchronized(lockRecalculableShips) {
                val start = System.currentTimeMillis()

                val allImos = staticShipInfoService.getAllImos()
                val deletedImos = visitDataSource.distinctImos() - allImos
                val calculatedImos = calculationInfoDataSource.findAll()
                    .map { it._id }
                    .toSet()
                recalculableShips = (allImos - (calculatedImos - deletedImos))
                    .sorted()
                    .toMutableList()

                val end = System.currentTimeMillis()
                val duration = end - start

                logRefreshRecalculableShips(duration) {
                    "${allImos.size} ships in total, ${deletedImos.size} deleted ships, ${calculatedImos.size} calculated ships, "
                }
            }
            log.info { "Finished refreshing recalculatable ships for V1" }
        } catch (e: Throwable) {
            val failTime = ZonedDateTime.now()
            log.error(e) { "Error refreshing recalculable ships: ${e.message}" }
            slackMessageService?.sendMessage(
                title = "Scheduled task failed: RecalculationService.refreshRecalculableShips",
                text = "Error refreshing recalculable ships: ${e.message}\nStarted at: $startTime\nFailed at: $failTime",
                barColor = SLACK_COLOR_RED
            )
        }
    }

    private fun logRefreshRecalculableShips(duration: Long, additionalLogLines: () -> String = { "" }): String {
        val totalRecalculationDuration = recalculateShipInterval.multipliedBy(recalculableShips.size.toLong())

        val message = "Refreshed recalculable ships in $duration ms: " +
            additionalLogLines() +
            "${recalculableShips.size} recalculable ships. " +
            "With the configured interval of recalculating one ship every $recalculateShipInterval, " +
            "we will be done by ${ZonedDateTime.now().plus(totalRecalculationDuration)}. " +
            "Don't forget to bring cake"

        return message.also { log.info(it) }
    }

    @Scheduled(fixedDelayString = "\${recalculation.recalculate-ship-interval}")
    fun recalculateNextShip() {
        val startTime = ZonedDateTime.now()
        try {
            if (!recalculationEnabled) {
                return
            }

            if (!running) {
                log.info { "Recalculation is enabled but not running, trying again in ${properties.recalculateShipInterval}" }
                return
            }

            val imo = synchronized(lockRecalculableShips) {
                recalculableShips.removeFirstOrNull()
            } ?: return

            log.info { "Start recalculating ship for V1 (imo = $imo)" }
            // prevent the server from blowing up due to "bad" ships having a huge amount of mmsi's
            // (like 1234567 and 4194304), which is mostly a problem when generating the trace from AIS.
            val maxMmsiCount = properties.maxMmsiCount
            val mmsiCount = staticShipInfoService.getMmsiMappingFromImo(imo)?.size ?: 0
            if (mmsiCount > maxMmsiCount) {
                val message = "Cancelling recalculation of ship with IMO $imo because it has too many MMSI's. " +
                    "Please recalculate this ship by hand. " +
                    "(number of mmsis: $mmsiCount, max: $maxMmsiCount)"

                log.warn { message }

                slackMessageService?.sendMessage(
                    title = "VesselVoyage ${appProperties.environment} recalculation of ship with IMO $imo failed",
                    text = "Recalculation of this ship will be tried again later on. " +
                        "Error message: $message",
                    barColor = SLACK_COLOR_RED
                )

                return
            }

            recalculationThreadPool.execute {
                try {
                    log.info { "Scheduled recalculation of story for ship with IMO $imo" }

                    // double check whether the ship isn't recalculated manually already
                    val calculationInfo = calculationInfoDataSource.find(imo)
                    if (calculationInfo == null) {
                        recalculate(imo, forceRegenerateTraces = false)
                    }
                } catch (err: Throwable) {
                    log.error(err) { "Failed to recalculate ship with IMO $imo" }

                    slackMessageService?.sendMessage(
                        title = "VesselVoyage ${appProperties.environment} recalculation of ship with IMO $imo failed",
                        text = "Recalculation of this ship will be tried again later on. " +
                            "Error message: ${err.message}",
                        barColor = SLACK_COLOR_RED
                    )
                }
            }

            log.info { "Finished recalculating ship for V1 (imo = $imo)" }
        } catch (e: Throwable) {
            val failTime = ZonedDateTime.now()
            log.error(e) { "Error in recalculateNextShip: ${e.message}" }
            slackMessageService?.sendMessage(
                title = "Scheduled task failed: RecalculationService.recalculateNextShip",
                text = "Error in scheduled ship recalculation task: ${e.message}\n" +
                    "This affects the automatic recalculation pipeline.\n" +
                    "Started at: $startTime\nFailed at: $failTime",
                barColor = SLACK_COLOR_RED
            )
        }
    }

    private fun createThreadPool(): ThreadPoolTaskExecutor {
        val threadPoolSize = properties.threadPoolSize

        log.info { "Creating recalculation thread pool (threadPoolSize: $threadPoolSize)" }

        val threadPool = ThreadPoolTaskExecutor()
        threadPool.corePoolSize = threadPoolSize
        threadPool.setThreadNamePrefix("recalculation-")
        threadPool.setWaitForTasksToCompleteOnShutdown(true)
        threadPool.initialize()

        return threadPool
    }

    fun shutdown() {
        log.info { "Shutting down recalculation" }
        running = false
        recalculationThreadPool.shutdown()
    }
}
