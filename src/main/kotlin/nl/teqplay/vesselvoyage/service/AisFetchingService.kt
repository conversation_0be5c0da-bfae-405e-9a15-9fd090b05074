package nl.teqplay.vesselvoyage.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.annotation.ProfileRevents
import nl.teqplay.vesselvoyage.logic.toLocationTimeHeading
import nl.teqplay.vesselvoyage.model.Stop
import nl.teqplay.vesselvoyage.model.StopDetectionLocation
import nl.teqplay.vesselvoyage.properties.AisFetchingProperties
import nl.teqplay.vesselvoyage.properties.TraceProperties
import nl.teqplay.vesselvoyage.util.MMSINotFoundException
import nl.teqplay.vesselvoyage.util.groupOverlapping
import org.springframework.stereotype.Service
import java.time.Duration
import java.time.ZonedDateTime

@ProfileRevents
@ProfileProcessing
@ProfileApi
@Service
class AisFetchingService(
    private val aisFetchingProperties: AisFetchingProperties,
    private val shipHistoryService: ShipHistoryService,
    private val staticShipInfoService: StaticShipInfoService,
    private val traceProperties: TraceProperties
) {
    private val log = KotlinLogging.logger {}

    fun getShipTrace(imo: String, startTime: ZonedDateTime, endTime: ZonedDateTime): List<AisHistoricMessage> {
        return try {
            getTraceFromAIS(imo, startTime, endTime) { ais ->
                ais
            }.sortedBy { it.messageTime }
        } catch (ex: MMSINotFoundException) {
            log.debug { "Could not find any mmsis when trying to retrieve ais for the vessel with imo: $imo" }
            emptyList()
        }
    }

    fun getTraceForStopDetection(imo: String, stop: Stop): List<StopDetectionLocation> {
        val endTime = stop.endTime ?: return emptyList()

        return try {
            getTraceFromAIS(imo, stop.startTime, endTime) { ais ->
                ais.map { it.toLocationTimeHeading() }
            }.sortedBy { it.time }
        } catch (ex: MMSINotFoundException) {
            log.debug { "Could not find any mmsis when trying to retrieve for the vessel with imo: $imo" }
            emptyList()
        }
    }

    fun <T> getTraceFromAIS(imo: String, startTime: ZonedDateTime, endTime: ZonedDateTime?, transformChunk: (ais: List<AisHistoricMessage>) -> List<T>): List<T> {
        val to = endTime ?: ZonedDateTime.now()

        if (Duration.between(startTime, to).toDays() > traceProperties.maxRequestLengthDays) {
            log.debug {
                "Cannot fetch AIS data for IMO $imo: Trace request length (${Duration.between(startTime, to)}) " +
                    "is bigger than max length (${traceProperties.maxRequestLengthDays} days), aborting"
            }
            return emptyList()
        }

        val mmsiMapping = staticShipInfoService.getMmsiMappingFromImoAndDateRange(imo, startTime, endTime)?.toList()
            ?: throw MMSINotFoundException(
                "Cannot fetch AIS data for IMO $imo: " +
                    "no corresponding MMSI number(s) found"
            )

        log.trace {
            "Fetch historic trace for IMO: $imo, mmsis: ${mmsiMapping.map { it.mmsi }}, " +
                "start: $startTime, end: $endTime"
        }

        return mmsiMapping.groupOverlapping(startTime, endTime).map { (groupedMmsis, groupedStartTime, groupedEndTime) ->
            if (groupedMmsis.isEmpty()) {
                log.warn { "Could not get trace for IMO: $imo it had no MMSI's from $groupedStartTime until $groupedEndTime" }
                emptyList()
            } else if (to.isAfter(startTime)) {
                shipHistoryService.queryAisHistoryInChunks(
                    mmsis = groupedMmsis,
                    from = groupedStartTime,
                    to = groupedEndTime ?: ZonedDateTime.now(),
                    maxDays = aisFetchingProperties.maxDays,
                    transformChunk = { ais ->
                        // TODO replace this with being able to filter in the query endpoint instead
                        val relevantAis = ais.filterNot { it.historic }
                        transformChunk(relevantAis)
                    },
                    accumulateChunks = { it.flatten() }
                )
            } else {
                emptyList()
            }
        }.flatten()
    }
}
