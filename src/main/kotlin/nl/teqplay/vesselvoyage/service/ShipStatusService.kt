package nl.teqplay.vesselvoyage.service

import nl.teqplay.vesselvoyage.datasource.NewESoFDataSource
import nl.teqplay.vesselvoyage.datasource.NewEntryDataSource
import nl.teqplay.vesselvoyage.datasource.NewShipStatusStateDataSource
import nl.teqplay.vesselvoyage.datasource.NewVisitDataSource
import nl.teqplay.vesselvoyage.datasource.NewVoyageDataSource
import nl.teqplay.vesselvoyage.datasource.VisitDataSource
import nl.teqplay.vesselvoyage.datasource.VoyageDataSource
import nl.teqplay.vesselvoyage.model.Entry
import nl.teqplay.vesselvoyage.model.InitialShipStatus
import nl.teqplay.vesselvoyage.model.ShipStatus
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.EntryESoFWrapper
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus
import java.util.Collections

abstract class ShipStatusService(
    private val visitDataSource: VisitDataSource,
    private val voyageDataSource: VoyageDataSource,
    protected val newVisitDataSource: NewVisitDataSource,
    protected val newVoyageDataSource: NewVoyageDataSource,
    private val newESoFDataSource: NewESoFDataSource,
    private val newShipStatusStateDataSource: NewShipStatusStateDataSource,
) {

    @Deprecated("V1")
    abstract operator fun get(imo: String): ShipStatus

    abstract fun getStatus(imo: Int): NewShipStatus

    @Deprecated("V1")
    abstract fun getEntry(imo: String): Entry?

    @Deprecated("V1")
    abstract operator fun set(imo: String, status: ShipStatus)

    @Deprecated("V1")
    abstract fun remove(imo: String)

    abstract fun removeStatus(imo: Int)

    protected fun getCurrentNewStatus(imo: Int): NewShipStatus {
        return loadNewShipStatus(imo)
    }

    @Deprecated("V1")
    protected fun getCurrentStatus(imo: String): ShipStatus {
        return loadShipStatus(imo)
    }

    private fun loadNewShipStatus(imo: Int): NewShipStatus {
        val (lastVisit, secondLastVisit) = getLastTwoAsPairByImo(newVisitDataSource, imo)
        val (lastVoyage, secondLastVoyage) = getLastTwoAsPairByImo(newVoyageDataSource, imo)
        val state = newShipStatusStateDataSource.get(imo)
        val eventBuffer = state?.eventBuffer ?: Collections.synchronizedList(emptyList())

        // We only need to check if the last visit is null because Voyages can't be created without a Visit
        if (lastVisit == null) {
            // We don't know anything about this vessel, so we can return an initial state
            return NewInitialShipStatus(eventBuffer)
        }

        if (lastVoyage == null) {
            // If we don't have a voyage it means we only started a Visit which is still ongoing
            return NewVisitShipStatus(
                visit = lastVisit.getAsEntryESoFWrapper(),
                previousVoyage = null,
                previousVisit = null,
                eventBuffer = eventBuffer
            )
        }

        // We do bigger than and equals as the last voyage can have the same start time when we have 0-second voyages
        return if (lastVisit.start.time >= lastVoyage.start.time) {
            NewVisitShipStatus(
                visit = lastVisit.getAsEntryESoFWrapper(),
                previousVoyage = lastVoyage.getAsEntryESoFWrapper(),
                previousVisit = secondLastVisit?.getAsEntryESoFWrapper(),
                eventBuffer = eventBuffer
            )
        } else {
            NewVoyageShipStatus(
                voyage = lastVoyage.getAsEntryESoFWrapper(),
                previousVisit = lastVisit.getAsEntryESoFWrapper(),
                previousVoyage = secondLastVoyage?.getAsEntryESoFWrapper(),
                eventBuffer = eventBuffer
            )
        }
    }

    /**
     * Load in the related esof if there is any.
     *
     * @return The provided entry and esof in a wrapper class.
     */
    private fun <T : NewEntry> T.getAsEntryESoFWrapper(): EntryESoFWrapper<T> {
        val esof = newESoFDataSource.findById(this._id)

        return EntryESoFWrapper(entry = this, esof = esof)
    }

    private fun <T : NewEntry> getLastTwoAsPairByImo(dataSource: NewEntryDataSource<T>, imo: Int): Pair<T?, T?> {
        val entries = dataSource.findLastByImo(
            imo = imo,
            limit = 2,
            finished = NewEntryFinishedFilter.ANY,
            confirmed = null
        )
        val firstEntry = entries.getOrNull(0)
        val secondEntry = entries.getOrNull(1)

        return firstEntry to secondEntry
    }

    @Deprecated("V1")
    private fun loadShipStatus(imo: String): ShipStatus {
        val (lastVisit, secondLastVisit) = visitDataSource.findLastTwoByIMO(imo)
        val (lastVoyage, secondLastVoyage) = voyageDataSource.findLastTwoByIMO(imo)

        // TODO: test integrity: the found visits/voyages must be successive and have matching previousEntryId/nextEntryId

        return if (lastVisit?.startTime != null &&
            (lastVoyage == null || lastVisit.startTime > lastVoyage.startTime)
        ) {
            val previousVoyage = if (lastVoyage != null && lastVoyage.finished) { lastVoyage } else { null }

            VisitShipStatus(lastVisit, previousVoyage, secondLastVisit)
        } else if (lastVoyage != null) {
            val previousVisit = if (lastVisit != null && lastVisit.finished) { lastVisit } else { null }

            VoyageShipStatus(lastVoyage, previousVisit, secondLastVoyage)
        } else {
            InitialShipStatus
        }
    }
}
