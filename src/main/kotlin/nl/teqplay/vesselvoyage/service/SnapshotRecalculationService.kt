package nl.teqplay.vesselvoyage.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.datasource.SnapshotShipStatusDataSource
import nl.teqplay.vesselvoyage.datasource.VisitDataSource
import nl.teqplay.vesselvoyage.model.internal.SnapshotShipStatus
import nl.teqplay.vesselvoyage.properties.RecalculationProperties
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.time.ZonedDateTime
import kotlin.system.measureTimeMillis

@ProfileProcessing
@Service
class SnapshotRecalculationService(
    private val properties: RecalculationProperties,
    private val dataSource: SnapshotShipStatusDataSource,
    private val visitDataSource: VisitDataSource,
    private val entryProcessingService: EntryProcessingService,
    private val slackMessageService: SlackMessageService?
) {
    @Bean
    @ConditionalOnProperty(name = ["recalculation.snapshot-enabled"], havingValue = "true")
    fun snapshotRecalculationJob() = SnapshotRecalculationJob(properties, dataSource, entryProcessingService, slackMessageService)

    /**
     * Create snapshots for all IMO numbers known.
     * Override all current snapshots for already existing ones.
     */
    fun createAndSaveSnapshots() {
        val allImos = visitDataSource.distinctImos()
        val now = ZonedDateTime.now()

        allImos.forEach { imo ->
            val currentStatus = entryProcessingService.getCurrentShipStatus(imo)
            val newSnapshot = SnapshotShipStatus(
                imo = imo,
                status = currentStatus,
                createdAt = now
            )

            dataSource.save(newSnapshot)
        }
    }

    class SnapshotRecalculationJob(
        private val properties: RecalculationProperties,
        private val dataSource: SnapshotShipStatusDataSource,
        private val entryProcessingService: EntryProcessingService,
        private val slackMessageService: SlackMessageService?
    ) {
        private val log = KotlinLogging.logger {}

        @Scheduled(fixedRateString = "\${recalculation.snapshot-interval}")
        fun recalculateBySnapshots() {
            val startTime = ZonedDateTime.now()
            try {
                val now = ZonedDateTime.now()
                val start: ZonedDateTime = now.minus(properties.snapshotAge)

                val snapshots = dataSource.findOldSnapshots(start)

                log.info { "Recalculating ${snapshots.size} imo numbers" }
                val durationMs = measureTimeMillis {
                    snapshots.forEach { snapshot ->
                        // We drop all found snapshots to avoid the possibility to recalculate the same snapshot multiple times
                        dataSource.delete(snapshot)
                    }

                    snapshots.forEach { snapshot ->
                        entryProcessingService.regenerateBySnapshot(snapshot)

                        val newStatus = entryProcessingService.getCurrentShipStatus(snapshot.imo)
                        val newSnapshot = SnapshotShipStatus(
                            imo = snapshot.imo,
                            status = newStatus,
                            createdAt = now
                        )

                        dataSource.save(newSnapshot)
                        log.info { "Saved new snapshot (imo: ${newSnapshot.imo}, createdAt: ${newSnapshot.createdAt})" }
                    }
                }
                log.info { "Finished recalculating all ${snapshots.size} snapshots (${durationMs}ms)" }
            } catch (e: Throwable) {
                val failTime = ZonedDateTime.now()
                log.error(e) { "Error in recalculateBySnapshots: ${e.message}" }
                slackMessageService?.sendMessage(
                    title = "Scheduled task failed: SnapshotRecalculationService.recalculateBySnapshots",
                    text = "Error recalculating by snapshots: ${e.message}\nStarted at: $startTime\nFailed at: $failTime",
                    barColor = SLACK_COLOR_RED
                )
            }
        }
    }
}
