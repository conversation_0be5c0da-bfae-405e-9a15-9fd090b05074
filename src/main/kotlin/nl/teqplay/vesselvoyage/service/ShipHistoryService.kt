package nl.teqplay.vesselvoyage.service

import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.skeleton.model.TimeWindow
import nl.teqplay.vesselvoyage.client.AisHistoryClient
import nl.teqplay.vesselvoyage.properties.AisFetchingProperties
import nl.teqplay.vesselvoyage.util.generateTimeWindows
import org.springframework.stereotype.Service
import java.time.ZonedDateTime

@Service
class ShipHistoryService(
    private val properties: AisFetchingProperties,
    private val aisHistoryClient: AisHistoryClient,
) {
    fun <T, U> queryAisHistoryInChunks(
        mmsis: Set<String>,
        from: ZonedDateTime,
        to: ZonedDateTime,
        maxDays: Int,
        transformChunk: (history: List<AisHistoricMessage>) -> T,
        accumulateChunks: (chunks: List<T>) -> U
    ): U {
        val chunkDuration = properties.chunkDuration

        return generateTimeWindows(from, to, chunkDuration)
            .map { timeWindow ->
                val query = mmsis.mapNotNull { mmsi ->
                    val mmsiInt = mmsi.toIntOrNull() ?: return@mapNotNull null
                    AisHistoryClient.AisHistoricMessageMmsiQuery(
                        mmsi = mmsiInt,
                        window = TimeWindow(
                            from = timeWindow.start.toInstant(),
                            to = timeWindow.end.toInstant()
                        )
                    )
                }

                aisHistoryClient.queryAisHistory(query, maxDays)
            }
            .map { transformChunk(it) }
            .let { accumulateChunks(it) }
    }
}
