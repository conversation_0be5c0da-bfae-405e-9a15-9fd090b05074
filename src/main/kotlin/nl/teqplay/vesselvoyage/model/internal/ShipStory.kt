package nl.teqplay.vesselvoyage.model.internal

import nl.teqplay.vesselvoyage.model.lightweight.poma.PomaModel
import nl.teqplay.vesselvoyage.model.v2.NewESoF
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewTrace

data class ShipStory(
    val imo: Int,
    val items: List<ShipStoryItem>,
    val pomaEntities: Map<InfraAreaType, List<PomaModel>>
) {
    data class ShipStoryItem(
        val mmsis: List<Int>,
        val entry: NewEntry,
        val esof: NewESoF?,
        val trace: NewTrace?
    )
}
