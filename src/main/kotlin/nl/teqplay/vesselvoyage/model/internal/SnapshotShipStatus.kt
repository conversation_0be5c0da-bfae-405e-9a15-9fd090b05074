package nl.teqplay.vesselvoyage.model.internal

import nl.teqplay.vesselvoyage.model.ShipStatus
import org.bson.codecs.pojo.annotations.BsonId
import java.time.ZonedDateTime

data class SnapshotShipStatus(
    /**
     * The IMO number of the vessel this snapshot belongs to.
     * We want to enforce to have only 1 snapshot per vessel, giving the reason why this is the id field in mongo.
     */
    @BsonId
    val imo: String,

    /**
     * The status known of the ship when this snapshot was taken.
     */
    val status: ShipStatus,

    /**
     * The time the snapshot was created.
     */
    val createdAt: ZonedDateTime
)
