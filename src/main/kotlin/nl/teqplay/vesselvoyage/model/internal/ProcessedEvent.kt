package nl.teqplay.vesselvoyage.model.internal

import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.platform.model.event.TeqplayEvent
import nl.teqplay.vesselvoyage.model.Change
import nl.teqplay.vesselvoyage.model.EventProcessingIssue

data class ProcessedEvent(
    val teqplayEvent: TeqplayEvent?,
    val event: Event?,
    val changes: List<Change>,
    val issues: List<EventProcessingIssue>
)
