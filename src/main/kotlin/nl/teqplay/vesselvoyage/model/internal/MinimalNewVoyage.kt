package nl.teqplay.vesselvoyage.model.internal

import nl.teqplay.vesselvoyage.model.v2.Destination
import nl.teqplay.vesselvoyage.model.v2.EntryId
import nl.teqplay.vesselvoyage.model.v2.LocationTime

/**
 * Minimal version of NewVoyage
 */
data class MinimalNewVoyage(
    val _id: EntryId,
    val imo: Int,
    val start: LocationTime,
    val destination: Destination?,
    val previous: EntryId?,
    val originPort: String?,
    val destinationPort: String?
)
