package nl.teqplay.vesselvoyage.model.internal

import io.nats.client.Message
import java.util.concurrent.atomic.AtomicBoolean

data class ProcessingItem<T>(
    val ongoing: AtomicBoolean,
    val natsMessage: Message,
    val data: T
) {
    fun isDone(): <PERSON><PERSON>an {
        return !this.ongoing.get()
    }
}

fun List<ProcessingItem<*>>.areAllDone(): <PERSON><PERSON>an {
    return this.all { message -> message.isDone() }
}
