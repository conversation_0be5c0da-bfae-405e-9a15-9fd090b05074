package nl.teqplay.vesselvoyage.model.internal

import nl.teqplay.csi.model.ship.info.ShipRegisterInfoCache
import nl.teqplay.csi.model.ship.mapping.ImoMmsiMapping
import nl.teqplay.platform.model.ShipInfo.ShipType

/**
 * Wrapper data class used to easily access information about a vessel based on its IMO number
 *
 * @param identifier either the MMSI or IMO of the ship, depending on what is available
 */
data class ShipCache(
    val identifier: String,
    val platform: PlatformShipCache? = null,
    val csi: CsiShipCache? = null,
    val csiImoMmsiCache: CsiImoMmsiCache? = null
) {
    data class PlatformShipCache(
        val type: ShipType?
    )

    data class CsiShipCache(
        val register: ShipRegisterInfoCache?
    )

    data class CsiImoMmsiCache(
        val mapping: List<ImoMmsiMapping>?
    )
}
