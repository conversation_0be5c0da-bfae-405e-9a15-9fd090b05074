package nl.teqplay.vesselvoyage.model.internal

import org.bson.codecs.pojo.annotations.BsonId

data class AutomaticRecalculationShip(
    @BsonId
    val imo: Int,

    val state: RecalculationState,

    /**
     * The scenario that was triggered when starting the automatic recalculation.
     */
    val scenarioId: String?
) {
    enum class RecalculationState {
        NOT_READY,
        READY,
        RUNNING,
        FINISHED,
        ERROR
    }
}
