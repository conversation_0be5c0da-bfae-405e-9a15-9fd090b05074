package nl.teqplay.vesselvoyage.model.internal

import nl.teqplay.skeleton.model.Location
import java.time.Instant

/**
 * Wrapper object containing the location and speedOverGround of a position message VesselVoyage is interested in to create the new Trace object.
 */
data class TraceItem(
    val location: Location,
    val speedOverGround: Float?,
    val timestamp: Instant,
    val draught: Float?
)
