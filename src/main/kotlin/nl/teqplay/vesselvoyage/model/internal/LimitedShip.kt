package nl.teqplay.vesselvoyage.model.internal

import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2
import nl.teqplay.vesselvoyage.model.v2.EntryId

/**
 * Model containing a ship that has been marked as limited.
 */
data class LimitedShip(
    /**
     * The ID of the entry where the ship got limited.
     */
    val id: EntryId,

    /**
     * The IMO of the ship we are marking as limited.
     */
    val imo: Int,

    /**
     * Name of the vessels known in CSI.
     */
    val name: String?,

    /**
     * Category of the limited ship.
     */
    val category: ShipCategoryV2?,

    /**
     * The reason why this ship was limited.
     */
    val reason: String?
)
