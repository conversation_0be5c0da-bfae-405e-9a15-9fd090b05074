package nl.teqplay.vesselvoyage.model.internal

import com.fasterxml.jackson.annotation.JsonProperty
import nl.teqplay.vesselvoyage.model.v2.AreaActivity
import nl.teqplay.vesselvoyage.model.v2.LocationTime

/**
 * Minimal version of NewVisit
 */
data class MinimalNewVisit(
    val _id: String,
    val imo: Int,
    val start: LocationTime,
    @JsonProperty("eosp")
    val eospAreaActivity: AreaActivity,
    @JsonProperty("port")
    val portAreaActivities: List<AreaActivity>
)
