package nl.teqplay.vesselvoyage.model

import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.skeleton.datasource.DbObject
import java.time.Instant
import java.util.UUID

data class ProcessorLog(
    override val _id: String = UUID.randomUUID().toString(),
    val imo: Int,
    /** The entries that were mutated */
    val entryIds: Set<String>,
    val event: Event,
    val origin: String?,
    /** NewEventProcessingResult.decision */
    val decision: String?,
    /** For every change: "action:entryId" */
    val changes: List<String>,
    val issues: List<String>,
    val durationMillis: Long,
    val processingStart: Instant
) : DbObject
