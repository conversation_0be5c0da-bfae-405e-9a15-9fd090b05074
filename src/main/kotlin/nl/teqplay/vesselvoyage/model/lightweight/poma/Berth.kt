package nl.teqplay.vesselvoyage.model.lightweight.poma

import nl.teqplay.poma.api.v1.CargoCategoryType
import nl.teqplay.poma.api.v1.MooringType
import nl.teqplay.skeleton.model.Location

data class Berth(
    val authorityId: String?,
    override val name: String,
    val nameLong: String?,
    val terminalId: String?,
    override val ports: List<String> = emptyList(),
    val length: Double?,
    val mooringType: MooringType?,
    val cargoCategoryType: Set<CargoCategoryType>?,
    override val location: Location,
    override val area: List<Location> = emptyList(),
    override val _id: String?,
    val mainPort: String?
) : PomaModel, PomaPorts
