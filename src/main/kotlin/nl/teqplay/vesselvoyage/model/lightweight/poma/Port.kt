package nl.teqplay.vesselvoyage.model.lightweight.poma

import nl.teqplay.skeleton.model.Location

data class Port(
    override val name: String,
    val unlocode: String?,
    override val location: Location,
    override val area: List<Location> = emptyList(),
    val outerArea: List<Location> = emptyList(),
    val eosArea: List<Location>?,
    val mainPort: String? = null,
    override val _id: String?,
) : PomaModel
