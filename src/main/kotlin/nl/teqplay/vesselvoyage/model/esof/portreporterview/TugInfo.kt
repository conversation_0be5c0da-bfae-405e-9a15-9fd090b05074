package nl.teqplay.vesselvoyage.model.esof.portreporterview

import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewEncounter
import nl.teqplay.vesselvoyage.model.v2.StartEnd

/**
 * Note that this is duplicate from PTO SOF, but we want those implementations explicitly separated, as they will
 * likely deviate in the future. Therefore, use this class only for PortReporterStatementOfFactsView!
 *
 * @see nl.teqplay.vesselvoyage.service.api.PortReporterStatementOfFactsViewGenerator
 */
data class TugInfo(
    val ref: Int,
    val encounter: NewEncounter,
    val waitingForDepartureEncounter: NewEncounter? = null
) : StartEnd {
    override val start: LocationTime = encounter.start
    override val end: LocationTime? = encounter.end
    val mmsi: Int = encounter.otherMmsi
    val imo: Int? = encounter.otherImo
}
