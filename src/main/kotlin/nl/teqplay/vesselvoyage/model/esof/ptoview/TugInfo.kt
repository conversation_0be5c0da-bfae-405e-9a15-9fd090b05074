package nl.teqplay.vesselvoyage.model.esof.ptoview

import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewEncounter
import nl.teqplay.vesselvoyage.model.v2.StartEnd

data class TugInfo(
    val id: String,
    val ref: Int,
    val encounter: NewEncounter,
    val waitingForDepartureEncounter: NewEncounter? = null
) : StartEnd {
    override val start: LocationTime = encounter.start
    override val end: LocationTime? = encounter.end
    val mmsi: Int = encounter.otherMmsi
    val imo: Int? = encounter.otherImo
}
