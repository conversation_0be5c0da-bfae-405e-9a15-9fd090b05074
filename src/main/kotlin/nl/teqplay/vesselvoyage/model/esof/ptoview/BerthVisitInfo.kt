package nl.teqplay.vesselvoyage.model.esof.ptoview

import nl.teqplay.vesselvoyage.model.lightweight.poma.Berth
import nl.teqplay.vesselvoyage.model.v2.AreaActivity
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.StartEnd

data class BerthVisitInfo(
    val id: String,
    @Deprecated("Use id instead", ReplaceWith("id"))
    val ref: Int,
    val activity: AreaActivity,
    val area: Berth?,
    val portArea: PortAreaInfo?,
    val arrivalTugs: List<TugInfo>,
    val departureTugs: List<TugInfo>,
    val firstLineSecured: LocationTime?,
    val allFast: LocationTime?,
    val lastLineReleased: LocationTime?,
    val terminalVisit: TerminalVisitInfo?
) : StartEnd {
    override val start: LocationTime = activity.start
    override val end: LocationTime? = activity.end
}
