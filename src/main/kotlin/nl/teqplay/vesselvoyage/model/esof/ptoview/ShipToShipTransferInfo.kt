package nl.teqplay.vesselvoyage.model.esof.ptoview

import nl.teqplay.vesselvoyage.model.ShipDetails
import nl.teqplay.vesselvoyage.model.lightweight.poma.ShipToShipArea
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.ShipToShipTransfer
import nl.teqplay.vesselvoyage.model.v2.StartEnd

data class ShipToShipTransferInfo(
    val id: String,
    val transfer: ShipToShipTransfer,
    val area: ShipToShipArea? = null,
    val otherShip: ShipDetails? = null
) : StartEnd {
    override val start: LocationTime = transfer.start
    override val end: LocationTime? = transfer.end
}
