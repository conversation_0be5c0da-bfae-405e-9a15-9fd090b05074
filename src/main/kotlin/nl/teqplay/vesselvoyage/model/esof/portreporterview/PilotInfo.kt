package nl.teqplay.vesselvoyage.model.esof.portreporterview

import nl.teqplay.vesselvoyage.model.lightweight.poma.PilotBoardingPlace
import nl.teqplay.vesselvoyage.model.v2.AreaActivity
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewEncounter
import nl.teqplay.vesselvoyage.model.v2.StartEnd
import java.time.temporal.ChronoUnit

/**
 * Note that this is duplicate from PTO SOF, but we want those implementations explicitly separated, as they will
 * likely deviate in the future. Therefore, use this class only for PortReporterStatementOfFactsView!
 *
 * @see nl.teqplay.vesselvoyage.service.api.PortReporterStatementOfFactsViewGenerator
 */
data class PilotInfo(
    /** Populated when based on a pilot ship encounter */
    val encounter: NewEncounter?,
    /** The pilot area, if the encounter happened inside a pilot area (other area types are ignored) */
    val pilotArea: PilotBoardingPlace?,
    /** In case the default detection type did not work, this denotes the fallback detection type */
    val fallbackDetectionType: PilotFallbackDetectionType?,
    override val start: LocationTime,
    override val end: LocationTime?
) : StartEnd {

    companion object {
        /**
         * @param encounter The encounter with the pilot vessel
         * @param pilotArea The pilot area, if the encounter happened inside a pilot area (other area types are ignored)
         */
        fun fromPilotShipEncounter(
            encounter: NewEncounter,
            pilotArea: PilotBoardingPlace?
        ) = PilotInfo(
            encounter = encounter,
            pilotArea = pilotArea,
            start = encounter.start,
            end = encounter.end,
            fallbackDetectionType = null
        )

        fun fromPilotAreaActivity(
            pilotAreaActivity: AreaActivity,
            pilotArea: PilotBoardingPlace?
        ) = PilotInfo(
            encounter = null,
            pilotArea = pilotArea,
            // PTO requires the start time to be 15 minutes before the fallback end time.
            // This will hopefully be adjusted in the future.
            start = pilotAreaActivity.end?.copy(time = pilotAreaActivity.end!!.time.minus(15, ChronoUnit.MINUTES)) ?: pilotAreaActivity.start,
            end = pilotAreaActivity.end,
            fallbackDetectionType = PilotFallbackDetectionType.PILOT_AREA
        )
    }
}
