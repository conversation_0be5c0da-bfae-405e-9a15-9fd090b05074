package nl.teqplay.vesselvoyage.model.esof.ptoview

import nl.teqplay.vesselvoyage.model.lightweight.poma.Port
import nl.teqplay.vesselvoyage.model.v2.AreaActivity

/**
 * List of port area that the ship visited or sailed through inside the EOSP.
 * A port area may occur multiple times, those objects will all have a unique reference.
 */
data class PortAreaInfo(
    val ref: Int,
    val isPassThrough: Boolean,
    val activity: AreaActivity,
    val area: Port
)
