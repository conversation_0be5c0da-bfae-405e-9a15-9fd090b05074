package nl.teqplay.vesselvoyage.model.esof.ptoview

import nl.teqplay.vesselvoyage.model.v2.NewEncounter
import nl.teqplay.vesselvoyage.model.v2.StartEnd

data class EncounterInfo(
    val id: String,
    val encounter: NewEncounter,
    val portAreaInfo: PortAreaInfo?,
    val berthVisitInfo: BerthVisitInfo?,
    val terminalVisitInfo: TerminalVisitInfo?,
) : StartEnd {
    override val start = encounter.start
    override val end = encounter.end
}
