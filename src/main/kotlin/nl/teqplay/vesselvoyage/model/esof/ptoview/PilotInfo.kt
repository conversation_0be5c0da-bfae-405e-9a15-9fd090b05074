package nl.teqplay.vesselvoyage.model.esof.ptoview

import nl.teqplay.vesselvoyage.model.lightweight.poma.PilotBoardingPlace
import nl.teqplay.vesselvoyage.model.v2.AreaActivity
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewEncounter
import nl.teqplay.vesselvoyage.model.v2.StartEnd
import java.time.temporal.ChronoUnit

/**
 * Pilot actions for the statement of facts.
 */
data class PilotInfo(
    val id: String,
    /** Populated when based on a pilot ship encounter */
    val encounter: NewEncounter?,
    /** The pilot area, if the encounter happened inside a pilot area (other area types are ignored) */
    val pilotArea: PilotBoardingPlace?,
    /** In case the default detection type did not work, this denotes the fallback detection type */
    val fallbackDetectionType: PilotFallbackDetectionType?,
    override val start: LocationTime,
    override val end: LocationTime?
) : StartEnd {

    companion object {
        /**
         * @param encounter The encounter with the pilot vessel
         * @param pilotArea The pilot area, if the encounter happened inside a pilot area (other area types are ignored)
         */
        fun fromPilotShipEncounter(
            visitId: String,
            encounter: NewEncounter,
            pilotArea: PilotBoardingPlace?
        ) = PilotInfo(
            id = "$visitId.${encounter.startEventId}",
            encounter = encounter,
            pilotArea = pilotArea,
            start = encounter.start,
            end = encounter.end,
            fallbackDetectionType = null
        )

        fun fromPilotAreaActivity(
            visitId: String,
            pilotAreaActivity: AreaActivity,
            pilotArea: PilotBoardingPlace?
        ) = PilotInfo(
            id = "$visitId.${pilotAreaActivity.id}",
            encounter = null,
            pilotArea = pilotArea,
            // PTO requires the start time to be 15 minutes before the fallback end time.
            // This will hopefully be adjusted in the future.
            start = pilotAreaActivity.end?.copy(time = pilotAreaActivity.end!!.time.minus(15, ChronoUnit.MINUTES)) ?: pilotAreaActivity.start,
            end = pilotAreaActivity.end,
            fallbackDetectionType = PilotFallbackDetectionType.PILOT_AREA
        )
    }
}
