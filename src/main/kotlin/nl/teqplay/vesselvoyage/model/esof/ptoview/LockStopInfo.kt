package nl.teqplay.vesselvoyage.model.esof.ptoview

import nl.teqplay.vesselvoyage.model.lightweight.poma.Lock
import nl.teqplay.vesselvoyage.model.v2.NewStop
import nl.teqplay.vesselvoyage.model.v2.StartEnd

data class LockStopInfo(
    val id: String,
    val stop: NewStop,
    val area: Lock?,
    val portArea: PortAreaInfo?
) : StartEnd {
    override val start = stop.start
    override val end = stop.end
}
