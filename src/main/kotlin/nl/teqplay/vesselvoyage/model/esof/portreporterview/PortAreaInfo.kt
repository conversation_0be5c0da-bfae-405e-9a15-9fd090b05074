package nl.teqplay.vesselvoyage.model.esof.portreporterview

import nl.teqplay.vesselvoyage.model.lightweight.poma.Port
import nl.teqplay.vesselvoyage.model.v2.AreaActivity

/**
 * List of port area that the ship visited or sailed through inside the EOSP.
 * A port area may occur multiple times, those objects will all have a unique reference [ref].
 *
 * Note that this is duplicate from PTO SOF, but we want those implementations explicitly separated, as they will
 * likely deviate in the future. Therefore, use this class only for PortReporterStatementOfFactsView!
 *
 * @see nl.teqplay.vesselvoyage.service.api.PortReporterStatementOfFactsViewGenerator
 */
data class PortAreaInfo(
    val ref: Int,
    val isPassThrough: Boolean,
    val activity: AreaActivity,
    val area: Port
)
