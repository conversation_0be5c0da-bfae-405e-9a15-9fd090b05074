package nl.teqplay.vesselvoyage.model.esof.portreporterview

import nl.teqplay.vesselvoyage.model.v2.NewEncounter
import nl.teqplay.vesselvoyage.model.v2.StartEnd

/**
 * Note that this is duplicate from PTO SOF, but we want those implementations explicitly separated, as they will
 * likely deviate in the future. Therefore, use this class only for PortReporterStatementOfFactsView!
 *
 * @see nl.teqplay.vesselvoyage.service.api.PortReporterStatementOfFactsViewGenerator
 */
data class EncounterInfo(
    val encounter: NewEncounter,
    val portAreaInfo: PortAreaInfo?,
    val berthVisitInfo: BerthVisitInfo?,
    val terminalVisitInfo: TerminalVisitInfo?,
) : StartEnd {
    override val start = encounter.start
    override val end = encounter.end
}
