package nl.teqplay.vesselvoyage.model.esof.ptoview

import nl.teqplay.vesselvoyage.model.lightweight.poma.Anchorage
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewStop
import nl.teqplay.vesselvoyage.model.v2.StartEnd

data class AnchorStopInfo(
    val id: String,
    val stop: NewStop,
    val area: Anchorage?,
    val portArea: PortAreaInfo?
) : StartEnd {
    override val start: LocationTime = stop.start
    override val end: LocationTime? = stop.end
}
