package nl.teqplay.vesselvoyage.model.esof.portreporterview

import nl.teqplay.vesselvoyage.model.lightweight.poma.Terminal
import nl.teqplay.vesselvoyage.model.v2.AreaActivity

/**
 * Note that this is duplicate from PTO SOF, but we want those implementations explicitly separated, as they will
 * likely deviate in the future. Therefore, use this class only for PortReporterStatementOfFactsView!
 *
 * @see nl.teqplay.vesselvoyage.service.api.PortReporterStatementOfFactsViewGenerator
 */
data class TerminalVisitInfo(
    val ref: Int,
    val activity: AreaActivity,
    val mooringActivity: AreaActivity?,
    val area: Terminal?,
    val portArea: PortAreaInfo?,
    val berthVisits: List<BerthVisitInfo>
)
