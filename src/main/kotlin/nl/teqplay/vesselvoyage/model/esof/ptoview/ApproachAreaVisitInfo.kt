package nl.teqplay.vesselvoyage.model.esof.ptoview

import nl.teqplay.vesselvoyage.model.lightweight.poma.ApproachArea
import nl.teqplay.vesselvoyage.model.v2.AreaActivity
import nl.teqplay.vesselvoyage.model.v2.StartEnd

data class ApproachAreaVisitInfo(
    val activity: AreaActivity,
    val portArea: PortAreaInfo?,
    val area: ApproachArea?
) : StartEnd {
    override val start = activity.start
    override val end = activity.end
}
