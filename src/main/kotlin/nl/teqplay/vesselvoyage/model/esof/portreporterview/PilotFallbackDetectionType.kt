package nl.teqplay.vesselvoyage.model.esof.portreporterview

/**
 * Fallback methods to determine inbound and outbound pilots, in case default detection (encounter) is not available.
 *
 * Note that this is duplicate from PTO SOF, but we want those implementations explicitly separated, as they will
 * likely deviate in the future. Therefore, use this class only for PortReporterStatementOfFactsView!
 * @see nl.teqplay.vesselvoyage.service.api.PortReporterStatementOfFactsViewGenerator
 */
enum class PilotFallbackDetectionType {
    /** Pilot detection through pilot area enter/exit */
    PILOT_AREA
}
