package nl.teqplay.vesselvoyage.model.esof.portreporterview

import nl.teqplay.vesselvoyage.model.lightweight.poma.Berth
import nl.teqplay.vesselvoyage.model.v2.AreaActivity
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.StartEnd

/**
 * Note that this is duplicate from PTO SOF, but we want those implementations explicitly separated, as they will
 * likely deviate in the future. Therefore, use this class only for PortReporterStatementOfFactsView!
 *
 * @see nl.teqplay.vesselvoyage.service.api.PortReporterStatementOfFactsViewGenerator
 */
data class BerthVisitInfo(
    val ref: Int,
    val activity: AreaActivity,
    val area: Berth?,
    val portArea: PortAreaInfo?,
    val arrivalTugs: List<TugInfo>,
    val departureTugs: List<TugInfo>,
    val firstLineSecured: LocationTime?,
    val allFast: LocationTime?,
    val lastLineReleased: LocationTime?,
    val terminalVisit: TerminalVisitInfo?
) : StartEnd {
    override val start: LocationTime = activity.start
    override val end: LocationTime? = activity.end
}
