package nl.teqplay.vesselvoyage.client

import nl.teqplay.aisengine.client.annotations.InternalApiRestTemplate
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.skeleton.common.AllOpen
import nl.teqplay.skeleton.common.network.postForObjectWithParams
import nl.teqplay.skeleton.model.TimeWindow
import org.springframework.web.client.RestTemplate

// TODO: move AisHistoryClient into ais-engine's ship-history client
@AllOpen
class AisHistoryClient(@InternalApiRestTemplate private val restTemplate: RestTemplate) {

    // TODO: move model into ais-engine's API
    data class AisHistoricMessageMmsiQuery(
        val mmsi: Int,
        val window: TimeWindow
    )

    fun queryAisHistory(
        query: List<AisHistoricMessageMmsiQuery>,
        maxDays: Int
    ): List<AisHistoricMessage> {
        return restTemplate.postForObjectWithParams<Array<AisHistoricMessage>>(
            path = "/v1/ship/history/mmsi",
            body = query,
            "maxDays" to maxDays
        ).toList()
    }
}
