package nl.teqplay.vesselvoyage.client.csi

import nl.teqplay.csi.model.ship.info.ShipRegisterInfoCache
import nl.teqplay.csi.model.ship.mapping.ShipRegisterMapping
import nl.teqplay.vesselvoyage.config.CSIRestTemplate
import org.springframework.stereotype.Component
import org.springframework.web.client.RestTemplate

@Component
class CSIClient(@CSIRestTemplate private val restTemplate: RestTemplate) {

    fun listShipRegisterInfoCache(): List<ShipRegisterInfoCache> {
        return restTemplate.getForObject("/v1/shipRegister/list/cache", Array<ShipRegisterInfoCache>::class.java)
            ?.toList()
            ?: emptyList()
    }

    fun listShipRegisterMapping(): List<ShipRegisterMapping> {
        return restTemplate.getForObject("/v1/shipMapping/list", Array<ShipRegisterMapping>::class.java)
            ?.toList()
            ?: emptyList()
    }
}
