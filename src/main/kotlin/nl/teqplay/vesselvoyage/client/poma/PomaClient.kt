package nl.teqplay.vesselvoyage.client.poma

import nl.teqplay.poma.api.v1.Anchorage
import nl.teqplay.poma.api.v1.ApproachArea
import nl.teqplay.poma.api.v1.Berth
import nl.teqplay.poma.api.v1.Lock
import nl.teqplay.poma.api.v1.PilotBoardingPlace
import nl.teqplay.poma.api.v1.Port
import nl.teqplay.poma.api.v1.ShipToShipArea
import nl.teqplay.poma.api.v1.Terminal
import nl.teqplay.vesselvoyage.config.PomaRestTemplate
import nl.teqplay.vesselvoyage.mapper.PomaMapper
import org.springframework.stereotype.Component
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.getForEntity
import nl.teqplay.vesselvoyage.model.lightweight.poma.Anchorage as LightweightAnchorage
import nl.teqplay.vesselvoyage.model.lightweight.poma.ApproachArea as LightweightApproachArea
import nl.teqplay.vesselvoyage.model.lightweight.poma.Berth as LightweightBerth
import nl.teqplay.vesselvoyage.model.lightweight.poma.Lock as LightweightLock
import nl.teqplay.vesselvoyage.model.lightweight.poma.PilotBoardingPlace as LightweightPilotBoardingPlace
import nl.teqplay.vesselvoyage.model.lightweight.poma.Port as LightweightPort
import nl.teqplay.vesselvoyage.model.lightweight.poma.ShipToShipArea as LightweightShipToShipArea
import nl.teqplay.vesselvoyage.model.lightweight.poma.Terminal as LightweightTerminal

@Component
class PomaClient(
    @PomaRestTemplate private val restTemplate: RestTemplate,
    private val mapper: PomaMapper
) {
    fun getAllPorts(): List<LightweightPort> {
        return restTemplate.getForEntity<Array<Port>>("/v1/port").body
            ?.map(mapper::toLightweightPort)
            ?: emptyList()
    }

    fun getAllAnchorages(): List<LightweightAnchorage> {
        return restTemplate.getForEntity<Array<Anchorage>>("/v1/anchorage").body
            ?.map(mapper::toLightweightPort)
            ?: emptyList()
    }

    fun getAllBerths(): List<LightweightBerth> {
        return restTemplate.getForEntity<Array<Berth>>("/v1/berth").body
            ?.map(mapper::toLightweightPort)
            ?: emptyList()
    }

    fun getAllPilotBoardingPlaces(): List<LightweightPilotBoardingPlace> {
        return restTemplate.getForEntity<Array<PilotBoardingPlace>>("/v1/pilotboardingplace").body
            ?.map(mapper::toLightweightPort)
            ?: emptyList()
    }

    fun getAllTerminals(): List<LightweightTerminal> {
        return restTemplate.getForEntity<Array<Terminal>>("/v1/terminal").body
            ?.map(mapper::toLightweightPort)
            ?: emptyList()
    }

    fun getAllLocks(): List<LightweightLock> {
        return restTemplate.getForEntity<Array<Lock>>("/v1/lock").body
            ?.map(mapper::toLightweightPort)
            ?: emptyList()
    }

    fun getAllApproachAreas(): List<LightweightApproachArea> {
        return restTemplate.getForEntity<Array<ApproachArea>>("/v1/approacharea").body
            ?.map(mapper::toLightweightPort)
            ?: emptyList()
    }

    fun getAllShipToShipAreas(): List<LightweightShipToShipArea> {
        return restTemplate.getForEntity<Array<ShipToShipArea>>("/v1/stsarea").body
            ?.map(mapper::toLightweightPort)
            ?: emptyList()
    }
}
