package nl.teqplay.vesselvoyage.client.driftpredictor

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.vesselvoyage.config.DriftPredictorRestTemplate
import nl.teqplay.vesselvoyage.model.internal.DriftSegment
import nl.teqplay.vesselvoyage.model.internal.DriftSegmentPredictionItem
import nl.teqplay.vesselvoyage.model.internal.DriftSegmentResponseBody
import org.springframework.stereotype.Component
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.postForObject

@Component
class DriftPredictorClient(
    @DriftPredictorRestTemplate private val restTemplate: RestTemplate
) {
    private val log = KotlinLogging.logger {}

    fun getSegmentPredictions(aisMessages: List<AisHistoricMessage>): List<DriftSegment> {
        if (aisMessages.isEmpty()) return emptyList()

        val predictionItems = aisMessages.mapNotNull { message ->
            val speedOverGround = message.speedOverGround ?: return@mapNotNull null
            val heading = message.heading?.toFloat() ?: return@mapNotNull null
            val courseOverGround = message.courseOverGround ?: return@mapNotNull null

            DriftSegmentPredictionItem(
                speedOverGround = speedOverGround,
                heading = heading,
                courseOverGround = courseOverGround,
                messageTime = message.messageTime
            )
        }

        // We can't do any predictions when there are no items that can be done for drifting
        if (predictionItems.isEmpty()) {
            return emptyList()
        }

        return try {
            val driftSegmentResponseBody: DriftSegmentResponseBody = restTemplate.postForObject<DriftSegmentResponseBody>(
                url = "/v1/predict/segment",
                request = predictionItems
            )
            driftSegmentResponseBody.driftSegments
        } catch (ex: Exception) {
            log.warn { "Failed to get a result from the predictor: ${ex.message}" }
            emptyList()
        }
    }
}
