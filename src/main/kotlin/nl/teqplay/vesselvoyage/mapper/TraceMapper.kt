package nl.teqplay.vesselvoyage.mapper

import nl.teqplay.skeleton.model.Polyline
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.Named
import org.mapstruct.ReportingPolicy
import nl.teqplay.vesselvoyage.apiv2.model.Trace as ApiTrace
import nl.teqplay.vesselvoyage.model.v2.NewTrace as InternalTrace

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.ERROR
)
abstract class TraceMapper {

    @Mapping(target = "entryId", source = "_id")
    @Mapping(target = "polyline", source = ".", qualifiedByName = ["combinePolyline"])
    @Mapping(target = "speedMin", source = "speed.min")
    @Mapping(target = "speedMax", source = "speed.max")
    @Mapping(target = "speedAvg", source = "speed.avg")
    @Mapping(target = "distanceMeters", source = "distance.distanceMeters")
    @Mapping(target = "draughtMin", source = "draught.min")
    @Mapping(target = "draughtMax", source = "draught.max")
    @Mapping(target = "draughtAvg", source = "draught.avg")
    abstract fun toApi(trace: InternalTrace): ApiTrace

    /**
     * The full polyline of the trace is divided in the simplifiedPolyline and polyline. In some cases, items in
     * 'polyline' are not simplified, so append them to the simplifiedPolyline to form a complete trace.
     * See the [InternalTrace] docs for more information.
     */
    @Named("combinePolyline")
    fun combinePolyline(trace: InternalTrace): Polyline {
        return trace.simplifiedPolyline?.appendPolyline(trace.polyline)
            ?: trace.polyline
    }
}
