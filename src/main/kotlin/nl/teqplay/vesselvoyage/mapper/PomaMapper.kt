package nl.teqplay.vesselvoyage.mapper

import nl.teqplay.skeleton.model.Location
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.Named
import org.mapstruct.ReportingPolicy
import nl.teqplay.poma.api.v1.Anchorage as PomaAnchorage
import nl.teqplay.poma.api.v1.ApproachArea as PomaApproachArea
import nl.teqplay.poma.api.v1.Berth as PomaBerth
import nl.teqplay.poma.api.v1.Location as PomaLocation
import nl.teqplay.poma.api.v1.Lock as PomaLock
import nl.teqplay.poma.api.v1.PilotBoardingPlace as PomaPilotBoardingPlace
import nl.teqplay.poma.api.v1.Port as PomaPort
import nl.teqplay.poma.api.v1.ShipToShipArea as PomaShipToShipArea
import nl.teqplay.poma.api.v1.Terminal as PomaTerminal
import nl.teqplay.vesselvoyage.model.lightweight.poma.Anchorage as LightweightAnchorage
import nl.teqplay.vesselvoyage.model.lightweight.poma.ApproachArea as LightweightApproachArea
import nl.teqplay.vesselvoyage.model.lightweight.poma.Berth as LightweightBerth
import nl.teqplay.vesselvoyage.model.lightweight.poma.Lock as LightweightLock
import nl.teqplay.vesselvoyage.model.lightweight.poma.PilotBoardingPlace as LightweightPilotBoardingPlace
import nl.teqplay.vesselvoyage.model.lightweight.poma.Port as LightweightPort
import nl.teqplay.vesselvoyage.model.lightweight.poma.ShipToShipArea as LightweightShipToShipArea
import nl.teqplay.vesselvoyage.model.lightweight.poma.Terminal as LightweightTerminal

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.ERROR
)
abstract class PomaMapper {

    @Mapping(target = "location", source = "location", qualifiedByName = ["convertLocation"])
    @Mapping(target = "area", source = "area", qualifiedByName = ["convertArea"])
    @Mapping(target = "outerArea", source = "outerArea", qualifiedByName = ["convertArea"])
    @Mapping(target = "eosArea", source = "eosArea", qualifiedByName = ["convertArea"])
    abstract fun toLightweightPort(port: PomaPort): LightweightPort

    @Mapping(target = "location", source = "location", qualifiedByName = ["convertLocation"])
    @Mapping(target = "area", source = "area", qualifiedByName = ["convertArea"])
    abstract fun toLightweightPort(port: PomaAnchorage): LightweightAnchorage

    @Mapping(target = "location", source = "location", qualifiedByName = ["convertLocation"])
    @Mapping(target = "area", source = "area", qualifiedByName = ["convertArea"])
    abstract fun toLightweightPort(port: PomaBerth): LightweightBerth

    @Mapping(target = "location", source = "location", qualifiedByName = ["convertLocation"])
    @Mapping(target = "area", source = "area", qualifiedByName = ["convertArea"])
    abstract fun toLightweightPort(port: PomaPilotBoardingPlace): LightweightPilotBoardingPlace

    @Mapping(target = "location", source = "location", qualifiedByName = ["convertLocation"])
    @Mapping(target = "area", source = "area", qualifiedByName = ["convertArea"])
    abstract fun toLightweightPort(port: PomaTerminal): LightweightTerminal

    @Mapping(target = "location", source = "location", qualifiedByName = ["convertLocation"])
    @Mapping(target = "area", source = "area", qualifiedByName = ["convertArea"])
    abstract fun toLightweightPort(port: PomaLock): LightweightLock

    @Mapping(target = "location", source = "location", qualifiedByName = ["convertLocation"])
    @Mapping(target = "area", source = "area", qualifiedByName = ["convertArea"])
    abstract fun toLightweightPort(port: PomaApproachArea): LightweightApproachArea

    @Mapping(target = "location", source = "location", qualifiedByName = ["convertLocation"])
    @Mapping(target = "area", source = "area", qualifiedByName = ["convertArea"])
    abstract fun toLightweightPort(port: PomaShipToShipArea): LightweightShipToShipArea

    @Named("convertLocation")
    fun convertLocation(location: PomaLocation): Location {
        return Location(
            lat = location.latitude,
            lon = location.longitude
        )
    }

    @Named("convertArea")
    fun convertArea(area: List<PomaLocation>): List<Location> {
        return area.map { convertLocation(it) }
    }
}
