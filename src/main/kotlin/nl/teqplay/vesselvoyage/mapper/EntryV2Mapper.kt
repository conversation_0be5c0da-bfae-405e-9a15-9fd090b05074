package nl.teqplay.vesselvoyage.mapper

import nl.teqplay.vesselvoyage.apiv2.model.Area
import nl.teqplay.vesselvoyage.apiv2.model.ESoF
import nl.teqplay.vesselvoyage.apiv2.model.Encounter
import nl.teqplay.vesselvoyage.apiv2.model.Entry
import nl.teqplay.vesselvoyage.apiv2.model.OtherEntryPointer
import nl.teqplay.vesselvoyage.apiv2.model.PassThrough
import nl.teqplay.vesselvoyage.apiv2.model.Port
import nl.teqplay.vesselvoyage.apiv2.model.SlowMovingPeriod
import nl.teqplay.vesselvoyage.apiv2.model.Visit
import nl.teqplay.vesselvoyage.model.internal.InfraAreaType
import nl.teqplay.vesselvoyage.model.v2.AreaActivity
import nl.teqplay.vesselvoyage.model.v2.Destination
import nl.teqplay.vesselvoyage.model.v2.JournalItem
import nl.teqplay.vesselvoyage.model.v2.NewESoF
import nl.teqplay.vesselvoyage.model.v2.NewEncounter
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewSlowMovingPeriod
import nl.teqplay.vesselvoyage.model.v2.NewStop
import nl.teqplay.vesselvoyage.model.v2.NewStopType
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import nl.teqplay.vesselvoyage.model.v2.Speed
import nl.teqplay.vesselvoyage.service.InfraService
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.Named
import org.mapstruct.ReportingPolicy
import org.springframework.beans.factory.annotation.Autowired
import java.time.Duration
import nl.teqplay.vesselvoyage.apiv2.model.Destination as ApiDestination
import nl.teqplay.vesselvoyage.apiv2.model.JournalItem as ApiJournalItem
import nl.teqplay.vesselvoyage.apiv2.model.LocationTime as ApiLocationTime
import nl.teqplay.vesselvoyage.apiv2.model.ShipToShipTransfer as ApiShipToShipTransfer
import nl.teqplay.vesselvoyage.apiv2.model.Speed as ApiSpeed
import nl.teqplay.vesselvoyage.apiv2.model.Stop as ApiStop
import nl.teqplay.vesselvoyage.apiv2.model.Visit as ApiVisit
import nl.teqplay.vesselvoyage.apiv2.model.Voyage as ApiVoyage
import nl.teqplay.vesselvoyage.model.lightweight.poma.Port as PomaPort
import nl.teqplay.vesselvoyage.model.v2.LocationTime as InternalLocationTime
import nl.teqplay.vesselvoyage.model.v2.NewVisit as InternalVisit
import nl.teqplay.vesselvoyage.model.v2.NewVoyage as InternalVoyage
import nl.teqplay.vesselvoyage.model.v2.ShipToShipTransfer as InternalShipToShipTransfer

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.ERROR
)
abstract class EntryV2Mapper {

    @Autowired
    private lateinit var infraService: InfraService

    // mapstruct allows for an interface instead of an abstract class, but with NewEntry + its children it doesn't
    // cooperate nicely in Java interop. Therefore still using an abstract class

    fun toApi(entry: NewEntry): Entry = when (entry) {
        is NewVisit -> toApi(entry)
        is NewVoyage -> toApi(entry)
    }

    @Mapping(target = "entryId", source = "_id")
    @Mapping(target = "passThrough", source = "passThroughPort")
    @Mapping(target = "stop", source = "stops")
    @Mapping(target = "port", source = ".")
    @Mapping(target = "portTimes", source = "portAreaActivities", qualifiedByName = ["determinePortTimes"])
    @Mapping(target = "previous", source = ".", qualifiedByName = ["determinePrevious"])
    @Mapping(target = "next", source = ".", qualifiedByName = ["determineNext"])
    @Mapping(target = "trace", ignore = true) // TODO: map properly when object is available
    @Mapping(target = "finished", source = ".", qualifiedByName = ["determineFinished"])
    abstract fun toApi(visit: InternalVisit): ApiVisit

    @Mapping(target = "entryId", source = "_id")
    @Mapping(target = "passThrough", source = "passThroughPort")
    @Mapping(target = "stop", source = "stops")
    @Mapping(target = "previous", source = ".", qualifiedByName = ["determinePrevious"])
    @Mapping(target = "next", source = ".", qualifiedByName = ["determineNext"])
    @Mapping(target = "trace", ignore = true) // TODO: map properly when object is available
    @Mapping(target = "finished", source = ".", qualifiedByName = ["determineFinished"])
    abstract fun toApi(voyage: InternalVoyage): ApiVoyage

    abstract fun toApi(locationTime: InternalLocationTime): ApiLocationTime

    fun toApi(stops: List<NewStop>): List<ApiStop> {
        val mergeStopsDuration = Duration.ofMinutes(5)
        val minimumStopDuration = Duration.ofMinutes(5)

        // First merge stops together if they are nearby in time and about the same area.
        return stops.fold(mutableListOf<ApiStop>()) { acc, stop ->
            val area = convertStopArea(stop)
            val apiStop = ApiStop(
                start = toApi(stop.start),
                end = stop.end?.let { toApi(it) },
                location = stop.location,
                area = area,
                accuracy = stop.accuracy
            )

            val previous = acc.lastOrNull()
            if (previous == null ||
                previous.end?.time == null ||
                previous.area.type != area.type ||
                previous.area.id != area.id ||
                Duration.between(requireNotNull(previous.end).time, apiStop.start.time) >= mergeStopsDuration
            ) {
                acc.add(apiStop)
            } else {
                acc[acc.size - 1] = previous.copy(
                    end = apiStop.end,
                    location = apiStop.location,
                    accuracy = listOfNotNull(previous.accuracy, apiStop.accuracy).maxOrNull(),
                )
            }

            acc
        }.filter {
            // Then, remove any stops that are too short.
            val end = it.end
            end == null || Duration.between(it.start.time, end.time) >= minimumStopDuration
        }
    }

    // TODO: map activity.areaId to api.port. One on one or conversion logic needed?
    @Mapping(target = "port", ignore = true) // TODO: map properly when object is available
    abstract fun passThroughToApi(activity: AreaActivity): PassThrough

    fun portToApi(entry: NewVisit): Port {
        val port = infraService.getById(
            id = entry.eospAreaActivity.areaId.removeSuffix(".eosp"),
            areaType = InfraAreaType.PORT
        ) as? PomaPort

        if (port != null) {
            val subPorts = entry.portAreaActivities.mapNotNull { activity ->
                val possibleSubPort = infraService.getById(
                    id = activity.areaId,
                    areaType = InfraAreaType.PORT
                ) as? PomaPort

                // Only take the sub ports of our main port
                // Ignore any other sub ports that are not part of our visit port
                possibleSubPort.takeIf { it?.mainPort == port._id }
                    ?.unlocode
            }

            return Port(
                main = port.unlocode!!,
                sub = subPorts
            )
        }

        // TODO poma port is missing? This should not happen?
        return Port(
            main = "",
            sub = emptyList()
        )
    }

    private fun convertStopArea(stop: NewStop): Area {
        return Area(
            type = stop.type.toAreaType(),
            id = stop.areaId
        )
    }

    private fun NewStopType.toAreaType(): String {
        return when (this) {
            NewStopType.ANCHOR_AREA -> "Anchor"
            NewStopType.BERTH -> "Berth"
            NewStopType.LOCK -> "Lock"
            NewStopType.UNCLASSIFIED -> "Unclassified"
        }
    }

    @Named("determinePortTimes")
    fun determinePortTimes(portAreaActivities: List<AreaActivity>): List<Visit.PortTime> {
        return portAreaActivities.map { activity ->
            Visit.PortTime(
                id = activity.areaId,
                unlocode = getPortUnlocodeById(activity.areaId),
                start = toApi(activity.start),
                end = activity.end?.let(::toApi)
            )
        }
    }

    @Named("determinePrevious")
    fun determinePrevious(entry: NewEntry): OtherEntryPointer? {
        val portId = when (entry) {
            is NewVisit -> null
            is NewVoyage -> entry.originPort
        }

        return entry.previous?.let {
            OtherEntryPointer(
                port = getPortUnlocodeById(portId),
                entryId = it
            )
        }
    }

    @Named("determineNext")
    fun determineNext(entry: NewEntry): OtherEntryPointer? {
        val portId = when (entry) {
            is NewVisit -> null
            is NewVoyage -> entry.destinationPort
        }

        return entry.next?.let {
            OtherEntryPointer(
                port = getPortUnlocodeById(portId),
                entryId = it
            )
        }
    }

    private fun getPortUnlocodeById(id: String?): String? {
        // We can't find any port unlocode if the provided id is null
        if (id == null) {
            return null
        }

        // Remove any eosp suffix if it has any
        val preparedId = id.removeSuffix(".eosp")
        val port = infraService.getById(
            id = preparedId,
            areaType = InfraAreaType.PORT
        ) as? PomaPort

        return port?.unlocode
    }

    @Named("determineFinished")
    fun determineFinished(entry: NewEntry): Boolean {
        // Entries are finished when the end field is filled
        return entry.end != null
    }

    abstract fun toApi(journalItem: JournalItem): ApiJournalItem

    @Mapping(target = "port", source = "actual")
    abstract fun toApi(destination: Destination): ApiDestination

    abstract fun toApi(esof: NewESoF?): ESoF
    abstract fun toApi(encounter: NewEncounter): Encounter
    abstract fun toApi(slowMovingPeriod: NewSlowMovingPeriod): SlowMovingPeriod
    abstract fun toApi(shipToShipTransfer: InternalShipToShipTransfer): ApiShipToShipTransfer
    @Mapping(target = "average", source = "avg")
    abstract fun toApi(speed: Speed): ApiSpeed
}
