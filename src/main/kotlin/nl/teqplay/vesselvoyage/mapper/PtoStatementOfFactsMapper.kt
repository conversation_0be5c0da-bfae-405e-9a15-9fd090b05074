package nl.teqplay.vesselvoyage.mapper

import nl.teqplay.vesselvoyage.apiv2.model.SlowMovingPeriod
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.AnchorStop
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.ApproachAreaVisit
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.AreaMeta
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.BerthVisit
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.CategorizedPeriods
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.Encounter
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.EncounterMeta
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.LockStop
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.Pilot
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.PortArea
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.Ship
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.ShipToShipTransfer
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.TerminalVisit
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.Tug
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.UnclassifiedStop
import nl.teqplay.vesselvoyage.model.ShipDetails
import nl.teqplay.vesselvoyage.model.esof.ptoview.AnchorStopInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.ApproachAreaVisitInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.BerthVisitInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.EncounterInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.LockStopInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.PilotInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.PortAreaInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.ShipToShipTransferInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.TerminalVisitInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.TugInfo
import nl.teqplay.vesselvoyage.model.esof.ptoview.UnclassifiedStopInfo
import nl.teqplay.vesselvoyage.model.lightweight.poma.Anchorage
import nl.teqplay.vesselvoyage.model.lightweight.poma.ApproachArea
import nl.teqplay.vesselvoyage.model.lightweight.poma.Berth
import nl.teqplay.vesselvoyage.model.lightweight.poma.Lock
import nl.teqplay.vesselvoyage.model.lightweight.poma.PilotBoardingPlace
import nl.teqplay.vesselvoyage.model.lightweight.poma.Port
import nl.teqplay.vesselvoyage.model.lightweight.poma.ShipToShipArea
import nl.teqplay.vesselvoyage.model.lightweight.poma.Terminal
import nl.teqplay.vesselvoyage.model.v2.NewEncounter
import nl.teqplay.vesselvoyage.model.v2.NewSlowMovingPeriod
import nl.teqplay.vesselvoyage.model.v2.Speed
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.Named
import org.mapstruct.ReportingPolicy
import nl.teqplay.vesselvoyage.apiv2.model.Speed as ApiSpeed

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.ERROR
)
abstract class PtoStatementOfFactsMapper {

    @Mapping(target = "id", source = "activity.id")
    @Mapping(target = "start", source = "activity.start")
    @Mapping(target = "end", source = "activity.end")
    @Mapping(target = "isPassThrough", source = "passThrough") // issues with 'is' prefix...
    abstract fun toPortArea(portAreaInfo: PortAreaInfo): PortArea

    @Mapping(target = "areaId", source = "_id")
    @Mapping(target = "type", constant = "port")
    abstract fun toAreaMeta(port: Port): AreaMeta

    @Mapping(target = "areaId", source = "_id")
    @Mapping(target = "unlocode", ignore = true)
    @Mapping(target = "type", constant = "berth")
    abstract fun toAreaMeta(berth: Berth): AreaMeta

    @Mapping(target = "areaId", source = "_id")
    @Mapping(target = "unlocode", ignore = true)
    @Mapping(target = "type", constant = "terminal")
    abstract fun toAreaMeta(terminal: Terminal): AreaMeta

    @Mapping(target = "areaId", source = "_id")
    @Mapping(target = "unlocode", ignore = true)
    @Mapping(target = "type", constant = "anchorage")
    abstract fun toAreaMeta(anchorage: Anchorage): AreaMeta

    @Mapping(target = "areaId", source = "_id")
    @Mapping(target = "unlocode", ignore = true)
    @Mapping(target = "type", constant = "pilotBoardingPlace")
    abstract fun toAreaMeta(pilotBoardingPlace: PilotBoardingPlace): AreaMeta

    @Mapping(target = "areaId", source = "_id")
    @Mapping(target = "unlocode", ignore = true)
    @Mapping(target = "type", constant = "lock")
    abstract fun toAreaMeta(lock: Lock): AreaMeta

    @Mapping(target = "areaId", source = "_id")
    @Mapping(target = "unlocode", ignore = true)
    @Mapping(target = "type", constant = "approachArea")
    abstract fun toAreaMeta(approachArea: ApproachArea): AreaMeta

    @Mapping(target = "areaId", source = "_id")
    @Mapping(target = "unlocode", ignore = true)
    @Mapping(target = "type", constant = "shipToShip")
    abstract fun toAreaMeta(shipToShipArea: ShipToShipArea): AreaMeta

    @Mapping(target = "id", source = "id")
    @Mapping(target = "terminalVisitId", source = "terminalVisit.activity.id")
    @Mapping(target = "portVisitId", source = "portArea.activity.id")
    @Mapping(target = "portAreaRef", source = "portArea.ref")
    @Mapping(target = "terminalVisitRef", source = "terminalVisit.ref")
    @Mapping(target = "start", source = "activity.start")
    @Mapping(target = "end", source = "activity.end")
    @Mapping(target = "cargoCategoryType", source = "area.cargoCategoryType")
    @Mapping(target = "mooringType", source = "area.mooringType")
    @Mapping(target = "terminalId", source = "area.terminalId")
    abstract fun toBerthVisit(berthVisitInfo: BerthVisitInfo): BerthVisit

    @Mapping(target = "ship", source = ".", qualifiedByName = ["tugToEncounterMeta"])
    abstract fun toTug(tugInfo: TugInfo): Tug

    @Named("tugToEncounterMeta")
    @Mapping(target = "type", source = "encounter.type")
    abstract fun tugToEncounterMeta(tugInfo: TugInfo): EncounterMeta

    @Mapping(target = "id", source = "activity.id")
    @Mapping(target = "portVisitId", source = "portArea.activity.id")
    @Mapping(target = "portAreaRef", source = "portArea.ref")
    @Mapping(target = "start", source = "activity.start")
    @Mapping(target = "end", source = "activity.end")
    @Mapping(target = "mooringStart", source = "mooringActivity.start")
    @Mapping(target = "mooringEnd", source = "mooringActivity.end")
    abstract fun toTerminalVisit(terminalVisitInfo: TerminalVisitInfo): TerminalVisit

    @Mapping(target = "portVisitId", source = "portArea.activity.id")
    @Mapping(target = "portAreaRef", source = "portArea.ref")
    @Mapping(target = "start", source = "stop.start")
    @Mapping(target = "end", source = "stop.end")
    abstract fun toAnchorStop(info: AnchorStopInfo): AnchorStop

    @Mapping(target = "portVisitId", source = "portArea.activity.id")
    @Mapping(target = "portAreaRef", source = "portArea.ref")
    @Mapping(target = "start", source = "stop.start")
    @Mapping(target = "end", source = "stop.end")
    abstract fun toUnclassifiedStop(info: UnclassifiedStopInfo): UnclassifiedStop

    @Mapping(target = "type", source = "encounter.type")
    @Mapping(target = "start", source = "encounter.start")
    @Mapping(target = "end", source = "encounter.end")
    @Mapping(target = "ship", source = ".", qualifiedByName = ["encounterToEncounterMeta"])
    @Mapping(target = "berthVisitId", source = "berthVisitInfo.id")
    @Mapping(target = "terminalVisitId", source = "terminalVisitInfo.activity.id")
    @Mapping(target = "portVisitId", source = "portAreaInfo.activity.id")
    @Mapping(target = "portAreaRef", source = "portAreaInfo.ref")
    @Mapping(target = "berthVisitRef", source = "berthVisitInfo.ref")
    @Mapping(target = "terminalVisitRef", source = "terminalVisitInfo.ref")
    abstract fun toEncounter(info: EncounterInfo): Encounter

    @Named("encounterToEncounterMeta")
    @Mapping(target = "imo", source = "encounter.otherImo")
    @Mapping(target = "mmsi", source = "encounter.otherMmsi")
    @Mapping(target = "type", source = "encounter.type")
    abstract fun toEncounterMeta(info: EncounterInfo): EncounterMeta

    @Mapping(target = "imo", source = "otherImo")
    @Mapping(target = "mmsi", source = "otherMmsi")
    abstract fun toEncounterMeta(internal: NewEncounter): EncounterMeta

    @Mapping(target = "ship", source = "encounter")
    @Mapping(target = "area", source = "pilotArea")
    abstract fun toPilot(info: PilotInfo): Pilot

    @Mapping(target = "type", source = "categories.v2")
    abstract fun toShip(details: ShipDetails): Ship

    @Mapping(target = "portVisitId", source = "portArea.activity.id")
    @Mapping(target = "portAreaRef", source = "portArea.ref")
    @Mapping(target = "start", source = "stop.start")
    @Mapping(target = "end", source = "stop.end")
    abstract fun toLockStop(info: LockStopInfo): LockStop

    @Mapping(target = "id", source = "activity.id")
    @Mapping(target = "portVisitId", source = "portArea.activity.id")
    @Mapping(target = "portAreaRef", source = "portArea.ref")
    @Mapping(target = "area",)
    abstract fun toApproachAreaVisit(info: ApproachAreaVisitInfo): ApproachAreaVisit

    @Mapping(target = "ship", source = "otherShip")
    abstract fun toShipToShip(info: ShipToShipTransferInfo): ShipToShipTransfer

    fun mapCategorizedAnchorStopInfo(source: CategorizedPeriods<AnchorStopInfo>): CategorizedPeriods<AnchorStop> {
        return CategorizedPeriods(
            arrival = source.arrival.map { toAnchorStop(it) }.toMutableList(),
            inPort = source.inPort.map { toAnchorStop(it) }.toMutableList(),
            departure = source.departure.map { toAnchorStop(it) }.toMutableList()
        )
    }

    fun mapCategorizedSlowMovingPeriods(source: CategorizedPeriods<NewSlowMovingPeriod>): CategorizedPeriods<SlowMovingPeriod> {
        return CategorizedPeriods(
            arrival = source.arrival.map { toSlowMovingPeriod(it) }.toMutableList(),
            inPort = source.inPort.map { toSlowMovingPeriod(it) }.toMutableList(),
            departure = source.departure.map { toSlowMovingPeriod(it) }.toMutableList()
        )
    }

    abstract fun toSlowMovingPeriod(source: NewSlowMovingPeriod): SlowMovingPeriod

    @Mapping(target = "average", source = "avg")
    abstract fun toSpeed(source: Speed): ApiSpeed
}
