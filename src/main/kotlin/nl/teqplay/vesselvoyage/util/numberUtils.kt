package nl.teqplay.vesselvoyage.util

import io.github.oshai.kotlinlogging.KotlinLogging
import kotlin.math.absoluteValue
import kotlin.math.max
import kotlin.math.min
import kotlin.math.sign

private val log = KotlinLogging.logger {}

fun isEven(value: Int): Boolean {
    return value % 2 == 0
}

/**
 * Convert a [Float] value to a [Double] without losing any precision.
 */
fun Float.toExactDouble(): Double {
    return this.toString().toDouble()
}

/**
 * @return The minimum value of [a] and [b] or 0.0 if both null
 */
fun minNotNull(a: Float?, b: Float?): Float {
    if (a == null) {
        return b ?: 0.0f
    }

    if (b == null) {
        return a
    }

    return min(a, b)
}

/**
 * @return The maximum value of [a] and [b] or 0.0 if both null
 */
fun maxNotNull(a: Float?, b: Float?): Float {
    if (a == null) {
        return b ?: 0.0f
    }

    if (b == null) {
        return a
    }

    return max(a, b)
}

/**
 * Divides this value by the other value, ceiling the result to an integer that is closer to positive infinity.
 *
 * Example:
 * - 0.ceilDiv(2) = 0
 * - 1.ceilDiv(2) = 1
 * - 2.ceilDiv(2) = 1
 * - 3.ceilDiv(2) = 2
 * - 4.ceilDiv(2) = 2
 * - 5.ceilDiv(2) = 2
 * - 6.ceilDiv(2) = 3
 *
 * From: stackoverflow.com/questions/77704199/kotlin-idiomatic-way-to-round-up-integer-division
 */
fun Int.ceilDiv(other: Int): Int {
    return this.floorDiv(other) + this.rem(other).sign.absoluteValue
}

/**
 * Calculates the weighted average of [valueA] and [valueB].
 *
 * @throws IllegalArgumentException When the weight is < 0
 */
fun weightedAverage(
    valueA: Float,
    weightA: Long,
    valueB: Float,
    weightB: Long
): Float {
    val parsedWeightA = max(weightA, 0L)
    val parsedWeightB = max(weightB, 0L)
    return if (parsedWeightA + parsedWeightB != 0L) {
        ((valueA * parsedWeightA) + (valueB * parsedWeightB)) / (parsedWeightA + parsedWeightB)
    } else {
        // both weights are 0, meaning the values are equal for an average, therefore divide by 2
        (valueA + valueB) / 2L
    }
}
