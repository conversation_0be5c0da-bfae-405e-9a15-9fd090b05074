package nl.teqplay.vesselvoyage.util

import java.time.Instant
import java.time.YearMonth
import java.time.ZoneOffset

/**
 * Object containing all constants that are used in VesselVoyage and are not exposed to any of the public libraries.
 */
object Constants {

    /**
     * Recalculating before this date doesn't make much sense.
     * We got Spire data from this date, so doing anything before that would result in not great data.
     */
    val recalculationStartDate: Instant = YearMonth.of(2022, 1)
        .atDay(1)
        .atStartOfDay()
        .toInstant(ZoneOffset.UTC)
}
