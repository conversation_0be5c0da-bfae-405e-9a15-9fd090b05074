package nl.teqplay.vesselvoyage.util

import nl.teqplay.vesselvoyage.model.TimeWindow
import java.time.Duration
import java.time.Instant
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalAdjusters

fun toZonedDateTime(timestampMillis: Long): ZonedDateTime {
    return ZonedDateTime.ofInstant(Instant.ofEpochMilli(timestampMillis), ZoneOffset.UTC)
}

fun toZonedDateTime(instant: Instant): ZonedDateTime {
    return instant.atZone(ZoneOffset.UTC)
}

fun toMillis(dateTime: ZonedDateTime): Long {
    return dateTime.toInstant().toEpochMilli()
}

fun List<Duration>.sum(): Duration {
    return this.fold(Duration.ZERO) { total, duration -> total + duration }
}

fun List<Duration>.average(): Duration {
    return if (this.isNotEmpty()) {
        this.sum().dividedBy(this.size.toLong())
    } else {
        Duration.ZERO
    }
}

fun ZonedDateTime.toEpochMillisecond(): Long {
    return this.toInstant().toEpochMilli()
}

fun generateTimeWindows(
    startTime: ZonedDateTime,
    endTime: ZonedDateTime,
    stepSize: Duration,
    limit: Int = 1000 // security measure to prevent an endless loop
): List<TimeWindow> {
    // We can't generate a TimeWindow when the provided startTime is after the provided endTime
    if (startTime.isAfter(endTime)) {
        return emptyList()
    }

    val timeWindows = mutableListOf<TimeWindow>()

    var index = 0
    var currentStartTime = startTime.toUTC()

    do {
        val currentEndTime = minOf(currentStartTime.plus(stepSize), endTime)

        timeWindows.add(TimeWindow(currentStartTime, currentEndTime))

        currentStartTime = currentEndTime

        index += 1
        if (index >= limit) {
            throw TimeWindowException("Maximum number of iterations reached")
        }
    } while (currentStartTime.isBefore(endTime))

    return timeWindows
}

/**
 * Create TimeWindows of full months.
 * The first returned month is the month in which startTime occurs,
 * The last returned month is the month in which endTime occurs
 */
fun generateTimeWindowsInMonths(
    startTime: ZonedDateTime,
    endTime: ZonedDateTime,
    limit: Int = 1000 // security measure to prevent an endless loop
): List<TimeWindow> {
    val timeWindows = mutableListOf<TimeWindow>()

    var index = 0
    var currentStartTime = startTime.toUTC()
        .with(TemporalAdjusters.firstDayOfMonth())
        .truncatedTo(ChronoUnit.DAYS)

    do {
        // note that the .plusMonths(1) method takes care of months having a different number of days (28, 30, 31)
        val currentEndTime = currentStartTime.plusMonths(1)

        timeWindows.add(TimeWindow(currentStartTime, currentEndTime))

        currentStartTime = currentEndTime

        index += 1
        if (index >= limit) {
            throw TimeWindowException("Maximum number of iterations reached")
        }
    } while (currentStartTime.isBefore(endTime))

    return timeWindows
}

fun ZonedDateTime.toUTC(): ZonedDateTime {
    return this.withZoneSameInstant(ZoneOffset.UTC)
}
