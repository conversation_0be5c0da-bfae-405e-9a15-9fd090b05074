package nl.teqplay.vesselvoyage.util

import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.SimpleStartEnd
import nl.teqplay.vesselvoyage.model.v2.StartEnd
import java.time.Instant

/**
 * Whether [instant] happens inside [this] time range.
 * When [isFinished]: `start >= instant < end`
 * When [isOngoing]: `start >= instant`
 */
operator fun StartEnd.contains(instant: Instant): Boolean {
    return if (isFinished()) {
        instant >= start.time && instant < this.end!!.time
    } else {
        instant >= start.time
    }
}

/**
 * Whether the time of the [locationTime] happens in this.
 * Note that this class is start-inclusive and end-exclusive, meaning that
 */
operator fun StartEnd.contains(locationTime: LocationTime): Boolean {
    return contains(locationTime.time)
}

/**
 * Whether [other] starts and ends in this.
 */
operator fun StartEnd.contains(other: StartEnd): Boolean {
    if (this.isFinished()) {
        if (other.isFinished()) {
            return other.start in this && other.end!! in this
        }

        // other is still ongoing, meaning other.end is always > this.end, meaning this can never contain other
        return false
    }

    return other.start in this
}

/**
 * When [end] is set, the system considers this activity finished. Opposite of [isOngoing].
 * For example, when the implementation is about a ship and the ship finished its activity, [end] should be set.
 */
fun StartEnd.isFinished() = end != null

/**
 * When [end] is null, the system considers this activity not ongoing, i.e. not finished. Opposite of [isFinished].
 * For example, when the implementation is about a ship and the ship is still busy with its activity, [end]
 * should not be set.
 */
fun StartEnd.isOngoing() = !isFinished()

/** Whether this.start happens in [other] */
fun StartEnd.startsIn(other: StartEnd) = this.start in other

/**
 * Checks if the `start` of this `StartEnd` occurs within the specified `other` range,
 * with optional tolerances for the start and end times.
 *
 * @param other The range to check if this `start` falls within.
 * @param startTolerance Tolerance in minutes to adjust the start of `other`.
 * @param endTolerance Tolerance in minutes to adjust the end of `other`.
 */
fun StartEnd.startsIn(other: StartEnd, startTolerance: Long = 0, endTolerance: Long = 0): Boolean {
    val adjustedOther = SimpleStartEnd(
        start = other.start.minusMinutes(startTolerance),
        end = other.end?.plusMinutes(endTolerance)
    )

    return this.start in adjustedOther
}

/** Whether this.end happens in [other] */
fun StartEnd.endsIn(other: StartEnd): Boolean {
    if (this.isFinished()) {
        val endedAfterOtherStart = this.end!! > other.start
        if (other.isFinished()) {
            return endedAfterOtherStart && this.end!! < other.end!!
        }

        // this is finished, other is ongoing, so this.end must be after other.start
        return endedAfterOtherStart
    }

    // 'this' is ongoing, other must also be ongoing, otherwise if other ended then this.end will always be
    // after other.end
    return other.isOngoing()
}

/**
 * Checks if the `end` of this `StartEnd` occurs within the specified `other` range,
 * with optional tolerances for the start and end times.
 *
 * @param other The range to check if this `end` falls within.
 * @param startTolerance Tolerance in minutes to adjust the start of `other`.
 * @param endTolerance Tolerance in minutes to adjust the end of `other`.
 */
fun StartEnd.endsIn(other: StartEnd, startTolerance: Long = 0, endTolerance: Long = 0): Boolean {
    if (this.isFinished()) {
        val adjustedStart = other.start.time.minusSeconds(startTolerance * 60)
        val endedAfterOtherStart = this.end!!.time > adjustedStart

        if (other.isFinished()) {
            val adjustedOtherEnd = other.end!!.time.plusSeconds(endTolerance * 60)
            return endedAfterOtherStart && this.end!!.time < adjustedOtherEnd
        }

        // this is finished, other is ongoing, so adjusted this.end must be after other.start
        return endedAfterOtherStart
    }

    // 'this' is ongoing, other must also be ongoing
    return other.isOngoing()
}

/**
 * Returns true when:
 * - [this] starts or ends in [other], or vice versa
 * - [this] fully encloses [other], or vice versa
 */
fun StartEnd.overlaps(other: StartEnd) = this.startsIn(other) ||
    other.startsIn(this) ||
    this.endsIn(other) ||
    other.endsIn(this) ||
    this.contains(other)

fun LocationTime.minusMinutes(minutes: Long) =
    this.copy(time = this.time.minusSeconds(minutes * 60))

fun LocationTime.plusMinutes(minutes: Long) =
    this.copy(time = this.time.plusSeconds(minutes * 60))
