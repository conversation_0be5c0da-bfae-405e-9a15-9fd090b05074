package nl.teqplay.vesselvoyage.util

import com.github.doyaaaaaken.kotlincsv.dsl.csvWriter
import nl.teqplay.vesselvoyage.model.ExtendedVisit
import nl.teqplay.vesselvoyage.model.ExtendedVoyage
import nl.teqplay.vesselvoyage.model.statistics.PortStatistic
import nl.teqplay.vesselvoyage.model.statistics.ShipStatistic
import nl.teqplay.vesselvoyage.model.statistics.VisitStatistic
import java.io.ByteArrayOutputStream
import java.io.OutputStream
import java.time.Duration

fun generateCSV(header: List<String>, rows: List<List<String?>>): OutputStream {
    val outputStream = ByteArrayOutputStream()
    csvWriter { delimiter = ';' }.writeAll(listOf(header) + rows, outputStream)

    return outputStream
}

enum class VisitCsvFields {
    _id,
    imo,
    mmsi,
    shipName,
    shipCategory,
    startTime,
    startTimePort,
    endTime,
    anchorages,
    ports,
    trueDestination,
    finished
}

val VisitCsvHeader = VisitCsvFields.values().toList()

fun ExtendedVisit.toCsvRow(): List<String?> {
    // TODO: Do we want to return more detailed results for every anchorage and port area?
    //  If so, how? Flatten? or multiple lines per visit
    return VisitCsvHeader.map { fieldName ->
        when (fieldName) {
            VisitCsvFields._id -> this.visit._id
            VisitCsvFields.imo -> this.visit.imo
            VisitCsvFields.mmsi -> this.visit.mmsi
            VisitCsvFields.shipName -> this.ship?.name
            VisitCsvFields.shipCategory -> this.ship?.categories?.v1?.toString()
            VisitCsvFields.startTime -> this.visit.startTime.toUTC().toString()
            VisitCsvFields.startTimePort -> this.visit.startTimePort?.toString()
            VisitCsvFields.endTime -> this.visit.endTime?.toUTC().toString()
            VisitCsvFields.anchorages -> this.visit.anchorAreas.joinToString { it.anchorAreaId }
            VisitCsvFields.ports -> this.visit.portAreas.joinToString { it.portId }
            VisitCsvFields.trueDestination -> this.visit.dest?.trueDestination
            VisitCsvFields.finished -> this.visit.finished.toString()
        }
    }
}

enum class VoyageCsvFields {
    _id,
    imo,
    mmsi,
    shipName,
    shipCategory,
    startTime,
    startPorts,
    trueDestination,
    eta,
    endTime,
    endPorts,
    finished
}

val VoyageCsvHeader = VoyageCsvFields.values().toList()

fun ExtendedVoyage.toCsvRow(): List<String?> {
    // TODO: Do we want to return more detailed results for every anchorage and port area?
    //  If so, how? Flatten? or multiple lines per visit
    return VoyageCsvHeader.map { fieldName ->
        when (fieldName) {
            VoyageCsvFields._id -> this.voyage._id
            VoyageCsvFields.imo -> this.voyage.imo
            VoyageCsvFields.mmsi -> this.voyage.mmsi
            VoyageCsvFields.shipName -> this.ship?.name
            VoyageCsvFields.shipCategory -> this.ship?.categories?.v1?.toString()
            VoyageCsvFields.startTime -> this.voyage.startTime.toUTC().toString()
            VoyageCsvFields.startPorts -> this.voyage.startPortIds.joinToString()
            VoyageCsvFields.trueDestination -> this.voyage.dest?.trueDestination
            VoyageCsvFields.eta -> this.voyage.eta?.predictedTime?.toString()
            VoyageCsvFields.endTime -> this.voyage.endTime?.toUTC().toString()
            VoyageCsvFields.endPorts -> this.voyage.endPortIds?.joinToString()
            VoyageCsvFields.finished -> this.voyage.finished.toString()
        }
    }
}

enum class ShipStatisticCsvFields {
    imo,
    startTime,
    endTime,
    periodDurationHours,
    totalSailingDurationHours,
    totalMooredDurationHours,
    totalAnchoredDurationHours,
    totalUnclassifiedStopDurationHours
}

val ShipStatisticCsvHeader = ShipStatisticCsvFields.values().toList()

fun ShipStatistic.toCsvRow(): List<String?> {
    return ShipStatisticCsvHeader.map { fieldName ->
        when (fieldName) {
            ShipStatisticCsvFields.imo -> this.imo
            ShipStatisticCsvFields.startTime -> this.startTime.toUTC().toString()
            ShipStatisticCsvFields.endTime -> this.endTime.toUTC().toString()
            ShipStatisticCsvFields.periodDurationHours -> this.periodDuration.toHoursWithDecimals().toString()
            ShipStatisticCsvFields.totalSailingDurationHours -> this.totalSailingDuration.toHoursWithDecimals().toString()
            ShipStatisticCsvFields.totalMooredDurationHours -> this.totalMooredDuration.toHoursWithDecimals().toString()
            ShipStatisticCsvFields.totalAnchoredDurationHours -> this.totalAnchoredDuration.toHoursWithDecimals().toString()
            ShipStatisticCsvFields.totalUnclassifiedStopDurationHours -> this.totalUnclassifiedStopDuration.toHoursWithDecimals().toString()
        }
    }
}

enum class VisitStatisticCsvFields {
    imo,
    portId,
    visitStartTime,
    visitEndTime,
    visitDurationHours,
    sailingDurationHours,
    mooredDurationHours,
    anchoredDurationHours,
    unclassifiedStopDurationHours
}

val VisitStatisticCsvHeader = VisitStatisticCsvFields.values().toList()

fun VisitStatistic.toCsvRow(): List<String?> {
    return VisitStatisticCsvHeader.map { fieldName ->
        when (fieldName) {
            VisitStatisticCsvFields.imo -> this.imo
            VisitStatisticCsvFields.portId -> this.portId
            VisitStatisticCsvFields.visitStartTime -> this.visitStartTime.toUTC().toString()
            VisitStatisticCsvFields.visitEndTime -> this.visitEndTime.toUTC().toString()
            VisitStatisticCsvFields.visitDurationHours -> this.visitDuration.toHoursWithDecimals().toString()
            VisitStatisticCsvFields.sailingDurationHours -> this.sailingDuration.toHoursWithDecimals().toString()
            VisitStatisticCsvFields.mooredDurationHours -> this.mooredDuration.toHoursWithDecimals().toString()
            VisitStatisticCsvFields.anchoredDurationHours -> this.anchoredDuration.toHoursWithDecimals().toString()
            VisitStatisticCsvFields.unclassifiedStopDurationHours -> this.unclassifiedStopDuration.toHoursWithDecimals().toString()
        }
    }
}

enum class PortStatisticCsvFields {
    portId,
    startTime,
    endTime,
    visitCount,
    periodDurationHours,
    averageVisitDurationHours,
    averageSailingDurationHours,
    averageMooredDurationHours,
    averageAnchoredDurationHours,
    averageUnclassifiedStopDurationHours
}

val PortStatisticCsvHeader = PortStatisticCsvFields.values().toList()

fun PortStatistic.toCsvRow(): List<String?> {
    return PortStatisticCsvHeader.map { fieldName ->
        when (fieldName) {
            PortStatisticCsvFields.portId -> this.portId
            PortStatisticCsvFields.startTime -> this.startTime.toUTC().toString()
            PortStatisticCsvFields.endTime -> this.endTime.toUTC().toString()
            PortStatisticCsvFields.visitCount -> this.visitCount.toString()
            PortStatisticCsvFields.periodDurationHours -> this.periodDuration.toHoursWithDecimals().toString()
            PortStatisticCsvFields.averageVisitDurationHours -> this.averageVisitDuration.toHoursWithDecimals().toString()
            PortStatisticCsvFields.averageSailingDurationHours -> this.averageSailingDuration.toHoursWithDecimals().toString()
            PortStatisticCsvFields.averageMooredDurationHours -> this.averageMooredDuration.toHoursWithDecimals().toString()
            PortStatisticCsvFields.averageAnchoredDurationHours -> this.averageAnchoredDuration.toHoursWithDecimals().toString()
            PortStatisticCsvFields.averageUnclassifiedStopDurationHours -> this.averageUnclassifiedStopDuration.toHoursWithDecimals().toString()
        }
    }
}

// TODO: unit test
private fun Duration.toHoursWithDecimals(): Double {
    val millis = this.toMillis().toDouble()
    return millis / 1000.0 / 60.0 / 60.0
}
