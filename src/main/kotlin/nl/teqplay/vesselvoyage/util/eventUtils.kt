package nl.teqplay.vesselvoyage.util

import nl.teqplay.aisengine.event.interfaces.LocationBasedEvent
import nl.teqplay.aisengine.event.interfaces.PredictedEvent
import nl.teqplay.aisengine.event.model.LockEtaEvent
import nl.teqplay.aisengine.event.model.PortcallPilotBoardingEtaEvent
import nl.teqplay.aisengine.event.model.hamis.HamisEtaEvent
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.vesselvoyage.model.Eta
import nl.teqplay.vesselvoyage.model.EventPair
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import java.time.Duration
import java.time.ZoneOffset

fun PredictedEvent.toEta(): Eta? {
    return when (this) {
        is HamisEtaEvent -> { this.toEta() }
        is LockEtaEvent -> { this.toEta() }
        is PortcallPilotBoardingEtaEvent -> { this.toEta() }
        else -> return null // Event not supported
    }
}

fun HamisEtaEvent.toEta(): Eta? {
    val portId = this.port.unlocode ?: return null
    val source = this.source ?: return null
    val type = when (this.area.type) {
        AreaIdentifier.AreaType.PORT -> "port"
        AreaIdentifier.AreaType.BERTH -> "berth"
        AreaIdentifier.AreaType.PILOT_BOARDING_PLACE -> "pilotBoardingPlace"
        AreaIdentifier.AreaType.LOCK -> "lock"
        else -> "unknown"
    }
    return Eta(
        type = type,
        predictedTime = this.predictedTime.atZone(ZoneOffset.UTC),
        predictedAt = this.createdTime.atZone(ZoneOffset.UTC),
        vesselAgent = this.vesselAgent,
        portId = portId,
        location = this.area.name ?: "Unknown",
        ucrn = this.portcallId,
        source = source,
    )
}

fun LockEtaEvent.toEta(): Eta? {
    val portId = this.area.unlocode ?: return null

    return Eta(
        type = "lock",
        predictedTime = this.predictedTime.atZone(ZoneOffset.UTC),
        predictedAt = this.createdTime.atZone(ZoneOffset.UTC),
        vesselAgent = null,
        portId = portId,
        location = this.area.name ?: "Unknown",
        ucrn = null,
        source = "Unknown",
    )
}

fun PortcallPilotBoardingEtaEvent.toEta(): Eta? {
    val portId = this.area.unlocode ?: return null

    return Eta(
        type = "pilotBoardingPlace",
        predictedTime = this.predictedTime.atZone(ZoneOffset.UTC),
        predictedAt = this.createdTime.atZone(ZoneOffset.UTC),
        vesselAgent = null,
        portId = portId,
        location = this.area.name ?: "Unknown",
        ucrn = this.portcallId,
        source = this.source,
    )
}

fun EventPair.isFinished(): Boolean {
    return this.endTime != null
}

fun EventPair.duration(): Duration {
    return Duration.between(this.startTime, this.endTime)
}

fun EventPair.isNotFinished(): Boolean {
    return !this.isFinished()
}

fun LocationBasedEvent.generateLocationTime() = LocationTime(
    location = this.location,
    time = this.actualTime
)
