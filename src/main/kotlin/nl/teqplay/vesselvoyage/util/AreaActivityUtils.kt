package nl.teqplay.vesselvoyage.util

import nl.teqplay.vesselvoyage.model.v2.AreaActivity
import nl.teqplay.vesselvoyage.model.v2.FallbackType
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import java.time.Duration

fun AreaActivity.asDuration(): Duration? {
    val end = this.end ?: return null

    return Duration.between(this.start.time, end.time)
}

/**
 * Replace the first activity matching [currentActivity] with the [updatedActivity].
 *
 * @param currentActivity The activity we want to replace.
 * @param updatedActivity The replacement activity.
 * @param sortByStartTime When true it automatically sorts the result list by activity start time.
 */
fun MutableList<AreaActivity>.replaceFirst(
    currentActivity: AreaActivity,
    updatedActivity: AreaActivity,
    sortByStartTime: Boolean = true,
): List<AreaActivity> {
    this.remove(currentActivity)
    this.add(updatedActivity)

    if (sortByStartTime) {
        return this.sortedBy { activity -> activity.start.time }
    }

    // Don't do any sorting
    return this
}

fun List<AreaActivity>.overrideEndFallbacks(newEnd: LocationTime, vararg matchingFallbackTypes: FallbackType): MutableList<AreaActivity> {
    return this.map { activity -> activity.overrideEndIfFallback(newEnd, *matchingFallbackTypes) }.toMutableList()
}

fun AreaActivity.overrideEndIfFallback(newEnd: LocationTime, vararg matchingFallbackTypes: FallbackType): AreaActivity {
    if (this.end?.fallback in matchingFallbackTypes) {
        return this.copy(end = newEnd)
    }

    return this
}

/**
 * End all activities with the provided [end]
 */
fun List<AreaActivity>.finishActivitiesIfEndMissing(end: LocationTime): MutableList<AreaActivity> =
    this.map { activity -> activity.finishActivityIfEndMissing(end) }.toMutableList()

/**
 * Finish the area activity with the provided [end] is still ongoing
 */
fun AreaActivity.finishActivityIfEndMissing(end: LocationTime): AreaActivity {
    // Only fill in the provided end time whe missing
    if (this.isOngoing()) {
        return this.copy(end = end)
    }

    // Nothing to finish, result the activity as is
    return this
}

/**
 * Check if [this] area activity has the same [AreaActivity.areaId] as [other].
 *
 * @param other The other area activity we get the id from
 * @param suffix When not null, remove the provided suffix from both the  [this] and [other].
 */
fun AreaActivity.matchingAreaId(other: AreaActivity, suffix: String? = null): Boolean {
    val a = this.areaId
    val b = other.areaId

    return if (suffix != null) {
        a.removeSuffix(suffix) == b.removeSuffix(suffix)
    } else {
        a == b
    }
}
