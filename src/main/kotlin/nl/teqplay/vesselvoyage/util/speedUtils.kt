package nl.teqplay.vesselvoyage.util

import nl.teqplay.vesselvoyage.model.internal.TraceItem
import nl.teqplay.vesselvoyage.model.v2.Speed
import java.time.Duration
import java.time.Instant
import kotlin.math.max
import kotlin.math.min

/**
 * Minimal allowed speed over ground.
 */
const val STANDSTILL_SPEED_OVER_GROUND = 0.0f

/** A mutable version of [Speed], to help make for-loops less expensive */
private class MutableSpeed(
    var min: Float,
    var max: Float,
    var avg: Float,

    /**
     * Total amount of points used to calculate the [avg], useful in case of a sample-size average calculation.
     */
    var count: Int,

    /** The last speed over ground that was added to the [avg] */
    var lastSpeedOverGround: Float,

    /** The duration of the [avg] speed, useful in case of a weighted average calculation */
    var durationSeconds: Long,

    /** Marks the timestamp of [duration]'s end, to determine the duration of any later amended speeds */
    var lastSpeedOverGroundTimestamp: Instant
) {
    companion object {
        fun ofImmutable(immutable: Speed) = MutableSpeed(
            min = immutable.min,
            max = immutable.max,
            avg = immutable.avg,
            count = immutable.count,
            lastSpeedOverGround = immutable.lastSpeedOverGround,
            durationSeconds = immutable.duration.toSeconds(),
            lastSpeedOverGroundTimestamp = immutable.lastSpeedOverGroundTimestamp,
        )
    }

    fun toImmutable() = Speed(
        min = min,
        max = max,
        avg = avg,
        count = count,
        lastSpeedOverGround = lastSpeedOverGround,
        duration = Duration.ofSeconds(durationSeconds),
        lastSpeedOverGroundTimestamp = lastSpeedOverGroundTimestamp,
    )

    /**
     * Expands the duration of the [lastSpeedOverGround], changing the weight of that speed over ground, affecting
     * the average speed. The duration is derived using [lastSpeedOverGroundTimestamp] and [timestamp].
     */
    fun amendDurationForLastSpeed(timestamp: Instant) = amendSpeedWithDuration(lastSpeedOverGround, timestamp)

    /**
     * Amends the average speed with the given [speedOverGround] and [timestamp]. [min] and [max] are updated when
     * the new speed over ground is a new lower or higher value respectively.
     * The duration is derived using [lastSpeedOverGroundTimestamp] and [timestamp].
     *
     * @return This instance, for chaining
     */
    fun amendSpeedWithDuration(
        speedOverGround: Float,
        timestamp: Instant
    ): MutableSpeed {
        val durationToAddSec = Duration.between(lastSpeedOverGroundTimestamp, timestamp).toSeconds()

        // the last speedOverGround was not yet taken into account to the average speed
        // only now we know the duration, so apply that and
        val newAvgSpeed = weightedAverage(
            valueA = avg,
            weightA = durationSeconds,
            valueB = lastSpeedOverGround,
            weightB = durationToAddSec
        )

        min = min(min, speedOverGround)
        max = max(max, speedOverGround)
        avg = newAvgSpeed
        count++
        durationSeconds += durationToAddSec
        lastSpeedOverGround = speedOverGround
        lastSpeedOverGroundTimestamp = timestamp

        return this
    }
}

/**
 * Merges this and [other] speed.
 *
 * This function assumes:
 * - this and [other] are adjacent without time in between. The order (this-other or other-this) does not matter.
 * - this and [other] do not overlap, as that would mess up the weights
 */
fun Speed.merge(other: Speed): Speed {
    val newest = if (lastSpeedOverGroundTimestamp > other.lastSpeedOverGroundTimestamp) {
        this
    } else {
        other
    }
    return Speed(
        min = min(min, other.min),
        max = max(max, other.max),
        avg = weightedAverage(
            valueA = avg,
            weightA = duration.toSeconds(),
            valueB = other.avg,
            weightB = other.duration.toSeconds()
        ),
        count = count + other.count,
        lastSpeedOverGround = newest.lastSpeedOverGround,
        lastSpeedOverGroundTimestamp = newest.lastSpeedOverGroundTimestamp,
        duration = duration + other.duration
    )
}

private fun Speed.toMutable() = MutableSpeed.ofImmutable(this)

fun Speed.amendSpeedWithDuration(traceItems: List<TraceItem>) = amendSpeedWithTrace(traceItems, this)

fun createSpeedFromSingleSpeedWithDuration(
    speedOverGround: Float,
    timestamp: Instant,
    duration: Duration
) = Speed(
    min = speedOverGround,
    max = speedOverGround,
    avg = speedOverGround,
    count = 1,
    lastSpeedOverGround = speedOverGround,
    duration = duration,
    lastSpeedOverGroundTimestamp = timestamp
)

/**
 * Derives average speed from the [trace] using a weighted average. Returns null when it's not able to calculate the
 * speed, or when [trace] is empty.
 *
 * Weighted average speed calculation
 * A [TraceItem.speedOverGround] only means something when it gets a duration, or 'weight' in a weighted average
 * calculation. When deriving from trace items and the last item in the list has a speedOverGround set, then this
 * speedOverGround has 0 duration, therefore it may seem like it's not taken into account.
 * Consider the following examples of trace items, the value being the speedOverGround, _ is a trace item without speed:
 * time:  0  15  30  45  60
 *    a:  2   2   2   2   2 -> average = 2
 *    b:  2   2   2   2   4 -> average = 2, because last speed has no duration, therefore no weight in the calculation
 *    c:  2   2   2   4   _ -> average = 2.5, because last trace item gives duration to speed 4
 *
 * Starting speed
 * A starting speed is required to do an average speed calculation. The system tries (1) to find it from the first
 * [TraceItem.speedOverGround], or (2) using the [speedOverGroundWhenMissingFirst]. If both are null, the function
 * returns null. An empty trace also results in null. The system could find the speed in other trace items, but that
 * behavior would become unpredictable.
 *
 * @param trace The trace, where the [TraceItem.speedOverGround] is used to calculate the speed. The function expects
 * a value for the first [TraceItem.speedOverGround]. If that's not available, set [speedOverGroundWhenMissingFirst]!
 * @param speedOverGroundWhenMissingFirst Starting speed when the first [TraceItem.speedOverGround] is null.
 */
fun createSpeedOfTraceItemsOrNull(
    trace: List<TraceItem>,
    speedOverGroundWhenMissingFirst: Float?
): Speed? {
    if (trace.isEmpty()) return null

    // create the speed from the first trace item, or use the speedOverGroundWhenMissingFirst
    val first = trace.first()
    val initialSpeedOverGround = first.speedOverGround
        ?: speedOverGroundWhenMissingFirst
        ?: return null

    val initialSpeed = createSpeedFromSingleSpeedWithDuration(
        speedOverGround = initialSpeedOverGround,
        timestamp = first.timestamp,
        duration = Duration.ZERO // duration is only known when adding the next trace item
    )

    // amend the rest of the trace
    return if (trace.size > 1) {
        initialSpeed.amendSpeedWithDuration(trace.drop(1))
    } else {
        initialSpeed
    }
}

/**
 * Amends the speed and duration of the new trace items to [currentSpeed], using a weighted average calculation for
 * the average speed. Trace items with a duration of 0 seconds are ignored, as a ship would never sail a speed for 0
 * seconds.
 */
fun amendSpeedWithTrace(
    traceItems: List<TraceItem>,
    currentSpeed: Speed
): Speed {

    if (traceItems.isEmpty()) return currentSpeed

    return traceItems.fold(currentSpeed.toMutable()) { current, item ->
        val itemDurationSec = Duration.between(current.lastSpeedOverGroundTimestamp, item.timestamp).toSeconds()

        // A ship would never sail a certain speed for 0 seconds. It would also mean/weigh nothing in the weighted
        // average calculation and therefore not influence the outcome.
        if (itemDurationSec > 0) {
            if (item.speedOverGround != null && item.speedOverGround >= STANDSTILL_SPEED_OVER_GROUND) {
                current.amendSpeedWithDuration(item.speedOverGround, item.timestamp)
            } else {
                current.amendDurationForLastSpeed(item.timestamp)
            }
        }
        current
    }.toImmutable()
}

/**
 * Merge the [existing] Speed and [new] Speed into one.
 */
fun mergeSpeeds(existing: Speed?, new: Speed?): Speed? {
    // nothing to merge when one or both are null
    if (existing == null || new == null) {
        return existing ?: new
    }

    return existing.merge(new)
}
