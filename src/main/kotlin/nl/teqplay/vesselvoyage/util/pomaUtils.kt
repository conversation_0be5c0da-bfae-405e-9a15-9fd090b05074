package nl.teqplay.vesselvoyage.util

import nl.teqplay.vesselvoyage.model.lightweight.poma.Anchorage
import nl.teqplay.vesselvoyage.model.lightweight.poma.ApproachArea
import nl.teqplay.vesselvoyage.model.lightweight.poma.Berth
import nl.teqplay.vesselvoyage.model.lightweight.poma.Lock
import nl.teqplay.vesselvoyage.model.lightweight.poma.PilotBoardingPlace
import nl.teqplay.vesselvoyage.model.lightweight.poma.PomaModel
import nl.teqplay.vesselvoyage.model.lightweight.poma.Terminal

/** Poma has no abstraction for the 'ports' property, try to solve that this way */
fun PomaModel.ports(): List<String> = when (this) {
    is Anchorage -> ports
    is ApproachArea -> ports
    is Berth -> ports
    is Lock -> ports
    is PilotBoardingPlace -> ports
    is Terminal -> ports
    else -> emptyList()
}
