package nl.teqplay.vesselvoyage.util

import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.BAD_REQUEST)
class ProcessEventException(message: String, cause: Throwable? = null) : RuntimeException(message, cause)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class TimeWindowException(message: String, cause: Throwable? = null) : RuntimeException(message, cause)

@ResponseStatus(HttpStatus.NOT_FOUND)
class ResourceNotFoundException(message: String, cause: Throwable? = null) : RuntimeException(message, cause)

@ResponseStatus(HttpStatus.NOT_FOUND)
class MMSINotFoundException(message: String, cause: Throwable? = null) : RuntimeException(message, cause)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class InvalidStatisticsInputException(message: String, cause: Throwable? = null) : RuntimeException(message, cause)

@ResponseStatus(HttpStatus.NOT_FOUND)
class PomaException(message: String, cause: Throwable? = null) : RuntimeException(message, cause)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class ConvertTeqplayEventException(message: String, cause: Throwable? = null) : RuntimeException(message, cause)
