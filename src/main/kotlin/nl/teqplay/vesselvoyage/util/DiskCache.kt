package nl.teqplay.vesselvoyage.util

import com.fasterxml.jackson.core.type.TypeReference
import nl.teqplay.vesselvoyage.datasource.TempFileDataSource

class DiskCache<T>(
    private val enabled: Boolean,
    private val filename: String,
    private val fetch: () -> List<T>,
    private val tempFileDataSource: TempFileDataSource,
    private val typeReference: TypeReference<List<T>>
) {
    fun load(onLoad: (data: List<T>) -> Unit) {
        val data = if (enabled) {
            tempFileDataSource.readFromFile(filename, typeReference) ?: refresh()
        } else {
            refresh()
        }

        onLoad(data)
    }

    fun refresh(): List<T> {
        val result = fetch()

        if (enabled) {
            tempFileDataSource.writeToFile(filename, result)
        }

        return result
    }
}
