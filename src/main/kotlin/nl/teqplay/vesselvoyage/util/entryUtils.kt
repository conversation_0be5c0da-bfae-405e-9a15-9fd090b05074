package nl.teqplay.vesselvoyage.util

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.event.interfaces.AnchoredEvent
import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.aisengine.event.interfaces.StopEvent
import nl.teqplay.aisengine.event.interfaces.UniqueBerthEvent
import nl.teqplay.aisengine.event.model.AreaEndEvent
import nl.teqplay.csi.model.ship.info.ShipRegisterInfoCache
import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2
import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2.CONTAINER
import nl.teqplay.skeleton.common.exception.BadRequestException
import nl.teqplay.skeleton.model.Location
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.ShipPropertyFilterRequest
import nl.teqplay.vesselvoyage.logic.FindAnchorage
import nl.teqplay.vesselvoyage.logic.FindPortById
import nl.teqplay.vesselvoyage.logic.FindShipTrace
import nl.teqplay.vesselvoyage.logic.detectSlowMovingPeriods
import nl.teqplay.vesselvoyage.model.AnchorAreaVisit
import nl.teqplay.vesselvoyage.model.BerthAreaVisit
import nl.teqplay.vesselvoyage.model.Destination
import nl.teqplay.vesselvoyage.model.Entry
import nl.teqplay.vesselvoyage.model.Eta
import nl.teqplay.vesselvoyage.model.EventPair
import nl.teqplay.vesselvoyage.model.MAX_NON_MATCHING_ANCHOR_AREAS
import nl.teqplay.vesselvoyage.model.MAX_OVERLAPPING_PORT_DISTANCE_METERS
import nl.teqplay.vesselvoyage.model.MAX_PASS_THROUGH_AREAS
import nl.teqplay.vesselvoyage.model.PortAreaVisit
import nl.teqplay.vesselvoyage.model.Stop
import nl.teqplay.vesselvoyage.model.StopType
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.Voyage
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.model.createVisitId
import nl.teqplay.vesselvoyage.model.createVoyageId
import nl.teqplay.vesselvoyage.model.internal.ClassifiedStop
import nl.teqplay.vesselvoyage.model.lightweight.poma.Anchorage
import nl.teqplay.vesselvoyage.model.v2.AreaActivity
import nl.teqplay.vesselvoyage.model.v2.FallbackType
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewStop
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import nl.teqplay.vesselvoyage.properties.EventProcessingProperties
import nl.teqplay.vesselvoyage.service.processing.EventUtil.relatedEvent
import java.time.Duration
import java.time.Instant
import java.time.ZoneOffset

private val log = KotlinLogging.logger {}

fun startInitialVisit(event: AreaEvent, imo: Int, unlocode: String): VisitShipStatus {
    val visit = Visit(
        _id = createVisitId(event._id),
        imo = imo.toString(),
        mmsi = event.ship.mmsi.toString(),
        anchorAreas = listOf(),
        portAreas = listOf(
            PortAreaVisit(
                portId = unlocode,
                startEventId = event._id,
                startTime = event.actualTime.atZone(ZoneOffset.UTC),
                startLocation = event.location.toVesselVoyageLocation(),
                startDraught = event.draught?.toDouble(),
                endEventId = null,
                endTime = null,
                endLocation = null,
                endDraught = null
            )
        ),
        berthAreas = listOf(),
        dest = null,
        eta = null,
        esof = null,
        passThroughAreas = null,
        finished = false,
        previousEntryId = null,
        nextEntryId = null
    )

    return VisitShipStatus(
        visit = visit,
        previousVoyage = null,
        previousVisit = null
    )
}

// TODO: unit test
fun startVisit(event: AreaEvent, voyageShipStatus: VoyageShipStatus, findShipTrace: FindShipTrace, imo: Int, unlocode: String): VisitShipStatus {
    val (currentVoyage, previousVisit) = voyageShipStatus

    val newVisit = Visit(
        _id = createVisitId(event._id),
        imo = imo.toString(),
        mmsi = event.ship.mmsi.toString(),
        anchorAreas = listOf(),
        portAreas = listOf(
            PortAreaVisit(
                portId = unlocode,
                startEventId = event._id,
                startTime = event.actualTime.atZone(ZoneOffset.UTC),
                startLocation = event.location.toVesselVoyageLocation(),
                startDraught = event.draught?.toDouble(),
                endEventId = null,
                endTime = null,
                endLocation = null,
                endDraught = null
            )
        ),
        berthAreas = listOf(),
        dest = getDestinationIfStillValid(currentVoyage, event),
        eta = getEtaIfNotOutdated(currentVoyage),
        esof = null, // will be filled later if needed when regrouping
        passThroughAreas = null,
        finished = false,
        previousEntryId = currentVoyage._id,
        nextEntryId = null
    )

    val finishedVoyage = currentVoyage.copy(
        endPortIds = newVisit.portAreas.map { it.portId },
        endTime = newVisit.startTime,
        finished = true,
        esof = currentVoyage.esof?.copy(
            slowMovingPeriods = detectSlowMovingPeriods(
                findShipTrace(currentVoyage.imo, currentVoyage.startTime, newVisit.startTime)
            )
        ),
        nextEntryId = newVisit._id
    )

    return VisitShipStatus(
        visit = newVisit,
        previousVoyage = finishedVoyage,
        previousVisit = previousVisit
    ).regroupESof()
}

// TODO: unit test
fun startInitialVisit(event: AnchoredEvent, destinations: Set<String>, imo: Int): VisitShipStatus {
    val visit = Visit(
        _id = createVisitId(event._id),
        imo = imo.toString(),
        mmsi = event.ship.mmsi.toString(),
        anchorAreas = listOf(
            AnchorAreaVisit(
                anchorAreaId = event.area.name ?: throw ProcessEventException("No area id available"),
                startEventId = event._id,
                startTime = event.actualTime.atZone(ZoneOffset.UTC) ?: throw ProcessEventException("No startTime available"),
                destinations = destinations,
                endEventId = null,
                endTime = null
            )
        ),
        portAreas = listOf(),
        berthAreas = listOf(),
        dest = null,
        eta = null,
        esof = null,
        passThroughAreas = null,
        finished = false,
        previousEntryId = null,
        nextEntryId = null
    )

    return VisitShipStatus(
        visit = visit,
        previousVoyage = null,
        previousVisit = null
    )
}

// TODO: unit test
fun startVisit(
    event: AnchoredEvent,
    voyageShipStatus: VoyageShipStatus,
    destinations: Set<String>,
    imo: Int
): VisitShipStatus {
    val (currentVoyage, previousVisit) = voyageShipStatus

    val newVisit = Visit(
        _id = createVisitId(event._id),
        imo = imo.toString(),
        mmsi = event.ship.mmsi.toString(),
        anchorAreas = listOf(
            AnchorAreaVisit(
                anchorAreaId = event.area.name ?: throw ProcessEventException("No area id found"),
                startEventId = event._id,
                startTime = event.actualTime.atZone(ZoneOffset.UTC) ?: throw ProcessEventException("No startTime available"),
                destinations = destinations,
                endEventId = null,
                endTime = null
            )
        ),
        portAreas = listOf(),
        berthAreas = listOf(),
        dest = currentVoyage.dest,
        eta = getEtaIfNotOutdated(currentVoyage),
        esof = null, // will be filled later if needed when regrouping
        passThroughAreas = null,
        finished = false,
        previousEntryId = currentVoyage._id,
        nextEntryId = null
    )

    val finishedVoyage = currentVoyage.copy(
        endPortIds = null, // endPorts will be filled in later on when the first port start event comes in
        endTime = newVisit.startTime,
        finished = true,
        nextEntryId = newVisit._id
    )

    return VisitShipStatus(
        visit = newVisit,
        previousVoyage = finishedVoyage,
        previousVisit = previousVisit
    ).regroupESof()
}

fun startInitialVoyage(event: AreaEvent, imo: Int, unlocode: String): VoyageShipStatus {
    val voyage = Voyage(
        _id = createVoyageId(event._id),
        imo = imo.toString(),
        mmsi = event.ship.mmsi.toString(),

        startPortIds = listOf(unlocode),
        startTime = event.actualTime.atZone(ZoneOffset.UTC),

        dest = null,
        eta = null,
        esof = null,
        nonMatchingAnchorAreas = null,
        passThroughAreas = null,

        endPortIds = null,
        endTime = null,

        finished = false,
        previousEntryId = null,
        nextEntryId = null
    )

    return VoyageShipStatus(
        voyage = voyage,
        previousVisit = null,
        previousVoyage = null
    )
}

// TODO: unit test
fun startVoyage(
    visitShipStatus: VisitShipStatus,
    findAnchorage: FindAnchorage
): VoyageShipStatus {
    val (visit) = visitShipStatus

    val (matchingAnchorAreas, nonMatchingAnchorAreas) =
        partitionMatchingAnchorAreas(visit.imo, visit.anchorAreas, visit.portAreas, findAnchorage)

    // use the id of the latest event, check both (final) anchor and port areas
    val eventId = (matchingAnchorAreas + visit.portAreas)
        .maxByOrNull { it.endTime ?: it.startTime }
        ?.let { it.endEventId ?: it.startEventId }
        ?: throw ProcessEventException("No endEventId found") // cannot happen in practice

    val newVoyageId = createVoyageId(eventId)

    val finishedVisit = visit.copy(
        anchorAreas = matchingAnchorAreas,
        finished = true,
        nextEntryId = newVoyageId
    )

    val updatedPreviousVoyage = updatePreviousVoyageEndInformation(visitShipStatus.previousVoyage, finishedVisit)
        ?.appendAnchorAreas(nonMatchingAnchorAreas)

    val newVoyage = Voyage(
        _id = newVoyageId,
        imo = finishedVisit.imo,
        mmsi = finishedVisit.mmsi,

        startPortIds = finishedVisit.portAreas.map { it.portId },
        startTime = finishedVisit.endTime
            ?: throw ProcessEventException(
                "Cannot create voyage: preceding visit has no endTime " +
                    "(imo: " + finishedVisit.imo + ")"
            ),

        dest = visit.dest,
        eta = getEtaIfNotOutdated(visit),
        esof = null, // will be filled later if needed when regrouping
        nonMatchingAnchorAreas = null,
        passThroughAreas = null,

        endPortIds = null,
        endTime = null,

        finished = false,
        previousEntryId = finishedVisit._id,
        nextEntryId = null
    )

    return VoyageShipStatus(
        voyage = newVoyage,
        previousVisit = finishedVisit,
        previousVoyage = updatedPreviousVoyage
    ).regroupESof()
}

fun unfinishVoyage(voyage: Voyage, deletedVisit: Visit): Voyage {
    return voyage
        .copy(
            endPortIds = null,
            endTime = null,
            dest = deletedVisit.dest,
            eta = deletedVisit.eta,
            esof = mergeESofOrNull(
                deletedVisit.esof?.copy(slowMovingPeriods = null),
                voyage.esof?.copy(slowMovingPeriods = null)
            ),
            finished = false,
            nextEntryId = null
        )
        .appendAnchorAreas(deletedVisit.anchorAreas)
        .appendPassThroughAreas(deletedVisit.portAreas + (deletedVisit.passThroughAreas ?: emptyList()))
}

data class PartitionedMatchingAnchorAreas(
    val matchingAnchorAreas: List<AnchorAreaVisit>,
    val nonMatchingAnchorAreas: List<AnchorAreaVisit>
)

// Partition anchor areas of which the related port ids do not match with any of
// the port id's of the visit. This must be done only when the visit has finished,
// else it is still possible that new matching port areas will be added.
private fun partitionMatchingAnchorAreas(
    imo: String,
    anchorAreas: List<AnchorAreaVisit>,
    portAreas: List<PortAreaVisit>,
    findAnchorage: FindAnchorage
): PartitionedMatchingAnchorAreas {
    val visitPortIds = portAreas.map { it.portId }.toSet()
    val visitLocation = portAreas.first().startLocation

    val (matchingAnchorAreas, nonMatchingAnchorAreas) = anchorAreas.partition { anchorAreaVisit ->
        // note that the visit location is not exactly the anchorage,
        // but the location is only used to find the anchorage *near* it so this is good enough
        val anchorage: Anchorage? = findAnchorage(anchorAreaVisit.anchorAreaId, visitLocation)

        anchorage?.ports
            ?.any { visitPortIds.contains(it) }
            ?.also {
                if (!it) {
                    log.debug {
                        "Removing anchor area visit '${anchorAreaVisit.anchorAreaId}' from visit " +
                            "because it has no related ports matching with any of the port areas of the visit " +
                            "(imo: $imo, visit: $visitPortIds, anchor area: ${anchorage.ports}) "
                    }
                }
            }
            ?: true // keep it in case of unknown anchorage (be conservative in this regard)
    }

    return PartitionedMatchingAnchorAreas(
        matchingAnchorAreas,
        nonMatchingAnchorAreas
    )
}

/**
 * Finds the last matching eventPair in a list with eventPairs:
 * - first, tries to match by id (related event id)
 * - else, tries to match by properties like matching portId or berthId
 * - then, if found, the match must not have endEventId defined
 * - if there is a match, the eventPair is replaced with the result of the `update` function.
 */
fun <T : EventPair> replaceLastMatchingEventPair(
    eventPairs: List<T>,
    matchById: (eventPair: T) -> Boolean,
    matchByProps: (eventPair: T) -> Boolean,
    replace: (eventPair: T) -> T
): List<T> {
    val matchingEventPair = eventPairs
        .findLast { matchById(it) }
        ?: eventPairs.findLast { matchByProps(it) }
            ?.let {
                // do not override when endEventId is already filled in
                if (it.endEventId != null) {
                    null
                } else {
                    it
                }
            }

    return if (matchingEventPair != null) {
        eventPairs.map { item ->
            if (item == matchingEventPair) {
                replace(item)
            } else {
                item
            }
        }
    } else {
        return eventPairs
    }
}

fun updateMatchingPortAreaVisit(
    portAreas: List<PortAreaVisit>,
    endEvent: AreaEvent,
    unlocode: String
): List<PortAreaVisit> {
    return replaceLastMatchingEventPair(
        eventPairs = portAreas,
        matchById = { it.startEventId == endEvent.relatedEvent() },
        matchByProps = { it.portId == unlocode },
        replace = {
            it.copy(
                endEventId = endEvent._id,
                endTime = endEvent.actualTime.atZone(ZoneOffset.UTC),
                endLocation = endEvent.location.toVesselVoyageLocation(),
                endDraught = endEvent.draught?.toDouble()
            )
        }
    )
}

fun AreaEvent.relatedEvent() = (this as? AreaEndEvent)?.startEventId

fun matchingBerth(berthAreaVisit: BerthAreaVisit, unlocode: String, berthId: String): Boolean {
    return berthAreaVisit.portId == unlocode &&
        berthAreaVisit.berthId == berthId
}

fun updateMatchingBerthAreaVisit(
    berthAreas: List<BerthAreaVisit>,
    endEvent: UniqueBerthEvent,
    unlocode: String,
    berthId: String
): List<BerthAreaVisit> {
    return replaceLastMatchingEventPair(
        eventPairs = berthAreas,
        matchById = { it.startEventId == endEvent.relatedEvent() },
        matchByProps = { matchingBerth(it, unlocode, berthId) },
        replace = {
            it.copy(
                endEventId = endEvent._id,
                endTime = endEvent.actualTime.atZone(ZoneOffset.UTC),
                endLocation = endEvent.location.toVesselVoyageLocation(),
                endDraught = endEvent.draught?.toDouble()
            )
        }
    )
}

fun updateMatchingAnchorAreaVisit(
    anchorAreas: List<AnchorAreaVisit>,
    endEvent: AnchoredEvent
): List<AnchorAreaVisit> {
    return replaceLastMatchingEventPair(
        eventPairs = anchorAreas,
        matchById = { it.startEventId == endEvent.relatedEvent() },
        matchByProps = { it.anchorAreaId == (endEvent.area.name ?: "") },
        replace = {
            it.copy(
                endEventId = endEvent._id,
                endTime = endEvent.actualTime.atZone(ZoneOffset.UTC)
            )
        }
    )
}

fun isPassThrough(area: PortAreaVisit, stops: List<Stop>?, config: EventProcessingProperties): Boolean {
    // it IS NOT a pass through when the area visit is not yet ended
    val areaEndLocation = area.endLocation
    val areaEndTime = area.endTime
    if (areaEndTime == null || areaEndLocation == null) {
        return false
    }

    // it IS NOT a pass through when the ship had a classified stop (berth or anchor) during the area visit
    // we don't rule out based on unclassified stops: those can be passing through a lock for example
    val stopDuringAreaVisit = stops?.find { stop ->
        val stopEndTime = stop.endTime ?: stop.startTime
        stop.startTime.isBefore(areaEndTime) && stopEndTime.isAfter(area.startTime)
    }
    if (stopDuringAreaVisit != null && stopDuringAreaVisit.type != StopType.UNCLASSIFIED) {
        return false
    }

    // it IS a pass-through when the duration is too short
    val duration = Duration.between(area.startTime, areaEndTime)
    if (duration < config.minDuration) {
        return true
    }

    // it IS a pass-through when the average speed is too high
    val distance = haversineDistance(area.startLocation, areaEndLocation)
    val avgSpeedMps = distance / duration.toSeconds()
    return avgSpeedMps > config.maxSpeedMps
}

/**
 * When we return a destination:
 * - We entered a port, but it isn't the destination we provided.
 *   All we can do at this point is assume the following port will have our current destination.
 *
 * When null:
 * - No destination was provided in the first place.
 * - The true destination was equal to the arrived port.
 *
 * @return The [Destination] if it is still relevant when we received a [PortEvent].
 */
fun getDestinationIfStillValid(entry: Entry, portEvent: AreaEvent): Destination? {
    return entry.dest?.takeUnless { dest ->
        // We entered the port, destination can be dropped
        dest.trueDestination == portEvent.area.unlocode
    }
}

/**
 * When we return a destination:
 * - We anchored on an anchor area of a port that isn't our destination.
 *   Assume we will continue our journey to the actual port.
 * - The provided set of [anchorAreaDestinations] is empty.
 *
 * When null:
 * - No destination was provided in the first place.
 * - The true destination was equal to the destinations of the anchorage.
 *
 * @return The [Destination] if it is still relevant when we received a [AnchorEvent].
 */
fun getDestinationIfStillValid(entry: Entry, anchorAreaDestinations: Set<String>): Destination? {
    // Return the destination when the provided anchor area destinations is empty
    if (anchorAreaDestinations.isEmpty()) {
        return entry.dest
    }

    return entry.dest?.takeUnless { dest ->
        // Drop the destination if we anchored in the anchor area for our destination port
        anchorAreaDestinations.any { it == dest.trueDestination }
    }
}

/**
 * Get the eta when it is updated within the duration of the entry.
 * We consider it outdated when it was updated before the entry
 */
fun getEtaIfNotOutdated(entry: Entry): Eta? {
    return entry.eta?.takeIf { eta ->
        (eta.predictedAt.isAfter(entry.startTime) || eta.predictedAt.isEqual(entry.startTime))
    }
}

// when one of the port areas was removed (being a pass through),
// we need to update the end port(s) and end time of the previousVoyage accordingly
fun updatePreviousVoyageEndInformation(shipStatus: VisitShipStatus): VisitShipStatus {
    return shipStatus.copy(
        previousVoyage = updatePreviousVoyageEndInformation(shipStatus.previousVoyage, shipStatus.visit)
    )
}

// when one of the port areas was removed (being a pass through),
// we need to update the end port(s) and end time of the previousVoyage accordingly
private fun updatePreviousVoyageEndInformation(previousVoyage: Voyage?, successiveVisit: Visit?): Voyage? {
    if (previousVoyage == null || successiveVisit == null) {
        return previousVoyage
    }

    return previousVoyage.copy(
        endPortIds = if (successiveVisit.portAreas.isNotEmpty()) {
            successiveVisit.portAreas.map { it.portId }
        } else {
            null
        },
        endTime = successiveVisit.startTime
    )
}

fun Voyage.appendAnchorAreas(newAnchorAreas: List<AnchorAreaVisit>): Voyage {
    if (newAnchorAreas.isEmpty()) {
        return this
    }

    return this.copy(
        nonMatchingAnchorAreas = ((this.nonMatchingAnchorAreas ?: emptyList()) + newAnchorAreas)
            .sortedBy { it.startTime }
            .let {
                if (it.size > MAX_NON_MATCHING_ANCHOR_AREAS) {
                    log.warn {
                        "Cannot append non-matching anchor area to voyage: maximum exceeded " +
                            "(imo: ${this.imo}, area count: ${it.size})"
                    }

                    it.take(MAX_NON_MATCHING_ANCHOR_AREAS)
                } else {
                    it
                }
            }
    )
}

fun Voyage.appendPassThroughAreas(newPassThroughAreas: List<PortAreaVisit>): Voyage {
    if (newPassThroughAreas.isEmpty()) {
        return this
    }

    return this.copy(
        passThroughAreas = mergePassThroughAreas(this.passThroughAreas, newPassThroughAreas, this.imo)
    )
}

fun Visit.appendPassThroughAreas(newPassThroughAreas: List<PortAreaVisit>): Visit {
    if (newPassThroughAreas.isEmpty()) {
        return this
    }

    return this.copy(
        passThroughAreas = mergePassThroughAreas(this.passThroughAreas, newPassThroughAreas, this.imo)
    )
}

// merge, sort, and limit two lists with passThroughAreas
private fun mergePassThroughAreas(a: List<PortAreaVisit>?, b: List<PortAreaVisit>?, imo: String): List<PortAreaVisit> {
    return ((a ?: emptyList()) + (b ?: emptyList()))
        .sortedBy { it.startTime }
        .let {
            if (it.size > MAX_PASS_THROUGH_AREAS) {
                log.debug {
                    "Cannot append pass-through area to voyage: maximum exceeded " +
                        "(imo: $imo, area count: ${it.size})"
                }

                it.take(MAX_PASS_THROUGH_AREAS)
            } else {
                it
            }
        }
}

/**
 * Test whether an event is part of a visit. This is the case when the port of the event
 * is the same or overlapping with any of the current visit
 */
fun eventIsPartOfVisit(visit: Visit, findPortById: FindPortById, unlocode: String): Boolean {
    // cheap check: equal portId
    if (visit.portAreas.any { it.portId == unlocode }) {
        return true
    }

    if (visit.anchorAreas.isNotEmpty() && visit.portAreas.isEmpty()) {
        // TODO: check whether the portId of the event corresponds with the anchorArea. Log a warning if not
        return true
    }

    // check port areas geographically
    return visit.portAreas.any { area ->
        val areaPort = findPortById(area.portId)
        val eventPort = findPortById(unlocode)

        if (areaPort != null && eventPort != null) {
            val outerAreaPort = areaPort.getNonEmptyOuterArea()
            val outerAreaEvent = eventPort.getNonEmptyOuterArea()

            if (outerAreaPort != null && outerAreaEvent != null) {
                // Check whether the outer areas overlap. Important is to take the outer areas, because those are
                // used to trigger port exit events.
                overlapping(outerAreaPort, outerAreaEvent)
            } else {
                // Fallback: check whether any of the port areas are "nearby enough" to be able to overlap
                val areaPortLocation = areaPort.location.toVesselVoyageLocation()
                val eventPortLocation = eventPort.location.toVesselVoyageLocation()
                haversineDistance(areaPortLocation, eventPortLocation) < MAX_OVERLAPPING_PORT_DISTANCE_METERS
            }
        } else {
            false
        }
    }
}

fun createNewStop(event: StopEvent, getStopClassification: (Location) -> ClassifiedStop): NewStop {
    val stopClassification = getStopClassification(event.location)

    return NewStop(
        startEventId = event._id,
        endEventId = null,
        location = null,
        start = LocationTime(
            location = event.location,
            time = event.actualTime
        ),
        end = null,
        type = stopClassification.type,
        areaId = stopClassification.areaId,
        accuracy = null
    )
}

fun createNewVisit(
    event: AreaEvent,
    currentVoyage: NewVoyage?,
    visitStart: LocationTime,
    updateTime: Instant,
    imo: Int,
    areaId: String
): NewVisit {
    val entryId = "${event._id}.VISIT"

    return NewVisit(
        _id = entryId,
        imo = imo,
        start = visitStart,
        end = null,
        stops = emptyList(),
        destination = currentVoyage?.destination,
        previous = currentVoyage?._id,
        next = null,
        eospAreaActivity = AreaActivity(
            id = event._id,
            start = visitStart,
            end = null,
            areaId = areaId
        ),
        updatedAt = updateTime
    )
}

fun createNewVoyage(
    event: AreaEvent,
    currentVisit: NewVisit,
    voyageStart: LocationTime,
    updateTime: Instant,
    imo: Int,
    areaId: String
): NewVoyage {
    val entryId = "${event._id}.VOYAGE"

    return NewVoyage(
        _id = entryId,
        imo = imo,
        start = voyageStart,
        actualStart = voyageStart,
        end = null,
        stops = emptyList(),
        destination = currentVisit.destination,
        previous = currentVisit._id,
        next = null,
        originPort = areaId,
        destinationPort = null,
        updatedAt = updateTime
    )
}

fun resumeVisit(
    currentVoyage: NewVoyage,
    previousVisit: NewVisit,
    updateTime: Instant
): NewVisit {
    val resumedEospAreaActivity = previousVisit.eospAreaActivity.copy(
        end = null
    )

    return previousVisit.copy(
        eospAreaActivity = resumedEospAreaActivity,
        stops = mergeStops(
            previousStops = previousVisit.stops,
            currentStops = currentVoyage.stops
        ),
        next = null,
        end = null,
        updatedAt = updateTime
    )
}

fun resumeVoyage(
    currentVisit: NewVisit,
    previousVoyage: NewVoyage,
    passThroughEndTime: LocationTime,
    updateTime: Instant
): NewVoyage {
    val visitPassThrough = AreaActivity(
        id = currentVisit.eospAreaActivity.id,
        start = currentVisit.eospAreaActivity.start,
        end = passThroughEndTime,
        areaId = currentVisit.eospAreaActivity.areaId
    )
    val visitPassThroughDuration = visitPassThrough.asDuration()

    // Only add the visit pass-through when it took at least 1 second to avoid clashing of EOSP events that got generated with the same AIS point
    val mergedPassThrough = if (visitPassThroughDuration != null && visitPassThroughDuration > Duration.ZERO) {
        previousVoyage.passThroughEosp + currentVisit.passThroughEosp + visitPassThrough
    } else {
        previousVoyage.passThroughEosp + currentVisit.passThroughEosp
    }

    return previousVoyage.copy(
        end = null,
        next = null,
        destinationPort = null,
        stops = mergeStops(
            previousStops = previousVoyage.stops,
            currentStops = currentVisit.stops
        ),
        passThroughEosp = mergedPassThrough,
        destination = currentVisit.destination,
        updatedAt = updateTime
    )
}

/**
 * Merge two lists of stops into a single one, merging previously split up stops back into a single stop using the start event id as the key.
 */
fun mergeStops(previousStops: List<NewStop>, currentStops: List<NewStop>): List<NewStop> {
    return (previousStops + currentStops)
        // Ensure that the stops are sorted by start time, so we can take the correct start and end times
        .sortedBy { stop -> stop.start.time }
        .groupBy { stop -> stop.startEventId }
        .map { (_, stops) ->
            // Because we are sorted by start time we can safely assume that the first stop is the start of the stop
            val firstStop = stops.first()
            val lastStop = stops.last()

            firstStop.copy(
                endEventId = lastStop.endEventId,
                end = lastStop.end
            )
        }
}

fun finishVisit(
    currentVisit: NewVisit,
    newVoyage: NewVoyage,
    voyageStart: LocationTime,
    updateTime: Instant,
    portAreaActivitiesToRemove: Set<AreaActivity> = emptySet()
): NewVisit {
    val leftOverPortAreaActivities = currentVisit.portAreaActivities - portAreaActivitiesToRemove
    val fallbackEndLocationTime = voyageStart.copy(fallback = FallbackType.ACTIVITY_END_BY_EOSP)
    return currentVisit.copy(
        next = newVoyage._id,
        end = voyageStart,
        eospAreaActivity = currentVisit.eospAreaActivity.copy(
            end = voyageStart
        ),
        otherOngoingEospAreaActivities = emptyList(),
        stops = currentVisit.stops.map { stop -> stop.copy(end = stop.end ?: fallbackEndLocationTime) },
        portAreaActivities = leftOverPortAreaActivities.finishActivitiesIfEndMissing(fallbackEndLocationTime),
        anchorAreaActivities = currentVisit.anchorAreaActivities.finishActivitiesIfEndMissing(fallbackEndLocationTime),
        berthAreaActivities = currentVisit.berthAreaActivities.finishActivitiesIfEndMissing(fallbackEndLocationTime),
        pilotAreaActivities = currentVisit.pilotAreaActivities.finishActivitiesIfEndMissing(fallbackEndLocationTime),
        anchorAreaAreaActivities = currentVisit.anchorAreaAreaActivities.finishActivitiesIfEndMissing(fallbackEndLocationTime),
        terminalMooringAreaActivities = currentVisit.terminalMooringAreaActivities.finishActivitiesIfEndMissing(fallbackEndLocationTime),
        lockAreaActivities = currentVisit.lockAreaActivities.finishActivitiesIfEndMissing(fallbackEndLocationTime),
        approachAreaActivities = currentVisit.approachAreaActivities.finishActivitiesIfEndMissing(fallbackEndLocationTime),
        updatedAt = updateTime
    )
}

/**
 * Correct the [finishedVisit] when we create a zero-second Voyage as otherwise the end time of eosp is not aligned with said voyage.
 * Next to this, all end times we used the eosp end time of should be corrected as well to match the [zeroSecondVoyageStart].
 *
 * @param finishedVisit The [NewVisit] we are currently finished.
 * @param zeroSecondVoyageStart The start time of the zero-second voyage we just created.
 */
fun correctFinishedVisitToZeroSecondVoyageStart(
    finishedVisit: NewVisit,
    zeroSecondVoyageStart: LocationTime,
    updateTime: Instant
): NewVisit {
    val newFallbackEndLocationTime = zeroSecondVoyageStart.copy(fallback = FallbackType.ACTIVITY_END_BY_EOSP)

    return finishedVisit.copy(
        eospAreaActivity = finishedVisit.eospAreaActivity.copy(end = zeroSecondVoyageStart),
        end = zeroSecondVoyageStart,

        stops = finishedVisit.stops.map { stop ->
            if (stop.end?.fallback == FallbackType.ACTIVITY_END_BY_EOSP) {
                stop.copy(end = newFallbackEndLocationTime)
            } else {
                stop
            }
        },
        portAreaActivities = finishedVisit.portAreaActivities.overrideEndFallbacks(newFallbackEndLocationTime, FallbackType.ACTIVITY_END_BY_EOSP),
        anchorAreaActivities = finishedVisit.anchorAreaActivities.overrideEndFallbacks(newFallbackEndLocationTime, FallbackType.ACTIVITY_END_BY_EOSP),
        berthAreaActivities = finishedVisit.berthAreaActivities.overrideEndFallbacks(newFallbackEndLocationTime, FallbackType.ACTIVITY_END_BY_EOSP),
        pilotAreaActivities = finishedVisit.pilotAreaActivities.overrideEndFallbacks(newFallbackEndLocationTime, FallbackType.ACTIVITY_END_BY_EOSP),
        anchorAreaAreaActivities = finishedVisit.anchorAreaAreaActivities.overrideEndFallbacks(newFallbackEndLocationTime, FallbackType.ACTIVITY_END_BY_EOSP),
        terminalMooringAreaActivities = finishedVisit.terminalMooringAreaActivities.overrideEndFallbacks(newFallbackEndLocationTime, FallbackType.ACTIVITY_END_BY_EOSP),
        lockAreaActivities = finishedVisit.lockAreaActivities.overrideEndFallbacks(newFallbackEndLocationTime, FallbackType.ACTIVITY_END_BY_EOSP),
        approachAreaActivities = finishedVisit.approachAreaActivities.overrideEndFallbacks(newFallbackEndLocationTime, FallbackType.ACTIVITY_END_BY_EOSP),
        updatedAt = updateTime
    )
}

fun finishVoyage(
    currentVoyage: NewVoyage,
    newVisit: NewVisit,
    visitStart: LocationTime,
    addedPassThrough: List<AreaActivity> = emptyList(),
    updateTime: Instant
): NewVoyage {
    val mergedPassThrough = currentVoyage.passThroughEosp + addedPassThrough

    return currentVoyage.copy(
        passThroughEosp = mergedPassThrough,
        next = newVisit._id,
        end = visitStart,
        destinationPort = newVisit.eospAreaActivity.areaId,
        updatedAt = updateTime
    )
}

fun NewVisit.isPassThrough(): Boolean {
    return this.stops.isEmpty()
}

fun List<NewEntry>.sortedByStartAndEndTime(): List<NewEntry> = sortedBy { entry ->
    val startTime = entry.start.time
    val endTime = entry.end?.time
        // Set the default time to now as otherwise ongoing entries don't get sorted correctly with 0-second voyages
        ?: Instant.now()

    // Sort by the middle of the visit/voyage time as 0-second voyages can have the same start time as visits that follow
    val duration = Duration.between(startTime, endTime)
        .dividedBy(2)
    startTime + duration
}

/**
 * Filters the list of items by the given categories, and optionally TEU (for CONTAINER category) or DWT (for other
 * categories). Filtering on TEU/DWT always requires a category to be set. A TEU/DWT filter can be partial, the
 * system allows to only set the min or max.
 *
 * [minTeu]-[maxTeu] is only applied to [ShipCategoryV2.CONTAINER],
 * [minDwt]-[maxDwt] is applied to all other categories
 *
 * @param shipByImoResolver Resolves the ship by its IMO
 * @param requestedCategoriesV2 The resulting list will only contain entries where the ship has one of these categories
 * @param minDwt Minimum DWT of the ship. When [maxDwt] is set, but [minDwt] not, it becomes 0.
 * @param maxDwt Maximum DWT of the ship. When [minDwt] is set, but [maxDwt] not, it becomes Int.MAX_VALUE.
 * @param minTeu Minimum TEU of the ship. When [maxTeu] is set, but [minTeu] not, it becomes 0.
 * @param maxDwt Maximum TEU of the ship. When [minTeu] is set, but [maxTeu] not, it becomes Int.MAX_VALUE.
 */
fun shipMatchesCategoryAndOptionalRange(
    ship: ShipRegisterInfoCache,
    request: ShipPropertyFilterRequest,
): Boolean {

    val requestedCategoriesV2 = request.categories
    if (requestedCategoriesV2.isNullOrEmpty()) {
        return false
    }

    val dwtRange = rangeOrNull(request.minDwt, request.maxDwt)
    val teuRange = rangeOrNull(request.minTeu, request.maxTeu)

    // ship must match the category, and optionally the dwt/teu range
    val shipCategory = ship.categories.v2
    val categoryMatches = shipCategory != null && shipCategory in requestedCategoriesV2
    return when {
        shipCategory == CONTAINER && teuRange != null -> categoryMatches && ship.teu() in teuRange
        shipCategory != CONTAINER && dwtRange != null -> categoryMatches && ship.dwt() in dwtRange
        else -> categoryMatches
    }
}

fun validateDwtTeu(
    request: ShipPropertyFilterRequest
) {
    val minDwt = request.minDwt
    val maxDwt = request.maxDwt
    val dwtProvided = minDwt != null || maxDwt != null

    val minTeu = request.minTeu
    val maxTeu = request.maxTeu
    val teuProvided = minTeu != null || maxTeu != null

    if (dwtProvided && teuProvided) {
        throw BadRequestException("DWT and TEU filter cannot be applied simultaneously")
    }

    if (minDwt != null && maxDwt != null && maxDwt < minDwt) {
        throw BadRequestException("maxDwt must be bigger than minDwt")
    }
    if (minTeu != null && maxTeu != null && maxTeu < minTeu) {
        throw BadRequestException("maxTeu must be bigger than minTeu")
    }

    val categories = request.categories
        ?: return

    if (CONTAINER in categories) {
        if (categories.size > 1 && teuProvided) {
            throw BadRequestException("TEU filter can only be combined ship category CONTAINER")
        } else if (dwtProvided) {
            throw BadRequestException("Cannot combine DWT filter with ship category CONTAINER. Use TEU filter.")
        }
    } else if (CONTAINER !in categories && teuProvided) {
        throw BadRequestException("TEU filter can only be combined ship category CONTAINER")
    }
}

private fun ShipRegisterInfoCache.dwt(): Int? {
    return this.specification.deadWeightTonnage?.toInt()
}
private fun ShipRegisterInfoCache.teu(): Int? {
    return this.specification.twentyFootEquivalentUnit?.toInt()
        ?: this.calculated.twentyFootEquivalentUnit?.toInt()
}

private fun rangeOrNull(
    min: Int?,
    max: Int?,
): IntRange? {
    return if (min != null || max != null) {
        (min ?: 0)..(max ?: Int.MAX_VALUE)
    } else {
        null
    }
}
