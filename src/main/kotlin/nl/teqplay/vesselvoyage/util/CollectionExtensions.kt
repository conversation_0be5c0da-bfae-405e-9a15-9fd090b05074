package nl.teqplay.vesselvoyage.util

/**
 * Loops over the list like a regular map operation, giving a window with the previous and next item (when not out of
 * bounds). Alternative for [List.windowed], where this method does not create intermediate lists.
 *
 * @param operation The mapping operation. Note that [previous] is from the original list, not the mapped item!
 */
fun <T, R> List<T>.mapWithSurrounding(operation: (previous: T?, current: T, next: T?) -> R): List<R> {
    if (size == 0) return emptyList()
    if (size == 1) return listOf(operation(null, first(), null))

    return mapIndexed { index, item -> operation(getOrNull(index - 1), item, getOrNull(index + 1)) }
}
