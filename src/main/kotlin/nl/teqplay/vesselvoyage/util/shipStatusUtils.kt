package nl.teqplay.vesselvoyage.util

import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.Change
import nl.teqplay.vesselvoyage.model.Entry
import nl.teqplay.vesselvoyage.model.InitialShipStatus
import nl.teqplay.vesselvoyage.model.ShipStatus
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.Voyage
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.EntryESoFWrapper
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewESoF
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewInitialShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.NewVisitShipStatus
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import nl.teqplay.vesselvoyage.model.v2.NewVoyageShipStatus

fun NewShipStatus.getShipImo(): Int? {
    return when (this) {
        is NewVisitShipStatus -> { this.visit.entry.imo }
        is NewVoyageShipStatus -> { this.voyage.entry.imo }
        is NewInitialShipStatus -> null
    }
}

fun NewShipStatus.getEndOrStart(): LocationTime? {
    return when (this) {
        is NewVisitShipStatus -> { this.visit.entry.end ?: this.visit.entry.start }
        is NewVoyageShipStatus -> { this.voyage.entry.end ?: this.voyage.entry.start }
        is NewInitialShipStatus -> null
    }
}

fun NewShipStatus.getEntryId(): String? {
    return when (this) {
        is NewVisitShipStatus -> { this.visit.entry._id }
        is NewVoyageShipStatus -> { this.voyage.entry._id }
        is NewInitialShipStatus -> null
    }
}

fun NewShipStatus.getPreviousEntryId(): String? {
    return getPreviousEntry()?._id
}

fun NewShipStatus.getPreviousEntry(): NewEntry? {
    return when (this) {
        is NewVisitShipStatus -> { this.previousVoyage?.entry }
        is NewVoyageShipStatus -> { this.previousVisit?.entry }
        is NewInitialShipStatus -> null
    }
}

/**
 * Helper function to update the current [NewVisitShipStatus.visit] entry and potentially the [NewESoF] as well if not null.
 */
fun NewVisitShipStatus.updateCurrentVisit(
    updatedVisit: NewVisit,
    updatedESoF: NewESoF? = null,
): NewVisitShipStatus {
    // Take the updated esof only if we have an update, otherwise fallback to what we currently know
    val newESoF = updatedESoF ?: this.visit.esof

    val newWrapper = EntryESoFWrapper(
        entry = updatedVisit,
        esof = newESoF
    )

    return this.copy(visit = newWrapper)
}

/**
 * Helper function to update the current [NewVoyageShipStatus.voyage] entry and potentially the [NewESoF] as well if not null.
 */
fun NewVoyageShipStatus.updateCurrentVoyage(
    updatedVoyage: NewVoyage,
    updatedESoF: NewESoF? = null,
): NewVoyageShipStatus {
    // Take the updated esof only if we have an update, otherwise fallback to what we currently know
    val newESoF = updatedESoF ?: this.voyage.esof

    val newWrapper = EntryESoFWrapper(
        entry = updatedVoyage,
        esof = newESoF
    )

    return this.copy(voyage = newWrapper)
}

fun ShipStatus.getCurrentEntry(): Entry? {
    return when (this) {
        is VisitShipStatus -> this.visit
        is VoyageShipStatus -> this.voyage
        is InitialShipStatus -> null
    }
}

/**
 * Get all entries ordered newest first, oldest last
 */
fun ShipStatus.getAllEntries(): List<Entry> {
    return when (this) {
        is VisitShipStatus -> listOfNotNull(this.visit, this.previousVoyage, this.previousVisit)
        is VoyageShipStatus -> listOfNotNull(this.voyage, this.previousVisit, this.previousVoyage)
        is InitialShipStatus -> emptyList()
    }
}

fun ShipStatus.updateEntryOrThrow(
    updateVisit: (visit: Visit) -> Visit,
    updateVoyage: (voyage: Voyage) -> Voyage,
): ShipStatus {
    return when (this) {
        is VisitShipStatus -> {
            val updatedVisit = updateVisit(this.visit)
            this.copy(visit = updatedVisit)
        }

        is VoyageShipStatus -> {
            val updatedVoyage = updateVoyage(this.voyage)
            this.copy(voyage = updatedVoyage)
        }

        is InitialShipStatus -> {
            throw ProcessEventException("Cannot update entry: initial ship status does not have an entry")
        }
    }
}

/**
 * Get the list with creates and updates
 * WARNING: this function will not detect deletions
 */
fun createChangeList(previousShipStatus: ShipStatus?, newShipStatus: ShipStatus): List<Change> {
    return createChangeList(previousShipStatus, listOf(newShipStatus))
}

/**
 * Get the list with creates and updates, sorted by startTime
 * WARNING: this function will not detect deletions
 */
fun createChangeList(previousShipStatus: ShipStatus?, newShipStatuses: List<ShipStatus>): List<Change> {
    val initialEntries: Map<String, Entry> = previousShipStatus
        ?.getAllEntries()
        ?.associateBy { it._id }
        ?: emptyMap()

    // distinct keeps the *first* entry, but we need to keep the *last* entry,
    // therefore we first reverse the statuses and allEntries, and at the end reverse it again
    val finalEntries: List<Entry> = newShipStatuses
        .reversed()
        .flatMap { it.getAllEntries() }
        .sortedByDescending { it.startTime }
        .distinctBy { it._id }
        .reversed()

    return finalEntries.mapNotNull { finalEntry ->
        val initialEntry = initialEntries[finalEntry._id]
        if (initialEntry != null) {
            if (finalEntry != initialEntry) {
                Change(Action.UPDATE, finalEntry)
            } else {
                // no change
                null
            }
        } else {
            Change(Action.CREATE, finalEntry)
        }
    }
}
