package nl.teqplay.vesselvoyage.util

import nl.teqplay.csi.model.ship.info.ShipRegisterInfo
import nl.teqplay.csi.model.ship.info.ShipRegisterInfoCache
import nl.teqplay.vesselvoyage.model.ShipDetails

fun ShipRegisterInfoCache.toShipDetails(): ShipDetails? {
    val imo = this.identifiers.imo
    val mmsi = this.identifiers.mmsi ?: return null

    return ShipDetails(
        imo = imo,
        type = null,
        categories = this.categories,
        mmsi = mmsi,
        name = this.identifiers.name,
        length = this.dimensions.length,
        beam = this.dimensions.beam,
        maxDraught = this.dimensions.maxDraught,
        dwt = this.specification.deadWeightTonnage
    )
}

fun ShipRegisterInfo.toShipDetails(): ShipDetails? {
    val imo = identifiers.imo ?: return null
    val mmsi = identifiers.mmsi ?: return null
    return ShipDetails(
        imo = imo,
        type = null,
        categories = categories,
        mmsi = mmsi,
        name = identifiers.name,
        length = dimensions.length,
        beam = dimensions.beam,
        maxDraught = dimensions.maxDraught,
        dwt = specification.deadWeightTonnage
    )
}
