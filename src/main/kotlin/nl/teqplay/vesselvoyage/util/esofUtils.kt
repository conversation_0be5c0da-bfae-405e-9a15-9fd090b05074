package nl.teqplay.vesselvoyage.util

import nl.teqplay.vesselvoyage.model.ESOF_GROUPING_MAX_DISTANCE_METERS
import nl.teqplay.vesselvoyage.model.ESOF_VOYAGE_END_MARGIN
import nl.teqplay.vesselvoyage.model.ESOF_VOYAGE_START_MARGIN
import nl.teqplay.vesselvoyage.model.ESof
import nl.teqplay.vesselvoyage.model.Entry
import nl.teqplay.vesselvoyage.model.LocationBasedEventPair
import nl.teqplay.vesselvoyage.model.Stop
import nl.teqplay.vesselvoyage.model.StopDetectionVersion
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.model.VisitShipStatus
import nl.teqplay.vesselvoyage.model.Voyage
import nl.teqplay.vesselvoyage.model.VoyageShipStatus
import nl.teqplay.vesselvoyage.model.v2.EntryESoFWrapper
import nl.teqplay.vesselvoyage.model.v2.EntryId
import nl.teqplay.vesselvoyage.model.v2.FallbackType
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewESoF
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import java.time.Duration
import java.time.Instant
import java.time.ZonedDateTime

fun emptyESof() = ESof(emptyList(), emptyList(), null)

/**
 * Merge ESof b into ESof a.
 * In case of duplicate events, the events from ESof a will be taken.
 */
fun mergeESofOrNull(a: ESof?, b: ESof?): ESof? {
    return if (a == null) {
        b
    } else if (b == null) {
        a
    } else {
        mergeESof(a, b)
    }
}

/**
 * Merge ESof b into ESof a.
 * In case of duplicate events, the events from ESof a will be taken.
 */
fun mergeESof(a: ESof, b: ESof): ESof {
    return ESof(
        encounters = (a.encounters + b.encounters)
            .distinctBy { it.startEventId }
            .sortedBy { it.startTime },

        stops = (a.stops + b.stops)
            // We don't district here as we can now have stops generated from the same movement event
            .sortedBy { it.startTime },
        slowMovingPeriods = if (a.slowMovingPeriods == null && b.slowMovingPeriods == null) {
            null
        } else {
            (a.slowMovingPeriods ?: emptyList()) + (b.slowMovingPeriods ?: emptyList())
        }
    )
}

fun ESof.isEmpty(): Boolean {
    return encounters.isEmpty() && stops.isEmpty()
}

fun ESof.isNotEmpty(): Boolean {
    return !isEmpty()
}

fun List<ESof>.mergeEsofs(): ESof {
    if (isEmpty()) {
        return emptyESof()
    }

    return reduce(::mergeESof)
}

fun <T> ESof.groupBy(predicate: (item: LocationBasedEventPair) -> T): Map<T, ESof> {
    val encounterGroups = encounters.groupBy { predicate(it) }
    val stopGroups = stops.groupBy { predicate(it) }
    val slowMovingPeriodGroups = slowMovingPeriods?.groupBy { predicate(it) }

    val eSofGroups = mutableMapOf<T, ESof>()
    val allKeys = encounterGroups.keys + stopGroups.keys

    allKeys.forEach { key ->
        eSofGroups[key] = ESof(
            encounters = encounterGroups[key] ?: emptyList(),
            stops = stopGroups[key] ?: emptyList(),
            slowMovingPeriods = slowMovingPeriodGroups?.get(key)
        )
    }

    return eSofGroups
}

fun VisitShipStatus.regroupESof(): VisitShipStatus {
    // we use three local variables 1-2-3 that are in chronological order and allow smart casting to non-nullable
    val visit1 = this.previousVisit
    val voyage2 = this.previousVoyage
    val visit3 = this.visit

    val mergedEsof = listOfNotNull(visit1?.esof, voyage2?.esof, visit3.esof).mergeEsofs()

    val regroupedEsofs: Map<Entry, ESof> = mergedEsof.groupBy {
        if (visit1 != null && isPartOfVisit(it, null, visit1, voyage2)) {
            visit1
        } else if (isPartOfVisit(it, voyage2, visit3, null)) {
            visit3
        } else {
            voyage2 ?: visit3
        }
    }
    var voyageEsof: ESof? = voyage2?.let { regroupedEsofs[it]?.takeIf { it.isNotEmpty() } }
    if (voyageEsof != null && voyage2 != null && voyage2.finished && voyageEsof.slowMovingPeriods == null) {
        voyageEsof = voyageEsof.copy(slowMovingPeriods = emptyList())
    }

    return VisitShipStatus(

        previousVisit = visit1?.copy(esof = regroupedEsofs[visit1]?.takeIf { it.isNotEmpty() }),
        previousVoyage = voyage2?.copy(esof = voyageEsof),
        visit = visit3.copy(esof = regroupedEsofs[visit3]?.takeIf { it.isNotEmpty() })
    )
}

fun VoyageShipStatus.regroupESof(): VoyageShipStatus {
    // we use three local variables 1-2-3 that are in chronological order and allow smart casting to non-nullable
    val voyage1 = this.previousVoyage
    val visit2 = this.previousVisit
    val voyage3 = this.voyage

    val mergedEsof = listOfNotNull(voyage1?.esof, visit2?.esof, voyage3.esof).mergeEsofs()

    val regroupedEsofs: Map<Entry, ESof> = mergedEsof.groupBy {
        if (visit2 != null && isPartOfVisit(it, voyage1, visit2, voyage3)) {
            visit2
        } else if (it.startTime.isBefore(voyage3.startTime)) {
            voyage1 ?: visit2 ?: voyage3
        } else {
            voyage3
        }
    }

    return VoyageShipStatus(
        previousVoyage = voyage1?.copy(esof = regroupedEsofs[voyage1]?.takeIf { it.isNotEmpty() }),
        previousVisit = visit2?.copy(esof = regroupedEsofs[visit2]?.takeIf { it.isNotEmpty() }),
        voyage = voyage3.copy(esof = regroupedEsofs[voyage3]?.takeIf { it.isNotEmpty() })
    )
}

/**
 * An eventPair (stop or encounter) is part of a visit when:
 *
 * - It took place during the visit
 * - It took place shortly before or after the visit AND is in the neighborhood of the visit
 */
fun isPartOfVisit(
    eventPair: LocationBasedEventPair,
    previousVoyage: Voyage?,
    visit: Visit,
    nextVoyage: Voyage?
): Boolean {
    val eventPairStart = eventPair.startTime
    val eventPairEnd = eventPair.endTime ?: eventPair.startTime

    val isDuringVisit = eventPairEnd.isAfter(visit.startTime) &&
        (visit.endTime == null || eventPairStart.isBefore(visit.endTime))

    if (isDuringVisit) {
        return true
    }

    val visitStartMargin = calculateEsofVoyageMargins(previousVoyage).voyageMarginEnd
    val visitEndMargin = calculateEsofVoyageMargins(nextVoyage).voyageMarginStart

    val visitEsofStart = visit.startTime.minus(visitStartMargin)
    val visitESofEnd = visit.endTime?.plus(visitEndMargin)

    val isDuringVisitWithTimeMargins = (visitESofEnd == null || eventPairStart.isBefore(visitESofEnd)) &&
        eventPairEnd.isAfter(visitEsofStart)

    val visitLocation = visit.portAreas.firstOrNull()?.startLocation
    val isNearby = visitLocation != null &&
        haversineDistance(visitLocation, eventPair.startLocation) < ESOF_GROUPING_MAX_DISTANCE_METERS

    return (isDuringVisitWithTimeMargins && isNearby)
}

data class ESofVoyageMargins(
    val voyageMarginStart: Duration,
    val voyageMarginEnd: Duration
)

/**
 * The ESof of the visit before and after a voyage will get some extra margin,
 * and the ESof of the voyage is extra short.
 */
fun calculateEsofVoyageMargins(voyageStart: ZonedDateTime?, voyageEnd: ZonedDateTime?): ESofVoyageMargins {
    if (voyageStart == null || voyageEnd == null) {
        return ESofVoyageMargins(
            voyageMarginStart = ESOF_VOYAGE_START_MARGIN,
            voyageMarginEnd = ESOF_VOYAGE_END_MARGIN
        )
    }

    val voyageDuration = Duration.between(voyageStart, voyageEnd)

    val voyageMarginStart = minOf(ESOF_VOYAGE_START_MARGIN, voyageDuration.dividedBy(2))
    val voyageMarginEnd = minOf(ESOF_VOYAGE_END_MARGIN, voyageDuration - voyageMarginStart)

    return ESofVoyageMargins(
        voyageMarginStart,
        voyageMarginEnd
    )
}

fun calculateEsofVoyageMargins(voyage: Voyage?) =
    calculateEsofVoyageMargins(voyage?.startTime, voyage?.endTime)

/**
 * Restructure the new stop reverting any changes via the stop detection mechanism and instead always use
 *  the berth event id, time and location.
 */
fun restructureStops(preparedStops: List<Stop>): List<Stop> {
    return preparedStops.map { stop ->
        var restructuredStop: Stop = stop
        val berthStart = stop.berthStart
        val berthEnd = stop.berthEnd

        // We have a berth start event, so use this instead of the existing start info
        if (berthStart != null) {
            restructuredStop = stop.copy(
                detectionVersion = StopDetectionVersion.BERTH_EVENT,
                startEventId = berthStart.id,
                startTime = berthStart.time,
                startLocation = berthStart.location
            )
        }

        // We have berth end event, so use this instead of the existing end info
        if (berthEnd != null) {
            restructuredStop = stop.copy(
                detectionVersion = StopDetectionVersion.BERTH_EVENT,
                endEventId = berthEnd.id,
                endTime = berthEnd.time,
                endLocation = berthEnd.location
            )
        }

        restructuredStop
    }
}

fun finishESoF(
    esof: NewESoF?,
    voyageStart: LocationTime,
    updateTime: Instant
): NewESoF? {
    if (esof == null) { return null }

    val fallbackEnd = voyageStart.copy(fallback = FallbackType.ACTIVITY_END_BY_EOSP)
    val finishedEncounters = esof.encounters.map { encounter ->
        if (encounter.end == null) {
            encounter.copy(end = fallbackEnd)
        } else {
            encounter
        }
    }

    return esof.copy(
        encounters = finishedEncounters,
        updatedAt = updateTime
    )
}

fun correctESoFForZeroSecondVoyage(
    finishedEsof: NewESoF?,
    newVisitStartTime: LocationTime,
    updateTime: Instant
): NewESoF? {
    if (finishedEsof == null) { return null }

    val newFallbackEnd = newVisitStartTime.copy(fallback = FallbackType.ACTIVITY_END_BY_EOSP)
    val newFinishedEncounters = finishedEsof.encounters.map { encounter ->
        if (encounter.end?.fallback == FallbackType.ACTIVITY_END_BY_EOSP) {
            encounter.copy(end = newFallbackEnd)
        } else {
            encounter
        }
    }

    return finishedEsof.copy(encounters = newFinishedEncounters, updatedAt = updateTime)
}

fun mergeESoF(
    newEntryId: EntryId,
    visit: EntryESoFWrapper<NewVisit>,
    voyage: EntryESoFWrapper<NewVoyage>?,
    updateTime: Instant
): Pair<NewESoF?, Boolean> {
    require(visit.entry._id == newEntryId || (voyage != null && voyage.entry._id == newEntryId)) {
        "newEntryId must be a reference to either the visit or voyage."
    }

    // No need to do any merging if we don't have a voyage
    if (voyage == null) {
        val esof = visit.esof
        return when {
            esof == null -> null to false
            visit.entry._id == newEntryId -> esof to false
            else -> esof.copy(_id = newEntryId) to true
        }
    }

    // We can't do any merging when both esof are not set
    if (visit.esof == null && voyage.esof == null) {
        return null to false
    }

    val visitEncounters = visit.esof?.encounters ?: emptyList()
    val voyageEncounters = voyage.esof?.encounters ?: emptyList()
    val visitSlowMovingPeriods = visit.esof?.slowMovingPeriods ?: emptyList()
    val voyageSlowMovingPeriods = voyage.esof?.slowMovingPeriods ?: emptyList()
    val visitShipToShipTransfers = visit.esof?.shipToShipTransfers ?: emptyList()
    val voyageShipToShipTransfers = voyage.esof?.shipToShipTransfers ?: emptyList()

    val isNew = when (newEntryId) {
        visit.entry._id -> visit.esof == null
        else -> voyage.esof == null
    }

    return NewESoF(
        _id = newEntryId,
        encounters = visitEncounters + voyageEncounters,
        slowMovingPeriods = (visitSlowMovingPeriods + voyageSlowMovingPeriods).takeIf { it.isNotEmpty() },
        shipToShipTransfers = visitShipToShipTransfers + voyageShipToShipTransfers,
        updatedAt = updateTime
    ) to isNew
}

fun createEmptyEsof(entry: NewEntry, updateTime: Instant): NewESoF {
    return NewESoF(
        _id = entry._id,
        encounters = emptyList(),
        slowMovingPeriods = null,
        shipToShipTransfers = emptyList(),
        updatedAt = updateTime
    )
}
