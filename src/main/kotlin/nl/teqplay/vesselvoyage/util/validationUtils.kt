package nl.teqplay.vesselvoyage.util

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.skeleton.common.exception.BadRequestException

private val log = KotlinLogging.logger {}

/**
 * Throws a BadRequestException when the size is equal or larger than limit
 */
fun throwWhenThresholdReached(actualOrLimitedSize: Int, threshold: Int) {
    if (actualOrLimitedSize >= threshold) {
        // IMPORTANT: we have to test larger or equal and not just larger.
        // Typically, we use the threshold (limit) for both fetching a limited number of results from MongoDB AND to
        // test whether the results exceed the maximum. I.e. if limit is 100, and we get back 100 results from MongoDB,
        // we want to throw the exception: in that case the size is not the actual size but the limited size of 100.
        //
        // (so, we also cannot mention the actual size in the message!)
        val message = "Number of results exceeds the maximum of $threshold entries. " +
            "Please break the request into multiple smaller requests."

        log.warn { "Bad Request: $message" }

        throw BadRequestException(message)
    }
}
