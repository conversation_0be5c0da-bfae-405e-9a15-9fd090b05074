package nl.teqplay.vesselvoyage.util

import nl.teqplay.csi.model.ship.info.component.ShipCategoryV1
import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2

fun ShipCategoryV1.toV2Category(): ShipCategoryV2? {
    return when (this) {
        ShipCategoryV1.CARGO -> ShipCategoryV2.GENERAL_CARGO
        ShipCategoryV1.TUG -> ShipCategoryV2.TUG
        ShipCategoryV1.PILOT -> null
        ShipCategoryV1.PASSENGER -> ShipCategoryV2.PASSENGER
        ShipCategoryV1.CONTAINER -> ShipCategoryV2.CONTAINER
        ShipCategoryV1.BULK -> ShipCategoryV2.BULK_CARRIER
        ShipCategoryV1.TANKER -> ShipCategoryV2.TANKER
        ShipCategoryV1.YACHT -> ShipCategoryV2.PLEASURE_CRAFT
        ShipCategoryV1.FISHING -> ShipCategoryV2.FISHING
        ShipCategoryV1.ORE_CARRIER -> ShipCategoryV2.BULK_CARRIER
    }
}

fun Collection<ShipCategoryV1>.toV2Categories(): Collection<ShipCategoryV2> {
    return this.mapNotNull { it.toV2Category() }
}
