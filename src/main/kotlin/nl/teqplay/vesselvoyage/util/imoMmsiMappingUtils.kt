package nl.teqplay.vesselvoyage.util

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.csi.model.ship.mapping.ImoMmsiMapping
import java.time.Instant
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.util.NavigableMap
import java.util.TreeMap

private val log = KotlinLogging.logger {}

data class GroupedMapping(
    val mmsis: Set<String>,
    val from: ZonedDateTime,
    val to: ZonedDateTime?
) {
    companion object {
        fun of(mmsis: Set<String>, from: Long, to: Long?): GroupedMapping {
            return GroupedMapping(
                mmsis = mmsis,
                from = Instant.ofEpochMilli(from).atZone(ZoneOffset.UTC),
                to = to?.let { Instant.ofEpochMilli(to).atZone(ZoneOffset.UTC) }
            )
        }
    }
}

fun List<ImoMmsiMapping>.groupOverlapping(from: ZonedDateTime, to: ZonedDateTime? = null): List<GroupedMapping> {
    // Instantly return an empty list when the provided range has a 'to' which is before the 'from'
    if (to != null && from.isAfter(to)) {
        log.debug { "Can't group ImoMmsiMapping, incorrect input range. (from = $from, to = $to)" }
        return emptyList()
    }

    // Default to 'now' when only a 'from' date is provided
    val parsedTo = to ?: ZonedDateTime.now(ZoneOffset.UTC)

    val map: NavigableMap<Long, MutableSet<String>> = TreeMap()
    for (mapping in this) {
        val groupFrom = maxOf(mapping.from ?: from.toEpochMillisecond(), from.toEpochMillisecond())
        val groupTo = mapping.to?.let { minOf(it, parsedTo.toEpochMillisecond()) } ?: parsedTo.toEpochMillisecond()

        // Ignore any mappings where the 'from' is bigger then the 'to'
        if (groupFrom >= groupTo) {
            log.debug { "Can't group ImoMmsiMapping entry, from is before to. (MMSI = ${mapping.mmsi}, from = $groupFrom, to = $groupTo)" }
            continue
        }

        if (groupFrom !in map) {
            map[groupFrom] = HashSet(map.lowerEntry(groupFrom)?.value ?: setOf())
        }

        if (groupTo !in map) {
            map[groupTo] = HashSet(map.lowerEntry(groupTo)?.value ?: setOf())
        }

        for (set in map.subMap(groupFrom, groupTo).values) {
            set.add(mapping.mmsi)
        }
    }

    return groupToDateRanges(map)
}

private fun groupToDateRanges(map: Map<Long, Set<String>>): List<GroupedMapping> {
    val result = mutableListOf<GroupedMapping>()
    var from = 0L
    var previousMapping = emptySet<String>()

    map.forEach { (to, value) ->
        if (previousMapping.isNotEmpty()) {
            result.add(GroupedMapping.of(previousMapping, from, to))
        }

        from = to
        previousMapping = value
    }

    return result
}
