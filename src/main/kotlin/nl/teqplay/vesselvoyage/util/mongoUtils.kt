package nl.teqplay.vesselvoyage.util

import com.mongodb.kotlin.client.FindIterable
import org.bson.Document
import org.bson.RawBsonDocument

fun setArrayFields(vararg arrayFieldAndValues: Pair<String, Double>): Document {
    val preparedJson = arrayFieldAndValues.map { (arrayField, value) ->
        setArrayField(arrayField, value)
    }.joinToString { it }

    return Document("\$set", RawBsonDocument.parse("{ $preparedJson }"))
}

fun setArrayField(arrayField: String, value: Double): String {
    return "'$arrayField': $value"
}

fun <T : Any> FindIterable<T>.limitIfNotNull(limit: Int?) = if (limit != null) limit(limit) else this
