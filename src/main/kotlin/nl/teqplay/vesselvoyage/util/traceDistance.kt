package nl.teqplay.vesselvoyage.util

import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.util.haversineDistance
import nl.teqplay.vesselvoyage.model.v2.TraceDistance

fun createTraceDistanceBetween(locationA: Location, locationB: Location): TraceDistance = TraceDistance(
    distanceMeters = haversineDistance(locationA, locationB).toLong(),
    lastLocation = locationB
)

fun createTraceDistanceFrom(location: Location) = TraceDistance(
    distanceMeters = 0L,
    lastLocation = location
)

fun createTraceDistanceFrom(newItems: List<Location>): TraceDistance? = when (newItems.size) {
    0 -> null
    1 -> createTraceDistanceFrom(newItems.first())
    2 -> createTraceDistanceBetween(newItems[0], newItems[1])
    else -> {
        val distance = createTraceDistanceFrom(newItems.first())
        appendToTraceDistance(distance, newItems.drop(1))
    }
}

fun appendToTraceDistance(
    distance: TraceDistance,
    newLocations: List<Location>
): TraceDistance = when (newLocations.size) {
    0 -> distance
    1 -> distance.append(newLocations.first())
    else -> newLocations.fold(distance.toMutable()) { acc, location -> acc.append(location) }.toImmutable()
}

private fun TraceDistance.append(location: Location): TraceDistance = copy(
    distanceMeters = distanceMeters + haversineDistance(lastLocation, location).toLong(),
    lastLocation = location
)

/** Helper class for folding multiple locations into a TraceDistance */
private data class MutableTraceDistance(
    var distanceMeters: Long,
    var lastLocation: Location
) {
    fun toImmutable() = TraceDistance(distanceMeters, lastLocation)

    fun append(location: Location): MutableTraceDistance {
        distanceMeters += haversineDistance(lastLocation, location).toLong()
        lastLocation = location
        return this
    }
}

private fun TraceDistance.toMutable() = MutableTraceDistance(distanceMeters, lastLocation)
