package nl.teqplay.vesselvoyage.util

import java.util.concurrent.ConcurrentHashMap

/**
 * Create a truncated, limited string from a list, like "A, B, C, ... and 4 more"
 */
fun <T> List<T>.joinToStringTruncated(limit: Int, transform: ((T) -> CharSequence)? = null): String {
    return this.joinToString(
        limit = limit,
        transform = transform,
        truncated = "... and ${this.size - limit} more"
    )
}

fun intersect(set1: Set<String>?, set2: Set<String>?): Set<String>? =
    listOfNotNull(set1, set2).fold<Set<String>, Set<String>?>(null) { acc, curr -> acc?.intersect(curr) ?: curr }

fun <K, V> Map<K, V>.toConcurrentHashMap(): ConcurrentHashMap<K, V> {
    return ConcurrentHashMap(this)
}

/**
 * Find the first element matching the given [predicate] and replace it with the result of [onReplace].
 * If [onReplace] returns null, it will instead remove the item from the list.
 * @throws [IndexOutOfBoundsException] if no such element is found.
 */
fun <T> List<T>.replaceFirst(
    predicate: (T) -> <PERSON><PERSON><PERSON>,
    onReplace: (T) -> T?
): List<T> {
    val indexOfMatchingObject = this.indexOfFirst(predicate)
    val matchingObject = this[indexOfMatchingObject]

    val newList = this.toMutableList()
    val replacedItem = onReplace(matchingObject)
    if (replacedItem != null) {
        newList[indexOfMatchingObject] = replacedItem
    } else {
        newList.removeAt(indexOfMatchingObject)
    }

    return newList
}

/**
 * Replace the first element matching [item] and replace it with the [newItem].
 * @throws [IndexOutOfBoundsException] if no such element is found.
 */
fun <T> List<T>.replaceFirst(
    item: T,
    newItem: T
): List<T> {
    val indexOfMatchingObject = this.indexOf(item)

    val newList = this.toMutableList()
    newList[indexOfMatchingObject] = newItem

    return newList
}

/**
 * Replace the first element matching [item] and replace it with the [newItem].
 * @throws [IndexOutOfBoundsException] if no such element is found.
 */
fun <T> List<T>.replaceLast(
    item: T,
    newItem: T
): List<T> {
    val indexOfMatchingObject = this.lastIndexOf(item)

    val newList = this.toMutableList()
    newList[indexOfMatchingObject] = newItem

    return newList
}
