package nl.teqplay.vesselvoyage.util

import nl.teqplay.platform.util.LocationUtils
import nl.teqplay.vesselvoyage.model.Location
import java.awt.Polygon
import java.awt.geom.Area
import java.math.BigDecimal
import java.math.RoundingMode
import kotlin.math.atan2
import kotlin.math.cos
import kotlin.math.pow
import kotlin.math.sin
import kotlin.math.sqrt
import nl.teqplay.platform.model.Location as PlatformLocation
import nl.teqplay.skeleton.model.Location as SkeletonLocation

private const val EARTH_RADIUS_KM = 6378.1370
const val EARTH_RADIUS = EARTH_RADIUS_KM * 1000.0

/**
 * Approximates a straight line distance from one Location to another
 * Location using Haversine equation, with 0.3% accuracy in most cases.
 *
 * @see [Haversine Formula](http://www.movable-type.co.uk/scripts/latlong.html)
 *
 * @return distance in meters between points, with 3.10<sup>-2</sup> precision
 */
fun haversineDistance(location1: Location, location2: Location): Double {
    val dLat = Math.toRadians(location1.latitude - location2.latitude)
    val dLon = Math.toRadians(location1.longitude - location2.longitude)

    val a = sin(dLat / 2).pow(2.0) + (
        cos(Math.toRadians(location2.latitude)) *
            cos(Math.toRadians(location1.latitude)) * sin(dLon / 2).pow(2.0)
        )
    val c = 2 * atan2(sqrt(a), sqrt(1 - a))

    return EARTH_RADIUS * c // distance = radius * c
}

/**
 * Test whether two polygons are overlapping
 *
 * This is an approximation with a precision of about 6 digits,
 * and not taking into account the curve of the earth so not applicable
 * for large polygons
 */
fun overlapping(polygon1: List<SkeletonLocation>, polygon2: List<SkeletonLocation>): Boolean {
    // We use a trick here: Java's built in Polygon and Area only support integers.
    // Since we're only interested in the end result (true/false), we create a
    // temporary polygon with values multiplied by 1.0e6 and then rounded to an
    // integer. So we get a precision of about 6 digits in our end result.
    fun createTempPolygon(locations: List<SkeletonLocation>): Polygon {
        val polygon = Polygon()

        locations.forEach {
            polygon.addPoint(
                (it.lat * 1.0e6).toInt(),
                (it.lon * 1.0e6).toInt()
            )
        }

        return polygon
    }

    val area1 = Area(createTempPolygon(polygon1))
    val area2 = Area(createTempPolygon(polygon2))

    area1.intersect(area2)

    return !area1.isEmpty
}

fun pointInPolygon(polygon: List<Location>, point: Location): Boolean {
    val platformPolygon = polygon.map { it.toPlatformLocation() }.toTypedArray()
    val platformPoint = point.toPlatformLocation()

    return LocationUtils.pointInPolygon(platformPolygon, platformPoint)
}

fun PlatformLocation.toVesselVoyageLocation() = Location(
    latitude = this.latitude,
    longitude = this.longitude
)

fun PlatformLocation.toSkeletonLocation() = SkeletonLocation(
    lat = this.latitude,
    lon = this.longitude
)

fun SkeletonLocation.toPlatformLocation() = PlatformLocation(this.lat, this.lon)

fun SkeletonLocation.toVesselVoyageLocation() = Location(
    latitude = this.lat,
    longitude = this.lon
)

fun Location.toSkeletonLocation() = SkeletonLocation(
    lat = this.latitude,
    lon = this.longitude
)

fun Location.toPlatformLocation() = PlatformLocation(this.latitude, this.longitude)

fun Location.toScaled(newScale: Int): Location {
    val actualLat = BigDecimal.valueOf(this.latitude).setScale(newScale, RoundingMode.FLOOR)
    val actualLon = BigDecimal.valueOf(this.longitude).setScale(newScale, RoundingMode.FLOOR)

    return Location(
        latitude = actualLat.toDouble(),
        longitude = actualLon.toDouble()
    )
}

fun SkeletonLocation.toScaled(newScale: Int): SkeletonLocation {
    val actualLat = BigDecimal.valueOf(this.lat).setScale(newScale, RoundingMode.FLOOR)
    val actualLon = BigDecimal.valueOf(this.lon).setScale(newScale, RoundingMode.FLOOR)

    return SkeletonLocation(
        lat = actualLat.toDouble(),
        lon = actualLon.toDouble()
    )
}
