package nl.teqplay.vesselvoyage.util

import nl.teqplay.skeleton.auth.credentials.User
import nl.teqplay.skeleton.common.exception.UnauthorizedException
import org.springframework.security.authentication.AnonymousAuthenticationToken
import org.springframework.security.core.Authentication

fun Authentication?.asUser(): User =
    when (this) {
        null, is AnonymousAuthenticationToken -> throw UnauthorizedException("action requires login")
        else -> principal as User
    }
