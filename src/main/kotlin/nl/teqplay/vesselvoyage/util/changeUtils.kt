package nl.teqplay.vesselvoyage.util

import nl.teqplay.vesselvoyage.model.ENTRY_TYPE_VISIT
import nl.teqplay.vesselvoyage.model.ENTRY_TYPE_VOYAGE
import nl.teqplay.vesselvoyage.model.OutgoingChange
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.model.Voyage

fun OutgoingChange.getRoutingKey(): String {
    return when (entry) {
        is Visit -> "$ENTRY_TYPE_VISIT.${entry.imo}.$action"
        is Voyage -> "$ENTRY_TYPE_VOYAGE.${entry.imo}.$action"
    }
}
