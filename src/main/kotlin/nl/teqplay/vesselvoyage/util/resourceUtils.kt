package nl.teqplay.vesselvoyage.util

import java.net.URL
import java.nio.charset.Charset
import java.nio.charset.StandardCharsets

fun loadResource(name: String): URL {
    // We use a temporary class in order to have the right context so we can read a resource
    class LoadResourceHelperClass {
        fun load(): URL {
            return javaClass.classLoader.getResource(name)
                ?: throw ResourceNotFoundException("Resource '$name' not found")
        }
    }

    return LoadResourceHelperClass().load()
}

fun loadTextResource(name: String, encoding: Charset = StandardCharsets.UTF_8) =
    String(loadResource(name).openStream().readAllBytes(), encoding)
