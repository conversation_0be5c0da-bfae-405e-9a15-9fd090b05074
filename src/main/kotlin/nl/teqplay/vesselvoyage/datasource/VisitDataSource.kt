package nl.teqplay.vesselvoyage.datasource

import com.mongodb.client.model.ReplaceOptions
import com.mongodb.kotlin.client.FindIterable
import com.mongodb.kotlin.client.MongoCollection
import com.mongodb.kotlin.client.MongoDatabase
import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.skeleton.datasource.kmongo.and
import nl.teqplay.skeleton.datasource.kmongo.ascending
import nl.teqplay.skeleton.datasource.kmongo.ascendingSort
import nl.teqplay.skeleton.datasource.kmongo.deleteOneById
import nl.teqplay.skeleton.datasource.kmongo.descending
import nl.teqplay.skeleton.datasource.kmongo.descendingSort
import nl.teqplay.skeleton.datasource.kmongo.distinct
import nl.teqplay.skeleton.datasource.kmongo.div
import nl.teqplay.skeleton.datasource.kmongo.ensureIndex
import nl.teqplay.skeleton.datasource.kmongo.eq
import nl.teqplay.skeleton.datasource.kmongo.findOne
import nl.teqplay.skeleton.datasource.kmongo.findOneById
import nl.teqplay.skeleton.datasource.kmongo.gt
import nl.teqplay.skeleton.datasource.kmongo.`in`
import nl.teqplay.skeleton.datasource.kmongo.index
import nl.teqplay.skeleton.datasource.kmongo.lt
import nl.teqplay.skeleton.datasource.kmongo.replaceOneById
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.datasource.util.FindShipsByCategory
import nl.teqplay.vesselvoyage.datasource.util.resolveQueryImos
import nl.teqplay.vesselvoyage.model.AnchorAreaVisit
import nl.teqplay.vesselvoyage.model.Destination
import nl.teqplay.vesselvoyage.model.PortAreaVisit
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.model.VisitQuery
import org.springframework.stereotype.Component
import java.time.ZonedDateTime

private val log = KotlinLogging.logger {}

@ProfileProcessing
@ProfileApi
@Component
class VisitDataSource(private val database: MongoDatabase) : EntryDataSource<Visit>() {
    override fun initCollection(): MongoCollection<Visit> {
        return database.getCollection<Visit>("visits").apply {
            // for findByIMO(), findByIMOs()
            // is also used by findRecentByIMO()
            ensureIndex(Visit::imo, Visit::startTime, Visit::endTime)

            // for findByPortId()
            ensureIndex(Visit::portAreas / PortAreaVisit::portId, Visit::startTime, Visit::endTime)

            // for findCurrentVisitsByPortId()
            ensureIndex(Visit::portAreas / PortAreaVisit::portId, Visit::finished, Visit::startTime)

            // for findCurrentAnchorageByPortId()
            ensureIndex(
                Visit::anchorAreas / AnchorAreaVisit::destinations,
                Visit::startTimePort,
                Visit::finished,
                Visit::startTime
            )

            // for findPreviousLocationByMMSI()
            ensureIndex(
                index(
                    Visit::mmsi to true /* ascending order */,
                    Visit::endTime to false /* descending order */
                )
            )

            // for findPreviousLocationByIMO()
            ensureIndex(
                index(
                    Visit::imo to true /* ascending order */,
                    Visit::endTime to false /* descending order */
                )
            )

            // for findByDestination()
            ensureIndex(Visit::finished, Visit::dest / Destination::trueDestination, Visit::startTime)

            // for find()
            // This can have any combination of optional fields, so we cannot create a single index matching all options.
            // Relying on mongo to use those indexes as smart as possible (maybe it can use index intersection)
            ensureIndex(
                Visit::startTime,
                Visit::endTime,
                Visit::portAreas / PortAreaVisit::portId
            )
            ensureIndex(Visit::imo) // used to search individual ships or a list with ships of a specific type
            ensureIndex(Visit::finished)
        }
    }

    fun findById(visitId: String) = collection.findOneById(visitId)

    fun findByIds(visitIds: List<String>) = collection.find(Visit::_id `in` visitIds).toList()

    fun findByIdAndImo(visitId: String, imo: String): Visit? {
        return collection.findOne(
            and(
                Visit::_id eq visitId,
                Visit::imo eq imo
            )
        )
    }

    /**
     * It returns the list (size <= limit) of past consecutive visits starting from the given [startingVisit]
     * (not containing it).
     * Note that if the startingVisit is close to the first visit in time, the returning length may vary (i.e. requesting
     * the next 5 visits from the n-1 most recent visit).
     */
    fun findPastVisits(startingVisit: Visit, limit: UInt): List<Visit> {
        // check the limit not being 0 or negative.
        val queryFilter = and(
            Visit::imo eq startingVisit.imo,
            Visit::startTime lt startingVisit.startTime
        )
        return collection
            .find(queryFilter)
            .sort(descending(Visit::startTime))
            .limit(limit.toInt())
            .toList()
    }

    /**
     * It returns the list (size <= limit) of future consecutive visits starting from the given [startingVisit]
     * (not containing it).
     * Note that if the startingVisit is close to the first visit in time, the returning length may vary (i.e. requesting
     * the next 5 visits from the n-1 most recent visit).
     */
    fun findFutureVisits(startingVisit: Visit, limit: UInt): List<Visit> {
        val queryFilter = and(
            Visit::imo eq startingVisit.imo,
            Visit::startTime gt startingVisit.startTime
        )
        return collection
            .find(queryFilter)
            .sort(ascending(Visit::startTime)) // ascending to be able to take the requested entries with "limit.(limit)"
            .limit(limit.toInt())
            .toList()
            .reversed() // so the method list result is descencing
    }

    fun findByIMO(imo: String, start: ZonedDateTime, endExcl: ZonedDateTime, limit: Int) = collection
        .find(
            and(
                listOf(Visit::imo eq imo) +
                    queryPeriod(start, endExcl)
            )
        )
        .ascendingSort(Visit::startTime)
        .limit(limit)
        .toList()

    fun findByDestination(destination: String, imos: Set<String>?) = collection
        .find(
            and(
                Visit::finished eq false,
                Visit::dest / Destination::trueDestination eq destination,
                imos?.let { Visit::imo `in` it }
            )
        )
        .ascendingSort(Visit::startTime)

    fun getVisitIdsByImo(imo: String): List<String> {
        data class IdProjectionClass(val _id: String)

        return collection
            .withDocumentClass<IdProjectionClass>()
            .find(Visit::imo eq imo)
            .descendingSort(Visit::startTime)
            .toList()
            .map { it._id }
    }

    fun countByImo(imo: String) = collection.countDocuments(Visit::imo eq imo)

    fun findByIMOs(imos: List<String>, start: ZonedDateTime, endExcl: ZonedDateTime, limit: Int) = collection
        .find(
            and(
                listOf(Visit::imo `in` imos) +
                    queryPeriod(start, endExcl)
            )
        )
        .ascendingSort(Visit::startTime)
        .limit(limit)

    fun findByPortId(
        portId: String,
        start: ZonedDateTime,
        endExcl: ZonedDateTime,
        limit: Int,
        imos: Set<String>?,
    ): FindIterable<Visit> {
        val filter = and(
            listOfNotNull(
                imos?.let { Visit::imo `in` it },
                Visit::portAreas / PortAreaVisit::portId eq portId
            ) + queryPeriod(start, endExcl)
        )

        return collection
            .find(filter)
            .ascendingSort(Visit::startTime)
            .limit(limit)
    }

    fun findCurrentVisitsByPortId(portId: String, imos: Set<String>?): FindIterable<Visit> {
        val filter = and(
            listOfNotNull(
                Visit::portAreas / PortAreaVisit::portId eq portId,
                Visit::finished eq false,
                imos?.let { Visit::imo `in` it }
            )
        )

        return collection
            .find(filter)
            .ascendingSort(Visit::startTime)
    }

    fun findCurrentAnchorageByPortId(portId: String, imos: Set<String>?): FindIterable<Visit> {
        val filter = and(
            listOfNotNull(
                Visit::anchorAreas / AnchorAreaVisit::destinations `in` portId,
                Visit::startTimePort eq null,
                Visit::finished eq false,
                imos?.let { Visit::imo `in` it }
            )
        )

        return collection
            .find(filter)
            .ascendingSort(Visit::startTime)
    }

    fun findRecentByIMO(imo: String, limit: Int = 10) = collection
        .find(Visit::imo eq imo)
        .descendingSort(Visit::startTime)
        .limit(limit)

    data class LastTwoVisits(
        val lastVisit: Visit?,
        val secondLastVisit: Visit?,
    )

    fun findLastTwoByIMO(imo: String): LastTwoVisits {
        val visits = findRecentByIMO(imo, 2).toList()

        return LastTwoVisits(
            lastVisit = visits.getOrNull(0),
            secondLastVisit = visits.getOrNull(1)
        )
    }

    fun findPreviousLocationByIMO(imo: String, before: ZonedDateTime): Visit? = collection.find(
        and(
            Visit::imo eq imo,
            Visit::endTime lt before
        )
    )
        .descendingSort(Visit::endTime)
        .limit(1)
        .firstOrNull()

    fun find(query: VisitQuery, findShipsByType: FindShipsByCategory): List<Visit> {
        require(query.startTime <= query.endTime) { "end time should be after start time" }

        val imos = resolveQueryImos(query.imos, query.shipCategoriesV2, query.shipCategories, findShipsByType)

        log.debug { "find query: $query, number of imos: ${imos?.size ?: "(none)"}" }

        return collection
            .find(
                and(
                    queryPeriod(query.startTime, query.endTime) + listOfNotNull(
                        query.portIds?.let { portIds ->
                            Visit::portAreas / PortAreaVisit::portId `in` portIds
                        },

                        imos?.let {
                            Visit::imo `in` imos
                        },

                        query.finished?.let {
                            Visit::finished eq query.finished
                        },
                    )
                )
            )
            .ascendingSort(Visit::startTime)
            .limit(query.limit)
            .toList()
    }

    fun distinctImos() = collection.distinct(Visit::imo).toSet()

    fun createOrReplace(visit: Visit): Visit {
        collection.replaceOneById(visit._id, visit, ReplaceOptions().upsert(true))
        return visit
    }

    fun deleteById(visitId: String) {
        collection.deleteOneById(visitId)
    }

    fun deleteAllByIMO(imo: String) {
        val result = collection.deleteMany(Visit::imo eq imo)
        log.debug { "Deleted all visits of ship with IMO $imo, count: ${result.deletedCount}" }
    }
}
