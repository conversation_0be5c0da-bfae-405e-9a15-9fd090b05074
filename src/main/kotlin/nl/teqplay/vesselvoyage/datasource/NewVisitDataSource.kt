package nl.teqplay.vesselvoyage.datasource

import com.mongodb.kotlin.client.MongoCollection
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.skeleton.datasource.kmongo.and
import nl.teqplay.skeleton.datasource.kmongo.ascendingSort
import nl.teqplay.skeleton.datasource.kmongo.descendingSort
import nl.teqplay.skeleton.datasource.kmongo.div
import nl.teqplay.skeleton.datasource.kmongo.ensureIndex
import nl.teqplay.skeleton.datasource.kmongo.eq
import nl.teqplay.skeleton.datasource.kmongo.gte
import nl.teqplay.skeleton.datasource.kmongo.`in`
import nl.teqplay.skeleton.datasource.kmongo.lte
import nl.teqplay.skeleton.datasource.kmongo.projection
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.model.internal.MinimalNewVisit
import nl.teqplay.vesselvoyage.model.v2.AreaActivity
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import org.bson.conversions.Bson
import org.springframework.stereotype.Component
import java.time.Instant
import kotlin.math.abs

@ProfileProcessing
@ProfileApi
@Component
class NewVisitDataSource(database: MongoDatabase) : NewEntryDataSource<NewVisit>(database) {
    override val collectionName: String
        get() = "visitV2"
    override val clazz: Class<NewVisit>
        get() = NewVisit::class.java

    override fun applyIndexes(collection: MongoCollection<NewVisit>) {
        super.applyIndexes(collection)

        with(collection) {
            ensureIndex(
                NewVisit::eospAreaActivity / AreaActivity::areaId,
                NewVisit::start / LocationTime::time,
                NewVisit::end / LocationTime::time
            )
        }
    }

    fun findByPortAreaId(
        areaId: String,
        start: Instant?,
        end: Instant?,
        finished: NewEntryFinishedFilter,
        confirmed: Boolean?,
        aisTrueDestination: String?,
        qualifyingImos: Set<Int>
    ): List<NewVisit> {
        val preparedAreaId = preparePortAreaIdToEosp(areaId)
        val filters = buildList {
            addAll(queryPeriodWithOptionalStartAndEnd(start, end))
            add(NewVisit::eospAreaActivity / AreaActivity::areaId eq preparedAreaId)
            add(queryFinishedFilter(finished))
            add(queryConfirmedFilter(confirmed))
            queryAisTrueDestination(aisTrueDestination)?.let(::add)
            if (qualifyingImos.isNotEmpty()) {
                add(NewVisit::imo `in` qualifyingImos)
            }
        }
        return collection.find(and(filters))
            .descendingSort(NewVisit::start / LocationTime::time)
            .toList()
    }

    override fun queryConfirmedFilter(confirmed: Boolean?): Bson? {
        return confirmed?.let { NewVisit::confirmed eq confirmed }
    }

    fun findByPortAreaIdAndImos(
        areaId: String,
        imos: Set<Int>,
        finished: NewEntryFinishedFilter
    ): List<MinimalNewVisit> {
        val preparedAreaId = preparePortAreaIdToEosp(areaId)
        val filters = buildList {
            // Only filter on imos if the provided set is not empty, otherwise we just want all of them
            if (imos.isNotEmpty()) {
                add(NewVisit::imo `in` imos)
            }

            add(NewVisit::eospAreaActivity / AreaActivity::areaId eq preparedAreaId)
            add(queryFinishedFilter(finished))
        }

        return collection.withDocumentClass<MinimalNewVisit>()
            .find(and(filters))
            .descendingSort(NewVisit::start / LocationTime::time)
            .projection(
                MinimalNewVisit::_id,
                MinimalNewVisit::imo,
                MinimalNewVisit::start,
                MinimalNewVisit::eospAreaActivity,
                MinimalNewVisit::portAreaActivities
            )
            .toList()
    }

    fun findByImoLookAround(
        imo: Int,
        timestamp: Instant,
        limit: Int,
        finished: NewEntryFinishedFilter,
        confirmed: Boolean?
    ): List<NewVisit> {
        if (limit == 0) {
            return emptyList()
        }
        val timestampFilter = if (limit > 0) {
            NewVisit::start / LocationTime::time gte timestamp
        } else {
            NewVisit::start / LocationTime::time lte timestamp
        }

        val filters = and(
            NewVisit::imo eq imo,
            timestampFilter,
            queryFinishedFilter(finished),
            queryConfirmedFilter(confirmed),
        )
        val query = collection.find(filters)
            .limit(abs(limit))

        return if (limit > 0) {
            // looking ahead (future), so sort the result set ascending to get the items closest to the timestamp
            query.ascendingSort(NewEntry::start / LocationTime::time)
        } else {
            // looking back (past), so sort the result set descending to get the items closest to the timestamp
            query.descendingSort(NewEntry::start / LocationTime::time)
        }.toList()
    }

    private fun preparePortAreaIdToEosp(areaId: String): String {
        // Always add the eosp suffix to the area when searching in the database
        return if (areaId.endsWith(".eosp")) {
            areaId
        } else {
            "$areaId.eosp"
        }
    }
}
