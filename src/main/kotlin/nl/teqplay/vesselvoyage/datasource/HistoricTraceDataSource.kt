package nl.teqplay.vesselvoyage.datasource

import com.mongodb.kotlin.client.MongoDatabase
import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.skeleton.datasource.kmongo.and
import nl.teqplay.skeleton.datasource.kmongo.ensureIndex
import nl.teqplay.skeleton.datasource.kmongo.eq
import nl.teqplay.skeleton.datasource.kmongo.findOne
import nl.teqplay.skeleton.datasource.kmongo.`in`
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.model.HistoricTrace
import nl.teqplay.vesselvoyage.util.joinToStringTruncated
import org.springframework.stereotype.Component

private val log = KotlinLogging.logger {}

@ProfileProcessing
@ProfileApi
@Component
class HistoricTraceDataSource(database: MongoDatabase) {
    private val collection = database.getCollection<HistoricTrace>("traces").apply {
        ensureIndex(HistoricTrace::entryId, HistoricTrace::tolerance)
        ensureIndex(HistoricTrace::imo, HistoricTrace::startTime)
    }

    fun findByEntryId(entryId: String, tolerance: Double) = collection.findOne(
        HistoricTrace::entryId eq entryId,
        HistoricTrace::tolerance eq tolerance,
    )

    fun findByEntryIds(entryIds: List<String>, tolerance: Double) = collection.find(
        and(
            HistoricTrace::entryId `in` entryIds,
            HistoricTrace::tolerance eq tolerance,
        )
    )

    fun findByImo(imo: String) = collection.find(
        HistoricTrace::imo eq imo
    ).toList()

    fun upsert(trace: HistoricTrace) {
        collection.deleteMany(
            and(
                HistoricTrace::entryId eq trace.entryId,
                HistoricTrace::tolerance eq trace.tolerance,
            )
        )
        collection.insertOne(trace)
    }

    fun deleteByEntryId(entryId: String) {
        val result = collection.deleteOne(HistoricTrace::entryId eq entryId)

        if (result.deletedCount > 0) {
            log.debug { "Deleted historic trace of entry with id $entryId" }
        }
    }

    fun deleteByEntryIds(entryIds: List<String>) {
        val result = collection.deleteMany(HistoricTrace::entryId `in` entryIds)

        if (result.deletedCount > 0) {
            log.debug { "Deleted historic traces of entries ${entryIds.joinToStringTruncated(10)}" }
        }
    }
}
