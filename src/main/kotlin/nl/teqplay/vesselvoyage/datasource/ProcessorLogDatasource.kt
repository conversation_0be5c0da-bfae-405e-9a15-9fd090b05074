package nl.teqplay.vesselvoyage.datasource

import com.mongodb.client.model.IndexOptions
import com.mongodb.client.model.ReplaceOptions
import com.mongodb.kotlin.client.MongoCollection
import com.mongodb.kotlin.client.MongoDatabase
import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.skeleton.datasource.kmongo.and
import nl.teqplay.skeleton.datasource.kmongo.div
import nl.teqplay.skeleton.datasource.kmongo.ensureIndex
import nl.teqplay.skeleton.datasource.kmongo.eq
import nl.teqplay.skeleton.datasource.kmongo.findOne
import nl.teqplay.skeleton.datasource.kmongo.gte
import nl.teqplay.skeleton.datasource.kmongo.`in`
import nl.teqplay.skeleton.datasource.kmongo.lte
import nl.teqplay.skeleton.datasource.kmongo.replaceOneById
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.model.ProcessorLog
import org.springframework.stereotype.Component
import java.time.Instant
import java.util.UUID
import java.util.concurrent.TimeUnit

private val log = KotlinLogging.logger {}

@ProfileProcessing
@Component
class ProcessorLogDatasource(db: MongoDatabase) {

    private val collection: MongoCollection<ProcessorLog> = db.getCollection("processorLogs")

    init {
        collection.ensureIndex(ProcessorLog::imo, ProcessorLog::event / Event::createdTime)
        collection.ensureIndex(ProcessorLog::entryIds)
        collection.ensureIndex(ProcessorLog::event / Event::_id)
        // remove logs after 7 days
        collection.ensureIndex(
            ProcessorLog::processingStart,
            indexOptions = IndexOptions().expireAfter(7, TimeUnit.DAYS)
        )
    }

    fun save(processorLog: ProcessorLog) {
        // ensure an _id is set
        val withId = if (processorLog._id.isBlank()) {
            processorLog.copy(_id = UUID.randomUUID().toString())
        } else {
            processorLog
        }
        try {
            collection.replaceOneById(withId._id, withId, ReplaceOptions().upsert(true))
        } catch (e: Exception) {
            log.debug { "Failed to insert log: ${e.message}" }
        }
    }

    /**
     * Query for IMO and when processing of the event started
     */
    fun queryByImo(imo: Int, startTime: Instant? = null, endTime: Instant? = null): List<ProcessorLog> {
        val filters = listOfNotNull(
            ProcessorLog::imo eq imo,
            startTime?.let { ProcessorLog::processingStart gte it },
            endTime?.let { ProcessorLog::processingStart lte it },
        )
        return collection.find(and(filters)).toList()
    }

    fun findByEntryId(entryIds: Set<String>): ProcessorLog? {
        return collection.findOne(ProcessorLog::entryIds `in` entryIds)
    }

    fun findById(id: String): ProcessorLog? {
        return collection.findOne(ProcessorLog::_id eq id)
    }

    fun queryByEventId(eventId: String): List<ProcessorLog> {
        return collection.find(ProcessorLog::event / Event::_id eq eventId).toList()
    }
}
