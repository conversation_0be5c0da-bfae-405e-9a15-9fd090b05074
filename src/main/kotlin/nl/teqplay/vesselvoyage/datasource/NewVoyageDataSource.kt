package nl.teqplay.vesselvoyage.datasource

import com.mongodb.client.model.CountOptions
import com.mongodb.client.model.IndexOptions
import com.mongodb.kotlin.client.MongoCollection
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.skeleton.datasource.kmongo.and
import nl.teqplay.skeleton.datasource.kmongo.ascending
import nl.teqplay.skeleton.datasource.kmongo.ascendingSort
import nl.teqplay.skeleton.datasource.kmongo.descendingSort
import nl.teqplay.skeleton.datasource.kmongo.div
import nl.teqplay.skeleton.datasource.kmongo.ensureIndex
import nl.teqplay.skeleton.datasource.kmongo.eq
import nl.teqplay.skeleton.datasource.kmongo.gte
import nl.teqplay.skeleton.datasource.kmongo.`in`
import nl.teqplay.skeleton.datasource.kmongo.lt
import nl.teqplay.skeleton.datasource.kmongo.or
import nl.teqplay.skeleton.datasource.kmongo.projection
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.model.internal.MinimalNewVoyage
import nl.teqplay.vesselvoyage.model.v2.Destination
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import nl.teqplay.vesselvoyage.util.limitIfNotNull
import org.bson.conversions.Bson
import org.springframework.stereotype.Component
import java.time.Instant

@ProfileProcessing
@ProfileApi
@Component
class NewVoyageDataSource(database: MongoDatabase) : NewEntryDataSource<NewVoyage>(database) {
    override val collectionName: String
        get() = "voyageV2"
    override val clazz: Class<NewVoyage>
        get() = NewVoyage::class.java

    override fun applyIndexes(collection: MongoCollection<NewVoyage>) {
        super.applyIndexes(collection)

        with(collection) {
            val bg = IndexOptions().background(true)

            val imo = NewVoyage::imo
            val originPort = NewVoyage::originPort
            val destinationPort = NewVoyage::destinationPort
            val start = NewVoyage::start
            val end = NewVoyage::end

            createIndex(keys = ascending(imo, originPort, start, end), options = bg)
            createIndex(keys = ascending(imo, destinationPort, start, end), options = bg)
            createIndex(keys = ascending(imo, originPort, destinationPort, start, end), options = bg)

            createIndex(keys = ascending(originPort, start, end), options = bg)
            createIndex(keys = ascending(destinationPort, start, end), options = bg)
            createIndex(keys = ascending(originPort, destinationPort, start, end), options = bg)

            // The destination.actual field is not always populated, it depends on the 'trueDestination' events,
            // therefore make it sparse
            ensureIndex(NewVoyage::destination / Destination::actual)
        }
    }

    /**
     * Finds voyages by origin and/or destination.
     *
     * Note that [NewVoyage.originPort] and [NewVoyage.destinationPort] are area ids and refer to the EOSP of the port.
     * Querying by sub ports is not supported at this moment. The [NewVoyage.originPort] and [NewVoyage.destinationPort]
     * refer to the EOSP of the main port.
     * TODO: extend this with sub ports
     *
     * This function needs at least one port to query on. Providing an empty list for both [originPortAreaIds] and
     * [destinationPortAreaIds] results in an error.
     *
     * @param originPortAreaIds Voyage port origin. Pass an empty list for any port.
     * @param destinationPortAreaIds Voyage destination port. Pass an empty list for any port.
     * @param start Voyage start is greater or equal to this value
     * @param end Voyage end is smaller or equal to this value. Note that the query for [finished] looks at
     * [NewVoyage.end] to be null or not!
     * @param limit Limits the result set to this given size. Limit is not applied when null.
     * @param finished Whether the voyages should be finished or not. ONGOING: end == null, FINISHED: end !=
     * null, ANY returns for both.
     */
    fun findByPortAreaIds(
        originPortAreaIds: List<String>,
        destinationPortAreaIds: List<String>,
        start: Instant?,
        end: Instant?,
        limit: Int?,
        finished: NewEntryFinishedFilter,
        qualifyingImos: Set<Int>
    ): List<NewVoyage> {
        val filters = buildList {
            addAll(queryPeriodWithOptionalStartAndEnd(start, end))
            if (originPortAreaIds.isNotEmpty()) {
                add(NewVoyage::originPort `in` originPortAreaIds.appenedEospSuffixIfNeeded())
            }
            if (destinationPortAreaIds.isNotEmpty()) {
                add(NewVoyage::destinationPort `in` destinationPortAreaIds.appenedEospSuffixIfNeeded())
            }
            add(queryFinishedFilter(finished))
            if (qualifyingImos.isNotEmpty()) {
                add(NewVoyage::imo `in` qualifyingImos)
            }
        }
        return collection.find(and(filters))
            .limitIfNotNull(limit)
            .toList()
    }

    /**
     * Helper function which adds ".eosp" to all items when it doesn't end with this.
     */
    private fun List<String>.appenedEospSuffixIfNeeded(): List<String> =
        this.map { id -> id.appenedEospSuffixIfNeeded() }

    private fun String.appenedEospSuffixIfNeeded(): String {
        return if (this.endsWith(".eosp")) {
            this
        } else {
            "$this.eosp"
        }
    }

    /**
     * Finds voyages by the AIS destination of the ship during the voyage. Note that the system uses the 'true
     * destination' to match on, as given by PortMatcher.
     */
    fun findByAisDestination(
        aisDestinationUnlocodes: List<String>,
        start: Instant?,
        end: Instant?,
        limit: Int?,
        finished: NewEntryFinishedFilter,
        qualifyingImos: Set<Int>
    ): List<NewVoyage> {
        // Uppercase all unlocodes to always provide a value even when didn't provide a full uppercased unlocode
        val preparedUnlocodes = aisDestinationUnlocodes.map { unlocode -> unlocode.uppercase() }
        val filters = buildList {
            addAll(queryPeriodWithOptionalStartAndEnd(start, end))
            add(NewVoyage::destination / Destination::actual `in` preparedUnlocodes)
            add(queryFinishedFilter(finished))
            if (qualifyingImos.isNotEmpty()) {
                add(NewVoyage::imo `in` qualifyingImos)
            }
        }
        return collection.find(and(filters))
            .limitIfNotNull(limit)
            .toList()
    }

    /**
     * Check if there is any Voyage that is currently ongoing for the provided [imo] which started before the provided [before].
     *
     * @param imo The Ship we are checking.
     * @param before The timestamp our Voyage need to be started "before".
     * @return True when we found a Voyage or false when still in an ongoing Visit.
     */
    fun hasVoyageStartedBeforeForShip(imo: Int, before: Instant): Boolean {
        val result = collection.countDocuments(
            and(
                NewVoyage::imo eq imo,
                NewVoyage::start / LocationTime::time lt before,
                NewVoyage::end eq null
            ),
            CountOptions().limit(1)
        )

        return result == 1L
    }

    fun findNewestStartedBeforeByImo(imo: Int, before: Instant): NewVoyage? = collection.find(
        and(
            NewVoyage::imo eq imo,
            // Use 'lt' and not 'lte', VesselVoyage might be corrupted in such a way that a visit>voyage>visit
            // all have the same startTime, taking an equal time would not get the voyage before the first visit.
            NewVoyage::start / LocationTime::time lt before
        )
    ).descendingSort(NewVoyage::start / LocationTime::time).limit(1).firstOrNull()

    fun findOldestEndedAfterByImo(imo: Int, start: Instant, after: Instant): NewVoyage? = collection.find(
        and(
            NewVoyage::imo eq imo,
            NewVoyage::start / LocationTime::time gte start,
            or(
                NewVoyage::end eq null,
                NewVoyage::end / LocationTime::time gte after
            )
        )
    ).ascendingSort(NewVoyage::start / LocationTime::time).limit(1).firstOrNull()

    override fun queryConfirmedFilter(confirmed: Boolean?): Bson? {
        // Voyages can't be filtered based on the confirmed flag
        return null
    }

    fun findMinimalByPortAreaIdAndImos(
        originPortAreaId: String,
        imos: Set<Int>,
        finished: NewEntryFinishedFilter
    ): List<MinimalNewVoyage> {
        val preparedPortAreaId = originPortAreaId.appenedEospSuffixIfNeeded()
        val filters = buildList {
            // Only filter on imos if the provided set is not empty, otherwise we just want all of them
            if (imos.isNotEmpty()) {
                add(NewVisit::imo `in` imos)
            }

            add(NewVoyage::originPort eq preparedPortAreaId)
            add(queryFinishedFilter(finished))
        }

        return collection.withDocumentClass<MinimalNewVoyage>()
            .find(and(filters))
            .projection(
                MinimalNewVoyage::_id,
                MinimalNewVoyage::imo,
                MinimalNewVoyage::start,
                MinimalNewVoyage::destination,
                MinimalNewVoyage::previous,
                MinimalNewVoyage::originPort,
                MinimalNewVoyage::destinationPort
            )
            .toList()
    }

    fun findMinimalByAisDestinationAndImos(
        aisDestinationUnlocode: String,
        imos: Set<Int>,
        finished: NewEntryFinishedFilter
    ): List<MinimalNewVoyage> {
        // Uppercase all unlocodes to always provide a value even when didn't provide a full uppercased unlocode
        val preparedUnlocode = aisDestinationUnlocode.uppercase()
        val filters = buildList {
            // Only filter on imos if the provided set is not empty, otherwise we just want all of them
            if (imos.isNotEmpty()) {
                add(NewVisit::imo `in` imos)
            }

            add(NewVoyage::destination / Destination::actual eq preparedUnlocode)
            add(queryFinishedFilter(finished))
        }

        return collection.withDocumentClass<MinimalNewVoyage>()
            .find(and(filters))
            .projection(
                MinimalNewVoyage::_id,
                MinimalNewVoyage::imo,
                MinimalNewVoyage::start,
                MinimalNewVoyage::destination,
                MinimalNewVoyage::previous,
                MinimalNewVoyage::originPort,
                MinimalNewVoyage::destinationPort
            )
            .toList()
    }
}
