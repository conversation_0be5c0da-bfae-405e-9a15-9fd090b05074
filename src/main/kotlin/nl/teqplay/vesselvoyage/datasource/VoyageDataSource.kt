package nl.teqplay.vesselvoyage.datasource

import com.mongodb.client.model.ReplaceOptions
import com.mongodb.kotlin.client.FindIterable
import com.mongodb.kotlin.client.MongoCollection
import com.mongodb.kotlin.client.MongoDatabase
import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.skeleton.datasource.kmongo.and
import nl.teqplay.skeleton.datasource.kmongo.ascendingSort
import nl.teqplay.skeleton.datasource.kmongo.deleteOneById
import nl.teqplay.skeleton.datasource.kmongo.descendingSort
import nl.teqplay.skeleton.datasource.kmongo.div
import nl.teqplay.skeleton.datasource.kmongo.ensureIndex
import nl.teqplay.skeleton.datasource.kmongo.eq
import nl.teqplay.skeleton.datasource.kmongo.findOneById
import nl.teqplay.skeleton.datasource.kmongo.gte
import nl.teqplay.skeleton.datasource.kmongo.`in`
import nl.teqplay.skeleton.datasource.kmongo.lt
import nl.teqplay.skeleton.datasource.kmongo.or
import nl.teqplay.skeleton.datasource.kmongo.replaceOneById
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.datasource.util.FindShipsByCategory
import nl.teqplay.vesselvoyage.datasource.util.resolveQueryImos
import nl.teqplay.vesselvoyage.model.Destination
import nl.teqplay.vesselvoyage.model.Voyage
import nl.teqplay.vesselvoyage.model.VoyageQuery
import org.springframework.stereotype.Component
import java.time.ZonedDateTime

private val log = KotlinLogging.logger {}

@ProfileProcessing
@ProfileApi
@Component
class VoyageDataSource(private val database: MongoDatabase) : EntryDataSource<Voyage>() {
    override fun initCollection(): MongoCollection<Voyage> {
        return database.getCollection<Voyage>("voyages").apply {
            // for findByIMO(), findByIMOs()
            // and is also used by findRecentByIMO()
            ensureIndex(Voyage::imo, Voyage::startTime, Voyage::endTime)

            // for findByDestination()
            ensureIndex(Voyage::finished, Voyage::dest / Destination::trueDestination, Voyage::startTime)

            // for findByOrigin()
            ensureIndex(Voyage::finished, Voyage::startPortIds, Voyage::startTime)

            // for find()
            // This can have any combination of optional fields, so we cannot create a single index matching all options.
            // Relying on mongo to use those indexes as smart as possible (maybe it can use index intersection)
            ensureIndex(
                Voyage::startTime,
                Voyage::endTime
            )
            ensureIndex(Voyage::startPortIds)
            ensureIndex(Voyage::endPortIds)
            ensureIndex(Voyage::imo) // used to search individual ships or a list with ships of a specific type
            ensureIndex(Voyage::finished)
        }
    }

    fun findById(voyageId: String) = collection.findOneById(voyageId)

    fun findByIds(voyageIds: List<String>) = collection.find(Voyage::_id `in` voyageIds).toList()

    fun findByIMO(imo: String, start: ZonedDateTime, endExcl: ZonedDateTime, limit: Int) = collection
        .find(
            and(
                listOf(Voyage::imo eq imo) +
                    queryPeriod(start, endExcl)
            )
        )
        .ascendingSort(Voyage::startTime)
        .limit(limit)
        .toList()

    fun findByIMOs(imos: List<String>, start: ZonedDateTime, endExcl: ZonedDateTime, limit: Int) = collection
        .find(
            and(
                listOf(Voyage::imo `in` imos) +
                    queryPeriod(start, endExcl)
            )
        )
        .ascendingSort(Voyage::startTime)
        .limit(limit)

    fun findByDestination(destination: String, imos: Set<String>?) = collection
        .find(
            and(
                Voyage::finished eq false,
                Voyage::dest / Destination::trueDestination eq destination,
                imos?.let { Voyage::imo `in` it }
            )
        )
        .ascendingSort(Voyage::startTime)

    fun findByOrigin(origin: String, imos: Set<String>?) = collection
        .find(
            and(
                Voyage::finished eq false,
                Voyage::startPortIds `in` origin,
                imos?.let { Voyage::imo `in` it }
            )
        )
        .ascendingSort(Voyage::startTime)

    fun findRecentByIMO(imo: String, limit: Int = 10) = collection
        .find(Voyage::imo eq imo)
        .descendingSort(Voyage::startTime)
        .limit(limit)

    data class LastTwoVoyages(
        val lastVoyage: Voyage?,
        val secondLastVoyage: Voyage?
    )

    fun findLastTwoByIMO(imo: String): LastTwoVoyages {
        val voyages = findRecentByIMO(imo, 2).toList()

        return LastTwoVoyages(
            lastVoyage = voyages.getOrNull(0),
            secondLastVoyage = voyages.getOrNull(1)
        )
    }

    fun find(imos: Set<String>, destination: String?, origin: String?): FindIterable<Voyage> {
        return collection.find(
            and(
                Voyage::finished eq false,
                origin?.let { Voyage::startPortIds `in` it },
                destination?.let { Voyage::dest / Destination::trueDestination eq it },
            )
        ).ascendingSort(Voyage::startTime)
    }

    fun find(query: VoyageQuery, findShipsByType: FindShipsByCategory): FindIterable<Voyage> {
        require(query.startTime <= query.endTime) { "end time should be after start time" }

        if (query.finished == false) {
            require(query.endPortIds.isNullOrEmpty()) { "end ports must be empty when querying for non-finished voyages" }
        }

        val imos = resolveQueryImos(query.imos, query.shipCategoriesV2, query.shipCategories, findShipsByType)

        log.debug { "find query: $query, number of imos: ${imos?.size ?: "(none)"}" }

        return collection
            .find(
                and(
                    queryPeriod(query.startTime, query.endTime) + listOfNotNull(
                        // Note that there is no database index on both startPortIds and endPortIds,
                        // MongoDB doesn't support two indexes on arrays. We could create a solution where we
                        // create capture the start/end port ids in a single array, but that turns out not to
                        // be faster in practice (at the time of writing), so we stick with the simple solution
                        query.startPortIds?.let { portIds ->
                            Voyage::startPortIds `in` portIds
                        },
                        query.endPortIds?.let { portIds ->
                            Voyage::endPortIds `in` portIds
                        },

                        imos?.let {
                            Voyage::imo `in` imos
                        },

                        query.finished?.let {
                            Voyage::finished eq query.finished
                        },
                    )
                )
            )
            .ascendingSort(Voyage::startTime)
            .limit(query.limit)
    }

    fun createOrReplace(voyage: Voyage): Voyage {
        collection.replaceOneById(voyage._id, voyage, ReplaceOptions().upsert(true))
        return voyage
    }

    fun deleteById(voyageId: String) {
        collection.deleteOneById(voyageId)
    }

    fun deleteAllByIMO(imo: String) {
        val result = collection.deleteMany(Voyage::imo eq imo)
        log.debug { "Deleted all voyages of ship with IMO $imo, count: ${result.deletedCount}" }
    }

    fun findNewestStartedBeforeByImo(imo: String, before: ZonedDateTime): Voyage? = collection.find(
        and(
            Voyage::imo eq imo,
            // Use 'lt' and not 'lte', VesselVoyage might be corrupted in such a way that a visit>voyage>visit
            // all have the same startTime, taking an equal time would not get the voyage before the first visit.
            Voyage::startTime lt before
        )
    ).descendingSort(Voyage::startTime).limit(1).firstOrNull()

    fun findOldestEndedAfterByImo(imo: String, start: ZonedDateTime, after: ZonedDateTime): Voyage? = collection.find(
        and(
            Voyage::imo eq imo,
            Voyage::startTime gte start,
            or(
                Voyage::endTime eq null,
                Voyage::endTime gte after
            )
        )
    ).ascendingSort(Voyage::startTime).limit(1).firstOrNull()
}
