package nl.teqplay.vesselvoyage.datasource

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.skeleton.datasource.kmongo.ensureIndex
import nl.teqplay.skeleton.datasource.kmongo.eq
import nl.teqplay.skeleton.datasource.kmongo.replaceOneById
import nl.teqplay.skeleton.datasource.kmongo.setTo
import nl.teqplay.skeleton.datasource.kmongo.updateOneById
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.model.internal.AutomaticRecalculationShip
import org.springframework.stereotype.Component

@ProfileProcessing
@Component
class AutomaticRecalculationShipDataSource(database: MongoDatabase) {
    private val collection = database.getCollection<AutomaticRecalculationShip>("automaticRecalculations")
        .apply { ensureIndex(AutomaticRecalculationShip::state) }

    fun getAll(): List<AutomaticRecalculationShip> {
        return collection.find().toList()
    }

    fun getAllByState(state: AutomaticRecalculationShip.RecalculationState, limit: Int): List<AutomaticRecalculationShip> {
        return collection.find(AutomaticRecalculationShip::state eq state)
            .limit(limit)
            .toList()
    }

    fun insert(ship: AutomaticRecalculationShip) {
        collection.insertOne(ship)
    }

    fun update(ship: AutomaticRecalculationShip) {
        collection.replaceOneById(ship.imo, ship)
    }

    fun updateState(imo: Int, newState: AutomaticRecalculationShip.RecalculationState) {
        collection.updateOneById(imo, AutomaticRecalculationShip::state setTo newState)
    }
}
