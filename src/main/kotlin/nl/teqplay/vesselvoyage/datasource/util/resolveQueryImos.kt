package nl.teqplay.vesselvoyage.datasource.util

import nl.teqplay.csi.model.ship.info.component.ShipCategoryV1
import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2
import nl.teqplay.vesselvoyage.util.toV2Categories

typealias FindShipsByCategory = (shipType: ShipCategoryV2) -> Set<String>

fun resolveQueryImos(imos: Set<String>?, shipCategories: Set<ShipCategoryV2>?, shipV1Categories: Set<ShipCategoryV1>?, findShipsByCategory: FindShipsByCategory): Set<String>? {
    val imosWithMatchingCategories: Set<String>? = shipCategories
        ?.ifEmpty { shipV1Categories?.toV2Categories() }
        ?.flatMap { findShipsByCategory(it) }
        ?.toSet()

    return if (imos != null) {
        if (imosWithMatchingCategories != null) {
            // filter the IMO's that have the specified shipType (basically an AND operation)
            imos.filter { imosWithMatchingCategories.contains(it) }.toSet()
        } else {
            imos.toSet()
        }
    } else {
        imosWithMatchingCategories
    }
}
