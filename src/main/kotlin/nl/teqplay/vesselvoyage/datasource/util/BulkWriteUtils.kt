package nl.teqplay.vesselvoyage.datasource.util

import com.mongodb.WriteConcern
import com.mongodb.client.model.BulkWriteOptions
import com.mongodb.client.model.DeleteOneModel
import com.mongodb.client.model.InsertOneModel
import com.mongodb.client.model.ReplaceOneModel
import com.mongodb.kotlin.client.MongoCollection
import nl.teqplay.skeleton.datasource.kmongo.eq
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.v2.EntryDatabaseObject
import nl.teqplay.vesselvoyage.model.v2.NewChange

/**
 * Performs a bulk write operation on the given MongoDB collection.
 * This function prepares the write operations based on the provided changes and sorts them to ensure
 *  that delete operations are processed first.
 *
 * @param changes The list of changes to apply, each containing an action and a value.
 */
fun <T : EntryDatabaseObject, U : NewChange<T>> MongoCollection<T>.bulkWriteChanges(changes: List<U>) {
    val preparedWrites = changes.map { change ->
        val changeValue = change.value

        when (change.action) {
            Action.CREATE -> InsertOneModel(changeValue)
            Action.UPDATE,
            Action.REVISE -> ReplaceOneModel(EntryDatabaseObject::_id eq changeValue._id, changeValue)
            Action.DELETE,
            Action.DISCONTINUE -> DeleteOneModel(EntryDatabaseObject::_id eq changeValue._id)
        }
    }

    // Sort the writes so that deletes are processed first to avoid inserting issues
    val sortedWrites = preparedWrites.sortedBy { action -> action is DeleteOneModel }

    // Use a collection with WriteConcern.MAJORITY to ensure that the writes are acknowledged by the majority of nodes
    val collectionWithWc = this.withWriteConcern(WriteConcern.MAJORITY)
    collectionWithWc.bulkWrite(sortedWrites, BulkWriteOptions())
}
