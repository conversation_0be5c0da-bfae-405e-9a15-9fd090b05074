package nl.teqplay.vesselvoyage.datasource

import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter
import org.bson.conversions.Bson
import java.time.Instant

interface BaseApiDataSource<T : NewEntry> {
    fun findById(id: String): T?
    fun findByIds(id: Set<String>): List<T>
    fun findByImoStartedAfter(imo: Int, start: Instant, finished: NewEntryFinishedFilter, confirmed: Boolean?): List<T>
    fun findByImoAndTimeRange(imo: Int, start: Instant, end: Instant, finished: NewEntryFinishedFilter, confirmed: Boolean?): List<T>
    fun findLastByImo(imo: Int, limit: Int, finished: NewEntryFinishedFilter, confirmed: Boolean?): List<T>
    fun getLimitedEntries(limited: Boolean, imos: Set<Int>, limit: Int, skip: Int): List<T>
    fun countLimitedEntries(limited: Boolean, imos: Set<Int>): Long
    fun findAllWithPagination(limit: Int, skip: Int): List<T>
    fun countAllEntries(): Long

    /**
     * Create a [Bson] query filter when a [finished] param is provided.
     */
    fun queryFinishedFilter(finished: NewEntryFinishedFilter): Bson?

    /**
     * Create a [Bson] query filter based on the confirmed field when we are querying visits.
     */
    fun queryConfirmedFilter(confirmed: Boolean?): Bson?
}
