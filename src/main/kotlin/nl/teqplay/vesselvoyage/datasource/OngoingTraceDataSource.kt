package nl.teqplay.vesselvoyage.datasource

import com.mongodb.client.model.ReplaceOneModel
import com.mongodb.client.model.ReplaceOptions
import com.mongodb.kotlin.client.MongoDatabase
import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.skeleton.datasource.kmongo.and
import nl.teqplay.skeleton.datasource.kmongo.ensureIndex
import nl.teqplay.skeleton.datasource.kmongo.eq
import nl.teqplay.skeleton.datasource.kmongo.gte
import nl.teqplay.skeleton.datasource.kmongo.lt
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.model.ONGOING_TRACE_WRITE_BULK_SIZE
import nl.teqplay.vesselvoyage.model.OngoingTrace
import org.springframework.stereotype.Component
import java.time.ZonedDateTime

private val log = KotlinLogging.logger {}

@ProfileProcessing
@ProfileApi
@Component
class OngoingTraceDataSource(database: MongoDatabase) {
    private val collection = database.getCollection<OngoingTrace>("ongoingTraces").apply {
        ensureIndex(OngoingTrace::imo, OngoingTrace::maxTime)
        ensureIndex(OngoingTrace::maxTime)
    }

    // we don't have a limit on this request: we assume that the amount of data
    // of a single ship is small enough not to give any memory issues (famous last words?)
    fun findByIMO(imo: String, startTime: ZonedDateTime) = collection.find(
        and(
            OngoingTrace::imo eq imo,
            OngoingTrace::maxTime gte startTime
        )
    )

    fun upsert(ongoingTrace: OngoingTrace) {
        collection.replaceOne(
            OngoingTrace::_id eq ongoingTrace._id,
            ongoingTrace,
            ReplaceOptions().upsert(true)
        )
    }

    fun upsertAll(traces: List<OngoingTrace>) {
        traces.chunked(ONGOING_TRACE_WRITE_BULK_SIZE) { chuckedTraces ->
            try {
                collection.bulkWrite(
                    chuckedTraces.map { ongoingTrace ->
                        ReplaceOneModel(
                            OngoingTrace::_id eq ongoingTrace._id,
                            ongoingTrace,
                            ReplaceOptions().upsert(true)
                        )
                    }
                )
            } catch (err: Exception) {
                log.error(err) {
                    "Failed to write $ONGOING_TRACE_WRITE_BULK_SIZE ongoing traces in bulk. " +
                        "Will now try one by one. " +
                        "To fix this: decrease the number of docs written in bulk."
                }

                chuckedTraces.forEach { ongoingTrace ->
                    upsert(ongoingTrace)
                }
            }
        }
    }

    fun deleteAllByIMO(imo: String) {
        collection.deleteMany(OngoingTrace::imo eq imo)
    }

    fun deleteAllOlderThan(time: ZonedDateTime): Long {
        return collection.deleteMany(OngoingTrace::maxTime lt time).deletedCount
    }
}
