package nl.teqplay.vesselvoyage.datasource

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.skeleton.datasource.kmongo.deleteOneById
import nl.teqplay.skeleton.datasource.kmongo.save
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.model.internal.PostProcessable
import nl.teqplay.vesselvoyage.model.v2.EntryId
import org.springframework.stereotype.Component

@ProfileProcessing
@Component
class PostProcessableDataSource(database: MongoDatabase) {
    private val collection = database.getCollection<PostProcessable>("postProcessable")

    fun getAll(): List<PostProcessable> {
        return collection.find().toList()
    }

    fun save(postProcessable: PostProcessable) {
        collection.save(postProcessable)
    }

    fun remove(entryId: EntryId) {
        collection.deleteOneById(entryId)
    }
}
