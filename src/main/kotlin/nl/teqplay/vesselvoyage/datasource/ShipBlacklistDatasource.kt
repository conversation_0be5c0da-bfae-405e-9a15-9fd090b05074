package nl.teqplay.vesselvoyage.datasource

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.model.internal.ShipBlacklist
import org.springframework.stereotype.Repository

/**
 * Datasource for blacklisted ships, events will not be processed for these ships
 */
@ProfileProcessing
@Repository
class ShipBlacklistDatasource(
    private val database: MongoDatabase
) {
    private val collection = database.getCollection<ShipBlacklist>("blacklist")

    fun getAll(): List<Int> {
        return collection.find().toList().map { it.imo }
    }
}
