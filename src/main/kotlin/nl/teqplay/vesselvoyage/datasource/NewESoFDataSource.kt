package nl.teqplay.vesselvoyage.datasource

import com.mongodb.client.model.ReplaceOptions
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.skeleton.datasource.kmongo.deleteOneById
import nl.teqplay.skeleton.datasource.kmongo.eq
import nl.teqplay.skeleton.datasource.kmongo.findOneById
import nl.teqplay.skeleton.datasource.kmongo.`in`
import nl.teqplay.skeleton.datasource.kmongo.replaceOneById
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.datasource.util.bulkWriteChanges
import nl.teqplay.vesselvoyage.model.v2.ESoFChange
import nl.teqplay.vesselvoyage.model.v2.EntryId
import nl.teqplay.vesselvoyage.model.v2.NewESoF
import org.springframework.stereotype.Component

@ProfileProcessing
@ProfileApi
@Component
class NewESoFDataSource(database: MongoDatabase) {
    val collection = database.getCollection<NewESoF>("esofV2")

    fun findById(entryId: EntryId): NewESoF? {
        return collection.findOneById(entryId)
    }

    fun findByIds(entryIds: List<String>): List<NewESoF> {
        return collection.find(NewESoF::_id `in` entryIds).toList()
    }

    fun createOrReplace(newOrUpdatedESoF: NewESoF) {
        collection.replaceOneById(newOrUpdatedESoF._id, newOrUpdatedESoF, ReplaceOptions().upsert(true))
    }

    fun deleteById(entryId: EntryId) {
        collection.deleteOneById(entryId)
    }

    fun deleteAllByIds(entryIds: List<String>) {
        collection.deleteMany(NewESoF::_id `in` entryIds)
    }

    fun findAllNonPostProcessed(): List<NewESoF> {
        return collection.find(NewESoF::postProcessed eq false)
            .toList()
    }

    fun bulkWriteChanges(entries: List<ESoFChange>) {
        collection.bulkWriteChanges(entries)
    }
}
