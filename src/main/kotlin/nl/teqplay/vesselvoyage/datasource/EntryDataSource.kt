package nl.teqplay.vesselvoyage.datasource

import com.mongodb.client.result.DeleteResult
import com.mongodb.kotlin.client.MongoCollection
import nl.teqplay.skeleton.datasource.kmongo.and
import nl.teqplay.skeleton.datasource.kmongo.ascending
import nl.teqplay.skeleton.datasource.kmongo.eq
import nl.teqplay.skeleton.datasource.kmongo.findOneById
import nl.teqplay.skeleton.datasource.kmongo.gte
import nl.teqplay.skeleton.datasource.kmongo.lte
import nl.teqplay.skeleton.datasource.kmongo.or
import nl.teqplay.vesselvoyage.model.Entry
import org.bson.conversions.Bson
import java.time.ZonedDateTime

abstract class EntryDataSource<T : Entry> {

    protected val collection by lazy { initCollection() }

    abstract fun initCollection(): MongoCollection<T>

    /**
     * Get all entries between a given [startTime] and [endTime]
     *
     * - the entry has an [Entry.startTime] before [endTime], i.e. the entry isn't starting after our query period.
     * - the entry has no end time, i.e. the entry is still going on
     * - the entry has an [Entry.endTime] before [startTime], i.e. the entry hasn't ended before our query period
     */
    fun queryPeriod(startTime: ZonedDateTime, endTime: ZonedDateTime): List<Bson> {
        return listOf(
            Entry::startTime lte endTime,
            or(
                Entry::endTime gte startTime,
                Entry::endTime eq null
            )
        )
    }

    fun findByPreviousId(current: Entry?): T? {
        return current?.previousEntryId?.let { collection.findOneById(it) }
    }

    fun findFirst(imo: String, startTime: ZonedDateTime, endTime: ZonedDateTime): T? {
        val filter = and(
            listOf(Entry::imo eq imo) + queryPeriod(startTime, endTime)
        )

        return collection.find(filter)
            .sort(ascending(Entry::startTime))
            .firstOrNull()
    }

    fun delete(imo: String, startTime: ZonedDateTime, endTime: ZonedDateTime): DeleteResult {
        return collection.deleteMany(
            and(
                listOf(Entry::imo eq imo) + queryPeriod(startTime, endTime)
            )
        )
    }
}
