package nl.teqplay.vesselvoyage.datasource

import com.mongodb.client.model.ReplaceOptions
import com.mongodb.kotlin.client.MongoDatabase
import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.skeleton.datasource.kmongo.and
import nl.teqplay.skeleton.datasource.kmongo.findOneById
import nl.teqplay.skeleton.datasource.kmongo.`in`
import nl.teqplay.skeleton.datasource.kmongo.replaceOneById
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.model.VesselRecalculation
import org.springframework.stereotype.Component

private val log = KotlinLogging.logger {}

@ProfileProcessing
@Component
class CalculationInfoDataSource(database: MongoDatabase) {
    private val collection = database.getCollection<VesselRecalculation>("recalculations")

    fun findAll() = collection.find()

    fun find(imo: String) = collection.findOneById(imo)

    fun insertMany(imos: List<VesselRecalculation>) {
        collection.insertMany(imos)
    }

    fun createOrReplace(recalculation: VesselRecalculation): VesselRecalculation {
        collection.replaceOneById(recalculation._id, recalculation, ReplaceOptions().upsert(true))
        return recalculation
    }

    fun deleteAll() {
        val result = collection.deleteMany(and())
        log.debug { "Deleted ${result.deletedCount} recalculation records" }
    }

    fun deleteManyByImo(imos: Set<String>) {
        val result = collection.deleteMany(VesselRecalculation::_id `in` imos)
        log.debug { "Deleted ${result.deletedCount} recalculation records" }
    }
}
