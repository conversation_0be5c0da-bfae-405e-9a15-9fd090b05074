package nl.teqplay.vesselvoyage.datasource

import com.mongodb.client.model.CountOptions
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.skeleton.datasource.kmongo.deleteOneById
import nl.teqplay.skeleton.datasource.kmongo.eq
import nl.teqplay.skeleton.datasource.kmongo.findOneById
import nl.teqplay.skeleton.datasource.kmongo.`in`
import nl.teqplay.skeleton.datasource.kmongo.replaceOneById
import nl.teqplay.skeleton.datasource.kmongo.save
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.model.v2.EntryId
import nl.teqplay.vesselvoyage.model.v2.NewTrace
import org.springframework.stereotype.Component

@ProfileProcessing
@ProfileApi
@Component
class NewTraceDataSource(database: MongoDatabase) {
    protected val collection = database.getCollection<NewTrace>("traceV2")

    fun findById(entryId: EntryId): NewTrace? {
        return collection.findOneById(entryId)
    }

    fun findByIds(entryIds: List<EntryId>): List<NewTrace> {
        return collection.find(NewTrace::_id `in` entryIds).toList()
    }

    fun exists(entryId: EntryId): Boolean {
        return collection.countDocuments(NewTrace::_id eq entryId, CountOptions().limit(1)) > 0
    }

    fun insertOrUpdate(newTrace: NewTrace) {
        collection.save(newTrace)
    }

    fun replace(updatedTrace: NewTrace) {
        collection.replaceOneById(updatedTrace._id, updatedTrace)
    }

    fun deleteById(entryId: EntryId) {
        collection.deleteOneById(entryId)
    }

    fun deleteByEntryIds(entryIds: List<EntryId>) {
        collection.deleteMany(NewTrace::_id `in` entryIds)
    }
}
