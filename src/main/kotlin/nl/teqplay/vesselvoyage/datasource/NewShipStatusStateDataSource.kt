package nl.teqplay.vesselvoyage.datasource

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.skeleton.datasource.kmongo.findOneById
import nl.teqplay.skeleton.datasource.kmongo.save
import nl.teqplay.vesselvoyage.annotation.ProfileApi
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.model.v2.NewShipStatus
import org.bson.codecs.pojo.annotations.BsonId
import org.springframework.stereotype.Component

data class ShipStatusState(
    @BsonId val imo: Int,
    val eventBuffer: List<Event>
)

@ProfileProcessing
@ProfileApi
@Component
class NewShipStatusStateDataSource(database: MongoDatabase) {

    private val collection = database.getCollection<ShipStatusState>("shipStatusV2")

    fun get(imo: Int): ShipStatusState? {
        return collection.findOneById(imo)
    }

    fun save(imo: Int, status: NewShipStatus) {
        collection.save(ShipStatusState(imo, status.eventBuffer))
    }
}
