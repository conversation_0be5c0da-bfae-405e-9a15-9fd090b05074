package nl.teqplay.vesselvoyage.datasource

import com.fasterxml.jackson.core.type.TypeReference
import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.vesselvoyage.util.globalObjectMapper
import org.springframework.stereotype.Component
import java.io.File

private val log = KotlinLogging.logger {}

@Component
class TempFileDataSource {
    val tmpdir: String = System.getProperty("java.io.tmpdir")

    fun <T> readFromFile(filename: String, typeReference: TypeReference<T>): T? {
        val file = File(tmpdir + filename)

        if (!file.exists()) {
            return null
        }

        val data = try {
            log.info { "Reading file $file..." }

            val start = System.currentTimeMillis()
            val data: T = globalObjectMapper.readValue(file, typeReference)
            val end = System.currentTimeMillis()

            log.info { "Done reading file $file (duration: ${end - start} ms)" }

            return data
        } catch (err: Throwable) {
            log.warn { "Failed reading file $file: ${err.message}" }
            null
        }

        return data
    }

    fun <T> writeToFile(filename: String, data: T): Boolean {
        val file = File(tmpdir + filename)

        log.info { "Writing to file $file..." }

        return try {
            val start = System.currentTimeMillis()
            globalObjectMapper.writeValue(file, data)
            val end = System.currentTimeMillis()

            log.info { "Done writing to file $file (duration: ${end - start} ms)" }

            true
        } catch (err: Throwable) {
            log.warn { "Failed writing to file $file: ${err.message}" }

            false
        }
    }
}
