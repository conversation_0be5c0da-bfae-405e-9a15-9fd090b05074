package nl.teqplay.vesselvoyage.datasource

import com.mongodb.client.model.ReplaceOneModel
import com.mongodb.kotlin.client.MongoCollection
import com.mongodb.kotlin.client.MongoDatabase
import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.skeleton.datasource.kmongo.and
import nl.teqplay.skeleton.datasource.kmongo.ascendingSort
import nl.teqplay.skeleton.datasource.kmongo.deleteOneById
import nl.teqplay.skeleton.datasource.kmongo.descendingSort
import nl.teqplay.skeleton.datasource.kmongo.div
import nl.teqplay.skeleton.datasource.kmongo.ensureIndex
import nl.teqplay.skeleton.datasource.kmongo.eq
import nl.teqplay.skeleton.datasource.kmongo.findOneById
import nl.teqplay.skeleton.datasource.kmongo.gte
import nl.teqplay.skeleton.datasource.kmongo.`in`
import nl.teqplay.skeleton.datasource.kmongo.lte
import nl.teqplay.skeleton.datasource.kmongo.ne
import nl.teqplay.skeleton.datasource.kmongo.or
import nl.teqplay.skeleton.datasource.kmongo.save
import nl.teqplay.vesselvoyage.datasource.util.bulkWriteChanges
import nl.teqplay.vesselvoyage.model.v2.Destination
import nl.teqplay.vesselvoyage.model.v2.EntryId
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewChange
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter
import nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter.ANY
import nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter.FINISHED
import nl.teqplay.vesselvoyage.model.v2.NewEntryFinishedFilter.ONGOING
import nl.teqplay.vesselvoyage.model.v2.NewStop
import nl.teqplay.vesselvoyage.util.mergeStops
import org.bson.conversions.Bson
import java.time.Instant

abstract class NewEntryDataSource<T : NewEntry>(
    private val database: MongoDatabase
) : BaseApiDataSource<T> {
    private val log = KotlinLogging.logger {}

    protected val collection = initCollection()
        .also(::applyIndexes)

    abstract val collectionName: String
    abstract val clazz: Class<T>

    private fun initCollection(): MongoCollection<T> {
        return database.getCollection(collectionName, clazz)
    }

    /**
     * Ensure the indexes for our [NewEntry] exist.
     */
    protected open fun applyIndexes(collection: MongoCollection<T>) {
        collection.ensureIndex(NewEntry::limited)

        // Index used to load in recent ship state based on start time
        collection.ensureIndex(
            NewEntry::start / LocationTime::time
        )

        // Index used to load in the ship state
        collection.ensureIndex(
            NewEntry::imo,
            NewEntry::start / LocationTime::time
        )
    }

    /**
     * Get all entries between a given [startTime] and [endTime]
     *
     * - the entry has an [NewEntry.start] before [endTime], i.e. the entry isn't starting after our query period.
     * - the entry has no end time, i.e. the entry is still going on
     * - the entry has an [NewEntry.end] before [startTime], i.e. the entry hasn't ended before our query period
     */
    private fun queryPeriod(startTime: Instant, endTime: Instant): List<Bson> {
        return listOf(
            NewEntry::start / LocationTime::time lte endTime,
            or(
                NewEntry::end / LocationTime::time gte startTime,
                NewEntry::end / LocationTime::time eq null
            )
        )
    }

    /**
     * Returns filters for entries based on optional [startTime] and [endTime]:
     *
     * - If both are provided, returns entries overlapping the period ([queryPeriod]).
     * - If only [startTime] is provided, returns entries starting at or after [startTime].
     * - If only [endTime] is provided, returns entries ended at or before [endTime].
     * - If neither is provided, returns all entries.
     */
    fun queryPeriodWithOptionalStartAndEnd(startTime: Instant?, endTime: Instant?): List<Bson> {
        return when {
            startTime != null && endTime != null -> queryPeriod(startTime, endTime)
            startTime != null -> listOf(NewEntry::start / LocationTime::time gte startTime)
            endTime != null -> listOf(NewEntry::end / LocationTime::time lte endTime)
            else -> emptyList()
        }
    }

    fun findAllByImo(imo: Int): List<T> {
        return collection.find(NewEntry::imo eq imo)
            .toList()
    }

    fun createOrReplace(newOrUpdatedEntry: T): T {
        collection.save(newOrUpdatedEntry)
        return newOrUpdatedEntry
    }

    fun deleteById(entryId: EntryId) {
        collection.deleteOneById(entryId)
    }

    fun deleteAllByImo(imo: Int) {
        collection.deleteMany(NewEntry::imo eq imo)
    }

    override fun findById(id: EntryId): T? {
        return collection.findOneById(id)
    }

    override fun findByIds(id: Set<String>): List<T> {
        return collection.find(NewEntry::_id `in` id).toList()
    }

    fun findByIds(id: Set<String>, finished: NewEntryFinishedFilter): List<T> {
        val filters = and(
            buildList {
                add(NewEntry::_id `in` id)
                add(queryFinishedFilter(finished))
            }
        )

        return collection.find(filters).toList()
    }

    override fun findByImoAndTimeRange(
        imo: Int,
        start: Instant,
        end: Instant,
        finished: NewEntryFinishedFilter,
        confirmed: Boolean?
    ): List<T> {
        val filters = and(
            buildList {
                add(NewEntry::imo eq imo)
                addAll(queryPeriod(start, end))
                add(queryFinishedFilter(finished))
                add(queryConfirmedFilter(confirmed))
            }
        )
        return collection.find(filters)
            .ascendingSort(NewEntry::start / LocationTime::time)
            .toList()
    }

    override fun findByImoStartedAfter(
        imo: Int,
        start: Instant,
        finished: NewEntryFinishedFilter,
        confirmed: Boolean?
    ): List<T> {
        val filters = and(
            buildList {
                addAll(queryPeriodWithOptionalStartAndEnd(start, null))
                add(NewEntry::imo eq imo)
                add(queryFinishedFilter(finished))
                add(queryConfirmedFilter(confirmed))
            }
        )
        return collection.find(filters)
            .ascendingSort(NewEntry::start / LocationTime::time)
            .toList()
    }

    override fun findLastByImo(
        imo: Int,
        limit: Int,
        finished: NewEntryFinishedFilter,
        confirmed: Boolean?
    ): List<T> {
        val filters = and(
            buildList {
                add(NewEntry::imo eq imo)
                add(queryFinishedFilter(finished))
                add(queryConfirmedFilter(confirmed))
            }
        )
        return collection.find(filters)
            .descendingSort(NewEntry::start / LocationTime::time)
            .limit(limit)
            .toList()
    }

    private fun getLimitedFilter(limited: Boolean, imos: Set<Int>): Bson {
        return if (imos.isEmpty()) {
            NewEntry::limited eq limited
        } else {
            and(
                NewEntry::limited eq limited,
                NewEntry::imo `in` imos
            )
        }
    }

    override fun getLimitedEntries(limited: Boolean, imos: Set<Int>, limit: Int, skip: Int): List<T> {
        val filter = getLimitedFilter(limited, imos)

        return collection.find(filter)
            .skip(skip)
            .limit(limit)
            .toList()
    }

    override fun countLimitedEntries(limited: Boolean, imos: Set<Int>): Long {
        val filter = getLimitedFilter(limited, imos)
        return collection.countDocuments(filter)
    }

    /**
     * Retrieves all entries with pagination support, without any filtering.
     * This method returns ALL entries in the database regardless of their limited status or any other filters.
     *
     * @param limit Maximum number of entries to return
     * @param skip Number of entries to skip (for pagination)
     * @return List of entries for the requested page
     */
    override fun findAllWithPagination(limit: Int, skip: Int): List<T> {
        return collection.find()
            .skip(skip)
            .limit(limit)
            .ascendingSort(NewEntry::start / LocationTime::time)
            .toList()
    }

    /**
     * Counts the total number of entries in the collection without any filtering.
     * This method returns the total count of ALL entries in the database.
     *
     * @return Total number of entries in the collection
     */
    override fun countAllEntries(): Long {
        return collection.countDocuments()
    }

    fun findPreviousEntry(current: NewEntry): T? {
        return current.previous?.let { previousEntryId -> findById(previousEntryId) }
    }

    fun findNextEntry(current: NewEntry): T? {
        return current.next?.let { nextEntryId -> findById(nextEntryId) }
    }

    fun distinctImos(startTime: Instant): Set<Int> {
        val filter = NewEntry::start / LocationTime::time gte startTime
        return collection.distinct<Int>("imo", filter)
            .toSet()
    }

    override fun queryFinishedFilter(finished: NewEntryFinishedFilter): Bson? {
        return when (finished) {
            FINISHED -> NewEntry::end ne null
            ONGOING -> NewEntry::end eq null
            ANY -> null
        }
    }

    protected fun queryAisTrueDestination(aisTrueDestination: String?): Bson? {
        return aisTrueDestination?.let { NewEntry::destination / Destination::actual eq it.uppercase() }
    }

    fun <U : NewChange<T>> bulkWriteChanges(entries: List<U>) {
        collection.bulkWriteChanges(entries)
    }

    fun fixBrokenStopsInBatches(updateEntryWithStops: (entry: T, mergedStops: List<NewStop>) -> T) {
        val batchSize = 100
        val batch = mutableListOf<ReplaceOneModel<T>>()
        var totalProcessed = 0
        val cursor = collection.find()
            .batchSize(batchSize)
            .cursor()

        cursor.use { cursor ->
            while (cursor.hasNext()) {
                val entry = cursor.next()
                val newStops = mergeStops(entry.stops, emptyList())
                totalProcessed += 1
                if (totalProcessed % 1000 == 0) {
                    log.info { "Processed $totalProcessed entries" }
                }

                if (newStops == entry.stops) {
                    // No need to fix, stops are already merged
                    continue
                }
                log.info { "Fixing broken stops for entry with ID: ${entry._id}" }

                // Fix the broken stops
                val fixedEntry = updateEntryWithStops(entry, newStops)
                val model = ReplaceOneModel<T>(
                    NewEntry::_id eq entry._id,
                    fixedEntry
                )
                batch.add(model)

                // If we reach our batch size, write to DB to avoid memory issues
                if (batch.size >= batchSize) {
                    collection.bulkWrite(batch)
                    batch.clear()
                }
            }

            // Write any remaining items
            if (batch.isNotEmpty()) {
                collection.bulkWrite(batch)
            }
        }
    }
}
