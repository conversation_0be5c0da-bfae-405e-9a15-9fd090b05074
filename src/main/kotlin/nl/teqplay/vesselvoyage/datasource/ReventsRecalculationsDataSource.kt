package nl.teqplay.vesselvoyage.datasource

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.skeleton.datasource.kmongo.and
import nl.teqplay.skeleton.datasource.kmongo.contains
import nl.teqplay.skeleton.datasource.kmongo.descending
import nl.teqplay.skeleton.datasource.kmongo.div
import nl.teqplay.skeleton.datasource.kmongo.eq
import nl.teqplay.skeleton.datasource.kmongo.findOneById
import nl.teqplay.skeleton.datasource.kmongo.gt
import nl.teqplay.skeleton.datasource.kmongo.`in`
import nl.teqplay.skeleton.datasource.kmongo.or
import nl.teqplay.skeleton.datasource.kmongo.save
import nl.teqplay.skeleton.datasource.kmongo.setTo
import nl.teqplay.skeleton.datasource.kmongo.updateOneById
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.model.RecalculationResult
import nl.teqplay.vesselvoyage.model.RecalculationShipResult
import nl.teqplay.vesselvoyage.model.RecalculationShipsResult
import nl.teqplay.vesselvoyage.model.ReventsRecalculationStatus
import org.springframework.stereotype.Component
import java.time.Duration
import java.time.Instant
import java.time.ZoneOffset

@ProfileProcessing
@Component
class ReventsRecalculationsDataSource(database: MongoDatabase) {

    private val collection = database.getCollection<RecalculationResult>("revents.recalculations")

    fun findAllRunning(): List<RecalculationResult> {
        val progressingPhases = listOf(ReventsRecalculationStatus.Phase.QUEUED, ReventsRecalculationStatus.Phase.PROGRESSING)
        return collection
            .find(RecalculationResult::revents / ReventsRecalculationStatus::phase `in` progressingPhases)
            .sort(descending(RecalculationResult::startedAt))
            .toList()
    }

    fun findAll(): List<RecalculationResult> =
        collection.find()
            .sort(descending(RecalculationResult::startedAt))
            .toList()

    fun findAllByImo(imo: Int): List<RecalculationResult> =
        collection.find(RecalculationShipResult::imo eq imo)
            .sort(descending(RecalculationResult::startedAt))
            .toList()

    fun findById(id: String): RecalculationResult? =
        collection.findOneById(id)

    fun save(recalculationResult: RecalculationResult) {
        collection.save(recalculationResult)
    }

    fun updateStatus(id: String, revents: ReventsRecalculationStatus) {
        collection.updateOneById(id, RecalculationResult::revents setTo revents)
    }

    /**
     * Find the first scenario ID containing data for a given IMO that was started within the last 30 days.
     * Only considers recalculations that have either finished successfully or partially failed.
     *
     * @param imo The IMO number of the ship to search for
     * @return The scenario ID of the most recent valid recalculation, or null if none found
     */
    fun firstScenarioIdByImo(imo: Int): String? {
        val maxAge = Instant.now().minus(Duration.ofDays(30))
        return collection.find(
            // Search for either single result or multiple ships results
            or(
                and(
                    RecalculationShipsResult::imos contains imo,
                    RecalculationResult::startedAt gt maxAge.atZone(ZoneOffset.UTC),
                    RecalculationResult::revents / ReventsRecalculationStatus::phase `in` listOf(
                        ReventsRecalculationStatus.Phase.FINISHED,
                        ReventsRecalculationStatus.Phase.PARTIALLY_FAILED
                    )
                ),
                and(
                    RecalculationShipResult::imo eq imo,
                    RecalculationResult::startedAt gt maxAge.atZone(ZoneOffset.UTC),
                    RecalculationResult::revents / ReventsRecalculationStatus::phase `in` listOf(
                        ReventsRecalculationStatus.Phase.FINISHED,
                        ReventsRecalculationStatus.Phase.PARTIALLY_FAILED
                    )
                )
            )
        ).sort(descending(RecalculationResult::startedAt))
            .limit(1)
            .firstOrNull()
            ?.revents
            ?.scenarioId
    }

    /**
     * Finds the most recent recalculation result for each provided IMO number.
     * Only considers recalculations from the last 30 days that have either
     * finished successfully or partially failed.
     *
     * @param imos List of IMO numbers to search for
     * @return Map of IMO numbers to their most recent recalculation results
     */
    fun findMostRecentResultsByImos(imos: List<Int>): Map<Int, RecalculationShipResult> {
        if (imos.isEmpty()) return emptyMap()

        val cutoffTime = Instant.now().minus(Duration.ofDays(30))

        // Retrieve all matching results sorted by startedAt in descending order
        val resultsList = collection.find(
            and(
                RecalculationShipResult::imo `in` imos,
                RecalculationResult::startedAt gt cutoffTime.atZone(ZoneOffset.UTC),
                RecalculationResult::revents / ReventsRecalculationStatus::phase `in` listOf(
                    ReventsRecalculationStatus.Phase.FINISHED,
                    ReventsRecalculationStatus.Phase.PARTIALLY_FAILED
                )
            )
        ).sort(descending(RecalculationResult::startedAt))
            .toList()
            .filterIsInstance<RecalculationShipResult>()

        // Since the list is sorted in descending order, the first occurrence per IMO is the most recent.
        return resultsList.associateBy { it.imo }
    }
}
