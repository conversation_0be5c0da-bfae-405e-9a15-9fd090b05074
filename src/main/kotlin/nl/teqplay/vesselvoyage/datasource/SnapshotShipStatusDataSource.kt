package nl.teqplay.vesselvoyage.datasource

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.skeleton.datasource.kmongo.ascending
import nl.teqplay.skeleton.datasource.kmongo.deleteOneById
import nl.teqplay.skeleton.datasource.kmongo.ensureIndex
import nl.teqplay.skeleton.datasource.kmongo.lte
import nl.teqplay.skeleton.datasource.kmongo.save
import nl.teqplay.vesselvoyage.annotation.ProfileProcessing
import nl.teqplay.vesselvoyage.model.internal.SnapshotShipStatus
import org.springframework.stereotype.Component
import java.time.ZonedDateTime

@ProfileProcessing
@Component
class SnapshotShipStatusDataSource(database: MongoDatabase) {
    companion object {
        const val COLLECTION_NAME = "snapshotShipStatus"
        const val MAX_SNAPSHOTS = 200
    }

    private val collection = database.getCollection<SnapshotShipStatus>(COLLECTION_NAME).apply {
        ensureIndex(SnapshotShipStatus::createdAt)
    }

    fun findOldSnapshots(from: ZonedDateTime): List<SnapshotShipStatus> {
        return collection.find(SnapshotShipStatus::createdAt lte from)
            .sort(ascending(SnapshotShipStatus::createdAt))
            .limit(MAX_SNAPSHOTS)
            .toList()
    }

    fun delete(snapshot: SnapshotShipStatus) {
        collection.deleteOneById(snapshot.imo)
    }

    fun save(snapshot: SnapshotShipStatus) {
        collection.save(snapshot)
    }
}
