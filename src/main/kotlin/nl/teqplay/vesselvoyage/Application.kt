package nl.teqplay.vesselvoyage

import nl.teqplay.skeleton.actuator.AutoTimeoutHealthIndicator
import nl.teqplay.skeleton.common.logging.EnableIncomingRequestLogging
import nl.teqplay.skeleton.common.logging.EnableOutgoingRequestLogging
import nl.teqplay.skeleton.rabbitmq.RabbitMqAutoconfiguration
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer
import org.springframework.cache.annotation.EnableCaching
import org.springframework.context.annotation.EnableAspectJAutoProxy
import org.springframework.scheduling.annotation.EnableScheduling
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity

@SpringBootApplication(
    exclude = [
        // Spring boot auto configurations as they include health checks we don't want to use
        MongoAutoConfiguration::class,
        RabbitAutoConfiguration::class,

        // Skeleton health configurations
        RabbitMqAutoconfiguration::class,
        AutoTimeoutHealthIndicator::class
    ],
    scanBasePackages = ["nl.teqplay.vesselvoyage"]
)
@EnableAspectJAutoProxy
@EnableWebSecurity
@EnableGlobalMethodSecurity(jsr250Enabled = true, securedEnabled = true, prePostEnabled = true, proxyTargetClass = true)
@EnableIncomingRequestLogging
@EnableOutgoingRequestLogging
@EnableScheduling
@EnableCaching
@ConfigurationPropertiesScan
class Application : SpringBootServletInitializer()

fun main(args: Array<String>) {
    runApplication<Application>(*args)
}
