spring:
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration
      - nl.teqplay.skeleton.datasource.DataSourceAutoConfiguration
      - nl.teqplay.skeleton.auth.credentials.mongo.AutoConfiguration
      - nl.teqplay.skeleton.auth.credentials.refreshToken.AutoConfiguration
      - nl.teqplay.skeleton.auth.credentials.sharedSecret.AutoConfiguration
      - nl.teqplay.skeleton.auth.credentials.auth0.AutoConfiguration
      - nl.teqplay.skeleton.auth.credentials.auth0.s2s.server.AutoConfiguration
      - nl.teqplay.skeleton.auth.credentials.keycloak.s2s.mongo.AutoConfiguration
      - nl.teqplay.skeleton.auth.credentials.keycloak.s2s.server.AutoConfiguration
      - nl.teqplay.skeleton.auth.credentials.AuthCredentialsAutoConfiguration