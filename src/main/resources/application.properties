spring.profiles.group.api=api
spring.profiles.group.processing=api,processing
spring.profiles.group.revents=revents

cors.allowed-origins=https://vesselvoyage.teqplay.nl,https://vesselvoyagedev.teqplay.nl,http://localhost:3000

# Enable response compression (gzip)
server.compression.enabled=true
server.compression.mime-types=application/json,application/xml,text/html,text/xml,text/plain

# Fill the error message property in error responses
server.error.include-message=always

# The amount of threads the task scheduler can use
spring.task.scheduling.pool.size=10

# Enable enum default values as we use it for the FallbackType model in V2
spring.jackson.deserialization.read-unknown-enum-values-using-default-value=true

app.environment=DEV

mongodb.host=localhost
mongodb.port=27017
mongodb.authDb=admin
mongodb.username=
mongodb.password=
mongodb.db=vesselvoyage

internal-api.url=https://internalapidev.teqplay.dev
internal-api.domain=keycloakdev.teqplay.nl
internal-api.realm=dev
internal-api.client-id=
internal-api.client-secret=

auth-credentials-auth0.domain=teqplay.eu.auth0.com
auth-credentials-auth0.audience=https://vesselvoyagedev.teqplay.nl

auth-credentials-auth0-s2s.domain=teqplay.eu.auth0.com
auth-credentials-auth0-s2s.audience=https://vesselvoyagedev.teqplay.nl

auth-credentials-keycloak-s2s.domain=keycloakdev.teqplay.nl
auth-credentials-keycloak-s2s.realm=dev
auth-credentials-keycloak-s2s.audience=vesselvoyage

# token age is 24 hours: 86400 seconds
auth-credentials.token-age=86400

# the following property is required to fill in!
# auth-credentials.secret=

management.endpoint.health.show-details=when_authorized

amqp.outgoing.enabled=false
amqp.outgoing.uri=amqps://localhost:5671/VesselVoyageDev
amqp.outgoing.exchange=

nats.ais-stream.enabled=false
nats.ais-stream.url=nats://localhost:4222
nats.ais-stream.username=
nats.ais-stream.password=

nats.event-stream.enabled=false
nats.event-stream.url=nats://localhost:4222
nats.event-stream.username=
nats.event-stream.password=

nats.change-stream.enabled=false
nats.change-stream.url=nats://localhost:4222
nats.change-stream.username=
nats.change-stream.password=

auth0.url=

# Enable to make use of the KeyCloak instead of Auth0
keycloak.enabled=false

# PoMa m2m config
keycloak.poma.url=https://backendpoma.dev.teqplay.com
keycloak.poma.realm=dev
keycloak.poma.domain=keycloakdev.teqplay.nl
keycloak.poma.client-id=vesselvoyage
keycloak.poma.client-secret=

poma.domain=teqplay.eu.auth0.com
poma.url=
poma.client-id=
poma.client-secret=
poma.audience=

# CSI m2m config
keycloak.csi.url=https://csibackend.dev.teqplay.com
keycloak.csi.realm=dev
keycloak.csi.domain=keycloakdev.teqplay.nl
keycloak.csi.client-id=vesselvoyage
keycloak.csi.client-secret=

csi.domain=teqplay.eu.auth0.com
csi.url=https://csibackenddev.teqplay.nl
csi.client-id=
csi.client-secret=
csi.audience=csibackend.teqplay.nl


# Important: the event filters below must be in sync with:
# - the events that both platforms put on the TeqplayEvents queue, see system.conf -> teqplayevents.external
# - the routing keys that are configured in TeqplayEvents queue in rabbitmq
event-fetching.filter=type LIKE 'area.port.%' OR type LIKE 'ship.movement.%' OR type = 'ship.statuschanged' OR type LIKE 'ship-ship.%' OR type LIKE 'ship.anchored.%' OR type = 'ship.destinationchanged' OR type LIKE 'ship.eta%' OR (type LIKE 'ship-infrastructure.terminal.%' AND type LIKE '%.confirm.unique')
event-fetching.max-days=99999
event-fetching.chunk-duration=P120D

ais-fetching.maxDays=99999
ais-fetching.chunkDuration=P30D

processing.enable-real-time=true

event-processing.max-speed-mps=1
event-processing.min-duration=PT30M
event-processing.new-stop-detection=false
event-processing.enable-trace-calculations=true
event-processing.enable-slow-moving-periods=true

event-processing.total-threads=5
event-processing.enable-new-definition=false
# Turned on by default until everyone uses V2
event-processing.enable-old-definition=true
event-processing.log-results=false

event-processing.activeness-monitoring.enabled=true
event-processing.activeness-monitoring.interval=60000
event-processing.activeness-monitoring.max-idle-time=PT5M

trace.total-threads=5
trace.enable-new-definition=false
# Turned on by default until everyone uses V2
trace.enable-old-definition=true

# The maximum deviation of traces in rad from the original trace
trace.tolerance=0.0001

# the maximum length in days when requesting AIS information for traces
trace.max-request-length-days=90

# Ongoing traces will be deleted when older than the configured maxAge
trace.ongoing.max-age=P10D

# Ongoing traces will be simplified when the size exceeds the maxNonSimplifiedSize
trace.ongoing.max-non-simplified-size=25

# Ongoing traces will be kept in cache and appended to until the following
# max is reached after that a new ongoing trace will be started.
# This max is the maximum number of locations in a single ongoing trace.
trace.ongoing.max-size=50

# persist all ongoing traces once in a while,
# to prevent losing much ongoing trace AIS data in case of a crash
trace.ongoing.persist-interval=900000

# If true, an historic trace will be generated once as soon as a visit/voyage
# is finished. When false, historic traces will be generated lazily: only when
# requested and missing.
trace.historic.generate-when-finished=false

# When false, the ongoing trace will be used to generate historic traces
# when a visit/voyage is finished. When true, a full AIS trace will be
# fetched from platform, which is a slower and demanding operation.
trace.historic.finished.create-from-ais=false

# When false, the ongoing trace will be used to generate historic traces
# when requested for a non-finished visit/voyage. When true, a full AIS
# trace will be fetched from platform, which is a slower and more demanding
# operation.
trace.historic.unfinished.create-from-ais=false

trace.activeness-monitoring.enabled=true
trace.activeness-monitoring.interval=60000
trace.activeness-monitoring.max-idle-time=PT5M

# fetch all ports and anchorages once every day at 08:00:00 UTC
infra.refresh.cron=0 0 8 * * *

# fetch all ships once every day at 09:00:00 UTC
ship-info.refresh.cron=0 0 9 * * *

# persist all ongoing traces once in a while,
# to prevent losing much ongoing trace AIS data in case of a crash

# if true, the full story of all ships will be recalculated once automatically
# recalculation will be scheduled
recalculation.enabled=false

# refresh the list with ships that need to be recalculated once every day at 10:00:00 UTC
recalculation.refresh-recalculable-ships-cron=0 0 10 * * *

# recalculate a ship every interval
# note that if recalculation of a single ships takes 20 seconds, and you set
# the interval to say 10 seconds, effectively two ships will be recalculated
# simultaneously.
recalculation.recalculate-ship-interval=PT1M

# if true, recalculate every ship when the snapshot created is older than the configured snapshot age,
# creating a new snapshot when recalculated
recalculation.snapshot-enabled=false

# The amount of time between each batch of snapshots that is being recalculated
recalculation.snapshot-interval=PT1M

# The minimum age a snapshot should be before it is considered for recalculation
recalculation.snapshot-age=PT24h

# the maximum number of ships that can be recalculated in parallel
recalculation.thread-pool-size=1
recalculation.max-mmsi-count=10
recalculation.revents-retry-enabled=true

revents.url=https://reventsbackenddev.teqplay.nl
revents.domain=keycloakdev.teqplay.nl
revents.realm=dev
revents.client-id=vesselvoyage
revents.client-secret=

health.slack.webhook=
health.teqplay-events-queue.max-time-between-events=PT5M
health.ais-streaming-queue.max-time-between-events=PT5M

cache.ttl.poma-berths=PT24H

# When enabled diskcaches will be writen to a file on the local machine to speed up startup for development
diskcache.enabled=false

diskcache.csi-ships-file=vesselVoyageCSIShips.json
diskcache.csi-ship-registry-file=vesselVoyageCSIShipsRegistry.json

# limit the number of entries (visits, voyages) and traces in the controllers
# to prevent the backend from running out of memory due to a large request
controller.limit.max-entries=10000
controller.limit.max-traces=1000

## Kubernetes config
spring.cloud.kubernetes.config.namespace=voyage
spring.cloud.kubernetes.config.name=
spring.cloud.kubernetes.config.enable-api=false

# Explicitly disable the profile specific sources to prevent the API configmap to be loaded in for the processing component
# As the API release name is vesselvoyage-api and the processing release name is vesselvoyage, so it tries to load in vesselvoyage-api
spring.cloud.kubernetes.config.include-profile-specific-sources=false

# Prometheus JVM memory exposing
management.metrics.enable.jvm=true
management.endpoints.web.exposure.include=health,prometheus,atlas

automatic-recalculation.enabled=false
automatic-recalculation.event-history-pre-recalculation-enabled=false
automatic-recalculation.batch-size=1000
automatic-recalculation.cron=0 0 13 * * *
automatic-recalculation.ready-offset=PT48H
automatic-recalculation.ship-refresh-interval=PT15M

# DriftPredictor config
drift-predictor.url=https://driftpredictordev.teqplay.dev

## Post-processing config
post-processing.enabled=true
post-processing.total-threads=2
post-processing.interval=60000
post-processing.only-visits=false

# Result publishing for V2
event-publishing.enabled=false
event-publishing.nats.enabled=false
event-publishing.nats.url=
event-publishing.nats.username=
event-publishing.nats.password=
event-publishing.rabbit-mq.enabled=false
event-publishing.rabbit-mq.uri=
event-publishing.rabbit-mq.exchange=
event-publishing.rabbit-mq.v1-exchange=
event-publishing.enabled-publishers=entries,statement_of_facts
