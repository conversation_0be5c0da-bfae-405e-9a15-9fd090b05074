package nl.teqplay.vesselvoyage.apiv2.model

/**
 * Points to another entry, it depends on the context where it points to.
 */
data class OtherEntryPointer(

    /**
     * Destination of the previous or next Visit:
     * - when this class is set as [Visit.previous], this points to the previous [Visit.port]
     * - when this class is set as [Visit.next], this points to the next [Visit.port]
     * - when this class is set as [Voyage.previous], this points to the [Visit.port] before this voyage
     * - when this class is set as [Voyage.next], this points to the [Visit.port] after this voyage
     *
     * Note: pay attention that [port] does not always follow the same pointing as [entryId]!
     *
     * Consider these entries:
     * (entry id)  (port)   (.previous obj)             (.next obj)
     * 1           BEANR    entryId=null, port=null     entryId=2, port=NLRTM
     * 2           voyage   entryId=1, port=BEANR       entryId=3, port=NLRTM
     * 3           NLRTM    entryId=2, port=BEANR       entryId=4, port=NLAMS
     * 4           voyage   entryId=3, port=NLRTM       entryId=5, port=NLAMS
     * 5           NLAMS    entryId=4, port=NLRTM       entryId=null, port=null
     *
     * Either Poma ID or unlocode
     */
    val port: String?,

    /**
     * The previous or next entry:
     * - when this class is set as [Visit.previous], this points to the last [Voyage]
     * - when this class is set as [Visit.next], this points to the next [Voyage]
     * - when this class is set as [Voyage.previous], this points to the last [Visit]
     * - when this class is set as [Voyage.next], this points to the next [Visit]
     *
     * For all of the above: if null, there is no previous/next visit/voyage (yet)
     */
    val entryId: String
)
