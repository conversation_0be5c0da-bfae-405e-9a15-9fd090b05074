package nl.teqplay.vesselvoyage.apiv2.model

import nl.teqplay.skeleton.model.Location
import java.time.Instant

data class LocationTime(
    val location: Location,
    val time: Instant,
    /**
     * When set, indicates whether a fallback was used to get this LocationTime.
     *
     * For example, when the ship exits a port area, and the port area end event is missed, another port area start or
     * EOSP end event may be used. This could also occur in a real-time scenario, where data arrives later, and the
     * system may already decide to use a fallback as other information arrived earlier. Such as satellite data.
     *
     * Note that this ONLY denotes the fallback used to determine THIS LocationTime. This may be part of a larger
     * object that has its own detection fallback types. Such as pilots, where the normal detection type is through
     * encounter events and a fallback is pilot area start+end events. In case of a pilot area fallback, the area end
     * event may be missing, the system may decide to use the area end event of another (encompassing) area as fallback.
     */
    val fallback: String? = null
)
