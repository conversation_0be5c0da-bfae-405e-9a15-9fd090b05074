package nl.teqplay.vesselvoyage.apiv2.model.sof.pto

import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2
import nl.teqplay.vesselvoyage.apiv2.model.LocationTime
import nl.teqplay.vesselvoyage.apiv2.model.SlowMovingPeriod
import nl.teqplay.vesselvoyage.apiv2.model.sof.StatementOfFactsView
import nl.teqplay.vesselvoyage.model.ShipDetails

data class PtoStatementOfFactsView(
    /** SOF generator version */
    override val version: Int,
    /** Generation id, for future use when other products will use versioning */
    override val generationId: String,
    /** The VesselVoyage visit id this SOF is based on */
    override val entryId: String,

    /** Details of the ship where this SOF is about */
    val ship: ShipDetails,

    /** EOSP start */
    val start: LocationTime,
    /** EOSP end */
    val end: LocationTime?,

    /**
     * The EOSP area info
     */
    val area: AreaMeta?,

    val previousPort: AreaMeta?,

    /** The port areas that the ship sailed through. These are port inner areas, not the EOSP area. */
    val portAreas: List<PortArea>,
    val berthVisits: List<BerthVisit>,
    val terminalVisits: List<TerminalVisit>,

    @Deprecated("Use categorizedAnchorStops instead")
    val anchorStops: List<AnchorStop>,
    val unclassifiedStops: List<UnclassifiedStop>,
    val encounters: List<Encounter>,

    val pilotInbound: Pilot?,
    val pilotOutbound: Pilot?,

    val lockStops: List<LockStop>,
    val approachAreas: List<ApproachAreaVisit>,

    val shipToShipTransferTransfers: List<ShipToShipTransfer>,
    val slowMovingPeriods: CategorizedPeriods<SlowMovingPeriod>?,
    val categorizedAnchorStops: CategorizedPeriods<AnchorStop>
) : StatementOfFactsView

/**
 * List of port area that the ship visited or sailed through inside the EOSP.
 * A port area may occur multiple times, those objects will all have a unique reference.
 */
data class PortArea(
    @Deprecated("Use id instead", ReplaceWith("id"))
    val ref: Int,
    val id: String,
    val isPassThrough: Boolean,
    val start: LocationTime,
    val end: LocationTime?,
    val area: AreaMeta
)

data class AreaMeta(
    /** The area id, as known as in poma */
    val areaId: String,
    /** The unlocode when [type]=PORT, otherwise null */
    val unlocode: String?,
    /** Name of the area, for indicative purposes. Makes debugging a lot easier than looking everything up by id ;) */
    val name: String,
    val type: String?,
)

data class BerthVisit(
    @Deprecated("Use id instead", ReplaceWith("id"))
    val ref: Int,
    @Deprecated("Use terminalVisitId instead", ReplaceWith("terminalVisitId"))
    val terminalVisitRef: Int?,
    @Deprecated("Use portVisitId instead", ReplaceWith("portVisitId"))
    val portAreaRef: Int?,
    val id: String,
    val terminalVisitId: String?,
    val portVisitId: String?,
    val start: LocationTime,
    val end: LocationTime?,
    val firstLineSecured: LocationTime?,
    val allFast: LocationTime?,
    val lastLineReleased: LocationTime?,
    val area: AreaMeta?,
    val cargoCategoryType: List<String>?,
    val mooringType: String?,
    val terminalId: String?,
    val arrivalTugs: List<Tug>,
    val departureTugs: List<Tug>,
)

data class TerminalVisit(
    @Deprecated("Use id instead", ReplaceWith("id"))
    val ref: Int,
    @Deprecated("Use portVisitId instead", ReplaceWith("portVisitId"))
    val portAreaRef: Int?,
    val id: String,
    val portVisitId: String?,
    val start: LocationTime,
    val end: LocationTime?,
    val mooringStart: LocationTime?,
    val mooringEnd: LocationTime?,
    val area: AreaMeta?
)

data class EncounterMeta(
    val imo: Int?,
    val mmsi: Int?,
    val type: String
)

data class Tug(
    val id: String,
    val ship: EncounterMeta,
    val start: LocationTime,
    val end: LocationTime?
)

data class AnchorStop(
    val id: String,
    val portVisitId: String?,
    @Deprecated("Use portVisitId instead", ReplaceWith("portVisitId"))
    val portAreaRef: Int?,
    val start: LocationTime,
    val end: LocationTime?,
    val area: AreaMeta?
)

data class UnclassifiedStop(
    val id: String,
    val portVisitId: String?,
    @Deprecated("Use portVisitId instead", ReplaceWith("portVisitId"))
    val portAreaRef: Int?,
    val start: LocationTime,
    val end: LocationTime?
)

data class Encounter(
    val id: String,
    val berthVisitId: String?,
    val terminalVisitId: String?,
    val portVisitId: String?,
    val type: String,
    val ship: EncounterMeta,
    val start: LocationTime,
    val end: LocationTime?,
    @Deprecated("Use berthVisitId instead", ReplaceWith("berthVisitId"))
    val berthVisitRef: Int?,
    @Deprecated("Use terminalVisitId instead", ReplaceWith("terminalVisitId"))
    val terminalVisitRef: Int?,
    @Deprecated("Use portVisitId instead", ReplaceWith("portVisitId"))
    val portAreaRef: Int?
)

data class Pilot(
    val id: String,
    val start: LocationTime,
    val end: LocationTime?,
    val ship: EncounterMeta?,
    val area: AreaMeta?,
    /** If set, indicates which fallback detection was used to determine this pilot info. */
    val fallbackDetectionType: String? = null
)

data class Ship(
    val imo: Int,
    val name: String?,
    val type: ShipCategoryV2?
)

data class LockStop(
    val id: String,
    val portVisitId: String?,
    @Deprecated("Use portVisitId instead", ReplaceWith("portVisitId"))
    val portAreaRef: Int?,
    val start: LocationTime,
    val end: LocationTime?,
    val area: AreaMeta?
)

data class ApproachAreaVisit(
    val id: String,
    val portVisitId: String?,
    @Deprecated("Use portVisitId instead", ReplaceWith("portVisitId"))
    val portAreaRef: Int?,
    val start: LocationTime,
    val end: LocationTime?,
    val area: AreaMeta?,
)

data class ShipToShipTransfer(
    val id: String,
    val start: LocationTime,
    val end: LocationTime?,
    val ship: Ship,
    val area: AreaMeta?
)

data class CategorizedPeriods<T : Any>(
    /** All slow moving periods before pilotInbound.start (or fallback: first inner port.start) */
    val arrival: List<T> = emptyList(),
    /** All slow moving periods between pilotInbound and pilotOutbound (or their fallbacks) */
    val inPort: List<T> = emptyList(),
    /** All slow moving periods after pilotOutbound.end (or fallback: last inner port.end) */
    val departure: List<T> = emptyList(),
)
