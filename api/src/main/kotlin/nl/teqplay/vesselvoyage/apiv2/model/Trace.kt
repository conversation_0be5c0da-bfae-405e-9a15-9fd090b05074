package nl.teqplay.vesselvoyage.apiv2.model

import nl.teqplay.skeleton.model.Polyline

/**
 * A ship's trace during a visit or voyage, with distance, speed and draught information.
 * The polyline is simplified to limit the amount of data points, but in such a way that it is usable to show on a map.
 *
 * Note: to keep the system performant, it will not generate traces where the visit/voyage is longer than 3 months.
 * It returns an empty trace in that case.
 */
data class Trace(
    /** The visit or voyage id this trace belongs to */
    val entryId: String,
    /** The simplified polyline */
    val polyline: Polyline,

    /** Minimum speed during this trace, in knots. Null if the trace has not enough points to calculate it. */
    val speedMin: Float? = null,
    /** Maximum speed sailed during this trace, in knots. Null if the trace has not enough points to calculate it. */
    val speedMax: Float? = null,
    /**
     * Average speed sailed during this trace, in knots. Calculated using a weighted average, using time as the weight.
     * Null if the trace has not enough points to calculate it.
     */
    val speedAvg: Float? = null,

    val distanceMeters: Long? = null,

    /**
     * Minimum draught during this trace, in meters. Null if the trace has not enough points to calculate it.
     * If the draught was not reported via AIS during the trace, the system takes the last known draught, if available.
     */
    val draughtMin: Float? = null,
    /**
     * Maximum draught during this trace, in meters. Null if the trace has not enough points to calculate it.
     * If the draught was not reported via AIS during the trace, the system takes the last known draught, if available.
     */
    val draughtMax: Float? = null,
    /**
     * Average draught during this trace, in meters. Calculated using a weighted average, using time as the weight.
     * Null if the trace has not enough points to calculate it.
     * If the draught was not reported via AIS during the trace, the system takes the last known draught, if available.
     */
    val draughtAvg: Float? = null,
)
