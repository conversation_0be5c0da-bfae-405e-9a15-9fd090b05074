package nl.teqplay.vesselvoyage.apiv2.model.requestresponse

import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2
import nl.teqplay.vesselvoyage.apiv2.model.sof.StatementOfFactsViewName
import java.time.Instant

/**
 * Request data by port using [unlocode] or [areaId] as the identifier and [start]+[end] as the time range.
 * Providing both [unlocode] and [areaId] will result in an error.
 * Use [finished] to control whether returning ongoing entries, finished entries or both.
 */
data class StatementOfFactsViewByPortRequest(
    val view: StatementOfFactsViewName,
    val start: Instant?,
    val end: Instant?,
    val unlocode: String?,
    val areaId: String?,
    val aisTrueDestination: String?,

    /**
     * The entries where the ship has one of these categories.
     * Can be combined with [minDwt]/[maxDwt] and/or [minTeu]/[maxTeu].
     */
    override val categories: Set<ShipCategoryV2>?,

    /**
     * Whether the returned entries must be finished.
     * Pass `true` for only ongoing finished, `false` for only ongoing entries, or `null` for both.
     */
    val finished: Boolean?,

    /**
     * Whether the returned Visits must be confirmed.
     * - `true` = only confirmed Visits
     * - `false` = only unconfirmed Visits
     * - `null` = all Visits
     */
    val confirmed: Boolean?,

    /**
     * Ship's minimum DWT (deadWeightTonnage). Requires at least one [categories]. Applies to all ship categories
     * except [ShipCategoryV2.CONTAINER]. Not setting this value implies 0.
     */
    override val minDwt: Int?,

    /**
     * Ship's maximum DWT (deadWeightTonnage). Requires at least one [categories]. Applies to all ship categories
     * except [ShipCategoryV2.CONTAINER]. Not setting this value implies Int.MAX_VALUE.
     */
    override val maxDwt: Int?,

    /**
     * Ship's minimum TEU (twentyFootEquivalentUnit). Requires at least [ShipCategoryV2.CONTAINER] to be set in
     * [categories]. Only applies to ship category [ShipCategoryV2.CONTAINER]. Not setting this value implies 0.
     */
    override val minTeu: Int?,

    /**
     * Ship's maximum TEU (twentyFootEquivalentUnit). Requires at least [ShipCategoryV2.CONTAINER] to be set in
     * [categories]. Only applies to ship category [ShipCategoryV2.CONTAINER]. Not setting this value implies 0.
     */
    override val maxTeu: Int?,
) : ShipPropertyFilterRequest
