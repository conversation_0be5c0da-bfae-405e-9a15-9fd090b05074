package nl.teqplay.vesselvoyage.apiv2.model

/**
 * Data class representing a ESoF (Electronic Statement of Facts) entry.
 *
 * @property _id Visit or Voyage ID this ESoF is for.
 * @property encounters Encounters with service vessels. Encounters with other cargo vessels are stored in [shipToShipTransfers].
 * @property slowMovingPeriods The time when a ship did any kind of slow moving. We can have 3 states here:
 * @property shipToShipTransfers Transfers between this and another ship. The other ship is NEVER a service vessel (tug, etc). Encounters with service vessels are stored in [encounters].
 */
data class ESoF(
    val _id: String,
    val encounters: List<Encounter>,
    val slowMovingPeriods: List<SlowMovingPeriod>?,
    val shipToShipTransfers: List<ShipToShipTransfer> = emptyList(),
)
