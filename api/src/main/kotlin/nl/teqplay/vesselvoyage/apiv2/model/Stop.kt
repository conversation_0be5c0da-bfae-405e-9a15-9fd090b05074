package nl.teqplay.vesselvoyage.apiv2.model

import nl.teqplay.skeleton.model.Location

/**
 * A vessel stopping at a [location] for at least 15 minutes.
 */
data class Stop(
    val start: LocationTime,
    /** The end of the stop, or null if it's still happening */
    val end: LocationTime?,
    /** The actual location of the stop */
    val location: Location?,
    val area: Area,
    /** Accuracy in percentage, within the range of 0.0..1.0 */
    val accuracy: Float?
)
