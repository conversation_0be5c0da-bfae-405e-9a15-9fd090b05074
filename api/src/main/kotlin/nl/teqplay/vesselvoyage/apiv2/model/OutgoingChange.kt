package nl.teqplay.vesselvoyage.apiv2.model

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import nl.teqplay.vesselvoyage.apiv2.model.sof.StatementOfFactsView
import nl.teqplay.vesselvoyage.model.Action

@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "_type"
)
@JsonSubTypes(
    JsonSubTypes.Type(value = OutgoingEntryChange::class, name = "ENTRY"),
    JsonSubTypes.Type(value = OutgoingSofChange::class, name = "SOF")
)
sealed interface OutgoingChange<T : EntryIdentifier> {
    val action: Action
    val value: T
}

data class OutgoingEntryChange(
    override val action: Action,
    override val value: Entry
) : OutgoingChange<Entry>

data class OutgoingSofChange(
    override val action: Action,
    override val value: StatementOfFactsView
) : OutgoingChange<StatementOfFactsView>
