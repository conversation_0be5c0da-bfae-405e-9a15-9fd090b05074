package nl.teqplay.vesselvoyage.apiv2.model.sof

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import nl.teqplay.vesselvoyage.apiv2.model.EntryIdentifier
import nl.teqplay.vesselvoyage.apiv2.model.sof.portreporter.PortReporterStatementOfFactsView
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.PtoStatementOfFactsView

@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "view"
)
@JsonSubTypes(
    JsonSubTypes.Type(value = PtoStatementOfFactsView::class, name = "pto"),
    JsonSubTypes.Type(value = PortReporterStatementOfFactsView::class, name = "portreporter"),
)
interface StatementOfFactsView : EntryIdentifier {
    /** SOF generator version */
    val version: Int

    /** Generation id, for future use when other products will use versioning */
    val generationId: String
}
