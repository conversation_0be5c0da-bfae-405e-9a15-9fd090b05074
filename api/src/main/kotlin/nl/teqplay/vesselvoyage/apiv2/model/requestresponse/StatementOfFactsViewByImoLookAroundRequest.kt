package nl.teqplay.vesselvoyage.apiv2.model.requestresponse

import nl.teqplay.vesselvoyage.apiv2.model.sof.StatementOfFactsViewName
import java.time.Instant

/**
 * Request data by ship using its [imo], querying for items before or after [timestamp]. The system compares the
 * [timestamp] to the object's `start` timestamp.
 * Use a positive [limit] for items that happened after [timestamp], in other words: looking into the future.
 * Use a negative [limit] for items that happened before [timestamp], in other words: looking in the past.
 */
data class StatementOfFactsViewByImoLookAroundRequest(
    val view: StatementOfFactsViewName,
    val imo: Int,

    /**
     * The timestamp, system compares the [timestamp] to the object's `start` timestamp.
     */
    val timestamp: Instant,

    /**
     * The amount of items to return before or after the timestamp.
     * Use a positive [limit] for items that happened after [timestamp], in other words: looking into the future.
     * Use a negative [limit] for items that happened before [timestamp], in other words: looking in the past.
     */
    val limit: Int,

    /**
     * Whether the returned entries must be finished.
     * - `true` = only finished entries
     * - `false` = only ongoing entries
     * - `null` = both finished and ongoing entries
     */
    val finished: Boolean?,

    /**
     * Whether the returned Visits must be confirmed.
     * - `true` = only confirmed Visits
     * - `false` = only unconfirmed Visits
     * - `null` = all Visits
     */
    val confirmed: Boolean?
)
