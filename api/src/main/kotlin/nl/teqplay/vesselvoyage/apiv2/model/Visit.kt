package nl.teqplay.vesselvoyage.apiv2.model

/**
 * A vessel having a [stop] within the end-of-sea (EOS) boundaries of a port.
 * Only 1 visit can happen at a time. A visit can only be followed up by a [Voyage].
 */
data class Visit(
    override val entryId: String,
    override val imo: Int,
    override val start: LocationTime,

    /** The end of this visit, or null if it's still happening */
    override val end: LocationTime?,
    val port: Port,
    val portTimes: List<PortTime>,
    override val trace: Trace?,
    override val stop: List<Stop>,
    override val passThrough: List<PassThrough>,

    /**
     * Points to the previous entry, in this case the voyage before this visit.
     * Also contains the port of the previous _visit_ if that information is available.
     */
    override val previous: OtherEntryPointer?,

    /**
     * Points to the previous entry, in this case the voyage before this visit.
     * Also contains the port of the previous _visit_ if that information is available.
     */
    override val next: OtherEntryPointer?,

    override val destination: Destination?,

    /** Whether this visit is finished, meaning that the [end] property is filled in */
    override val finished: Boolean
) : Entry {
    data class PortTime(
        val id: String,
        val unlocode: String?,
        val start: LocationTime,
        val end: LocationTime?
    )
}
