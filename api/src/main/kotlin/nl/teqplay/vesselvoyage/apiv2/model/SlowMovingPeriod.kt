package nl.teqplay.vesselvoyage.apiv2.model

/**
 * Data class representing a period of slow movement for a vessel.
 *
 * @property id UUID unique to every slow moving period to make life easier for the frontend
 * @property start The start of the slow moving period.
 * @property end The end of the slow moving period.
 * @property speed The [Speed] during the slow moving period.
 */
data class SlowMovingPeriod(
    val id: String,
    val start: LocationTime,
    val end: LocationTime,
    val speed: Speed
)
