package nl.teqplay.vesselvoyage.apiv2.model

/**
 * Travel between 2 [Visit]s.
 * Only 1 voyage can happen at a time. A voyage can only be followed up by a [Visit].
 */
data class Voyage(
    override val entryId: String,
    override val imo: Int,
    /** The time when and location where this entry started */
    override val start: LocationTime,

    /** The time when and location where this voyage ended, or null if it's still happening */
    override val end: LocationTime?,

    /**
     * The trace that the vessel made during this entry.
     * Consider this list incomplete when this entry is not [finished].
     */
    override val trace: Trace?,

    /**
     * The stops that the vessel made during this entry.
     * Consider this list incomplete when this entry is not [finished].
     */
    override val stop: List<Stop>,

    /**
     * The pass-throughs that the vessel made during this entry.
     * Consider this list incomplete when this entry is not [finished].
     */
    override val passThrough: List<PassThrough>,

    /**
     * Points to the previous the visit before this voyage, if that's available.
     * Also contains the port of the previous _visit_ if that information is available.
     */
    override val previous: OtherEntryPointer?,

    /**
     * Points to the next the visit after this voyage, if that's available.
     * Also contains the port of the previous _visit_ if that information is available.
     */
    override val next: OtherEntryPointer?,

    override val destination: Destination?,

    /** Whether this visit is finished, meaning that the [end] property is filled in */
    override val finished: Boolean
) : Entry
