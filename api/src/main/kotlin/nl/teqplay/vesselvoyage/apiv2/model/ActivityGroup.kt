package nl.teqplay.vesselvoyage.apiv2.model

/**
 * A group of activities, such as all activities during port arrival.
 * The [start] and [end] denote the period in which the activities took place.
 */
data class ActivityGroup(
    val start: LocationTime,
    val startType: String,
    /**
     * Populated when this group of activities is finished, otherwise null.
     * It depends on the context of the class that uses this activity group what 'finished' exactly means.
     */
    val end: LocationTime?,
    val endType: String?,
    val activity: List<Activity>
)
