package nl.teqplay.vesselvoyage.apiv2.model

import java.time.Instant

/**
 * Destination reported by AIS.
 */
data class Destination(
    /**
     * The time when the destination changed.
     */
    val time: Instant,

    /**
     * Destination reported in the event.
     */
    val ais: String,

    /**
     * Resolved [ais] destination into an [actual] unlocode of a port.
     */
    val port: String?
)
