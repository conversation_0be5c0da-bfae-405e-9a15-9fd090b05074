package nl.teqplay.vesselvoyage.apiv2.model.requestresponse

import java.time.Instant

/**
 * Request data by ship using its [imo] and [start]+[end] as time range or [last] number of items.
 * Providing both [start]+[end] and [last] results in an error.
 * Use [finished] to control whether returning ongoing entries, finished entries or both.
 */
data class EntryByImoRequest(
    val imo: Int,
    // require start + end OR last
    val start: Instant?,
    val end: Instant?,
    val last: Int?,
    /**
     * Whether the returned entries must be finished.
     * Pass `true` for only ongoing finished, `false` for only ongoing entries, or `null` for both.
     */
    val finished: Boolean?,

    /**
     * Whether the returned Visits must be confirmed.
     * - `true` = only confirmed Visits
     * - `false` = only unconfirmed Visits
     * - `null` = all Visits
     */
    val confirmed: Boolean?
)
