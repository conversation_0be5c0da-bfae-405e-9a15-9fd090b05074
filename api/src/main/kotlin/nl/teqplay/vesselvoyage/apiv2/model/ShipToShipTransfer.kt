package nl.teqplay.vesselvoyage.apiv2.model

/**
 * An encounter specific to ship-to-ship cargo operations. This differs from a regular [NewEncounter].
 *
 * @property otherMmsi The MMSI of the other ship involved in the transfer.
 * @property otherImo The IMO number of the other ship involved in the transfer.
 * @property startEventId The event that triggered this encounter.
 * @property start The start time and location of the encounter.
 * @property end The end time and location of the encounter.
 * @property areaId The POMA id of the area where the encounter took place, determined by the encounter monitor.
 */
data class ShipToShipTransfer(
    val otherMmsi: Int,
    val otherImo: Int?,
    val startEventId: String,
    val start: LocationTime,
    val end: LocationTime?,
    val areaId: String?
)
