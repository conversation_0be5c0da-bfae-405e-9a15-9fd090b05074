package nl.teqplay.vesselvoyage.apiv2.model.requestresponse

/**
 * Generic paginated response wrapper.
 */
data class PaginatedResponse<T>(
    val data: List<T>,
    val pagination: PaginationMetadata
)

/**
 * Pagination metadata containing information about the current page and total elements.
 */
data class PaginationMetadata(
    val page: Int,
    val size: Int,
    val totalElements: Long,
    val totalPages: Long
)
