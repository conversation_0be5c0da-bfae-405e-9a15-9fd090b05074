package nl.teqplay.vesselvoyage.apiv2.model

import nl.teqplay.skeleton.model.Location
import java.time.Instant

/**
 * A single activity of a ship at a [time] on a [location].
 * The [type] depends on the context where this activity is used, for example 'anchor up'.
 *
 * Activities can be grouped in an [ActivityGroup]. For example when a ship arrives in a port, as denoted in
 * [PortStatementOfFacts.arrival].
 */
data class SingleActivity(
    val location: Location,
    val time: Instant,

    override val type: String,
    override val area: Area?
) : Activity
