package nl.teqplay.vesselvoyage.apiv2.model

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonSubTypes.Type
import com.fasterxml.jackson.annotation.JsonTypeInfo

private const val TYPE_VISIT = "API_VISIT"
private const val TYPE_VOYAGE = "API_VOYAGE"

/**
 * Properties shared between [Visit] and [Voyage].
 */
@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "_type"
)
@JsonSubTypes(
    Type(value = Visit::class, name = TYPE_VISIT),
    Type(value = Voyage::class, name = TYPE_VOYAGE),
)
interface Entry : EntryIdentifier {
    /**
     * The IMO of the vessel where this entry is happening for.
     */
    val imo: Int

    /** The time when and location where this entry started */
    val start: LocationTime

    /** The time when and location where this entry ended, or null if it's still happening */
    val end: LocationTime?

    /**
     * The trace that the vessel made during this entry.
     * Consider this list incomplete when this entry is not [finished].
     */
    val trace: Trace?

    /**
     * The stops that the vessel made during this entry.
     * Consider this list incomplete when this entry is not [finished].
     */
    val stop: List<Stop>

    /**
     * The pass-throughs that the vessel made during this entry.
     * Consider this list incomplete when this entry is not [finished].
     */
    val passThrough: List<PassThrough>

    /** Points to the previous entry, if that's available */
    val previous: OtherEntryPointer?

    /** Points to the next entry, if that's available */
    val next: OtherEntryPointer?

    /**
     * AIS Destination of the vessel at the time of this entry
     */
    val destination: Destination?

    /** Whether this entry is finished */
    val finished: Boolean
}
