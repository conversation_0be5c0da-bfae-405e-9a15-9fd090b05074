package nl.teqplay.vesselvoyage.apiv2.model.requestresponse

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2
import nl.teqplay.vesselvoyage.model.v2.Destination
import java.time.Instant

/**
 * Request voyages by origin and/or destination and a time range.
 *
 * @see VoyagesByPortRequestUsingUnlocode
 * @see VoyagesByPortRequestUsingAreaId
 */
@JsonSubTypes(
    JsonSubTypes.Type(VoyagesByPortRequestUsingUnlocode::class),
    JsonSubTypes.Type(VoyagesByPortRequestUsingAreaId::class),
    JsonSubTypes.Type(VoyagesByAisDestination::class),
)
@JsonTypeInfo(use = JsonTypeInfo.Id.DEDUCTION)
interface VoyagesByPortRequest : ShipPropertyFilterRequest {
    val start: Instant?
    val end: Instant?
    val limit: Int?

    /**
     * The voyages where the ship has one of these categories.
     * Can be combined with [minDwt]/[maxDwt] and/or [minTeu]/[maxTeu].
     */
    override val categories: Set<ShipCategoryV2>?

    /**
     * Whether the returned entries must be finished.
     * Pass `true` for only ongoing finished, `false` for only ongoing entries, or `null` for both.
     */
    val finished: Boolean?

    /**
     * Ship's minimum DWT (deadWeightTonnage). Requires at least one [categories]. Applies to all ship categories
     * except [ShipCategoryV2.CONTAINER]. Not setting this value implies 0.
     */
    override val minDwt: Int?

    /**
     * Ship's maximum DWT (deadWeightTonnage). Requires at least one [categories]. Applies to all ship categories
     * except [ShipCategoryV2.CONTAINER]. Not setting this value implies Int.MAX_VALUE.
     */
    override val maxDwt: Int?

    /**
     * Ship's minimum TEU (twentyFootEquivalentUnit). Requires at least [ShipCategoryV2.CONTAINER] to be set in
     * [categories]. Only applies to ship category [ShipCategoryV2.CONTAINER]. Not setting this value implies 0.
     */
    override val minTeu: Int?

    /**
     * Ship's maximum TEU (twentyFootEquivalentUnit). Requires at least [ShipCategoryV2.CONTAINER] to be set in
     * [categories]. Only applies to ship category [ShipCategoryV2.CONTAINER]. Not setting this value implies 0.
     */
    override val maxTeu: Int?
}

/**
 * Request voyages by origin and/or destination unlocode and a time range.
 *
 * Origin and/or destination options:
 * - at least 1 origin or 1 destination port must be set
 * - when providing both origin and destination, the returned voyages will match at least 1 origin port AND 1
 * destination port.
 * - it is not possible to mix unlocodes and areaIds
 *
 * To use areaIds, see [VoyagesByPortRequest.UsingAreaId].
 */
data class VoyagesByPortRequestUsingUnlocode(
    val originPortUnlocodes: List<String> = emptyList(),
    val destinationPortUnlocodes: List<String> = emptyList(),
    override val start: Instant? = null,
    override val end: Instant? = null,
    override val limit: Int? = null,
    override val categories: Set<ShipCategoryV2>? = emptySet(),
    override val finished: Boolean? = null,
    override val minDwt: Int? = null,
    override val maxDwt: Int? = null,
    override val minTeu: Int? = null,
    override val maxTeu: Int? = null,
) : VoyagesByPortRequest

/**
 * Request voyages by origin and/or destination and a time range. The origin and destination are expressed as
 * unlocodes or area ids
 *
 * Origin and/or destination options:
 * - at least 1 origin or 1 destination port must be set
 * - when providing both origin and destination, the returned voyages will match at least 1 origin port AND 1
 * destination port.
 * - it is not possible to mix unlocodes and areaIds.
 *
 * To use unlocodes, see [VoyagesByPortRequest.UsingUnlocode].
 */
data class VoyagesByPortRequestUsingAreaId(
    val originPortAreaIds: List<String> = emptyList(),
    val destinationPortAreaIds: List<String> = emptyList(),
    override val start: Instant? = null,
    override val end: Instant? = null,
    override val limit: Int? = null,
    override val categories: Set<ShipCategoryV2>? = emptySet(),
    override val finished: Boolean? = null,
    override val minDwt: Int? = null,
    override val maxDwt: Int? = null,
    override val minTeu: Int? = null,
    override val maxTeu: Int? = null,
) : VoyagesByPortRequest

/**
 * Request voyages by the ais destination that the vessel had during the voyage. This refers to
 * [NewVoyage.destination] / [Destination.actual], which is the 'true' destination, as determined by PortMatcher.
 */
data class VoyagesByAisDestination(
    val aisDestinationUnlocodes: List<String> = emptyList(),
    override val start: Instant? = null,
    override val end: Instant? = null,
    override val limit: Int? = null,
    override val categories: Set<ShipCategoryV2>? = emptySet(),
    override val finished: Boolean? = null,
    override val minDwt: Int? = null,
    override val maxDwt: Int? = null,
    override val minTeu: Int? = null,
    override val maxTeu: Int? = null,
) : VoyagesByPortRequest
