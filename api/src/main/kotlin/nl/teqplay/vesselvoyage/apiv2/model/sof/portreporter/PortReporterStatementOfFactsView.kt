package nl.teqplay.vesselvoyage.apiv2.model.sof.portreporter

import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2
import nl.teqplay.vesselvoyage.apiv2.model.LocationTime
import nl.teqplay.vesselvoyage.apiv2.model.sof.StatementOfFactsView

/**
 * A statement of facts on a VesselVoyage visit, from PortReporter point-of-view.
 * Please note that this SOF view is tailored for PortReporter only! Business logic may differ from other views.
 */
data class PortReporterStatementOfFactsView(
    /** SOF generator version */
    override val version: Int,
    /** Generation id, for future use when other products will use versioning */
    override val generationId: String,
    /** The VesselVoyage visit id this SOF is based on */
    override val entryId: String,

    /** The ID of the voyage prior to this visit. Can be null when this is the ships first visit. */
    val previousVoyageId: String?,
    /** The ID of the voyage after this visit. Can be null if the visit is the current visit. */
    val nextVoyageId: String?,

    /** Details of the ship */
    val ship: Ship,

    /** EOSP start */
    val start: LocationTime,
    /** EOSP end */
    val end: LocationTime?,

    /** The EOSP area info */
    val area: AreaMeta?,

    /**
     * The port areas that the ship sailed through. These are port inner areas, not the EOSP area.
     * A port area may occur multiple times if the ship entered/exited and then entered/exited again.
     * Port areas may be marked as 'passthrough' when the ship made no stop.
     */
    val portAreas: List<PortArea>,

    /**
     * The berth(s) where the ship made a stop in the berth.
     * A berth may occur multiple times if the ship entered/exited and then entered/exited again.
     */
    val berthVisits: List<BerthVisit>,

    /**
     * The terminals of the berths where the ship made a stop in the berth.
     * A terminal may occur multiple times if the ship entered/exited and then entered/exited again.
     */
    val terminalVisits: List<TerminalVisit>,

    /**
     * The anchor stops of the ship.
     * An anchor place may occur multiple times if the ship entered/exited and then entered/exited again.
     */
    val anchorStops: List<AnchorStop>,

    /** Stops that could not be classified as anchor or berth stops. */
    val unclassifiedStops: List<UnclassifiedStop>,

    /**
     * All encounters with service vessels during this visit
     * An encountered vessel occurs multiple times if multiple encounters with the same other vessel happened.
     */
    val encounters: List<Encounter>,

    /**
     * The last pilot before the first berth. Based on pilot encounter events, or if not available based on sailing
     * through the last pilot area before the first berth. All pilot encounters are listed in [encounters].
     */
    val pilotInbound: Pilot?,
    /**
     * The first pilot after the last berth. Based on pilot encounter events, or if not available based on sailing
     * through the first pilot area after the last berth. All pilot encounters are listed in [encounters].
     */
    val pilotOutbound: Pilot?,

) : StatementOfFactsView

/**
 * A port area that the ship stopped inside or sailed through.
 * Note that resembles is the INNER port area, not the End-Of-Sea-Passage (EOSP)!
 * A port area may occur multiple times if the ship entered/exited and then entered/exited again.
 */
data class PortArea(
    /**
     * Unique reference for this port area visit within this [PortReporterStatementOfFactsView], other objects in this
     * SOF can refer to this (usually with portAreaRef).
     */
    val ref: Int,
    /** True if the ship made no stop in this port area. In other words, just sailing through */
    val isPassThrough: Boolean,
    val start: LocationTime,
    val end: LocationTime?,
    /** Port area details */
    val area: AreaMeta
)

/** Details of an area, based on its POMA definition. */
data class AreaMeta(
    /** The area id, as known as in poma */
    val areaId: String,
    /** The unlocode when [type]=PORT, otherwise null */
    val unlocode: String?,
    /** Name of the area, for indicative purposes. Makes debugging a lot easier than looking everything up by id ;) */
    val name: String,
    val type: String?,
)

/** Denotes a visit to a berth, where the vessel actually stopped inside the berth area. */
data class BerthVisit(
    /**
     * Unique reference for this berth visit within this [PortReporterStatementOfFactsView], other objects in this SOF
     * can refer to this
     */
    val ref: Int,
    /** Refers to the [TerminalVisit] with this reference, see [PortReporterStatementOfFactsView.terminalVisits] */
    val terminalVisitRef: Int?,
    /** Refers to the [PortArea] with this reference, see [PortReporterStatementOfFactsView.portAreas] */
    val portAreaRef: Int?,
    val start: LocationTime,
    val end: LocationTime?,
    /** First tug leaving the ship in the arrival process */
    val firstLineSecured: LocationTime?,
    /** Last tug leaving the ship in the arrival process */
    val allFast: LocationTime?,
    /** Last tug arriving at the ship in the departure process */
    val lastLineReleased: LocationTime?,
    /** Berth area */
    val area: AreaMeta?,
    /** Cargo category type, as listed in the berth definition in POMA */
    val cargoCategoryType: List<String>?,
    /** Cargo category type, as listed in the berth definition in POMA */
    val mooringType: String?,
    /** ID of the terminal, if the berth belongs to one, as listed in the berth definition in POMA */
    val terminalId: String?,
    /** All tugs involved in the arrival at this berth */
    val arrivalTugs: List<Tug>,
    /** All tugs involved in the departure from this berth */
    val departureTugs: List<Tug>,
)

/**
 * A terminal visit consists of consecutive berth visits having the same terminal id AND same port area id.
 * In other words, berth visits with these criteria area folded into a terminal visit.
 * A terminal visit is based on a stop in a berth area, and the berth having a terminal id.
 */
data class TerminalVisit(
    /**
     * Unique reference for this terminal visit within this [PortReporterStatementOfFactsView], other objects in this
     * SOF can refer to this
     */
    val ref: Int,
    /** Reference to the [PortArea] where this terminal visit took place (based on time, not location!). */
    val portAreaRef: Int?,
    /** First berth start. This looks at the berth area, not the terminal mooring area! */
    val start: LocationTime,
    /** Last berth end. This looks at the berth area, not the terminal mooring area! */
    val end: LocationTime?,
    /** Terminal mooring area start */
    val mooringStart: LocationTime?,
    /** Terminal mooring area end. Null if still ongoing. */
    val mooringEnd: LocationTime?,
    /** The terminal */
    val area: AreaMeta?
)

/** Denotes details of the other ship encountering the main ship. */
data class EncounterMeta(
    /** IMO of the encountered ship. Note that a lot of service vessels have no IMO */
    val imo: Int?,
    /** MMSI of the encountered ship */
    val mmsi: Int?,
    /** Type of the encountered ship, one of: [nl.teqplay.vesselvoyage.model.event.EncounterType] */
    val type: String
)

/** Denotes details of a tug involved with berth arrival / departure */
data class Tug(
    val ship: EncounterMeta,
    val start: LocationTime,
    val end: LocationTime?
)

/** Denotes an anchor stop inside the EOSP. */
data class AnchorStop(
    /**
     * Reference to the [PortArea] where this anchor stop took place (based on time, not location!).
     * Only populated when the related anchor place is _inside_ the port inner area! For anchor places inside the
     * EOSP, but outside the inner port area, this will be null.
     */
    val portAreaRef: Int?,
    val start: LocationTime,
    val end: LocationTime?,
    /** The anchor place where the stop took place, if available */
    val area: AreaMeta?
)

/** Denotes a stop that could not be classified as an anchor, berth or lock stop. */
data class UnclassifiedStop(
    /**
     * If this happened during a port area visit, this is the reference to the [PortArea] (based on time, not
     * location!).
     */
    val portAreaRef: Int?,
    val start: LocationTime,
    val end: LocationTime?
)

/** Denotes a notable encounter with another vessel, such as service vessels (tugs, pilots, etc.). */
data class Encounter(
    /** Type of encounter. One of [nl.teqplay.vesselvoyage.model.event.EncounterType]*/
    val type: String,
    /** The encountered ship */
    val ship: EncounterMeta,
    val start: LocationTime,
    val end: LocationTime?,
    /**
     * If this happened during a berth visit, this is the reference to the [BerthVisit] (based on time, not location!).
     */
    val berthVisitRef: Int?,
    /**
     * If this happened during a terminal visit, this is the reference to the [TerminalVisit] (based on time, not
     * location!).
     */
    val terminalVisitRef: Int?,
    /**
     * If this happened during a port area visit, this is the reference to the [PortArea] (based on time, not
     * location!).
     */
    val portAreaRef: Int?
)

/**
 * Denotes a pilot activity, either a pilot encounter, or sailing through a pilot area as a fallback when no pilot
 * encounter is available
 */
data class Pilot(
    val start: LocationTime,
    val end: LocationTime?,
    /** The pilot ship, or null if no pilot ship was involved, e.g. just sailing through a pilot area */
    val ship: EncounterMeta?,
    /** The pilot area where this */
    val area: AreaMeta?,
    /** If set, indicates which fallback detection was used to determine this pilot info. */
    val fallbackDetectionType: String? = null
)

/** Details of the main ship */
data class Ship(
    val imo: Int,
    val name: String?,
    val type: ShipCategoryV2?
)
