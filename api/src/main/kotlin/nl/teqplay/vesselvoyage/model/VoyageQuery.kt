package nl.teqplay.vesselvoyage.model

import nl.teqplay.csi.model.ship.info.component.ShipCategoryV1
import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2
import java.time.ZonedDateTime

data class VoyageQuery(
    val startTime: ZonedDateTime,
    val endTime: ZonedDateTime, // excluded
    val startPortIds: Set<String>?,
    val endPortIds: Set<String>?,
    val imos: Set<String>?,
    val shipCategories: Set<ShipCategoryV1>?,
    val shipCategoriesV2: Set<ShipCategoryV2>?,
    val finished: Boolean?,
    val limit: Int = QUERY_LIMIT_DEFAULT
)
