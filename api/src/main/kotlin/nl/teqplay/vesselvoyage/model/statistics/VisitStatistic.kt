package nl.teqplay.vesselvoyage.model.statistics

import java.time.Duration
import java.time.ZonedDateTime

data class VisitStatistic(
    val imo: String,
    val portId: String,
    val visitId: String,
    val visitStartTime: ZonedDateTime,
    val visitEndTime: ZonedDateTime,
    val visitDuration: Duration,
    val sailingDuration: Duration,
    val mooredDuration: Duration,
    val anchoredDuration: Duration,
    val unclassifiedStopDuration: Duration,
)
