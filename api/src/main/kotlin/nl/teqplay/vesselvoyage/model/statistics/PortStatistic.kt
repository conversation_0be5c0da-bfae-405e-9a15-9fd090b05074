package nl.teqplay.vesselvoyage.model.statistics

import java.time.Duration
import java.time.ZonedDateTime

data class PortStatistic(
    val portId: String,
    val startTime: ZonedDateTime,
    val endTime: ZonedDateTime,
    val visitCount: Long,
    val periodDuration: Duration,
    val averageVisitDuration: Duration,
    val averageSailingDuration: Duration,
    val averageMooredDuration: Duration,
    val averageAnchoredDuration: Duration,
    val averageUnclassifiedStopDuration: Duration,
)
