package nl.teqplay.vesselvoyage.model.v2

import nl.teqplay.skeleton.model.Location
import java.time.Instant

data class LocationTime(
    /**
     * The location something is happening at
     */
    val location: Location,

    /**
     * The time when something happened
     */
    val time: Instant,

    /**
     * The fallback used to specify this [location] and/or [time].
     * When this is set, the event processing resulted in unexpected behaviours, resulting in having to resort to fallbacks.
     * This is done, so we can ensure the data in VesselVoyage stays consistent.
     */
    val fallback: FallbackType? = null
) : Comparable<LocationTime> {
    override fun compareTo(other: LocationTime) = this.time.compareTo(other.time)
}
