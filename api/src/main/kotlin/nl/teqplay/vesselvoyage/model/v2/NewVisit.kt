package nl.teqplay.vesselvoyage.model.v2

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import java.time.Instant

data class NewVisit(
    override val _id: EntryId,
    override val imo: Int,
    override val start: LocationTime,
    override val end: LocationTime?,
    override val stops: List<NewStop>,
    override val passThroughEosp: List<AreaActivity> = emptyList(),
    override val passThroughPort: List<AreaActivity> = emptyList(),
    override val destination: Destination?,
    override val previous: EntryId?,
    override val next: EntryId?,
    override val updatedAt: Instant = Instant.now(),

    /**
     * The port End of Sea Passage (EOSP) start and end location and times.
     */
    @JsonProperty("eosp")
    val eospAreaActivity: AreaActivity,

    /**
     * List of other main port End of Sea Passage the vessel is in when this Visit is ongoing.
     */
    @JsonProperty("otherOngoingEosp")
    val otherOngoingEospAreaActivities: List<AreaActivity> = emptyList(),

    /**
     * All port activities containing both the port start and end location and times.
     * - Start = inner area
     * - End = outer area
     *
     * This includes a main port and their sub-ports.
     */
    @JsonProperty("port")
    val portAreaActivities: MutableList<AreaActivity> = mutableListOf(),

    /**
     * All activities that happened in anchor areas where the ship actual went for anchor during this Visit.
     */
    @JsonProperty("anchor")
    val anchorAreaActivities: MutableList<AreaActivity> = mutableListOf(),

    /**
     * All activities that happened at a berth during this Visit.
     */
    @JsonProperty("berth")
    val berthAreaActivities: MutableList<AreaActivity> = mutableListOf(),

    /**
     * All activities that happened in pilot areas during this Visit.
     */
    @JsonProperty("pilot")
    val pilotAreaActivities: MutableList<AreaActivity> = mutableListOf(),

    /**
     * All activities that happened in anchor areas during this Visit.
     */
    @JsonProperty("anchorArea")
    val anchorAreaAreaActivities: MutableList<AreaActivity> = mutableListOf(),

    /**
     * All activities that happened in terminal mooring areas during this Visit.
     */
    @JsonProperty("terminalMooring")
    val terminalMooringAreaActivities: MutableList<AreaActivity> = mutableListOf(),

    /**
     * All activities that happened in lock areas during this Visit.
     */
    @JsonProperty("lock")
    val lockAreaActivities: MutableList<AreaActivity> = mutableListOf(),

    /**
     * All activities that happened in approach areas during this Visit.
     */
    @JsonProperty("approach")
    val approachAreaActivities: MutableList<AreaActivity> = mutableListOf(),

    /**
     * Indicate if the visit is really happening. This can happen for example when we have [stops] inside a berth of our port.
     */
    val confirmed: Boolean = false,

    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    override val regenerated: Boolean = false,

    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    override val limited: Boolean = false
) : NewEntry
