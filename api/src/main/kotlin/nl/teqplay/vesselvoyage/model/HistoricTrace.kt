package nl.teqplay.vesselvoyage.model

import java.time.ZonedDateTime

data class HistoricTrace(
    // note that the _id cannot be the same as entryId because we currently can have different tolerances
    val _id: String,
    val mmsi: String,
    val imo: String,
    val entryId: String, // corresponding visit/voyage
    val startTime: ZonedDateTime, // equal to the startTime of the corresponding visit/voyage
    val endTime: ZonedDateTime?, // equal to the endTime of the corresponding visit/voyage
    val finished: Boolean,
    val locations: List<LocationTime>,
    val tolerance: Double,
    val version: Int
)
