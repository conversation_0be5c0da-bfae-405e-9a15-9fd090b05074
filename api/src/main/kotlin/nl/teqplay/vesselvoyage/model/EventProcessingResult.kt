package nl.teqplay.vesselvoyage.model

import nl.teqplay.vesselvoyage.model.v2.EntryId
import nl.teqplay.vesselvoyage.model.v2.NewChange
import nl.teqplay.vesselvoyage.model.v2.NewShipStatus

@Deprecated("Use the NewEventProcessingResult")
data class EventProcessingResult(
    val status: ShipStatus,
    val changes: List<Change>,
    val issues: List<EventProcessingIssue> = emptyList()
)

data class NewEventProcessingResult(
    val status: NewShipStatus,
    val changes: List<NewChange<*>>,
    val issues: List<EventProcessingIssue> = emptyList(),

    /**
     * All entry ids that are now eligible for post-processing
     */
    val readyForPostProcessing: List<EntryId> = emptyList(),
    val origin: String? = null,
    val decision: String? = null
)

data class EventProcessingIssue(
    val eventId: String,
    val description: String
)
