package nl.teqplay.vesselvoyage.model

import com.fasterxml.jackson.annotation.JsonInclude
import java.time.ZonedDateTime

@Deprecated("Use the NewVisit model instead")
data class Visit(
    override val _id: String,
    override val imo: String,
    override val mmsi: String,

    val anchorAreas: List<AnchorAreaVisit>,
    val portAreas: List<PortAreaVisit>,
    val berthAreas: List<BerthAreaVisit>,

    // next port
    override val dest: Destination? = null,
    override val eta: Eta?,

    // electronic statement of facts
    override val esof: ESof?,

    override val passThroughAreas: List<PortAreaVisit>?,

    override val finished: Boolean,
    override val previousEntryId: String?,
    override val nextEntryId: String?,

    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    override val regenerated: Boolean = false
) : Entry {
    override val startTime = calculateStartTimeOrThrow(anchorAreas, portAreas)
    val startTimePort = calculateStartTimePortOrNull(portAreas)
    override val endTime = calculateEndTimeOrNull(anchorAreas, portAreas, finished)
}

private fun calculateStartTimeOrThrow(
    anchorAreas: List<AnchorAreaVisit>,
    portAreas: List<PortAreaVisit>
): ZonedDateTime {
    val earliestStartTime = (anchorAreas.map { it.startTime } + portAreas.map { it.startTime })
        .minByOrNull { it }

    return requireNotNull(earliestStartTime) {
        "Cannot create visit: visit must have at least one port area or anchor area"
    }
}

private fun calculateStartTimePortOrNull(portAreas: List<PortAreaVisit>): ZonedDateTime? {
    return portAreas.map { it.startTime }
        .minByOrNull { it }
}

private fun calculateEndTimeOrNull(
    anchorAreas: List<AnchorAreaVisit>,
    portAreas: List<PortAreaVisit>,
    finished: Boolean
): ZonedDateTime? {
    if (!finished) {
        return null
    }

    // Note that we fallback to startTime when none of the areas has
    // an endTime but the visit was finished
    return (anchorAreas.map { it.endTime ?: it.startTime } + portAreas.map { it.endTime ?: it.startTime })
        .maxByOrNull { it }
}
