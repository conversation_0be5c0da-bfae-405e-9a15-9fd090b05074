package nl.teqplay.vesselvoyage.model.v2

/**
 * Wrapper model contain a [start] location and time, [end] location and time, and an [areaId] of the area
 *  the [start] and [end] are taking place.
 *
 * Here the "activity" can refer to actual stopping like an Anchor moment or not stopping at all such as passing through a port.
 */
data class AreaActivity(
    val id: String,
    override val start: LocationTime,
    override val end: LocationTime?,

    /**
     * The id of the area known in poma
     */
    val areaId: String
) : StartEnd
