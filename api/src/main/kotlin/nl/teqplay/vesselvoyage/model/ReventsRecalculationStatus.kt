package nl.teqplay.vesselvoyage.model

import java.time.ZonedDateTime

data class ReventsRecalculationStatus(
    val queuedAt: ZonedDateTime,
    val scenarioId: String,
    val phase: Phase,
    val guarantees: Set<Guarantee>,
    val username: String? = null,
    val from: ZonedDateTime? = null,
    val to: ZonedDateTime? = null,

    val errors: List<Error> = emptyList(),
) {

    enum class Phase {
        /**
         * Scenario is queued, waiting to be picked up.
         */
        QUEUED,

        /**
         * Scenario is progressing, data is being fetched and events are being generated.
         */
        PROGRESSING,

        /**
         * Scenario is finished. Data is available.
         */
        FINISHED,

        /**
         * Scenario is stopped, either it crashed or it's cancelled.
         * Data is not to be trusted.
         */
        STOPPED,

        /**
         * Some IMOs have failed, but the scenario is finished
         */
        PARTIALLY_FAILED,
    }

    /**
     * A scenario could have one or more guarantees to merge V1/V2 data back into VesselVoyage.
     */
    enum class Guarantee { V1, V2, }

    data class Error(
        val type: ErrorType,
        val version: Int? = null,
        var imo: String? = null,
        val message: String? = null,
        val level: ErrorLevel = ErrorLevel.ERROR,
    )

    enum class ErrorLevel {
        ERROR,
        WARNING
    }

    enum class ErrorType {
        /**
         * A request for interests was made, but it failed.
         */
        REQUEST_FOR_INTERESTS_FAILED,

        /**
         * A request for entries was made, but it failed.
         */
        REQUEST_FOR_ENTRIES_FAILED,

        /**
         * Scenario doesn't allow for data to be merged.
         */
        MERGING_NOT_ALLOWED,

        /**
         * Merge is not allowed to be performed.
         */
        ILLEGAL_MERGE,

        /**
         * Error while overwriting ESoF.
         */
        ESOF_OVERWRITE,

        /**
         * Error resulted in scenario being stopped.
         */
        STOPPED_EXCEPTION,

        /**
         * Time window being smaller than existing entries
         */
        INVALID_WINDOW,
    }
}
