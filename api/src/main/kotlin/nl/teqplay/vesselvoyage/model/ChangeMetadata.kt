package nl.teqplay.vesselvoyage.model

import nl.teqplay.csi.model.ship.info.component.ShipCalculated
import nl.teqplay.csi.model.ship.info.component.ShipCategories
import nl.teqplay.csi.model.ship.info.component.ShipDimensions
import nl.teqplay.csi.model.ship.info.component.ShipSpecification

data class ChangeMetadata(
    val categories: ShipCategories?,
    val dimensions: ShipDimensions?,
    val specification: ShipSpecification?,
    val calculated: ShipCalculated?
)
