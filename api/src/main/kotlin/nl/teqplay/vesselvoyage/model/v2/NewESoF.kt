package nl.teqplay.vesselvoyage.model.v2

import java.time.Instant

data class NewESoF(
    /**
     * Visit or Voyage ID this ESoF is for
     */
    override val _id: EntryId,

    /**
     * Encounters with service vessels. Encounters with other cargo vessels are stored in [shipToShipTransfers].
     */
    val encounters: List<NewEncounter>,

    /**
     * The time when a ship did any kind of slow moving. We can have 3 states here:
     * 1. We have slow moving periods
     * 2. We requested slow moving periods but there was non (empty list)
     * 3. We still have to post-process the slow moving periods (null)
     */
    val slowMovingPeriods: List<NewSlowMovingPeriod>?,

    /**
     * Indicate if this esof has been post-processed
     */
    val postProcessed: Boolean = false,

    /**
     * Transfers between this and another ship. The other ship is NEVER a service vessel (tug, etc). Encounters with
     * service vessels are stored in [encounters].
     * */
    val shipToShipTransfers: List<ShipToShipTransfer> = emptyList(),

    /**
     * The time when this esof got updated.
     */
    val updatedAt: Instant = Instant.now()
) : EntryDatabaseObject
