package nl.teqplay.vesselvoyage.model.statistics

import java.time.Duration
import java.time.ZonedDateTime

data class ShipStatistic(
    val imo: String,
    val startTime: ZonedDateTime,
    val endTime: ZonedDateTime,
    val periodDuration: Duration,
    val totalSailingDuration: Duration,
    val totalMooredDuration: Duration,
    val totalAnchoredDuration: Duration,
    val totalUnclassifiedStopDuration: Duration,
)
