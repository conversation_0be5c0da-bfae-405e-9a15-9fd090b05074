package nl.teqplay.vesselvoyage.model

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import java.time.ZonedDateTime

const val ENTRY_TYPE_VISIT = "VISIT"
const val ENTRY_TYPE_VOYAGE = "VOYAGE"

private const val VISIT_ID_SUFFIX = ".$ENTRY_TYPE_VISIT"
private const val VOYAGE_ID_SUFFIX = ".$ENTRY_TYPE_VOYAGE"

// TODO: come up with a better class name than "Entry"?
// Note that we made Entry sealed, so that when using Visit and Voyage
// in pattern matching with `when`, there is no need to add a default case.
@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "_type"
)
@JsonSubTypes(
    JsonSubTypes.Type(value = Visit::class, name = ENTRY_TYPE_VISIT),
    JsonSubTypes.Type(value = Voyage::class, name = ENTRY_TYPE_VOYAGE),
)
@Deprecated("Use the NewEntry model instead")
sealed interface Entry {
    val _id: String
    val mmsi: String
    val imo: String
    val startTime: ZonedDateTime
    val endTime: ZonedDateTime?

    // next port
    val dest: Destination?
    val eta: Eta?

    // electronic statement of facts
    val esof: ESof?

    val passThroughAreas: List<PortAreaVisit>?

    val finished: Boolean
    val previousEntryId: String?
    val nextEntryId: String?

    val regenerated: Boolean
}

fun createVisitId(eventId: String): String {
    return eventId + VISIT_ID_SUFFIX
}

fun createVoyageId(eventId: String): String {
    return eventId + VOYAGE_ID_SUFFIX
}

fun isVisitId(entryId: String) = entryId.endsWith(VISIT_ID_SUFFIX)

fun isVoyageId(entryId: String) = entryId.endsWith(VOYAGE_ID_SUFFIX)
