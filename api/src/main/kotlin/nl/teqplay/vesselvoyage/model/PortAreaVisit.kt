package nl.teqplay.vesselvoyage.model

import java.time.ZonedDateTime

data class PortAreaVisit(
    val portId: String,

    override val startEventId: String,
    override val startTime: ZonedDateTime,
    val startLocation: Location,
    val startDraught: Double?,

    override val endEventId: String?,
    override val endTime: ZonedDateTime?,
    val endLocation: Location?,
    val endDraught: Double?
) : EventPair
