package nl.teqplay.vesselvoyage.model.v2

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import nl.teqplay.vesselvoyage.model.Action

@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "_type"
)
@JsonSubTypes(
    JsonSubTypes.Type(value = VisitChange::class, name = "VISIT"),
    JsonSubTypes.Type(value = VoyageChange::class, name = "VOYAGE"),
    JsonSubTypes.Type(value = ESoFChange::class, name = "ESOF"),
)
sealed interface NewChange<T : Any> {
    val action: Action
    val value: T
}

data class VisitChange(
    override val action: Action,
    override val value: NewVisit
) : NewChange<NewVisit>

data class VoyageChange(
    override val action: Action,
    override val value: NewVoyage
) : NewChange<NewVoyage>

data class ESoFChange(
    override val action: Action,
    override val value: NewESoF
) : NewChange<NewESoF>
