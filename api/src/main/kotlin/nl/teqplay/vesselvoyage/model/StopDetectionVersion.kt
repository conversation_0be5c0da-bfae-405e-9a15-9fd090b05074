package nl.teqplay.vesselvoyage.model

enum class StopDetectionVersion {
    /**
     * The stop is only detected via the movement events.
     */
    MOVEMENT_EVENT,

    /**
     * The stop is detected using the AIS trace.
     */
    TRACE_BETWEEN_MOVEMENT_EVENT,

    /**
     * The multiple stops combined into one once the Visit finished.
     */
    COMBINED_STOPS_VISIT_FINISHED,

    /**
     * The stop is created via a BerthEvent
     */
    BERTH_EVENT
}
