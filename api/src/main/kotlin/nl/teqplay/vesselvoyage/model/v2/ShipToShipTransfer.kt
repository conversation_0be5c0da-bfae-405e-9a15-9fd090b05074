package nl.teqplay.vesselvoyage.model.v2

/**
 * An encounter specific to ship-to-ship cargo operations. This differs from a regular [NewEncounter]:
 */
data class ShipToShipTransfer(
    val otherMmsi: Int,
    val otherImo: Int?,

    /**
     * Event that triggered this encounter
     */
    val startEventId: String,
    override val start: LocationTime,
    override val end: LocationTime?,

    /** The area where the encounter took place. This is determined by the encounter monitor. */
    val areaId: String?

) : StartEnd
