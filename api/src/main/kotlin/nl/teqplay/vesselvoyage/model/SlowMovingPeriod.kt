package nl.teqplay.vesselvoyage.model

import java.time.ZonedDateTime

@Deprecated("Use the NewSlowMovingPeriod model instead")
data class SlowMovingPeriod(
    override val startEventId: String,
    override val startTime: ZonedDateTime,
    override val endEventId: String,
    override val endTime: ZonedDateTime,
    override val startLocation: Location,
    override val endLocation: Location
) : LocationBasedEventPair
