package nl.teqplay.vesselvoyage.model.v2

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import java.time.Instant

const val ENTRY_TYPE_NEW_VISIT = "VISIT"
const val ENTRY_TYPE_NEW_VOYAGE = "VOYAGE"

@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "_type"
)
@JsonSubTypes(
    JsonSubTypes.Type(value = NewVisit::class, name = ENTRY_TYPE_NEW_VISIT),
    JsonSubTypes.Type(value = NewVoyage::class, name = ENTRY_TYPE_NEW_VOYAGE),
)
sealed interface NewEntry : EntryDatabaseObject {
    override val _id: EntryId

    /**
     * The IMO of the ship this entry is tied to.
     */
    val imo: Int

    /**
     * The start time and location of the entry.
     */
    val start: LocationTime

    /**
     * The end time and location of the entry or null when still ongoing.
     */
    val end: LocationTime?

    /**
     * All stops that happened during this entry.
     */
    val stops: List<NewStop>

    /**
     * All port EOSP's that we've passed during this entry.
     */
    val passThroughEosp: List<AreaActivity>

    /**
     * All ports that we've passed during this entry.
     */
    val passThroughPort: List<AreaActivity>

    /**
     * Destination reported by AIS.
     */
    val destination: Destination?

    /**
     * The [_id] of the entry before this one.
     */
    val previous: EntryId?

    /**
     * The [_id] of the entry after this one.
     */
    val next: EntryId?

    /**
     * Whether the entry was regenerated.
     */
    val regenerated: Boolean

    /**
     * Whether the entry was limited when processing.
     * This means something must be wrong with this entry as we had to stop processing data for this entry.
     */
    val limited: Boolean

    /**
     * The time when this entry got updated.
     */
    val updatedAt: Instant
}
