package nl.teqplay.vesselvoyage.model.v2

import nl.teqplay.skeleton.model.Location
import nl.teqplay.vesselvoyage.model.StopType

data class NewStop(
    /**
     * The start event that triggered this stop.
     */
    val startEventId: String,

    /**
     * The end event that finished this stop.
     */
    val endEventId: String?,

    /**
     * The "actual" location of a stop, which should be where the stop is actually taking place
     */
    val location: Location?,

    /**
     * The start location and time of the stop
     */
    override val start: LocationTime,

    /**
     * The end location and time of the stop
     */
    override val end: LocationTime?,

    /**
     * The [StopType] of the stop, specifying if we are we in a Berth, Anchorage or something else.
     *
     * This is determined by the AIS status or using Poma
     */
    val type: NewStopType,

    /**
     * The area the stop is happening
     */
    val areaId: String?,

    /**
     * The accuracy of the stop
     */
    val accuracy: Float?
) : StartEnd
