package nl.teqplay.vesselvoyage.model

import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType
import java.time.ZonedDateTime

@Deprecated("Use the NewEncounter model instead")
data class Encounter(
    val type: EncounterType,
    val otherMmsi: String,
    val otherImo: String?,

    override val startEventId: String,
    override val startTime: ZonedDateTime,
    override val startLocation: Location,

    override val endEventId: String?,
    override val endTime: ZonedDateTime?,
    override val endLocation: Location?
) : LocationBasedEventPair
