package nl.teqplay.vesselvoyage.model

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo

@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "_type"
)
@JsonSubTypes(
    JsonSubTypes.Type(value = VisitShipStatus::class, name = "VISIT_SHIP_STATUS"),
    JsonSubTypes.Type(value = VoyageShipStatus::class, name = "VOYAGE_SHIP_STATUS"),
    JsonSubTypes.Type(value = InitialShipStatus::class, name = "INITIAL_SHIP_STATUS"),
)
@Deprecated("Use the NewShipStatus model instead")
sealed interface ShipStatus

@Deprecated("Use the VisitShipStatus model instead")
data class VisitShipStatus(
    val visit: Visit,
    val previousVoyage: Voyage?,
    val previousVisit: Visit?
) : ShipStatus

@Deprecated("Use the VoyageShipStatus model instead")
data class VoyageShipStatus(
    val voyage: Voyage,
    val previousVisit: Visit?,
    val previousVoyage: Voyage?
) : ShipStatus

@Deprecated("Use the InitialShipStatus model instead")
object InitialShipStatus : ShipStatus
