package nl.teqplay.vesselvoyage.model.v2

import nl.teqplay.skeleton.model.Polyline

data class NewTrace(
    /**
     * The Visit or Voyage ID related to this Trace
     */
    val _id: EntryId,

    /**
     * Polyline containing only the latest trace items which are not simplified
     */
    val polyline: Polyline,

    /**
     * The total amount of trace items used to make the [polyline]. This counter is used, so we can determine
     *  when we need to simplify the ongoing trace when it exceeds the limit provided in the ProcessingTraceService.
     */
    val totalOngoingTraceItems: Int,

    /**
     * The simplified part of all the trace items that happened before the first point of [polyline], or null if nothing is simplified yet.
     */
    val simplifiedPolyline: Polyline?,

    /**
     * The speed we have during this trace, or null if there is no sufficient speed information.
     */
    val speed: Speed?,

    /**
     * The distance of this trace, built from trace items that are added. Does not change when the polyline gets
     * simplified.
     */
    val distance: TraceDistance?,

    /**
     * The min, max and average draught for this trace. If the ship did not give draught information during this
     * entry (via AIS), the draught from the previous entry trace is used, if available.
     */
    val draught: TraceStatistic?
)
