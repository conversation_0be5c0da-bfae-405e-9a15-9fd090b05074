package nl.teqplay.vesselvoyage.model

import java.time.ZonedDateTime

/**
 * The detection information how a part of the stop is defined.
 * This can be filled in by a UniqueBerthEvent or found via the stop detection mechanism.
 */
interface StopDetectionInfo {
    /**
     * Identifier of the event that triggered the detection of this stop
     */
    val id: String?

    /**
     * Location of the stop
     */
    val location: Location?

    /**
     * Time of the stop
     */
    val time: ZonedDateTime?
}
