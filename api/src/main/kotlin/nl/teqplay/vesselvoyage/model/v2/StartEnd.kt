package nl.teqplay.vesselvoyage.model.v2

/**
 * A range of a start and optional end time, where both are accompanied by a location. This usually refers to a ship
 * starting and ending an activity. This object is not bound to an area, such as [AreaActivity], but leaves it open
 * to the implementor.
 *
 * Regarding the time range, [start] is inclusive, [end] is exclusive.
 *
 * The system expects this activity always to be finished in the future, in other words [end] is set. See
 * [isFinished] and [isOngoing].
 */
interface StartEnd {

    /** Start location and time, time is handled inclusive */
    val start: LocationTime

    /** End location and time, time is handled exclusive */
    val end: LocationTime?
}
