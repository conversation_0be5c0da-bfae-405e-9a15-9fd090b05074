package nl.teqplay.vesselvoyage.model

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import java.time.ZonedDateTime

/**
 * Result object when requesting recalculation for a ship/port.
 *
 * Containing the [revents] data that is used to run the recalculation based on (r)events.
 */
@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "_type"
)
@JsonSubTypes(
    JsonSubTypes.Type(value = RecalculationTrackResult::class, name = "TRACK"),
    JsonSubTypes.Type(value = RecalculationShipResult::class, name = "SHIP"),
    JsonSubTypes.Type(value = RecalculationShipsResult::class, name = "SHIPS"),
    JsonSubTypes.Type(value = RecalculationPortResult::class, name = "PORT"),
    JsonSubTypes.Type(value = RecalculationMergeResult::class, name = "MERGE"),
)
interface RecalculationResult {
    val _id: String
    val revents: ReventsRecalculationStatus
    val startedAt: ZonedDateTime
    val external: Boolean
    val automated: Boolean
    val username: String?
    val from: ZonedDateTime?
    val to: ZonedDateTime?
    val mode: RecalculationMode
}

data class RecalculationTrackResult(
    override val _id: String,
    override val revents: ReventsRecalculationStatus,
    override val startedAt: ZonedDateTime,
    override val external: Boolean,
    override val automated: Boolean,
    override val username: String? = null,
    override val from: ZonedDateTime? = null,
    override val to: ZonedDateTime? = null,
    override val mode: RecalculationMode = RecalculationMode.FULL_RECALCULATION
) : RecalculationResult {
    constructor(revents: ReventsRecalculationStatus) : this(revents.scenarioId, revents, revents.queuedAt, true, false, revents.username, revents.from, revents.to)
}

data class RecalculationShipResult(
    override val _id: String,
    val imo: Int,
    override val revents: ReventsRecalculationStatus,
    override val startedAt: ZonedDateTime,
    override val external: Boolean,
    override val automated: Boolean,
    override val username: String? = null,
    override val from: ZonedDateTime? = null,
    override val to: ZonedDateTime? = null,
    override val mode: RecalculationMode = RecalculationMode.FULL_RECALCULATION
) : RecalculationResult {
    constructor(imo: Int, revents: ReventsRecalculationStatus, automated: Boolean) : this(revents.scenarioId, imo, revents, revents.queuedAt, false, automated, revents.username, revents.from, revents.to)
}

data class RecalculationShipsResult(
    override val _id: String,
    val imos: List<Int>,
    override val revents: ReventsRecalculationStatus,
    override val startedAt: ZonedDateTime,
    override val external: Boolean,
    override val automated: Boolean,
    override val username: String? = null,
    override val from: ZonedDateTime? = null,
    override val to: ZonedDateTime? = null,
    override val mode: RecalculationMode = RecalculationMode.FULL_RECALCULATION
) : RecalculationResult {
    constructor(imos: List<Int>, revents: ReventsRecalculationStatus, automated: Boolean) : this(revents.scenarioId, imos, revents, revents.queuedAt, false, automated, revents.username, revents.from, revents.to)
}

data class RecalculationPortResult(
    override val _id: String,
    val unlocode: String,
    override val revents: ReventsRecalculationStatus,
    override val startedAt: ZonedDateTime,
    override val external: Boolean,
    override val automated: Boolean,
    override val username: String? = null,
    override val from: ZonedDateTime? = null,
    override val to: ZonedDateTime? = null,
    override val mode: RecalculationMode = RecalculationMode.FULL_RECALCULATION
) : RecalculationResult {
    constructor(unlocode: String, revents: ReventsRecalculationStatus, automated: Boolean) : this(revents.scenarioId, unlocode, revents, revents.queuedAt, false, automated, revents.username, revents.from, revents.to)
}

/**
 * Recalculation result of a [RecalculationMode.MERGE_ONLY] recalculation.
 *
 * Is used for only merging back the result of a previous recalculation
 *
 * @param ships the list of ships and their respective scenario IDs that should be merged back.
 */
data class RecalculationMergeResult(
    override val _id: String,
    val ships: List<ScenarioShip>,
    override val automated: Boolean = false,
    override val username: String? = null,
    override val revents: ReventsRecalculationStatus,
    override val startedAt: ZonedDateTime,
    override val external: Boolean = false,
    override val from: ZonedDateTime? = null,
    override val to: ZonedDateTime? = null,
    override val mode: RecalculationMode = RecalculationMode.MERGE_ONLY
) : RecalculationResult {
    constructor(
        id: String,
        revents: ReventsRecalculationStatus,
        username: String? = null,
        imoScenarioMap: List<ScenarioShip>
    ) : this(
        _id = id,
        username = username,
        ships = imoScenarioMap,
        revents = revents,
        startedAt = revents.queuedAt,
    )

    /**
     * Combination of scenario ID and IMO.
     */
    data class ScenarioShip(
        val scenarioId: String,
        val imo: Int,
    )
}

const val MERGE_SUFFIX = ".MERGE"

enum class RecalculationMode {
    /**
     * Run the full recalculation.
     */
    FULL_RECALCULATION,

    /**
     * Only merge back the result of an existing recalculation.
     */
    MERGE_ONLY
}
