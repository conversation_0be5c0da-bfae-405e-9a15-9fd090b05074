package nl.teqplay.vesselvoyage.model

import java.time.ZonedDateTime

@Deprecated("Use the NewStop model instead")
data class Stop(
    /**
     * The [StopType] of the stop, specifying if we are we in a Berth, Anchorage or something else.
     *
     * This is determined by the [aisType] and [pomaType]
     */
    val type: StopType,

    /**
     * The type of the stop decided on the AIS status of a vessel.
     */
    val aisType: StopType = StopType.UNCLASSIFIED,

    /**
     * The type of the stop decided with what we know from Poma.
     */
    val pomaType: StopType = StopType.UNCLASSIFIED,

    /**
     * The id of the stop known in poma
     */
    val pomaId: String? = null,

    /**
     * The name of the stop if it has any
     * This is the name of the berth or anchorage when we know a [pomaType]
     */
    val name: String? = null,

    /**
     * The movement end event id that triggered the creation of this stop.
     */
    override val startEventId: String,

    /**
     * The time when we started detecting the stop.
     */
    override val startTime: ZonedDateTime,

    /**
     * The location where we started detecting the stop.
     */
    override val startLocation: Location,

    /**
     * The movement start event id that triggered the finishing of this stop.
     */
    override val endEventId: String?,

    /**
     * The time when we determined the stop was finished.
     */
    override val endTime: ZonedDateTime?,

    /**
     * The last known location of the detected stop.
     */
    override val endLocation: Location?,

    /**
     * The location where the stop actually happened.
     */
    val actualLocation: Location?,

    /**
     * The time of the determined [actualLocation].
     */
    val actualTime: ZonedDateTime?,

    /**
     * The detection mechanism used to determine the stop.
     *
     * TODO change nullable type to non-nullable once we recalculated everything
     */
    val detectionVersion: StopDetectionVersion?,

    /**
     * The accuracy of the stop, calculated by the [nl.teqplay.vesselvoyage.logic.stop.calculateAccuracy] extension function.
     */
    val accuracy: Float?,

    /**
     * Information about the start UniqueBerthEvent when matching to this Stop.
     */
    val berthStart: StopStartDetectionInfo?,

    /**
     * Information about the end UniqueBerthEvent when matching to this Stop.
     */
    val berthEnd: StopEndDetectionInfo?,

    /**
     * Stop start detection based on the AIS trace
     */
    val aisStart: StopStartDetectionInfo?,

    /**
     * Stop end detection based on the AIS trace
     */
    val aisEnd: StopEndDetectionInfo?
) : LocationBasedEventPair
