package nl.teqplay.vesselvoyage.model

import java.time.Duration

const val MAX_ANCHOR_AREAS = 100
const val MAX_ENCOUNTERS = 100
const val MAX_BERTH_VISITS = 100
const val MAX_STOPS = 100
const val MAX_PORT_AREAS = 100
const val MAX_NON_MATCHING_ANCHOR_AREAS = 100
const val MAX_PASS_THROUGH_AREAS = 100

const val MAX_OVERLAPPING_PORT_DISTANCE_METERS = 100_000
const val QUERY_LIMIT_DEFAULT = 1000
const val SEARCH_PORTS_DEFAULT_LIMIT = 100
const val SEARCH_SHIPS_DEFAULT_LIMIT = 100

const val ONGOING_TRACE_WRITE_BULK_SIZE = 1000

val ESOF_VOYAGE_START_MARGIN: Duration = Duration.ofHours(4)
val ESOF_VOYAGE_END_MARGIN: Duration = Duration.ofHours(24)
const val ESOF_GROUPING_MAX_DISTANCE_METERS = 100_000

/**
 * The amount of knots needed to start labeling this as a stop.
 */
const val ESOF_STOP_MAX_INITIAL_SPEED = 0.25

/**
 * The maximum amount of knots allowed to still determine the stop as ongoing.
 * The stop can potentially be ended when the vessel moves faster than this speed.
 */
const val ESOF_STOP_MAX_ONGOING_SPEED = 1.5

/**
 * The maximum allowed of distance in meters between the start position of the stop
 *  and the current position of the vessel.
 * When this exceeds this value then an attempt will be done to finish the stop.
 */
const val ESOF_STOP_MAX_DISTANCE = 100

/**
 * When combining all the found stops, try to merge them together when the distance
 *  between the stops is not bigger than 150 meters.
 */
const val ESOF_STOP_MAX_COMBINE_DISTANCE = 150

/**
 * The amount of difference that is allowed when merging stops. If this value is greater make use of
 *  [ESOF_STOP_MAX_COMBINE_HEADING_DISTANCE] instead of [ESOF_STOP_MAX_COMBINE_DISTANCE]
 */
const val ESOF_STOP_MAX_COMBINE_HEADING_DIFFERENCE = 45

/**
 * The amount of distance in meters that is allowed when merging the found stops, this is taken into account when
 *  the heading change was higher than [ESOF_STOP_MAX_COMBINE_HEADING_DIFFERENCE]
 */
const val ESOF_STOP_MAX_COMBINE_HEADING_DISTANCE = 50

/**
 * The amount of distance in meters that is allowed when merging the found stops, this is taking into account when
 *  the time between the previous and current stop is less than [ESOF_STOP_MAX_COMBINE_DURATION].
 */
const val ESOF_STOP_MAX_COMBINE_DISTANCE_SHORT_DURATION = 1000

/**
 * The amount of time max is allowed between two stops when using the more lenient combining range
 */
val ESOF_STOP_MAX_COMBINE_DURATION: Duration = Duration.ofMinutes(30)

/**
 * The amount of distance in meters allowed between stops when combining overlapping stops of different movement blocks.
 * This will also take the [ESOF_STOP_MAX_COMBINE_VISIT_FINISHED_DURATION] into account based on the known length of the vessel.
 */
const val ESOF_STOP_MAX_COMBINE_VISIT_FINISHED_DISTANCE = 100

/**
 * The allowed distance in meters between stops when combining overlapping stops of different movement blocks.
 * This doesn't take a duration into account, thus being much smaller then [ESOF_STOP_MAX_COMBINE_VISIT_FINISHED_DISTANCE].
 */
const val ESOF_STOP_MAX_COMBINE_VISIT_FINISHED_SMALL_DISTANCE = 10

/**
 * The duration allowed between a stops to be considered to be combined once the Visit containing the stops is finished.
 * This is based on the ship's length, for smaller ships we don't want any big time between as they can move away from
 *  the first stop and get back with the second stop in a fairly short time.
 */
val ESOF_STOP_MAX_COMBINE_VISIT_FINISHED_DURATION = mapOf(
    150 to Duration.ofHours(2),
    75 to Duration.ofHours(1),
    0 to Duration.ofMinutes(30)
).toSortedMap(compareByDescending { it })

/**
 * The duration between a stop allowed to be always combined regardless of any distance the vessel has sailed.
 */
val ESOF_STOP_MAX_COMBINE_VISIT_FINISHED_SHORT_DURATION = Duration.ofMinutes(5)

/**
 * Minimal duration when something can be classified as a stop
 */
val ESOF_STOP_MIN_DURATION: Duration = Duration.ofMinutes(15)

/**
 * The maximum amount of outliers needed until a stop will be finished.
 * This number cannot be set below 1.
 */
const val ESOF_STOP_MAX_OUTLIER_TRACKING = 1

const val SIMPLIFY_TRACE_LOCATIONS_HALFWAY_COUNT = 5
val SIMPLIFY_TRACE_STOP_TIME_MARGIN: Duration = Duration.ofMinutes(30)

// The value of the trace algorithm version must never be changed, unless:
//
// 1.  the trace algorithm is changed/improved, and all traces needs to be recalculated
// 2.  there were external changes in the quality of the AIS data,
//     and all traces needs to be recalculated
//
// Recalculation of all persisted traces is not going automatically but will be effectuated
// when recalculating a ship (either manually or via the RecalculationService)
const val TRACE_ALGORITHM_VERSION = 2

val MINIMUM_SLOW_MOVEMENT_TIME: Duration = Duration.ofMinutes(15)
const val SLOW_MOVING_SPEED = 5f

/** Maximum duration between a TUG_WAITING_FOR_DEPARTURE and TUG encounter for the waiting encounter to match the tug */
val MAX_DURATION_DEPART_AND_TUG: Duration = Duration.ofHours(1)
