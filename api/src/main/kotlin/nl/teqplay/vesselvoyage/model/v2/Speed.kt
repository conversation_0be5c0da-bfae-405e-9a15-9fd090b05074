package nl.teqplay.vesselvoyage.model.v2

import java.time.Duration
import java.time.Instant

/**
 * Statistics of speed over a period of time, with [min], [max] and average ([avg]) speed.
 *
 * Helper variables for calculating the average speed are saved, to be able to recalculate the speed when a new speed
 * item is available. This is particularly helpful for ongoing traces.
 *
 * The [avg] speed is calculated using a weighted average.
 */
data class Speed(
    val min: Float,
    val max: Float,
    val avg: Float,

    /**
     * Total amount of points used to calculate the [avg], useful in case of a sample-size average calculation.
     */
    val count: Int,

    /** The last speed over ground that was added to the [avg] */
    val lastSpeedOverGround: Float,

    /** The duration of the [avg] speed, useful in case of a weighted average calculation */
    val duration: Duration = Duration.ZERO,

    /** Marks the timestamp of [duration]'s end, to determine the duration of any later amended speeds */
    val lastSpeedOverGroundTimestamp: Instant
)
