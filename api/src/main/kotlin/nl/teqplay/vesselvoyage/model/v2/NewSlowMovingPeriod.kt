package nl.teqplay.vesselvoyage.model.v2

data class NewSlowMovingPeriod(
    /**
     * UUID unique to every slow moving period to make life easier for the frontend
     */
    val id: String,

    /**
     * The start of the slow moving period.
     */
    override val start: LocationTime,

    /**
     * The end of the slow moving period.
     */
    override val end: LocationTime,

    /**
     * The [Speed] during the slow moving period.
     */
    val speed: Speed
) : StartEnd
