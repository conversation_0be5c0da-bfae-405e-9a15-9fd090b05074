package nl.teqplay.vesselvoyage.model

enum class Action {
    CREATE,
    UPDATE,
    DELETE,

    // REVISE is used when a status update can be totally different from the previous status,
    // for example after a full recalculation of a ship's story
    REVISE,

    // DISCONTINUE is used in cases where a ship did have a status before,
    // but the ship is completely removed from the system. For example
    // when it turns out the ship has a type that is considered irrelevant,
    // like when a ship is a pleasure craft.
    DISCONTINUE
}

@Deprecated("Use the NewChange")
data class Change(
    val action: Action,
    val entry: Entry
)

data class OutgoingChange(
    val action: Action,
    val entry: Entry,
    val metadata: ChangeMetadata
) {
    constructor(change: Change, metadata: ChangeMetadata) : this(
        action = change.action,
        entry = change.entry,
        metadata = metadata
    )
}
