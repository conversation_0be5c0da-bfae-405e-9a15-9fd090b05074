package nl.teqplay.vesselvoyage.model.v2

/**
 * When listing entries, filter for entries with a state finished, ongoing or both.
 * Finished means that an [NewEntry.end] is populated, where an ongoing entry has no end value.
 */
enum class NewEntryFinishedFilter {
    /** Both ongoing and finished entries */
    ANY,

    /** Only finished entries */
    FINISHED,

    /** Only ongoing entries */
    ONGOING;

    companion object {

        /** Entry endpoints use a Boolean for the finished filter.  */
        fun ofBoolean(finished: Boolean?) = when (finished) {
            true -> FINISHED
            false -> ONGOING
            null -> ANY
        }
    }
}
