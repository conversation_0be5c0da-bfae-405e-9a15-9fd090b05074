package nl.teqplay.vesselvoyage.model.v2

import com.fasterxml.jackson.annotation.JsonEnumDefaultValue

/**
 * An enum containing all types of fallbacks that can happen in a [LocationTime].
 */
enum class FallbackType {
    /**
     * Used when we received an EOSP ATD and there are still ongoing [AreaActivity], [NewStop] or [NewEncounter].
     */
    ACTIVITY_END_BY_EOSP,

    /**
     * Used when we receive a Stop start and there is still an ongoing [NewStop].
     */
    ACTIVITY_END_BY_STOP_START,

    ACTIVITY_END_BY_EOSP_START_EVENT,

    ACTIVITY_END_BY_PORT,

    ACTIVITY_END_BY_PILOT_AREA,

    ACTIVITY_END_BY_ANCHOR_AREA,

    @JsonEnumDefaultValue UNKNOWN
}
