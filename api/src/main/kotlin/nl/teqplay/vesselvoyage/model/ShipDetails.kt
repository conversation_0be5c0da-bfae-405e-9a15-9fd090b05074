package nl.teqplay.vesselvoyage.model

import nl.teqplay.csi.model.ship.info.component.ShipCategories
import nl.teqplay.platform.model.ShipInfo

// TODO: use StaticShipInfo from platform here? Or the corresponding class from CSI?
data class ShipDetails(
    val mmsi: String,
    val imo: String?,
    val name: String?,
    @Deprecated("Always null")
    val type: ShipInfo.ShipType?,
    val categories: ShipCategories?,
    val length: Double?,
    val beam: Double?,
    val maxDraught: Double?,
    val dwt: Double?
)
