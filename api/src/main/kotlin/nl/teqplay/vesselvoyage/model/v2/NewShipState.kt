package nl.teqplay.vesselvoyage.model.v2

import nl.teqplay.aisengine.event.interfaces.Event

sealed interface NewShipStatus {
    var eventBuffer: List<Event>
}

data class NewVisitShipStatus(
    val visit: EntryESoFWrapper<NewVisit>,
    val previousVoyage: EntryESoFWrapper<NewVoyage>?,
    val previousVisit: EntryESoFWrapper<NewVisit>?,

    override var eventBuffer: List<Event> = emptyList(),
) : NewShipStatus

data class NewVoyageShipStatus(
    val voyage: EntryESoFWrapper<NewVoyage>,
    val previousVisit: EntryESoFWrapper<NewVisit>?,
    val previousVoyage: EntryESoFWrapper<NewVoyage>?,

    override var eventBuffer: List<Event> = emptyList(),
) : NewShipStatus

data class NewInitialShipStatus(
    override var eventBuffer: List<Event> = emptyList(),
) : NewShipStatus

data class EntryESoFWrapper<T : NewEntry>(
    val entry: T,
    val esof: NewESoF?
)
