package nl.teqplay.vesselvoyage.model

import java.time.ZonedDateTime

data class OngoingTrace(
    val _id: String,
    val imo: String,
    val mmsi: String,

    // we use a mutable list and variables here for performance
    // minTime and maxTime are to be able to query these documents in Mongo
    val locations: MutableList<LocationTime>,
    var minTime: ZonedDateTime?,
    var maxTime: ZonedDateTime?,

    val simplifiedCount: Int
) {
    // insert the new location so that all locations stay ordered chronologically,
    // and minTime and maxTime are updated accordingly
    fun insert(location: LocationTime) {
        val index = locations.indexOfLast { it.time <= location.time }

        // index+1 to insert right after the match
        // If there is no match, it will insert at index 0 which is good
        locations.add(index + 1, location)

        minTime = locations.firstOrNull()?.time
        maxTime = locations.lastOrNull()?.time
    }
}
