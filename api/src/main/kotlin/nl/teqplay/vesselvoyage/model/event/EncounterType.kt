package nl.teqplay.vesselvoyage.model.event

import com.fasterxml.jackson.annotation.JsonAlias

@Deprecated("Use EncounterType from AisEngine instead")
enum class EncounterType {
    PILOT,
    TUG,
    SWOG,
    WASTE,
    AUTH<PERSON>ITY,
    <PERSON><PERSON><PERSON><PERSON>,
    B<PERSON>KER,
    <PERSON>R<PERSON>_BUNKER,
    WATER,
    TEN<PERSON><PERSON>,
    <PERSON>AN<PERSON>,
    <PERSON>ENDE<PERSON>,
    @<PERSON>son<PERSON>lia<PERSON>("BARGE_SUPPLY")
    SUPPLY_BARGE,
    @<PERSON>son<PERSON>lias("BARGE_PUSH")
    PUSH_BARGE,
    @<PERSON>son<PERSON><PERSON><PERSON>("BARGE_TANKER")
    TANKER_BARGE,
    @<PERSON><PERSON><PERSON><PERSON><PERSON>("BARGE_CARGO")
    CARGO_BARGE,
    @<PERSON><PERSON><PERSON><PERSON><PERSON>("BARGE_WATER")
    WATER_BARGE,
    LUBES,
    TUG_WAITING_FOR_ARRIVAL,
    @<PERSON>sonAlias("TUG_WAITING_DEPARTURE")
    TUG_WAITING_FOR_DEPARTURE,
    SHIP_TO_SHIP,
    UNCLASSIFIED // This is the TEQPERIMENT event
}
