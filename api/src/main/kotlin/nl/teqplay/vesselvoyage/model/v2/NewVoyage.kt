package nl.teqplay.vesselvoyage.model.v2

import com.fasterxml.jackson.annotation.JsonInclude
import java.time.Instant

data class NewVoyage(
    override val _id: EntryId,
    override val imo: Int,
    override val start: LocationTime,
    override val end: LocationTime?,
    override val stops: List<NewStop>,
    override val passThroughEosp: List<AreaActivity> = emptyList(),
    override val passThroughPort: List<AreaActivity> = emptyList(),
    override val destination: Destination?,
    override val previous: EntryId?,
    override val next: EntryId?,
    override val updatedAt: Instant = Instant.now(),

    /**
     * The start when it actually would've been created if EOSP time drifting wasn't a thing.
     * - We have no overlapping EOSP when the value is the same as [start].
     * - We have overlapping EOSP when the value is later than [start].
     *
     * NOTE this value should never be exposed but instead [start] because it only for definition use.
     */
    val actualStart: LocationTime?,

    /**
     * Origin port (poma id) of this voyage, or null if there is no previous visit.
     * If the preceding visit is at a sub port, then this is the EOSP of the main port!
     */
    val originPort: String?,

    /**
     * Destination port (poma id) of this voyage, or null if the voyage is not finished yet.
     * If the visit hereafter is at a sub port, then this is the EOSP of the main port!
     */
    val destinationPort: String?,

    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    override val regenerated: Boolean = false,

    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    override val limited: Boolean = false
) : NewEntry
