package nl.teqplay.vesselvoyage.model

import com.fasterxml.jackson.annotation.JsonInclude
import java.time.ZonedDateTime

@Deprecated("Use the NewVoyage model instead")
data class Voyage(
    override val _id: String,
    override val mmsi: String,
    override val imo: String,

    val startPortIds: List<String>,
    override val startTime: ZonedDateTime,

    // next port
    override val dest: Destination? = null,
    override val eta: Eta?,

    // electronic statement of facts
    override val esof: ESof?,

    override val passThroughAreas: List<PortAreaVisit>?,

    // anchorages that could not be matched to the visit of a port are stored in the voyage
    val nonMatchingAnchorAreas: List<AnchorAreaVisit>?,

    val endPortIds: List<String>?,
    override val endTime: ZonedDateTime?,

    override val finished: Boolean,
    override val previousEntryId: String?,
    override val nextEntryId: String?,

    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    override val regenerated: Boolean = false
) : Entry
