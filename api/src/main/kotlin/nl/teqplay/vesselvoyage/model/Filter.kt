package nl.teqplay.vesselvoyage.model

import nl.teqplay.csi.model.ship.info.component.ShipCategoryV1
import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2

data class Filter(
    val categoriesV1: Set<ShipCategoryV1>? = null,
    val categoriesV2: Set<ShipCategoryV2>? = null,
    val deadWeightTonnages: List<Range<Double>>? = null,
    val perCategory: List<PerCategory>? = null
) {
    data class Range<T : Comparable<T>>(
        val start: T?,
        val end: T?
    ) {
        fun contains(value: T) = (start == null || start <= value) && (end == null || value <= end)
    }

    data class PerCategory(
        val v2: ShipCategoryV2,
        val deadWeightTonnages: List<Range<Double>>? = null,
        val twentyFootEquivalentUnits: List<Range<Double>>? = null
    )
}
