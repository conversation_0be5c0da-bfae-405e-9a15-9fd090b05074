name: Code Coverage Report

on:
  push:
    branches: [ master, develop ]
  pull_request:

jobs:
  coverage:
    name: Code Coverage Report
    uses: teqplay/actions/.github/workflows/backend-code-coverage.yml@master
    secrets:
      aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
      aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      codecov_token: ${{ secrets.CODECOV_TOKEN }}
