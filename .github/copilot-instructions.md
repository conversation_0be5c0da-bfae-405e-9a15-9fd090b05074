# Copilot Code Review Guidelines

## Clarity and Maintainability

Ensure all functions clearly state their purpose with descriptive comments or docstrings. Comments must explain the reasoning behind complex logic instead of restating obvious details.

## Naming Conventions

Use descriptive, meaningful names for all variables, functions, and classes. Avoid single-letter names except for simple loop counters.

## Avoiding Deep Nesting

Avoid nesting loops and conditionals beyond three levels. Refactor deeply nested logic into separate, clearly named functions.

## Error Handling Best Practices

Handle potential errors explicitly. Provide clear and actionable error messages or exceptions instead of silent failures or generic responses.

## Performance Awareness

Avoid unnecessary object creation and redundant loops. Use efficient data structures (e.g., sets for membership checks) where appropriate to improve performance.

## Security Practices

Never commit sensitive information, such as API keys or credentials, directly in source code. Always reference environment variables or secure secrets management.

## Testing Coverage

Include tests for every new feature or significant change. Tests must cover both typical scenarios and edge cases.

## Consistent Logging

Use structured logging consistently throughout the project. Avoid excessive logging at INFO or DEBUG levels in production code.

## Modularity and Reusability

Ensure functions and modules have a single, clear responsibility. Break up functions that are excessively long (typically longer than one screen) into smaller, reusable components.

## API Coding Standards

### Empty Object Fields

Empty fields should not be returned through the endpoint. Always leave them undefined so they are not included in the response.  
Avoid returning empty fields as: `null`, `0`, `""`, or `"-"`.

### Timestamps and ISODates

Choose either timestamps or ISOStrings for dates within a project, and use that consistently. Do not mix timestamps and ISOStrings unless both are returned at all times.

### Not Found Return Statement

For endpoints using search criteria:
- If searching for a list of items and nothing is found, return an empty array `[]`.
- If searching for a single item and nothing is found, return `404: Not Found`.
