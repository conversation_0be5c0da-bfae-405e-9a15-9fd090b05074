global:
  storageClass: "gp2"
  namespaceOverride: "voyage"

image:
  repository: 050356841556.dkr.ecr.eu-west-1.amazonaws.com/vesselvoyage

mongodb:
  enabled: true
  auth:
    database: vesselvoyage
    username: vesselvoyage
  resources:
    requests:
      cpu: 0.5
      memory: 4Gi
    limits:
      memory: 4Gi

# VesselVoyage uses the @PreDestroy to persist ongoing data to the Database
# This action takes quit a long time taking 2+ minutes most of the time
terminationGracePeriodSeconds: 240

podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/path: /actuator/prometheus
  prometheus.io/port: "8080"

logs:
  - name: request-log
    file: /var/log/requests.log

atlas:
  enabled: true
