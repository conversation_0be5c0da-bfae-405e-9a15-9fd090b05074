#!/bin/bash

package=
while [ -z "$package" ]; do
  read -ei "nl.teqplay." -p "Enter application package name: " package
done 

app=${package##*.}

echo "Setting up for package $package and application name $app"

sed -i "/^rootProject\.name/s/skeleton/$app/" settings.gradle
sed -i "/^group /s/nl\.teqplay\.skeleton/$package/" build.gradle

appsrc="src/main/kotlin/nl/teqplay/skeleton/Application.kt"
configsrc="src/main/kotlin/nl/teqplay/skeleton/config/KonfigConfiguration.kt"
testsrc="src/test/kotlin/nl/teqplay/skeleton/ApplicationTest.kt"
sed -i "/^package /s/nl\.teqplay\.skeleton/$package/" $appsrc $configsrc $testsrc

package_path=${package//.///}

appdir="src/main/kotlin/$package_path"
configdir="src/main/kotlin/$package_path/config"
testdir="src/test/kotlin/$package_path"

mkdir -p $appdir $configdir $testdir

mv $appsrc $appdir
mv $configsrc $configdir
mv $testsrc $testdir

rm -rf src/main/kotlin/nl/teqplay/skeleton
rm -rf src/test/kotlin/nl/teqplay/skeleton
