import com.bmuschko.gradle.docker.tasks.image.DockerPushImage

buildscript {
    ext {
        // Base version of the application
        base_version = '0.0.1-SNAPSHOT'

        // dependencies
        kotlin_version = '1.9.25'
        skeleton_version = '2.7.1-b101.1'
        platform_version = '9.6.1'
        csi_version = '20250305-b38.1'
        poma_version = '20250304-b34.1'
        aisengine_version = "master-2.3.1"

        spring_boot_version = '3.4.4'
        spring_dependency_version = '1.1.7'
        spring_cloud_version = '3.2.1'
        springdoc_version = "2.8.6"

        kotlin_logging_version = '7.0.7'
        kotlin_csv_version = '1.10.0'
        xsynx_version = '1.3'
        mockito_kotlin_version = '2.2.0'
        slack_version = '3.4.2'
        jaxb_version = '2.3.1'
        simplify_version = '1.0.0'
        mongodb_version = '4.11.0'
        mongock_version = '4.3.8'
        mapstruct_version = "1.6.3"
        kover_version = "0.9.1"

        // plugins
        ktlint_version = '10.2.1'
        gradle_versions_plugin_version = '0.39.0'
        test_logger_version = '3.0.0'
        docker_api_version = '7.4.0'
        ecr_version = '0.7.0'
        cyclonedx_bom_version = '2.3.1'

        ecr_repo_url = '050356841556.dkr.ecr.eu-west-1.amazonaws.com'
    }
    ext['mongodb.version'] = ext.mongodb_version
}

plugins {
    id "org.jetbrains.kotlin.jvm" version "$kotlin_version"
    id "org.jetbrains.kotlin.kapt" version "$kotlin_version"
    id "org.jetbrains.kotlin.plugin.spring" version "$kotlin_version"
    id "org.springframework.boot" version "$spring_boot_version"
    id "io.spring.dependency-management" version "$spring_dependency_version"
    id "org.jlleitschuh.gradle.ktlint" version "$ktlint_version"
    id "org.jetbrains.kotlinx.kover" version "$kover_version"
    id "com.bmuschko.docker-remote-api" version "$docker_api_version"
    id "com.patdouble.awsecr" version "$ecr_version"
    id "org.cyclonedx.bom" version "$cyclonedx_bom_version"
}

group 'nl.teqplay.vesselvoyage'

repositories {
    mavenCentral()
    maven { url 'https://jitpack.io' }
    maven {
        url 's3://repo.teqplay.nl/release'
        credentials(AwsCredentials) {
            accessKey "$s3_access_key"
            secretKey "$s3_secret_key"
        }
    }
    maven {
        url 's3://repo.teqplay.nl/snapshot'
        credentials(AwsCredentials) {
            accessKey "$s3_access_key"
            secretKey "$s3_secret_key"
        }
    }
}

apply plugin: 'org.jlleitschuh.gradle.ktlint'

dependencies {
    implementation project(':api')

    // skeleton-plugins
    implementation "nl.teqplay.skeleton:common:$skeleton_version"
    implementation "nl.teqplay.skeleton:common-network:$skeleton_version"
    implementation "nl.teqplay.skeleton:actuator:$skeleton_version"
    implementation "nl.teqplay.skeleton:auth-credentials:$skeleton_version"
    implementation "nl.teqplay.skeleton:auth-credentials-refresh-token:$skeleton_version"
    implementation "nl.teqplay.skeleton:auth-credentials-auth-zero:$skeleton_version"
    implementation "nl.teqplay.skeleton:auth-credentials-auth-zero-s2s-common:$skeleton_version"
    implementation "nl.teqplay.skeleton:auth-credentials-auth-zero-s2s-client:$skeleton_version"
    implementation "nl.teqplay.skeleton:auth-credentials-auth-zero-s2s-server2:$skeleton_version"
    implementation "nl.teqplay.skeleton:auth-credentials-keycloak-s2s-common:$skeleton_version"
    implementation "nl.teqplay.skeleton:auth-credentials-keycloak-s2s-client:$skeleton_version"
    implementation "nl.teqplay.skeleton:auth-credentials-keycloak-s2s-server:$skeleton_version"
    implementation "nl.teqplay.skeleton:auth-credentials-keycloak-s2s-mongo2:$skeleton_version"
    implementation "nl.teqplay.skeleton:auth-credentials-mongo2:$skeleton_version"
    implementation "nl.teqplay.skeleton:datasource2:$skeleton_version"
    implementation "nl.teqplay.skeleton:rabbitmq:$skeleton_version"
    implementation "nl.teqplay.skeleton:metrics:$skeleton_version"
    implementation "nl.teqplay.skeleton:util-location2:$skeleton_version"
    implementation "nl.teqplay.skeleton:atlas-actuator:$skeleton_version"

    // kotlin
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core"

    // logging
    implementation "io.github.oshai:kotlin-logging-jvm:$kotlin_logging_version"
    implementation "ch.qos.logback:logback-classic"
    implementation "org.codehaus.janino:janino"

    // spring boot
    implementation "org.springframework.boot:spring-boot-starter"
    implementation "org.springframework.boot:spring-boot-starter-web"
    implementation "org.springframework.boot:spring-boot-starter-validation"
    implementation "org.springframework.boot:spring-boot-starter-security"
    implementation "org.springframework.boot:spring-boot-starter-actuator" // health checks and metrics
    implementation "org.springframework.boot:spring-boot-starter-aop" // aspectj
    implementation "org.springframework.boot:spring-boot-starter-amqp"

    // spring cloud
    implementation "org.springframework.cloud:spring-cloud-starter-kubernetes-fabric8-config:$spring_cloud_version"

    // Prometheus metrics
    implementation "io.micrometer:micrometer-registry-prometheus"

    // Database migrations
    implementation platform("com.github.cloudyrock.mongock:mongock-bom:$mongock_version")
    implementation 'com.github.cloudyrock.mongock:mongock-spring-v5'
    implementation 'com.github.cloudyrock.mongock:mongodb-sync-v4-driver'

    //Swagger
    implementation "org.springdoc:springdoc-openapi-starter-webmvc-ui:$springdoc_version"

    // json
    implementation "com.fasterxml.jackson.core:jackson-databind"
    implementation "com.fasterxml.jackson.module:jackson-module-kotlin"
    implementation "com.fasterxml.jackson.datatype:jackson-datatype-jsr310"

    // csv
    implementation "com.github.doyaaaaaken:kotlin-csv-jvm:$kotlin_csv_version"

    // cache
    implementation "org.springframework.boot:spring-boot-starter-cache"
    implementation "com.github.ben-manes.caffeine:caffeine"

    // simplifying polylines
    implementation "com.goebl:simplify:$simplify_version"

    // API modules
    implementation "nl.teqplay.platform:api:$platform_version"
    implementation "nl.teqplay.csi:api:$csi_version"
    implementation "nl.teqplay.poma:api:$poma_version"
    implementation "nl.teqplay.aisengine:models:$aisengine_version"

    // AisEngine libraries
    implementation "nl.teqplay.aisengine:client-event-history:$aisengine_version"
    implementation "nl.teqplay.aisengine:client-revents-engine-api:$aisengine_version"
    implementation "nl.teqplay.aisengine:client-ship-history:$aisengine_version"
    implementation "nl.teqplay.aisengine:nats-stream-ais-consume-diff:$aisengine_version"
    implementation "nl.teqplay.aisengine:nats-stream-event:$aisengine_version"

    // Slack for sending alerts
    implementation "com.github.seratch:jslack:$slack_version"

    // mapstruct for converting internal <-> api models
    implementation "org.mapstruct:mapstruct:$mapstruct_version"
    testImplementation "org.mapstruct:mapstruct:$mapstruct_version"
    kapt "org.mapstruct:mapstruct-processor:$mapstruct_version"

    testImplementation "org.junit.jupiter:junit-jupiter-api"
    testImplementation "org.junit.jupiter:junit-jupiter-params"
    testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine"

    testImplementation("org.springframework.boot:spring-boot-starter-test") {
        // Ensure we don't get JUnit4 in our class path
        exclude group: 'junit', module: 'junit'
    }
    testImplementation("org.springframework.security:spring-security-test")
    testImplementation "com.nhaarman.mockitokotlin2:mockito-kotlin:$mockito_kotlin_version"
    testImplementation "nl.teqplay.skeleton:nats-test:$skeleton_version"
}

tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).all {
    kotlinOptions {
        jvmTarget = "17"
        freeCompilerArgs = ['-Xjsr305=strict']
    }
}

subprojects {
    apply plugin: "org.jlleitschuh.gradle.ktlint"

    repositories {
        mavenCentral()
    }
}

test {
    useJUnitPlatform {
        includeEngines 'junit-jupiter'
    }
}

version = generateVersion()

/**
 * EKS Deployment tasks
 */
docker {
    registryCredentials {
        url.set(ecr_repo_url)
    }
}

def dockerImageName = "$ecr_repo_url/${project.name.replace("-backend", "")}:${project.version}"

bootBuildImage {
    imageName.set(dockerImageName)
}

tasks.register('dockerPushImage', DockerPushImage) {
    images.add(dockerImageName)
}

tasks.register('getVersion') {
    doLast {
        println project.version
    }
}

static def generateVersion() {
    if (System.getenv('GH') != null) {
        def branch = System.getenv('BRANCH').replace("/", "_").toLowerCase()
        def buildNum = System.getenv('GITHUB_RUN_NUMBER')
        def formattedTimestamp = getTimestamp()
        return branch + "-" + formattedTimestamp + "-b" + buildNum
    } else {
        return 'local'
    }
}

static def getTimestamp() {
    def date = new Date()
    def formattedDate = date.format('yyyy-MM-dd')
    return formattedDate
}

/**
 * CycloneDX BOM generation
 */
cyclonedxBom {
    includeConfigs.set(["runtimeClasspath"])
    outputFormat.set("json")
    // Spring boot somehow changes this value in some versions! Workaround: set it back to the default value here
    outputName.set("bom")
    componentVersion.set(generateVersion())
}
