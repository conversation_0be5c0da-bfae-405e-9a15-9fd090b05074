buildscript {
    ext {
        // dependencies
        kotlin_version = '1.9.25'
        skeleton_version = '2.4.5-b78.1'
        platform_version = '9.6.1'
        csi_version = '20250305-b38.1'

        spring_boot_version = '3.4.4'
        spring_dependency_version = '1.1.7'

        publish_plugin = '0.16.0'
    }

    repositories {
        mavenCentral()
        maven {
            url "https://plugins.gradle.org/m2/"
        }
    }

    dependencies {
        classpath "com.gradle.publish:plugin-publish-plugin:$publish_plugin"
    }
}

plugins {
    id "org.jetbrains.kotlin.jvm" version "$kotlin_version"
    id "org.jetbrains.kotlin.plugin.spring" version "$kotlin_version"
    id "org.springframework.boot" version "$spring_boot_version" apply false
    id "io.spring.dependency-management" version "$spring_dependency_version"
}

dependencyManagement {
    imports {
        mavenBom org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES
    }
}

group 'nl.teqplay.vesselvoyage'
version getDate() + getSuffix()

repositories {
    mavenCentral()
    maven { url 'https://jitpack.io' }
    maven {
        url 's3://repo.teqplay.nl/release'
        credentials(AwsCredentials) {
            accessKey "$s3_access_key"
            secretKey "$s3_secret_key"
        }
    }
    maven {
        url 's3://repo.teqplay.nl/snapshot'
        credentials(AwsCredentials) {
            accessKey "$s3_access_key"
            secretKey "$s3_secret_key"
        }
    }
}

apply plugin: 'maven-publish'

dependencies {
    implementation(project(":api"))
    api "nl.teqplay.csi:api:$csi_version"

    // spring boot
    implementation "org.springframework.boot:spring-boot"
    implementation "org.springframework:spring-web"

    // spring cloud
    implementation "org.springframework.cloud:spring-cloud-starter-kubernetes-fabric8-config:$spring_cloud_version"

    implementation "nl.teqplay.skeleton:common:$skeleton_version"
    implementation "nl.teqplay.skeleton:common-network:$skeleton_version"
    implementation "nl.teqplay.skeleton:auth-credentials-keycloak-s2s-client:$skeleton_version"
    implementation "com.fasterxml.jackson.module:jackson-module-kotlin"
}

sourceCompatibility = "17"
tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).all {
    kotlinOptions {
        jvmTarget = "17"
        freeCompilerArgs = ['-Xjsr305=strict']
    }
}

// ===== Versioning and Publishing =====
static def getDate() {
    return new Date().format('yyyyMMdd')
}

static def getSuffix() {
    if (System.getenv('GH') != null) {
        if (System.getenv('BRANCH') == 'master') {
            return '-b' + System.getenv('GITHUB_RUN_NUMBER')
        }
        else {
            return '-SNAPSHOT'
        }
    }
    else {
        return '-SNAPSHOT'
    }
}

task sourcesJar(type: Jar) {
    from sourceSets.main.allSource
    archiveClassifier = 'sources'
}

publishing {
    publications {
        portcallLibrary(MavenPublication) {
            from components.java
            artifact sourcesJar

            repositories {
                maven {
                    if (project.version.endsWith('-SNAPSHOT')) {
                        url "s3://repo.teqplay.nl/snapshot"
                    }
                    else {
                        url "s3://repo.teqplay.nl/release"
                    }

                    credentials(AwsCredentials) {
                        accessKey s3_access_key
                        secretKey s3_secret_key
                    }
                }
            }
        }
    }
}
// =====================================
