package nl.teqplay.vesselvoyage.client

import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2
import nl.teqplay.skeleton.common.network.getForObjectWithParams
import nl.teqplay.skeleton.common.network.postForObjectWithParams
import nl.teqplay.vesselvoyage.apiv2.model.Entry
import nl.teqplay.vesselvoyage.apiv2.model.Trace
import nl.teqplay.vesselvoyage.apiv2.model.Visit
import nl.teqplay.vesselvoyage.apiv2.model.Voyage
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.EntryByImoRequest
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.EntryByImoResponse
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.StatementOfFactsViewByImoLookAroundResponse
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.StatementOfFactsViewByImoRequest
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.StatementOfFactsViewByImoResponse
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.StatementOfFactsViewByPortRequest
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.StatementOfFactsViewByPortResponse
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.VisitByImoRequest
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.VisitByImoResponse
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.VisitByPortRequest
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.VisitByPortResponse
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.VoyageByImoRequest
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.VoyageByImoResponse
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.VoyagesByPortRequest
import nl.teqplay.vesselvoyage.apiv2.model.requestresponse.VoyagesByPortResponse
import nl.teqplay.vesselvoyage.apiv2.model.sof.StatementOfFactsView
import nl.teqplay.vesselvoyage.apiv2.model.sof.StatementOfFactsViewName
import nl.teqplay.vesselvoyage.model.v2.EntryId
import org.springframework.web.client.HttpClientErrorException
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.postForObject
import java.time.Instant

/**
 * Client for VesselVoyage.
 *
 * Provides access to:
 * - [visits]: A visit denotes the visit to a port and records timestamps of all areas. This does not include
 * encounters with other (service) vessels.
 * - [voyages]: A voyage is the voyage between two visits.
 * - [entries]: An entry is a visit or a voyage. This abstraction exists to support a list of
 * visit-voyage-visit-voyage-etc.
 * - [sof]: A statement of facts (SOF) combines data of a visit with encounters, slow moving/drifting data and other
 * data that happened during the visit. See the [StatementOfFacts] class docs for more information.
 * - [traces]: a trace of the ship during a visit or voyage. The trace is simplified, but still decent to show on a map.
 *
 * Let Spring discover the autoconfiguration: [VesselVoyageClientAutoConfiguration] and use as follows:
 *
 * ```
 * class Foo(val client: VesselVoyageClient) {
 *   fun getVisits(): List<Visit> {
 *     return client.visits.findByImo(...)
 *   }
 * }
 * ```
 */
class VesselVoyageClient(restTemplate: RestTemplate) {
    val entries = Entries(restTemplate, "/v2/entry")
    val visits = Visits(restTemplate, "/v2/visit")
    val voyages = Voyages(restTemplate, "/v2/voyage")
    val sof = StatementOfFacts(restTemplate, "/v2/sof")
    val traces = Traces(restTemplate, "/v2/traces")

    /**
     * Base endpoints that are available on all sub clients.
     */
    abstract class BaseSubClient<T>(
        open val restTemplate: RestTemplate,
        // NOTE class can only be used with non-list return types. List<T> will result in Jackson issues.
        private val clazz: Class<T>
    ) {
        abstract val prefix: String

        fun findById(id: EntryId): T? {
            return try {
                restTemplate.getForObject("$prefix/{id}", clazz, id)
            } catch (_: HttpClientErrorException.NotFound) {
                // 404 are caught in the client and instead return null
                null
            }
        }
    }

    class Entries(
        override val restTemplate: RestTemplate,
        override val prefix: String
    ) : BaseSubClient<Entry>(restTemplate, Entry::class.java) {

        /**
         * Finds [Visit]s and [Voyage]s by their IDs in batch.
         *
         * @param ids The list of entry IDs to find.
         * @return A list of [Visit] and [Voyage] matching the provided IDs.
         */
        fun findByIds(ids: List<EntryId>): List<Entry> {
            return restTemplate.postForObject<Array<Entry>>("$prefix/ids", ids).toList()
        }

        /**
         * Finds entries for a single ship by IMO.
         * Requires a time-range ([start], [end]) or [last] X amount.
         *
         * Returns the list of entries from most recent to least recent, sorting by [Entry.start.time]. The list has
         * alternating types: visit-voyage-visit-voyage, which resembles the natural behavior of a vessel.
         *
         * Use [findByImoBatch] to request multiple ships at once.
         *
         * @param imo Ship imo. Required.
         * @param start Time range start. Use in combination with [end]. Required, or use [last].
         * @param end Time range end. Use in combination with [start]. Required, or use [last].
         * @param last Limits the result to X most recent items. Required when not using time range ([start]+[end])
         * @param finished Whether the returned items must be finished. Pass `true` for only finished, `false` for only
         * ongoing items, or `null` (default) for both.
         * @param confirmed Whether the returned items must be confirmed, meaning it contains at least 1 stop. Pass
         * `true` for only confirmed items, `false` for only non-confirmed items, or `null` (default) for both.
         */
        fun findByImo(
            imo: Int,
            start: Instant? = null,
            end: Instant? = null,
            last: Int? = null,
            finished: Boolean? = null,
            confirmed: Boolean? = null
        ): EntryByImoResponse {
            return restTemplate.getForObjectWithParams<EntryByImoResponse>(
                path = "$prefix/byImo/{imo}",
                pathParams = listOf(imo),
                "start" to start,
                "end" to end,
                "last" to last,
                "finished" to finished,
                "confirmed" to confirmed
            )
        }

        /** Request multiple [findByImo] calls at once */
        fun findByImoBatch(
            body: List<EntryByImoRequest>
        ): Array<EntryByImoResponse> {
            return restTemplate.postForObject<Array<EntryByImoResponse>>(
                url = "$prefix/byImo",
                request = body
            )
        }
    }

    class Visits(
        override val restTemplate: RestTemplate,
        override val prefix: String
    ) : BaseSubClient<Visit>(restTemplate, Visit::class.java) {

        /**
         * Finds visits by their IDs in batch.
         *
         * @param ids The list of visit IDs to find.
         * @return A list of visits matching the provided IDs.
         */
        fun findByIds(ids: List<EntryId>): List<Visit> {
            return restTemplate.postForObject<Array<Visit>>("$prefix/ids", ids).toList()
        }

        /**
         * Finds visits for a single ship by IMO.
         * Requires a time-range ([start], [end]) or [last] X amount.
         *
         * Returns the list of visits from most recent to least recent, sorting by [Entry.start.time].
         *
         * Use [findByImoBatch] to request multiple ships at once.
         *
         * @param imo Ship imo. Required.
         * @param start Time range start. Use in combination with [end]. Required, or use [last].
         * @param end Time range end. Use in combination with [start]. Required, or use [last].
         * @param last Limits the result to X most recent items. Required when not using time range ([start]+[end])
         * @param finished Whether the returned items must be finished. Pass `true` for only finished, `false` for only
         * ongoing items, or `null` (default) for both.
         * @param confirmed Whether the returned items must be confirmed, meaning it contains at least 1 stop. Pass
         * `true` for only confirmed items, `false` for only non-confirmed items, or `null` (default) for both.
         */
        fun findByImo(
            imo: Int,
            start: Instant? = null,
            end: Instant? = null,
            last: Int? = null,
            finished: Boolean? = null,
            confirmed: Boolean? = null
        ): VisitByImoResponse {
            return restTemplate.getForObjectWithParams<VisitByImoResponse>(
                path = "$prefix/byImo/{imo}",
                pathParams = listOf(imo),
                "start" to start,
                "end" to end,
                "last" to last,
                "finished" to finished,
                "confirmed" to confirmed
            )
        }

        /** Request multiple [findByImo] calls at once */
        fun findByImoBatch(
            body: List<VisitByImoRequest>
        ): Array<VisitByImoResponse> {
            return restTemplate.postForObject<Array<VisitByImoResponse>>(
                url = "$prefix/byImo",
                request = body
            )
        }

        /**
         * Finds visits for a given port.
         *
         * Required parameters:
         * - [unlocode] or poma [areaId]
         * - time range ([start], [end]) or [limit]
         *
         * The resulting list contains visits most recent to least recent.
         *
         * @param unlocode Unlocode of the port, e.g. NLRTM. Required, unless using [areaId]
         * @param areaId POMA area id. Required, unless using [unlocode]
         * @param start Time range start. Use in combination with [end]. Required, or use [last].
         * @param end Time range end. Use in combination with [start]. Required, or use [last].
         * @param limit Limits the result to X most recent items. Required when not using time range ([start]+[end])
         * @param finished Whether the returned items must be finished. Pass `true` for only finished, `false` for only
         * ongoing items, or `null` (default) for both.
         * @param confirmed Whether the returned items must be confirmed, meaning it contains at least 1 stop. Pass
         * `true` for only confirmed items, `false` for only non-confirmed items, or `null` (default) for both.
         * @param categories Limits the result to ships that match the given categories. Pass `null` (default) for all
         * ships.
         * @param minDwt When set, the ship's DWT >= given [minDwt]. Can be combined with [maxDwt] or used solely.
         * Requires at least 1 ship [categories]. Cannot be combined with [ShipCategoryV2.CONTAINER], which uses TEU.
         * @param maxDwt When set, the ship's DWT <= given [maxDwt]. Can be combined with [minDwt] or used solely.
         * Requires at least 1 ship [categories]. Cannot be combined with [ShipCategoryV2.CONTAINER], which uses TEU.
         * @param minTeu When set, the ship's TEU >= given [minTeu]. Can be combined with [maxTeu] or used solely.
         * Requires [categories] to be set to [ShipCategoryV2.CONTAINER], cannot be combined with other ship categories.
         * @param maxTeu When set, the ship's TEU <= given [maxTeu]. Can be combined with [minTeu] or used solely.
         * Requires [categories] to be set to [ShipCategoryV2.CONTAINER], cannot be combined with other ship categories.
         */
        fun findByPort(
            unlocode: String? = null,
            areaId: String? = null,
            start: Instant? = null,
            end: Instant? = null,
            limit: Int? = null,
            categories: Set<ShipCategoryV2>? = null,
            finished: Boolean? = null,
            confirmed: Boolean? = null,
            minDwt: Int? = null,
            maxDwt: Int? = null,
            minTeu: Int? = null,
            maxTeu: Int? = null,
        ): VisitByPortResponse {
            return restTemplate.getForObjectWithParams<VisitByPortResponse>(
                path = "$prefix/byPort",
                "unlocode" to unlocode,
                "areaId" to areaId,
                "start" to start,
                "end" to end,
                "limit" to limit,
                "categories" to categories?.joinToString(),
                "finished" to finished,
                "confirmed" to confirmed,
                "minDwt" to minDwt,
                "maxDwt" to maxDwt,
                "minTeu" to minTeu,
                "maxTeu" to maxTeu,
            )
        }

        fun findByPortBatch(
            body: List<VisitByPortRequest>
        ): Array<VisitByPortResponse> {
            return restTemplate.postForObject<Array<VisitByPortResponse>>(
                url = "$prefix/byPort",
                request = body
            )
        }
    }

    class Voyages(
        override val restTemplate: RestTemplate,
        override val prefix: String
    ) : BaseSubClient<Voyage>(restTemplate, Voyage::class.java) {

        /**
         * Finds voyages by their IDs in batch.
         *
         * @param ids The list of voyage IDs to find.
         * @return A list of voyages matching the provided IDs.
         */
        fun findByIds(ids: List<EntryId>): List<Voyage> {
            return restTemplate.postForObject<Array<Voyage>>("$prefix/ids", ids).toList()
        }

        /**
         * Finds voyages for a single ship by IMO.
         * Requires a time-range ([start], [end]) or [last] X amount.
         *
         * Returns the list of voyages from most recent to least recent, sorting by [Voyage.start.time].
         *
         * Use [findByImoBatch] to request for multiple ships at once.
         *
         * @param imo Ship imo. Required.
         * @param start Time range start. Use in combination with [end]. Required, or use [last].
         * @param end Time range end. Use in combination with [start]. Required, or use [last].
         * @param last Limits the result to X most recent items. Required when not using time range ([start]+[end])
         * @param finished Whether the returned items must be finished. Pass `true` for only finished, `false` for only
         * ongoing items, or `null` (default) for both.
         * @param confirmed Whether the returned items must be confirmed, meaning it contains at least 1 stop. Pass
         * `true` for only confirmed items, `false` for only non-confirmed items, or `null` (default) for both.
         */
        fun findByImo(
            imo: Int,
            start: Instant? = null,
            end: Instant? = null,
            last: Int? = null,
            finished: Boolean? = null
        ): VoyageByImoResponse {
            return restTemplate.getForObjectWithParams<VoyageByImoResponse>(
                path = "$prefix/byImo/{imo}",
                pathParams = listOf(imo),
                "start" to start,
                "end" to end,
                "last" to last,
                "finished" to finished
            )
        }

        /** Request multiple [findByImo] calls at once */
        fun findByImoBatch(
            body: List<VoyageByImoRequest>
        ): Array<VoyageByImoResponse> {
            return restTemplate.postForObject<Array<VoyageByImoResponse>>(
                url = "$prefix/byImo",
                request = body
            )
        }

        /**
         * Finds voyages with several filter options.
         *
         * Required parameters:
         * - at least 1 filter option (see below)
         * - time range ([start], [end]) or [limit]
         *
         * Pick a filter option (required):
         * 1. Filtering on origin/destination
         *    1. Port A -> port B: provide an origin & destination
         *    2. Port A -> any port: provide an origin
         *    3. Any port -> port B: provide a destination
         *    Any combination works, but at least 1 origin or destination is required.
         *    When supplying multiple ports, all combinations of origin/destination are considered.  For example, using
         *    origin A,B and destination C,D results in voyages A->C, A->D, B->C, B->D.
         * 2. Filtering on AIS destination, that is the resolved value, so NLRTM where AIS would contain 'ROTTERDAM'.
         *
         * Using multiple origin and/or destination ports
         * All origin/destination combinations are considered when entering multiple origin and/or destinations.
         * For example, using [A,B -> C,D] results in voyages [A->C, A->D, B->C, B->D].
         *
         * Unlocodes and POMA areaIds
         * This endpoint accepts either unlocodes or poma area ids. Using both (e.g. origin unlocode + destination
         * areaId) results in a 400 Bad Request.
         *
         * @param originPortUnlocodes When set, resulting voyage origin will match one of these. When providing a
         * destination port as well, this parameter can only be combined with [destinationPortUnlocodes].
         * @param destinationPortUnlocodes When set, resulting voyage destination will match one of these. When
         * providing a destination port as well, this parameter can only be combined with [originPortUnlocodes].
         * @param originPortAreaIds When set, resulting voyage origin will match one of these. When providing a
         * destination port as well, this parameter can only be combined with [destinationPortAreaIds].
         * @param destinationPortAreaIds When set, resulting voyage destination will match one of these. When
         * providing a destination port as well, this parameter can only be combined with [originPortAreaIds].
         * @param start Time range start. Use in combination with [end]. Required, or use [last].
         * @param end Time range end. Use in combination with [start]. Required, or use [last].
         * @param limit Limits the result to X most recent items. Required when not using time range ([start]+[end])
         * @param finished Whether the returned items must be finished. Pass `true` for only finished, `false` for only
         * ongoing items, or `null` (default) for both.
         * @param categories Limits the result to ships that match the given categories. Pass `null` (default) for all
         * ships.
         * @param minDwt When set, the ship's DWT >= given [minDwt]. Can be combined with [maxDwt] or used solely.
         * Requires at least 1 ship [categories]. Cannot be combined with [ShipCategoryV2.CONTAINER], which uses TEU.
         * @param maxDwt When set, the ship's DWT <= given [maxDwt]. Can be combined with [minDwt] or used solely.
         * Requires at least 1 ship [categories]. Cannot be combined with [ShipCategoryV2.CONTAINER], which uses TEU.
         * @param minTeu When set, the ship's TEU >= given [minTeu]. Can be combined with [maxTeu] or used solely.
         * Requires [categories] to be set to [ShipCategoryV2.CONTAINER], cannot be combined with other ship categories.
         * @param maxTeu When set, the ship's TEU <= given [maxTeu]. Can be combined with [minTeu] or used solely.
         * Requires [categories] to be set to [ShipCategoryV2.CONTAINER], cannot be combined with other ship categories.
         */
        fun findByPort(
            originPortUnlocodes: List<String>? = null,
            destinationPortUnlocodes: List<String>? = null,
            originPortAreaIds: List<String>? = null,
            destinationPortAreaIds: List<String>? = null,
            aisDestinationUnlocodes: List<String>? = null,
            start: Instant? = null,
            end: Instant? = null,
            limit: Int? = null,
            categories: Set<ShipCategoryV2>? = null,
            finished: Boolean? = null,
            minDwt: Int? = null,
            maxDwt: Int? = null,
            minTeu: Int? = null,
            maxTeu: Int? = null,
        ): VoyagesByPortResponse {
            return restTemplate.getForObjectWithParams<VoyagesByPortResponse>(
                path = "$prefix/byPort",
                "originPortUnlocodes" to originPortUnlocodes?.joinToString(),
                "destinationPortUnlocodes" to destinationPortUnlocodes?.joinToString(),
                "originPortAreaIds" to originPortAreaIds?.joinToString(),
                "destinationPortAreaIds" to destinationPortAreaIds?.joinToString(),
                "aisDestinationUnlocodes" to aisDestinationUnlocodes?.joinToString(),
                "start" to start,
                "end" to end,
                "limit" to limit,
                "categories" to categories,
                "finished" to finished,
                "minDwt" to minDwt,
                "maxDwt" to maxDwt,
                "minTeu" to minTeu,
                "maxTeu" to maxTeu,
            )
        }

        fun findByPortBatch(
            body: List<VoyagesByPortRequest>
        ): Array<VoyagesByPortResponse> {
            return restTemplate.postForObject<Array<VoyagesByPortResponse>>(
                url = "$prefix/byPort",
                request = body
            )
        }
    }

    class StatementOfFacts(
        val restTemplate: RestTemplate,
        val prefix: String
    ) {
        inline fun <reified T : StatementOfFactsView> findByVisit(
            visitId: EntryId,
            view: StatementOfFactsViewName
        ): T {
            return restTemplate.getForObjectWithParams<T>(
                path = "$prefix/byVisit/{visitId}",
                pathParams = listOf(visitId),
                "view" to view
            )
        }

        /**
         * Retrieves statement of facts (SOF) for the given ship by IMO. A SOF is based on a visit.
         * Requires a time-range ([start], [end]) or [last] X amount.
         *
         * Returns items from most recent to least recent, sorting by its start time.
         *
         * Use [findByImoBatch] to request multiple ships at once.
         *
         * @param imo Ship imo. Required.
         * @param start Time range start. Use in combination with [end]. Required, or use [last].
         * @param end Time range end. Use in combination with [start]. Required, or use [last].
         * @param last Limits the result to X most recent items. Required when not using time range ([start]+[end])
         * @param finished Whether the returned items must be finished. Pass `true` for only finished, `false` for only
         * ongoing items, or `null` (default) for both.
         * @param confirmed Whether the returned items must be confirmed, meaning it contains at least 1 stop. Pass
         * `true` for only confirmed items, `false` for only non-confirmed items, or `null` (default) for both.
         */
        inline fun <reified T : StatementOfFactsView> findByImo(
            view: StatementOfFactsViewName,
            imo: Int,
            start: Instant? = null,
            end: Instant? = null,
            last: Int? = null,
            finished: Boolean? = null,
            confirmed: Boolean? = null
        ): StatementOfFactsViewByImoResponse<T> {
            return restTemplate.getForObjectWithParams<StatementOfFactsViewByImoResponse<T>>(
                path = "$prefix/byImo/{imo}",
                pathParams = listOf(imo),
                "view" to view,
                "start" to start,
                "end" to end,
                "last" to last,
                "finished" to finished,
                "confirmed" to confirmed
            )
        }

        /** Request multiple [findByImo] calls at once */
        inline fun <reified T : StatementOfFactsView> findByImoBatch(
            view: StatementOfFactsViewName,
            body: List<StatementOfFactsViewByImoRequest>
        ): Array<StatementOfFactsViewByImoResponse<T>> {
            return restTemplate.postForObject<Array<StatementOfFactsViewByImoResponse<T>>>(
                url = "$prefix/byImo",
                request = body,
                "view" to view,
            )
        }

        /**
         * Retrieves statement of facts (SOF) for the given ship by IMO, querying for items before or after
         * [timestamp].
         * Use a positive [limit] for items that happened after [timestamp], in other words: looking into the future.
         * Use a negative [limit] for items that happened before [timestamp], in other words: looking in the past.
         *
         * @param imo Ship imo. Required.
         * @param timestamp The timestamp, the system compares the [timestamp] to the [Visit.start] timestamp.
         * @param limit The amount of items to return before or after the timestamp. Use a positive [limit] for items
         * that happened after [timestamp] or a negative [limit] for items that happened before [timestamp].
         * @param finished Whether the returned items must be finished. Pass `true` for only finished, `false` for only
         * ongoing items, or `null` (default) for both.
         * @param confirmed Whether the returned items must be confirmed, meaning it contains at least 1 stop. Pass
         * `true` for only confirmed items, `false` for only non-confirmed items, or `null` (default) for both.
         */
        inline fun <reified T : StatementOfFactsView> findByImoLookAround(
            view: StatementOfFactsViewName,
            imo: Int,
            timestamp: Instant,
            limit: Int,
            finished: Boolean? = null,
            confirmed: Boolean? = null
        ): StatementOfFactsViewByImoLookAroundResponse<T> {
            return restTemplate.getForObjectWithParams<StatementOfFactsViewByImoLookAroundResponse<T>>(
                path = "$prefix/byImo/{imo}/lookaround",
                pathParams = listOf(imo),
                "view" to view,
                "timestamp" to timestamp,
                "limit" to limit,
                "finished" to finished,
                "confirmed" to confirmed
            )
        }

        /**
         * Retrieves statement of facts (SOF) for all ships in the given port, during the given time range or most
         * recent X amount (limit) of ships.
         *
         * Required parameters:
         * - [unlocode] or poma [areaId]
         * - time range ([start], [end]) or [limit]
         *
         * The resulting list contains SOFs most recent to least recent.
         *
         * @param unlocode Unlocode of the port, e.g. NLRTM. Required, unless using [areaId]
         * @param areaId POMA area id. Required, unless using [unlocode]
         * @param start Time range start. Use in combination with [end]. Required, or use [last].
         * @param end Time range end. Use in combination with [start]. Required, or use [last].
         * @param limit Limits the result to X most recent items. Required when not using time range ([start]+[end])
         * @param finished Whether the returned items must be finished. Pass `true` for only finished, `false` for only
         * ongoing items, or `null` (default) for both.
         * @param categories Limits the result to ships that match the given categories. Pass `null` (default) for all
         * ships.
         * @param minDwt When set, the ship's DWT >= given [minDwt]. Can be combined with [maxDwt] or used solely.
         * Requires at least 1 ship [categories]. Cannot be combined with [ShipCategoryV2.CONTAINER], which uses TEU.
         * @param maxDwt When set, the ship's DWT <= given [maxDwt]. Can be combined with [minDwt] or used solely.
         * Requires at least 1 ship [categories]. Cannot be combined with [ShipCategoryV2.CONTAINER], which uses TEU.
         * @param minTeu When set, the ship's TEU >= given [minTeu]. Can be combined with [maxTeu] or used solely.
         * Requires [categories] to be set to [ShipCategoryV2.CONTAINER], cannot be combined with other ship categories.
         * @param maxTeu When set, the ship's TEU <= given [maxTeu]. Can be combined with [minTeu] or used solely.
         * Requires [categories] to be set to [ShipCategoryV2.CONTAINER], cannot be combined with other ship categories.
         */
        inline fun <reified T : StatementOfFactsView> findByPort(
            view: StatementOfFactsViewName,
            unlocode: String? = null,
            areaId: String? = null,
            start: Instant? = null,
            end: Instant? = null,
            limit: Int? = null,
            categories: Set<ShipCategoryV2>? = null,
            finished: Boolean? = null,
            confirmed: Boolean? = null,
            minDwt: Int? = null,
            maxDwt: Int? = null,
            minTeu: Int? = null,
            maxTeu: Int? = null,
        ): StatementOfFactsViewByPortResponse<T> {
            return restTemplate.getForObjectWithParams<StatementOfFactsViewByPortResponse<T>>(
                path = "$prefix/byPort",
                "view" to view,
                "unlocode" to unlocode,
                "areaId" to areaId,
                "start" to start,
                "end" to end,
                "limit" to limit,
                "categories" to categories?.joinToString(),
                "finished" to finished,
                "confirmed" to confirmed,
                "minDwt" to minDwt,
                "maxDwt" to maxDwt,
                "minTeu" to minTeu,
                "maxTeu" to maxTeu,
            )
        }

        /** Request multiple [findByPort] calls at once */
        inline fun <reified T : StatementOfFactsView> findByPortBatch(
            view: StatementOfFactsViewName,
            body: List<StatementOfFactsViewByPortRequest>
        ): Array<StatementOfFactsViewByPortResponse<T>> {
            return restTemplate.postForObject<Array<StatementOfFactsViewByPortResponse<T>>>(
                url = "$prefix/byPort",
                request = body,
                "view" to view,
            )
        }
    }

    class Traces(
        val restTemplate: RestTemplate,
        val prefix: String,
    ) {
        /**
         * Finds a trace by its id.
         *
         * @param id The id of the entry we want to find the trace for.
         * @param blocking Whether the request should block until the trace is available. Otherwise, the trace will be scheduled for generation, meaning the first request will return null and the trace will be available later.
         */
        fun findById(
            id: EntryId,
            blocking: Boolean? = null
        ): Trace? {
            return try {
                restTemplate.getForObjectWithParams<Trace>(
                    path = "$prefix/{id}",
                    pathParams = listOf(id),
                    queryParams = listOfNotNull(
                        // Only include blocking if it is not null given that we only allow true or false
                        blocking?.let { "blocking" to blocking }
                    ).toMap()
                )
            } catch (_: HttpClientErrorException.NotFound) {
                // 404 are caught in the client and instead return null
                null
            }
        }

        /**
         * Finds traces by their ids.
         *
         * @param ids A list of ids of the entries we want to find the traces for.
         * @param blocking Whether the request should block until the traces are available. Otherwise, the traces will be scheduled for generation, meaning the first request will return null and the traces will be available later.
         */
        fun findByIds(
            ids: List<EntryId>,
            blocking: Boolean? = null
        ): List<Trace> {
            return restTemplate.postForObjectWithParams<Array<Trace>>(
                path = "$prefix/ids",
                body = ids,
                params = listOfNotNull(
                    // Only include blocking if it is not null given that we only allow true or false
                    blocking?.let { "blocking" to blocking }
                ).toMap()
            ).toList()
        }
    }
}
