package nl.teqplay.vesselvoyage.client

import nl.teqplay.skeleton.auth.credentials.keycloak.s2s.client.KeycloakS2SClientWrapper
import nl.teqplay.skeleton.common.config.VesselVoyageProperties
import nl.teqplay.skeleton.common.logging.OutgoingRequestLogger
import org.springframework.beans.factory.ObjectProvider
import org.springframework.boot.autoconfigure.AutoConfiguration
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.context.annotation.Bean
import org.springframework.web.client.RestTemplate

@AutoConfiguration
@EnableConfigurationProperties(VesselVoyageProperties::class)
class VesselVoyageClientAutoConfiguration {
    @Bean(BeanNames.VESSELVOYAGE_REST_TEMPLATE)
    fun vesselVoyageRestTemplate(
        restTemplateBuilder: RestTemplateBuilder,
        vesselVoyageProperties: VesselVoyageProperties,
        outgoingRequestLoggerProvider: ObjectProvider<OutgoingRequestLogger>,
    ): RestTemplate {
        return KeycloakS2SClientWrapper.create(
            restTemplateBuilder,
            outgoingRequestLoggerProvider,
            vesselVoyageProperties
        )
    }

    @Bean
    @ConditionalOnMissingBean
    fun vesselVoyageClient(@VesselVoyageRestTemplate restTemplate: RestTemplate): VesselVoyageClient =
        VesselVoyageClient(restTemplate)

    @Bean
    @ConditionalOnMissingBean
    fun vesselVoyageV1Client(@VesselVoyageRestTemplate restTemplate: RestTemplate): VesselVoyageV1Client =
        VesselVoyageV1Client(restTemplate)
}
