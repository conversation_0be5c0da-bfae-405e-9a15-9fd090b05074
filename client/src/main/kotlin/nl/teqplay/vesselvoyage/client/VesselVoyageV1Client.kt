package nl.teqplay.vesselvoyage.client

import nl.teqplay.csi.model.ship.info.component.ShipCategoryV2
import nl.teqplay.skeleton.common.network.getForObjectWithParams
import nl.teqplay.vesselvoyage.model.Entry
import nl.teqplay.vesselvoyage.model.HistoricTrace
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.model.VisitQuery
import nl.teqplay.vesselvoyage.model.VisitVoyage
import nl.teqplay.vesselvoyage.model.Voyage
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.postForObject
import java.time.Instant
import java.time.ZoneOffset

@Deprecated("V1 is not maintained anymore and will eventually be removed")
class VesselVoyageV1Client(private val restTemplate: RestTemplate) {
    fun getVisitHistory(
        imo: String,
        start: Instant,
        end: Instant
    ): Array<Visit> {
        return restTemplate.getForObjectWithParams(
            "/v1/ships/{imo}/history/visits",
            listOf(imo),
            "start" to start,
            "end" to end
        )
    }

    fun getVisits(
        start: Instant,
        end: Instant,
        portIds: Set<String>? = null,
        imos: Set<String>? = null,
        shipCategoriesV2: Set<ShipCategoryV2>? = null,
        finished: Boolean? = null,
        includeSubPorts: Boolean? = null,
        limit: Int = 1000
    ): Array<Visit> {
        return restTemplate.postForObject(
            "/v1/visits/query",
            VisitQuery(
                startTime = start.atZone(ZoneOffset.UTC),
                endTime = end.atZone(ZoneOffset.UTC),
                portIds = portIds,
                imos = imos,
                shipCategories = null,
                shipCategoriesV2 = shipCategoriesV2,
                finished = finished,
                includeSubPorts = includeSubPorts,
                limit = limit
            )
        )
    }

    fun getVoyageHistory(
        imo: String,
        start: Instant,
        end: Instant
    ): Array<Voyage> {
        return restTemplate.getForObjectWithParams(
            "/v1/ships/{imo}/history/voyages",
            listOf(imo),
            "start" to start,
            "end" to end
        )
    }

    fun getEntry(entryId: String): Entry? {
        return restTemplate.getForObjectWithParams(
            "/v1/entries/{entryId}",
            listOf(entryId)
        )
    }

    fun getEntries(entryIds: Set<String>): Array<Entry> {
        return restTemplate.postForObject(
            "/v1/entries",
            entryIds
        )
    }

    /**
     * Gets the last [limit] entries of [imo]
     * Entries can be a Voyage or Visit
     */
    fun getRecentHistory(
        imo: String,
        limit: Int = 5
    ): Array<Entry> {
        return restTemplate.getForObjectWithParams(
            "/v1/ships/{imo}/recent",
            listOf(imo),
            "limit" to limit
        )
    }

    fun getTraceByEntryId(
        entryId: String
    ): HistoricTrace {
        return restTemplate.getForObjectWithParams(
            "/v1/traces/entry/{entryId}",
            listOf(entryId)
        )
    }

    fun getTracesByEntryIds(
        entryIds: Set<String>
    ): Array<HistoricTrace> {
        return restTemplate.postForObject(
            "/v1/traces/entry",
            entryIds
        )
    }

    fun getVisitCount(imo: String): Int {
        return restTemplate.getForObjectWithParams(
            "/v1/visits/{imo}/count",
            listOf(imo)
        )
    }

    fun getVisitIds(imo: String): Array<String> {
        return restTemplate.getForObjectWithParams(
            "/v1/visits/{imo}/ids",
            listOf(imo)
        )
    }

    fun getVisitAroundVisit(imo: String, visitId: String?, radius: UInt): Array<VisitVoyage> {
        return restTemplate.getForObjectWithParams(
            "/v1/entries/visits/{imo}",
            listOf(imo),
            "visitId" to visitId,
            "radius" to radius
        )
    }
}
